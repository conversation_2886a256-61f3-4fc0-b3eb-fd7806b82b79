{"version": 3, "file": "success.js", "sources": ["carpool-package/pages/carpool/publish/success.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/Y2FycG9vbC1wYWNrYWdlXHBhZ2VzXGNhcnBvb2xccHVibGlzaFxzdWNjZXNzLnZ1ZQ"], "sourcesContent": ["<template>\n  <view class=\"success-container\">\n    <!-- 导航栏 -->\n    <carpool-nav title=\"发布成功\"></carpool-nav>\n    \n    <!-- 页面主体 -->\n    <view class=\"success-content\">\n      <!-- 成功状态卡片 - 新设计 -->\n      <view class=\"main-card\">\n        <view class=\"main-title\">信息发布成功 - 磁州拼车</view>\n        <view class=\"main-desc\">完善信息或置顶，可以大幅度提高拼车信息传播效果哦</view>\n        <view class=\"main-btns\">\n          <view class=\"main-btn\" @click=\"goHome\">\n            <view class=\"btn-text-container\">\n              <text class=\"btn-text\">首</text>\n              <text class=\"btn-text\">页</text>\n            </view>\n          </view>\n          <view class=\"main-btn main-btn-active\" @click=\"viewDetail\">\n            <view class=\"btn-text-container\">\n              <text class=\"btn-text\">查看</text>\n              <text class=\"btn-text\">信息</text>\n            </view>\n          </view>\n          <view class=\"main-btn\" @click=\"publishAgain\">\n            <view class=\"btn-text-container\">\n              <text class=\"btn-text\">再发</text>\n              <text class=\"btn-text\">一条</text>\n            </view>\n          </view>\n          <view class=\"main-btn\" @click=\"shareInfo\">\n            <view class=\"btn-text-container\">\n              <text class=\"btn-text\">分享</text>\n              <text class=\"btn-text\">信息</text>\n            </view>\n          </view>\n          <view class=\"main-btn\" @click=\"goToMyPublish\">\n            <view class=\"btn-text-container\">\n              <text class=\"btn-text\">管理</text>\n              <text class=\"btn-text\">信息</text>\n            </view>\n          </view>\n        </view>\n      </view>\n      \n      <!-- 置顶信息组件 -->\n      <view class=\"premium-card\">\n        <view class=\"premium-title\">\n          <view class=\"premium-title-icon\">\n            <image src=\"/static/images/tabbar/crown.png\" mode=\"aspectFit\"></image>\n          </view>\n          <text class=\"premium-title-text\">置顶信息，提升10倍曝光率</text>\n        </view>\n        <view class=\"custom-premium-actions\">\n          <!-- 广告置顶选项 -->\n          <view class=\"premium-option-item ad-option\" @click=\"handleAdTop\">\n            <view class=\"option-left\">\n              <view class=\"option-circle blue-circle\">\n                <image src=\"/static/images/tabbar/crown.png\" mode=\"aspectFit\" class=\"option-icon\"></image>\n              </view>\n              <view class=\"option-info\">\n                <text class=\"option-title\">看广告置顶</text>\n                <text class=\"option-desc\">免费置顶2小时，排名次于付费</text>\n              </view>\n            </view>\n            <view class=\"option-right\">\n              <view class=\"option-button free-button\">免费</view>\n            </view>\n          </view>\n          \n          <!-- 付费置顶选项 -->\n          <view class=\"premium-option-item paid-option\" @click=\"handlePaidTop\">\n            <view class=\"option-left\">\n              <view class=\"option-circle orange-circle\">\n                <image src=\"/static/images/tabbar/crown.png\" mode=\"aspectFit\" class=\"option-icon\"></image>\n              </view>\n              <view class=\"option-info\">\n                <text class=\"option-title\">付费置顶</text>\n                <text class=\"option-desc\">获得优先展示位置，排名最靠前</text>\n              </view>\n            </view>\n            <view class=\"option-right\">\n              <view class=\"option-button paid-button\">付费</view>\n            </view>\n          </view>\n        </view>\n      </view>\n      \n      <!-- 信息卡片 -->\n        <view class=\"info-card\">\n        <view class=\"card-header\">\n          <text class=\"card-title\">拼车信息概览</text>\n            <view class=\"info-tag\" :class=\"typeClass\">{{typeText}}</view>\n          </view>\n          \n        <!-- 路线信息 -->\n        <view class=\"route-section\">\n          <view class=\"route-points\">\n            <view class=\"route-point\">\n              <view class=\"point-dot start\"></view>\n              <text class=\"point-text\">{{formData.startPoint}}</text>\n            </view>\n            <view class=\"route-divider\">\n              <view class=\"divider-line\"></view>\n              <view class=\"divider-arrow\">\n                <image src=\"/static/images/tabbar/arrow-right.png\" mode=\"aspectFit\" class=\"arrow-icon\"></image>\n              </view>\n            </view>\n            <view class=\"route-point\">\n              <view class=\"point-dot end\"></view>\n              <text class=\"point-text\">{{formData.endPoint}}</text>\n            </view>\n          </view>\n          \n          <!-- 途径点 -->\n          <view class=\"via-points\" v-if=\"formData.viaPoints && formData.viaPoints.length > 0\">\n            <view class=\"via-title\">\n              <image src=\"/static/images/tabbar/location.png\" mode=\"aspectFit\" class=\"via-icon\"></image>\n              <text class=\"via-text\">途径：</text>\n            </view>\n            <view class=\"via-list\">\n              <text class=\"via-item\" v-for=\"(point, index) in formData.viaPoints\" :key=\"index\">{{point}}</text>\n            </view>\n          </view>\n        </view>\n        \n        <!-- 详细信息 -->\n        <view class=\"details-section\">\n          <view class=\"detail-row\">\n            <view class=\"detail-item\">\n              <image src=\"/static/images/tabbar/calendar.png\" mode=\"aspectFit\" class=\"detail-icon\"></image>\n              <text class=\"detail-text\">{{formData.departureDate}}</text>\n            </view>\n            <view class=\"detail-item\">\n              <image src=\"/static/images/tabbar/time.png\" mode=\"aspectFit\" class=\"detail-icon\"></image>\n              <text class=\"detail-text\">{{formData.departureTime}}</text>\n            </view>\n            </view>\n            \n          <view class=\"detail-row\">\n            <view class=\"detail-item\" v-if=\"publishType === 'people-to-car'\">\n              <image src=\"/static/images/tabbar/user.png\" mode=\"aspectFit\" class=\"detail-icon\"></image>\n              <text class=\"detail-text\">{{formData.passengers}}人</text>\n            </view>\n            <view class=\"detail-item\" v-if=\"publishType === 'car-to-people'\">\n              <image src=\"/static/images/tabbar/座位.png\" mode=\"aspectFit\" class=\"detail-icon\"></image>\n              <text class=\"detail-text\">空余座位 {{formData.availableSeats}}</text>\n            </view>\n            <view class=\"detail-item\" v-if=\"publishType === 'goods-to-car' || publishType === 'car-to-goods'\">\n              <image src=\"/static/images/tabbar/货物.png\" mode=\"aspectFit\" class=\"detail-icon\"></image>\n              <text class=\"detail-text\">{{formData.goodsType || '普通货物'}}</text>\n            </view>\n            <view class=\"detail-item\" v-if=\"mode === 'premium'\">\n              <image src=\"/static/images/tabbar/vip-crown.png\" mode=\"aspectFit\" class=\"detail-icon premium-icon\"></image>\n              <text class=\"detail-text premium-text\">已置顶展示</text>\n            </view>\n          </view>\n          \n          <view class=\"contact-row\">\n            <view class=\"contact-item\">\n              <image src=\"/static/images/tabbar/phone.png\" mode=\"aspectFit\" class=\"contact-icon\"></image>\n              <text class=\"contact-text\">{{formData.contactPhone}}</text>\n            </view>\n            <view class=\"contact-item\" v-if=\"formData.contactName\">\n              <image src=\"/static/images/tabbar/user-circle.png\" mode=\"aspectFit\" class=\"contact-icon\"></image>\n              <text class=\"contact-text\">{{formData.contactName}}</text>\n          </view>\n        </view>\n        \n          <view class=\"remark-row\" v-if=\"formData.remark\">\n            <text class=\"remark-label\">备注：</text>\n            <text class=\"remark-content\">{{formData.remark}}</text>\n          </view>\n        </view>\n      </view>\n      \n      <!-- 温馨提示 -->\n      <view class=\"tips-card\">\n        <view class=\"tips-header\">\n          <image src=\"/static/images/tabbar/info.png\" mode=\"aspectFit\" class=\"tips-icon\"></image>\n            <text class=\"tips-title\">温馨提示</text>\n          </view>\n        <view class=\"tips-content\">\n          <text class=\"tips-text\">· 发布信息将在人工审核后展示，预计5分钟内完成</text>\n          <text class=\"tips-text\">· 请保持电话畅通，以便有意向的用户联系您</text>\n          <text class=\"tips-text\">· 拼车出行请注意安全，提前确认对方身份</text>\n        </view>\n      </view>\n      \n      <!-- 悬浮分享按钮 -->\n      <button class=\"float-btn share-btn\" open-type=\"share\" @click=\"beforeShare\">\n        <image src=\"/static/images/tabbar/share.png\" mode=\"aspectFit\" class=\"float-icon\"></image>\n        </button>\n        \n      <!-- 客服按钮 -->\n      <view class=\"float-btn kefu-btn\" @click=\"showKefu\">\n        <image src=\"/static/images/tabbar/kefu.png\" mode=\"aspectFit\" class=\"float-icon\"></image>\n      </view>\n      </view>\n      \n    <!-- 分享提示弹窗 -->\n    <view class=\"share-tips-overlay\" v-if=\"shareTipsVisible\">\n      <view class=\"share-tips-card\" @click.stop>\n        <view class=\"share-tips-icon\">\n          <image src=\"/static/images/tabbar/crown.png\" mode=\"aspectFit\" class=\"crown-icon\"></image>\n        </view>\n        <view class=\"share-tips-title\">恭喜你!获得免费置顶和群发机会</view>\n        \n        <view class=\"share-tips-item\">\n          <view class=\"tips-item-number\">1</view>\n          <view class=\"tips-item-content\">\n            <text class=\"tips-text\">把信息分享到朋友圈、微信群或好友处，</text>\n            <text class=\"tips-text\">您的信息将自动置顶1天！</text>\n          </view>\n        </view>\n        \n        <view class=\"share-tips-item\">\n          <view class=\"tips-item-number\">2</view>\n          <view class=\"tips-item-content\">\n            <text class=\"tips-text\">把你发布的信息发送给客服后，客服会给</text>\n            <text class=\"tips-text\">您群发多个群，扩大曝光量!</text>\n          </view>\n        </view>\n        \n        <view class=\"share-tips-btns\">\n          <view class=\"share-btn-item close-btn\" @click=\"hideShareTips\">关闭</view>\n          <view class=\"share-btn-item share-btn-blue pulsing-btn\" @click=\"jumpToDetailAndShare\">\n            <text>去分享</text>\n          </view>\n          <view class=\"share-btn-item share-btn-green\" @click=\"contactService\">加客服</view>\n        </view>\n      </view>\n    </view>\n    \n    <!-- 客服二维码弹窗 -->\n    <view class=\"qrcode-overlay\" v-if=\"qrcodeVisible\" @click=\"hideQrcode\">\n      <view class=\"qrcode-card\" @click.stop>\n        <view class=\"qrcode-header\">\n          <view class=\"qrcode-close\" @click=\"hideQrcode\">\n            <svg t=\"1692586074385\" class=\"close-icon\" viewBox=\"0 0 1024 1024\" version=\"1.1\" xmlns=\"http://www.w3.org/2000/svg\" width=\"20\" height=\"20\">\n              <path d=\"M572.16 512l183.466667-183.04a42.666667 42.666667 0 1 0-60.586667-60.586667L512 451.84 328.96 268.373333a42.666667 42.666667 0 0 0-60.586667 60.586667l183.04 183.04-183.04 183.466667a42.666667 42.666667 0 0 0 60.586667 60.586666L512 572.16l183.04 183.466667a42.666667 42.666667 0 0 0 60.586667-60.586667z\" fill=\"#999999\"></path>\n            </svg>\n          </view>\n        </view>\n        \n        <view class=\"qrcode-content\">\n          <view class=\"qrcode-title-container\">\n            <image class=\"qrcode-title-icon\" src=\"/static/images/icons/customer-service.png\" mode=\"aspectFit\"></image>\n            <text class=\"qrcode-title\">微信扫码添加客服</text>\n          </view>\n          \n          <view class=\"qrcode-desc\">添加客服微信，提供更多发布推广服务</view>\n          \n          <view class=\"qrcode-image-container\">\n            <image src=\"/static/images/qrcode.png\" mode=\"aspectFit\" class=\"qrcode-image\"></image>\n            <view class=\"qrcode-scan-hint\">\n              <view class=\"scan-icon-container\">\n                <svg viewBox=\"0 0 24 24\" width=\"16\" height=\"16\" class=\"scan-icon\">\n                  <path fill=\"currentColor\" d=\"M9.5,6.5v3h-3v-3H9.5 M11,5H5v6h6V5L11,5z M9.5,14.5v3h-3v-3H9.5 M11,13H5v6h6V13z M17.5,6.5v3h-3v-3H17.5 M19,5h-6v6h6V5L19,5z M13,13h1.5v1.5H13V13z M14.5,14.5H16V16h-1.5V14.5z M16,13h1.5v1.5H16V13z M13,16h1.5v1.5H13V16z M14.5,17.5H16V19h-1.5V17.5z M16,16h1.5v1.5H16V16z M17.5,14.5H19V16h-1.5V14.5z M17.5,17.5H19V19h-1.5V17.5z M22,7h-2V4h-3V2h5V7z M22,22v-5h-2v3h-3v2H22z M2,22h5v-2H4v-3H2V22z M2,2v5h2V4h3V2H2z\"/>\n                </svg>\n              </view>\n              <text>长按识别二维码添加客服</text>\n            </view>\n          </view>\n          \n          <view class=\"qrcode-info-container\">\n            <view class=\"qrcode-info-item\">\n              <svg viewBox=\"0 0 24 24\" width=\"16\" height=\"16\" class=\"info-icon\">\n                <path fill=\"currentColor\" d=\"M12,2A10,10 0 0,1 22,12A10,10 0 0,1 12,22A10,10 0 0,1 2,12A10,10 0 0,1 12,2M12,4A8,8 0 0,0 4,12A8,8 0 0,0 12,20A8,8 0 0,0 20,12A8,8 0 0,0 12,4M11,7H13V13H11V7M11,15H13V17H11V15Z\"/>\n              </svg>\n              <text>客服在线时间: 8:00-22:00</text>\n            </view>\n            <view class=\"qrcode-info-item\">\n              <svg viewBox=\"0 0 24 24\" width=\"16\" height=\"16\" class=\"info-icon\">\n                <path fill=\"currentColor\" d=\"M12,4A4,4 0 0,1 16,8A4,4 0 0,1 12,12A4,4 0 0,1 8,8A4,4 0 0,1 12,4M12,6A2,2 0 0,0 10,8A2,2 0 0,0 12,10A2,2 0 0,0 14,8A2,2 0 0,0 12,6M12,13C14.67,13 20,14.33 20,17V20H4V17C4,14.33 9.33,13 12,13M12,14.9C9.03,14.9 5.9,16.36 5.9,17V18.1H18.1V17C18.1,16.36 14.97,14.9 12,14.9Z\"/>\n              </svg>\n              <text>客服微信: cishangtc</text>\n            </view>\n          </view>\n        </view>\n        \n        <view class=\"qrcode-actions\">\n          <view class=\"qrcode-btn copy-btn\" @click=\"copyWechatId\">\n            <svg viewBox=\"0 0 24 24\" width=\"16\" height=\"16\" class=\"btn-icon\">\n              <path fill=\"currentColor\" d=\"M19,21H8V7H19M19,5H8A2,2 0 0,0 6,7V21A2,2 0 0,0 8,23H19A2,2 0 0,0 21,21V7A2,2 0 0,0 19,5M16,1H4A2,2 0 0,0 2,3V17H4V3H16V1Z\"/>\n            </svg>\n            <text>复制微信号</text>\n          </view>\n          <view class=\"qrcode-btn save-btn\" @click=\"saveQrcode\">\n            <svg viewBox=\"0 0 24 24\" width=\"16\" height=\"16\" class=\"btn-icon\">\n              <path fill=\"currentColor\" d=\"M15,9H5V5H15M12,19A3,3 0 0,1 9,16A3,3 0 0,1 12,13A3,3 0 0,1 15,16A3,3 0 0,1 12,19M17,3H5C3.89,3 3,3.9 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V7L17,3Z\"/>\n            </svg>\n            <text>保存图片</text>\n          </view>\n        </view>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script setup>\nimport { ref, computed, onMounted, onUnmounted } from 'vue';\nimport rewardedAdService from '@/services/rewardedAdService';\n\n// 发布信息数据\nconst publishId = ref('');\nconst publishType = ref('people-to-car');\nconst mode = ref('ad'); // ad 或 premium\n\n// 表单数据\nconst formData = ref({\n  startPoint: '磁州城区',\n  endPoint: '邯郸站',\n  departureDate: '2023-10-15',\n  departureTime: '14:30',\n  passengers: '2',\n  availableSeats: 3,\n  contactName: '张先生',\n  contactPhone: '138****5678',\n  remark: '行李少，准时出发',\n  viaPoints: ['磁县政府', '磁县一中']\n});\n\n// 计算属性\nconst typeText = computed(() => {\n  const typeMap = {\n    'people-to-car': '人找车',\n    'car-to-people': '车找人',\n    'goods-to-car': '货找车',\n    'car-to-goods': '车找货'\n  };\n  return typeMap[publishType.value] || '人找车';\n});\n\nconst typeClass = computed(() => {\n  return publishType.value;\n});\n\n// 页面加载\nonMounted(() => {\n  const pages = getCurrentPages();\n  const currentPage = pages[pages.length - 1];\n  const options = currentPage.options || {};\n\n  // 获取发布类型和模式\n  if (options && options.type) {\n    publishType.value = options.type;\n  }\n  if (options && options.mode) {\n    mode.value = options.mode;\n  }\n  if (options && options.id) {\n    publishId.value = options.id;\n    getPublishDetail();\n  }\n\n  // 初始化激励视频广告\n  rewardedAdService.initRewardedVideoAd();\n\n  // 监听奖励发放成功事件\n  uni.$on('rewardGranted', handleRewardGranted);\n\n  // 自动显示分享提示弹窗\n  setTimeout(() => {\n    showShareTips();\n  }, 1000);\n});\n\nonUnmounted(() => {\n  // 清理事件监听\n  uni.$off('rewardGranted', handleRewardGranted);\n\n  // 销毁广告实例\n  rewardedAdService.destroy();\n});\n\n// 分享配置\n// 注意：在组合式API中，需要使用defineExpose将分享方法暴露出去\nconst onShareAppMessage = () => {\n  return {\n    title: `${typeText.value}：${formData.value.startPoint} → ${formData.value.endPoint}`,\n    path: `/carpool-package/pages/carpool/detail/index?id=${publishId.value}`\n  };\n};\n\n// 获取发布详情\nconst getPublishDetail = () => {\n  // 这里应该是真实的API调用\n  // 目前使用模拟数据\n  console.log('获取发布ID:', publishId.value);\n};\n\n// 处理奖励发放成功\nconst handleRewardGranted = (rewardInfo) => {\n  console.log('奖励发放成功', rewardInfo);\n\n  // 根据奖励类型更新界面状态\n  switch (rewardInfo.type) {\n    case 'free_top':\n      // 更新置顶状态显示\n      uni.showModal({\n        title: '置顶成功',\n        content: '您的拼车信息已成功置顶2小时，将获得更多曝光机会！',\n        showCancel: false\n      });\n      break;\n    case 'free_publish':\n      uni.showModal({\n        title: '发布成功',\n        content: '恭喜您获得免费发布机会！',\n        showCancel: false\n      });\n      break;\n    case 'free_refresh':\n      uni.showModal({\n        title: '刷新成功',\n        content: '您的拼车信息已刷新，排名已更新！',\n        showCancel: false\n      });\n      break;\n  }\n\n  // 可以在这里刷新页面数据或更新状态\n  // getPublishDetail();\n};\n\n// 处理置顶选项选择\nconst handlePremiumAction = (option) => {\n  console.log('选择了置顶选项:', option);\n  if (option.type === 'paid') {\n    uni.navigateTo({\n      url: `/carpool-package/pages/carpool/premium/top?id=${publishId.value}`\n    });\n  } else {\n    // 处理广告置顶\n    uni.showToast({\n      title: '广告置顶成功！信息已置顶2小时',\n      icon: 'success',\n      duration: 2000\n    });\n  }\n};\n\n// 处理广告置顶点击\nconst handleAdTop = async () => {\n  try {\n    // 检查今日观看次数\n    const checkResult = await rewardedAdService.checkTodayWatchCount('top');\n    if (!checkResult.canWatch) {\n      uni.showToast({\n        title: checkResult.message,\n        icon: 'none'\n      });\n      return;\n    }\n\n    // 显示激励视频广告\n    const success = await rewardedAdService.showRewardedVideoAd('free_top', publishId.value);\n\n    if (!success) {\n      // 广告显示失败，提供备选方案\n      uni.showModal({\n        title: '广告暂不可用',\n        content: '是否选择付费置顶？',\n        success: (res) => {\n          if (res.confirm) {\n            handlePaidTop();\n          }\n        }\n      });\n    }\n  } catch (error) {\n    console.error('广告置顶失败', error);\n    uni.showToast({\n      title: '操作失败，请重试',\n      icon: 'none'\n    });\n  }\n};\n\n// 处理付费置顶点击\nconst handlePaidTop = () => {\n  // 跳转到付费置顶页面\n  uni.navigateTo({\n    url: `/carpool-package/pages/carpool/premium/top?id=${publishId.value}`\n  });\n};\n\n// 分享提示相关\nconst shareTipsVisible = ref(false);\n\n// 客服二维码相关\nconst qrcodeVisible = ref(false);\nconst wechatId = ref('cishangtc'); // 客服微信ID\n\n// 分享相关方法\nconst beforeShare = () => {\n  // 可以在这里记录分享行为或其他操作\n  console.log('用户点击了分享按钮');\n};\n\n// 显示分享提示\nconst showShareTips = () => {\n  shareTipsVisible.value = true;\n};\n\n// 隐藏分享提示\nconst hideShareTips = () => {\n  shareTipsVisible.value = false;\n};\n\n// 跳转到详情页并分享\nconst jumpToDetailAndShare = () => {\n  hideShareTips();\n  viewDetail();\n  // 延迟显示系统分享菜单\n  setTimeout(() => {\n    uni.showShareMenu({\n      withShareTicket: true,\n      menus: ['shareAppMessage', 'shareTimeline']\n    });\n  }, 500);\n};\n\n// 联系客服\nconst contactService = () => {\n  hideShareTips();\n  showKefu();\n};\n\n// 显示客服二维码\nconst showKefu = () => {\n  qrcodeVisible.value = true;\n};\n\n// 隐藏客服二维码\nconst hideQrcode = () => {\n  qrcodeVisible.value = false;\n};\n\n// 复制微信号\nconst copyWechatId = () => {\n  uni.setClipboardData({\n    data: wechatId.value,\n    success: () => {\n      uni.showToast({\n        title: '微信号已复制',\n        icon: 'success'\n      });\n    }\n  });\n};\n\n// 保存二维码图片\nconst saveQrcode = () => {\n  uni.saveImageToPhotosAlbum({\n    filePath: '/static/images/qrcode.png',\n    success: () => {\n      uni.showToast({\n        title: '二维码已保存',\n        icon: 'success'\n      });\n    },\n    fail: (err) => {\n      if (err.errMsg.indexOf('auth deny') >= 0) {\n        uni.showModal({\n          title: '提示',\n          content: '需要您授权保存图片到相册',\n          success: (res) => {\n            if (res.confirm) {\n              uni.openSetting();\n            }\n          }\n        });\n      }\n    }\n  });\n};\n\n// 分享信息\nconst shareInfo = () => {\n  // 调用分享功能\n  uni.showShareMenu({\n    withShareTicket: true\n  });\n};\n\n// 查看详情\nconst viewDetail = () => {\n  uni.navigateTo({\n    url: `/carpool-package/pages/carpool/detail/index?id=${publishId.value}`\n  });\n};\n\n// 返回首页\nconst goHome = () => {\n  uni.switchTab({\n    url: '/pages/index/index'\n  });\n};\n\n// 继续发布\nconst publishAgain = () => {\n  uni.redirectTo({\n    url: '/carpool-package/pages/carpool/publish/ad-publish'\n  });\n};\n\n// 跳转到我的发布列表\nconst goToMyPublish = () => {\n  uni.navigateTo({\n    url: '/carpool-package/pages/carpool/my/published-list'\n  });\n};\n\n// 暴露分享方法\ndefineExpose({\n  onShareAppMessage\n});\n</script>\n\n<style lang=\"scss\">\n.success-container {\n  min-height: 100vh;\n  background-color: #f5f7fa;\n  padding-bottom: 40rpx;\n}\n\n.success-content {\n  padding: 30rpx;\n  display: flex;\n  flex-direction: column;\n  gap: 30rpx;\n}\n\n/* 成功状态卡片 - 新设计 */\n.main-card {\n  background-color: #ffffff;\n  border-radius: 24rpx;\n  padding: 40rpx 30rpx;\n  box-shadow: 0 8rpx 20rpx rgba(10, 132, 255, 0.15);\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n}\n\n.main-title {\n  color: #1976d2;\n  font-size: 32rpx;\n  font-weight: bold;\n  margin-bottom: 16rpx;\n  text-align: center;\n}\n\n.main-desc {\n  color: #888;\n  font-size: 24rpx;\n  margin-bottom: 36rpx;\n  text-align: center;\n}\n\n.main-btns {\n  display: flex;\n  justify-content: space-between;\n  width: 100%;\n  margin-top: 10rpx;\n}\n\n.main-btn {\n  flex: 1;\n  margin: 0 10rpx;\n  background: #eaf3ff;\n  color: #1976d2;\n  border-radius: 32rpx;\n  font-weight: 500;\n  text-align: center;\n  padding: 25rpx 0;\n  height: 180rpx;\n  transition: background 0.2s, color 0.2s;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.btn-text-container {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  height: 100%;\n  padding: 10rpx 0;\n}\n\n.btn-text {\n  font-size: 28rpx;\n  line-height: 1.8;\n  display: block;\n  font-weight: bold;\n  letter-spacing: 2rpx;\n}\n\n.main-btn-active {\n  background: linear-gradient(90deg, #1976d2 0%, #4a90e2 100%);\n  color: #fff;\n}\n\n.main-btn-active .btn-text {\n  color: #fff;\n}\n\n/* 置顶信息组件 */\n.premium-card {\n  background-color: #ffffff;\n  border-radius: 24rpx;\n  padding: 30rpx;\n  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.04);\n  margin-bottom: 30rpx;\n}\n\n.premium-title {\n  display: flex;\n  align-items: center;\n  margin-bottom: 20rpx;\n}\n\n.premium-title-icon {\n  width: 36rpx;\n  height: 36rpx;\n  margin-right: 10rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.premium-title-icon image {\n  width: 36rpx;\n  height: 36rpx;\n}\n\n.premium-title-text {\n  font-size: 32rpx;\n  font-weight: 600;\n  color: #333333;\n}\n\n/* 自定义置顶选项 */\n.custom-premium-actions {\n  display: flex;\n  flex-direction: column;\n  gap: 20rpx;\n}\n\n.premium-option-item {\n  background-color: #f8f9fe;\n  border-radius: 16rpx;\n  padding: 24rpx;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.option-left {\n  display: flex;\n  align-items: center;\n}\n\n.option-circle {\n  width: 80rpx;\n  height: 80rpx;\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-right: 20rpx;\n}\n\n.blue-circle {\n  background-color: #4a90e2;\n}\n\n.orange-circle {\n  background-color: #f5a623;\n}\n\n.option-icon {\n  width: 40rpx;\n  height: 40rpx;\n  filter: brightness(0) invert(1);\n}\n\n.option-info {\n  display: flex;\n  flex-direction: column;\n}\n\n.option-title {\n  font-size: 30rpx;\n  font-weight: bold;\n  color: #333333;\n  margin-bottom: 6rpx;\n}\n\n.option-desc {\n  font-size: 24rpx;\n  color: #999999;\n}\n\n.option-right {\n  display: flex;\n  align-items: center;\n}\n\n.option-button {\n  padding: 10rpx 30rpx;\n  border-radius: 30rpx;\n  font-size: 26rpx;\n  font-weight: bold;\n}\n\n.free-button {\n  background-color: #4a90e2;\n  color: #ffffff;\n}\n\n.paid-button {\n  background-color: #f5a623;\n  color: #ffffff;\n}\n\n/* 信息卡片 */\n.info-card {\n  background-color: #ffffff;\n  border-radius: 24rpx;\n  padding: 30rpx;\n  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.04);\n}\n\n.card-header {\n  display: flex;\n  align-items: center;\n  margin-bottom: 30rpx;\n}\n\n.card-title {\n  font-size: 32rpx;\n  font-weight: 600;\n  color: #333333;\n}\n\n.info-tag {\n  margin-left: 15rpx;\n  font-size: 22rpx;\n  color: #ffffff;\n  padding: 4rpx 12rpx;\n  border-radius: 6rpx;\n}\n\n.people-to-car {\n  background-color: #0A84FF;\n}\n\n.car-to-people {\n  background-color: #FF453A;\n}\n\n.goods-to-car {\n  background-color: #30D158;\n}\n\n.car-to-goods {\n  background-color: #FF9F0A;\n}\n\n/* 路线信息 */\n.route-section {\n  margin-bottom: 30rpx;\n  border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);\n  padding-bottom: 20rpx;\n}\n\n.route-points {\n  margin-bottom: 20rpx;\n}\n\n.route-point {\n  display: flex;\n  align-items: center;\n  margin-bottom: 16rpx;\n}\n\n.point-dot {\n  width: 16rpx;\n  height: 16rpx;\n  border-radius: 50%;\n  margin-right: 15rpx;\n  flex-shrink: 0;\n}\n\n.start {\n  background-color: #0A84FF;\n}\n\n.end {\n  background-color: #FF453A;\n}\n\n.point-text {\n  font-size: 32rpx;\n  color: #333333;\n  font-weight: 500;\n}\n\n.route-divider {\n  padding-left: 7rpx;\n  margin: 10rpx 0;\n  display: flex;\n}\n\n.divider-line {\n  width: 2rpx;\n  height: 30rpx;\n  background-color: #dddddd;\n}\n\n.divider-arrow {\n  margin-left: -7rpx;\n  margin-top: 30rpx;\n}\n\n.arrow-icon {\n  width: 24rpx;\n  height: 24rpx;\n  opacity: 0.6;\n}\n\n/* 途径点 */\n.via-points {\n  margin-top: 20rpx;\n  background-color: rgba(10, 132, 255, 0.05);\n  border-radius: 12rpx;\n  padding: 15rpx;\n}\n\n.via-title {\n  display: flex;\n  align-items: center;\n  margin-bottom: 10rpx;\n}\n\n.via-icon {\n  width: 24rpx;\n  height: 24rpx;\n  margin-right: 10rpx;\n  opacity: 0.7;\n}\n\n.via-text {\n  font-size: 26rpx;\n  color: #666666;\n  font-weight: 500;\n}\n\n.via-list {\n  display: flex;\n  flex-wrap: wrap;\n}\n\n.via-item {\n  font-size: 26rpx;\n  color: #0A84FF;\n  background-color: rgba(10, 132, 255, 0.1);\n  padding: 4rpx 16rpx;\n  border-radius: 20rpx;\n  margin-right: 16rpx;\n  margin-bottom: 10rpx;\n}\n\n/* 详细信息 */\n.details-section {\n  display: flex;\n  flex-direction: column;\n  gap: 20rpx;\n}\n\n.detail-row, .contact-row {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 30rpx;\n}\n\n.detail-item, .contact-item {\n  display: flex;\n  align-items: center;\n}\n\n.detail-icon, .contact-icon {\n  width: 28rpx;\n  height: 28rpx;\n  margin-right: 10rpx;\n  opacity: 0.6;\n}\n\n.detail-text, .contact-text {\n  font-size: 28rpx;\n  color: #666666;\n}\n\n.premium-icon {\n  opacity: 1;\n}\n\n.premium-text {\n  color: #FF9F0A;\n  font-weight: 500;\n}\n\n.remark-row {\n  background-color: rgba(0, 0, 0, 0.02);\n  padding: 15rpx;\n  border-radius: 12rpx;\n}\n\n.remark-label {\n  font-size: 26rpx;\n  color: #999999;\n  margin-right: 10rpx;\n}\n\n.remark-content {\n  font-size: 26rpx;\n  color: #666666;\n  line-height: 1.5;\n}\n\n/* 温馨提示 */\n.tips-card {\n  background-color: rgba(10, 132, 255, 0.05);\n  border-radius: 24rpx;\n  padding: 20rpx 30rpx;\n}\n\n.tips-header {\n  display: flex;\n  align-items: center;\n  margin-bottom: 15rpx;\n}\n\n.tips-icon {\n  width: 28rpx;\n  height: 28rpx;\n  margin-right: 10rpx;\n  opacity: 0.7;\n}\n\n.tips-title {\n  font-size: 28rpx;\n  font-weight: 600;\n  color: #333333;\n}\n\n.tips-content {\n  display: flex;\n  flex-direction: column;\n  gap: 10rpx;\n}\n\n.tips-text {\n  font-size: 24rpx;\n  color: #666666;\n  line-height: 1.5;\n}\n\n/* 悬浮分享按钮 */\n.float-btn {\n  position: fixed;\n  width: 90rpx;\n  height: 90rpx;\n  border-radius: 50%;\n  background-color: #1677FF;\n  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.2);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  z-index: 100;\n  padding: 0;\n  margin: 0;\n  border: none;\n}\n\n.share-btn {\n  bottom: 40rpx;\n  right: 40rpx;\n}\n\n.kefu-btn {\n  bottom: 40rpx;\n  left: 40rpx;\n}\n\n.float-icon {\n  width: 48rpx;\n  height: 48rpx;\n  filter: brightness(0) invert(1);\n}\n\n/* 分享提示弹窗 */\n.share-tips-overlay {\n  position: fixed;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  background: rgba(0, 0, 0, 0.5);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  z-index: 1000;\n}\n\n.share-tips-card {\n  background-color: #ffffff;\n  border-radius: 24rpx;\n  padding: 40rpx;\n  max-width: 80%;\n  width: 100%;\n  text-align: center;\n}\n\n.share-tips-icon {\n  display: flex;\n  justify-content: center;\n  margin-bottom: 20rpx;\n}\n\n.crown-icon {\n  width: 80rpx;\n  height: 80rpx;\n}\n\n.share-tips-title {\n  font-size: 32rpx;\n  font-weight: bold;\n  color: #FF6B00;\n  margin-bottom: 30rpx;\n}\n\n.share-tips-item {\n  display: flex;\n  align-items: flex-start;\n  margin-bottom: 20rpx;\n  text-align: left;\n}\n\n.tips-item-number {\n  width: 40rpx;\n  height: 40rpx;\n  line-height: 40rpx;\n  text-align: center;\n  font-size: 28rpx;\n  font-weight: bold;\n  color: #ffffff;\n  background-color: #1677FF;\n  border-radius: 50%;\n  margin-right: 20rpx;\n  flex-shrink: 0;\n}\n\n.tips-item-content {\n  flex: 1;\n}\n\n.tips-text {\n  font-size: 28rpx;\n  color: #333333;\n  display: block;\n  line-height: 1.5;\n}\n\n.share-tips-btns {\n  display: flex;\n  justify-content: space-between;\n  margin-top: 40rpx;\n  border-top: 1rpx solid #EEEEEE;\n}\n\n.share-btn-item {\n  flex: 1;\n  padding: 20rpx 0;\n  font-size: 30rpx;\n  font-weight: bold;\n}\n\n.close-btn {\n  color: #999999;\n  border-right: 1rpx solid #EEEEEE;\n}\n\n.share-btn-blue {\n  background-color: #1677FF;\n  color: #ffffff;\n}\n\n.share-btn-green {\n  background-color: #07C160;\n  color: #ffffff;\n}\n\n/* 脉动动画 */\n.pulsing-btn {\n  animation: pulse 1.5s infinite;\n  position: relative;\n}\n\n@keyframes pulse {\n  0% {\n    transform: scale(1);\n    box-shadow: 0 0 0 0 rgba(22, 119, 255, 0.7);\n  }\n  \n  70% {\n    transform: scale(1.05);\n    box-shadow: 0 0 0 10px rgba(22, 119, 255, 0);\n  }\n  \n  100% {\n    transform: scale(1);\n    box-shadow: 0 0 0 0 rgba(22, 119, 255, 0);\n  }\n}\n\n/* 客服二维码弹窗 */\n.qrcode-overlay {\n  position: fixed;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  background: rgba(0, 0, 0, 0.5);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  z-index: 1000;\n}\n\n.qrcode-card {\n  background-color: #ffffff;\n  border-radius: 24rpx;\n  padding: 30rpx;\n  width: 80%;\n  max-width: 600rpx;\n}\n\n.qrcode-header {\n  display: flex;\n  align-items: center;\n  justify-content: flex-end;\n  margin-bottom: 20rpx;\n}\n\n.qrcode-close {\n  width: 40rpx;\n  height: 40rpx;\n  padding: 10rpx;\n  border-radius: 50%;\n  background-color: rgba(0, 0, 0, 0.05);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.close-icon {\n  width: 24rpx;\n  height: 24rpx;\n}\n\n.qrcode-content {\n  text-align: center;\n}\n\n.qrcode-title-container {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-bottom: 20rpx;\n}\n\n.qrcode-title-icon {\n  width: 40rpx;\n  height: 40rpx;\n  margin-right: 10rpx;\n}\n\n.qrcode-title {\n  font-size: 32rpx;\n  font-weight: bold;\n  color: #333333;\n}\n\n.qrcode-desc {\n  font-size: 26rpx;\n  color: #666666;\n  margin-bottom: 30rpx;\n}\n\n.qrcode-image-container {\n  position: relative;\n  margin: 0 auto 30rpx;\n  width: 300rpx;\n  height: 300rpx;\n}\n\n.qrcode-image {\n  width: 100%;\n  height: 100%;\n}\n\n.qrcode-scan-hint {\n  position: absolute;\n  bottom: 0;\n  left: 0;\n  right: 0;\n  padding: 10rpx;\n  background-color: rgba(0, 0, 0, 0.5);\n  color: #ffffff;\n  font-size: 22rpx;\n  border-radius: 0 0 8rpx 8rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.scan-icon-container {\n  margin-right: 8rpx;\n  color: #ffffff;\n}\n\n.scan-icon {\n  width: 24rpx;\n  height: 24rpx;\n}\n\n.qrcode-info-container {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  margin-bottom: 30rpx;\n}\n\n.qrcode-info-item {\n  display: flex;\n  align-items: center;\n  margin-bottom: 10rpx;\n  color: #666666;\n  font-size: 24rpx;\n}\n\n.info-icon {\n  margin-right: 8rpx;\n  color: #1677FF;\n}\n\n.qrcode-actions {\n  display: flex;\n  justify-content: space-around;\n  border-top: 1rpx solid #EEEEEE;\n  padding-top: 20rpx;\n}\n\n.qrcode-btn {\n  flex: 1;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  padding: 20rpx 0;\n  font-size: 28rpx;\n  color: #1677FF;\n}\n\n.copy-btn {\n  border-right: 1rpx solid #EEEEEE;\n}\n\n.btn-icon {\n  margin-right: 8rpx;\n  color: #1677FF;\n}\n</style> ", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/carpool-package/pages/carpool/publish/success.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "computed", "onMounted", "rewardedAdService", "uni", "onUnmounted", "MiniProgramPage"], "mappings": ";;;;;;;;;;;;;AAiTA,UAAA,YAAAA,cAAAA,IAAA,EAAA;AACA,UAAA,cAAAA,cAAAA,IAAA,eAAA;AACA,UAAA,OAAAA,cAAAA,IAAA,IAAA;AAGA,UAAA,WAAAA,cAAAA,IAAA;AAAA,MACA,YAAA;AAAA,MACA,UAAA;AAAA,MACA,eAAA;AAAA,MACA,eAAA;AAAA,MACA,YAAA;AAAA,MACA,gBAAA;AAAA,MACA,aAAA;AAAA,MACA,cAAA;AAAA,MACA,QAAA;AAAA,MACA,WAAA,CAAA,QAAA,MAAA;AAAA,IACA,CAAA;AAGA,UAAA,WAAAC,cAAA,SAAA,MAAA;AACA,YAAA,UAAA;AAAA,QACA,iBAAA;AAAA,QACA,iBAAA;AAAA,QACA,gBAAA;AAAA,QACA,gBAAA;AAAA,MACA;AACA,aAAA,QAAA,YAAA,KAAA,KAAA;AAAA,IACA,CAAA;AAEA,UAAA,YAAAA,cAAA,SAAA,MAAA;AACA,aAAA,YAAA;AAAA,IACA,CAAA;AAGAC,kBAAAA,UAAA,MAAA;AACA,YAAA,QAAA;AACA,YAAA,cAAA,MAAA,MAAA,SAAA,CAAA;AACA,YAAA,UAAA,YAAA,WAAA;AAGA,UAAA,WAAA,QAAA,MAAA;AACA,oBAAA,QAAA,QAAA;AAAA,MACA;AACA,UAAA,WAAA,QAAA,MAAA;AACA,aAAA,QAAA,QAAA;AAAA,MACA;AACA,UAAA,WAAA,QAAA,IAAA;AACA,kBAAA,QAAA,QAAA;AACA;MACA;AAGAC,iCAAA,kBAAA,oBAAA;AAGAC,oBAAAA,MAAA,IAAA,iBAAA,mBAAA;AAGA,iBAAA,MAAA;AACA;MACA,GAAA,GAAA;AAAA,IACA,CAAA;AAEAC,kBAAAA,YAAA,MAAA;AAEAD,oBAAAA,MAAA,KAAA,iBAAA,mBAAA;AAGAD,iCAAA,kBAAA,QAAA;AAAA,IACA,CAAA;AAIA,UAAA,oBAAA,MAAA;AACA,aAAA;AAAA,QACA,OAAA,GAAA,SAAA,KAAA,IAAA,SAAA,MAAA,UAAA,MAAA,SAAA,MAAA,QAAA;AAAA,QACA,MAAA,kDAAA,UAAA,KAAA;AAAA,MACA;AAAA,IACA;AAGA,UAAA,mBAAA,MAAA;AAGAC,oBAAA,MAAA,MAAA,OAAA,4DAAA,WAAA,UAAA,KAAA;AAAA,IACA;AAGA,UAAA,sBAAA,CAAA,eAAA;AACAA,oBAAA,MAAA,MAAA,OAAA,4DAAA,UAAA,UAAA;AAGA,cAAA,WAAA,MAAA;AAAA,QACA,KAAA;AAEAA,wBAAAA,MAAA,UAAA;AAAA,YACA,OAAA;AAAA,YACA,SAAA;AAAA,YACA,YAAA;AAAA,UACA,CAAA;AACA;AAAA,QACA,KAAA;AACAA,wBAAAA,MAAA,UAAA;AAAA,YACA,OAAA;AAAA,YACA,SAAA;AAAA,YACA,YAAA;AAAA,UACA,CAAA;AACA;AAAA,QACA,KAAA;AACAA,wBAAAA,MAAA,UAAA;AAAA,YACA,OAAA;AAAA,YACA,SAAA;AAAA,YACA,YAAA;AAAA,UACA,CAAA;AACA;AAAA,MACA;AAAA,IAIA;AAoBA,UAAA,cAAA,YAAA;AACA,UAAA;AAEA,cAAA,cAAA,MAAAD,2BAAAA,kBAAA,qBAAA,KAAA;AACA,YAAA,CAAA,YAAA,UAAA;AACAC,wBAAAA,MAAA,UAAA;AAAA,YACA,OAAA,YAAA;AAAA,YACA,MAAA;AAAA,UACA,CAAA;AACA;AAAA,QACA;AAGA,cAAA,UAAA,MAAAD,6CAAA,oBAAA,YAAA,UAAA,KAAA;AAEA,YAAA,CAAA,SAAA;AAEAC,wBAAAA,MAAA,UAAA;AAAA,YACA,OAAA;AAAA,YACA,SAAA;AAAA,YACA,SAAA,CAAA,QAAA;AACA,kBAAA,IAAA,SAAA;AACA;cACA;AAAA,YACA;AAAA,UACA,CAAA;AAAA,QACA;AAAA,MACA,SAAA,OAAA;AACAA,sBAAA,MAAA,MAAA,SAAA,4DAAA,UAAA,KAAA;AACAA,sBAAAA,MAAA,UAAA;AAAA,UACA,OAAA;AAAA,UACA,MAAA;AAAA,QACA,CAAA;AAAA,MACA;AAAA,IACA;AAGA,UAAA,gBAAA,MAAA;AAEAA,oBAAAA,MAAA,WAAA;AAAA,QACA,KAAA,iDAAA,UAAA,KAAA;AAAA,MACA,CAAA;AAAA,IACA;AAGA,UAAA,mBAAAJ,cAAAA,IAAA,KAAA;AAGA,UAAA,gBAAAA,cAAAA,IAAA,KAAA;AACA,UAAA,WAAAA,cAAAA,IAAA,WAAA;AAGA,UAAA,cAAA,MAAA;AAEAI,oBAAAA,MAAA,MAAA,OAAA,4DAAA,WAAA;AAAA,IACA;AAGA,UAAA,gBAAA,MAAA;AACA,uBAAA,QAAA;AAAA,IACA;AAGA,UAAA,gBAAA,MAAA;AACA,uBAAA,QAAA;AAAA,IACA;AAGA,UAAA,uBAAA,MAAA;AACA;AACA;AAEA,iBAAA,MAAA;AACAA,sBAAAA,MAAA,cAAA;AAAA,UACA,iBAAA;AAAA,UACA,OAAA,CAAA,mBAAA,eAAA;AAAA,QACA,CAAA;AAAA,MACA,GAAA,GAAA;AAAA,IACA;AAGA,UAAA,iBAAA,MAAA;AACA;AACA;IACA;AAGA,UAAA,WAAA,MAAA;AACA,oBAAA,QAAA;AAAA,IACA;AAGA,UAAA,aAAA,MAAA;AACA,oBAAA,QAAA;AAAA,IACA;AAGA,UAAA,eAAA,MAAA;AACAA,oBAAAA,MAAA,iBAAA;AAAA,QACA,MAAA,SAAA;AAAA,QACA,SAAA,MAAA;AACAA,wBAAAA,MAAA,UAAA;AAAA,YACA,OAAA;AAAA,YACA,MAAA;AAAA,UACA,CAAA;AAAA,QACA;AAAA,MACA,CAAA;AAAA,IACA;AAGA,UAAA,aAAA,MAAA;AACAA,oBAAAA,MAAA,uBAAA;AAAA,QACA,UAAA;AAAA,QACA,SAAA,MAAA;AACAA,wBAAAA,MAAA,UAAA;AAAA,YACA,OAAA;AAAA,YACA,MAAA;AAAA,UACA,CAAA;AAAA,QACA;AAAA,QACA,MAAA,CAAA,QAAA;AACA,cAAA,IAAA,OAAA,QAAA,WAAA,KAAA,GAAA;AACAA,0BAAAA,MAAA,UAAA;AAAA,cACA,OAAA;AAAA,cACA,SAAA;AAAA,cACA,SAAA,CAAA,QAAA;AACA,oBAAA,IAAA,SAAA;AACAA,gCAAA,MAAA,YAAA;AAAA,gBACA;AAAA,cACA;AAAA,YACA,CAAA;AAAA,UACA;AAAA,QACA;AAAA,MACA,CAAA;AAAA,IACA;AAGA,UAAA,YAAA,MAAA;AAEAA,oBAAAA,MAAA,cAAA;AAAA,QACA,iBAAA;AAAA,MACA,CAAA;AAAA,IACA;AAGA,UAAA,aAAA,MAAA;AACAA,oBAAAA,MAAA,WAAA;AAAA,QACA,KAAA,kDAAA,UAAA,KAAA;AAAA,MACA,CAAA;AAAA,IACA;AAGA,UAAA,SAAA,MAAA;AACAA,oBAAAA,MAAA,UAAA;AAAA,QACA,KAAA;AAAA,MACA,CAAA;AAAA,IACA;AAGA,UAAA,eAAA,MAAA;AACAA,oBAAAA,MAAA,WAAA;AAAA,QACA,KAAA;AAAA,MACA,CAAA;AAAA,IACA;AAGA,UAAA,gBAAA,MAAA;AACAA,oBAAAA,MAAA,WAAA;AAAA,QACA,KAAA;AAAA,MACA,CAAA;AAAA,IACA;AAGA,aAAA;AAAA,MACA;AAAA,IACA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACzmBA,GAAG,WAAWE,SAAe;"}