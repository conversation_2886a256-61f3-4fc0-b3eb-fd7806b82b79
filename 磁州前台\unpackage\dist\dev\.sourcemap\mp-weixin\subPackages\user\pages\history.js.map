{"version": 3, "file": "history.js", "sources": ["subPackages/user/pages/history.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/c3ViUGFja2FnZXNcdXNlclxwYWdlc1xoaXN0b3J5LnZ1ZQ"], "sourcesContent": ["<template>\r\n  <view class=\"history-container\">\r\n    <!-- 自定义导航栏 -->\r\n    <view class=\"custom-navbar\" :style=\"{ paddingTop: statusBarHeight + 'px' }\">\r\n      <view class=\"navbar-left\" @click=\"goBack\">\r\n        <image src=\"/static/images/tabbar/返回键.png\" class=\"back-icon\"></image>\r\n      </view>\r\n      <view class=\"navbar-title\">浏览记录</view>\r\n      <view class=\"navbar-right\" @click=\"clearHistory\">\r\n        <text class=\"clear-text\">清空</text>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 浏览记录列表 -->\r\n    <view class=\"history-content\" :style=\"{ paddingTop: navbarHeight + 'px' }\">\r\n      <view v-if=\"historyList.length > 0\" class=\"history-list\">\r\n        <!-- 日期分组 -->\r\n        <view class=\"date-group\" v-for=\"(group, date) in groupedHistory\" :key=\"date\">\r\n          <view class=\"date-header\">\r\n            <text class=\"date-text\">{{ formatDate(date) }}</text>\r\n          </view>\r\n          \r\n          <!-- 浏览记录项 -->\r\n          <view class=\"history-item\" v-for=\"(item, index) in group\" :key=\"index\" @click=\"viewDetail(item)\">\r\n            <view class=\"history-left\">\r\n              <image class=\"history-image\" :src=\"item.image\" mode=\"aspectFill\"></image>\r\n            </view>\r\n            <view class=\"history-right\">\r\n              <view class=\"history-title\">{{ item.title }}</view>\r\n              <view class=\"history-desc\">{{ item.desc }}</view>\r\n              <view class=\"history-meta\">\r\n                <text class=\"history-type\">{{ item.type }}</text>\r\n                <text class=\"history-time\">{{ formatTime(item.time) }}</text>\r\n              </view>\r\n            </view>\r\n          </view>\r\n        </view>\r\n        \r\n        <!-- 底部提示 -->\r\n        <view class=\"list-bottom\">没有更多了</view>\r\n      </view>\r\n      <view v-else class=\"empty-view\">\r\n        <image class=\"empty-icon\" src=\"/static/images/empty.png\" mode=\"aspectFit\"></image>\r\n        <view class=\"empty-text\">暂无浏览记录</view>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 清空确认弹窗 -->\r\n    <view class=\"clear-popup\" v-if=\"showClearConfirm\" @click=\"cancelClear\">\r\n      <view class=\"clear-dialog\" @click.stop>\r\n        <view class=\"clear-title\">确认清空浏览记录？</view>\r\n        <view class=\"clear-desc\">清空后将无法恢复</view>\r\n        <view class=\"clear-actions\">\r\n          <view class=\"clear-btn cancel\" @click=\"cancelClear\">取消</view>\r\n          <view class=\"clear-btn confirm\" @click=\"confirmClear\">确认清空</view>\r\n        </view>\r\n      </view>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script setup>\r\nimport { ref, computed, onMounted } from 'vue';\r\n\r\n// Vue 3 Composition API 代码开始\r\n// 状态栏高度\r\nconst statusBarHeight = ref(20);\r\nconst navbarHeight = ref(64); // 导航栏高度\r\nconst historyList = ref([]);\r\nconst showClearConfirm = ref(false);\r\n\r\n// 按日期分组的计算属性\r\nconst groupedHistory = computed(() => {\r\n  const groups = {};\r\n  \r\n  historyList.value.forEach(item => {\r\n    // 提取日期部分\r\n    const date = new Date(item.time).toISOString().slice(0, 10);\r\n    \r\n    if (!groups[date]) {\r\n      groups[date] = [];\r\n    }\r\n    \r\n    groups[date].push(item);\r\n  });\r\n  \r\n  return groups;\r\n});\r\n\r\n// 生命周期钩子\r\nonMounted(() => {\r\n  // 获取状态栏高度\r\n  const sysInfo = uni.getSystemInfoSync();\r\n  statusBarHeight.value = sysInfo.statusBarHeight;\r\n  navbarHeight.value = statusBarHeight.value + 44;\r\n  \r\n  // 加载浏览记录\r\n  loadHistoryData();\r\n});\r\n\r\n// 返回上一页\r\nconst goBack = () => {\r\n  uni.navigateBack();\r\n};\r\n\r\n// 加载浏览记录数据\r\nconst loadHistoryData = () => {\r\n  // 模拟请求延迟\r\n  setTimeout(() => {\r\n    // 模拟数据\r\n    historyList.value = [\r\n      // 今天\r\n      {\r\n        id: 'history_1',\r\n        title: '专业家政保洁服务',\r\n        desc: '专业保洁，上门服务，价格实惠',\r\n        image: '/static/images/service1.jpg',\r\n        type: '服务',\r\n        time: new Date().toISOString()\r\n      },\r\n      {\r\n        id: 'history_2',\r\n        title: '黄金地段餐饮店整体转让',\r\n        desc: '位于商业街黄金地段，客流稳定，接手即可盈利',\r\n        image: '/static/images/shop1.jpg',\r\n        type: '生意转让',\r\n        time: new Date().toISOString()\r\n      },\r\n      \r\n      // 昨天\r\n      {\r\n        id: 'history_3',\r\n        title: '求职行政文员',\r\n        desc: '有1年工作经验，熟悉办公软件，可立即上岗',\r\n        image: '/static/images/avatar.png',\r\n        type: '求职信息',\r\n        time: new Date(Date.now() - 86400000).toISOString()\r\n      },\r\n      {\r\n        id: 'history_4',\r\n        title: '餐饮店急招服务员',\r\n        desc: '工作轻松，待遇优厚，提供食宿',\r\n        image: '/static/images/service2.jpg',\r\n        type: '招聘信息',\r\n        time: new Date(Date.now() - 86400000).toISOString()\r\n      },\r\n      \r\n      // 前天\r\n      {\r\n        id: 'history_5',\r\n        title: '市中心两室一厅出租',\r\n        desc: '位于市中心，交通便利，家具家电齐全',\r\n        image: '/static/images/house1.jpg',\r\n        type: '房屋出租',\r\n        time: new Date(Date.now() - 86400000 * 2).toISOString()\r\n      },\r\n      {\r\n        id: 'history_6',\r\n        title: '二手苹果手机出售',\r\n        desc: 'iPhone 13 Pro，128G，成色新，无拆修',\r\n        image: '/static/images/phone.jpg',\r\n        type: '二手闲置',\r\n        time: new Date(Date.now() - 86400000 * 2).toISOString()\r\n      }\r\n    ];\r\n  }, 500);\r\n};\r\n\r\n// 格式化日期显示\r\nconst formatDate = (dateStr) => {\r\n  const date = new Date(dateStr);\r\n  const today = new Date();\r\n  const yesterday = new Date(today);\r\n  yesterday.setDate(yesterday.getDate() - 1);\r\n  \r\n  if (date.toDateString() === today.toDateString()) {\r\n    return '今天';\r\n  } else if (date.toDateString() === yesterday.toDateString()) {\r\n    return '昨天';\r\n  } else {\r\n    return `${date.getMonth() + 1}月${date.getDate()}日`;\r\n  }\r\n};\r\n\r\n// 格式化时间显示\r\nconst formatTime = (timeStr) => {\r\n  const date = new Date(timeStr);\r\n  const hours = date.getHours().toString().padStart(2, '0');\r\n  const minutes = date.getMinutes().toString().padStart(2, '0');\r\n  \r\n  return `${hours}:${minutes}`;\r\n};\r\n\r\n// 查看详情\r\nconst viewDetail = (item) => {\r\n  let url = '';\r\n  \r\n  // 根据不同类型跳转到不同页面\r\n  switch(item.type) {\r\n    case '服务':\r\n      url = `/pages/publish/home-service-detail?id=${item.id}`;\r\n      break;\r\n    case '生意转让':\r\n      url = `/pages/publish/business-transfer-detail?id=${item.id}`;\r\n      break;\r\n    case '求职信息':\r\n      url = `/pages/publish/job-seeking-detail?id=${item.id}`;\r\n      break;\r\n    case '招聘信息':\r\n      url = `/pages/publish/job-detail?id=${item.id}`;\r\n      break;\r\n    case '房屋出租':\r\n      url = `/pages/publish/house-rent-detail?id=${item.id}`;\r\n      break;\r\n    case '二手闲置':\r\n      url = `/pages/publish/second-hand-detail?id=${item.id}`;\r\n      break;\r\n    default:\r\n      url = `/pages/publish/info-detail?id=${item.id}`;\r\n  }\r\n  \r\n  uni.navigateTo({ url });\r\n};\r\n\r\n// 清空历史记录\r\nconst clearHistory = () => {\r\n  showClearConfirm.value = true;\r\n};\r\n\r\n// 取消清空\r\nconst cancelClear = () => {\r\n  showClearConfirm.value = false;\r\n};\r\n\r\n// 确认清空\r\nconst confirmClear = () => {\r\n  historyList.value = [];\r\n  showClearConfirm.value = false;\r\n  \r\n  uni.showToast({\r\n    title: '已清空浏览记录',\r\n    icon: 'success'\r\n  });\r\n};\r\n// Vue 3 Composition API 代码结束\r\n</script>\r\n\r\n<style>\r\n.history-container {\r\n  min-height: 100vh;\r\n  background-color: #f5f7fa;\r\n  position: relative;\r\n}\r\n\r\n/* 自定义导航栏 */\r\n.custom-navbar {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  height: 44px;\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 0 15px;\r\n  background-color: #0052CC;\r\n  color: #fff;\r\n  z-index: 100;\r\n}\r\n\r\n.navbar-left {\r\n  width: 80rpx;\r\n  height: 44px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.navbar-title {\r\n  flex: 1;\r\n  text-align: center;\r\n  font-size: 16px;\r\n  font-weight: 500;\r\n}\r\n\r\n.navbar-right {\r\n  width: 80rpx;\r\n  text-align: right;\r\n}\r\n\r\n.clear-text {\r\n  font-size: 14px;\r\n  color: #fff;\r\n}\r\n\r\n.back-icon {\r\n  width: 40rpx;\r\n  height: 40rpx;\r\n}\r\n\r\n/* 历史记录内容 */\r\n.history-content {\r\n  padding: 15px;\r\n}\r\n\r\n/* 日期分组 */\r\n.date-group {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.date-header {\r\n  padding: 10px 5px;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.date-text {\r\n  font-size: 14px;\r\n  color: #666;\r\n  font-weight: 500;\r\n}\r\n\r\n/* 历史记录项 */\r\n.history-item {\r\n  display: flex;\r\n  padding: 15px;\r\n  background-color: #fff;\r\n  border-radius: 8px;\r\n  margin-bottom: 15px;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n.history-left {\r\n  width: 140rpx;\r\n  height: 140rpx;\r\n  margin-right: 15px;\r\n}\r\n\r\n.history-image {\r\n  width: 100%;\r\n  height: 100%;\r\n  border-radius: 4px;\r\n}\r\n\r\n.history-right {\r\n  flex: 1;\r\n  display: flex;\r\n  flex-direction: column;\r\n  justify-content: space-between;\r\n}\r\n\r\n.history-title {\r\n  font-size: 16px;\r\n  font-weight: 500;\r\n  color: #333;\r\n  margin-bottom: 8px;\r\n  line-height: 1.4;\r\n  display: -webkit-box;\r\n  -webkit-line-clamp: 1;\r\n  -webkit-box-orient: vertical;\r\n  overflow: hidden;\r\n}\r\n\r\n.history-desc {\r\n  font-size: 14px;\r\n  color: #666;\r\n  line-height: 1.4;\r\n  margin-bottom: 10px;\r\n  display: -webkit-box;\r\n  -webkit-line-clamp: 2;\r\n  -webkit-box-orient: vertical;\r\n  overflow: hidden;\r\n}\r\n\r\n.history-meta {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  font-size: 12px;\r\n}\r\n\r\n.history-type {\r\n  color: #0052CC;\r\n  background-color: rgba(0, 82, 204, 0.1);\r\n  padding: 2px 8px;\r\n  border-radius: 4px;\r\n}\r\n\r\n.history-time {\r\n  color: #999;\r\n}\r\n\r\n/* 空状态 */\r\n.empty-view {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  padding-top: 100px;\r\n}\r\n\r\n.empty-icon {\r\n  width: 200rpx;\r\n  height: 200rpx;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.empty-text {\r\n  font-size: 14px;\r\n  color: #999;\r\n}\r\n\r\n/* 列表底部 */\r\n.list-bottom {\r\n  text-align: center;\r\n  padding: 15px 0;\r\n  font-size: 14px;\r\n  color: #999;\r\n}\r\n\r\n/* 清空确认弹窗 */\r\n.clear-popup {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background-color: rgba(0, 0, 0, 0.5);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  z-index: 101;\r\n}\r\n\r\n.clear-dialog {\r\n  width: 80%;\r\n  background-color: #fff;\r\n  border-radius: 12px;\r\n  padding: 20px;\r\n}\r\n\r\n.clear-title {\r\n  font-size: 18px;\r\n  font-weight: 500;\r\n  color: #333;\r\n  text-align: center;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.clear-desc {\r\n  font-size: 14px;\r\n  color: #999;\r\n  text-align: center;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.clear-actions {\r\n  display: flex;\r\n  border-top: 1px solid #eee;\r\n  padding-top: 15px;\r\n}\r\n\r\n.clear-btn {\r\n  flex: 1;\r\n  text-align: center;\r\n  height: 40px;\r\n  line-height: 40px;\r\n  font-size: 16px;\r\n}\r\n\r\n.cancel {\r\n  color: #999;\r\n}\r\n\r\n.confirm {\r\n  color: #ff4d4f;\r\n  font-weight: 500;\r\n}\r\n</style> ", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/subPackages/user/pages/history.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "computed", "onMounted", "uni", "MiniProgramPage"], "mappings": ";;;;;;AAkEA,UAAM,kBAAkBA,cAAAA,IAAI,EAAE;AAC9B,UAAM,eAAeA,cAAAA,IAAI,EAAE;AAC3B,UAAM,cAAcA,cAAAA,IAAI,CAAA,CAAE;AAC1B,UAAM,mBAAmBA,cAAAA,IAAI,KAAK;AAGlC,UAAM,iBAAiBC,cAAQ,SAAC,MAAM;AACpC,YAAM,SAAS,CAAA;AAEf,kBAAY,MAAM,QAAQ,UAAQ;AAEhC,cAAM,OAAO,IAAI,KAAK,KAAK,IAAI,EAAE,YAAW,EAAG,MAAM,GAAG,EAAE;AAE1D,YAAI,CAAC,OAAO,IAAI,GAAG;AACjB,iBAAO,IAAI,IAAI;QAChB;AAED,eAAO,IAAI,EAAE,KAAK,IAAI;AAAA,MAC1B,CAAG;AAED,aAAO;AAAA,IACT,CAAC;AAGDC,kBAAAA,UAAU,MAAM;AAEd,YAAM,UAAUC,oBAAI;AACpB,sBAAgB,QAAQ,QAAQ;AAChC,mBAAa,QAAQ,gBAAgB,QAAQ;AAG7C;IACF,CAAC;AAGD,UAAM,SAAS,MAAM;AACnBA,oBAAG,MAAC,aAAY;AAAA,IAClB;AAGA,UAAM,kBAAkB,MAAM;AAE5B,iBAAW,MAAM;AAEf,oBAAY,QAAQ;AAAA;AAAA,UAElB;AAAA,YACE,IAAI;AAAA,YACJ,OAAO;AAAA,YACP,MAAM;AAAA,YACN,OAAO;AAAA,YACP,MAAM;AAAA,YACN,OAAM,oBAAI,KAAM,GAAC,YAAa;AAAA,UAC/B;AAAA,UACD;AAAA,YACE,IAAI;AAAA,YACJ,OAAO;AAAA,YACP,MAAM;AAAA,YACN,OAAO;AAAA,YACP,MAAM;AAAA,YACN,OAAM,oBAAI,KAAM,GAAC,YAAa;AAAA,UAC/B;AAAA;AAAA,UAGD;AAAA,YACE,IAAI;AAAA,YACJ,OAAO;AAAA,YACP,MAAM;AAAA,YACN,OAAO;AAAA,YACP,MAAM;AAAA,YACN,MAAM,IAAI,KAAK,KAAK,IAAG,IAAK,KAAQ,EAAE,YAAa;AAAA,UACpD;AAAA,UACD;AAAA,YACE,IAAI;AAAA,YACJ,OAAO;AAAA,YACP,MAAM;AAAA,YACN,OAAO;AAAA,YACP,MAAM;AAAA,YACN,MAAM,IAAI,KAAK,KAAK,IAAG,IAAK,KAAQ,EAAE,YAAa;AAAA,UACpD;AAAA;AAAA,UAGD;AAAA,YACE,IAAI;AAAA,YACJ,OAAO;AAAA,YACP,MAAM;AAAA,YACN,OAAO;AAAA,YACP,MAAM;AAAA,YACN,MAAM,IAAI,KAAK,KAAK,IAAK,IAAG,QAAW,CAAC,EAAE,YAAa;AAAA,UACxD;AAAA,UACD;AAAA,YACE,IAAI;AAAA,YACJ,OAAO;AAAA,YACP,MAAM;AAAA,YACN,OAAO;AAAA,YACP,MAAM;AAAA,YACN,MAAM,IAAI,KAAK,KAAK,IAAK,IAAG,QAAW,CAAC,EAAE,YAAa;AAAA,UACxD;AAAA,QACP;AAAA,MACG,GAAE,GAAG;AAAA,IACR;AAGA,UAAM,aAAa,CAAC,YAAY;AAC9B,YAAM,OAAO,IAAI,KAAK,OAAO;AAC7B,YAAM,QAAQ,oBAAI;AAClB,YAAM,YAAY,IAAI,KAAK,KAAK;AAChC,gBAAU,QAAQ,UAAU,QAAS,IAAG,CAAC;AAEzC,UAAI,KAAK,aAAY,MAAO,MAAM,aAAY,GAAI;AAChD,eAAO;AAAA,MACR,WAAU,KAAK,aAAc,MAAK,UAAU,aAAY,GAAI;AAC3D,eAAO;AAAA,MACX,OAAS;AACL,eAAO,GAAG,KAAK,aAAa,CAAC,IAAI,KAAK,SAAS;AAAA,MAChD;AAAA,IACH;AAGA,UAAM,aAAa,CAAC,YAAY;AAC9B,YAAM,OAAO,IAAI,KAAK,OAAO;AAC7B,YAAM,QAAQ,KAAK,SAAU,EAAC,SAAQ,EAAG,SAAS,GAAG,GAAG;AACxD,YAAM,UAAU,KAAK,WAAY,EAAC,SAAQ,EAAG,SAAS,GAAG,GAAG;AAE5D,aAAO,GAAG,KAAK,IAAI,OAAO;AAAA,IAC5B;AAGA,UAAM,aAAa,CAAC,SAAS;AAC3B,UAAI,MAAM;AAGV,cAAO,KAAK,MAAI;AAAA,QACd,KAAK;AACH,gBAAM,yCAAyC,KAAK,EAAE;AACtD;AAAA,QACF,KAAK;AACH,gBAAM,8CAA8C,KAAK,EAAE;AAC3D;AAAA,QACF,KAAK;AACH,gBAAM,wCAAwC,KAAK,EAAE;AACrD;AAAA,QACF,KAAK;AACH,gBAAM,gCAAgC,KAAK,EAAE;AAC7C;AAAA,QACF,KAAK;AACH,gBAAM,uCAAuC,KAAK,EAAE;AACpD;AAAA,QACF,KAAK;AACH,gBAAM,wCAAwC,KAAK,EAAE;AACrD;AAAA,QACF;AACE,gBAAM,iCAAiC,KAAK,EAAE;AAAA,MACjD;AAEDA,oBAAAA,MAAI,WAAW,EAAE,IAAG,CAAE;AAAA,IACxB;AAGA,UAAM,eAAe,MAAM;AACzB,uBAAiB,QAAQ;AAAA,IAC3B;AAGA,UAAM,cAAc,MAAM;AACxB,uBAAiB,QAAQ;AAAA,IAC3B;AAGA,UAAM,eAAe,MAAM;AACzB,kBAAY,QAAQ;AACpB,uBAAiB,QAAQ;AAEzBA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,MACV,CAAG;AAAA,IACH;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AClPA,GAAG,WAAWC,SAAe;"}