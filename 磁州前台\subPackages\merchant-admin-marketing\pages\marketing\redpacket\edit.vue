<template>
  <view class="page-container">
    <!-- 自定义导航栏 -->
    <view class="navbar">
      <view class="navbar-back" @click="goBack">
        <view class="back-icon"></view>
      </view>
      <text class="navbar-title">编辑红包</text>
      <view class="navbar-right">
        <view class="help-icon" @click="showHelp">?</view>
      </view>
    </view>
    
    <!-- 表单容器 -->
    <view class="form-container">
      <!-- 步骤指示器 -->
      <view class="step-indicator">
        <view class="step-item" :class="{ active: currentStep >= 1, completed: currentStep > 1 }">
          <view class="step-number">1</view>
          <text class="step-text">基本信息</text>
        </view>
        <view class="step-line" :class="{ active: currentStep > 1 }"></view>
        <view class="step-item" :class="{ active: currentStep >= 2, completed: currentStep > 2 }">
          <view class="step-number">2</view>
          <text class="step-text">红包设置</text>
        </view>
        <view class="step-line" :class="{ active: currentStep > 2 }"></view>
        <view class="step-item" :class="{ active: currentStep >= 3 }">
          <view class="step-number">3</view>
          <text class="step-text">发放规则</text>
        </view>
      </view>
      
      <!-- 步骤1：基本信息 -->
      <view class="form-step" v-if="currentStep === 1">
        <view class="form-group">
          <view class="form-label">红包名称</view>
          <input class="form-input" type="text" v-model="formData.name" placeholder="请输入红包活动名称" />
        </view>
        
        <view class="form-group">
          <view class="form-label">活动时间</view>
          <view class="date-range-picker" @click="showDateRangePicker">
            <text class="date-range-text">{{formData.startDate}} ~ {{formData.endDate}}</text>
            <view class="picker-arrow"></view>
          </view>
        </view>
        
        <view class="form-group">
          <view class="form-label">红包类型</view>
          <view class="radio-group">
            <view 
              class="radio-item" 
              v-for="(type, index) in redpacketTypes" 
              :key="index"
              :class="{ active: formData.type === type.value }"
              @click="selectRedpacketType(type.value)">
              <view class="radio-icon" :class="{ active: formData.type === type.value }"></view>
              <text class="radio-text">{{type.label}}</text>
            </view>
          </view>
        </view>
        
        <view class="form-group">
          <view class="form-label">红包封面</view>
          <view class="cover-uploader" @click="uploadCover">
            <image v-if="formData.coverUrl" class="cover-preview" :src="formData.coverUrl" mode="aspectFill"></image>
            <view v-else class="upload-placeholder">
              <view class="upload-icon">+</view>
              <text class="upload-text">上传封面图</text>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 步骤2：红包设置 -->
      <view class="form-step" v-if="currentStep === 2">
        <view class="form-group">
          <view class="form-label">红包金额类型</view>
          <view class="radio-group">
            <view 
              class="radio-item" 
              v-for="(type, index) in amountTypes" 
              :key="index"
              :class="{ active: formData.amountType === type.value }"
              @click="selectAmountType(type.value)">
              <view class="radio-icon" :class="{ active: formData.amountType === type.value }"></view>
              <text class="radio-text">{{type.label}}</text>
            </view>
          </view>
        </view>
        
        <view class="form-group" v-if="formData.amountType === 'fixed'">
          <view class="form-label">红包金额 (元)</view>
          <input class="form-input" type="digit" v-model="formData.fixedAmount" placeholder="请输入红包金额" />
        </view>
        
        <view class="form-group" v-if="formData.amountType === 'random'">
          <view class="form-label">最小金额 (元)</view>
          <input class="form-input" type="digit" v-model="formData.minAmount" placeholder="请输入最小金额" />
        </view>
        
        <view class="form-group" v-if="formData.amountType === 'random'">
          <view class="form-label">最大金额 (元)</view>
          <input class="form-input" type="digit" v-model="formData.maxAmount" placeholder="请输入最大金额" />
        </view>
        
        <view class="form-group">
          <view class="form-label">红包总数量</view>
          <input class="form-input" type="number" v-model="formData.totalCount" placeholder="请输入红包总数量" />
        </view>
        
        <view class="form-group">
          <view class="form-label">单用户领取上限</view>
          <input class="form-input" type="number" v-model="formData.userLimit" placeholder="请输入单用户领取上限" />
        </view>
        
        <view class="form-group">
          <view class="form-label">红包有效期</view>
          <view class="validity-selector">
            <view 
              class="validity-option" 
              v-for="(option, index) in validityOptions" 
              :key="index"
              :class="{ active: formData.validity === option.value }"
              @click="selectValidity(option.value)">
              {{option.label}}
            </view>
          </view>
        </view>
      </view>
      
      <!-- 步骤3：发放规则 -->
      <view class="form-step" v-if="currentStep === 3">
        <view class="form-group">
          <view class="form-label">发放对象</view>
          <view class="radio-group">
            <view 
              class="radio-item" 
              v-for="(target, index) in targetOptions" 
              :key="index"
              :class="{ active: formData.target === target.value }"
              @click="selectTarget(target.value)">
              <view class="radio-icon" :class="{ active: formData.target === target.value }"></view>
              <text class="radio-text">{{target.label}}</text>
            </view>
          </view>
        </view>
        
        <view class="form-group" v-if="formData.target === 'member'">
          <view class="form-label">会员等级</view>
          <view class="checkbox-group">
            <view 
              class="checkbox-item" 
              v-for="(level, index) in memberLevels" 
              :key="index"
              :class="{ active: formData.memberLevels.includes(level.value) }"
              @click="toggleMemberLevel(level.value)">
              <view class="checkbox-icon" :class="{ active: formData.memberLevels.includes(level.value) }"></view>
              <text class="checkbox-text">{{level.label}}</text>
            </view>
          </view>
        </view>
        
        <view class="form-group">
          <view class="form-label">使用门槛</view>
          <view class="radio-group">
            <view 
              class="radio-item" 
              v-for="(threshold, index) in thresholdOptions" 
              :key="index"
              :class="{ active: formData.threshold === threshold.value }"
              @click="selectThreshold(threshold.value)">
              <view class="radio-icon" :class="{ active: formData.threshold === threshold.value }"></view>
              <text class="radio-text">{{threshold.label}}</text>
            </view>
          </view>
        </view>
        
        <view class="form-group" v-if="formData.threshold === 'minimum'">
          <view class="form-label">最低消费金额 (元)</view>
          <input class="form-input" type="digit" v-model="formData.minimumAmount" placeholder="请输入最低消费金额" />
        </view>
        
        <view class="form-group">
          <view class="form-label">使用说明</view>
          <textarea class="form-textarea" v-model="formData.description" placeholder="请输入红包使用说明"></textarea>
        </view>
      </view>
      
      <!-- 底部按钮 -->
      <view class="form-actions">
        <view class="action-btn back" v-if="currentStep > 1" @click="prevStep">上一步</view>
        <view class="action-btn next" v-if="currentStep < 3" @click="nextStep">下一步</view>
        <view class="action-btn submit" v-if="currentStep === 3" @click="submitForm">保存</view>
        <view class="action-btn cancel" @click="cancelEdit">取消</view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      currentStep: 1,
      formData: {
        id: 1,
        name: '新用户专享红包',
        startDate: '2023-05-01',
        endDate: '2023-05-31',
        type: 'normal',
        coverUrl: '/static/images/redpacket/cover-placeholder.jpg',
        amountType: 'fixed',
        fixedAmount: '10.00',
        minAmount: '',
        maxAmount: '',
        totalCount: '1000',
        userLimit: '1',
        validity: '7',
        target: 'all',
        memberLevels: [],
        threshold: 'none',
        minimumAmount: '',
        description: '1. 每位用户限领1个红包\n2. 红包领取后7天内有效\n3. 红包可在下单时直接抵扣\n4. 不可与其他优惠同时使用\n5. 最终解释权归商家所有'
      },
      
      // 红包类型选项
      redpacketTypes: [
        { label: '普通红包', value: 'normal' },
        { label: '裂变红包', value: 'fission' },
        { label: '群发红包', value: 'mass' },
        { label: '红包雨', value: 'rain' }
      ],
      
      // 金额类型选项
      amountTypes: [
        { label: '固定金额', value: 'fixed' },
        { label: '随机金额', value: 'random' }
      ],
      
      // 有效期选项
      validityOptions: [
        { label: '3天', value: '3' },
        { label: '7天', value: '7' },
        { label: '15天', value: '15' },
        { label: '30天', value: '30' }
      ],
      
      // 发放对象选项
      targetOptions: [
        { label: '所有用户', value: 'all' },
        { label: '新用户', value: 'new' },
        { label: '会员用户', value: 'member' }
      ],
      
      // 会员等级选项
      memberLevels: [
        { label: '普通会员', value: 'normal' },
        { label: '银卡会员', value: 'silver' },
        { label: '金卡会员', value: 'gold' },
        { label: '钻石会员', value: 'diamond' }
      ],
      
      // 使用门槛选项
      thresholdOptions: [
        { label: '无门槛', value: 'none' },
        { label: '满额可用', value: 'minimum' }
      ]
    }
  },
  onLoad(options) {
    // 在实际应用中，这里会根据传入的红包ID从服务器获取红包详情
    if (options && options.id) {
      this.loadRedpacketData(options.id);
    }
  },
  methods: {
    loadRedpacketData(id) {
      // 模拟从服务器获取数据
      // 实际应用中应该发起网络请求获取红包详情
      console.log('加载红包ID:', id);
      // 这里使用的是预设数据，实际应用中应该替换为从服务器获取的数据
    },
    goBack() {
      uni.navigateBack();
    },
    showHelp() {
      uni.showModal({
        title: '编辑红包帮助',
        content: '在此页面您可以修改红包活动的各项参数，包括基本信息、红包设置和发放规则。',
        showCancel: false
      });
    },
    showDateRangePicker() {
      // 显示日期范围选择器
      uni.showToast({
        title: '日期选择功能开发中',
        icon: 'none'
      });
    },
    selectRedpacketType(type) {
      this.formData.type = type;
    },
    uploadCover() {
      uni.chooseImage({
        count: 1,
        success: (res) => {
          this.formData.coverUrl = res.tempFilePaths[0];
        }
      });
    },
    selectAmountType(type) {
      this.formData.amountType = type;
    },
    selectValidity(validity) {
      this.formData.validity = validity;
    },
    selectTarget(target) {
      this.formData.target = target;
    },
    toggleMemberLevel(level) {
      const index = this.formData.memberLevels.indexOf(level);
      if (index === -1) {
        this.formData.memberLevels.push(level);
      } else {
        this.formData.memberLevels.splice(index, 1);
      }
    },
    selectThreshold(threshold) {
      this.formData.threshold = threshold;
    },
    nextStep() {
      if (this.currentStep < 3) {
        this.currentStep++;
      }
    },
    prevStep() {
      if (this.currentStep > 1) {
        this.currentStep--;
      }
    },
    submitForm() {
      // 表单验证
      if (!this.validateForm()) {
        return;
      }
      
      // 提交表单
      uni.showLoading({
        title: '保存中...'
      });
      
      // 模拟提交
      setTimeout(() => {
        uni.hideLoading();
        uni.showModal({
          title: '保存成功',
          content: '红包活动已更新成功',
          showCancel: false,
          success: (res) => {
            if (res.confirm) {
              uni.navigateBack();
            }
          }
        });
      }, 1500);
    },
    cancelEdit() {
      uni.showModal({
        title: '确认取消',
        content: '确定要取消编辑吗？未保存的修改将丢失。',
        success: (res) => {
          if (res.confirm) {
            uni.navigateBack();
          }
        }
      });
    },
    validateForm() {
      // 简单表单验证
      if (!this.formData.name) {
        uni.showToast({
          title: '请输入红包名称',
          icon: 'none'
        });
        return false;
      }
      
      if (this.formData.amountType === 'fixed' && !this.formData.fixedAmount) {
        uni.showToast({
          title: '请输入红包金额',
          icon: 'none'
        });
        return false;
      }
      
      if (this.formData.amountType === 'random') {
        if (!this.formData.minAmount || !this.formData.maxAmount) {
          uni.showToast({
            title: '请输入红包金额范围',
            icon: 'none'
          });
          return false;
        }
        
        if (parseFloat(this.formData.minAmount) >= parseFloat(this.formData.maxAmount)) {
          uni.showToast({
            title: '最小金额必须小于最大金额',
            icon: 'none'
          });
          return false;
        }
      }
      
      if (!this.formData.totalCount) {
        uni.showToast({
          title: '请输入红包总数量',
          icon: 'none'
        });
        return false;
      }
      
      if (this.formData.target === 'member' && this.formData.memberLevels.length === 0) {
        uni.showToast({
          title: '请选择会员等级',
          icon: 'none'
        });
        return false;
      }
      
      if (this.formData.threshold === 'minimum' && !this.formData.minimumAmount) {
        uni.showToast({
          title: '请输入最低消费金额',
          icon: 'none'
        });
        return false;
      }
      
      return true;
    }
  }
}
</script>

<style lang="scss">
.page-container {
  min-height: 100vh;
  background-color: #F5F7FA;
  padding-bottom: env(safe-area-inset-bottom);
}

/* 导航栏样式 */
.navbar {
  position: sticky;
  top: 0;
  left: 0;
  right: 0;
  background: linear-gradient(135deg, #FF5858, #FF0000);
  color: #fff;
  padding: 44px 16px 15px;
  display: flex;
  align-items: center;
  z-index: 100;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.15);
}

.navbar-back {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-icon {
  width: 12px;
  height: 12px;
  border-left: 2px solid #fff;
  border-bottom: 2px solid #fff;
  transform: rotate(45deg);
}

.navbar-title {
  flex: 1;
  text-align: center;
  font-size: 18px;
  font-weight: 600;
  letter-spacing: 0.5px;
}

.navbar-right {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.help-icon {
  width: 24px;
  height: 24px;
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: bold;
}

/* 表单容器 */
.form-container {
  padding: 15px;
}

/* 步骤指示器 */
.step-indicator {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20px;
  padding: 15px;
  background-color: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.step-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.step-number {
  width: 28px;
  height: 28px;
  border-radius: 14px;
  background-color: #eee;
  color: #999;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  margin-bottom: 5px;
}

.step-item.active .step-number {
  background-color: #FF5858;
  color: #fff;
}

.step-item.completed .step-number {
  background-color: #4ECDC4;
  color: #fff;
}

.step-text {
  font-size: 12px;
  color: #999;
}

.step-item.active .step-text,
.step-item.completed .step-text {
  color: #333;
  font-weight: 500;
}

.step-line {
  flex: 1;
  height: 1px;
  background-color: #eee;
  margin: 0 10px;
}

.step-line.active {
  background-color: #4ECDC4;
}

/* 表单步骤 */
.form-step {
  background-color: #fff;
  border-radius: 12px;
  padding: 15px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  margin-bottom: 15px;
}

.form-group {
  margin-bottom: 15px;
}

.form-label {
  font-size: 14px;
  color: #333;
  margin-bottom: 8px;
  font-weight: 500;
}

.form-input {
  width: 100%;
  height: 44px;
  background-color: #F5F7FA;
  border-radius: 8px;
  padding: 0 12px;
  font-size: 14px;
  color: #333;
}

.form-textarea {
  width: 100%;
  height: 100px;
  background-color: #F5F7FA;
  border-radius: 8px;
  padding: 10px 12px;
  font-size: 14px;
  color: #333;
}

/* 日期范围选择器 */
.date-range-picker {
  height: 44px;
  background-color: #F5F7FA;
  border-radius: 8px;
  padding: 0 12px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.date-range-text {
  font-size: 14px;
  color: #333;
}

.picker-arrow {
  width: 10px;
  height: 10px;
  border-right: 1px solid #999;
  border-bottom: 1px solid #999;
  transform: rotate(45deg);
}

/* 单选按钮组 */
.radio-group {
  display: flex;
  flex-wrap: wrap;
}

.radio-item {
  display: flex;
  align-items: center;
  margin-right: 20px;
  margin-bottom: 10px;
}

.radio-icon {
  width: 18px;
  height: 18px;
  border-radius: 9px;
  border: 1px solid #ddd;
  margin-right: 6px;
  position: relative;
}

.radio-icon.active {
  border-color: #FF5858;
}

.radio-icon.active::after {
  content: '';
  position: absolute;
  width: 10px;
  height: 10px;
  border-radius: 5px;
  background-color: #FF5858;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.radio-text {
  font-size: 14px;
  color: #333;
}

/* 封面上传 */
.cover-uploader {
  width: 100%;
  height: 180px;
  background-color: #F5F7FA;
  border-radius: 8px;
  overflow: hidden;
}

.upload-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.upload-icon {
  font-size: 30px;
  color: #ccc;
  margin-bottom: 5px;
}

.upload-text {
  font-size: 14px;
  color: #999;
}

.cover-preview {
  width: 100%;
  height: 100%;
}

/* 有效期选择器 */
.validity-selector {
  display: flex;
  flex-wrap: wrap;
}

.validity-option {
  height: 36px;
  padding: 0 15px;
  background-color: #F5F7FA;
  border-radius: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  color: #666;
  margin-right: 10px;
  margin-bottom: 10px;
}

.validity-option.active {
  background-color: #FF5858;
  color: #fff;
}

/* 复选框组 */
.checkbox-group {
  display: flex;
  flex-wrap: wrap;
}

.checkbox-item {
  display: flex;
  align-items: center;
  margin-right: 20px;
  margin-bottom: 10px;
}

.checkbox-icon {
  width: 18px;
  height: 18px;
  border-radius: 4px;
  border: 1px solid #ddd;
  margin-right: 6px;
  position: relative;
}

.checkbox-icon.active {
  background-color: #FF5858;
  border-color: #FF5858;
}

.checkbox-icon.active::after {
  content: '';
  position: absolute;
  width: 10px;
  height: 5px;
  border-left: 2px solid #fff;
  border-bottom: 2px solid #fff;
  top: 40%;
  left: 50%;
  transform: translate(-50%, -50%) rotate(-45deg);
}

.checkbox-text {
  font-size: 14px;
  color: #333;
}

/* 底部按钮 */
.form-actions {
  display: flex;
  justify-content: space-between;
  margin-top: 20px;
  margin-bottom: 30px;
}

.action-btn {
  flex: 1;
  height: 44px;
  border-radius: 22px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 15px;
  font-weight: 500;
}

.action-btn.back {
  background-color: #F5F7FA;
  color: #666;
  margin-right: 10px;
}

.action-btn.next,
.action-btn.submit {
  background: linear-gradient(135deg, #FF5858, #FF0000);
  color: #fff;
  margin-left: 10px;
}

.action-btn.cancel {
  background-color: #F5F7FA;
  color: #666;
  margin-left: 10px;
}
</style>