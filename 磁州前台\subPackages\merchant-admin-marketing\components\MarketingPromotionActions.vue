<template>
  <view class="marketing-promotion-container">
    <!-- 发布区域 -->
    <view class="promotion-section" v-if="showPublish">
      <view class="section-header">
        <text class="section-title">{{ publishModeOnly ? '选择发布方式' : '活动发布' }}</text>
        <text class="section-desc">{{ publishModeOnly ? '' : '发布活动到客户端，提升曝光率' }}</text>
      </view>
      
      <!-- 发布方式选择卡片 - 新样式 -->
      <view class="publish-options-card" v-if="publishModeOnly">
        <!-- 看广告发布 -->
        <view class="publish-option-row">
          <view class="publish-option-left">
            <view class="publish-option-icon ad-icon">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z" />
                <polyline points="7.5 4.21 12 6.81 16.5 4.21" />
                <polyline points="7.5 19.79 7.5 14.6 3 12" />
                <polyline points="21 12 16.5 14.6 16.5 19.79" />
                <polyline points="3.27 6.96 12 12.01 20.73 6.96" />
                <line x1="12" y1="22.08" x2="12" y2="12" />
              </svg>
            </view>
            <view class="publish-option-content">
              <text class="publish-option-title">看广告发布</text>
              <text class="publish-option-desc">看一个广告免费发布一天</text>
            </view>
          </view>
          <view class="publish-option-right">
            <button class="publish-btn free-btn" @click="selectDirectOption('publish', 'ad')">免费</button>
          </view>
        </view>
        
        <!-- 付费发布 -->
        <view class="publish-option-row">
          <view class="publish-option-left">
            <view class="publish-option-icon paid-icon">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24" fill="none" stroke="currentColor" stroke-width="2">
                <rect x="3" y="3" width="18" height="18" rx="2" ry="2" />
                <path d="M12 8v8M8 12h8" />
              </svg>
            </view>
            <view class="publish-option-content">
              <text class="publish-option-title">付费发布</text>
              <text class="publish-option-desc">3天/1周/1个月任选</text>
            </view>
          </view>
          <view class="publish-option-right">
            <button class="publish-btn paid-btn" @click="selectDirectOption('publish', 'paid')">付费</button>
          </view>
        </view>
        
        <!-- 发布时长选择 -->
        <view class="publish-duration-select" v-if="showDurationSelect">
          <text class="duration-title">选择发布时长</text>
          <view class="duration-options">
            <view 
              class="duration-option" 
              :class="{ active: selectedDuration === '3d' }"
              @click="selectDuration('3d')"
            >
              <text class="duration-text">3天</text>
              <text class="duration-price">+2.8</text>
            </view>
            <view 
              class="duration-option" 
              :class="{ active: selectedDuration === '1w' }"
              @click="selectDuration('1w')"
            >
              <text class="duration-text">1周</text>
              <text class="duration-price">+5.8</text>
            </view>
            <view 
              class="duration-option" 
              :class="{ active: selectedDuration === '1m' }"
              @click="selectDuration('1m')"
            >
              <text class="duration-text">1个月</text>
              <text class="duration-price">+13.8</text>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 原有的发布选项 -->
      <view class="promotion-options" v-else>
        <!-- 看广告发布选项 -->
        <view class="promotion-option" @click="selectDirectOption('publish', 'ad')">
          <view class="option-icon-container normal-icon">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z" />
              <polyline points="7.5 4.21 12 6.81 16.5 4.21" />
              <polyline points="7.5 19.79 7.5 14.6 3 12" />
              <polyline points="21 12 16.5 14.6 16.5 19.79" />
              <polyline points="3.27 6.96 12 12.01 20.73 6.96" />
              <line x1="12" y1="22.08" x2="12" y2="12" />
            </svg>
          </view>
          <view class="option-content">
            <text class="option-title">看广告发布</text>
            <text class="option-desc">观看一个广告免费发布活动</text>
          </view>
          <view class="option-tag free-tag">
            <text>广告</text>
          </view>
        </view>
        
        <!-- 精选发布选项 -->
        <view class="promotion-option" @click="selectDirectOption('publish', 'featured')">
          <view class="option-icon-container featured-icon">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24" fill="none" stroke="currentColor" stroke-width="2">
              <polygon points="12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2" />
            </svg>
          </view>
          <view class="option-content">
            <text class="option-title">精选发布</text>
            <text class="option-desc">在首页精选区域展示，提高曝光</text>
          </view>
          <view class="option-tag paid-tag">
            <text>付费</text>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 置顶区域 -->
    <view class="promotion-section" v-if="showTop">
      <view class="section-header">
        <text class="section-title">活动置顶</text>
        <text class="section-desc">置顶活动到列表顶部，获得优先展示</text>
      </view>
      
      <view class="promotion-options">
        <!-- 看广告置顶选项 -->
        <view class="promotion-option" @click="selectDirectOption('top', 'ad')">
          <view class="option-icon-container normal-top-icon">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M12 19V5M5 12l7-7 7 7" />
              <rect x="3" y="3" width="18" height="4" rx="1" />
            </svg>
          </view>
          <view class="option-content">
            <text class="option-title">看广告置顶</text>
            <text class="option-desc">观看广告获得2小时置顶展示</text>
          </view>
          <view class="option-tag free-tag">
            <text>广告</text>
          </view>
        </view>
        
        <!-- 普通置顶选项 -->
        <view class="promotion-option" @click="selectDirectOption('top', 'normal')">
          <view class="option-icon-container normal-top-icon">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M12 19V5M5 12l7-7 7 7" />
            </svg>
          </view>
          <view class="option-content">
            <text class="option-title">普通置顶</text>
            <text class="option-desc">活动列表置顶展示3天</text>
          </view>
          <view class="option-tag paid-tag">
            <text>¥9.9</text>
          </view>
        </view>
        
        <!-- 高级置顶选项 -->
        <view class="promotion-option" @click="selectDirectOption('top', 'premium')">
          <view class="option-icon-container premium-top-icon">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M12 19V5M5 12l7-7 7 7" />
              <circle cx="12" cy="5" r="2" />
            </svg>
          </view>
          <view class="option-content">
            <text class="option-title">高级置顶</text>
            <text class="option-desc">活动列表置顶展示7天，首页推荐</text>
          </view>
          <view class="option-tag paid-tag">
            <text>¥19.9</text>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 刷新区域 -->
    <view class="promotion-section" v-if="showRefresh">
      <view class="section-header">
        <text class="section-title">活动刷新</text>
        <text class="section-desc">刷新活动展示时间，提升排名</text>
      </view>
      
      <view class="promotion-options">
        <!-- 看广告刷新选项 -->
        <view class="promotion-option" @click="selectDirectOption('refresh', 'ad')">
          <view class="option-icon-container refresh-icon">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M23 4v6h-6M1 20v-6h6" />
              <path d="M3.51 9a9 9 0 0114.85-3.36L23 10M1 14l4.64 4.36A9 9 0 0020.49 15" />
              <rect x="10" y="1" width="4" height="4" rx="1" />
            </svg>
          </view>
          <view class="option-content">
            <text class="option-title">看广告刷新</text>
            <text class="option-desc">观看广告免费刷新一次活动</text>
          </view>
          <view class="option-tag free-tag">
            <text>广告</text>
          </view>
        </view>
        
        <!-- 单次刷新选项 -->
        <view class="promotion-option" @click="handleRefreshClick">
          <view class="option-icon-container refresh-icon">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M23 4v6h-6M1 20v-6h6" />
              <path d="M3.51 9a9 9 0 0114.85-3.36L23 10M1 14l4.64 4.36A9 9 0 0020.49 15" />
            </svg>
          </view>
          <view class="option-content">
            <text class="option-title">单次刷新</text>
            <text class="option-desc" v-if="remainingRefreshCount > 0">剩余刷新次数: {{ remainingRefreshCount }}次</text>
            <text class="option-desc" v-else>刷新一次活动展示时间</text>
          </view>
          <view class="option-tag paid-tag">
            <text>¥2.9</text>
          </view>
        </view>
        
        <!-- 刷新套餐选项 -->
        <view class="promotion-option" @click="selectDirectOption('refresh', 'package')">
          <view class="option-icon-container package-icon">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24" fill="none" stroke="currentColor" stroke-width="2">
              <rect x="3" y="3" width="18" height="18" rx="2" ry="2" />
              <path d="M3 9h18M9 21V9" />
            </svg>
          </view>
          <view class="option-content">
            <text class="option-title">刷新套餐</text>
            <text class="option-desc">10次刷新，有效期30天</text>
          </view>
          <view class="option-tag paid-tag">
            <text>¥19.9</text>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 支付确认弹窗 -->
    <view class="payment-modal" v-if="showPaymentModal">
      <view class="modal-overlay" @click="closePaymentModal"></view>
      <view class="modal-container">
        <view class="modal-header">
          <text class="modal-title">{{ paymentModalTitle }}</text>
          <view class="modal-close" @click="closePaymentModal">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="20" height="20" fill="none" stroke="currentColor" stroke-width="2">
              <line x1="18" y1="6" x2="6" y2="18" />
              <line x1="6" y1="6" x2="18" y2="18" />
            </svg>
          </view>
        </view>
        <view class="modal-content">
          <view class="payment-info">
            <text class="payment-desc">{{ paymentModalDesc }}</text>
            <text class="payment-price">{{ paymentModalPrice }}</text>
          </view>
          <view class="payment-methods">
            <text class="methods-title">支付方式</text>
            <view class="method-options">
              <view 
                class="method-option" 
                :class="{ active: paymentMethod === 'wxpay' }"
                @click="paymentMethod = 'wxpay'"
              >
                <view class="method-icon wxpay-icon">
                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24" fill="none" stroke="currentColor" stroke-width="2">
                    <path d="M9 8.5a1.5 1.5 0 1 0 0-3 1.5 1.5 0 0 0 0 3z" />
                    <path d="M15 8.5a1.5 1.5 0 1 0 0-3 1.5 1.5 0 0 0 0 3z" />
                    <path d="M9 15.5a1.5 1.5 0 1 0 0-3 1.5 1.5 0 0 0 0 3z" />
                    <path d="M15 15.5a1.5 1.5 0 1 0 0-3 1.5 1.5 0 0 0 0 3z" />
                    <rect x="3" y="3" width="18" height="18" rx="2" />
                  </svg>
                </view>
                <text class="method-name">微信支付</text>
                <view class="method-check">
                  <view class="check-inner" v-if="paymentMethod === 'wxpay'"></view>
                </view>
              </view>
              <view 
                class="method-option" 
                :class="{ active: paymentMethod === 'alipay' }"
                @click="paymentMethod = 'alipay'"
              >
                <view class="method-icon alipay-icon">
                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24" fill="none" stroke="currentColor" stroke-width="2">
                    <path d="M22 9.3V5H2v4.3L12 15l10-5.7z" />
                    <path d="M2 9.3V19h20V9.3" />
                    <path d="M12 15v4" />
                  </svg>
                </view>
                <text class="method-name">支付宝</text>
                <view class="method-check">
                  <view class="check-inner" v-if="paymentMethod === 'alipay'"></view>
                </view>
              </view>
            </view>
          </view>
        </view>
        <view class="modal-footer">
          <button class="btn-cancel" @click="closePaymentModal">取消</button>
          <button class="btn-confirm" @click="confirmPayment">确认支付</button>
        </view>
      </view>
    </view>
    
    <!-- 刷新次数用完后的付费弹窗 -->
    <view class="refresh-payment-modal" v-if="showRefreshPaymentModal">
      <view class="refresh-payment-overlay" @click="closeRefreshPaymentModal"></view>
      <view class="refresh-payment-content modern-design">
        <!-- 卡片顶部 -->
        <view class="refresh-header-gradient">
          <view class="refresh-badge">
            <svg viewBox="0 0 24 24" width="16" height="16">
              <path fill="currentColor" d="M17.65,6.35C16.2,4.9 14.21,4 12,4A8,8 0 0,0 4,12A8,8 0 0,0 12,20C15.73,20 18.84,17.45 19.73,14H17.65C16.83,16.33 14.61,18 12,18A6,6 0 0,1 6,12A6,6 0 0,1 12,6C13.66,6 15.14,6.69 16.22,7.78L13,11H20V4L17.65,6.35Z" />
            </svg>
            <text>活动刷新</text>
          </view>
          <text class="refresh-title">提升活动曝光力</text>
          <text class="refresh-subtitle">获得更多精准客户</text>
        </view>
        
        <!-- 价格区域 -->
        <view class="refresh-price-area">
          <view class="price-tag">
            <text class="price-symbol">¥</text>
            <text class="price-value">{{ refreshPrice }}</text>
          </view>
          <view class="price-label">单次刷新</view>
        </view>
        
        <!-- 提示信息区 -->
        <view class="refresh-info-box">
          <view class="info-highlight">刷新后置顶信息排名立刻升到置顶第一位</view>
          <view class="info-detail">未置顶的会升到未置顶第一位</view>
        </view>
        
        <!-- 套餐提示 -->
        <view class="refresh-package-hint">
          <view class="package-hint-left">
            <text>购买刷新套餐</text>
          </view>
          <view class="package-hint-right">
            <text>最高可省</text>
            <text class="highlight-discount">70%</text>
          </view>
        </view>
        
        <!-- 按钮区 -->
        <view class="refresh-action-buttons">
          <button class="btn-check-package" @click="buyRefreshPackage">查看套餐</button>
          <button class="btn-refresh-now" @click="confirmPaidRefresh">立即刷新</button>
        </view>
      </view>
    </view>
    
    <!-- 刷新套餐选择弹窗 -->
    <view class="refresh-packages-modal" v-if="showRefreshOptions">
      <view class="refresh-packages-overlay" @click="closeRefreshOptions"></view>
      <view class="refresh-packages-content">
        <view class="refresh-packages-header">
          <text class="refresh-packages-title">
            <view class="refresh-icon-wrapper">
              <svg class="refresh-svg-icon" viewBox="0 0 24 24" width="20" height="20">
                <path fill="#0066FF" d="M17.65,6.35C16.2,4.9 14.21,4 12,4A8,8 0 0,0 4,12A8,8 0 0,0 12,20C15.73,20 18.84,17.45 19.73,14H17.65C16.83,16.33 14.61,18 12,18A6,6 0 0,1 6,12A6,6 0 0,1 12,6C13.66,6 15.14,6.69 16.22,7.78L13,11H20V4L17.65,6.35Z" />
              </svg>
            </view>
            刷新套餐
          </text>
        </view>
        <view class="refresh-packages-body">
          <text class="refresh-packages-desc">您当前有{{ remainingRefreshCount }}次刷新次数</text>
          <view class="refresh-packages-list">
            <view 
              class="refresh-package-item" 
              v-for="(pkg, index) in refreshPackages" 
              :key="index"
              @click="selectRefreshPackage(pkg)"
            >
              <text class="refresh-package-count">{{ pkg.count }}次</text>
              <text class="refresh-package-price">¥{{ pkg.price }}</text>
              <text class="refresh-package-discount" v-if="pkg.count === 10">省50%</text>
              <text class="refresh-package-discount" v-if="pkg.count === 30">省60%</text>
              <text class="refresh-package-discount" v-if="pkg.count === 100">省70%</text>
            </view>
          </view>
        </view>
        <view class="refresh-packages-actions">
          <button class="refresh-packages-confirm-btn" @click="useRefreshCount" v-if="remainingRefreshCount > 0">使用刷新次数</button>
          <button class="refresh-packages-cancel-btn" @click="closeRefreshOptions">取消</button>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, computed, reactive } from 'vue';

// 组件属性
const props = defineProps({
  // 活动ID
  activityId: {
    type: String,
    default: ''
  },
  // 活动类型：coupon, discount, flash, group
  activityType: {
    type: String,
    default: 'coupon',
    validator: (value) => {
      return ['coupon', 'discount', 'flash', 'group'].includes(value);
    }
  },
  // 控制显示哪些功能
  showActions: {
    type: Array,
    default: () => ['publish', 'top', 'refresh'],
    validator: (value) => {
      return value.every(item => ['publish', 'top', 'refresh'].includes(item));
    }
  },
  // 是否只显示发布模式（新的UI样式）
  publishModeOnly: {
    type: Boolean,
    default: false
  },
  // 显示模式：standard(标准模式) 或 direct(直接显示选项)
  showMode: {
    type: String,
    default: 'direct',
    validator: (value) => {
      return ['standard', 'direct'].includes(value);
    }
  },
  // 刷新次数
  refreshCount: {
    type: Number,
    default: 0
  }
});

// 组件事件
const emit = defineEmits(['action-completed']);

// 响应式状态
const isPanelExpanded = ref(false);
const showPaymentModal = ref(false);
const paymentModalTitle = ref('');
const paymentModalDesc = ref('');
const paymentModalPrice = ref('');
const paymentMethod = ref('wxpay');
const currentAction = ref('');
const currentActionType = ref('');
const showDurationSelect = ref(false);
const selectedDuration = ref('3d');
const showRefreshOptions = ref(false); // 是否显示刷新选项
const showRefreshPaymentModal = ref(false); // 是否显示刷新付费弹窗
const selectedOption = ref(null);
const selectedPrice = ref(null);

// 刷新次数
const remainingRefreshCount = ref(props.refreshCount);

// 时长选项
const durationOptions = [
  { label: '3天', price: '2.8' },
  { label: '1周', price: '5.8' },
  { label: '1个月', price: '13.8' }
];

// 刷新价格
const refreshPrice = ref('2');

// 刷新套餐选项
const refreshPackages = [
  { count: 10, price: '15', label: '10次刷新套餐' },
  { count: 30, price: '39', label: '30次刷新套餐' },
  { count: 100, price: '99', label: '100次刷新套餐' }
];

// 计算属性
const showPublish = computed(() => {
  return props.showActions.includes('publish');
});

const showTop = computed(() => {
  return !props.publishModeOnly && props.showActions.includes('top');
});

const showRefresh = computed(() => {
  return !props.publishModeOnly && props.showActions.includes('refresh');
});

// 判断是否为拼车系统发布页面
const isCarpool = computed(() => {
  // 检查当前页面路径是否包含carpool
  const pages = getCurrentPages();
  const currentPage = pages[pages.length - 1];
  const route = currentPage ? currentPage.route || '' : '';
  return route.includes('carpool');
});

// 模态框配置
const modalConfig = reactive({
  title: '',
  description: '',
  options: []
});

// 切换面板展开/折叠
const togglePanel = () => {
  isPanelExpanded.value = !isPanelExpanded.value;
};

// 处理操作
const handleAction = (action, type) => {
  currentAction.value = action;
  currentActionType.value = type;
  
  // 根据不同操作类型配置支付弹窗
  switch(action) {
    case 'publish':
      if (type === 'ad') {
        // 看广告发布，显示广告
        showAdForAction('publish');
      } else if (type === 'paid') {
        // 付费发布，显示时长选择
        showDurationSelect.value = true;
      } else if (type === 'featured') {
        // 精选发布，显示支付弹窗
        showPaymentDialog('精选发布', '在首页精选区域展示，提高曝光', '¥29.9');
      }
      break;
    case 'top':
      if (type === 'ad') {
        // 看广告置顶，显示广告
        showAdForAction('top');
      } else if (type === 'normal') {
        showPaymentDialog('普通置顶', '活动列表置顶展示3天', '¥9.9');
      } else if (type === 'premium') {
        showPaymentDialog('高级置顶', '活动列表置顶展示7天，首页推荐', '¥19.9');
      }
      break;
    case 'refresh':
      if (type === 'ad') {
        // 看广告刷新，显示广告
        showAdForAction('refresh');
      } else if (type === 'single') {
        showPaymentDialog('单次刷新', '刷新一次活动展示时间', '¥2.9');
      } else if (type === 'package') {
        showPaymentDialog('刷新套餐', '10次刷新，有效期30天', '¥19.9');
      }
      break;
  }
};

// 直接选择选项（用于直接显示模式）
const selectDirectOption = (action, optionType) => {
  currentAction.value = action;
  
  // 如果是付费选项，显示时长选择
  if (optionType === 'paid') {
    // 拼车系统直接处理付费发布，无需选择时长
    if (isCarpool.value && action === 'publish') {
      const option = {
        title: '付费发布',
        subtitle: '付费1元发布一条信息',
        price: '¥1.00',
        icon: '/static/images/premium/paid-publish.png',
        type: 'paid',
        duration: '一条信息',
        amount: '1'
      };
      selectedOption.value = option;
      processPaidAction();
      return;
    }
    
    showDurationSelect.value = true;
    return;
  }
  
  // 创建选项对象
  let option = null;
  
  if (action === 'publish') {
    if (optionType === 'ad') {
      if (isCarpool.value) {
        option = {
          title: '免费发布',
          subtitle: '观看15秒广告发布一条信息',
          price: '免费',
          icon: '/static/images/premium/ad-publish.png',
          type: 'ad',
          duration: '一条信息'
        };
      } else {
      option = {
        title: '免费发布',
        subtitle: '观看15秒广告后发布',
        price: '免费',
        icon: '/static/images/premium/ad-publish.png',
        type: 'ad',
        duration: '1天'
      };
      }
    }
  } else if (action === 'top') {
    if (optionType === 'ad') {
      option = {
        title: '广告置顶',
        subtitle: '观看30秒广告获得2小时置顶',
        price: '免费',
        icon: '/static/images/premium/ad-top.png',
        type: 'ad',
        duration: '2小时'
      };
    }
  } else if (action === 'refresh') {
    if (optionType === 'ad') {
      option = {
        title: '广告刷新',
        subtitle: '观看15秒广告刷新一次',
        price: '免费',
        icon: '/static/images/premium/ad-refresh.png',
        type: 'ad'
      };
    }
  }
  
  if (option) {
    selectedOption.value = option;
    
    // 直接处理选择的选项
    if (optionType === 'ad') {
      showAd();
    }
  }
};

// 选择发布时长
const selectDuration = (duration, price) => {
  selectedDuration.value = duration;
  selectedPrice.value = price;
  
  let durationText = '';
  if (duration === '3d') {
    durationText = '3天';
    price = '2.8';
  } else if (duration === '1w') {
    durationText = '1周';
    price = '5.8';
  } else if (duration === '1m') {
    durationText = '1个月';
    price = '13.8';
  }
  
  // 创建付费选项
  const option = {
    title: currentAction.value === 'publish' ? '付费发布' : 
           currentAction.value === 'top' ? '付费置顶' : '付费刷新',
    subtitle: `${currentAction.value === 'publish' ? '付费发布' : 
               currentAction.value === 'top' ? '付费置顶' : '付费刷新'}${durationText}`,
    price: `¥${price}`,
    icon: currentAction.value === 'publish' ? '/static/images/premium/paid-publish.png' : 
          currentAction.value === 'top' ? '/static/images/premium/paid-top.png' : 
          '/static/images/premium/paid-refresh.png',
    type: 'paid',
    duration: durationText,
    amount: price
  };
  
  selectedOption.value = option;
  processPaidAction();
  
  // 显示支付弹窗
  showPaymentDialog('付费发布', `发布${durationText}`, `¥${price}`);
  showDurationSelect.value = false;
};

// 显示支付弹窗
const showPaymentDialog = (title, desc, price) => {
  paymentModalTitle.value = title;
  paymentModalDesc.value = desc;
  paymentModalPrice.value = price;
  showPaymentModal.value = true;
};

// 关闭支付弹窗
const closePaymentModal = () => {
  showPaymentModal.value = false;
};

// 确认支付
const confirmPayment = () => {
  // 显示加载提示
  uni.showLoading({
    title: '处理支付中...'
  });
  
  // 模拟支付过程
  setTimeout(() => {
    uni.hideLoading();
    
    // 关闭支付弹窗
    closePaymentModal();
    
    // 根据不同操作类型处理结果
    switch(currentAction.value) {
      case 'publish':
        processPublish();
        break;
      case 'top':
        processTop();
        break;
      case 'refresh':
        processRefresh();
        break;
    }
  }, 1500);
};

// 显示广告进行操作
const showAdForAction = (action) => {
  let actionText = '';
  switch(action) {
    case 'publish':
      actionText = '发布';
      break;
    case 'top':
      actionText = '置顶';
      break;
    case 'refresh':
      actionText = '刷新';
      break;
  }
  
  uni.showLoading({
    title: '准备广告中...'
  });
  
  // 模拟广告加载
  setTimeout(() => {
    uni.hideLoading();
    
    // 显示广告观看提示
    uni.showModal({
      title: '广告观看',
      content: `观看一个广告即可免费${actionText}活动`,
      confirmText: '观看广告',
      cancelText: '取消',
      success: (res) => {
        if (res.confirm) {
          // 用户确认观看广告
          playAdvertisementForAction(action);
        }
      }
    });
  }, 1000);
};

// 显示广告
const showAd = () => {
  // 模拟广告显示
  uni.showLoading({
    title: '正在加载广告...'
  });
  
  setTimeout(() => {
    uni.hideLoading();
    
    // 模拟广告播放完成
    uni.showModal({
      title: '广告观看完成',
      content: '您已成功观看广告，即将完成操作',
      showCancel: false,
      success: () => {
        processActionCompletion();
      }
    });
  }, 1500);
};

// 播放广告
const playAdvertisementForAction = (action) => {
  uni.showLoading({
    title: '加载广告中...'
  });
  
  // 模拟广告播放
  setTimeout(() => {
    uni.hideLoading();
    
    // 显示广告播放中
    uni.showToast({
      title: '广告播放中...',
      icon: 'none',
      duration: 3000
    });
    
    // 模拟广告播放完成
    setTimeout(() => {
      // 广告播放完成，处理相应操作
      switch(action) {
        case 'publish':
          processPublish();
          break;
        case 'top':
          processTop();
          break;
        case 'refresh':
          processRefresh();
          break;
      }
      
      // 显示成功提示
      uni.showToast({
        title: `广告观看完成，${action === 'publish' ? '发布' : action === 'top' ? '置顶' : '刷新'}成功`,
        icon: 'success',
        duration: 2000
      });
    }, 3000);
  }, 1000);
};

// 处理付费操作
const processPaidAction = () => {
  // 关闭时长选择面板
  showDurationSelect.value = false;
  
  // 模拟支付流程
  uni.showLoading({
    title: '正在处理支付...'
  });
  
  setTimeout(() => {
    uni.hideLoading();
    
    // 模拟支付成功
    uni.showModal({
      title: '支付成功',
      content: '您已成功支付，即将完成操作',
      showCancel: false,
      success: () => {
        processActionCompletion();
      }
    });
  }, 1500);
};

// 处理发布操作
const processPublish = () => {
  uni.showLoading({
    title: '发布中...'
  });
  
  // 模拟API调用
  setTimeout(() => {
    uni.hideLoading();
    
    // 显示成功提示
    uni.showToast({
      title: currentActionType.value === 'normal' ? '发布成功' : '精选发布成功',
      icon: 'success'
    });
    
    // 触发事件
    emit('action-completed', {
      action: 'publish',
      type: currentActionType.value,
      activityId: props.activityId,
      activityType: props.activityType
    });
  }, 1000);
};

// 处理置顶操作
const processTop = () => {
  uni.showLoading({
    title: '置顶中...'
  });
  
  // 模拟API调用
  setTimeout(() => {
    uni.hideLoading();
    
    // 显示成功提示
    uni.showToast({
      title: currentActionType.value === 'normal' ? '普通置顶成功' : '高级置顶成功',
      icon: 'success'
    });
    
    // 触发事件
    emit('action-completed', {
      action: 'top',
      type: currentActionType.value,
      activityId: props.activityId,
      activityType: props.activityType
    });
  }, 1000);
};

// 处理刷新操作
const processRefresh = () => {
  uni.showLoading({
    title: '刷新中...'
  });
  
  // 模拟API调用
  setTimeout(() => {
    uni.hideLoading();
    
    // 显示成功提示
    uni.showToast({
      title: currentActionType.value === 'single' ? '刷新成功' : '刷新套餐购买成功',
      icon: 'success'
    });
    
    // 触发事件
    emit('action-completed', {
      action: 'refresh',
      type: currentActionType.value,
      activityId: props.activityId,
      activityType: props.activityType
    });
  }, 1000);
};

// 处理操作完成
const processActionCompletion = () => {
  // 根据不同操作类型进行处理
  const actionType = currentAction.value;
  const optionType = selectedOption.value.type;
  
  // 发送操作完成事件
  let eventType = '';
  if (optionType === 'ad') {
    eventType = 'adPublish';
  } else if (optionType === 'paid') {
    eventType = 'paidPublish';
  } else if (optionType === 'count') {
    eventType = 'countRefresh';
  }
  
  emit('action-completed', eventType, {
    action: actionType,
    type: optionType,
    activityId: props.activityId,
    activityType: props.activityType,
    option: selectedOption.value,
    amount: selectedOption.value.amount || '0',
    remainingCount: optionType === 'count' ? selectedOption.value.remainingCount : undefined
  });
  
  // 显示操作成功提示
  let successMessage = '';
  switch(actionType) {
    case 'publish':
      successMessage = '发布成功！';
      break;
    case 'top':
      successMessage = `置顶成功！有效期${selectedOption.value.duration || ''}`;
      break;
    case 'refresh':
      if (optionType === 'count') {
        successMessage = `刷新成功！剩余${selectedOption.value.remainingCount}次`;
      } else {
        successMessage = '刷新成功！';
      }
      break;
  }
  
  uni.showToast({
    title: successMessage,
    icon: 'success'
  });
};

// 处理刷新操作
const handleRefreshClick = () => {
  if (remainingRefreshCount.value > 0) {
    showRefreshOptions.value = true;
  } else {
    showRefreshPaymentModal.value = true;
  }
};

// 关闭刷新选项
const closeRefreshOptions = () => {
  showRefreshOptions.value = false;
};

// 关闭刷新付费弹窗
const closeRefreshPaymentModal = () => {
  showRefreshPaymentModal.value = false;
};

// 购买刷新套餐
const buyRefreshPackage = () => {
  closeRefreshPaymentModal();
  showRefreshOptions.value = true;
};

// 确认付费刷新
const confirmPaidRefresh = () => {
  closeRefreshPaymentModal();
  
  // 模拟支付流程
  uni.showLoading({
    title: '正在处理支付...'
  });
  
  setTimeout(() => {
    uni.hideLoading();
    
    // 模拟支付成功
    uni.showModal({
      title: '支付成功',
      content: '您已成功支付，即将刷新信息',
      showCancel: false,
      success: () => {
        // 创建付费刷新选项
        const option = {
          title: '付费刷新',
          subtitle: '立即刷新信息至列表顶部',
          price: `¥${refreshPrice.value}`,
          icon: '/static/images/premium/paid-refresh.png',
          type: 'paid',
          amount: refreshPrice.value
        };
        
        selectedOption.value = option;
        currentAction.value = 'refresh';
        
        // 处理操作完成
        processActionCompletion();
      }
    });
  }, 1500);
};

// 选择刷新套餐
const selectRefreshPackage = (pkg) => {
  // 模拟支付流程
  uni.showLoading({
    title: '正在处理支付...'
  });
  
  setTimeout(() => {
    uni.hideLoading();
    
    // 模拟支付成功
    uni.showModal({
      title: '购买成功',
      content: `您已成功购买${pkg.count}次刷新次数`,
      showCancel: false,
      success: () => {
        // 更新刷新次数
        remainingRefreshCount.value += pkg.count;
        closeRefreshOptions();
        
        // 通知父组件刷新次数已更新
        emit('action-completed', 'refreshPackage', {
          count: pkg.count,
          price: pkg.price,
          totalCount: remainingRefreshCount.value
        });
      }
    });
  }, 1500);
};

// 使用刷新次数
const useRefreshCount = () => {
  if (remainingRefreshCount.value <= 0) {
    uni.showToast({
      title: '您没有可用的刷新次数',
      icon: 'none'
    });
    return;
  }
  
  // 减少刷新次数
  remainingRefreshCount.value--;
  closeRefreshOptions();
  
  uni.showLoading({
    title: '正在刷新...'
  });
  
  setTimeout(() => {
    uni.hideLoading();
    
    // 创建使用刷新次数的选项
    const option = {
      title: '使用刷新次数',
      subtitle: '使用刷新次数刷新信息',
      price: '免费',
      icon: '/static/images/premium/paid-refresh.png',
      type: 'count',
      remainingCount: remainingRefreshCount.value
    };
    
    selectedOption.value = option;
    currentAction.value = 'refresh';
    
    // 处理操作完成
    processActionCompletion();
    
    // 通知父组件刷新次数已更新
    emit('action-completed', 'useRefreshCount', {
      remainingCount: remainingRefreshCount.value
    });
  }, 1000);
};
</script>

<style lang="scss" scoped>
.marketing-promotion-container {
  padding: 20rpx 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Helvetica Neue', Helvetica, Arial, sans-serif;
}

.promotion-section {
  margin-bottom: 30rpx;
  background-color: #FFFFFF;
  border-radius: 12rpx;
  padding: 24rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.section-header {
  margin-bottom: 20rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
}

.section-desc {
  font-size: 24rpx;
  color: #999999;
  margin-top: 6rpx;
}

.promotion-options {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.promotion-option {
  display: flex;
  align-items: center;
  padding: 20rpx;
  background-color: #F8F9FA;
  border-radius: 10rpx;
  position: relative;
  transition: all 0.3s ease;
}

.promotion-option:active {
  background-color: #F0F0F0;
  transform: scale(0.99);
}

.option-icon-container {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
}

.normal-icon {
  background: linear-gradient(135deg, #4FACFE, #00F2FE);
  color: white;
}

.featured-icon {
  background: linear-gradient(135deg, #FFD26F, #FF9A44);
  color: white;
}

.normal-top-icon {
  background: linear-gradient(135deg, #FF9A9E, #FAD0C4);
  color: white;
}

.premium-top-icon {
  background: linear-gradient(135deg, #FF6B6B, #FF3366);
  color: white;
}

.refresh-icon {
  background: linear-gradient(135deg, #A1C4FD, #C2E9FB);
  color: white;
}

.package-icon {
  background: linear-gradient(135deg, #84FAB0, #8FD3F4);
  color: white;
}

.option-content {
  flex: 1;
}

.option-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #333333;
}

.option-desc {
  font-size: 24rpx;
  color: #999999;
  margin-top: 4rpx;
}

.option-tag {
  padding: 6rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  font-weight: 500;
}

.free-tag {
  background-color: #E8F5E9;
  color: #4CAF50;
}

.paid-tag {
  background-color: #FFF3E0;
  color: #FF9800;
}

/* 支付弹窗样式 */
.payment-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
}

.modal-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
}

.modal-container {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  width: 80%;
  max-width: 600rpx;
  background-color: #FFFFFF;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #F0F0F0;
}

.modal-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
}

.modal-close {
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #999999;
}

.modal-content {
  padding: 30rpx;
}

.payment-info {
  margin-bottom: 30rpx;
  text-align: center;
}

.payment-desc {
  font-size: 28rpx;
  color: #666666;
}

.payment-price {
  font-size: 48rpx;
  font-weight: 600;
  color: #FF6B6B;
  margin-top: 16rpx;
  display: block;
}

.payment-methods {
  margin-top: 30rpx;
}

.methods-title {
  font-size: 28rpx;
  color: #333333;
  margin-bottom: 16rpx;
  display: block;
}

.method-options {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.method-option {
  display: flex;
  align-items: center;
  padding: 20rpx;
  background-color: #F8F9FA;
  border-radius: 10rpx;
  transition: all 0.3s ease;
}

.method-option.active {
  background-color: #F0F7FF;
  border: 1rpx solid #4FACFE;
}

.method-icon {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
  color: white;
}

.wxpay-icon {
  background-color: #09BB07;
}

.alipay-icon {
  background-color: #00A0E9;
}

.method-name {
  flex: 1;
  font-size: 28rpx;
  color: #333333;
}

.method-check {
  width: 36rpx;
  height: 36rpx;
  border-radius: 50%;
  border: 1rpx solid #DDDDDD;
  display: flex;
  align-items: center;
  justify-content: center;
}

.method-option.active .method-check {
  border-color: #4FACFE;
}

.check-inner {
  width: 20rpx;
  height: 20rpx;
  border-radius: 50%;
  background-color: #4FACFE;
}

.modal-footer {
  display: flex;
  border-top: 1rpx solid #F0F0F0;
}

.btn-cancel, .btn-confirm {
  flex: 1;
  height: 90rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  border: none;
  background-color: transparent;
}

.btn-cancel {
  color: #999999;
  border-right: 1rpx solid #F0F0F0;
}

.btn-confirm {
  color: #4FACFE;
  font-weight: 500;
}

/* 新的发布选项卡片样式 */
.publish-options-card {
  background-color: #FFFFFF;
  border-radius: 12rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.publish-option-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx 24rpx;
  border-bottom: 1rpx solid #F5F5F5;
}

.publish-option-row:last-child {
  border-bottom: none;
}

.publish-option-left {
  display: flex;
  align-items: center;
  flex: 1;
}

.publish-option-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
}

.ad-icon {
  background: linear-gradient(135deg, #4FACFE, #00F2FE);
  color: white;
}

.paid-icon {
  background: linear-gradient(135deg, #FF9A44, #FF6B6B);
  color: white;
}

.publish-option-content {
  flex: 1;
}

.publish-option-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333333;
  margin-bottom: 8rpx;
}

.publish-option-desc {
  font-size: 24rpx;
  color: #999999;
}

.publish-option-right {
  margin-left: 20rpx;
}

.publish-btn {
  min-width: 120rpx;
  height: 64rpx;
  border-radius: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  font-weight: 500;
  border: none;
}

.free-btn {
  background: linear-gradient(135deg, #4FACFE, #00F2FE);
  color: white;
}

.paid-btn {
  background: linear-gradient(135deg, #FF9A44, #FF6B6B);
  color: white;
}

/* 发布时长选择 */
.publish-duration-select {
  padding: 24rpx;
  background-color: #F8F9FA;
}

.duration-title {
  font-size: 28rpx;
  color: #333333;
  margin-bottom: 16rpx;
  display: block;
}

.duration-options {
  display: flex;
  justify-content: space-between;
}

.duration-option {
  flex: 1;
  height: 100rpx;
  background-color: #FFFFFF;
  border-radius: 8rpx;
  margin: 0 10rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border: 2rpx solid #EEEEEE;
  transition: all 0.3s ease;
}

.duration-option:first-child {
  margin-left: 0;
}

.duration-option:last-child {
  margin-right: 0;
}

.duration-option.active {
  border-color: #FF9A44;
  background-color: #FFF9F5;
}

.duration-text {
  font-size: 28rpx;
  color: #333333;
  margin-bottom: 4rpx;
}

.duration-price {
  font-size: 24rpx;
  color: #FF6B6B;
  font-weight: 500;
}

.duration-option.active .duration-text {
  color: #FF6B6B;
  font-weight: 500;
}

/* 刷新付费弹窗样式 */
.refresh-payment-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  z-index: 1000;
  backdrop-filter: blur(8px);
}

.refresh-payment-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.refresh-payment-content {
  position: fixed;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  width: 90%;
  max-width: 650rpx;
  z-index: 1001;
  background: linear-gradient(135deg, #ffffff, #f8f9ff);
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 20rpx 40rpx rgba(0, 0, 0, 0.2);
  border: 1rpx solid rgba(255, 255, 255, 0.8);
}

/* 现代设计风格的弹窗样式 */
.modern-design {
  border-radius: 18px;
  overflow: hidden;
  background: #ffffff;
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1), 0 5px 15px rgba(0, 0, 0, 0.07);
  width: 85%;
  max-width: 330px;
  padding: 0;
  border: none;
}

.refresh-header-gradient {
  background: linear-gradient(135deg, #3a7bd5, #00d2ff);
  padding: 22px 20px;
  color: #fff;
  position: relative;
  text-align: center;
}

.refresh-badge {
  display: inline-flex;
  align-items: center;
  background: rgba(255, 255, 255, 0.25);
  backdrop-filter: blur(5px);
  border-radius: 20px;
  padding: 4px 12px;
  margin-bottom: 14px;
  color: #fff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.refresh-badge svg {
  margin-right: 6px;
}

.refresh-title {
  font-size: 20px;
  font-weight: 600;
  margin-bottom: 4px;
  display: block;
  letter-spacing: 0.5px;
}

.refresh-subtitle {
  font-size: 14px;
  opacity: 0.9;
  display: block;
}

.refresh-price-area {
  background: #f8faff;
  padding: 16px 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px solid #eef2f9;
}

.price-tag {
  display: flex;
  align-items: baseline;
}

.price-symbol {
  font-size: 16px;
  color: #3a7bd5;
  margin-right: 2px;
}

.price-value {
  font-size: 28px;
  font-weight: 700;
  color: #3a7bd5;
}

.price-label {
  color: #8a9ab0;
  font-size: 14px;
}

.refresh-info-box {
  margin: 0 20px;
  padding: 12px 14px;
  background: #ebf3ff;
  border-left: 3px solid #3a7bd5;
  border-radius: 8px;
}

.info-highlight {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
}

.info-detail {
  font-size: 12px;
  color: #666;
}

.highlight-text {
  color: #ff6b42;
  font-weight: 600;
}

.refresh-package-hint {
  margin: 16px 20px 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.package-hint-left {
  font-size: 13px;
  font-weight: 500;
  color: #3a7bd5;
  background: rgba(58, 123, 213, 0.1);
  padding: 4px 12px;
  border-radius: 14px;
}

.package-hint-right {
  font-size: 12px;
  color: #8a9ab0;
}

.highlight-discount {
  margin-left: 4px;
  font-size: 16px;
  font-weight: 700;
  color: #ff6b42;
}

.refresh-action-buttons {
  display: flex;
  padding: 16px 20px 20px;
  gap: 10px;
}

.btn-check-package {
  flex: 1;
  height: 40px;
  border-radius: 20px;
  border: 1px solid #3a7bd5;
  background: transparent;
  color: #3a7bd5;
  font-size: 14px;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
}

.btn-refresh-now {
  flex: 1;
  height: 40px;
  border-radius: 20px;
  background: linear-gradient(135deg, #3a7bd5, #00d2ff);
  color: #fff;
  font-size: 14px;
  font-weight: 500;
  box-shadow: 0 4px 10px rgba(58, 123, 213, 0.3);
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 刷新套餐选择弹窗样式 */
.refresh-packages-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  z-index: 1000;
  backdrop-filter: blur(8px);
}

.refresh-packages-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.refresh-packages-content {
  position: fixed;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  width: 90%;
  max-width: 650rpx;
  z-index: 1001;
  background: linear-gradient(135deg, #ffffff, #f8f9ff);
  border-radius: 24rpx;
  overflow: hidden;
  box-shadow: 0 20rpx 40rpx rgba(0, 0, 0, 0.2);
  border: 1rpx solid rgba(255, 255, 255, 0.8);
}

.refresh-packages-header {
  padding: 30rpx;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
  background: linear-gradient(135deg, #f0f7ff, #e8f4ff);
}

.refresh-packages-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  text-align: center;
  position: relative;
}

.refresh-icon-wrapper {
  display: inline-block;
  margin-right: 8rpx;
  font-size: 32rpx;
  animation: spin 2s infinite linear;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.refresh-packages-title::after {
  content: '';
  position: absolute;
  bottom: -10rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 60rpx;
  height: 4rpx;
  background: linear-gradient(90deg, #007aff, #5856d6);
  border-radius: 2rpx;
}

.refresh-packages-body {
  padding: 40rpx 30rpx;
}

.refresh-packages-desc {
  font-size: 28rpx;
  color: #555;
  margin-bottom: 30rpx;
  text-align: center;
  font-weight: 500;
  background: rgba(0, 122, 255, 0.08);
  padding: 16rpx;
  border-radius: 12rpx;
  line-height: 1.5;
}

.refresh-packages-list {
  display: flex;
  justify-content: space-around;
  flex-wrap: wrap;
  margin-bottom: 40rpx;
}

.refresh-package-item {
  width: 30%;
  height: 180rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #fff8f0 0%, #fff1e6 100%);
  border-radius: 16rpx;
  box-shadow: 0 8rpx 16rpx rgba(255, 145, 85, 0.15);
  border: 1rpx solid rgba(255, 145, 85, 0.1);
  position: relative;
  overflow: hidden;
  transition: all 0.3s;
}

.refresh-package-item:active {
  transform: scale(0.98);
  box-shadow: 0 4rpx 8rpx rgba(255, 145, 85, 0.1);
}

.refresh-package-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 6rpx;
  background: linear-gradient(90deg, #ff6b6b, #ff9f43);
}

.refresh-package-count {
  font-size: 36rpx;
  font-weight: 700;
  color: #ff6b6b;
  margin-bottom: 10rpx;
}

.refresh-package-price {
  font-size: 30rpx;
  font-weight: 600;
  color: #ff9f43;
}

.refresh-package-discount {
  position: absolute;
  top: 10rpx;
  right: 10rpx;
  font-size: 22rpx;
  color: #fff;
  font-weight: 600;
  background: linear-gradient(135deg, #ff6b6b, #ff9f43);
  padding: 4rpx 10rpx;
  border-radius: 8rpx;
  transform: rotate(10deg);
  box-shadow: 0 2rpx 6rpx rgba(255, 107, 107, 0.3);
}

.refresh-packages-actions {
  display: flex;
  justify-content: space-between;
  padding: 20rpx 30rpx 40rpx;
  gap: 20rpx;
}

.refresh-packages-confirm-btn {
  flex: 1;
  height: 90rpx;
  line-height: 90rpx;
  text-align: center;
  font-size: 30rpx;
  font-weight: 500;
  border-radius: 45rpx;
  background: linear-gradient(135deg, #007aff, #5856d6);
  color: #FFFFFF;
  box-shadow: 0 6rpx 12rpx rgba(0, 122, 255, 0.2);
  transition: all 0.3s;
}

.refresh-packages-confirm-btn:active {
  transform: scale(0.98);
  box-shadow: 0 3rpx 6rpx rgba(0, 122, 255, 0.15);
}

.refresh-packages-cancel-btn {
  flex: 1;
  height: 90rpx;
  line-height: 90rpx;
  text-align: center;
  font-size: 30rpx;
  font-weight: 500;
  border-radius: 45rpx;
  background: rgba(0, 0, 0, 0.05);
  color: #666;
  transition: all 0.3s;
}

.refresh-packages-cancel-btn:active {
  transform: scale(0.98);
  background: rgba(0, 0, 0, 0.08);
}
</style> 