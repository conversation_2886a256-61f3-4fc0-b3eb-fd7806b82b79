/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 移除阿里妈妈字体引用，改用系统默认字体 */
/* 移除原有阿里妈妈字体定义
$uni-font-family: 'AlimamaShuHeiTi';
$uni-font-family-text: 'AlimamaShuHeiTi', -apple-system, BlinkMacSystemFont, 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;
*/
body.data-v-91859119, html.data-v-91859119, #app.data-v-91859119, .index-container.data-v-91859119 {
  font-family: "AlimamaShuHeiTi", -apple-system, BlinkMacSystemFont, "Helvetica Neue", "PingFang SC", "Microsoft YaHei", sans-serif;
}
.page-container.data-v-91859119 {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f5f5f5;
}

/* 导航栏样式 */
.navbar.data-v-91859119 {
  display: flex;
  align-items: center;
  height: 44px;
  background-color: #fff;
  padding: 0 15px;
  position: relative;
}
.navbar-back.data-v-91859119 {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.back-icon.data-v-91859119 {
  width: 12px;
  height: 12px;
  border-top: 2px solid #333;
  border-left: 2px solid #333;
  transform: rotate(-45deg);
}
.navbar-title.data-v-91859119 {
  flex: 1;
  text-align: center;
  font-size: 17px;
  font-weight: 600;
  color: #333;
}
.navbar-right.data-v-91859119 {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.share-icon.data-v-91859119 {
  color: #333;
}

/* 指南内容样式 */
.guide-content.data-v-91859119 {
  flex: 1;
  padding-bottom: 20px;
}
.guide-header.data-v-91859119 {
  position: relative;
  height: 180px;
  overflow: hidden;
}
.guide-banner.data-v-91859119 {
  width: 100%;
  height: 100%;
}
.guide-intro.data-v-91859119 {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 15px;
  background: linear-gradient(to top, rgba(0, 0, 0, 0.7), transparent);
}
.guide-title.data-v-91859119 {
  font-size: 22px;
  font-weight: 700;
  color: #fff;
  display: block;
  margin-bottom: 5px;
}
.guide-subtitle.data-v-91859119 {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.9);
  display: block;
}
.guide-sections.data-v-91859119 {
  padding: 15px;
}
.guide-section.data-v-91859119 {
  background-color: #fff;
  border-radius: 10px;
  padding: 15px;
  margin-bottom: 15px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}
.section-header.data-v-91859119 {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}
.section-icon.data-v-91859119 {
  width: 36px;
  height: 36px;
  border-radius: 18px;
  background-color: rgba(255, 77, 79, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 10px;
}
.section-title.data-v-91859119 {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}
.section-content.data-v-91859119 {
  padding-left: 46px;
}
.section-text.data-v-91859119 {
  font-size: 14px;
  color: #666;
  line-height: 1.5;
}
.section-image-container.data-v-91859119 {
  margin-top: 15px;
  border-radius: 8px;
  overflow: hidden;
}
.section-image.data-v-91859119 {
  width: 100%;
  height: 150px;
}

/* 优势项样式 */
.advantage-item.data-v-91859119 {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}
.advantage-icon.data-v-91859119 {
  margin-right: 10px;
  color: #FF4D4F;
}
.advantage-text.data-v-91859119 {
  font-size: 14px;
  color: #666;
}

/* 场景项样式 */
.scenario-item.data-v-91859119 {
  margin-bottom: 12px;
}
.scenario-title.data-v-91859119 {
  font-size: 15px;
  font-weight: 600;
  color: #333;
  display: block;
  margin-bottom: 5px;
}
.scenario-desc.data-v-91859119 {
  font-size: 13px;
  color: #666;
  display: block;
}

/* 最佳实践样式 */
.practice-item.data-v-91859119 {
  display: flex;
  margin-bottom: 15px;
}
.practice-number.data-v-91859119 {
  font-size: 18px;
  font-weight: 700;
  color: #FF4D4F;
  margin-right: 12px;
}
.practice-detail.data-v-91859119 {
  flex: 1;
}
.practice-title.data-v-91859119 {
  font-size: 15px;
  font-weight: 600;
  color: #333;
  display: block;
  margin-bottom: 5px;
}
.practice-desc.data-v-91859119 {
  font-size: 13px;
  color: #666;
  display: block;
}

/* FAQ样式 */
.faq-item.data-v-91859119 {
  margin-bottom: 10px;
  border-bottom: 1px solid #eee;
  padding-bottom: 10px;
}
.faq-question.data-v-91859119 {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.question-text.data-v-91859119 {
  font-size: 15px;
  font-weight: 600;
  color: #333;
}
.arrow-icon.data-v-91859119 {
  width: 12px;
  height: 12px;
  border-top: 1px solid #999;
  border-right: 1px solid #999;
  transform: rotate(45deg);
  transition: transform 0.3s;
}
.arrow-down.data-v-91859119 {
  transform: rotate(135deg);
}
.faq-answer.data-v-91859119 {
  margin-top: 10px;
  padding: 10px;
  background-color: #f9f9f9;
  border-radius: 6px;
}
.answer-text.data-v-91859119 {
  font-size: 14px;
  color: #666;
  line-height: 1.5;
}

/* 联系我们样式 */
.contact-section.data-v-91859119 {
  margin: 0 15px 20px;
  padding: 20px;
  background-color: #fff;
  border-radius: 10px;
  text-align: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}
.contact-title.data-v-91859119 {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  display: block;
  margin-bottom: 10px;
}
.contact-desc.data-v-91859119 {
  font-size: 14px;
  color: #666;
  display: block;
  margin-bottom: 15px;
}
.contact-btn.data-v-91859119 {
  background-color: #FF4D4F;
  color: #fff;
  border: none;
  border-radius: 20px;
  padding: 8px 20px;
  font-size: 14px;
}