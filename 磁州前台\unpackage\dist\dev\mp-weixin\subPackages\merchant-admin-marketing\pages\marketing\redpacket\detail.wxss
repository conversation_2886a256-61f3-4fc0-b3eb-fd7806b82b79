/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 移除阿里妈妈字体引用，改用系统默认字体 */
/* 移除原有阿里妈妈字体定义
$uni-font-family: 'AlimamaShuHeiTi';
$uni-font-family-text: 'AlimamaShuHeiTi', -apple-system, BlinkMacSystemFont, 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;
*/
body, html, #app, .index-container {
  font-family: "AlimamaShuHeiTi", -apple-system, BlinkMacSystemFont, "Helvetica Neue", "PingFang SC", "Microsoft YaHei", sans-serif;
}
.page-container {
  min-height: 100vh;
  background-color: #F5F7FA;
  padding-bottom: 80px;
}

/* 导航栏样式 */
.navbar {
  position: -webkit-sticky;
  position: sticky;
  top: 0;
  left: 0;
  right: 0;
  background: linear-gradient(135deg, #FF5858, #FF0000);
  color: #fff;
  padding: 44px 16px 15px;
  display: flex;
  align-items: center;
  z-index: 100;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.15);
}
.navbar-back {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.back-icon {
  width: 12px;
  height: 12px;
  border-left: 2px solid #fff;
  border-bottom: 2px solid #fff;
  transform: rotate(45deg);
}
.navbar-title {
  flex: 1;
  text-align: center;
  font-size: 18px;
  font-weight: 600;
  letter-spacing: 0.5px;
}
.navbar-right {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.more-icon {
  font-size: 20px;
  font-weight: bold;
  transform: rotate(90deg);
}

/* 红包头部信息 */
.redpacket-header {
  background-color: #fff;
  padding: 15px;
  margin-bottom: 15px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}
.redpacket-cover {
  width: 100%;
  height: 160px;
  border-radius: 8px;
  overflow: hidden;
  position: relative;
  margin-bottom: 15px;
}
.cover-image {
  width: 100%;
  height: 100%;
}
.redpacket-status {
  position: absolute;
  top: 10px;
  right: 10px;
  padding: 4px 10px;
  border-radius: 15px;
  font-size: 12px;
}
.status-active {
  background-color: #E8F5E9;
  color: #388E3C;
}
.status-upcoming {
  background-color: #E3F2FD;
  color: #1976D2;
}
.status-ended {
  background-color: #EEEEEE;
  color: #757575;
}
.status-draft {
  background-color: #FFF3E0;
  color: #E65100;
}
.redpacket-info {
  padding: 0 5px;
}
.redpacket-name {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin-bottom: 10px;
}
.redpacket-type {
  margin-bottom: 10px;
}
.type-tag {
  display: inline-block;
  padding: 3px 8px;
  border-radius: 4px;
  font-size: 12px;
}
.type-normal {
  background-color: #FFE0E0;
  color: #FF5858;
}
.type-fission {
  background-color: #E0F7F4;
  color: #4ECDC4;
}
.type-mass {
  background-color: #FFF3E0;
  color: #FFD166;
}
.type-rain {
  background-color: #E8E0F7;
  color: #6A0572;
}
.redpacket-time {
  display: flex;
  align-items: center;
}
.time-icon {
  width: 14px;
  height: 14px;
  background-color: #ccc;
  border-radius: 7px;
  margin-right: 5px;
}
.time-text {
  font-size: 12px;
  color: #666;
}

/* 数据概览卡片 */
.stats-card {
  background-color: #fff;
  padding: 15px;
  margin: 0 15px 15px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}
.stats-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}
.stats-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}
.stats-action {
  display: flex;
  align-items: center;
}
.action-text {
  font-size: 12px;
  color: #999;
  margin-right: 5px;
}
.action-arrow {
  width: 8px;
  height: 8px;
  border-top: 1px solid #999;
  border-right: 1px solid #999;
  transform: rotate(45deg);
}
.stats-grid {
  display: flex;
  justify-content: space-between;
  margin-bottom: 15px;
}
.stats-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.stats-value {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin-bottom: 5px;
}
.stats-label {
  font-size: 12px;
  color: #999;
}
.progress-section {
  padding-top: 10px;
  border-top: 1px solid #eee;
}
.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}
.progress-title {
  font-size: 14px;
  color: #333;
}
.progress-value {
  font-size: 12px;
  color: #666;
}
.progress-bar {
  height: 6px;
  background-color: #F5F7FA;
  border-radius: 3px;
  overflow: hidden;
}
.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #FF5858, #FF0000);
  border-radius: 3px;
}

/* 红包设置卡片 */
.settings-card {
  background-color: #fff;
  padding: 15px;
  margin: 0 15px 15px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}
.settings-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}
.settings-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}
.settings-action {
  display: flex;
  align-items: center;
}
.settings-list {
  padding-top: 5px;
}
.settings-item {
  display: flex;
  justify-content: space-between;
  padding: 10px 0;
  border-bottom: 1px solid #f5f5f5;
}
.settings-item:last-child {
  border-bottom: none;
}
.item-label {
  font-size: 14px;
  color: #666;
}
.item-value {
  font-size: 14px;
  color: #333;
  font-weight: 500;
}

/* 使用说明卡片 */
.description-card {
  background-color: #fff;
  padding: 15px;
  margin: 0 15px 15px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}
.description-header {
  margin-bottom: 10px;
}
.description-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}
.description-content {
  padding: 10px;
  background-color: #F9F9F9;
  border-radius: 8px;
}
.description-text {
  font-size: 14px;
  color: #666;
  line-height: 1.6;
}

/* 领取记录卡片 */
.records-card {
  background-color: #fff;
  padding: 15px;
  margin: 0 15px 15px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}
.records-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}
.records-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}
.records-action {
  display: flex;
  align-items: center;
}
.records-list {
  max-height: 300px;
  overflow-y: auto;
}
.records-item {
  display: flex;
  align-items: center;
  padding: 10px 0;
  border-bottom: 1px solid #f5f5f5;
}
.records-item:last-child {
  border-bottom: none;
}
.user-avatar {
  width: 40px;
  height: 40px;
  border-radius: 20px;
  overflow: hidden;
  margin-right: 10px;
}
.avatar-image {
  width: 100%;
  height: 100%;
}
.record-info {
  flex: 1;
}
.user-name {
  font-size: 14px;
  color: #333;
  margin-bottom: 5px;
  display: block;
}
.record-time {
  font-size: 12px;
  color: #999;
}
.record-amount {
  text-align: right;
}
.amount-value {
  font-size: 16px;
  font-weight: 600;
  color: #FF5858;
  display: block;
  margin-bottom: 5px;
}
.amount-status {
  font-size: 12px;
  color: #388E3C;
}
.amount-status.unused {
  color: #999;
}
.empty-records {
  padding: 30px 0;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.empty-icon {
  width: 60px;
  height: 60px;
  background-color: #F5F7FA;
  border-radius: 30px;
  margin-bottom: 10px;
}
.empty-text {
  font-size: 14px;
  color: #999;
}

/* 底部操作按钮 */
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  display: flex;
  padding: 10px 15px;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);
}
.action-button {
  flex: 1;
  height: 44px;
  border-radius: 22px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 5px;
}
.action-button.share {
  background: linear-gradient(135deg, #FF5858, #FF0000);
  color: #fff;
}
.action-button.start {
  background: linear-gradient(135deg, #4ECDC4, #2BAF9F);
  color: #fff;
}
.action-button.stop {
  background: linear-gradient(135deg, #FF9500, #FF5E3A);
  color: #fff;
}
.action-button.delete {
  background-color: #F5F7FA;
  color: #FF3B30;
}
.action-button.duplicate {
  background-color: #F5F7FA;
  color: #666;
}
.button-icon {
  width: 16px;
  height: 16px;
  margin-right: 5px;
}
.button-text {
  font-size: 14px;
  font-weight: 500;
}

/* 分享弹窗 */
.share-popup {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
}
.popup-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
}
.popup-content {
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #fff;
  border-top-left-radius: 16px;
  border-top-right-radius: 16px;
  padding: 15px;
  transform: translateY(0);
  transition: transform 0.3s;
}
.popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}
.popup-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}
.popup-close {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  color: #999;
}
.share-options {
  display: flex;
  justify-content: space-around;
  margin-bottom: 20px;
}
.share-option {
  display: flex;
  flex-direction: column;
  align-items: center;
}
.option-icon {
  width: 50px;
  height: 50px;
  border-radius: 25px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 8px;
}
.icon-image {
  width: 30px;
  height: 30px;
}
.option-text {
  font-size: 12px;
  color: #666;
}
.share-qrcode {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 15px 0;
  border-top: 1px solid #eee;
}
.qrcode-image {
  width: 150px;
  height: 150px;
  margin-bottom: 10px;
}
.qrcode-tip {
  font-size: 12px;
  color: #999;
}