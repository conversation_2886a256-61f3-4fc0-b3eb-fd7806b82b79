<!-- 秒杀活动创建页面 (create.vue) -->
<template>
  <view class="flash-create-container">
    <!-- 自定义导航栏 -->
    <view class="navbar">
      <view class="navbar-left">
        <view class="back-button" @tap="goBack">
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M15 18L9 12L15 6" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
        </view>
      </view>
      <view class="navbar-title">
        <text class="title-text">创建秒杀活动</text>
      </view>
      <view class="navbar-right">
        <view class="save-button" @tap="saveFlashSale">创建</view>
      </view>
    </view>
    
    <!-- 页面内容区域 -->
    <scroll-view scroll-y class="content-area">
      <!-- 表单区域 -->
      <view class="form-section">
        <!-- 基本信息 -->
        <view class="form-group">
          <view class="form-header">基本信息</view>
          
          <view class="form-item">
            <text class="form-label required">活动名称</text>
            <input class="form-input" v-model="formData.name" placeholder="请输入秒杀活动名称" maxlength="30" />
            <text class="form-counter">{{formData.name.length}}/30</text>
          </view>
          
          <view class="form-item">
            <text class="form-label required">商品图片</text>
            <view class="image-uploader">
              <view 
                class="image-item" 
                v-for="(image, index) in formData.images" 
                :key="index"
              >
                <image class="uploaded-image" :src="image" mode="aspectFill"></image>
                <view class="delete-icon" @tap="removeImage(index)">
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <circle cx="12" cy="12" r="10" fill="rgba(0,0,0,0.5)"/>
                    <path d="M15 9L9 15M9 9L15 15" stroke="white" stroke-width="2" stroke-linecap="round"/>
                  </svg>
                </view>
              </view>
              <view class="upload-button" @tap="addImage" v-if="formData.images.length < 5">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M12 5V19M5 12H19" stroke="#999999" stroke-width="2" stroke-linecap="round"/>
                </svg>
              </view>
            </view>
            <text class="form-tip">最多上传5张图片，建议尺寸750x750像素</text>
          </view>
        </view>
        
        <!-- 价格设置 -->
        <view class="form-group">
          <view class="form-header">价格设置</view>
          
          <view class="form-item">
            <text class="form-label required">原价 (元)</text>
            <input class="form-input" type="digit" v-model="formData.originalPrice" placeholder="请输入商品原价" />
          </view>
          
          <view class="form-item">
            <text class="form-label required">秒杀价 (元)</text>
            <input class="form-input" type="digit" v-model="formData.flashPrice" placeholder="请输入秒杀价格" />
          </view>
          
          <view class="form-item">
            <text class="form-label">折扣</text>
            <text class="discount-value">{{discountValue}}</text>
          </view>
        </view>
        
        <!-- 库存设置 -->
        <view class="form-group">
          <view class="form-header">库存设置</view>
          
          <view class="form-item">
            <text class="form-label required">活动库存</text>
            <input class="form-input" type="number" v-model="formData.stockTotal" placeholder="请输入活动库存" />
          </view>
          
          <view class="form-item">
            <text class="form-label">每人限购</text>
            <input class="form-input" type="number" v-model="formData.purchaseLimit" placeholder="0表示不限购" />
            <text class="form-tip">设置为0表示不限购</text>
          </view>
        </view>
        
        <!-- 时间设置 -->
        <view class="form-group">
          <view class="form-header">时间设置</view>
          
          <view class="form-item">
            <text class="form-label required">开始时间</text>
            <picker 
              mode="dateTime" 
              :value="formData.startTime" 
              start="2023-01-01 00:00" 
              end="2030-12-31 23:59" 
              @change="startTimeChange"
            >
              <view class="picker-value">
                <text>{{formData.startTime || '请选择开始时间'}}</text>
                <view class="arrow-icon"></view>
              </view>
            </picker>
          </view>
          
          <view class="form-item">
            <text class="form-label required">结束时间</text>
            <picker 
              mode="dateTime" 
              :value="formData.endTime" 
              start="2023-01-01 00:00" 
              end="2030-12-31 23:59" 
              @change="endTimeChange"
            >
              <view class="picker-value">
                <text>{{formData.endTime || '请选择结束时间'}}</text>
                <view class="arrow-icon"></view>
              </view>
            </picker>
            <text class="form-error" v-if="timeError">{{timeError}}</text>
          </view>
        </view>
        
        <!-- 活动规则 -->
        <view class="form-group">
          <view class="form-header">活动规则</view>
          
          <view class="form-item">
            <text class="form-label">活动规则</text>
            <textarea class="form-textarea" v-model="formData.rules" placeholder="请输入活动规则说明" maxlength="200" />
            <text class="form-counter">{{formData.rules.length}}/200</text>
          </view>
        </view>
        
        <!-- 分销设置 -->
        <view class="form-group" v-if="hasMerchantDistribution">
          <view class="form-header">分销设置</view>
          
          <distribution-setting 
            :initial-settings="formData.distributionSettings"
            @update="updateDistributionSettings"
          />
        </view>
        
        <!-- 商品详情 -->
        <view class="form-group">
          <view class="form-header">商品详情</view>
          
          <view class="form-item">
            <text class="form-label">商品描述</text>
            <textarea class="form-textarea" v-model="formData.description" placeholder="请输入商品描述" maxlength="500" />
            <text class="form-counter">{{formData.description.length}}/500</text>
          </view>
          
          <view class="form-item">
            <text class="form-label">详情图片</text>
            <view class="image-uploader">
              <view 
                class="image-item detail-image-item" 
                v-for="(image, index) in formData.detailImages" 
                :key="index"
              >
                <image class="uploaded-image" :src="image" mode="aspectFill"></image>
                <view class="delete-icon" @tap="removeDetailImage(index)">
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <circle cx="12" cy="12" r="10" fill="rgba(0,0,0,0.5)"/>
                    <path d="M15 9L9 15M9 9L15 15" stroke="white" stroke-width="2" stroke-linecap="round"/>
                  </svg>
                </view>
              </view>
              <view class="upload-button detail-upload" @tap="addDetailImage">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M12 5V19M5 12H19" stroke="#999999" stroke-width="2" stroke-linecap="round"/>
                </svg>
              </view>
            </view>
            <text class="form-tip">建议上传清晰的商品细节图</text>
          </view>
        </view>
        
        <!-- 活动提示 -->
        <view class="form-group">
          <view class="form-header">活动提示</view>
          
          <view class="tips-list">
            <view class="tip-item">
              <view class="tip-icon">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22Z" stroke="#FF7600" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                  <path d="M12 16V12" stroke="#FF7600" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                  <path d="M12 8H12.01" stroke="#FF7600" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
              </view>
              <view class="tip-content">
                <view class="tip-title">选择热门时段</view>
                <view class="tip-desc">选择用户活跃度高的时段发布秒杀活动，如12:00-13:00或18:00-20:00</view>
              </view>
            </view>
            <view class="tip-item">
              <view class="tip-icon">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M12 2L15.09 8.26L22 9.27L17 14.14L18.18 21.02L12 17.77L5.82 21.02L7 14.14L2 9.27L8.91 8.26L12 2Z" stroke="#FF7600" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
              </view>
              <view class="tip-content">
                <view class="tip-title">明确价格优势</view>
                <view class="tip-desc">秒杀商品价格应明显低于原价，建议达到5-7折的优惠幅度</view>
              </view>
            </view>
            <view class="tip-item">
              <view class="tip-icon">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M8 7V3M16 7V3M7 11H17M5 21H19C20.1046 21 21 20.1046 21 19V7C21 5.89543 20.1046 5 19 5H5C3.89543 5 3 5.89543 3 7V19C3 20.1046 3.89543 21 5 21Z" stroke="#FF7600" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
              </view>
              <view class="tip-content">
                <view class="tip-title">提前预热宣传</view>
                <view class="tip-desc">秒杀活动发布前1-3天进行预热宣传，增加用户关注度</view>
              </view>
            </view>
          </view>
        </view>

        <!-- 活动推广 -->
        <view class="form-group">
          <view class="form-header">
            <text class="form-title">活动发布</text>
            <text class="form-subtitle">选择活动发布方式</text>
          </view>
          
          <MarketingPromotionActions 
            :activityId="formData.id || ''" 
            activityType="flash"
            :showActions="['publish']"
            :publishModeOnly="true"
            @action-completed="handleActionCompleted"
          />
        </view>
      </view>
      
      <!-- 底部空间 -->
      <view class="bottom-space"></view>
    </scroll-view>
  </view>
</template>

<script>
import MarketingPromotionActions from '../../../components/MarketingPromotionActions.vue'
import { ActivityService, ActivityType } from '@/common/services/ActivityService.js'

export default {
  components: {
    MarketingPromotionActions
  },
  data() {
    return {
      formData: {
  name: '',
  images: [],
  originalPrice: '',
  flashPrice: '',
  stockTotal: '',
  purchaseLimit: '0',
  startTime: '',
  endTime: '',
        rules: '',
  description: '',
        detailImages: [],
  distributionSettings: {
    enabled: false,
          commission: 5,
          secondLevel: false,
          secondCommission: 2
        },
        type: ActivityType.FLASH // 设置活动类型为秒杀
      },
      timeError: '',
      hasMerchantDistribution: false
  }
  },
  computed: {
    // 计算折扣值
    discountValue() {
      if (!this.formData.originalPrice || !this.formData.flashPrice) {
        return '-';
  }
  
      const originalPrice = parseFloat(this.formData.originalPrice);
      const flashPrice = parseFloat(this.formData.flashPrice);
      
      if (isNaN(originalPrice) || isNaN(flashPrice) || originalPrice <= 0) {
        return '-';
      }
      
      const discount = (flashPrice / originalPrice * 10).toFixed(1);
      return `${discount}折`;
    }
  },
  onLoad() {
    // 检查商家是否开通了分销功能
    this.checkMerchantDistribution();
  },
  methods: {
// 返回上一页
    goBack() {
  uni.navigateBack();
    },

// 添加商品图片
    addImage() {
  uni.chooseImage({
        count: 5 - this.formData.images.length,
    sizeType: ['compressed'],
    sourceType: ['album', 'camera'],
    success: (res) => {
          // 这里应该上传图片到服务器，获取URL
          // 示例中直接使用本地临时路径
          this.formData.images = [...this.formData.images, ...res.tempFilePaths];
    }
  });
    },

    // 删除商品图片
    removeImage(index) {
      this.formData.images.splice(index, 1);
    },

// 添加详情图片
    addDetailImage() {
  uni.chooseImage({
    count: 9,
    sizeType: ['compressed'],
    sourceType: ['album', 'camera'],
    success: (res) => {
          // 这里应该上传图片到服务器，获取URL
          // 示例中直接使用本地临时路径
          this.formData.detailImages = [...this.formData.detailImages, ...res.tempFilePaths];
    }
  });
    },

    // 删除详情图片
    removeDetailImage(index) {
      this.formData.detailImages.splice(index, 1);
    },

// 开始时间变化
    startTimeChange(e) {
      this.formData.startTime = e.detail.value;
      this.validateTime();
    },

// 结束时间变化
    endTimeChange(e) {
      this.formData.endTime = e.detail.value;
      this.validateTime();
    },

// 验证时间
    validateTime() {
      if (this.formData.startTime && this.formData.endTime) {
        const startTime = new Date(this.formData.startTime.replace(/-/g, '/'));
        const endTime = new Date(this.formData.endTime.replace(/-/g, '/'));
  
        if (endTime <= startTime) {
          this.timeError = '结束时间必须晚于开始时间';
          return false;
  } else {
          this.timeError = '';
          return true;
        }
      }
      return true;
    },
    
    // 检查表单是否有效
    validateForm() {
      if (!this.formData.name) {
    uni.showToast({
      title: '请输入活动名称',
      icon: 'none'
    });
    return false;
  }
  
      if (this.formData.images.length === 0) {
    uni.showToast({
      title: '请上传至少一张商品图片',
      icon: 'none'
    });
    return false;
  }
  
      if (!this.formData.originalPrice) {
    uni.showToast({
          title: '请输入商品原价',
      icon: 'none'
    });
    return false;
  }
  
      if (!this.formData.flashPrice) {
    uni.showToast({
          title: '请输入秒杀价格',
      icon: 'none'
    });
    return false;
  }
  
      if (!this.formData.stockTotal) {
    uni.showToast({
          title: '请输入活动库存',
      icon: 'none'
    });
    return false;
  }
  
      if (!this.formData.startTime) {
    uni.showToast({
      title: '请选择开始时间',
      icon: 'none'
    });
    return false;
  }
  
      if (!this.formData.endTime) {
    uni.showToast({
      title: '请选择结束时间',
      icon: 'none'
    });
    return false;
  }
  
      return this.validateTime();
    },
    
    // 保存秒杀活动
    saveFlashSale() {
      if (!this.validateForm()) {
        return;
  }
  
      // 显示加载提示
      uni.showLoading({
        title: '保存中...'
      });

      // 准备提交的数据
      const activityData = {
        ...this.formData,
        coverImage: this.formData.images[0], // 设置封面图为第一张图片
        soldCount: 0, // 初始销量为0
        tag: '新品', // 默认标签
        merchantId: this.getMerchantId() // 获取商家ID
      };
      
      try {
        // 使用ActivityService创建活动
        const createdActivity = ActivityService.createActivity(activityData);
        
        // 隐藏加载提示
        uni.hideLoading();
        
        // 显示成功提示
    uni.showToast({
          title: '活动创建成功',
      icon: 'success'
    });
        
        // 保存活动ID到表单数据中，用于后续发布
        this.formData.id = createdActivity.id;
        
        // 显示发布提示
        setTimeout(() => {
          uni.showModal({
            title: '活动已创建',
            content: '您可以选择发布方式将活动展示给用户',
            confirmText: '立即发布',
            cancelText: '稍后发布',
            success: (res) => {
              if (res.confirm) {
                // 用户选择立即发布，可以滚动到发布区域
                // 或者直接触发发布操作
              } else {
                // 用户选择稍后发布，返回上一页
                setTimeout(() => {
                  uni.navigateBack();
                }, 500);
              }
            }
          });
        }, 1000);
      } catch (error) {
        console.error('创建活动失败:', error);
        uni.hideLoading();
    uni.showToast({
          title: '创建失败，请重试',
          icon: 'none'
    });
  }
    },
    
    // 获取商家ID
    getMerchantId() {
      // 实际项目中应该从用户信息或全局状态获取
      return 'merchant_' + Math.floor(Math.random() * 1000);
    },
    
    // 检查商家是否开通了分销功能
    checkMerchantDistribution() {
      // 实际项目中应该从API获取
      // 这里模拟一个异步操作
      setTimeout(() => {
        this.hasMerchantDistribution = true;
      }, 500);
    },
    
    // 更新分销设置
    updateDistributionSettings(settings) {
      this.formData.distributionSettings = settings;
    },
    
    // 处理活动发布操作完成
    handleActionCompleted(eventType, data) {
      console.log('活动发布操作完成', eventType, data);
      
      // 根据不同的发布方式处理
      if (eventType === 'adPublish' || eventType === 'paidPublish') {
        // 使用ActivityService发布活动
        const publishOptions = {
          publishType: eventType === 'adPublish' ? 'ad' : 'paid',
          publishDuration: data.option?.duration || '1天',
          publishAmount: data.amount || '0'
        };
        
        try {
          ActivityService.publishActivity(this.formData.id, publishOptions);
          
          uni.showToast({
            title: '发布设置已保存',
            icon: 'success'
          });
          
          // 发布成功后延迟返回上一页
          setTimeout(() => {
            uni.navigateBack();
          }, 1500);
        } catch (error) {
          console.error('发布活动失败:', error);
          uni.showToast({
            title: '发布失败，请重试',
            icon: 'none'
          });
        }
      }
    }
  }
}
</script>

<style lang="scss" scoped>
/* 页面容器 */
.flash-create-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #F5F7FA;
  position: relative;
}

/* 导航栏样式 */
.navbar {
  position: sticky;
  top: 0;
  left: 0;
  right: 0;
  background: linear-gradient(135deg, #FF7600, #FF3C00);
  color: white;
  padding: 48px 20px 16px;
  display: flex;
  align-items: center;
  z-index: 100;
  box-shadow: 0 2px 8px rgba(255, 120, 0, 0.15);
}

.navbar-left {
  width: 40px;
}

.back-button {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.navbar-title {
  flex: 1;
  text-align: center;
}

.title-text {
  font-size: 18px;
  font-weight: 600;
}

.navbar-right {
  min-width: 40px;
}

.save-button {
  font-size: 16px;
  padding: 6px 12px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 16px;
}

/* 内容区域 */
.content-area {
  flex: 1;
  box-sizing: border-box;
  height: calc(100vh - 80px - 60px);
}

/* 表单区域 */
.form-section {
  padding: 12px;
}

.form-group {
  background: #FFFFFF;
  border-radius: 8px;
  margin-bottom: 12px;
  overflow: hidden;
}

.form-header {
  padding: 12px 16px;
  font-size: 16px;
  font-weight: 600;
  color: #333333;
  border-bottom: 1px solid #F5F5F5;
}

.form-item {
  padding: 12px 16px;
  border-bottom: 1px solid #F5F5F5;
  position: relative;
}

.form-item:last-child {
  border-bottom: none;
}

.form-label {
  display: block;
  font-size: 14px;
  color: #333333;
  margin-bottom: 8px;
}

.required:after {
  content: '*';
  color: #FF3B30;
  margin-left: 4px;
}

.form-input {
  width: 100%;
  height: 40px;
  background: #F9F9F9;
  border: 1px solid #EEEEEE;
  border-radius: 4px;
  padding: 0 12px;
  font-size: 14px;
  color: #333333;
}

.form-textarea {
  width: 100%;
  height: 100px;
  background: #F9F9F9;
  border: 1px solid #EEEEEE;
  border-radius: 4px;
  padding: 8px 12px;
  font-size: 14px;
  color: #333333;
}

.form-counter {
  position: absolute;
  right: 16px;
  bottom: 12px;
  font-size: 12px;
  color: #999999;
}

.form-tip {
  font-size: 12px;
  color: #999999;
  margin-top: 8px;
  display: block;
}

.form-error {
  font-size: 12px;
  color: #FF3B30;
  margin-top: 8px;
  display: block;
}

.discount-value {
  height: 40px;
  line-height: 40px;
  font-size: 16px;
  color: #FF3B30;
  font-weight: 600;
}

.picker-value {
  width: 100%;
  height: 40px;
  background: #F9F9F9;
  border: 1px solid #EEEEEE;
  border-radius: 4px;
  padding: 0 12px;
  font-size: 14px;
  color: #333333;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.arrow-icon {
  width: 0;
  height: 0;
  border-left: 5px solid transparent;
  border-right: 5px solid transparent;
  border-top: 5px solid #999999;
}

/* 图片上传 */
.image-uploader {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -4px;
}

.image-item {
  width: calc(25% - 8px);
  margin: 4px;
  position: relative;
  aspect-ratio: 1;
}

.detail-image-item {
  width: calc(33.33% - 8px);
}

.uploaded-image {
  width: 100%;
  height: 100%;
  border-radius: 4px;
}

.delete-icon {
  position: absolute;
  top: -8px;
  right: -8px;
  width: 20px;
  height: 20px;
  z-index: 10;
}

.upload-button {
  width: calc(25% - 8px);
  margin: 4px;
  aspect-ratio: 1;
  background: #F9F9F9;
  border: 1px dashed #DDDDDD;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #999999;
}

.detail-upload {
  width: calc(33.33% - 8px);
}

/* 活动提示 */
.tips-list {
  padding: 0 16px;
}

.tip-item {
  display: flex;
  padding: 12px 0;
  border-bottom: 1px solid #F5F5F5;
}

.tip-item:last-child {
  border-bottom: none;
}

.tip-icon {
  width: 36px;
  height: 36px;
  margin-right: 12px;
  color: #FF7600;
}

.tip-content {
  flex: 1;
}

.tip-title {
  font-size: 16px;
  font-weight: 500;
  color: #333333;
  margin-bottom: 4px;
}

.tip-desc {
  font-size: 14px;
  color: #666666;
  line-height: 1.5;
}

/* 活动推广样式 */
.form-group .marketing-promotion-container {
  margin-top: 10px;
}

/* 底部空间 */
.bottom-space {
  height: 80px;
}

/* 底部操作栏 */
.bottom-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #FFFFFF;
  padding: 10px 16px;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);
  z-index: 90;
  display: flex;
}

.action-button {
  flex: 1;
  height: 40px;
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 6px;
}

.action-button.preview {
  background: #F5F5F5;
  color: #666666;
}

.action-button.save {
  background: linear-gradient(135deg, #FF7600, #FF3C00);
  color: white;
}

.button-text {
  font-size: 16px;
  font-weight: 500;
}

@media screen and (min-width: 768px) {
  .form-section {
    max-width: 600px;
    margin: 0 auto;
  }
  
  .bottom-bar {
    max-width: 600px;
    left: 50%;
    transform: translateX(-50%);
  }
}
</style>