/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 移除阿里妈妈字体引用，改用系统默认字体 */
/* 移除原有阿里妈妈字体定义
$uni-font-family: 'AlimamaShuHeiTi';
$uni-font-family-text: 'AlimamaShuHeiTi', -apple-system, BlinkMacSystemFont, 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;
*/
body, html, #app, .index-container {
  font-family: "AlimamaShuHeiTi", -apple-system, BlinkMacSystemFont, "Helvetica Neue", "PingFang SC", "Microsoft YaHei", sans-serif;
}
.manual-container {
  min-height: 100vh;
  background-color: #F5F7FA;
  padding-bottom: 20px;
}

/* 导航栏样式 */
.navbar {
  position: -webkit-sticky;
  position: sticky;
  top: 0;
  left: 0;
  right: 0;
  background: linear-gradient(135deg, #2980b9, #2c3e50);
  color: #fff;
  padding: 44px 16px 15px;
  display: flex;
  align-items: center;
  z-index: 100;
  box-shadow: 0 4px 20px rgba(41, 128, 185, 0.2);
}
.navbar-back {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.back-icon {
  width: 12px;
  height: 12px;
  border-left: 2px solid #fff;
  border-bottom: 2px solid #fff;
  transform: rotate(45deg);
}
.navbar-title {
  flex: 1;
  text-align: center;
  font-size: 18px;
  font-weight: 600;
  letter-spacing: 0.5px;
}
.navbar-right {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.help-icon {
  width: 24px;
  height: 24px;
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: bold;
}

/* 输入区域样式 */
.input-area {
  margin: 25px 15px;
  background: #FFFFFF;
  border-radius: 20px;
  padding: 25px 20px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.08);
}
.input-header {
  margin-bottom: 25px;
}
.input-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}
.code-input-box {
  position: relative;
  margin-bottom: 25px;
}
.code-input {
  width: 100%;
  height: 56px;
  background: #F5F7FA;
  border-radius: 16px;
  padding: 0 18px;
  font-size: 17px;
  color: #333;
  border: 1px solid rgba(0, 0, 0, 0.05);
  transition: border-color 0.2s, box-shadow 0.2s;
}
.code-input:focus {
  border-color: #2980b9;
  box-shadow: 0 0 0 3px rgba(41, 128, 185, 0.1);
}
.clear-btn {
  position: absolute;
  right: 18px;
  top: 50%;
  transform: translateY(-50%);
  width: 24px;
  height: 24px;
  border-radius: 12px;
  background: #C7C7CC;
  color: #FFFFFF;
  font-size: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.verify-btn {
  width: 100%;
  height: 56px;
  background: #E5E5EA;
  color: #8E8E93;
  border-radius: 16px;
  font-size: 17px;
  font-weight: 600;
  margin-bottom: 18px;
  transition: background-color 0.2s, transform 0.1s;
}
.verify-btn.active {
  background: #2980b9;
  color: #FFFFFF;
}
.verify-btn.active:active {
  transform: scale(0.98);
}
.tip-text {
  text-align: center;
  font-size: 13px;
  color: #8E8E93;
}

/* 历史记录样式 */
.history-section {
  margin: 0 15px 25px;
  background: #FFFFFF;
  border-radius: 20px;
  padding: 25px 20px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.08);
}
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}
.section-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}
.view-all {
  font-size: 14px;
  color: #2980b9;
  font-weight: 500;
}
.empty-history {
  padding: 30px 0;
  text-align: center;
}
.empty-text {
  font-size: 14px;
  color: #999;
}
.history-item {
  display: flex;
  align-items: center;
  padding: 15px 0;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}
.history-item:last-child {
  border-bottom: none;
  padding-bottom: 0;
}
.record-type {
  width: 60px;
  height: 28px;
  border-radius: 14px;
  font-size: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 14px;
  font-weight: 500;
}
.type-group {
  background-color: rgba(39, 174, 96, 0.15);
  color: #27ae60;
}
.type-coupon {
  background-color: rgba(41, 128, 185, 0.15);
  color: #2980b9;
}
.type-flash {
  background-color: rgba(231, 76, 60, 0.15);
  color: #e74c3c;
}
.record-content {
  flex: 1;
}
.record-main {
  margin-bottom: 4px;
}
.record-title {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin-right: 8px;
}
.record-code {
  font-size: 12px;
  color: #999;
}
.record-info {
  display: flex;
  font-size: 12px;
  color: #999;
}
.record-status {
  font-size: 13px;
  font-weight: 500;
}
.status-success {
  color: #30D158;
}
.status-pending {
  color: #FF9500;
}
.status-failed {
  color: #FF453A;
}

/* 核销弹窗样式 */
.verification-popup {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
  z-index: 999;
  display: flex;
  align-items: center;
  justify-content: center;
}
.popup-content {
  width: 85%;
  background: #fff;
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.3);
}
.popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 18px 20px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  background: #f8f8f8;
}
.popup-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}
.popup-close {
  font-size: 22px;
  color: #777;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 18px;
}
.popup-close:active {
  background: rgba(0, 0, 0, 0.05);
}
.verification-info {
  padding: 20px;
}
.info-item {
  display: flex;
  margin-bottom: 16px;
}
.info-item:last-child {
  margin-bottom: 0;
}
.info-label {
  width: 90px;
  font-size: 15px;
  color: #777;
}
.info-value {
  flex: 1;
  font-size: 15px;
  color: #333;
  font-weight: 500;
}
.verification-actions {
  display: flex;
  border-top: 1px solid rgba(0, 0, 0, 0.05);
}
.btn-cancel, .btn-confirm {
  flex: 1;
  height: 56px;
  line-height: 56px;
  text-align: center;
  font-size: 17px;
  font-weight: 500;
  border: none;
  border-radius: 0;
}
.btn-cancel {
  background: #f8f8f8;
  color: #666;
}
.btn-confirm {
  background: #2980b9;
  color: #fff;
}