<template>
  <view>
    <!-- 红包入口卡片 -->
    <view class="red-packet-card" @click="showDialog = true">
      <image class="icon" src="/static/images/red-packet-apple.svg" />
      <view class="info">
        <view class="title">任务红包</view>
        <view class="desc">设置任务红包，用户转发/助力后可抢</view>
      </view>
      <view class="status" :class="{ set: isSet }">{{ isSet ? '已设置' : '未设置' }}</view>
      <image class="arrow" src="/static/images/arrow-right.svg" />
    </view>

    <!-- 红包设置弹窗 -->
    <uni-popup v-model="showDialog" type="bottom">
      <view class="red-packet-setup-popup">
        <view class="popup-title">设置任务红包</view>
        <view class="form-row">
          <text class="label">红包总金额</text>
          <input v-model="form.amount" type="number" placeholder="请输入金额" />
          <text class="unit">元</text>
        </view>
        <view class="form-row">
          <text class="label">红包个数</text>
          <input v-model="form.count" type="number" placeholder="请输入个数" />
          <text class="unit">个</text>
        </view>
        <view class="form-row">
          <text class="label">任务要求</text>
          <picker :range="taskOptions" :value="form.taskType" @change="onTaskTypeChange">
            <view class="picker-value">{{ taskOptions[form.taskType] }}</view>
          </picker>
        </view>
        <button class="primary-btn" @click="confirmRedPacket">确定</button>
      </view>
    </uni-popup>
  </view>
</template>

<script setup>
import { ref, watch, computed } from 'vue'
const props = defineProps({ modelValue: Object })
const emit = defineEmits(['update:modelValue'])

const showDialog = ref(false)
const form = ref({
  amount: '',
  count: '',
  taskType: 0
})
const taskOptions = ['转发到群', '邀请助力', '完成浏览', '自定义']

const isSet = computed(() => !!form.value.amount && !!form.value.count)

watch(
  () => props.modelValue,
  (val) => {
    if (val) Object.assign(form.value, val)
  },
  { immediate: true }
)

function confirmRedPacket() {
  if (!form.value.amount || !form.value.count) {
    uni.showToast({ title: '请填写完整', icon: 'none' })
    return
  }
  emit('update:modelValue', { ...form.value })
  showDialog.value = false
}

function onTaskTypeChange(e) {
  form.value.taskType = e.detail.value
}
</script>

<style scoped>
.red-packet-card {
  display: flex;
  align-items: center;
  background: #fff;
  border-radius: 18rpx;
  box-shadow: 0 4rpx 16rpx rgba(22,119,255,0.08);
  padding: 24rpx 30rpx;
  margin: 32rpx 0;
  cursor: pointer;
}
.icon {
  width: 60rpx;
  height: 60rpx;
  margin-right: 20rpx;
}
.info {
  flex: 1;
}
.title {
  font-size: 30rpx;
  font-weight: 600;
  color: #1677FF;
}
.desc {
  font-size: 24rpx;
  color: #888;
  margin-top: 4rpx;
}
.status {
  font-size: 24rpx;
  color: #bbb;
  margin-right: 16rpx;
}
.status.set {
  color: #1677FF;
}
.arrow {
  width: 32rpx;
  height: 32rpx;
}
.red-packet-setup-popup {
  background: #fff;
  border-radius: 24rpx 24rpx 0 0;
  padding: 40rpx 30rpx 30rpx 30rpx;
}
.popup-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #222;
  text-align: center;
  margin-bottom: 30rpx;
}
.form-row {
  display: flex;
  align-items: center;
  margin-bottom: 28rpx;
}
.label {
  width: 160rpx;
  font-size: 28rpx;
  color: #333;
}
input {
  flex: 1;
  font-size: 28rpx;
  border: none;
  border-bottom: 1rpx solid #eee;
  background: transparent;
  padding: 10rpx 0;
  margin: 0 10rpx;
}
.unit {
  font-size: 26rpx;
  color: #888;
}
.picker-value {
  flex: 1;
  font-size: 28rpx;
  color: #1677FF;
  padding: 10rpx 0;
}
.primary-btn {
  width: 100%;
  height: 80rpx;
  background: linear-gradient(90deg, #1677FF 60%, #50aaff 100%);
  color: #fff;
  font-size: 30rpx;
  border-radius: 40rpx;
  margin-top: 30rpx;
  font-weight: 600;
}
</style> 