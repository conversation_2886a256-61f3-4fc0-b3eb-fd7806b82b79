'use strict';
const restoreCursor = require('restore-cursor');

let hidden = false;

exports.show = stream => {
	const s = stream || process.stderr;

	if (!s.isTTY) {
		return;
	}

	hidden = false;
	s.write('\u001b[?25h');
};

exports.hide = stream => {
	const s = stream || process.stderr;

	if (!s.isTTY) {
		return;
	}

	restoreCursor();
	hidden = true;
	s.write('\u001b[?25l');
};

exports.toggle = (force, stream) => {
	if (force !== undefined) {
		hidden = force;
	}

	if (hidden) {
		exports.show(stream);
	} else {
		exports.hide(stream);
	}
};
