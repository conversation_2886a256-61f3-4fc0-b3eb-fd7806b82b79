// try {
//   var vueVersion = require('megalo/package.json').version
// } catch (e) {}

// var packageName = require('./package.json').name
// var packageVersion = require('./package.json').version
// if (vueVersion && vueVersion !== packageVersion) {
//   throw new Error(
//     '\n\nVue packages version mismatch:\n\n' +
//     '- vue@' + vueVersion + '\n' +
//     '- ' + packageName + '@' + packageVersion + '\n\n' +
//     'This may cause things to work incorrectly. Make sure to use the same version for both.\n' +
//     'If you are using megalo-loader, re-installing them should bump ' + packageName + ' to the latest.\n'
//   )
// }

module.exports = require('./build')
