<template>
  <view class="loyalty-container">
    <!-- 自定义导航栏 -->
    <view class="navbar">
      <view class="navbar-back" @click="goBack">
        <view class="back-icon"></view>
      </view>
      <text class="navbar-title">客户忠诚度管理</text>
      <view class="navbar-right">
        <view class="help-icon">?</view>
      </view>
    </view>
    
    <!-- 忠诚度数据概览 -->
    <view class="overview-section">
      <view class="overview-card">
        <view class="overview-item">
          <text class="overview-value">{{loyaltyData.repeatRate}}%</text>
          <text class="overview-label">复购率</text>
        </view>
        <view class="overview-item">
          <text class="overview-value">{{loyaltyData.avgLifetime}}</text>
          <text class="overview-label">客户生命周期</text>
        </view>
        <view class="overview-item">
          <text class="overview-value">{{loyaltyData.loyalCustomers}}</text>
          <text class="overview-label">忠诚客户数</text>
        </view>
        <view class="overview-item">
          <text class="overview-value">{{loyaltyData.npsScore}}</text>
          <text class="overview-label">NPS得分</text>
        </view>
      </view>
    </view>
    
    <!-- 忠诚度趋势图 -->
    <view class="trend-section">
      <view class="section-header">
        <text class="section-title">忠诚度趋势</text>
        <view class="date-selector">
          <text class="date-label">{{currentDateRange}}</text>
          <view class="date-icon" @click="showDatePicker">📅</view>
        </view>
      </view>
      
      <view class="trend-card">
        <view class="chart-tabs">
          <view 
            v-for="(tab, index) in chartTabs" 
            :key="index"
            :class="['tab-item', {'active': currentChartTab === tab.value}]"
            @click="switchChartTab(tab.value)">
            {{tab.name}}
          </view>
        </view>
        
        <view class="chart-container">
          <!-- 这里应该是图表组件，暂时用占位符 -->
          <view class="chart-placeholder">
            <text class="chart-text">{{getChartTitle()}}</text>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 忠诚度管理模块 -->
    <view class="module-section">
      <view class="section-header">
        <text class="section-title">忠诚度管理</text>
      </view>
      
      <view class="module-grid">
        <!-- 复购激励 -->
        <view class="module-card" @click="navigateTo('./repurchase')">
          <view class="card-icon repurchase-icon">🔄</view>
          <text class="card-name">复购激励</text>
          <text class="card-desc">复购奖励、折扣方案</text>
          <view class="arrow-icon">›</view>
        </view>
        
        <!-- 连续购买特权 -->
        <view class="module-card" @click="navigateTo('./continuous')">
          <view class="card-icon continuous-icon">📆</view>
          <text class="card-name">连续购买特权</text>
          <text class="card-desc">连续消费奖励机制</text>
          <view class="arrow-icon">›</view>
        </view>
        
        <!-- 推荐计划 -->
        <view class="module-card" @click="navigateTo('./referral')">
          <view class="card-icon referral-icon">👥</view>
          <text class="card-name">推荐计划</text>
          <text class="card-desc">客户推荐奖励机制</text>
          <view class="arrow-icon">›</view>
        </view>
        
        <!-- 会员专享 -->
        <view class="module-card" @click="navigateTo('./exclusive')">
          <view class="card-icon exclusive-icon">🎁</view>
          <text class="card-name">会员专享</text>
          <text class="card-desc">专属服务、专享价格</text>
          <view class="arrow-icon">›</view>
        </view>
      </view>
    </view>
    
    <!-- 忠诚客户排行 -->
    <view class="ranking-section">
      <view class="section-header">
        <text class="section-title">忠诚客户排行</text>
        <text class="section-more" @click="navigateTo('./loyal-customers')">查看更多</text>
      </view>
      
      <view class="ranking-card">
        <view 
          v-for="(customer, index) in loyalCustomers" 
          :key="index"
          class="ranking-item"
          @click="viewCustomerDetail(customer.id)">
          <view class="ranking-number">{{index + 1}}</view>
          <image class="customer-avatar" :src="customer.avatar" mode="aspectFill"></image>
          <view class="customer-info">
            <text class="customer-name">{{customer.name}}</text>
            <text class="customer-meta">{{customer.purchaseCount}}次消费 | 客户生命周期{{customer.lifetime}}</text>
          </view>
          <view class="customer-value">
            <text class="value-label">客户价值</text>
            <text class="value-number">¥{{customer.totalSpent}}</text>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 忠诚度提升工具 -->
    <view class="tools-section">
      <view class="section-header">
        <text class="section-title">忠诚度提升工具</text>
      </view>
      
      <view class="tools-list">
        <view class="tool-item" @click="navigateTo('./win-back')">
          <view class="tool-icon win-back-icon">🔔</view>
          <view class="tool-content">
            <text class="tool-name">客户召回计划</text>
            <text class="tool-desc">针对流失客户的召回活动</text>
          </view>
          <view class="arrow-icon">›</view>
        </view>
        
        <view class="tool-item" @click="navigateTo('./vip-care')">
          <view class="tool-icon vip-care-icon">👑</view>
          <view class="tool-content">
            <text class="tool-name">VIP客户关怀</text>
            <text class="tool-desc">高价值客户专属服务</text>
          </view>
          <view class="arrow-icon">›</view>
        </view>
        
        <view class="tool-item" @click="navigateTo('./satisfaction')">
          <view class="tool-icon satisfaction-icon">😊</view>
          <view class="tool-content">
            <text class="tool-name">满意度管理</text>
            <text class="tool-desc">客户满意度调查与改进</text>
          </view>
          <view class="arrow-icon">›</view>
        </view>
        
        <view class="tool-item" @click="navigateTo('./milestone')">
          <view class="tool-icon milestone-icon">🏆</view>
          <view class="tool-content">
            <text class="tool-name">消费里程碑奖励</text>
            <text class="tool-desc">特定消费额度达成奖励</text>
          </view>
          <view class="arrow-icon">›</view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      loyaltyData: {
        repeatRate: '68.5',
        avgLifetime: '14个月',
        loyalCustomers: '256',
        npsScore: '8.7'
      },
      currentDateRange: '2023-01-01 至 2023-05-31',
      currentChartTab: 'repeat',
      chartTabs: [
        { name: '复购率', value: 'repeat' },
        { name: '客户生命周期', value: 'lifetime' },
        { name: '客户价值', value: 'value' }
      ],
      loyalCustomers: [
        {
          id: '10001',
          name: '张三',
          avatar: '/static/images/avatar-1.png',
          purchaseCount: 28,
          lifetime: '18个月',
          totalSpent: '5,680.50'
        },
        {
          id: '10002',
          name: '李四',
          avatar: '/static/images/avatar-2.png',
          purchaseCount: 24,
          lifetime: '16个月',
          totalSpent: '4,920.00'
        },
        {
          id: '10003',
          name: '王五',
          avatar: '/static/images/avatar-3.png',
          purchaseCount: 22,
          lifetime: '15个月',
          totalSpent: '4,560.80'
        },
        {
          id: '10004',
          name: '赵六',
          avatar: '/static/images/avatar-4.png',
          purchaseCount: 20,
          lifetime: '14个月',
          totalSpent: '3,980.25'
        },
        {
          id: '10005',
          name: '钱七',
          avatar: '/static/images/avatar-5.png',
          purchaseCount: 18,
          lifetime: '12个月',
          totalSpent: '3,450.60'
        }
      ]
    }
  },
  methods: {
    goBack() {
      uni.navigateBack();
    },
    navigateTo(url) {
      uni.navigateTo({ url });
    },
    showDatePicker() {
      uni.showToast({
        title: '日期选择功能开发中',
        icon: 'none'
      });
    },
    switchChartTab(tab) {
      this.currentChartTab = tab;
    },
    getChartTitle() {
      const titles = {
        repeat: '客户复购率趋势图',
        lifetime: '客户生命周期趋势图',
        value: '客户价值趋势图'
      };
      return titles[this.currentChartTab] || '趋势图';
    },
    viewCustomerDetail(id) {
      uni.navigateTo({
        url: `/pages/customer/index?id=${id}`
      });
    }
  }
}
</script>

<style>
.loyalty-container {
  min-height: 100vh;
  background-color: #f5f7fa;
  padding-bottom: 20px;
}

.navbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 44px 16px 10px;
  background: linear-gradient(135deg, #1677FF, #065DD2);
  position: relative;
  z-index: 100;
}

.navbar-back {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
}

.back-icon {
  width: 12px;
  height: 12px;
  border-left: 2px solid #fff;
  border-bottom: 2px solid #fff;
  transform: rotate(45deg);
}

.navbar-title {
  color: #fff;
  font-size: 18px;
  font-weight: 600;
  flex: 1;
  text-align: center;
}

.navbar-right {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.help-icon {
  width: 24px;
  height: 24px;
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  font-size: 14px;
  font-weight: 600;
}

.overview-section {
  padding: 16px;
}

.overview-card {
  background-color: #fff;
  border-radius: 8px;
  padding: 16px;
  display: flex;
  justify-content: space-between;
  box-shadow: 0 2px 8px rgba(0,0,0,0.05);
}

.overview-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.overview-value {
  font-size: 20px;
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
}

.overview-label {
  font-size: 12px;
  color: #999;
}

.trend-section {
  padding: 0 16px;
  margin-bottom: 20px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.section-more {
  font-size: 12px;
  color: #1677FF;
}

.date-selector {
  display: flex;
  align-items: center;
  font-size: 14px;
  color: #666;
}

.date-icon {
  margin-left: 8px;
  font-size: 16px;
}

.trend-card {
  background-color: #fff;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.05);
}

.chart-tabs {
  display: flex;
  border-bottom: 1px solid #f0f0f0;
  margin-bottom: 16px;
}

.tab-item {
  padding: 8px 16px;
  font-size: 14px;
  color: #666;
  position: relative;
  cursor: pointer;
}

.tab-item.active {
  color: #1677FF;
  font-weight: 500;
}

.tab-item.active::after {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 0;
  right: 0;
  height: 2px;
  background-color: #1677FF;
}

.chart-container {
  height: 200px;
}

.chart-placeholder {
  height: 100%;
  background-color: #f9f9f9;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.chart-text {
  font-size: 14px;
  color: #999;
}

.module-section {
  padding: 0 16px;
  margin-bottom: 20px;
}

.module-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
}

.module-card {
  background-color: #fff;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.05);
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
}

.card-icon {
  width: 48px;
  height: 48px;
  border-radius: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  margin-bottom: 12px;
}

.repurchase-icon {
  background-color: #e6f7ff;
  color: #1677FF;
}

.continuous-icon {
  background-color: #fff7e6;
  color: #fa8c16;
}

.referral-icon {
  background-color: #f6ffed;
  color: #52c41a;
}

.exclusive-icon {
  background-color: #fff1f0;
  color: #f5222d;
}

.card-name {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
}

.card-desc {
  font-size: 12px;
  color: #999;
  text-align: center;
}

.arrow-icon {
  position: absolute;
  right: 12px;
  top: 12px;
  font-size: 20px;
  color: #ccc;
}

.ranking-section {
  padding: 0 16px;
  margin-bottom: 20px;
}

.ranking-card {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.05);
  overflow: hidden;
}

.ranking-item {
  display: flex;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.ranking-item:last-child {
  border-bottom: none;
}

.ranking-number {
  width: 24px;
  height: 24px;
  border-radius: 12px;
  background-color: #f0f0f0;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: 600;
  color: #666;
  margin-right: 12px;
}

.ranking-item:nth-child(1) .ranking-number {
  background-color: #FFD700;
  color: #fff;
}

.ranking-item:nth-child(2) .ranking-number {
  background-color: #C0C0C0;
  color: #fff;
}

.ranking-item:nth-child(3) .ranking-number {
  background-color: #CD7F32;
  color: #fff;
}

.customer-avatar {
  width: 40px;
  height: 40px;
  border-radius: 20px;
  margin-right: 12px;
  background-color: #f0f0f0;
}

.customer-info {
  flex: 1;
}

.customer-name {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
  display: block;
}

.customer-meta {
  font-size: 12px;
  color: #999;
}

.customer-value {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.value-label {
  font-size: 12px;
  color: #999;
  margin-bottom: 2px;
}

.value-number {
  font-size: 14px;
  font-weight: 600;
  color: #ff6a00;
}

.tools-section {
  padding: 0 16px;
}

.tools-list {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.05);
  overflow: hidden;
}

.tool-item {
  display: flex;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.tool-item:last-child {
  border-bottom: none;
}

.tool-icon {
  width: 40px;
  height: 40px;
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  margin-right: 12px;
}

.win-back-icon {
  background-color: #fff7e6;
  color: #fa8c16;
}

.vip-care-icon {
  background-color: #e6f7ff;
  color: #1677FF;
}

.satisfaction-icon {
  background-color: #f6ffed;
  color: #52c41a;
}

.milestone-icon {
  background-color: #fff1f0;
  color: #f5222d;
}

.tool-content {
  flex: 1;
}

.tool-name {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
  display: block;
}

.tool-desc {
  font-size: 12px;
  color: #999;
}
</style> 