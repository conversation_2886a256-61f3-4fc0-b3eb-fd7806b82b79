{"version": 3, "file": "member-agreement.js", "sources": ["subPackages/merchant-admin-marketing/pages/marketing/member/member-agreement.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/c3ViUGFja2FnZXNcbWVyY2hhbnQtYWRtaW4tbWFya2V0aW5nXHBhZ2VzXG1hcmtldGluZ1xtZW1iZXJcbWVtYmVyLWFncmVlbWVudC52dWU"], "sourcesContent": ["<template>\r\n  <view class=\"agreement-container\">\r\n    <!-- 自定义导航栏 -->\r\n    <view class=\"navbar\">\r\n      <view class=\"navbar-back\" @click=\"goBack\">\r\n        <view class=\"back-icon\"></view>\r\n      </view>\r\n      <text class=\"navbar-title\">会员协议</text>\r\n      <view class=\"navbar-right\"></view>\r\n    </view>\r\n    \r\n    <!-- 会员协议内容 -->\r\n    <view class=\"agreement-content\">\r\n      <!-- 协议开关 -->\r\n      <view class=\"section-card\">\r\n        <view class=\"switch-item\">\r\n          <view class=\"switch-content\">\r\n            <text class=\"switch-title\">会员协议</text>\r\n            <text class=\"switch-desc\">开启后，用户注册成为会员时需要同意会员协议</text>\r\n          </view>\r\n          <switch :checked=\"agreementSettings.enabled\" @change=\"toggleAgreement\" color=\"#4A00E0\" />\r\n        </view>\r\n      </view>\r\n      \r\n      <block v-if=\"agreementSettings.enabled\">\r\n        <!-- 协议编辑 -->\r\n        <view class=\"section-card\">\r\n          <view class=\"section-title\">协议标题</view>\r\n          <view class=\"form-item\">\r\n            <input class=\"form-input-full\" v-model=\"agreementSettings.title\" placeholder=\"请输入协议标题\" />\r\n          </view>\r\n          \r\n          <view class=\"section-title\">协议内容</view>\r\n          <view class=\"editor-toolbar\">\r\n            <view class=\"toolbar-btn\" @click=\"formatText('bold')\">\r\n              <text class=\"toolbar-icon bold\">B</text>\r\n            </view>\r\n            <view class=\"toolbar-btn\" @click=\"formatText('italic')\">\r\n              <text class=\"toolbar-icon italic\">I</text>\r\n            </view>\r\n            <view class=\"toolbar-btn\" @click=\"formatText('underline')\">\r\n              <text class=\"toolbar-icon underline\">U</text>\r\n            </view>\r\n            <view class=\"toolbar-btn\" @click=\"formatText('header')\">\r\n              <text class=\"toolbar-icon\">H</text>\r\n            </view>\r\n            <view class=\"toolbar-btn\" @click=\"formatText('list')\">\r\n              <text class=\"toolbar-icon\">•</text>\r\n            </view>\r\n          </view>\r\n          <view class=\"form-item\">\r\n            <textarea class=\"form-textarea\" v-model=\"agreementSettings.content\" placeholder=\"请输入会员协议内容\" />\r\n          </view>\r\n          \r\n          <view class=\"form-tips\">提示：可以使用富文本编辑器编辑更复杂的格式</view>\r\n        </view>\r\n        \r\n        <!-- 协议预览 -->\r\n        <view class=\"section-card\">\r\n          <view class=\"section-title\">协议预览</view>\r\n          \r\n          <view class=\"preview-container\">\r\n            <view class=\"preview-title\">{{agreementSettings.title || '会员协议'}}</view>\r\n            <view class=\"preview-content\">\r\n              <text class=\"preview-text\">{{agreementSettings.content || '请输入会员协议内容'}}</text>\r\n            </view>\r\n          </view>\r\n          \r\n          <button class=\"preview-btn\" @click=\"showFullPreview\">查看完整预览</button>\r\n        </view>\r\n        \r\n        <!-- 协议设置 -->\r\n        <view class=\"section-card\">\r\n          <view class=\"section-title\">协议设置</view>\r\n          \r\n          <view class=\"form-item switch-item\">\r\n            <text class=\"form-label\">强制阅读</text>\r\n            <switch :checked=\"agreementSettings.forceRead\" @change=\"toggleForceRead\" color=\"#4A00E0\" />\r\n          </view>\r\n          \r\n          <view class=\"form-item\" v-if=\"agreementSettings.forceRead\">\r\n            <text class=\"form-label\">最短阅读时间</text>\r\n            <view class=\"form-input-group\">\r\n              <input type=\"number\" class=\"form-input\" v-model=\"agreementSettings.minReadTime\" />\r\n              <text class=\"input-suffix\">秒</text>\r\n            </view>\r\n          </view>\r\n          \r\n          <view class=\"form-item switch-item\">\r\n            <text class=\"form-label\">更新提醒</text>\r\n            <switch :checked=\"agreementSettings.updateNotify\" @change=\"toggleUpdateNotify\" color=\"#4A00E0\" />\r\n          </view>\r\n          \r\n          <view class=\"form-item switch-item\">\r\n            <text class=\"form-label\">更新后重新同意</text>\r\n            <switch :checked=\"agreementSettings.reconfirmAfterUpdate\" @change=\"toggleReconfirm\" color=\"#4A00E0\" />\r\n          </view>\r\n        </view>\r\n        \r\n        <!-- 隐私政策 -->\r\n        <view class=\"section-card\">\r\n          <view class=\"section-title\">隐私政策</view>\r\n          \r\n          <view class=\"form-item switch-item\">\r\n            <text class=\"form-label\">启用隐私政策</text>\r\n            <switch :checked=\"agreementSettings.privacyEnabled\" @change=\"togglePrivacyPolicy\" color=\"#4A00E0\" />\r\n          </view>\r\n          \r\n          <view class=\"form-item\" v-if=\"agreementSettings.privacyEnabled\">\r\n            <text class=\"form-label\">隐私政策链接</text>\r\n            <view class=\"form-input-group\">\r\n              <input class=\"form-input\" v-model=\"agreementSettings.privacyUrl\" placeholder=\"请输入隐私政策链接\" />\r\n              <view class=\"input-btn\" @click=\"editPrivacyPolicy\">编辑</view>\r\n            </view>\r\n          </view>\r\n        </view>\r\n      </block>\r\n    </view>\r\n    \r\n    <!-- 保存按钮 -->\r\n    <view class=\"bottom-bar\" v-if=\"agreementSettings.enabled\">\r\n      <button class=\"save-btn\" @click=\"saveSettings\">保存设置</button>\r\n    </view>\r\n    \r\n    <!-- 全屏预览弹窗 -->\r\n    <view class=\"fullscreen-preview\" v-if=\"showPreview\">\r\n      <view class=\"preview-header\">\r\n        <text class=\"preview-header-title\">协议预览</text>\r\n        <view class=\"preview-close\" @click=\"closePreview\">×</view>\r\n      </view>\r\n      <scroll-view class=\"preview-scroll\" scroll-y>\r\n        <view class=\"preview-title\">{{agreementSettings.title || '会员协议'}}</view>\r\n        <view class=\"preview-content\">\r\n          <text class=\"preview-text\">{{agreementSettings.content || '请输入会员协议内容'}}</text>\r\n        </view>\r\n      </scroll-view>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  data() {\r\n    return {\r\n      // 协议设置\r\n      agreementSettings: {\r\n        enabled: true,\r\n        title: '磁州生活网会员服务协议',\r\n        content: `尊敬的用户：\r\n\r\n感谢您选择磁州生活网！本协议是您与磁州生活网之间关于成为会员、使用会员服务所订立的契约。请您仔细阅读本协议，确保理解协议中各条款的含义，特别是免责条款和服务限制条款。\r\n\r\n一、会员服务内容\r\n1.1 会员等级：本平台设有多个会员等级，不同等级享受不同权益。\r\n1.2 会员权益：包括但不限于会员折扣、积分加速、免费配送、生日礼包、专属客服等。\r\n1.3 会员积分：会员可通过消费、签到等方式获取积分，积分可用于兑换商品或服务。\r\n\r\n二、会员规则\r\n2.1 会员资格获取：用户可通过注册账号并满足相应条件获取会员资格。\r\n2.2 会员等级晋升：会员等级根据累计消费金额、成长值等因素自动晋升。\r\n2.3 会员有效期：除特殊说明外，会员资格长期有效。\r\n\r\n三、会员权利与义务\r\n3.1 会员有权享受平台提供的各项会员权益。\r\n3.2 会员应遵守平台各项规则，不得利用会员身份从事违法或损害平台利益的行为。\r\n3.3 会员应妥善保管账号信息，因会员个人原因导致的账号安全问题由会员自行承担责任。\r\n\r\n四、协议修改\r\n4.1 本平台有权根据业务发展需要修改本协议，修改后的协议将通过网站公告或其他方式通知会员。\r\n4.2 会员如不同意修改后的协议，可申请终止会员服务；继续使用会员服务则视为接受修改后的协议。\r\n\r\n五、免责声明\r\n5.1 因不可抗力或第三方原因导致的服务中断或终止，本平台不承担责任。\r\n5.2 本平台有权根据实际情况调整会员权益，但会提前通知会员。\r\n\r\n六、其他条款\r\n6.1 本协议的解释权归磁州生活网所有。\r\n6.2 本协议自会员同意之日起生效。\r\n\r\n如您对本协议有任何疑问，请联系客服咨询。`,\r\n        forceRead: true,\r\n        minReadTime: 10,\r\n        updateNotify: true,\r\n        reconfirmAfterUpdate: true,\r\n        privacyEnabled: true,\r\n        privacyUrl: 'https://www.cizhou.com/privacy'\r\n      },\r\n      \r\n      // 预览控制\r\n      showPreview: false\r\n    };\r\n  },\r\n  methods: {\r\n    goBack() {\r\n      uni.navigateBack();\r\n    },\r\n    \r\n    toggleAgreement(e) {\r\n      this.agreementSettings.enabled = e.detail.value;\r\n    },\r\n    \r\n    formatText(type) {\r\n      uni.showToast({\r\n        title: '富文本编辑功能开发中',\r\n        icon: 'none'\r\n      });\r\n    },\r\n    \r\n    toggleForceRead(e) {\r\n      this.agreementSettings.forceRead = e.detail.value;\r\n    },\r\n    \r\n    toggleUpdateNotify(e) {\r\n      this.agreementSettings.updateNotify = e.detail.value;\r\n    },\r\n    \r\n    toggleReconfirm(e) {\r\n      this.agreementSettings.reconfirmAfterUpdate = e.detail.value;\r\n    },\r\n    \r\n    togglePrivacyPolicy(e) {\r\n      this.agreementSettings.privacyEnabled = e.detail.value;\r\n    },\r\n    \r\n    editPrivacyPolicy() {\r\n      uni.showToast({\r\n        title: '隐私政策编辑功能开发中',\r\n        icon: 'none'\r\n      });\r\n    },\r\n    \r\n    showFullPreview() {\r\n      this.showPreview = true;\r\n    },\r\n    \r\n    closePreview() {\r\n      this.showPreview = false;\r\n    },\r\n    \r\n    saveSettings() {\r\n      uni.showLoading({\r\n        title: '保存中...'\r\n      });\r\n      \r\n      setTimeout(() => {\r\n        uni.hideLoading();\r\n        uni.showToast({\r\n          title: '会员协议设置保存成功',\r\n          icon: 'success'\r\n        });\r\n      }, 1000);\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style>\r\n/* 会员协议页面样式开始 */\r\n.agreement-container {\r\n  min-height: 100vh;\r\n  background-color: #F5F7FA;\r\n  padding-bottom: 100rpx;\r\n}\r\n\r\n/* 导航栏样式 */\r\n.navbar {\r\n  position: sticky;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  background: linear-gradient(135deg, #8E2DE2, #4A00E0);\r\n  color: #fff;\r\n  padding: 44px 16px 15px;\r\n  display: flex;\r\n  align-items: center;\r\n  z-index: 100;\r\n  box-shadow: 0 2px 10px rgba(74, 0, 224, 0.15);\r\n}\r\n\r\n.navbar-back {\r\n  width: 36px;\r\n  height: 36px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.back-icon {\r\n  width: 12px;\r\n  height: 12px;\r\n  border-left: 2px solid #fff;\r\n  border-bottom: 2px solid #fff;\r\n  transform: rotate(45deg);\r\n}\r\n\r\n.navbar-title {\r\n  flex: 1;\r\n  text-align: center;\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n  letter-spacing: 0.5px;\r\n}\r\n\r\n.navbar-right {\r\n  width: 36px;\r\n  height: 36px;\r\n}\r\n\r\n/* 协议内容样式 */\r\n.agreement-content {\r\n  padding: 20rpx;\r\n}\r\n\r\n.section-card {\r\n  background: #fff;\r\n  border-radius: 12rpx;\r\n  padding: 30rpx;\r\n  margin-bottom: 20rpx;\r\n  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n.section-title {\r\n  font-size: 28rpx;\r\n  font-weight: 600;\r\n  color: #333;\r\n  margin-bottom: 20rpx;\r\n  padding-bottom: 15rpx;\r\n  border-bottom: 1rpx solid #f0f0f0;\r\n}\r\n\r\n/* 开关样式 */\r\n.switch-item {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.switch-content {\r\n  flex: 1;\r\n}\r\n\r\n.switch-title {\r\n  font-size: 28rpx;\r\n  font-weight: 600;\r\n  color: #333;\r\n  margin-bottom: 8rpx;\r\n  display: block;\r\n}\r\n\r\n.switch-desc {\r\n  font-size: 24rpx;\r\n  color: #999;\r\n}\r\n\r\n/* 表单样式 */\r\n.form-item {\r\n  margin-bottom: 20rpx;\r\n}\r\n\r\n.form-label {\r\n  font-size: 26rpx;\r\n  color: #666;\r\n  margin-bottom: 10rpx;\r\n  display: block;\r\n}\r\n\r\n.form-input-group {\r\n  display: flex;\r\n  align-items: center;\r\n  border: 1rpx solid #ddd;\r\n  border-radius: 8rpx;\r\n  overflow: hidden;\r\n}\r\n\r\n.form-input {\r\n  flex: 1;\r\n  height: 80rpx;\r\n  padding: 0 20rpx;\r\n  font-size: 28rpx;\r\n}\r\n\r\n.form-input-full {\r\n  width: 100%;\r\n  height: 80rpx;\r\n  padding: 0 20rpx;\r\n  font-size: 28rpx;\r\n  border: 1rpx solid #ddd;\r\n  border-radius: 8rpx;\r\n  box-sizing: border-box;\r\n}\r\n\r\n.input-suffix {\r\n  padding: 0 20rpx;\r\n  font-size: 24rpx;\r\n  color: #999;\r\n  background: #f5f5f5;\r\n  height: 80rpx;\r\n  line-height: 80rpx;\r\n}\r\n\r\n.input-btn {\r\n  padding: 0 30rpx;\r\n  font-size: 26rpx;\r\n  color: #4A00E0;\r\n  background: rgba(74, 0, 224, 0.1);\r\n  height: 80rpx;\r\n  line-height: 80rpx;\r\n}\r\n\r\n.form-textarea {\r\n  width: 100%;\r\n  height: 400rpx;\r\n  border: 1rpx solid #ddd;\r\n  border-radius: 8rpx;\r\n  padding: 20rpx;\r\n  font-size: 28rpx;\r\n  box-sizing: border-box;\r\n}\r\n\r\n.form-tips {\r\n  font-size: 24rpx;\r\n  color: #999;\r\n  margin-top: 10rpx;\r\n}\r\n\r\n/* 编辑器工具栏 */\r\n.editor-toolbar {\r\n  display: flex;\r\n  background: #f5f5f5;\r\n  border-radius: 8rpx 8rpx 0 0;\r\n  border: 1rpx solid #ddd;\r\n  border-bottom: none;\r\n}\r\n\r\n.toolbar-btn {\r\n  width: 80rpx;\r\n  height: 80rpx;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  border-right: 1rpx solid #ddd;\r\n}\r\n\r\n.toolbar-btn:last-child {\r\n  border-right: none;\r\n}\r\n\r\n.toolbar-icon {\r\n  font-size: 28rpx;\r\n  color: #666;\r\n}\r\n\r\n.toolbar-icon.bold {\r\n  font-weight: bold;\r\n}\r\n\r\n.toolbar-icon.italic {\r\n  font-style: italic;\r\n}\r\n\r\n.toolbar-icon.underline {\r\n  text-decoration: underline;\r\n}\r\n\r\n/* 预览样式 */\r\n.preview-container {\r\n  border: 1rpx solid #ddd;\r\n  border-radius: 8rpx;\r\n  overflow: hidden;\r\n  margin-bottom: 20rpx;\r\n}\r\n\r\n.preview-title {\r\n  font-size: 32rpx;\r\n  font-weight: 600;\r\n  color: #333;\r\n  padding: 30rpx;\r\n  text-align: center;\r\n  border-bottom: 1rpx solid #f0f0f0;\r\n}\r\n\r\n.preview-content {\r\n  padding: 30rpx;\r\n  max-height: 400rpx;\r\n  overflow-y: auto;\r\n}\r\n\r\n.preview-text {\r\n  font-size: 28rpx;\r\n  color: #333;\r\n  line-height: 1.6;\r\n  white-space: pre-wrap;\r\n}\r\n\r\n.preview-btn {\r\n  background: rgba(74, 0, 224, 0.1);\r\n  color: #4A00E0;\r\n  border: none;\r\n  border-radius: 40rpx;\r\n  height: 80rpx;\r\n  line-height: 80rpx;\r\n  font-size: 28rpx;\r\n}\r\n\r\n/* 全屏预览 */\r\n.fullscreen-preview {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background: #fff;\r\n  z-index: 1000;\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n.preview-header {\r\n  height: 100rpx;\r\n  background: #4A00E0;\r\n  color: #fff;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  position: relative;\r\n}\r\n\r\n.preview-header-title {\r\n  font-size: 32rpx;\r\n  font-weight: 600;\r\n}\r\n\r\n.preview-close {\r\n  position: absolute;\r\n  right: 30rpx;\r\n  top: 50%;\r\n  transform: translateY(-50%);\r\n  font-size: 40rpx;\r\n  width: 60rpx;\r\n  height: 60rpx;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.preview-scroll {\r\n  flex: 1;\r\n  padding: 30rpx;\r\n}\r\n\r\n/* 底部保存栏 */\r\n.bottom-bar {\r\n  position: fixed;\r\n  bottom: 0;\r\n  left: 0;\r\n  right: 0;\r\n  background: #fff;\r\n  padding: 20rpx;\r\n  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n.save-btn {\r\n  background: #4A00E0;\r\n  color: #fff;\r\n  border: none;\r\n  border-radius: 40rpx;\r\n  height: 80rpx;\r\n  line-height: 80rpx;\r\n  font-size: 28rpx;\r\n  width: 100%;\r\n}\r\n/* 会员协议页面样式结束 */\r\n</style> ", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/subPackages/merchant-admin-marketing/pages/marketing/member/member-agreement.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni"], "mappings": ";;AA6IA,MAAK,YAAU;AAAA,EACb,OAAO;AACL,WAAO;AAAA;AAAA,MAEL,mBAAmB;AAAA,QACjB,SAAS;AAAA,QACT,OAAO;AAAA,QACP,SAAS;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAgCT,WAAW;AAAA,QACX,aAAa;AAAA,QACb,cAAc;AAAA,QACd,sBAAsB;AAAA,QACtB,gBAAgB;AAAA,QAChB,YAAY;AAAA,MACb;AAAA;AAAA,MAGD,aAAa;AAAA;EAEhB;AAAA,EACD,SAAS;AAAA,IACP,SAAS;AACPA,oBAAG,MAAC,aAAY;AAAA,IACjB;AAAA,IAED,gBAAgB,GAAG;AACjB,WAAK,kBAAkB,UAAU,EAAE,OAAO;AAAA,IAC3C;AAAA,IAED,WAAW,MAAM;AACfA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,MACR,CAAC;AAAA,IACF;AAAA,IAED,gBAAgB,GAAG;AACjB,WAAK,kBAAkB,YAAY,EAAE,OAAO;AAAA,IAC7C;AAAA,IAED,mBAAmB,GAAG;AACpB,WAAK,kBAAkB,eAAe,EAAE,OAAO;AAAA,IAChD;AAAA,IAED,gBAAgB,GAAG;AACjB,WAAK,kBAAkB,uBAAuB,EAAE,OAAO;AAAA,IACxD;AAAA,IAED,oBAAoB,GAAG;AACrB,WAAK,kBAAkB,iBAAiB,EAAE,OAAO;AAAA,IAClD;AAAA,IAED,oBAAoB;AAClBA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,MACR,CAAC;AAAA,IACF;AAAA,IAED,kBAAkB;AAChB,WAAK,cAAc;AAAA,IACpB;AAAA,IAED,eAAe;AACb,WAAK,cAAc;AAAA,IACpB;AAAA,IAED,eAAe;AACbA,oBAAAA,MAAI,YAAY;AAAA,QACd,OAAO;AAAA,MACT,CAAC;AAED,iBAAW,MAAM;AACfA,sBAAG,MAAC,YAAW;AACfA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACR,CAAC;AAAA,MACF,GAAE,GAAI;AAAA,IACT;AAAA,EACF;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC5PA,GAAG,WAAW,eAAe;"}