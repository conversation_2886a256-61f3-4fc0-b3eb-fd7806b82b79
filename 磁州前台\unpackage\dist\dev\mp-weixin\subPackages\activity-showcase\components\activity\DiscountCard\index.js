"use strict";
const common_vendor = require("../../../../../common/vendor.js");
if (!Array) {
  const _component_rect = common_vendor.resolveComponent("rect");
  const _component_line = common_vendor.resolveComponent("line");
  const _component_svg = common_vendor.resolveComponent("svg");
  const _component_path = common_vendor.resolveComponent("path");
  const _component_circle = common_vendor.resolveComponent("circle");
  (_component_rect + _component_line + _component_svg + _component_path + _component_circle)();
}
if (!Math) {
  ActivityCard();
}
const ActivityCard = () => "../ActivityCard.js";
const maxDisplayMerchants = 3;
const _sfc_main = {
  __name: "index",
  props: {
    item: {
      type: Object,
      required: true
    }
  },
  setup(__props) {
    const props = __props;
    const displayMerchants = common_vendor.computed(() => {
      if (!props.item.merchants || !props.item.merchants.length)
        return [];
      return props.item.merchants.slice(0, maxDisplayMerchants);
    });
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: __props.item.merchantCount
      }, __props.item.merchantCount ? {
        b: common_vendor.t(__props.item.merchantCount)
      } : {}, {
        c: common_vendor.f(__props.item.discountRules, (rule, index, i0) => {
          return common_vendor.e({
            a: index === __props.item.discountRules.length - 1
          }, index === __props.item.discountRules.length - 1 ? {} : {}, {
            b: common_vendor.t(rule.threshold),
            c: common_vendor.t(rule.discount),
            d: common_vendor.t(rule.discount),
            e: index,
            f: index === __props.item.discountRules.length - 1 ? 1 : ""
          });
        }),
        d: __props.item.startDate && __props.item.endDate
      }, __props.item.startDate && __props.item.endDate ? {
        e: common_vendor.p({
          x: "3",
          y: "4",
          width: "18",
          height: "18",
          rx: "2",
          ry: "2"
        }),
        f: common_vendor.p({
          x1: "16",
          y1: "2",
          x2: "16",
          y2: "6"
        }),
        g: common_vendor.p({
          x1: "8",
          y1: "2",
          x2: "8",
          y2: "6"
        }),
        h: common_vendor.p({
          x1: "3",
          y1: "10",
          x2: "21",
          y2: "10"
        }),
        i: common_vendor.p({
          xmlns: "http://www.w3.org/2000/svg",
          width: "16",
          height: "16",
          viewBox: "0 0 24 24",
          fill: "none",
          stroke: "currentColor",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        j: common_vendor.t(__props.item.startDate),
        k: common_vendor.t(__props.item.endDate)
      } : {}, {
        l: __props.item.scope
      }, __props.item.scope ? {
        m: common_vendor.p({
          d: "M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"
        }),
        n: common_vendor.p({
          cx: "12",
          cy: "7",
          r: "4"
        }),
        o: common_vendor.p({
          xmlns: "http://www.w3.org/2000/svg",
          width: "16",
          height: "16",
          viewBox: "0 0 24 24",
          fill: "none",
          stroke: "currentColor",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        p: common_vendor.t(__props.item.scope)
      } : {}, {
        q: __props.item.merchants && __props.item.merchants.length > 0
      }, __props.item.merchants && __props.item.merchants.length > 0 ? common_vendor.e({
        r: common_vendor.t(__props.item.merchants.length),
        s: common_vendor.f(displayMerchants.value, (merchant, index, i0) => {
          return {
            a: merchant.logo,
            b: common_vendor.t(merchant.name),
            c: index
          };
        }),
        t: __props.item.merchants.length > maxDisplayMerchants
      }, __props.item.merchants.length > maxDisplayMerchants ? {} : {}) : {}, {
        v: common_vendor.o(($event) => _ctx.$emit("favorite", __props.item.id)),
        w: common_vendor.o(($event) => _ctx.$emit("action", {
          id: __props.item.id,
          type: __props.item.type,
          status: __props.item.status
        })),
        x: common_vendor.p({
          item: __props.item
        })
      });
    };
  }
};
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-ef845547"]]);
wx.createComponent(Component);
//# sourceMappingURL=../../../../../../.sourcemap/mp-weixin/subPackages/activity-showcase/components/activity/DiscountCard/index.js.map
