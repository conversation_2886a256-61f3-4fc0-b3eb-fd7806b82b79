{"version": 3, "file": "success.js", "sources": ["pages/business/success.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvYnVzaW5lc3Mvc3VjY2Vzcy52dWU"], "sourcesContent": ["<template>\n  <view class=\"success-container\">\n    <!-- 顶部背景渐变 -->\n    <view class=\"top-gradient\"></view>\n    \n    <!-- 状态栏占位 -->\n    <view class=\"status-bar\" :style=\"{ height: statusBarHeight + 'px' }\"></view>\n    \n    <!-- 导航栏 -->\n    <view class=\"navbar\">\n      <view class=\"navbar-left\" @click=\"goBack\">\n        <image src=\"/static/images/tabbar/返回.png\" class=\"back-icon\"></image>\n      </view>\n      <view class=\"navbar-title\">商家入驻</view>\n      <view class=\"navbar-right\"></view>\n    </view>\n    \n    <!-- 内容区域 -->\n    <view class=\"content\">\n      <!-- 成功信息 -->\n      <view class=\"success-info\">\n        <view class=\"success-message-card\">\n          <image :src=\"celebrateIcon || '/static/images/tabbar/成功.png'\" class=\"success-message-icon\"></image>\n          <view class=\"success-message-content\">\n            <view class=\"success-message-title\">恭喜！您已成功入驻</view>\n            <view class=\"success-message-subtitle\">审核通过，您的店铺已正式上线</view>\n        </view>\n      </view>\n      \n        <view class=\"shop-info\">\n          <view class=\"shop-avatar\">\n            <image :src=\"shopInfo.logo || '/static/images/default-shop.png'\" mode=\"aspectFill\"></image>\n          </view>\n          <view class=\"shop-name\">{{shopInfo.name}}</view>\n          <view class=\"shop-verify-tip\" v-if=\"!isVerified\">\n            <image src=\"/static/images/tabbar/认证.png\" class=\"verify-icon\"></image>\n            <text class=\"verify-text\">去认证享更多权益</text>\n            <view class=\"verify-btn\" @click=\"goToVerify\">去认证</view>\n          </view>\n          <view class=\"shop-verified\" v-if=\"isVerified\">\n            <image src=\"/static/images/tabbar/已认证.png\" class=\"verified-icon\"></image>\n            <text class=\"verified-text\">已认证商家</text>\n          </view>\n          <view class=\"test-mark\" v-if=\"isTestMerchant\">测试账号</view>\n        </view>\n        \n        <!-- 入驻版本信息 -->\n        <view class=\"membership-info\" v-if=\"memberType\">\n          <view class=\"membership-badge\" :class=\"memberClass\">{{memberType}}</view>\n          <view class=\"membership-date\">{{expiryDate}} 到期</view>\n        </view>\n      </view>\n      \n      <!-- 商家数据概览 -->\n      <view class=\"data-overview\">\n        <view class=\"data-card\">\n          <view class=\"data-item\">\n            <view class=\"data-num\">120万+</view>\n            <view class=\"data-label\">平台月流量</view>\n          </view>\n          <view class=\"data-divider\"></view>\n          <view class=\"data-item\">\n            <view class=\"data-num\">32%</view>\n            <view class=\"data-label\">商家转化率</view>\n          </view>\n          <view class=\"data-divider\"></view>\n          <view class=\"data-item\">\n            <view class=\"data-num\">16.8万</view>\n            <view class=\"data-label\">注册用户</view>\n          </view>\n        </view>\n      </view>\n      \n      <!-- 认证好处提示（仅未认证商家显示） -->\n      <view class=\"verify-benefits\" v-if=\"!isVerified\">\n        <view class=\"verify-benefits-title\">\n          <image src=\"/static/images/tabbar/认证.png\" class=\"verify-benefit-icon\"></image>\n          <text>商家认证享多重权益</text>\n        </view>\n        <view class=\"verify-benefits-list\">\n          <view class=\"verify-benefit-item\">\n            <view class=\"benefit-dot\"></view>\n            <text class=\"benefit-text\">店铺获得官方认证标识，提升信任度</text>\n          </view>\n          <view class=\"verify-benefit-item\">\n            <view class=\"benefit-dot\"></view>\n            <text class=\"benefit-text\">搜索结果排名靠前，提高曝光量</text>\n          </view>\n          <view class=\"verify-benefit-item\">\n            <view class=\"benefit-dot\"></view>\n            <text class=\"benefit-text\">获取平台流量扶持，拓展客源渠道</text>\n          </view>\n          <view class=\"verify-benefit-item\">\n            <view class=\"benefit-dot\"></view>\n            <text class=\"benefit-text\">专属客服通道，问题快速解决</text>\n          </view>\n        </view>\n        <button class=\"verify-now-btn\" @click=\"goToVerify\">立即认证</button>\n      </view>\n      \n      <!-- 功能入口 -->\n      <view class=\"function-section\">\n        <view class=\"function-title\">商家中心功能</view>\n        <view class=\"function-grid\">\n          <view class=\"function-item\" @click=\"navigateTo('/subPackages/merchant-plugin/pages/goods')\">\n            <view class=\"function-icon-bg bg-blue\">\n              <image src=\"/static/images/tabbar/商品.png\" class=\"function-icon\"></image>\n            </view>\n            <view class=\"function-name\">商品管理</view>\n          </view>\n          <view class=\"function-item\" @click=\"navigateTo('/subPackages/merchant-plugin/pages/orders')\">\n            <view class=\"function-icon-bg bg-orange\">\n              <image src=\"/static/images/tabbar/订单.png\" class=\"function-icon\"></image>\n            </view>\n            <view class=\"function-name\">订单管理</view>\n          </view>\n          <view class=\"function-item\" @click=\"navigateTo('/subPackages/merchant-plugin/pages/marketing')\">\n            <view class=\"function-icon-bg bg-green\">\n              <image src=\"/static/images/tabbar/营销.png\" class=\"function-icon\"></image>\n            </view>\n            <view class=\"function-name\">营销推广</view>\n          </view>\n          <view class=\"function-item\" @click=\"navigateTo('/subPackages/merchant-plugin/pages/analysis')\">\n            <view class=\"function-icon-bg bg-purple\">\n              <image src=\"/static/images/tabbar/数据.png\" class=\"function-icon\"></image>\n            </view>\n            <view class=\"function-name\">数据分析</view>\n          </view>\n        </view>\n      </view>\n      \n      <!-- 操作按钮 -->\n      <view class=\"action-buttons\">\n        <button class=\"primary-btn\" @click=\"goToMerchantCenter\">进入商家后台</button>\n        <button class=\"secondary-btn\" @click=\"goToShopPage\">查看店铺页面</button>\n      </view>\n      \n      <!-- 店铺推广操作 -->\n      <view class=\"promotion-section\">\n        <!-- 整合后的推广卡片 -->\n        <view class=\"promotion-card compact-card\">\n          <view class=\"card-header\">\n            <text class=\"card-title\">店铺推广</text>\n            <text class=\"card-subtitle\">提升店铺曝光度，获取更多客源</text>\n            </view>\n          <view class=\"promotion-tabs\">\n            <view class=\"promotion-tab\" :class=\"{'active': activeTab === 'top'}\" @click=\"activeTab = 'top'\">\n              <text class=\"promotion-tab-text\">置顶店铺</text>\n          </view>\n            <view class=\"promotion-tab\" :class=\"{'active': activeTab === 'refresh'}\" @click=\"activeTab = 'refresh'\">\n              <text class=\"promotion-tab-text\">刷新店铺</text>\n            </view>\n          </view>\n          \n          <view class=\"promotion-content\">\n            <view v-if=\"activeTab === 'top'\" class=\"tab-content-animation\">\n          <ConfigurablePremiumActions\n            showMode=\"direct\"\n            pageType=\"merchant_top\"\n            :itemData=\"topData\"\n            @action-completed=\"handleTopCompleted\"\n            @action-cancelled=\"handleTopCancelled\"\n          />\n        </view>\n            <view v-if=\"activeTab === 'refresh'\" class=\"tab-content-animation\">\n          <ConfigurablePremiumActions\n            showMode=\"direct\"\n            pageType=\"merchant_refresh\"\n            :itemData=\"refreshData\"\n            @action-completed=\"handleRefreshCompleted\"\n            @action-cancelled=\"handleRefreshCancelled\"\n          />\n            </view>\n          </view>\n        </view>\n      </view>\n      \n      <!-- 下一步提示 -->\n      <view class=\"next-steps\">\n        <view class=\"next-title\">推荐您下一步</view>\n        <view class=\"step-item\" @click=\"navigateTo('/subPackages/merchant-plugin/pages/goods')\">\n          <view class=\"step-left\">\n            <view class=\"step-num\">1</view>\n            <view class=\"step-text\">上传店铺商品，丰富店铺内容</view>\n          </view>\n          <view class=\"step-right\">\n            <text class=\"go-text\">去上传</text>\n            <image src=\"/static/images/tabbar/arrow-up.png\" class=\"go-icon\"></image>\n          </view>\n        </view>\n        <view class=\"step-item\" @click=\"navigateTo('/subPackages/merchant-plugin/pages/marketing')\">\n          <view class=\"step-left\">\n            <view class=\"step-num\">2</view>\n            <view class=\"step-text\">开通店铺推广，提升曝光量</view>\n          </view>\n          <view class=\"step-right\">\n            <text class=\"go-text\">去开通</text>\n            <image src=\"/static/images/tabbar/arrow-up.png\" class=\"go-icon\"></image>\n          </view>\n        </view>\n        <view class=\"step-item\" @click=\"goToMerchantCenter\">\n          <view class=\"step-left\">\n            <view class=\"step-num\">3</view>\n            <view class=\"step-text\">完善店铺信息，提高用户信任度</view>\n          </view>\n          <view class=\"step-right\">\n            <text class=\"go-text\">去完善</text>\n            <image src=\"/static/images/tabbar/arrow-up.png\" class=\"go-icon\"></image>\n          </view>\n        </view>\n      </view>\n      \n      <!-- 联系客服 -->\n      <view class=\"contact-section\">\n        <view class=\"contact-text\">遇到问题？联系客服获取帮助</view>\n        <button class=\"contact-btn\" @click=\"contactCustomerService\">联系客服</button>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script setup>\nimport { ref, computed } from 'vue';\nimport { onLoad } from '@dcloudio/uni-app';\nimport ConfigurablePremiumActions from '@/components/premium/ConfigurablePremiumActions.vue';\n\n// 响应式数据\nconst statusBarHeight = ref(20);\nconst shopInfo = ref({\n  logo: '',\n  name: '品牌专卖店',\n  id: 'shop123456'\n});\nconst celebrateIcon = ref('');\nconst memberType = ref('基础版');\nconst expiryDate = ref('2024-12-31');\nconst isTestMerchant = ref(false);\nconst isVerified = ref(false);\nconst refreshCount = ref(0); // 店铺刷新次数\nconst activeTab = ref('top');\n\n// ConfigurablePremiumActions组件需要的数据\nconst topData = ref({\n  id: '',\n  title: '商家置顶',\n  description: '置顶您的店铺，获得更多曝光'\n});\n\nconst refreshData = ref({\n  id: '',\n  title: '商家刷新',\n  description: '刷新您的店铺信息到最新'\n});\n\n// 计算属性\nconst memberClass = computed(() => {\n  const classMap = {\n    '基础版': 'basic-badge',\n    '高级版': 'premium-badge',\n    '尊贵版': 'deluxe-badge',\n    '免费版': 'free-badge',\n    '测试版': 'test-badge'\n  };\n  return classMap[memberType.value] || 'basic-badge';\n});\n\n// 生命周期钩子\nonLoad((options) => {\n  // 获取状态栏高度\n  const sysInfo = uni.getSystemInfoSync();\n  statusBarHeight.value = sysInfo.statusBarHeight;\n\n  // 获取管理后台上传的喝彩图标\n  getCelebrateIcon();\n\n  // 处理测试商家入驻的参数\n  if (options.isTest === 'true') {\n    isTestMerchant.value = true;\n    if (options.shopName) {\n      shopInfo.value.name = decodeURIComponent(options.shopName);\n    }\n    if (options.memberType) {\n      memberType.value = decodeURIComponent(options.memberType);\n    }\n  }\n  // 检查是否是测试入驻的商家\n  else if (options.id) {\n    loadMerchantData(options.id);\n  }\n  // 如果有参数传入则获取\n  else if (options.shopId) {\n    // 实际项目中应该从后端获取店铺信息\n    getShopInfo(options.shopId);\n  }\n\n  // 设置ConfigurablePremiumActions组件需要的数据\n  const shopId = options.shopId || options.id || 'shop_' + Date.now();\n  topData.value.id = shopId;\n  refreshData.value.id = shopId;\n\n  // 更新店铺信息\n  if (shopInfo.value.id !== shopId) {\n    shopInfo.value.id = shopId;\n  }\n\n  // 获取会员类型\n  if (options.memberType && !isTestMerchant.value) {\n    memberType.value = decodeURIComponent(options.memberType);\n\n    // 设置到期时间\n    const now = new Date();\n    if (memberType.value === '免费版') {\n      // 免费版一个月\n      now.setMonth(now.getMonth() + 1);\n    } else {\n      // 付费版一年\n      now.setFullYear(now.getFullYear() + 1);\n    }\n\n    expiryDate.value = now.getFullYear() + '-' +\n      String(now.getMonth() + 1).padStart(2, '0') + '-' +\n      String(now.getDate()).padStart(2, '0');\n  }\n  \n  // 加载刷新次数\n  try {\n    const userRefreshData = uni.getStorageSync('userRefreshData') || {};\n    const shopId = options.id || options.shopId || '';\n    if (shopId && userRefreshData[shopId]) {\n      refreshCount.value = userRefreshData[shopId].count || 0;\n    }\n  } catch (e) {\n    console.error('加载刷新次数失败:', e);\n    refreshCount.value = 0;\n  }\n});\n\n// 方法\nconst goBack = () => {\n  uni.switchTab({\n    url: '/pages/index/index'\n  });\n};\n\nconst goToVerify = () => {\n  uni.navigateTo({\n    url: '/pages/business/verify?id=' + shopInfo.value.id\n  });\n};\n\nconst navigateTo = (url) => {\n  uni.navigateTo({\n    url: url\n  });\n};\n\nconst goToMerchantCenter = () => {\n  // 假设商家后台是tabbar页面\n  uni.switchTab({\n    url: '/pages/my/my' \n  });\n};\n\nconst goToShopPage = () => {\n  uni.navigateTo({\n    url: '/pages/business/shop-detail?id=' + shopInfo.value.id\n  });\n};\n\nconst contactCustomerService = () => {\n  uni.makePhoneCall({\n    phoneNumber: '************' \n  });\n};\n\n// 处理置顶操作完成\nconst handleTopCompleted = (result) => {\n  console.log('置顶操作完成:', result);\n\n  if (result.type === 'ad') {\n    uni.showToast({\n      title: '广告观看完成，店铺已置顶',\n      icon: 'success'\n    });\n  } else if (result.type === 'payment') {\n    uni.showToast({\n      title: `付费成功，店铺已置顶`,\n      icon: 'success'\n    });\n  }\n};\n\n// 处理置顶操作取消\nconst handleTopCancelled = (result) => {\n  console.log('置顶操作取消:', result);\n\n  if (result.type === 'ad') {\n    uni.showToast({\n      title: '已取消观看广告',\n      icon: 'none'\n    });\n  } else if (result.type === 'payment') {\n    uni.showToast({\n      title: '已取消支付',\n      icon: 'none'\n    });\n  }\n};\n\n// 处理刷新操作完成\nconst handleRefreshCompleted = (result) => {\n  console.log('刷新操作完成:', result);\n\n  if (result.type === 'ad') {\n    uni.showToast({\n      title: '广告观看完成，店铺已刷新',\n      icon: 'success'\n    });\n  } else if (result.type === 'payment') {\n    uni.showToast({\n      title: `付费成功，店铺已刷新`,\n      icon: 'success'\n    });\n  }\n\n  // 更新刷新次数\n  if (result.data && result.data.remainingCount !== undefined) {\n    refreshCount.value = result.data.remainingCount;\n  }\n};\n\n// 处理刷新操作取消\nconst handleRefreshCancelled = (result) => {\n  console.log('刷新操作取消:', result);\n\n  if (result.type === 'ad') {\n    uni.showToast({\n      title: '已取消观看广告',\n      icon: 'none'\n    });\n  } else if (result.type === 'payment') {\n    uni.showToast({\n      title: '已取消支付',\n      icon: 'none'\n    });\n  }\n};\n\nconst getShopInfo = (shopId) => {\n  // 模拟API请求\n  setTimeout(() => {\n    shopInfo.value = {\n      logo: '/static/images/default-shop.png',\n      name: '示例店铺 ' + shopId,\n      id: shopId\n    };\n  }, 500);\n};\n\nconst loadMerchantData = (id) => {\n  console.log(\"加载商家数据:\", id);\n  \n  // 尝试从localStorage获取测试商家数据\n  try {\n    const merchantTestData = uni.getStorageSync('merchantTestData') || [];\n    const merchant = merchantTestData.find(item => item.id === id);\n    \n    if (merchant) {\n      isTestMerchant.value = true;\n      shopInfo.value = {\n        logo: merchant.logo || '/static/images/default-shop.png',\n        name: merchant.shopName || \"测试商家\",\n        id: merchant.id\n      };\n      memberType.value = \"测试版\";\n      return;\n    }\n  } catch (e) {\n    console.error(\"获取测试商家数据失败:\", e);\n  }\n  \n  // 如果没有找到测试商家数据，使用默认测试数据\n  if (id === 'test') {\n    isTestMerchant.value = true;\n    shopInfo.value.name = \"内部测试商家\";\n    memberType.value = \"测试版\";\n  }\n};\n\nconst getCelebrateIcon = () => {\n  // 模拟从远程获取图标\n  setTimeout(() => {\n    // celebrateIcon.value = 'https://example.com/path/to/celebrate-icon.png';\n  }, 1000);\n};\n</script>\n\n<style lang=\"scss\">\n.success-container {\n  display: flex;\n  flex-direction: column;\n  min-height: 100vh;\n  background-color: #f8f9fc;\n}\n\n/* 顶部渐变背景 */\n.top-gradient {\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  height: 30vh;\n  background: linear-gradient(135deg, #0046B3, #1677FF);\n  border-bottom-left-radius: 30px;\n  border-bottom-right-radius: 30px;\n  z-index: 0;\n}\n\n/* 导航栏 */\n.navbar {\n  display: flex;\n  align-items: center;\n  padding: 10px 20px;\n  position: relative;\n  z-index: 1;\n}\n\n.navbar-left {\n  width: 44px;\n  height: 44px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.back-icon {\n  width: 24px;\n  height: 24px;\n}\n\n.navbar-title {\n  flex: 1;\n  text-align: center;\n  font-size: 18px;\n  font-weight: bold;\n  color: #fff;\n}\n\n.navbar-right {\n  width: 44px;\n}\n\n/* 内容区域 */\n.content {\n  flex: 1;\n  position: relative;\n  z-index: 1;\n  padding: 0 20px 30px;\n}\n\n/* 成功信息 */\n.success-info {\n  margin-top: 20px;\n  text-align: center;\n  position: relative;\n  z-index: 5;\n}\n\n.success-message-card {\n  display: flex;\n  align-items: center;\n  background-color: #fff;\n  border-radius: 24px;\n  padding: 16px 20px;\n  margin-bottom: 20px;\n  box-shadow: \n    0 3px 6px rgba(0, 0, 0, 0.05),\n    0 8px 16px rgba(0, 0, 0, 0.1),\n    0 12px 24px -10px rgba(0, 82, 204, 0.15);\n  transform: perspective(1000px) translateZ(0) rotateX(0.5deg);\n  transform-style: preserve-3d;\n  border: 1px solid rgba(255, 255, 255, 0.7);\n  transition: transform 0.3s ease;\n  position: relative;\n}\n\n.success-message-card::before {\n  content: \"\";\n  position: absolute;\n  left: 0;\n  top: 0;\n  width: 100%;\n  height: 100%;\n  background: linear-gradient(135deg, \n    rgba(255, 255, 255, 0.6) 0%, \n    rgba(255, 255, 255, 0.2) 50%, \n    rgba(255, 255, 255, 0) 100%);\n  border-radius: 16px;\n  pointer-events: none;\n}\n\n.success-message-icon {\n  width: 48px;\n  height: 48px;\n  margin-right: 16px;\n  flex-shrink: 0;\n}\n\n.success-message-content {\n  flex: 1;\n  text-align: left;\n}\n\n.success-message-title {\n  font-size: 18px;\n  font-weight: bold;\n  color: #0052CC;\n  margin-bottom: 4px;\n}\n\n.success-message-subtitle {\n  font-size: 14px;\n  color: #666;\n  line-height: 1.4;\n}\n\n.shop-info {\n  background-color: #fff;\n  border-radius: 24px;\n  padding: 20px;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  position: relative;\n  box-shadow: \n    0 3px 6px rgba(0, 0, 0, 0.05),\n    0 8px 20px rgba(0, 0, 0, 0.1), \n    0 15px 30px -12px rgba(0, 82, 204, 0.2);\n  transform: perspective(1000px) translateZ(0) rotateX(1deg);\n  transform-style: preserve-3d;\n  border: 1px solid rgba(255, 255, 255, 0.7);\n  transition: transform 0.3s ease;\n}\n\n.shop-info::before {\n  content: \"\";\n  position: absolute;\n  left: 0;\n  top: 0;\n  width: 100%;\n  height: 100%;\n  background: linear-gradient(135deg, \n    rgba(255, 255, 255, 0.6) 0%, \n    rgba(255, 255, 255, 0.2) 50%, \n    rgba(255, 255, 255, 0) 100%);\n  border-radius: 16px;\n  pointer-events: none;\n}\n\n.shop-info::after {\n  content: \"\";\n  position: absolute;\n  left: 0;\n  top: 0;\n  width: 100%;\n  height: 100%;\n  box-shadow: inset 0 1px 1px rgba(255, 255, 255, 0.5);\n  border-radius: 16px;\n  pointer-events: none;\n}\n\n.shop-avatar {\n  width: 80px;\n  height: 80px;\n  border-radius: 50%;\n  overflow: hidden;\n  border: 3px solid #f0f4ff;\n  margin-bottom: 12px;\n  background-color: #f8f8f8;\n  box-shadow: 0 4px 10px rgba(0, 82, 204, 0.15);\n}\n\n.shop-avatar image {\n  width: 100%;\n  height: 100%;\n  object-fit: cover;\n}\n\n.shop-name {\n  font-size: 18px;\n  font-weight: bold;\n  color: #333;\n  margin-bottom: 8px;\n}\n\n.shop-verify-tip {\n  display: flex;\n  align-items: center;\n  background-color: #f0f8ff;\n  padding: 4px 12px;\n  border-radius: 20px;\n}\n\n.verify-icon {\n  width: 16px;\n  height: 16px;\n  margin-right: 4px;\n}\n\n.verify-text {\n  font-size: 14px;\n  color: #0052CC;\n}\n\n.verify-btn {\n  font-size: 14px;\n  color: #0052CC;\n  margin-left: 4px;\n  cursor: pointer;\n}\n\n.shop-verified {\n  display: flex;\n  align-items: center;\n  background-color: #f0f8ff;\n  padding: 4px 12px;\n  border-radius: 20px;\n}\n\n.verified-icon {\n  width: 16px;\n  height: 16px;\n  margin-right: 4px;\n}\n\n.verified-text {\n  font-size: 14px;\n  color: #0052CC;\n}\n\n/* 数据概览 */\n.data-overview {\n  margin-top: 20px;\n}\n\n.data-card {\n  background-color: #fff;\n  border-radius: 24px;\n  padding: 16px;\n  display: flex;\n  justify-content: space-between;\n  position: relative;\n  box-shadow: \n    0 3px 6px rgba(0, 0, 0, 0.05),\n    0 8px 16px rgba(0, 0, 0, 0.1),\n    0 12px 24px -10px rgba(0, 82, 204, 0.15);\n  transform: perspective(1000px) translateZ(0) rotateX(0.5deg);\n  transform-style: preserve-3d;\n  border: 1px solid rgba(255, 255, 255, 0.7);\n  transition: transform 0.3s ease;\n}\n\n.data-card::before {\n  content: \"\";\n  position: absolute;\n  left: 0;\n  top: 0;\n  width: 100%;\n  height: 100%;\n  background: linear-gradient(135deg, \n    rgba(255, 255, 255, 0.6) 0%, \n    rgba(255, 255, 255, 0.2) 50%, \n    rgba(255, 255, 255, 0) 100%);\n  border-radius: 16px;\n  pointer-events: none;\n}\n\n.data-item {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n}\n\n.data-num {\n  font-size: 18px;\n  font-weight: bold;\n  color: #0052CC;\n  margin-bottom: 4px;\n}\n\n.data-label {\n  font-size: 12px;\n  color: #666;\n}\n\n.data-divider {\n  width: 1px;\n  background-color: #eee;\n  margin: 0 10px;\n}\n\n/* 功能入口 */\n.function-section {\n  margin-top: 24px;\n}\n\n.function-title {\n  font-size: 18px;\n  font-weight: bold;\n  color: #333;\n  margin-bottom: 16px;\n}\n\n.function-grid {\n  display: flex;\n  flex-wrap: wrap;\n  margin: 0 -8px;\n}\n\n.function-item {\n  width: 25%;\n  padding: 8px;\n  box-sizing: border-box;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n}\n\n.function-icon-bg {\n  width: 50px;\n  height: 50px;\n  border-radius: 12px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-bottom: 8px;\n}\n\n.bg-blue {\n  background-color: #e6f0ff;\n}\n\n.bg-orange {\n  background-color: #fff2e6;\n}\n\n.bg-green {\n  background-color: #e6fff0;\n}\n\n.bg-purple {\n  background-color: #f0e6ff;\n}\n\n.function-icon {\n  width: 28px;\n  height: 28px;\n}\n\n.function-name {\n  font-size: 14px;\n  color: #333;\n  text-align: center;\n}\n\n/* 操作按钮 */\n.action-buttons {\n  margin-top: 30px;\n  display: flex;\n  flex-direction: column;\n  padding: 0 30rpx;\n  margin-bottom: 40rpx;\n}\n\n.primary-btn {\n  background: linear-gradient(to right, #007AFF, #5AC8FA);\n  color: #FFFFFF;\n  font-size: 32rpx;\n  height: 88rpx;\n  line-height: 88rpx;\n  border-radius: 44rpx;\n  margin-bottom: 30rpx;\n  box-shadow: 0 4rpx 12rpx rgba(0, 122, 255, 0.2);\n}\n\n.secondary-btn {\n  background: rgba(0, 122, 255, 0.1);\n  color: #007AFF;\n  font-size: 32rpx;\n  height: 88rpx;\n  line-height: 88rpx;\n  border-radius: 44rpx;\n  border: 1px solid rgba(0, 122, 255, 0.2);\n}\n\n/* 下一步提示 */\n.next-steps {\n  margin-top: 30px;\n  background-color: #fff;\n  border-radius: 24px;\n  padding: 20px;\n  position: relative;\n  box-shadow: \n    0 3px 6px rgba(0, 0, 0, 0.05),\n    0 8px 16px rgba(0, 0, 0, 0.08),\n    0 12px 20px -8px rgba(0, 82, 204, 0.15);\n  transform: perspective(1000px) translateZ(0) rotateX(0.8deg);\n  transform-style: preserve-3d;\n  border: 1px solid rgba(255, 255, 255, 0.7);\n  transition: transform 0.3s ease;\n}\n\n.next-steps::before {\n  content: \"\";\n  position: absolute;\n  left: 0;\n  top: 0;\n  width: 100%;\n  height: 100%;\n  background: linear-gradient(135deg, \n    rgba(255, 255, 255, 0.5) 0%, \n    rgba(255, 255, 255, 0.2) 50%, \n    rgba(255, 255, 255, 0) 100%);\n  border-radius: 16px;\n  pointer-events: none;\n}\n\n.next-title {\n  font-size: 16px;\n  font-weight: bold;\n  color: #333;\n  margin-bottom: 16px;\n}\n\n.step-item {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 12px 0;\n  border-bottom: 1px solid #f5f5f5;\n}\n\n.step-item:last-child {\n  border-bottom: none;\n}\n\n.step-left {\n  display: flex;\n  align-items: center;\n  flex: 1;\n}\n\n.step-num {\n  width: 24px;\n  height: 24px;\n  border-radius: 12px;\n  background-color: #0052CC;\n  color: #fff;\n  font-size: 14px;\n  font-weight: bold;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-right: 12px;\n}\n\n.step-text {\n  font-size: 14px;\n  color: #333;\n}\n\n.step-right {\n  display: flex;\n  align-items: center;\n}\n\n.go-text {\n  font-size: 14px;\n  color: #0052CC;\n  margin-right: 4px;\n}\n\n.go-icon {\n  width: 16px;\n  height: 16px;\n  transform: rotate(90deg);\n}\n\n/* 联系客服 */\n.contact-section {\n  margin-top: 30px;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n}\n\n.contact-text {\n  font-size: 14px;\n  color: #666;\n  margin-bottom: 12px;\n}\n\n.contact-btn {\n  width: 140px;\n  height: 36px;\n  line-height: 36px;\n  background: linear-gradient(135deg, #2FB8FF, #0076FF);\n  color: #fff;\n  font-size: 14px;\n  border-radius: 18px;\n  text-align: center;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  box-shadow: 0 4px 8px rgba(0, 118, 255, 0.25);\n  position: relative;\n  overflow: hidden;\n}\n\n.contact-btn::before {\n  content: \"\";\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  background: linear-gradient(45deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0));\n  border-radius: 18px;\n}\n\n/* 会员信息 */\n.membership-info {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  margin-top: 20rpx;\n}\n\n.membership-badge {\n  padding: 6rpx 24rpx;\n  border-radius: 20rpx;\n  font-size: 24rpx;\n  font-weight: 600;\n  color: #fff;\n  background: linear-gradient(135deg, #1677ff, #0052cc);\n  box-shadow: 0 4rpx 8rpx rgba(22, 119, 255, 0.2);\n}\n\n.free-badge {\n  background: linear-gradient(135deg, #52c41a, #389e0d);\n}\n\n.basic-badge {\n  background: linear-gradient(135deg, #1677ff, #0052cc);\n}\n\n.premium-badge {\n  background: linear-gradient(135deg, #fa8c16, #d46b08);\n}\n\n.deluxe-badge {\n  background: linear-gradient(135deg, #722ed1, #531dab);\n}\n\n.test-badge {\n  background: linear-gradient(135deg, #ffa500, #ff8c00);\n}\n\n.test-mark {\n  position: absolute;\n  top: -6px;\n  right: -10px;\n  background-color: #ff9800;\n  color: #fff;\n  font-size: 10px;\n  padding: 2px 6px;\n  border-radius: 10px;\n  transform: scale(0.85);\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);\n  z-index: 2;\n}\n\n.membership-date {\n  margin-top: 8rpx;\n  font-size: 22rpx;\n  color: #666;\n}\n\n/* 认证好处卡片 */\n.verify-benefits {\n  margin-top: 20px;\n  background-color: #fff;\n  border-radius: 24px;\n  padding: 20px;\n  position: relative;\n  box-shadow: \n    0 3px 6px rgba(0, 0, 0, 0.05),\n    0 8px 16px rgba(0, 0, 0, 0.08),\n    0 12px 20px -8px rgba(0, 82, 204, 0.15);\n  transform: perspective(1000px) translateZ(0) rotateX(0.7deg);\n  transform-style: preserve-3d;\n  border: 1px solid rgba(255, 255, 255, 0.7);\n  transition: transform 0.3s ease;\n}\n\n.verify-benefits::before {\n  content: \"\";\n  position: absolute;\n  left: 0;\n  top: 0;\n  width: 100%;\n  height: 100%;\n  background: linear-gradient(135deg, \n    rgba(255, 255, 255, 0.5) 0%, \n    rgba(255, 255, 255, 0.2) 50%, \n    rgba(255, 255, 255, 0) 100%);\n  border-radius: 16px;\n  pointer-events: none;\n}\n\n.verify-benefits-title {\n  display: flex;\n  align-items: center;\n  font-size: 16px;\n  font-weight: bold;\n  color: #333;\n  margin-bottom: 16px;\n}\n\n.verify-benefit-icon {\n  width: 20px;\n  height: 20px;\n  margin-right: 8px;\n}\n\n.verify-benefits-list {\n  margin-bottom: 16px;\n}\n\n.verify-benefit-item {\n  display: flex;\n  align-items: center;\n  margin-bottom: 10px;\n}\n\n.benefit-dot {\n  width: 8px;\n  height: 8px;\n  border-radius: 4px;\n  background-color: #1677FF;\n  margin-right: 8px;\n}\n\n.benefit-text {\n  font-size: 14px;\n  color: #333;\n  line-height: 1.5;\n}\n\n.verify-now-btn {\n  background: linear-gradient(135deg, #1677FF, #0052CC);\n  color: #fff;\n  font-size: 16px;\n  height: 40px;\n  border-radius: 20px;\n  margin-top: 10px;\n  box-shadow: 0 4px 8px rgba(0, 82, 204, 0.2);\n}\n\n/* 重新设计店铺推广样式 */\n.promotion-section {\n  padding: 0;\n  margin: 30rpx 30rpx 40rpx;\n}\n\n.promotion-card {\n  background-color: #fff;\n  border-radius: 24px;\n  overflow: hidden;\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);\n}\n\n.card-header {\n  padding: 16px 16px 12px;\n  background-color: #FFFFFF;\n}\n\n.card-title {\n  font-size: 16px;\n  font-weight: 600;\n  color: #333;\n  display: block;\n}\n\n.card-subtitle {\n  font-size: 12px;\n  color: #666;\n  display: block;\n  margin-top: 4px;\n}\n\n.promotion-tabs {\n  display: flex;\n  justify-content: space-between;\n  border-bottom: 1px solid rgba(0, 0, 0, 0.05);\n  background-color: #FFFFFF;\n  padding: 0 30px;\n}\n\n/* 紧凑卡片样式 */\n.compact-card {\n  border-radius: 24px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);\n}\n\n.compact-card .card-header {\n  padding: 12px 16px 8px;\n}\n\n.compact-card .card-title {\n  font-size: 15px;\n}\n\n.compact-card .card-subtitle {\n  font-size: 11px;\n  margin-top: 2px;\n}\n\n.compact-card .promotion-tabs {\n  padding: 0 20px;\n}\n\n.compact-card .promotion-tab {\n  padding: 8px 0;\n}\n\n.compact-card .promotion-content {\n  padding: 12px;\n}\n\n.promotion-tab {\n  flex: 1;\n  text-align: center;\n  padding: 12px 0;\n  position: relative;\n  transition: all 0.3s ease;\n  max-width: 120px;\n  margin: 0 auto;\n}\n\n.promotion-tab.active {\n  position: relative;\n  border-bottom: none;\n}\n\n.promotion-tab.active::after {\n  content: '';\n  position: absolute;\n  bottom: 0;\n  left: 50%;\n  transform: translateX(-50%);\n  width: 20px;\n  height: 3px;\n  background: linear-gradient(90deg, rgba(22, 119, 255, 0.7), #1677FF, rgba(22, 119, 255, 0.7));\n  border-radius: 3px 3px 0 0;\n  box-shadow: 0 1px 3px rgba(22, 119, 255, 0.2);\n}\n\n.promotion-tab-text {\n  font-size: 14px;\n  font-weight: 500;\n  color: #666;\n  transition: all 0.3s;\n  position: relative;\n  display: inline-block;\n}\n\n.promotion-tab.active .promotion-tab-text {\n  color: #1677FF;\n  font-weight: 600;\n}\n\n/* 紧凑选项卡样式 */\n.compact-card .promotion-tab {\n  padding: 8px 0;\n}\n\n.compact-card .promotion-tab-text {\n  font-size: 13px;\n}\n\n.compact-card .promotion-tab.active::after {\n  width: 16px;\n  height: 2px;\n}\n\n.promotion-content {\n  padding: 16px;\n  background-color: #FFFFFF;\n}\n\n.tab-content-animation {\n  animation: fadeIn 0.3s ease;\n}\n\n@keyframes fadeIn {\n  from {\n    opacity: 0;\n  }\n  to {\n    opacity: 1;\n  }\n}\n</style>", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/pages/business/success.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "computed", "onLoad", "uni", "shopId", "MiniProgramPage"], "mappings": ";;;;;;AAgOA,MAAM,6BAA6B,MAAW;;;;AAG9C,UAAM,kBAAkBA,cAAAA,IAAI,EAAE;AAC9B,UAAM,WAAWA,cAAAA,IAAI;AAAA,MACnB,MAAM;AAAA,MACN,MAAM;AAAA,MACN,IAAI;AAAA,IACN,CAAC;AACD,UAAM,gBAAgBA,cAAAA,IAAI,EAAE;AAC5B,UAAM,aAAaA,cAAAA,IAAI,KAAK;AAC5B,UAAM,aAAaA,cAAAA,IAAI,YAAY;AACnC,UAAM,iBAAiBA,cAAAA,IAAI,KAAK;AAChC,UAAM,aAAaA,cAAAA,IAAI,KAAK;AAC5B,UAAM,eAAeA,cAAAA,IAAI,CAAC;AAC1B,UAAM,YAAYA,cAAAA,IAAI,KAAK;AAG3B,UAAM,UAAUA,cAAAA,IAAI;AAAA,MAClB,IAAI;AAAA,MACJ,OAAO;AAAA,MACP,aAAa;AAAA,IACf,CAAC;AAED,UAAM,cAAcA,cAAAA,IAAI;AAAA,MACtB,IAAI;AAAA,MACJ,OAAO;AAAA,MACP,aAAa;AAAA,IACf,CAAC;AAGD,UAAM,cAAcC,cAAQ,SAAC,MAAM;AACjC,YAAM,WAAW;AAAA,QACf,OAAO;AAAA,QACP,OAAO;AAAA,QACP,OAAO;AAAA,QACP,OAAO;AAAA,QACP,OAAO;AAAA,MACX;AACE,aAAO,SAAS,WAAW,KAAK,KAAK;AAAA,IACvC,CAAC;AAGDC,kBAAM,OAAC,CAAC,YAAY;AAElB,YAAM,UAAUC,oBAAI;AACpB,sBAAgB,QAAQ,QAAQ;AAGhC;AAGA,UAAI,QAAQ,WAAW,QAAQ;AAC7B,uBAAe,QAAQ;AACvB,YAAI,QAAQ,UAAU;AACpB,mBAAS,MAAM,OAAO,mBAAmB,QAAQ,QAAQ;AAAA,QAC1D;AACD,YAAI,QAAQ,YAAY;AACtB,qBAAW,QAAQ,mBAAmB,QAAQ,UAAU;AAAA,QACzD;AAAA,MACF,WAEQ,QAAQ,IAAI;AACnB,yBAAiB,QAAQ,EAAE;AAAA,MAC5B,WAEQ,QAAQ,QAAQ;AAEvB,oBAAY,QAAQ,MAAM;AAAA,MAC3B;AAGD,YAAM,SAAS,QAAQ,UAAU,QAAQ,MAAM,UAAU,KAAK;AAC9D,cAAQ,MAAM,KAAK;AACnB,kBAAY,MAAM,KAAK;AAGvB,UAAI,SAAS,MAAM,OAAO,QAAQ;AAChC,iBAAS,MAAM,KAAK;AAAA,MACrB;AAGD,UAAI,QAAQ,cAAc,CAAC,eAAe,OAAO;AAC/C,mBAAW,QAAQ,mBAAmB,QAAQ,UAAU;AAGxD,cAAM,MAAM,oBAAI;AAChB,YAAI,WAAW,UAAU,OAAO;AAE9B,cAAI,SAAS,IAAI,SAAU,IAAG,CAAC;AAAA,QACrC,OAAW;AAEL,cAAI,YAAY,IAAI,YAAa,IAAG,CAAC;AAAA,QACtC;AAED,mBAAW,QAAQ,IAAI,YAAa,IAAG,MACrC,OAAO,IAAI,aAAa,CAAC,EAAE,SAAS,GAAG,GAAG,IAAI,MAC9C,OAAO,IAAI,QAAS,CAAA,EAAE,SAAS,GAAG,GAAG;AAAA,MACxC;AAGD,UAAI;AACF,cAAM,kBAAkBA,cAAG,MAAC,eAAe,iBAAiB,KAAK,CAAA;AACjE,cAAMC,UAAS,QAAQ,MAAM,QAAQ,UAAU;AAC/C,YAAIA,WAAU,gBAAgBA,OAAM,GAAG;AACrC,uBAAa,QAAQ,gBAAgBA,OAAM,EAAE,SAAS;AAAA,QACvD;AAAA,MACF,SAAQ,GAAG;AACVD,gFAAc,aAAa,CAAC;AAC5B,qBAAa,QAAQ;AAAA,MACtB;AAAA,IACH,CAAC;AAGD,UAAM,SAAS,MAAM;AACnBA,oBAAAA,MAAI,UAAU;AAAA,QACZ,KAAK;AAAA,MACT,CAAG;AAAA,IACH;AAEA,UAAM,aAAa,MAAM;AACvBA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,+BAA+B,SAAS,MAAM;AAAA,MACvD,CAAG;AAAA,IACH;AAEA,UAAM,aAAa,CAAC,QAAQ;AAC1BA,oBAAAA,MAAI,WAAW;AAAA,QACb;AAAA,MACJ,CAAG;AAAA,IACH;AAEA,UAAM,qBAAqB,MAAM;AAE/BA,oBAAAA,MAAI,UAAU;AAAA,QACZ,KAAK;AAAA,MACT,CAAG;AAAA,IACH;AAEA,UAAM,eAAe,MAAM;AACzBA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,oCAAoC,SAAS,MAAM;AAAA,MAC5D,CAAG;AAAA,IACH;AAEA,UAAM,yBAAyB,MAAM;AACnCA,oBAAAA,MAAI,cAAc;AAAA,QAChB,aAAa;AAAA,MACjB,CAAG;AAAA,IACH;AAGA,UAAM,qBAAqB,CAAC,WAAW;AACrCA,4EAAY,WAAW,MAAM;AAE7B,UAAI,OAAO,SAAS,MAAM;AACxBA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AAAA,MACL,WAAa,OAAO,SAAS,WAAW;AACpCA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AAAA,MACF;AAAA,IACH;AAGA,UAAM,qBAAqB,CAAC,WAAW;AACrCA,4EAAY,WAAW,MAAM;AAE7B,UAAI,OAAO,SAAS,MAAM;AACxBA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AAAA,MACL,WAAa,OAAO,SAAS,WAAW;AACpCA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AAAA,MACF;AAAA,IACH;AAGA,UAAM,yBAAyB,CAAC,WAAW;AACzCA,4EAAY,WAAW,MAAM;AAE7B,UAAI,OAAO,SAAS,MAAM;AACxBA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AAAA,MACL,WAAa,OAAO,SAAS,WAAW;AACpCA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AAAA,MACF;AAGD,UAAI,OAAO,QAAQ,OAAO,KAAK,mBAAmB,QAAW;AAC3D,qBAAa,QAAQ,OAAO,KAAK;AAAA,MAClC;AAAA,IACH;AAGA,UAAM,yBAAyB,CAAC,WAAW;AACzCA,4EAAY,WAAW,MAAM;AAE7B,UAAI,OAAO,SAAS,MAAM;AACxBA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AAAA,MACL,WAAa,OAAO,SAAS,WAAW;AACpCA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AAAA,MACF;AAAA,IACH;AAEA,UAAM,cAAc,CAAC,WAAW;AAE9B,iBAAW,MAAM;AACf,iBAAS,QAAQ;AAAA,UACf,MAAM;AAAA,UACN,MAAM,UAAU;AAAA,UAChB,IAAI;AAAA,QACV;AAAA,MACG,GAAE,GAAG;AAAA,IACR;AAEA,UAAM,mBAAmB,CAAC,OAAO;AAC/BA,oBAAY,MAAA,MAAA,OAAA,qCAAA,WAAW,EAAE;AAGzB,UAAI;AACF,cAAM,mBAAmBA,cAAG,MAAC,eAAe,kBAAkB,KAAK,CAAA;AACnE,cAAM,WAAW,iBAAiB,KAAK,UAAQ,KAAK,OAAO,EAAE;AAE7D,YAAI,UAAU;AACZ,yBAAe,QAAQ;AACvB,mBAAS,QAAQ;AAAA,YACf,MAAM,SAAS,QAAQ;AAAA,YACvB,MAAM,SAAS,YAAY;AAAA,YAC3B,IAAI,SAAS;AAAA,UACrB;AACM,qBAAW,QAAQ;AACnB;AAAA,QACD;AAAA,MACF,SAAQ,GAAG;AACVA,sBAAA,MAAA,MAAA,SAAA,qCAAc,eAAe,CAAC;AAAA,MAC/B;AAGD,UAAI,OAAO,QAAQ;AACjB,uBAAe,QAAQ;AACvB,iBAAS,MAAM,OAAO;AACtB,mBAAW,QAAQ;AAAA,MACpB;AAAA,IACH;AAEA,UAAM,mBAAmB,MAAM;AAE7B,iBAAW,MAAM;AAAA,MAEhB,GAAE,GAAI;AAAA,IACT;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC7eA,GAAG,WAAWE,SAAe;"}