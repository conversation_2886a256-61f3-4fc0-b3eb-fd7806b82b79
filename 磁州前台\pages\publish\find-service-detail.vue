<template>
  <view class="find-service-container">
    <!-- 隐藏的分享按钮，用于自动触发 -->
    <button id="shareButton" class="hidden-share-btn" open-type="share"></button>
    
    <!-- 隐藏的Canvas用于绘制海报 -->
    <canvas canvas-id="posterCanvas" class="poster-canvas" style="width: 600px; height: 900px; position: fixed; top: -9999px; left: -9999px;"></canvas>
    
    <!-- 悬浮海报按钮 -->
    <view class="float-poster-btn" @click="generateShareImage">
      <image src="/static/images/tabbar/海报.png" class="poster-icon"></image>
      <text class="poster-text">海报</text>
    </view>
    
    <view class="find-service-wrapper">
      <!-- 需求基本信息卡片 -->
      <view class="content-card service-info-card">
        <view class="service-header">
          <view class="service-title-row">
            <text class="service-title">{{serviceData.title}}</text>
            <text class="service-budget">{{serviceData.budget}}</text>
          </view>
          <view class="service-meta">
            <view class="service-tag-group">
              <view class="service-tag" v-for="(tag, index) in serviceData.tags" :key="index">{{tag}}</view>
            </view>
            <text class="service-publish-time">发布于 {{formatTime(serviceData.publishTime)}}</text>
          </view>
        </view>
        
        <!-- 需求描述 -->
        <view class="service-description">
          <text class="desc-text">{{serviceData.description}}</text>
        </view>
        
        <!-- 基本信息 -->
        <view class="service-basic-info">
          <view class="info-item">
            <text class="info-label">服务类型</text>
            <text class="info-value">{{serviceData.type}}</text>
          </view>
          <view class="info-item">
            <text class="info-label">服务区域</text>
            <text class="info-value">{{serviceData.area}}</text>
          </view>
          <view class="info-item">
            <text class="info-label">服务时间</text>
            <text class="info-value">{{serviceData.time}}</text>
          </view>
          <view class="info-item">
            <text class="info-label">服务方式</text>
            <text class="info-value">{{serviceData.method}}</text>
          </view>
        </view>
      </view>
      
      <!-- 需求详情 -->
      <view class="content-card service-detail-card">
        <view class="section-title">需求详情</view>
        <view class="detail-list">
          <view class="detail-item" v-for="(item, index) in serviceData.details" :key="index">
            <text class="detail-label">{{item.label}}</text>
            <text class="detail-value">{{item.value}}</text>
          </view>
        </view>
      </view>
      
      <!-- 补充说明 -->
      <view class="content-card service-notes-card">
        <view class="section-title">补充说明</view>
        <view class="notes-content">
          <rich-text :nodes="serviceData.notes"></rich-text>
        </view>
      </view>
      
      <!-- 发布者信息 -->
      <view class="content-card publisher-card">
        <view class="publisher-header">
          <view class="publisher-avatar">
            <image :src="serviceData.publisher.avatar" mode="aspectFill"></image>
          </view>
          <view class="publisher-info">
            <text class="publisher-name">{{serviceData.publisher.name}}</text>
            <view class="publisher-meta">
              <text class="publisher-type">{{serviceData.publisher.type}}</text>
              <text class="publisher-rating">信用等级 {{serviceData.publisher.rating}}</text>
            </view>
          </view>
          <view class="publisher-auth" v-if="serviceData.publisher.isVerified">
            <text class="iconfont icon-verified"></text>
            <text class="auth-text">已认证</text>
          </view>
        </view>
      </view>
      
      <!-- 联系方式 -->
      <view class="content-card contact-card">
        <view class="contact-header">
          <text class="card-title">联系方式</text>
        </view>
        <view class="contact-content">
          <view class="contact-item">
            <text class="contact-label">联系人</text>
            <text class="contact-value">{{serviceData.contact.name}}</text>
          </view>
          <view class="contact-item">
            <text class="contact-label">电话</text>
            <text class="contact-value contact-phone" @click="callPhone">{{serviceData.contact.phone}}</text>
          </view>
          <view class="contact-tips">
            <text class="tips-icon iconfont icon-info"></text>
            <text class="tips-text">请说明在"磁州生活网"看到的信息</text>
          </view>
        </view>
      </view>
      
      <!-- 相似需求推荐 -->
      <view class="content-card similar-needs-card">
        <view class="similar-header">
          <text class="card-title">相似需求</text>
        </view>
        <view class="similar-list">
          <view class="similar-item" v-for="(item, index) in similarServices" :key="index" @click="navigateToService(item.id)">
            <view class="similar-service-info">
              <text class="similar-service-title">{{item.title}}</text>
              <text class="similar-service-budget">{{item.budget}}</text>
              <text class="similar-service-meta">{{item.type}} | {{item.area}}</text>
            </view>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 底部操作栏 -->
    <view class="interaction-toolbar">
      <view class="toolbar-item" @click="goToHome">
        <image src="/static/images/tabbar/a首页.png" class="toolbar-icon"></image>
        <text class="toolbar-text">首页</text>
      </view>
      <view class="toolbar-item" @click="toggleCollect">
        <image src="/static/images/tabbar/a收藏.png" class="toolbar-icon"></image>
        <text class="toolbar-text">收藏</text>
      </view>
      <button class="share-button toolbar-item" open-type="share">
        <image src="/static/images/tabbar/a分享.png" class="toolbar-icon"></image>
        <text class="toolbar-text">分享</text>
      </button>
      <view class="toolbar-item" @click="openChat">
        <image src="/static/images/tabbar/a消息.png" class="toolbar-icon"></image>
        <text class="toolbar-text">私信</text>
      </view>
      <view class="toolbar-item call-button" @click="callPhone">
        <view class="call-button-content">
          <text class="call-text">打电话</text>
          <text class="call-subtitle">请说在磁州生活网看到的</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted } from 'vue'

// 格式化时间
const formatTime = (timestamp) => {
  const date = new Date(timestamp);
  return `${date.getMonth() + 1}月${date.getDate()}日`;
};

// 响应式数据
const isCollected = ref(false);
const serviceData = ref({
  id: 'find12345',
  title: '寻找专业水电维修师傅',
  budget: '200-500元（视情况而定）',
  tags: ['水电维修', '急聘', '可议价', '今日内', '有保修'],
  publishTime: Date.now() - 86400000 * 0.5, // 12小时前
  description: '家里厨房水管突然漏水，现已临时关闭总阀门。需要专业的水电维修师傅上门检查维修，要求经验丰富，能快速诊断问题并解决。因情况紧急，希望能今天下午或晚上来处理。',
  type: '水电维修',
  area: '磁县城区-县政府附近',
  time: '今天（越快越好）',
  method: '上门服务',
  details: [
    { label: '维修项目', value: '厨房水管漏水' },
    { label: '具体位置', value: '厨房水槽下方管道' },
    { label: '漏水情况', value: '渗水明显，已关闭总阀门' },
    { label: '紧急程度', value: '非常紧急（影响正常用水）' },
    { label: '服务要求', value: '持证上岗，经验丰富，维修彻底' },
    { label: '预算说明', value: '基础检查+维修费用，耗材另计' }
  ],
  notes: '<p>补充说明：</p><ul><li>需要师傅自带齐全工具，我家没有维修工具</li><li>希望能提供材料发票和维修保修服务</li><li>最好能提前电话联系确认到达时间</li><li>我家是6楼，有电梯</li><li>预算可根据实际情况适当调整，主要求修复彻底不返工</li></ul>',
  publisher: {
    name: '张先生',
    avatar: '/static/images/avatar.png',
    type: '个人用户',
    rating: 'A+（优质用户）',
    isVerified: true
  },
  contact: {
    name: '张先生',
    phone: '13912345678'
  }
});

const similarServices = ref([
  {
    id: 'find001',
    title: '急寻专业空调维修师傅上门检修',
    budget: '100-300元',
    type: '空调维修',
    area: '磁县城区'
  },
  {
    id: 'find002',
    title: '找专业小时工打扫新房',
    budget: '80元/小时',
    type: '保洁服务',
    area: '磁县城区'
  },
  {
    id: 'find003',
    title: '寻找搬家工人帮忙搬家',
    budget: '300元',
    type: '搬家服务',
    area: '磁县城区'
  },
  {
    id: 'find004',
    title: '找厨师上门做一天席面',
    budget: '800元/天',
    type: '厨师服务',
    area: '磁县城区'
  }
]);

// 相关服务推荐数据
const relatedServices = ref([]);

// 加载相关服务推荐
const loadRelatedServices = () => {
  // 这里应该调用API获取数据
  // 实际项目中应该根据当前服务需求的类型、标签等进行相关性匹配
  
  // 模拟数据
  setTimeout(() => {
    relatedServices.value = [
      {
        id: 'service001',
        title: '专业水电维修师傅',
        providerName: '老王维修店',
        providerLogo: '/static/images/tabbar/公司.png',
        tags: ['水电维修', '上门服务', '5年经验'],
        price: '上门费50元起'
      },
      {
        id: 'service002',
        title: '水管安装疏通修理',
        providerName: '王师傅维修',
        providerLogo: '/static/images/tabbar/企业.png',
        tags: ['管道疏通', '安装维修', '快速响应'],
        price: '定价优惠'
      },
      {
        id: 'service003',
        title: '全能水电维修服务',
        providerName: '张师傅维修',
        providerLogo: '/static/images/tabbar/个人.png',
        tags: ['专业水电', '随叫随到', '保修一年'],
        price: '面议'
      }
    ];
  }, 500);
};

// 跳转到服务详情页
const navigateToServiceDetail = (serviceId) => {
  // 防止跳转到当前页面
  if (serviceId === serviceData.value.id) {
    return;
  }
  
  uni.navigateTo({
    url: `/pages/publish/home-service-detail?id=${serviceId}`
  });
};

// 跳转到服务需求列表页
const navigateToServiceList = (e) => {
  if (e) e.stopPropagation();
  const serviceCategory = serviceData.value.type || '';
  uni.navigateTo({
    url: `/subPackages/service/pages/filter?type=find-service&title=${encodeURIComponent('服务需求')}&category=${encodeURIComponent(serviceCategory)}&active=find`
  });
};

// 海报相关数据
const posterImagePath = ref('');
const showPosterFlag = ref(false);

// 方法
const toggleCollect = () => {
  isCollected.value = !isCollected.value;
  if (isCollected.value) {
    uni.showToast({
      title: '收藏成功',
      icon: 'success'
    });
  }
};

const showShareOptions = () => {
  uni.showShareMenu({
    withShareTicket: true,
    menus: ['shareAppMessage', 'shareTimeline']
  });
};

const callPhone = () => {
  uni.makePhoneCall({
    phoneNumber: serviceData.value.contact.phone,
    fail: () => {
      uni.showToast({
        title: '拨打电话失败',
        icon: 'none'
      });
    }
  });
};

const navigateToService = (id) => {
  uni.navigateTo({
    url: `/pages/publish/find-service-detail?id=${id}`
  });
};

// 跳转到首页
const goToHome = () => {
  uni.switchTab({
    url: '/pages/index/index'
  });
};

// 打开私信聊天
const openChat = () => {
  if (!serviceData.value.publisher || !serviceData.value.publisher.id) {
    uni.showToast({
      title: '无法获取发布者信息',
      icon: 'none'
    });
    return;
  }
  
  // 跳转到聊天页面
  uni.navigateTo({
    url: `/pages/chat/index?userId=${serviceData.value.publisher.id}&username=${encodeURIComponent(serviceData.value.publisher.name || '发布者')}`
  });
};

// 加载服务详情数据
const loadServiceData = (id) => {
  // 判断是否是从发布页过来的临时数据
  if (id && id.startsWith('temp_')) {
    // 从本地缓存中获取数据
    const publishDataList = uni.getStorageSync('publishDataList') || [];
    const publishData = publishDataList.find(item => item.id === id);
    
    if (publishData) {
      // 更新服务数据
      updateServiceDataFromPublish(publishData);
    } else {
      uni.showToast({
        title: '数据加载失败',
        icon: 'none'
      });
    }
  } else {
    // 从服务器获取数据（这里使用模拟数据）
    console.log('获取服务ID:', id);
    // 保持默认的静态数据
  }
};

// 根据发布数据更新服务详情
const updateServiceDataFromPublish = (publishData) => {
  // 更新基本信息
  serviceData.value = {
    ...serviceData.value,
    id: publishData.id,
    title: publishData.title || '寻找服务',
    budget: publishData.budget || '预算待定',
    publishTime: publishData.publishTime || Date.now(),
    description: publishData.demandDescription || publishData.description || serviceData.value.description,
    type: publishData.serviceType || '服务类型',
    area: publishData.serviceArea || '磁县城区',
    time: publishData.urgency === '非常紧急' ? '今天（越快越好）' : '近期',
    method: '上门服务'
  };
  
  // 更新联系信息
  if (publishData.contact && publishData.phone) {
    serviceData.value.contact = {
      name: publishData.contact,
      phone: publishData.phone
    };
    
    // 同步更新发布者信息
    serviceData.value.publisher = {
      name: publishData.contact,
      avatar: '/static/images/avatar.png',
      type: '个人用户',
      rating: 'A（普通用户）',
      isVerified: true
    };
  }
  
  // 更新标签
  if (publishData.requirements && publishData.requirements.length > 0) {
    serviceData.value.tags = publishData.requirements;
  } else if (publishData.tags && publishData.tags.length > 0) {
    serviceData.value.tags = publishData.tags;
  }
  
  // 更新详情数据
  const newDetails = [];
  
  // 根据表单数据构建详情项
  if (publishData.serviceType) {
    newDetails.push({ label: '服务类型', value: publishData.serviceType });
  }
  
  if (publishData.serviceArea) {
    newDetails.push({ label: '服务区域', value: publishData.serviceArea });
  }
  
  if (publishData.urgency) {
    newDetails.push({ label: '紧急程度', value: publishData.urgency });
  }
  
  if (publishData.budget) {
    newDetails.push({ label: '预算', value: publishData.budget });
  }
  
  // 如果有图片，添加图片说明
  if (publishData.images && publishData.images.length > 0) {
    newDetails.push({ label: '相关图片', value: `已上传${publishData.images.length}张图片` });
  }
  
  // 更新详情列表
  if (newDetails.length > 0) {
    serviceData.value.details = newDetails;
  }
  
  // 构建补充说明HTML
  if (publishData.demandDescription) {
    serviceData.value.notes = `<p>需求详情：</p><p>${publishData.demandDescription}</p>`;
  }
};

// 生成海报的方法
const generateShareImage = () => {
  uni.showLoading({
    title: '正在生成海报...',
    mask: true
  });
  
  // 创建海报数据对象
  const posterData = {
    title: serviceData.value.title,
    budget: serviceData.value.budget,
    type: serviceData.value.type,
    address: serviceData.value.area,
    phone: serviceData.value.publisher ? serviceData.value.publisher.phone : '',
    description: serviceData.value.description ? serviceData.value.description.substring(0, 60) + '...' : '',
    qrcode: '/static/images/tabbar/客服微信.png',
    logo: '/static/images/tabbar/商家入驻.png',
    bgImage: '/static/images/banner/banner-1.png'
  };
  
  // #ifdef H5
  // H5环境不支持canvas绘制图片保存，提示用户
  setTimeout(() => {
    uni.hideLoading();
    uni.showModal({
      title: '提示',
      content: 'H5环境暂不支持保存海报，请使用App或小程序',
      showCancel: false
    });
  }, 1000);
  return;
  // #endif
  
  // 绘制海报
  const ctx = uni.createCanvasContext('posterCanvas');
  
  // 绘制背景
  ctx.save();
  ctx.drawImage(posterData.bgImage, 0, 0, 600, 400);
  // 添加半透明蒙层
  ctx.setFillStyle('rgba(0, 0, 0, 0.35)');
  ctx.fillRect(0, 0, 600, 900);
  ctx.restore();
  
  // 绘制白色卡片背景
  ctx.save();
  ctx.setFillStyle('#ffffff');
  ctx.fillRect(30, 280, 540, 550);
  ctx.restore();
  
  // 绘制Logo
  ctx.save();
  ctx.beginPath();
  ctx.arc(300, 200, 80, 0, 2 * Math.PI);
  ctx.setFillStyle('#ffffff');
  ctx.fill();
  // 在圆形内绘制Logo
  ctx.clip();
  ctx.drawImage(posterData.logo, 220, 120, 160, 160);
  ctx.restore();
  
  // 绘制标题
  ctx.setFillStyle('#333333');
  ctx.setFontSize(32);
  ctx.setTextAlign('center');
  ctx.fillText(posterData.title, 300, 350);
  
  // 绘制预算
  ctx.setFillStyle('#FF6B6B');
  ctx.setFontSize(28);
  ctx.fillText(posterData.budget, 300, 400);
  
  // 分割线
  ctx.beginPath();
  ctx.setStrokeStyle('#eeeeee');
  ctx.setLineWidth(2);
  ctx.moveTo(100, 430);
  ctx.lineTo(500, 430);
  ctx.stroke();
  
  // 绘制服务类型
  ctx.setFillStyle('#666666');
  ctx.setFontSize(24);
  ctx.setTextAlign('left');
  ctx.fillText('服务类型: ' + posterData.type, 80, 480);
  
  // 绘制服务区域
  ctx.fillText('服务区域: ' + posterData.address, 80, 520);
  
  // A wrap text function
  const wrapText = (ctx, text, x, y, maxWidth, lineHeight) => {
    if (text.length === 0) return;
    
    const words = text.split('');
    let line = '';
    let testLine = '';
    let lineCount = 0;
    
    for (let n = 0; n < words.length; n++) {
      testLine += words[n];
      const metrics = ctx.measureText(testLine);
      const testWidth = metrics.width;
      
      if (testWidth > maxWidth && n > 0) {
        ctx.fillText(line, x, y + (lineCount * lineHeight));
        line = words[n];
        testLine = words[n];
        lineCount++;
        
        if (lineCount >= 3) {
          line += '...';
          ctx.fillText(line, x, y + (lineCount * lineHeight));
          break;
        }
      } else {
        line = testLine;
      }
    }
    
    if (lineCount < 3) {
      ctx.fillText(line, x, y + (lineCount * lineHeight));
    }
  };
  
  // 绘制简介
  ctx.setFillStyle('#666666');
  wrapText(ctx, posterData.description, 80, 560, 440, 35);
  
  // 绘制电话
  if (posterData.phone) {
    ctx.fillText('联系电话: ' + posterData.phone, 80, 680);
  }
  
  // 绘制小程序码
  ctx.drawImage(posterData.qrcode, 225, 720, 150, 150);
  
  // 提示文字
  ctx.setFillStyle('#999999');
  ctx.setFontSize(20);
  ctx.setTextAlign('center');
  ctx.fillText('长按识别二维码查看详情', 300, 880);
  
  // 应用平台Logo
  ctx.setFillStyle('#333333');
  ctx.setFontSize(24);
  ctx.fillText('磁县同城 - 生活服务', 300, 840);
  
  // 绘制完成，输出图片
  ctx.draw(false, () => {
    setTimeout(() => {
      // 延迟确保canvas已完成渲染
      uni.canvasToTempFilePath({
        canvasId: 'posterCanvas',
        success: (res) => {
          uni.hideLoading();
          showPosterModal(res.tempFilePath);
        },
        fail: (err) => {
          console.error('生成海报失败', err);
          uni.hideLoading();
          uni.showToast({
            title: '生成海报失败',
            icon: 'none'
          });
        }
      });
    }, 800);
  });
};

// 显示海报预览和保存选项
const showPosterModal = (posterPath) => {
  posterImagePath.value = posterPath;
  showPosterFlag.value = true;
  
  uni.showModal({
    title: '海报已生成',
    content: '海报已生成，是否保存到相册？',
    confirmText: '保存',
    success: (res) => {
      if (res.confirm) {
        savePosterToAlbum(posterPath);
      } else {
        // 预览图片
        uni.previewImage({
          urls: [posterPath],
          current: posterPath
        });
      }
    }
  });
};

// 保存海报到相册
const savePosterToAlbum = (posterPath) => {
  uni.showLoading({
    title: '正在保存...'
  });
  
  uni.saveImageToPhotosAlbum({
    filePath: posterPath,
    success: () => {
      uni.hideLoading();
      uni.showToast({
        title: '已保存到相册',
        icon: 'success'
      });
    },
    fail: (err) => {
      uni.hideLoading();
      console.error('保存失败', err);
      
      if (err.errMsg.indexOf('auth deny') > -1) {
        uni.showModal({
          title: '提示',
          content: '保存失败，请授权相册权限后重试',
          confirmText: '去设置',
          success: (res) => {
            if (res.confirm) {
              uni.openSetting();
            }
          }
        });
      } else {
        uni.showToast({
          title: '保存失败',
          icon: 'none'
        });
      }
    }
  });
};

// 生命周期钩子
onMounted(() => {
  // 修改页面标题
  uni.setNavigationBarTitle({
    title: '需求详情'
  });
  uni.setNavigationBarColor({
    frontColor: '#000000',
    backgroundColor: '#ffffff'
  });
  
  // 获取路由参数
  const pages = getCurrentPages();
  const currentPage = pages[pages.length - 1];
  const options = currentPage.options || {};
  
  // 获取服务ID
  if (options.id) {
    loadServiceData(options.id);
  }
  
  // 加载相关服务推荐
  loadRelatedServices();
  
  // 如果是从发布页面跳转过来的，自动触发分享
  if (options.fromPublish) {
    setTimeout(() => {
      // 自动触发分享按钮点击
      const shareButton = document.getElementById('shareButton');
      if (shareButton) {
        shareButton.click();
      }
    }, 1500);
  }
});
</script>

<style>
.find-service-container {
  min-height: 100vh;
  background-color: #f5f7fa;
  padding-bottom: 110rpx;
  padding-top: 150rpx; /* 增加顶部内边距，为固定导航栏留出空间 */
}

.find-service-wrapper {
  padding: 24rpx;
}

.content-card {
  margin-bottom: 24rpx;
  border-radius: 24rpx;
  background-color: #fff;
  padding: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

/* 需求基本信息卡片 */
.service-title-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.service-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
}

.service-budget {
  font-size: 36rpx;
  font-weight: 600;
  color: #ff4d4f;
}

.service-meta {
  margin-bottom: 24rpx;
}

.service-tag-group {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 12rpx;
}

.service-tag {
  font-size: 24rpx;
  color: #1890ff;
  background-color: rgba(24, 144, 255, 0.1);
  padding: 4rpx 16rpx;
  border-radius: 6rpx;
  margin-right: 16rpx;
  margin-bottom: 12rpx;
}

.service-publish-time {
  font-size: 24rpx;
  color: #999;
}

/* 需求描述 */
.service-description {
  padding: 24rpx;
  background-color: #f9fafc;
  border-radius: 12rpx;
  margin-bottom: 24rpx;
}

.desc-text {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
}

/* 基本信息 */
.service-basic-info {
  display: flex;
  flex-wrap: wrap;
  padding: 24rpx;
  background-color: #f9fafc;
  border-radius: 12rpx;
}

.info-item {
  width: 50%;
  padding: 12rpx 24rpx;
  box-sizing: border-box;
}

.info-label {
  font-size: 26rpx;
  color: #999;
  margin-bottom: 8rpx;
  display: block;
}

.info-value {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

/* 需求详情 */
.detail-list {
  display: flex;
  flex-direction: column;
}

.detail-item {
  display: flex;
  padding: 16rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.detail-item:last-child {
  border-bottom: none;
}

.detail-label {
  width: 160rpx;
  font-size: 28rpx;
  color: #999;
}

.detail-value {
  flex: 1;
  font-size: 28rpx;
  color: #333;
}

/* 补充说明 */
.notes-content {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
  white-space: pre-wrap;
}

/* 发布者信息 */
.publisher-header {
  display: flex;
  align-items: center;
}

.publisher-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  overflow: hidden;
  margin-right: 20rpx;
}

.publisher-avatar image {
  width: 100%;
  height: 100%;
}

.publisher-info {
  flex: 1;
}

.publisher-name {
  font-size: 30rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 8rpx;
}

.publisher-meta {
  display: flex;
  align-items: center;
}

.publisher-type, .publisher-rating {
  font-size: 24rpx;
  color: #666;
  margin-right: 16rpx;
}

/* 相似需求 */
.similar-list {
  display: flex;
  flex-direction: column;
}

.similar-item {
  padding: 20rpx;
  background-color: #f9fafc;
  border-radius: 12rpx;
  margin-bottom: 16rpx;
}

.similar-service-info {
  display: flex;
  flex-direction: column;
}

.similar-service-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 8rpx;
}

.similar-service-budget {
  font-size: 28rpx;
  color: #ff4d4f;
  margin-bottom: 8rpx;
}

.similar-service-meta {
  font-size: 24rpx;
  color: #999;
}

/* 底部互动工具栏 */
.interaction-toolbar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  padding: 10rpx 10rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-top: 1rpx solid #f0f0f0;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  height: 120rpx;
  z-index: 100;
}

.toolbar-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 6rpx 0;
  margin: 0 4rpx;
}

.toolbar-icon {
  width: 44rpx;
  height: 44rpx;
  margin-bottom: 6rpx;
}

.toolbar-text {
  font-size: 22rpx;
  color: #666;
}

.share-button {
  background: transparent;
  border: none;
  margin: 0;
  padding: 0;
  line-height: normal;
  border-radius: 0;
  flex: 1;
}

.share-button::after {
  display: none;
}

.call-button {
  flex: 3; /* 占据更多空间，原来是2，现在改为3让按钮更长 */
  background: linear-gradient(135deg, #0052CC, #0066FF);
  height: 90rpx;
  margin: 0 0 0 10rpx;
  border-radius: 45rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 82, 204, 0.2);
}

.call-button-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
}

.call-text {
  color: #fff;
  font-size: 30rpx;
  font-weight: bold;
  line-height: 1.2;
}

.call-subtitle {
  color: rgba(255, 255, 255, 0.9);
  font-size: 20rpx;
  line-height: 1.2;
}

/* 隐藏原来的底部操作栏 */
.action-bar {
  display: none;
}

/* 隐藏的分享按钮 */
.hidden-share-btn {
  position: absolute;
  top: -9999rpx;
  left: -9999rpx;
  width: 0;
  height: 0;
  padding: 0;
  margin: 0;
  opacity: 0;
}

/* 悬浮海报按钮 */
.float-poster-btn {
  position: fixed;
  right: 30rpx;
  bottom: 200rpx;
  width: 100rpx;
  height: 100rpx;
  background: rgba(240, 240, 240, 0.9);
  border-radius: 50%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  box-shadow: 0 6rpx 20rpx rgba(0,0,0,0.08);
  z-index: 90;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1rpx solid rgba(230, 230, 230, 0.6);
  transition: all 0.2s ease;
}

.float-poster-btn:active {
  transform: scale(0.95);
  box-shadow: 0 3rpx 10rpx rgba(0,0,0,0.08);
}

.poster-icon {
  width: 40rpx;
  height: 40rpx;
  margin-bottom: 4rpx;
}

.poster-text {
  font-size: 20rpx;
  color: #444;
  line-height: 1;
}

/* 相关服务推荐样式 */
.related-services-card {
  margin-top: 12px;
  background-color: #fff;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.related-services-content {
  padding: 0 16px 16px;
  overflow: hidden;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 16px;
  position: relative;
  padding-left: 10px;
}

.section-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 4px;
  width: 3px;
  height: 16px;
  background-color: #0052CC;
  border-radius: 3px;
}

/* 相关服务列表样式 */
.related-services-list {
  margin-bottom: 12px;
}

.related-service-item {
  padding: 12px 0;
  border-bottom: 1px solid #f5f5f5;
}

.related-service-item:last-child {
  border-bottom: none;
}

.service-item-content {
  display: flex;
  align-items: center;
}

.service-item-left {
  margin-right: 12px;
}

.provider-logo {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  background-color: #f5f7fa;
}

.service-item-middle {
  flex: 1;
}

.service-item-title {
  font-size: 15px;
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
}

.service-item-provider {
  font-size: 13px;
  color: #666;
  margin-bottom: 4px;
}

.service-item-tags {
  display: flex;
  flex-wrap: wrap;
}

.service-item-tag {
  font-size: 12px;
  color: #666;
  background-color: #f5f7fa;
  padding: 2px 6px;
  border-radius: 4px;
  margin-right: 6px;
  margin-bottom: 4px;
}

.service-item-tag-more {
  font-size: 12px;
  color: #999;
  padding: 2px 0;
}

.service-item-right {
  text-align: right;
}

.service-item-price {
  font-size: 15px;
  color: #ff4d4f;
  font-weight: 500;
}

/* 查看更多按钮 */
.view-more-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 44px;
  background-color: #f7f9fc;
  border-radius: 8px;
  margin-top: 8px;
}

.view-more-text {
  font-size: 14px;
  color: #0052CC;
}

.view-more-icon {
  margin-left: 4px;
  font-size: 12px;
  color: #0052CC;
}

/* 空数据提示 */
.empty-related-services {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 24px 0;
}

.empty-image {
  width: 80px;
  height: 80px;
  margin-bottom: 12px;
}

.empty-text {
  font-size: 14px;
  color: #999;
}

.custom-navbar {
  background: linear-gradient(135deg, #0066FF, #0052CC);
  height: 88rpx;
  padding-top: 44px; /* 状态栏高度 */
  display: flex;
  align-items: center;
  position: fixed; /* 改为固定定位 */
  top: 0;
  left: 0;
  right: 0;
  padding-bottom: 10rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 82, 204, 0.2);
  z-index: 100; /* 提高z-index确保在最上层 */
}
</style> 