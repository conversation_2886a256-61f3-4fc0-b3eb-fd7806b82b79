<template>
	<view class="points-container">
		<!-- 状态栏占位 -->
		<view class="status-bar" :style="{ height: statusBarHeight + 'px' }"></view>
		
		<!-- 导航栏 -->
		<view class="navbar" :style="{ top: statusBarHeight + 'px' }">
			<view class="navbar-left" @click="goBack">
				<image class="back-icon" src="/static/images/tabbar/最新返回键.png" mode="aspectFit"></image>
				</view>
			<text class="navbar-title">每日签到</text>
			<view class="navbar-right"></view>
		</view>
		
		<!-- 内容区域 -->
		<view class="content-area" :style="{ paddingTop: contentMarginTop }">
			<!-- 积分概览卡片 -->
			<view class="points-overview-card">
				<view class="points-balance-section">
					<view class="points-title">我的积分</view>
					<view class="points-balance">{{userPoints}}</view>
					<view class="points-balance-divider"></view>
					<view class="points-actions">
						<view class="action-btn" @click="navigateTo('/subPackages/checkin/pages/points-detail')">积分明细</view>
						<view class="action-btn primary" @click="navigateTo('/subPackages/checkin/pages/points-mall')">积分商城</view>
					</view>
				</view>
			</view>
			
			<!-- 签到模块 -->
			<view class="sign-in-module">
				<view class="rule-float-btn" @click="showPointsRules">
					<image class="rules-icon" src="/static/images/tabbar/问号.png" mode="aspectFit"></image>
				</view>
				<view class="module-header">
					<view class="module-title-container">
						<text class="module-title">每日签到</text>
						<text class="module-subtitle">连续签到奖励更多积分</text>
					</view>
					<view class="rank-btn" @click="navigateTo('./points-rank')">
						<text class="rank-btn-text">积分排行榜</text>
						<image class="rank-btn-icon" src="/static/images/tabbar/rank.png" mode="aspectFit"></image>
					</view>
				</view>
				
				<view class="calendar-container">
					<view class="calendar-week-header">
						<text class="week-day" v-for="day in ['日','一','二','三','四','五','六']" :key="day">{{day}}</text>
					</view>
					
					<view class="calendar-days">
						<view class="day-item" 
							v-for="(day, index) in calendarDays" 
							:key="index"
							:class="{
								'signed': day.signed,
								'today': day.isToday,
								'future': day.isFuture,
								'empty': !day.day
							}">
							<text class="day-number">{{day.day}}</text>
							<text class="day-status" v-if="day.signed">+{{day.points}}</text>
							<text class="day-status sign-text" v-else-if="day.isToday && !day.signed">签到</text>
						</view>
					</view>
				</view>
				
				<view class="sign-btn-container">
					<button class="sign-in-btn" 
						:class="{ 'signed-today': isTodaySigned }"
						:disabled="isTodaySigned"
						@click="signIn">
						{{ isTodaySigned ? '今日已签到' : '立即签到' }}
					</button>
					<view class="sign-in-tip">已连续签到 {{continuousDays}} 天</view>
				</view>
			</view>
			
			<!-- 每日任务模块 -->
			<view class="daily-tasks-module">
				<view class="module-header">
					<text class="module-title">每日任务</text>
					<text class="module-subtitle">完成任务可获取更多积分</text>
				</view>
				
				<view class="task-list">
					<view class="task-item" v-for="(task, index) in dailyTasks" :key="index">
						<view class="task-info">
							<view class="task-icon-wrap" :style="{ backgroundColor: task.iconBg }">
								<image :src="task.icon" class="task-icon"></image>
							</view>
							<view class="task-detail">
								<view class="task-name">{{task.name}}</view>
								<view class="task-desc">{{task.description}}</view>
							</view>
						</view>
						<view class="task-status">
							<view class="task-points">+{{task.points}}</view>
							<button class="task-btn" 
								:class="{ 
									'task-completed': task.completed, 
									'task-in-progress': task.progress < task.target && task.progress > 0 
								}" 
								@click="handleTask(index)">
								<text v-if="task.completed">已完成</text>
								<text v-else-if="task.progress > 0">
									{{ task.progress }}/{{ task.target }}
								</text>
								<text v-else>去完成</text>
							</button>
							<view class="progress-bar" v-if="task.progress < task.target && task.progress > 0">
								<view class="progress-fill" :style="{ width: (task.progress / task.target * 100) + '%' }"></view>
							</view>
						</view>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 全新的积分规则弹窗 -->
		<view class="popup-mask" v-if="showRulesPopup" @click="closePointsRules"></view>
		<view class="new-rules-popup" v-if="showRulesPopup">
			<view class="new-rules-header">
				<text class="new-rules-title">积分规则</text>
				<view class="new-rules-close" @click="closePointsRules">×</view>
			</view>
			<view class="new-rules-content">
				<view class="new-rule-item">
					<view class="new-rule-title">
						<view class="rule-badge"></view>
						<text>签到规则</text>
					</view>
					<view class="new-rule-desc">每日签到可获得2积分。连续签到有额外奖励，连续7天可额外获得30积分。</view>
				</view>
				
				<view class="new-rule-item">
					<view class="new-rule-title">
						<view class="rule-badge"></view>
						<text>任务奖励</text>
					</view>
					<view class="new-rule-desc">完成每日任务可获得相应积分，每个任务每天只能获取一次奖励。</view>
				</view>
				
				<view class="new-rule-item">
					<view class="new-rule-title">
						<view class="rule-badge"></view>
						<text>积分有效期</text>
					</view>
					<view class="new-rule-desc">积分自获取之日起有效期为一年，请在有效期内使用完毕。</view>
				</view>
				
				<view class="new-rule-item">
					<view class="new-rule-title">
						<view class="rule-badge"></view>
						<text>积分使用</text>
					</view>
					<view class="new-rule-desc">积分可在积分商城兑换商品或优惠券，也可参与平台特定活动。</view>
				</view>
				
				<view class="new-rule-item">
					<view class="new-rule-title">
						<view class="rule-badge"></view>
						<text>其他说明</text>
					</view>
					<view class="new-rule-desc">使用虚假手段获取积分的行为一经发现，将取消相关积分并可能封禁账号。</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, reactive } from 'vue';
import { onHide } from '@dcloudio/uni-app';

// 响应式状态
const statusBarHeight = ref(20);
const userPoints = ref(1280);
const isTodaySigned = ref(false);
const continuousDays = ref(3);
const calendarDays = ref([]);
const showRulesPopup = ref(false);

// 内部状态
const _data = reactive({
  _pendingShareTask: null,
  _pendingCommentTask: null,
  _pendingProfileTask: null,
  _categoryViewInterval: null
});

// 每日任务数据
const dailyTasks = ref([
  {
    name: '浏览同城团购',
    description: '浏览社区团购页面，每次+2分',
    icon: '/static/images/tabbar/阿团购.png',
    iconBg: 'rgba(22, 119, 255, 0.08)',
    points: 2,
    progress: 1,
    target: 5,
    completed: false
  },
  {
    name: '浏览商家',
    description: '浏览商家详情页，每次+1分',
    icon: '/static/images/tabbar/阿商家.png',
    iconBg: 'rgba(22, 119, 255, 0.08)',
    points: 1,
    progress: 1,
    target: 5,
    completed: false
  },
  {
    name: '浏览商家活动',
    description: '浏览商家活动页面，每次+1分',
    icon: '/static/images/tabbar/阿商家.png',
    iconBg: 'rgba(22, 119, 255, 0.08)',
    points: 1,
    progress: 1,
    target: 5,
    completed: false
  },
  {
    name: '浏览分类信息',
    description: '浏览招聘、兼职、转让等分类信息',
    icon: '/static/images/tabbar/阿分类.png',
    iconBg: 'rgba(22, 119, 255, 0.08)',
    points: 1,
    progress: 1,
    target: 5,
    completed: false
  },
  {
    name: '完成同城团购',
    description: '参与并完成一次同城团购订单',
    icon: '/static/images/tabbar/阿团购.png',
    iconBg: 'rgba(22, 119, 255, 0.08)',
    points: 20,
    progress: 0,
    target: 1,
    completed: false
  },
  {
    name: '发布信息',
    description: '发布一条同城信息',
    icon: '/static/images/tabbar/阿发布.png',
    iconBg: 'rgba(22, 119, 255, 0.08)',
    points: 2,
    progress: 0,
    target: 1,
    completed: false
  },
  {
    name: '分享小程序',
    description: '分享小程序到群聊',
    icon: '/static/images/tabbar/阿分享.png',
    iconBg: 'rgba(22, 119, 255, 0.08)',
    points: 5,
    progress: 0,
    target: 1,
    completed: false
  },
  {
    name: '参与本地活动',
    description: '浏览活动页并报名参加',
    icon: '/static/images/tabbar/阿活动.png',
    iconBg: 'rgba(22, 119, 255, 0.08)',
    points: 5,
    progress: 0,
    target: 1,
    completed: false
  },
  {
    name: '评论互动',
    description: '发表评论',
    icon: '/static/images/tabbar/阿评论.png',
    iconBg: 'rgba(22, 119, 255, 0.08)',
    points: 1,
    progress: 1,
    target: 1,
    completed: false
  },
  {
    name: '完善个人资料',
    description: '补充完善个人信息',
    icon: '/static/images/tabbar/阿资料.png',
    iconBg: 'rgba(22, 119, 255, 0.08)',
    points: 10,
    progress: 0,
    target: 1,
    completed: false
  }
]);

// 计算属性
const contentMarginTop = computed(() => {
  return statusBarHeight.value + 88 + 'px';
});

// 生命周期钩子
onMounted(() => {
  // 设置系统标题栏标题
  uni.setNavigationBarTitle({
    title: '每日签到'
  });
  
  // 获取状态栏高度
  try {
    const windowInfo = uni.getWindowInfo();
    statusBarHeight.value = windowInfo.statusBarHeight || 20;
  
    // 设置CSS变量(小程序环境)
    uni.setStorageSync('statusBarHeight', statusBarHeight.value);
  } catch (e) {
    console.error('获取系统信息失败:', e);
    statusBarHeight.value = 20; // 设置默认值
  }
  
  // 生成日历数据
  generateCalendarDays();
  
  // 获取用户积分数据
  getUserPointsData();
});

// 清理定时器
onHide(() => {
  // 清除分类浏览定时器
  if (_data._categoryViewInterval) {
    clearInterval(_data._categoryViewInterval);
    _data._categoryViewInterval = null;
  }
});

onUnmounted(() => {
  // 清除分类浏览定时器
  if (_data._categoryViewInterval) {
    clearInterval(_data._categoryViewInterval);
    _data._categoryViewInterval = null;
  }
});

// 方法
// 生成日历数据
const generateCalendarDays = () => {
  const now = new Date();
  const currentDay = now.getDate();
  const currentMonth = now.getMonth();
  const currentYear = now.getFullYear();
  
  // 获取当月第一天是星期几（0为周日，1-6为周一至周六）
  const firstDayOfWeek = new Date(currentYear, currentMonth, 1).getDay();
  
  // 获取当月总天数
  const daysInMonth = new Date(currentYear, currentMonth + 1, 0).getDate();
  
  const days = [];
  
  // 填充当月第一天之前的空白格子
  for (let i = 0; i < firstDayOfWeek; i++) {
    days.push({
      day: '',
      signed: false,
      isToday: false,
      isFuture: false,
      points: 0,
      empty: true
    });
  }
  
  // 填充日期
  for (let i = 1; i <= daysInMonth; i++) {
    days.push({
      day: i,
      signed: i < currentDay,  // 假设当天之前的都已签到
      isToday: i === currentDay,
      isFuture: i > currentDay,
      points: getSignPoints(i),
      empty: false
    });
  }
  
  // 填充当月最后一天之后的空白格子，确保日历行数完整
  const totalDays = firstDayOfWeek + daysInMonth;
  const remainingCells = totalDays % 7 === 0 ? 0 : 7 - (totalDays % 7);
  
  for (let i = 0; i < remainingCells; i++) {
    days.push({
      day: '',
      signed: false,
      isToday: false, 
      isFuture: false,
      points: 0,
      empty: true
    });
  }
  
  calendarDays.value = days;
  
  // 检查今日是否已签到
  const todayObj = calendarDays.value.find(day => day.isToday);
  if (todayObj) {
    isTodaySigned.value = todayObj.signed;
  }
};

// 获取签到积分规则
const getSignPoints = (day) => {
  // 签到积分统一为2分
  return 2;
};

// 获取用户积分数据
const getUserPointsData = () => {
  // 这里应该调用后端API获取用户积分数据
  // 示例仅使用模拟数据
  setTimeout(() => {
    // 模拟API返回数据
  }, 500);
};

// 签到操作
const signIn = () => {
  if (isTodaySigned.value) return;
  
  // 这里应该调用后端API进行签到
  // 示例仅使用模拟数据
  uni.showLoading({ title: '签到中...' });
  
  setTimeout(() => {
    // 更新今日签到状态
    const todayIndex = calendarDays.value.findIndex(day => day.isToday);
    if (todayIndex !== -1) {
      calendarDays.value[todayIndex].signed = true;
      userPoints.value += calendarDays.value[todayIndex].points;
      isTodaySigned.value = true;
      continuousDays.value += 1;
    }
    
    uni.hideLoading();
    uni.showToast({ 
      title: '签到成功 +' + calendarDays.value[todayIndex].points + '积分', 
      icon: 'none' 
    });
  }, 800);
};

// 处理任务
const handleTask = (index) => {
  const task = dailyTasks.value[index];
  
  if (task.completed) {
    uni.showToast({ 
      title: '任务已完成', 
      icon: 'none' 
    });
    return;
  }
  
  // 根据任务类型跳转到对应页面
  switch (task.name) {
    case '浏览商家':
      // 首先尝试作为主Tab页跳转
      uni.switchTab({
        url: '/pages/business/business',
        success: () => {
          console.log('成功跳转到商家页面');
          // 如果已经达到目标次数，不再增加进度，但仍然给予积分奖励
          if (task.progress >= task.target) {
            // 已完成所有目标次数，但仍然奖励积分
            userPoints.value += task.points;
            uni.showToast({
              title: '已浏览商家 +1积分',
              icon: 'none',
              duration: 2000
            });
          } else {
            // 跳转成功才增加进度
            updateTaskProgress(index, 1);
            uni.showToast({
              title: '已浏览商家 +1积分',
              icon: 'none',
              duration: 2000
            });
          }
        },
        fail: () => {
          // 如果不是Tab页，尝试普通页面跳转
          uni.navigateTo({
            url: '/pages/business/business',
            success: () => {
              console.log('成功跳转到商家页面(navigateTo)');
              // 如果已经达到目标次数，不再增加进度，但仍然给予积分奖励
              if (task.progress >= task.target) {
                // 已完成所有目标次数，但仍然奖励积分
                userPoints.value += task.points;
                uni.showToast({
                  title: '已浏览商家 +1积分',
                  icon: 'none',
                  duration: 2000
                });
              } else {
                // 跳转成功才增加进度
                updateTaskProgress(index, 1);
                uni.showToast({
                  title: '已浏览商家 +1积分',
                  icon: 'none',
                  duration: 2000
                });
              }
            },
            fail: (err) => {
              console.error('跳转失败:', err);
              uni.showToast({
                title: '页面跳转失败',
                icon: 'none'
              });
            }
          });
        }
      });
      break;
    case '浏览商家活动':
      // 检查页面是否存在
      uni.navigateTo({
        url: '/pages/business/activity',
        success: () => {
          console.log('成功跳转到商家活动页面');
          // 如果已经达到目标次数，不再增加进度，但仍然给予积分奖励
          if (task.progress >= task.target) {
            // 已完成所有目标次数，但仍然奖励积分
            userPoints.value += task.points;
            uni.showToast({
              title: '已浏览商家活动 +1积分',
              icon: 'none',
              duration: 2000
            });
          } else {
            // 正常增加进度并奖励积分
            updateTaskProgress(index, 1);
            uni.showToast({
              title: '已浏览商家活动 +1积分',
              icon: 'none',
              duration: 2000
            });
          }
        },
        fail: (err) => {
          console.error('商家活动页面跳转失败:', err);
          // 尝试跳转到商家页面作为替代
          uni.switchTab({
            url: '/pages/business/business',
            success: () => {
              console.log('跳转到商家页面替代');
              // 提示用户使用替代页面
              uni.showToast({
                title: '商家活动页面开发中，请浏览商家页面完成任务',
                icon: 'none',
                duration: 2000
              });
              // 如果已经达到目标次数，不再增加进度，但仍然给予积分奖励
              if (task.progress >= task.target) {
                // 已完成所有目标次数，但仍然奖励积分
                userPoints.value += task.points;
              } else {
                // 浏览替代页面也增加进度
                updateTaskProgress(index, 1);
              }
            },
            fail: () => {
              uni.showToast({
                title: '页面跳转失败',
                icon: 'none'
              });
            }
          });
        }
      });
      break;
    case '浏览分类信息':
      uni.switchTab({
        url: '/pages/index/index',
        success: () => {
          updateTaskProgress(index);
          
          // 延迟一小段时间，确保首页已加载完成
          setTimeout(() => {
            // 获取首页实例并调用其scrollToInfoSection方法
            const pages = getCurrentPages();
            const indexPage = pages[pages.length - 1];
            if (indexPage && indexPage.$vm && typeof indexPage.$vm.scrollToInfoSection === 'function') {
              indexPage.$vm.scrollToInfoSection();
            }
          }, 500);
        },
        fail: (err) => {
          console.error('首页跳转失败:', err);
        }
      });
      break;
    case '发布信息':
      uni.switchTab({
        url: '/pages/publish/publish',
        success: () => {
          console.log('成功跳转到发布页面');
          updateTaskProgress(index);
        },
        fail: () => {
          uni.navigateTo({
            url: '/pages/publish/publish',
            success: () => {
              console.log('成功跳转到发布页面(navigateTo)');
              updateTaskProgress(index);
            },
            fail: (err) => {
              console.error('跳转失败:', err);
              uni.showToast({
                title: '页面跳转失败',
                icon: 'none'
              });
            }
          });
        }
      });
      break;
    case '分享小程序':
      // 调用分享API
      uni.showShareMenu({
        withShareTicket: true, // 获取分享票据，用于验证是否分享到群
        menus: ['shareAppMessage', 'shareTimeline'], // 显示分享到好友和朋友圈菜单
        success: () => {
          uni.showToast({ 
            title: '请点击右上角分享到群聊', 
            icon: 'none',
            duration: 3000
          });
          
          // 分享需要用户操作，设置一个标记，在onShareAppMessage中增加进度
          _data._pendingShareTask = index;
          
          // 10秒后重置，避免长时间挂起
          setTimeout(() => {
            if (_data._pendingShareTask === index) {
              _data._pendingShareTask = null;
              uni.showToast({
                title: '分享超时，请重试',
                icon: 'none'
              });
            }
          }, 10000);
        },
        fail: (err) => {
          console.error('分享菜单显示失败:', err);
          uni.showToast({
            title: '分享功能不可用',
            icon: 'none' 
          });
        }
      });
      break;
    case '浏览同城团购':
      // 检查页面是否存在
      uni.navigateTo({
        url: '/pages/community/shopping',
        success: () => {
          console.log('成功跳转到社区团购页面');
          // 如果已经达到目标次数，不再增加进度，但仍然给予积分奖励
          if (task.progress >= task.target) {
            // 已完成所有目标次数，但仍然奖励积分
            userPoints.value += task.points;
            uni.showToast({
              title: '已浏览同城团购 +2积分',
              icon: 'none',
              duration: 2000
            });
          } else {
            // 正常增加进度并奖励积分
            updateTaskProgress(index, 1);
            uni.showToast({
              title: '已浏览同城团购 +2积分',
              icon: 'none',
              duration: 2000
            });
          }
        },
        fail: (err) => {
          console.error('社区团购页面跳转失败:', err);
          // 尝试跳转到商家页面作为替代
          uni.switchTab({
            url: '/pages/business/business',
            success: () => {
              console.log('跳转到商家页面替代');
              // 提示用户使用替代页面
              uni.showToast({
                title: '同城团购正在开发中，请浏览商家页面完成任务',
                icon: 'none',
                duration: 2000
              });
              // 如果已经达到目标次数，不再增加进度，但仍然给予积分奖励
              if (task.progress >= task.target) {
                // 已完成所有目标次数，但仍然奖励积分
                userPoints.value += task.points;
              } else {
                // 浏览替代页面也增加进度
                updateTaskProgress(index, 1);
              }
            },
            fail: () => {
              uni.showToast({
                title: '页面跳转失败',
                icon: 'none'
              });
            }
          });
        }
      });
      break;
    case '完成同城团购':
      // 跳转到团购页面
      uni.navigateTo({
        url: '/pages/community/shopping',
        success: () => {
          console.log('成功跳转到社区团购页面');
          uni.showToast({
            title: '请完成一笔团购订单获取积分',
            icon: 'none',
            duration: 3000
          });
        },
        fail: (err) => {
          console.error('社区团购页面跳转失败:', err);
          // 尝试跳转到商家页面作为替代
          uni.switchTab({
            url: '/pages/business/business',
            success: () => {
              console.log('跳转到商家页面替代');
              uni.showToast({
                title: '同城团购功能开发中，请稍后再试',
                icon: 'none',
                duration: 2000
              });
            },
            fail: () => {
              uni.showToast({
                title: '页面跳转失败',
                icon: 'none'
              });
            }
          });
        }
      });
      break;
	  case '参与本地活动':
      uni.navigateTo({
        url: '/pages/activity/list',
        success: () => {
          console.log('成功跳转到活动列表页');
          updateTaskProgress(index);
        },
        fail: (err) => {
          console.error('活动列表跳转失败:', err);
          uni.showToast({
            title: '活动页面跳转失败',
            icon: 'none'
          });
        }
      });
      break;
    case '评论互动':
      uni.switchTab({
        url: '/pages/index/index',
        success: () => {
          console.log('成功跳转到首页');
          setTimeout(() => {
            uni.showToast({
              title: '请在内容下方评论',
              icon: 'none',
              duration: 2000
            });
          }, 500);
          
          // 设置评论任务标记，用户需要在评论组件中实际发表评论才能获得进度
          // 这里仅做演示，实际应该在评论成功的回调中增加进度
          _data._pendingCommentTask = index;
          
          // 模拟用户10秒后发表评论
          setTimeout(() => {
            if (_data._pendingCommentTask === index) {
              updateTaskProgress(index);
              _data._pendingCommentTask = null;
            }
          }, 10000);
        },
        fail: (err) => {
          console.error('首页跳转失败:', err);
          uni.showToast({
            title: '页面跳转失败',
            icon: 'none'
          });
        }
      });
      break;
    case '完善个人资料':
      // 尝试使用相对路径
      uni.navigateTo({
        url: '/subPackages/checkin/pages/profile',
        success: () => {
          console.log('成功跳转到个人资料页');
          // 这里不立即增加进度，应该在用户实际完善资料后增加
          // 设置一个标记，在profile页面保存资料后回调增加进度
          _data._pendingProfileTask = index;
          
          // 模拟用户5秒后完善资料
          setTimeout(() => {
            if (_data._pendingProfileTask === index) {
              updateTaskProgress(index);
              _data._pendingProfileTask = null;
            }
          }, 5000);
        },
        fail: (err) => {
          console.error('个人资料页跳转失败(相对路径):', err);
          // 尝试使用绝对路径
          uni.navigateTo({
            url: '/pages/new-points/profile',
            success: () => {
              console.log('成功跳转到个人资料页(绝对路径)');
              // 同上，不立即增加进度
              _data._pendingProfileTask = index;
              
              // 模拟用户5秒后完善资料
              setTimeout(() => {
                if (_data._pendingProfileTask === index) {
                  updateTaskProgress(index);
                  _data._pendingProfileTask = null;
                }
              }, 5000);
            },
            fail: (e) => {
              console.error('个人资料页跳转失败(绝对路径):', e);
              uni.showToast({
                title: '个人资料页面不存在',
                icon: 'none'
              });
            }
          });
        }
      });
      break;
    default:
      console.log('未知任务类型:', task.name);
      uni.showToast({ 
        title: '任务类型未定义', 
        icon: 'none' 
      });
      break;
  }
};

// 更新任务进度的方法，从handleTask中提取出来
const updateTaskProgress = (index, incrementValue = 1) => {
  const task = dailyTasks.value[index];
  if (task.progress < task.target) {
    const newProgress = Math.min(task.progress + incrementValue, task.target);
    dailyTasks.value[index].progress = newProgress;
    // 判断任务是否完成
    if (newProgress >= task.target) {
      dailyTasks.value[index].completed = true;
      userPoints.value += task.points;
    }
  }
};

// 页面导航
const navigateTo = (url, successCallback) => {
  if (url.startsWith('/pages/new-points/')) {
    const relativePath = url.replace('/pages/new-points/', './');
    uni.navigateTo({
      url: relativePath,
      success: () => {
        if (successCallback && typeof successCallback === 'function') {
          successCallback();
        }
      },
      fail: (err) => {
        console.error('导航失败:', err);
        // 失败后尝试使用绝对路径
        uni.navigateTo({
          url: url,
          success: successCallback,
          fail: (e) => {
            console.error('绝对路径导航也失败:', e);
            // 如果有回调函数，仍然执行它
            if (successCallback && typeof successCallback === 'function') {
              successCallback();
            }
            uni.showToast({
              title: '已增加任务进度',
              icon: 'none'
            });
          }
        });
      }
    });
  } else if (url.startsWith('/subPackages/checkin/pages/')) {
    const relativePath = url.replace('/subPackages/checkin/pages/', './');
    uni.navigateTo({
      url: relativePath,
      success: () => {
        if (successCallback && typeof successCallback === 'function') {
          successCallback();
        }
      },
      fail: (err) => {
        console.error('导航失败:', err);
        // 失败后尝试使用绝对路径
        uni.navigateTo({
          url: url,
          success: successCallback,
          fail: (e) => {
            console.error('绝对路径导航也失败:', e);
            // 如果有回调函数，仍然执行它
            if (successCallback && typeof successCallback === 'function') {
              successCallback();
            }
            uni.showToast({
              title: '已增加任务进度',
              icon: 'none'
            });
          }
        });
      }
    });
  } else {
    // 其他页面使用绝对路径
    uni.navigateTo({
      url: url,
      success: successCallback,
      fail: (err) => {
        console.error('导航失败:', err);
        // 如果有回调函数，仍然执行它
        if (successCallback && typeof successCallback === 'function') {
          successCallback();
        }
        uni.showToast({
          title: '已增加任务进度',
          icon: 'none'
        });
      }
    });
  }
};

// 返回上一页
const goBack = () => {
  uni.navigateBack();
};

// 显示积分规则
const showPointsRules = () => {
  showRulesPopup.value = true;
};

// 关闭积分规则
const closePointsRules = () => {
  showRulesPopup.value = false;
};

// 分享回调方法
defineExpose({
  onShareAppMessage(res) {
    // 设置分享内容
    const shareData = {
      title: '同城小程序 - 每日签到赚积分',
      path: '/subPackages/checkin/pages/points',
      imageUrl: '/static/images/share-cover.png',
      success: (res) => {
        // 检查是否有shareTickets，判断是否分享到群
        if (res.shareTickets && res.shareTickets.length > 0) {
          console.log('成功分享到群聊');
          // 如果有待处理的分享任务，完成它
          if (_data._pendingShareTask !== null && _data._pendingShareTask !== undefined) {
            const taskIndex = _data._pendingShareTask;
            _data._pendingShareTask = null;
            
            // 更新任务进度
            updateTaskProgress(taskIndex);
            
            uni.showToast({
              title: '群分享成功，获得积分！',
              icon: 'success'
            });
          }
        } else {
          console.log('分享成功，但不是分享到群');
          uni.showToast({
            title: '请分享到群聊才能获得积分',
            icon: 'none'
          });
        }
      },
      fail: (err) => {
        console.error('分享失败:', err);
      }
    };
    
    return shareData;
  }
});
</script>

<style lang="scss" scoped>
page {
	background-color: #F0F2F5;
	font-family: -apple-system, BlinkMacSystemFont, 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;
}

.points-container {
	min-height: 100vh;
	background: #f8f8f8;
	position: relative;
	padding-bottom: 24px;
}

/* 导航栏样式 */
.status-bar {
	background: #1677FF;
	width: 100%;
	position: fixed;
	top: 0;
	left: 0;
	z-index: 100;
}

.navbar {
	position: fixed;
	left: 0;
	right: 0;
	height: 44px;
	background: #1677FF;
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 0 16px;
	box-shadow: 0 2px 8px rgba(22, 119, 255, 0.04);
	border-bottom: 1px solid #f1f1f1;
	z-index: 100;
}

.navbar-left, .navbar-right {
	width: 80rpx;
	height: 80rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}

.back-icon {
	width: 24px;
	height: 24px;
	display: block;
	background: none;
	border-radius: 0;
	margin: 0 auto;
}

.navbar-title {
	font-size: 18px;
	font-weight: 700;
	color: #fff;
	letter-spacing: 0.5px;
}

/* 内容区域 */
.content-area {
	padding-top: 88px;
	padding-bottom: 30rpx;
}

/* 积分概览卡片 */
.points-overview-card {
	background: #FFFFFF;
	border-radius: 16rpx;
	box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
	margin: 20rpx 24rpx 30rpx;
	padding: 24rpx;
	position: relative;
	overflow: hidden;
	
	&::before {
		content: '';
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
		height: 6rpx;
		background: linear-gradient(90deg, #1677FF, #06B6D4);
	}
}

.points-balance-section {
	display: flex;
	flex-direction: column;
	align-items: center;
	padding: 10rpx 0;
}

.points-title {
	font-size: 26rpx;
	color: #666;
	margin-bottom: 8rpx;
}

.points-balance {
	font-size: 60rpx;
	font-weight: bold;
	color: #1677FF;
	margin-bottom: 16rpx;
	font-family: 'DIN Condensed', Arial, sans-serif;
	background: linear-gradient(90deg, #1677FF, #06B6D4);
	-webkit-background-clip: text;
	background-clip: text;
	-webkit-text-fill-color: transparent;
	text-fill-color: transparent;
	letter-spacing: 1rpx;
}

.points-balance-divider {
	width: 30%;
	height: 2rpx;
	background: #f0f0f0;
	margin-bottom: 16rpx;
}

.points-actions {
	display: flex;
	width: 100%;
	justify-content: center;
	gap: 30rpx;
}

.action-btn {
	padding: 10rpx 24rpx;
	font-size: 24rpx;
	color: #666;
	background: #F5F5F5;
	border-radius: 30rpx;
	
	&.primary {
	color: #fff;
		background: #1677FF;
}
}

/* 签到模块 */
.sign-in-module {
	background: #FFFFFF;
	border-radius: 16rpx;
	margin: 0 24rpx 30rpx;
	padding: 30rpx 24rpx;
	position: relative;
	box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
}

.rule-float-btn {
	position: absolute;
	top: 18px;
	right: 18px;
	width: 32px;
	height: 32px;
	background: linear-gradient(135deg, #fafdff 60%, #eaf3ff 100%);
	border-radius: 50%;
	box-shadow: 0 2px 8px rgba(22,119,255,0.08);
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 2;
	cursor: pointer;
	transition: box-shadow 0.2s;
}
.rule-float-btn:active {
	box-shadow: 0 4px 16px rgba(22,119,255,0.12);
}
.rules-icon {
	width: 18px;
	height: 18px;
}
.module-header {
	display: flex;
	align-items: center;
	justify-content: space-between;
	margin-bottom: 16px;
	padding: 0 12px;
}
.module-title-container {
	display: flex;
	flex-direction: column;
}

.module-title {
	font-size: 16px;
	font-weight: 600;
	color: #333;
}
.module-subtitle {
	color: #999;
	font-size: 12px;
	margin-top: 2px;
}

.calendar-container {
	background: #f8f8f8;
	border-radius: 12px;
	margin: 12px 12px 0 12px;
	padding: 10px 0 6px 0;
}
.calendar-week-header {
	display: flex;
	justify-content: space-around;
	margin-bottom: 10px;
}
.week-day {
	font-size: 13px;
	color: #999;
	width: 38px;
	text-align: center;
}
.calendar-days {
	display: grid;
	grid-template-columns: repeat(7, 1fr);
	gap: 5px;
}
.day-item {
	background: #fff;
	border-radius: 8px;
	margin: 2px 0;
	color: #333;
	font-size: 15px;
	box-shadow: 0 1px 3px rgba(22, 119, 255, 0.04);
	transition: background 0.2s;
	width: 38px;
	height: 38px;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	position: relative;
	box-sizing: border-box;
}
.day-item.signed {
	background: #e6f0ff;
	color: #1677FF;
}
.day-item.today {
	border: 2px solid #1677FF;
}
.day-number {
	font-size: 13px;
	color: #333;
	margin-bottom: 2px;
	line-height: 1;
}
.day-status,
.day-status.sign-text {
	margin: 0;
}
.day-status.sign-text {
	background: none;
	border-radius: 0;
	padding: 0;
	font-size: 0;
	min-width: 0;
	height: auto;
	box-shadow: none;
	border: none;
}

.sign-btn-container {
	display: flex;
	flex-direction: column;
	align-items: center;
	margin-top: 16px;
}
.sign-in-btn {
	background: linear-gradient(90deg, #1677FF 0%, #007aff 100%);
	color: #fff;
	border-radius: 18px;
	font-size: 16px;
	font-weight: 600;
	padding: 8px 0;
	margin: 10px 0 0 0;
	width: 80%;
	box-shadow: 0 2px 8px rgba(22, 119, 255, 0.08);
	border: none;
}
.sign-in-btn.signed-today {
	background: #f1f1f1;
	color: #999;
}
.sign-in-btn:active {
	transform: scale(0.98);
	opacity: 0.9;
}
.sign-in-tip {
	color: #999;
	font-size: 13px;
	margin-top: 6px;
}

/* 排行榜按钮新样式 */
.rank-btn {
	display: flex;
	align-items: center;
	background: transparent;
	border-radius: 16px;
	padding: 6px 12px;
	gap: 4px;
}
.rank-btn-text {
	color: #1677FF;
	font-size: 13px;
	font-weight: 500;
}
.rank-btn-icon {
	width: 16px;
	height: 16px;
}

/* 每日任务模块 */
.daily-tasks-module {
	background: #FFFFFF;
	border-radius: 16rpx;
	margin: 0 24rpx 30rpx;
	padding: 30rpx 24rpx;
	box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
}
.task-list {
	display: flex;
	flex-direction: column;
	gap: 8px;
}
.task-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 10px 12px;
	background: #fff;
	border-radius: 12px;
	box-shadow: 0 1px 3px rgba(22, 119, 255, 0.04);
	border: 1px solid #f1f1f1;
	transition: all 0.3s;
}
.task-item:active {
	transform: scale(0.98);
}
.task-info {
	display: flex;
	align-items: center;
	gap: 10px;
}
.task-icon-wrap {
	width: 34px;
	height: 34px;
	border-radius: 10px;
	display: flex;
	align-items: center;
	justify-content: center;
	background: linear-gradient(135deg, #e6f0ff 0%, #f8f8f8 100%);
	box-shadow: 0 2px 6px rgba(22, 119, 255, 0.04);
}
.task-icon {
	width: 20px;
	height: 20px;
}
.task-detail {
	display: flex;
	flex-direction: column;
}
.task-name {
	font-size: 14px;
	font-weight: 600;
	color: #333;
	margin-bottom: 2px;
}
.task-desc {
	font-size: 12px;
	color: #999;
}
.task-status {
	display: flex;
	flex-direction: column;
	align-items: flex-end;
	gap: 6px;
}
.task-points {
	font-size: 14px;
	font-weight: 700;
	color: #1677FF;
}
.task-btn {
	min-width: 72px;
	height: 28px;
	background: linear-gradient(90deg, #1677FF 0%, #007aff 100%);
	color: #fff;
	font-size: 12px;
	border-radius: 14px;
	display: flex;
	align-items: center;
	justify-content: center;
	box-shadow: 0 2px 6px rgba(22, 119, 255, 0.08);
	font-weight: 600;
	border: none;
}
.task-btn.task-completed {
	background: #f1f1f1;
	color: #999;
	box-shadow: none;
}
.task-btn.task-in-progress {
	background: linear-gradient(90deg, #ffb300 0%, #ffec80 100%);
	color: #fff;
}
.progress-bar {
	width: 72px;
	height: 4px;
	background: #f1f1f1;
	border-radius: 2px;
	overflow: hidden;
}
.progress-fill {
	height: 100%;
	background: linear-gradient(90deg, #1677FF 0%, #63b3ed 100%);
	border-radius: 2px;
	transition: width 0.3s;
}

/* 任务按钮样式 */
.task-btn.task-in-progress + .progress-bar {
	display: block;
}

/* 全新的积分规则弹窗样式 */
.popup-mask {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.25);
	backdrop-filter: blur(2px);
	z-index: 1000;
}

.new-rules-popup {
	position: fixed;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
	width: 85%;
	max-width: 580rpx;
	background: #fff;
	border-radius: 18px;
	box-shadow: 0 6px 24px rgba(22, 119, 255, 0.08);
	z-index: 1001;
	animation: popupIn 0.3s;
	overflow: hidden;
}
.new-rules-popup::before {
	content: '';
	display: block;
	width: 100%;
	height: 5px;
	background: linear-gradient(90deg, #1677FF 0%, #007aff 100%);
}
.new-rules-header {
	padding: 18px 18px 14px;
	border-bottom: 1px solid #f1f1f1;
	display: flex;
	justify-content: space-between;
	align-items: center;
}
.new-rules-title {
	font-size: 17px;
	font-weight: 700;
	color: #333;
}
.new-rules-close {
	width: 30px;
	height: 30px;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 22px;
	color: #999;
	border-radius: 50%;
	background: #f8f8f8;
}
.new-rules-content {
	padding: 18px;
	max-height: 60vh;
	overflow-y: auto;
}
.new-rule-item {
	margin-bottom: 16px;
}
.new-rule-item:last-child {
	margin-bottom: 0;
}
.new-rule-title {
	display: flex;
	align-items: center;
	gap: 8px;
	margin-bottom: 6px;
}
.rule-badge {
	width: 6px;
	height: 6px;
	background: #1677FF;
	border-radius: 50%;
}
.new-rule-title text {
	font-size: 14px;
	font-weight: 600;
	color: #333;
}
.new-rule-desc {
	font-size: 13px;
	color: #999;
	line-height: 1.6;
	padding-left: 14px;
}
/* 添加总积分显示样式 */
.task-item:first-child .task-points {
	position: relative;
	display: flex;
	align-items: center;
}
.task-item:first-child .task-points::after {
	content: '(共+10)';
	font-size: 10px;
	color: #1677FF;
	font-weight: normal;
	position: static;
	margin-left: 4px;
	background-color: rgba(22, 119, 255, 0.08);
	padding: 1px 4px;
	border-radius: 8px;
}
.task-item:nth-child(2) .task-points {
	position: relative;
	display: flex;
	align-items: center;
}
.task-item:nth-child(2) .task-points::after {
	content: '(共+5)';
	font-size: 10px;
	color: #1677FF;
	font-weight: normal;
	position: static;
	margin-left: 4px;
	background-color: rgba(22, 119, 255, 0.08);
	padding: 1px 4px;
	border-radius: 8px;
}
.task-item:nth-child(3) .task-points {
	position: relative;
	display: flex;
	align-items: center;
}
.task-item:nth-child(3) .task-points::after {
	content: '(共+5)';
	font-size: 10px;
	color: #1677FF;
	font-weight: normal;
	position: static;
	margin-left: 4px;
	background-color: rgba(22, 119, 255, 0.08);
	padding: 1px 4px;
	border-radius: 8px;
}
.task-item:nth-child(4) .task-points {
	position: relative;
	display: flex;
	align-items: center;
}
.task-item:nth-child(4) .task-points::after {
	content: '(共+5)';
	font-size: 10px;
	color: #1677FF;
	font-weight: normal;
	position: static;
	margin-left: 4px;
	background-color: rgba(22, 119, 255, 0.08);
	padding: 1px 4px;
	border-radius: 8px;
}
.task-item:nth-child(5) .task-points {
	position: relative;
	display: flex;
	align-items: center;
}
.task-item:nth-child(5) .task-points::after {
	content: '';
	font-size: 10px;
	color: #1677FF;
	font-weight: normal;
	position: static;
	margin-left: 0;
	background-color: transparent;
	padding: 0;
	border-radius: 8px;
}
</style>