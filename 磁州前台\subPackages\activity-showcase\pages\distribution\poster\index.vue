<template>
  <view class="poster-container">
    <!-- 海报预览区域 -->
    <view class="poster-preview" :style="{
      borderRadius: '35px',
      boxShadow: '0 8px 20px rgba(0,0,0,0.08)',
      background: '#FFFFFF',
      padding: '30rpx',
      marginBottom: '30rpx',
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'center'
    }">
      <view class="preview-header" :style="{
        width: '100%',
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: '30rpx'
      }">
        <text class="preview-title" :style="{
          fontSize: '32rpx',
          fontWeight: '600',
          color: '#333333'
        }">海报预览</text>
        
        <view class="poster-switch" :style="{
          display: 'flex',
          alignItems: 'center'
        }">
          <text :style="{
            fontSize: '26rpx',
            color: '#666666',
            marginRight: '10rpx'
          }">样式:</text>
          
          <view class="switch-buttons" :style="{
            display: 'flex',
            borderRadius: '20rpx',
            overflow: 'hidden',
            border: '1rpx solid #EFEFEF'
          }">
            <view 
              v-for="(style, index) in posterStyles" 
              :key="index"
              class="switch-item"
              :class="{ active: currentStyle === index }"
              @click="switchStyle(index)"
              :style="{
                padding: '8rpx 20rpx',
                fontSize: '24rpx',
                color: currentStyle === index ? '#FFFFFF' : '#666666',
                background: currentStyle === index ? '#AC39FF' : '#F8F8F8',
                borderRight: index < posterStyles.length - 1 ? '1rpx solid #EFEFEF' : 'none'
              }"
            >
              {{ style }}
            </view>
          </view>
        </view>
      </view>
      
      <!-- 海报图片 -->
      <view class="poster-image-wrapper" :style="{
        width: '600rpx',
        height: '900rpx',
        position: 'relative',
        borderRadius: '20rpx',
        overflow: 'hidden',
        boxShadow: '0 10px 30px rgba(0,0,0,0.1)'
      }">
        <image 
          :src="currentPosterUrl" 
          mode="aspectFill" 
          :style="{
            width: '100%',
            height: '100%'
          }"
        ></image>
        
        <!-- 二维码 -->
        <view class="qrcode-wrapper" :style="{
          position: 'absolute',
          right: '30rpx',
          bottom: '30rpx',
          width: '180rpx',
          height: '180rpx',
          background: '#FFFFFF',
          borderRadius: '10rpx',
          padding: '10rpx',
          boxShadow: '0 5px 15px rgba(0,0,0,0.1)'
        }">
          <image 
            src="/static/images/distribution/qrcode.png" 
            mode="aspectFit" 
            :style="{
              width: '100%',
              height: '100%'
            }"
          ></image>
        </view>
      </view>
      
      <!-- 操作按钮 -->
      <view class="poster-actions" :style="{
        display: 'flex',
        justifyContent: 'center',
        marginTop: '30rpx',
        width: '100%'
      }">
        <view class="action-btn preview-btn" @click="previewPoster" :style="{
          flex: '1',
          margin: '0 10rpx',
          height: '80rpx',
          borderRadius: '40rpx',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          background: 'rgba(172,57,255,0.1)',
          border: '1rpx solid #AC39FF',
          color: '#AC39FF',
          fontSize: '28rpx',
          fontWeight: '500'
        }">
          <svg class="icon" viewBox="0 0 24 24" width="20" height="20" :style="{ marginRight: '10rpx' }">
            <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z" stroke="#AC39FF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
            <circle cx="12" cy="12" r="3" stroke="#AC39FF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></circle>
          </svg>
          预览
        </view>
        
        <view class="action-btn save-btn" @click="savePoster" :style="{
          flex: '1',
          margin: '0 10rpx',
          height: '80rpx',
          borderRadius: '40rpx',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          background: 'linear-gradient(135deg, #AC39FF 0%, #B87AFF 100%)',
          color: '#FFFFFF',
          fontSize: '28rpx',
          fontWeight: '500',
          boxShadow: '0 5px 15px rgba(172,57,255,0.3)'
        }">
          <svg class="icon" viewBox="0 0 24 24" width="20" height="20" :style="{ marginRight: '10rpx' }">
            <path d="M19 21H5a2 2 0 01-2-2V5a2 2 0 012-2h11l5 5v11a2 2 0 01-2 2z" stroke="#FFFFFF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
            <polyline points="17 21 17 13 7 13 7 21" stroke="#FFFFFF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></polyline>
            <polyline points="7 3 7 8 15 8" stroke="#FFFFFF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></polyline>
          </svg>
          保存到相册
        </view>
      </view>
    </view>
    
    <!-- 商品选择区域 -->
    <view class="product-selection" :style="{
      borderRadius: '35px',
      boxShadow: '0 8px 20px rgba(0,0,0,0.08)',
      background: '#FFFFFF',
      padding: '30rpx',
      marginBottom: '30rpx'
    }">
      <view class="selection-header" :style="{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: '20rpx'
      }">
        <text class="selection-title" :style="{
          fontSize: '32rpx',
          fontWeight: '600',
          color: '#333333'
        }">选择推广商品</text>
        
        <view class="view-all" @click="navigateTo('/subPackages/activity-showcase/pages/distribution/products/index')" :style="{
          fontSize: '26rpx',
          color: '#AC39FF',
          display: 'flex',
          alignItems: 'center'
        }">
          <text>查看全部</text>
          <svg class="icon" viewBox="0 0 24 24" width="16" height="16">
            <path d="M9 18l6-6-6-6" stroke="#AC39FF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
          </svg>
        </view>
      </view>
      
      <!-- 商品列表 -->
      <view class="product-list" :style="{
        display: 'flex',
        flexDirection: 'column'
      }">
        <view 
          v-for="(product, index) in products" 
          :key="index"
          class="product-item"
          :class="{ active: currentProduct === index }"
          @click="selectProduct(index)"
          :style="{
            padding: '20rpx',
            borderRadius: '20rpx',
            marginBottom: '20rpx',
            display: 'flex',
            background: currentProduct === index ? 'rgba(172,57,255,0.1)' : '#F8F8F8',
            border: currentProduct === index ? '1rpx solid #AC39FF' : '1rpx solid transparent'
          }"
        >
          <image 
            :src="product.image" 
            mode="aspectFill" 
            :style="{
              width: '120rpx',
              height: '120rpx',
              borderRadius: '10rpx',
              marginRight: '20rpx'
            }"
          ></image>
          
          <view class="product-info" :style="{ flex: '1' }">
            <text class="product-name" :style="{
              fontSize: '28rpx',
              fontWeight: '500',
              color: '#333333',
              marginBottom: '10rpx',
              display: 'block',
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              display: '-webkit-box',
              '-webkit-line-clamp': '2',
              '-webkit-box-orient': 'vertical'
            }">{{ product.name }}</text>
            
            <view class="product-price" :style="{
              display: 'flex',
              alignItems: 'baseline',
              marginBottom: '10rpx'
            }">
              <text :style="{
                fontSize: '24rpx',
                color: '#FF3B69',
                marginRight: '5rpx'
              }">¥</text>
              <text :style="{
                fontSize: '32rpx',
                fontWeight: '600',
                color: '#FF3B69',
                marginRight: '10rpx'
              }">{{ product.price }}</text>
              <text :style="{
                fontSize: '24rpx',
                color: '#999999',
                textDecoration: 'line-through'
              }">¥{{ product.originalPrice }}</text>
            </view>
            
            <view class="product-commission" :style="{
              display: 'flex',
              alignItems: 'center'
            }">
              <text :style="{
                fontSize: '24rpx',
                color: '#AC39FF',
                marginRight: '5rpx'
              }">佣金:</text>
              <text :style="{
                fontSize: '24rpx',
                fontWeight: '600',
                color: '#AC39FF'
              }">¥{{ product.commission }}</text>
            </view>
          </view>
          
          <view class="product-check" :style="{
            width: '40rpx',
            height: '40rpx',
            borderRadius: '50%',
            border: currentProduct === index ? '0' : '1rpx solid #CCCCCC',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            background: currentProduct === index ? '#AC39FF' : 'transparent'
          }">
            <svg v-if="currentProduct === index" class="icon" viewBox="0 0 24 24" width="16" height="16">
              <path d="M5 12l5 5L20 7" stroke="#FFFFFF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
            </svg>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 推广文案 -->
    <view class="promotion-text" :style="{
      borderRadius: '35px',
      boxShadow: '0 8px 20px rgba(0,0,0,0.08)',
      background: '#FFFFFF',
      padding: '30rpx',
      marginBottom: '30rpx'
    }">
      <view class="text-header" :style="{
        marginBottom: '20rpx'
      }">
        <text class="text-title" :style="{
          fontSize: '32rpx',
          fontWeight: '600',
          color: '#333333'
        }">推广文案</text>
      </view>
      
      <!-- 文案列表 -->
      <view class="text-list">
        <view 
          v-for="(text, index) in promotionTexts" 
          :key="index"
          class="text-item"
          :style="{
            padding: '20rpx',
            borderRadius: '20rpx',
            marginBottom: '20rpx',
            background: '#F8F8F8',
            position: 'relative'
          }"
        >
          <text :style="{
            fontSize: '26rpx',
            color: '#333333',
            lineHeight: '1.6'
          }">{{ text.content }}</text>
          
          <view class="copy-btn" @click="copyText(text.content)" :style="{
            position: 'absolute',
            right: '20rpx',
            bottom: '20rpx',
            padding: '10rpx 20rpx',
            borderRadius: '30rpx',
            background: 'rgba(172,57,255,0.1)',
            color: '#AC39FF',
            fontSize: '24rpx',
            fontWeight: '500'
          }">
            复制
          </view>
        </view>
      </view>
    </view>
    
    <!-- 分享按钮 -->
    <button 
      open-type="share" 
      class="share-btn" 
      :style="{
        width: '100%',
        height: '90rpx',
        borderRadius: '45rpx',
        background: 'linear-gradient(135deg, #FF3B69 0%, #FF7A9E 100%)',
        color: '#FFFFFF',
        fontSize: '32rpx',
        fontWeight: '500',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        boxShadow: '0 5px 15px rgba(255,59,105,0.3)',
        marginBottom: '30rpx'
      }"
    >
      <svg class="icon" viewBox="0 0 24 24" width="20" height="20" :style="{ marginRight: '10rpx' }">
        <circle cx="18" cy="5" r="3" stroke="#FFFFFF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></circle>
        <circle cx="6" cy="12" r="3" stroke="#FFFFFF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></circle>
        <circle cx="18" cy="19" r="3" stroke="#FFFFFF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></circle>
        <line x1="8.59" y1="13.51" x2="15.42" y2="17.49" stroke="#FFFFFF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></line>
        <line x1="15.41" y1="6.51" x2="8.59" y2="10.49" stroke="#FFFFFF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></line>
      </svg>
      分享给好友
    </button>
    
    <!-- 底部安全区域 -->
    <view class="safe-area-bottom" :style="{
      height: '100rpx',
      paddingBottom: 'env(safe-area-inset-bottom)'
    }"></view>
  </view>
</template>

<script setup>
import { ref, computed } from 'vue';

// 海报样式
const posterStyles = ref(['样式一', '样式二', '样式三']);
const currentStyle = ref(0);

// 当前商品
const currentProduct = ref(0);

// 海报URL
const posterUrls = ref([
  '/static/images/distribution/poster-1.png',
  '/static/images/distribution/poster-2.png',
  '/static/images/distribution/poster-3.png'
]);

// 当前海报URL
const currentPosterUrl = computed(() => {
  return posterUrls.value[currentStyle.value];
});

// 商品列表
const products = ref([
  {
    id: 1,
    name: 'Apple iPhone 14 Pro Max 256GB 暗夜紫 移动联通电信5G双卡双待手机',
    image: 'https://via.placeholder.com/120',
    price: '8999.00',
    originalPrice: '9999.00',
    commission: '300.00'
  },
  {
    id: 2,
    name: '小米12S Ultra 12GB+256GB 丹青黑 骁龙8+旗舰处理器 徕卡专业光学镜头',
    image: 'https://via.placeholder.com/120',
    price: '5999.00',
    originalPrice: '6999.00',
    commission: '200.00'
  },
  {
    id: 3,
    name: '华为Mate 50 Pro 8GB+256GB 曜金黑 超光变XMAGE影像 北斗卫星消息',
    image: 'https://via.placeholder.com/120',
    price: '6799.00',
    originalPrice: '7299.00',
    commission: '250.00'
  }
]);

// 推广文案
const promotionTexts = ref([
  {
    id: 1,
    content: '🔥【限时特惠】iPhone 14 Pro Max 立省1000元！全网最低价，手慢无！点击链接立即抢购：https://t.cn/A6JxYZ7p'
  },
  {
    id: 2,
    content: '✨ 想要高性价比的旗舰手机？小米12S Ultra 现在只要5999元！徕卡专业光学镜头，拍照效果堪比单反！戳我购买：https://t.cn/A6JxYZ7p'
  },
  {
    id: 3,
    content: '💯 华为Mate 50 Pro 新品首发特惠！支持北斗卫星消息，紧急情况也能发短信！现在下单还送豪华配件大礼包！速戳：https://t.cn/A6JxYZ7p'
  }
]);

// 切换海报样式
function switchStyle(index) {
  currentStyle.value = index;
}

// 选择商品
function selectProduct(index) {
  currentProduct.value = index;
}

// 预览海报
function previewPoster() {
  uni.previewImage({
    urls: [currentPosterUrl.value],
    current: currentPosterUrl.value
  });
}

// 保存海报到相册
function savePoster() {
  uni.showLoading({
    title: '保存中...'
  });
  
  // 模拟保存过程
  setTimeout(() => {
    uni.hideLoading();
    
    uni.showToast({
      title: '保存成功',
      icon: 'success'
    });
  }, 1500);
}

// 复制文案
function copyText(text) {
  uni.setClipboardData({
    data: text,
    success: () => {
      uni.showToast({
        title: '复制成功',
        icon: 'success'
      });
    }
  });
}

// 页面导航
function navigateTo(url) {
  uni.navigateTo({ url });
}

// 分享配置
defineExpose({
  onShareAppMessage() {
    return {
      title: products.value[currentProduct.value].name,
      path: `/subPackages/activity-showcase/pages/detail/index?id=${products.value[currentProduct.value].id}&source=distribution`,
      imageUrl: currentPosterUrl.value
    };
  }
});
</script>

<style scoped>
.poster-container {
  padding: 30rpx;
  background-color: #F2F2F7;
  min-height: 100vh;
}

.action-btn:active, .copy-btn:active, .share-btn:active {
  opacity: 0.9;
  transform: scale(0.98);
}
</style> 