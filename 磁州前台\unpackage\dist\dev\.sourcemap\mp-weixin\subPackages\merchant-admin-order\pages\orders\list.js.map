{"version": 3, "file": "list.js", "sources": ["subPackages/merchant-admin-order/pages/orders/list.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/c3ViUGFja2FnZXNcbWVyY2hhbnQtYWRtaW4tb3JkZXJccGFnZXNcb3JkZXJzXGxpc3QudnVl"], "sourcesContent": ["<template>\r\n  <view class=\"order-list-container\">\r\n    <!-- 自定义导航栏 -->\r\n    <view class=\"navbar\">\r\n      <view class=\"navbar-back\" @click=\"goBack\">\r\n        <view class=\"back-icon\"></view>\r\n      </view>\r\n      <text class=\"navbar-title\">订单列表</text>\r\n      <view class=\"navbar-right\">\r\n        <view class=\"search-icon\" @click=\"showSearch\">🔍</view>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 搜索框 -->\r\n    <view v-if=\"showSearchBar\" class=\"search-bar\">\r\n      <input \r\n        class=\"search-input\" \r\n        placeholder=\"搜索订单号、客户名称\" \r\n        v-model=\"searchKeyword\"\r\n        @confirm=\"searchOrders\" />\r\n      <view class=\"search-btn\" @click=\"searchOrders\">搜索</view>\r\n      <view class=\"cancel-btn\" @click=\"cancelSearch\">取消</view>\r\n    </view>\r\n    \r\n    <!-- 筛选条件 -->\r\n    <view class=\"filter-section\">\r\n      <scroll-view scroll-x=\"true\" class=\"filter-scroll\">\r\n        <view \r\n          v-for=\"(status, index) in orderStatuses\" \r\n          :key=\"index\"\r\n          :class=\"['status-tag', {'active': currentStatus === status.value}]\"\r\n          @click=\"filterByStatus(status.value)\">\r\n          {{status.name}}\r\n        </view>\r\n      </scroll-view>\r\n      \r\n      <view class=\"filter-button\" @click=\"showFilterOptions\">\r\n        <text class=\"filter-icon\">⚙️</text>\r\n        <text>筛选</text>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 排序选项 -->\r\n    <view class=\"sort-section\">\r\n      <view \r\n        v-for=\"(option, index) in sortOptions\" \r\n        :key=\"index\"\r\n        :class=\"['sort-option', {'active': currentSort === option.value}]\"\r\n        @click=\"sortOrders(option.value)\">\r\n        {{option.name}}\r\n        <text v-if=\"currentSort === option.value\" class=\"sort-icon\">{{sortDirection === 'asc' ? '↑' : '↓'}}</text>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 订单列表 -->\r\n    <view class=\"order-list\">\r\n      <view \r\n        v-for=\"(order, index) in filteredOrders\" \r\n        :key=\"index\"\r\n        class=\"order-item\"\r\n        @click=\"viewOrderDetail(order.id)\">\r\n        <view class=\"order-header\">\r\n          <text class=\"order-number\">订单号: {{order.orderNo}}</text>\r\n          <text class=\"order-status\" :style=\"{color: getStatusColor(order.status)}\">{{getStatusText(order.status)}}</text>\r\n        </view>\r\n        <view class=\"order-info\">\r\n          <view class=\"customer-info\">\r\n            <text class=\"label\">客户:</text>\r\n            <text class=\"value\">{{order.customerName}}</text>\r\n          </view>\r\n          <view class=\"time-info\">\r\n            <text class=\"label\">下单时间:</text>\r\n            <text class=\"value\">{{order.createTime}}</text>\r\n          </view>\r\n          <view class=\"amount-info\">\r\n            <text class=\"label\">订单金额:</text>\r\n            <text class=\"value price\">¥{{order.totalAmount}}</text>\r\n          </view>\r\n        </view>\r\n        <view class=\"product-preview\">\r\n          <image \r\n            v-for=\"(product, productIndex) in order.products.slice(0, 3)\" \r\n            :key=\"productIndex\"\r\n            :src=\"product.image\"\r\n            mode=\"aspectFill\"\r\n            class=\"product-image\"></image>\r\n          <view v-if=\"order.products.length > 3\" class=\"more-products\">+{{order.products.length - 3}}</view>\r\n        </view>\r\n        <view class=\"order-actions\">\r\n          <view class=\"action-btn primary\" @click.stop=\"handleOrder(order.id)\">处理订单</view>\r\n          <view class=\"action-btn\" @click.stop=\"contactCustomer(order.id)\">联系客户</view>\r\n        </view>\r\n      </view>\r\n      \r\n      <!-- 加载更多 -->\r\n      <view v-if=\"hasMoreOrders\" class=\"load-more\" @click=\"loadMoreOrders\">\r\n        <text class=\"load-text\">加载更多</text>\r\n      </view>\r\n      \r\n      <!-- 空状态 -->\r\n      <view v-if=\"filteredOrders.length === 0\" class=\"empty-state\">\r\n        <view class=\"empty-icon\">📭</view>\r\n        <text class=\"empty-text\">暂无订单数据</text>\r\n        <view class=\"refresh-btn\" @click=\"refreshOrders\">刷新</view>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 批量操作浮动按钮 -->\r\n    <view class=\"batch-action-btn\" @click=\"toggleBatchMode\">\r\n      <text class=\"batch-icon\">{{batchMode ? '✓' : '☰'}}</text>\r\n    </view>\r\n    \r\n    <!-- 批量操作底部栏 -->\r\n    <view v-if=\"batchMode\" class=\"batch-action-bar\">\r\n      <view class=\"selection-info\">\r\n        已选择 <text class=\"selected-count\">{{selectedOrders.length}}</text> 个订单\r\n      </view>\r\n      <view class=\"batch-actions\">\r\n        <view class=\"batch-btn\" @click=\"batchExport\">导出</view>\r\n        <view class=\"batch-btn\" @click=\"batchPrint\">打印</view>\r\n        <view class=\"batch-btn primary\" @click=\"batchProcess\">批量处理</view>\r\n      </view>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  data() {\r\n    return {\r\n      showSearchBar: false,\r\n      searchKeyword: '',\r\n      currentStatus: 'all',\r\n      currentSort: 'time',\r\n      sortDirection: 'desc',\r\n      batchMode: false,\r\n      selectedOrders: [],\r\n      hasMoreOrders: true,\r\n      orderStatuses: [\r\n        { name: '全部', value: 'all' },\r\n        { name: '待处理', value: 'pending' },\r\n        { name: '处理中', value: 'processing' },\r\n        { name: '已完成', value: 'completed' },\r\n        { name: '已取消', value: 'cancelled' },\r\n        { name: '退款中', value: 'refunding' }\r\n      ],\r\n      sortOptions: [\r\n        { name: '下单时间', value: 'time' },\r\n        { name: '订单金额', value: 'amount' },\r\n        { name: '客户名称', value: 'customer' }\r\n      ],\r\n      orders: [\r\n        {\r\n          id: '1001',\r\n          orderNo: 'CZ20230501001',\r\n          status: 'pending',\r\n          customerName: '张三',\r\n          createTime: '2023-05-01 10:30',\r\n          totalAmount: '128.00',\r\n          products: [\r\n            { id: '2001', name: '精品水果礼盒', image: '/static/images/product-1.jpg' },\r\n            { id: '2002', name: '有机蔬菜套餐', image: '/static/images/product-2.jpg' }\r\n          ]\r\n        },\r\n        {\r\n          id: '1002',\r\n          orderNo: 'CZ20230501002',\r\n          status: 'processing',\r\n          customerName: '李四',\r\n          createTime: '2023-05-01 11:45',\r\n          totalAmount: '256.50',\r\n          products: [\r\n            { id: '2003', name: '生日蛋糕', image: '/static/images/product-3.jpg' },\r\n            { id: '2004', name: '鲜花束', image: '/static/images/product-4.jpg' },\r\n            { id: '2005', name: '贺卡', image: '/static/images/product-5.jpg' }\r\n          ]\r\n        },\r\n        {\r\n          id: '1003',\r\n          orderNo: 'CZ20230502003',\r\n          status: 'completed',\r\n          customerName: '王五',\r\n          createTime: '2023-05-02 09:15',\r\n          totalAmount: '89.90',\r\n          products: [\r\n            { id: '2006', name: '进口零食礼包', image: '/static/images/product-6.jpg' }\r\n          ]\r\n        },\r\n        {\r\n          id: '1004',\r\n          orderNo: 'CZ20230502004',\r\n          status: 'cancelled',\r\n          customerName: '赵六',\r\n          createTime: '2023-05-02 14:20',\r\n          totalAmount: '199.00',\r\n          products: [\r\n            { id: '2007', name: '红酒套装', image: '/static/images/product-7.jpg' },\r\n            { id: '2008', name: '高档茶叶', image: '/static/images/product-8.jpg' }\r\n          ]\r\n        },\r\n        {\r\n          id: '1005',\r\n          orderNo: 'CZ20230503005',\r\n          status: 'refunding',\r\n          customerName: '钱七',\r\n          createTime: '2023-05-03 16:35',\r\n          totalAmount: '158.80',\r\n          products: [\r\n            { id: '2009', name: '护肤品套装', image: '/static/images/product-9.jpg' },\r\n            { id: '2010', name: '面膜', image: '/static/images/product-10.jpg' },\r\n            { id: '2011', name: '洗面奶', image: '/static/images/product-11.jpg' },\r\n            { id: '2012', name: '爽肤水', image: '/static/images/product-12.jpg' }\r\n          ]\r\n        }\r\n      ]\r\n    }\r\n  },\r\n  computed: {\r\n    filteredOrders() {\r\n      let result = [...this.orders];\r\n      \r\n      // 状态筛选\r\n      if (this.currentStatus !== 'all') {\r\n        result = result.filter(order => order.status === this.currentStatus);\r\n      }\r\n      \r\n      // 关键词搜索\r\n      if (this.searchKeyword) {\r\n        const keyword = this.searchKeyword.toLowerCase();\r\n        result = result.filter(order => \r\n          order.orderNo.toLowerCase().includes(keyword) || \r\n          order.customerName.toLowerCase().includes(keyword)\r\n        );\r\n      }\r\n      \r\n      // 排序\r\n      result.sort((a, b) => {\r\n        let compareResult = 0;\r\n        \r\n        if (this.currentSort === 'time') {\r\n          compareResult = new Date(a.createTime) - new Date(b.createTime);\r\n        } else if (this.currentSort === 'amount') {\r\n          compareResult = parseFloat(a.totalAmount) - parseFloat(b.totalAmount);\r\n        } else if (this.currentSort === 'customer') {\r\n          compareResult = a.customerName.localeCompare(b.customerName);\r\n        }\r\n        \r\n        return this.sortDirection === 'asc' ? compareResult : -compareResult;\r\n      });\r\n      \r\n      return result;\r\n    }\r\n  },\r\n  methods: {\r\n    goBack() {\r\n      uni.navigateBack();\r\n    },\r\n    showSearch() {\r\n      this.showSearchBar = true;\r\n    },\r\n    cancelSearch() {\r\n      this.showSearchBar = false;\r\n      this.searchKeyword = '';\r\n    },\r\n    searchOrders() {\r\n      console.log('搜索关键词:', this.searchKeyword);\r\n      // 实际项目中，这里应该调用API进行搜索\r\n    },\r\n    filterByStatus(status) {\r\n      this.currentStatus = status;\r\n    },\r\n    showFilterOptions() {\r\n      uni.showActionSheet({\r\n        itemList: ['按时间范围筛选', '按商品类型筛选', '按支付方式筛选'],\r\n        success: (res) => {\r\n          uni.showToast({\r\n            title: '高级筛选功能开发中',\r\n            icon: 'none'\r\n          });\r\n        }\r\n      });\r\n    },\r\n    sortOrders(sortType) {\r\n      if (this.currentSort === sortType) {\r\n        // 切换排序方向\r\n        this.sortDirection = this.sortDirection === 'asc' ? 'desc' : 'asc';\r\n      } else {\r\n        this.currentSort = sortType;\r\n        this.sortDirection = 'desc'; // 默认降序\r\n      }\r\n    },\r\n    viewOrderDetail(orderId) {\r\n      uni.navigateTo({\r\n        url: `./detail?id=${orderId}`\r\n      });\r\n    },\r\n    handleOrder(orderId) {\r\n      uni.navigateTo({\r\n        url: `./detail?id=${orderId}&action=process`\r\n      });\r\n    },\r\n    contactCustomer(orderId) {\r\n      uni.showToast({\r\n        title: '联系客户功能开发中',\r\n        icon: 'none'\r\n      });\r\n    },\r\n    loadMoreOrders() {\r\n      uni.showLoading({\r\n        title: '加载中...'\r\n      });\r\n      \r\n      // 模拟加载更多订单\r\n      setTimeout(() => {\r\n        uni.hideLoading();\r\n        \r\n        // 这里应该是实际加载更多数据的逻辑\r\n        // 模拟没有更多数据了\r\n        this.hasMoreOrders = false;\r\n        \r\n        uni.showToast({\r\n          title: '没有更多订单了',\r\n          icon: 'none'\r\n        });\r\n      }, 1000);\r\n    },\r\n    refreshOrders() {\r\n      uni.showLoading({\r\n        title: '刷新中...'\r\n      });\r\n      \r\n      // 模拟刷新订单列表\r\n      setTimeout(() => {\r\n        uni.hideLoading();\r\n        \r\n        uni.showToast({\r\n          title: '刷新成功',\r\n          icon: 'success'\r\n        });\r\n      }, 1000);\r\n    },\r\n    toggleBatchMode() {\r\n      this.batchMode = !this.batchMode;\r\n      if (!this.batchMode) {\r\n        this.selectedOrders = [];\r\n      }\r\n    },\r\n    toggleOrderSelection(orderId) {\r\n      const index = this.selectedOrders.indexOf(orderId);\r\n      if (index === -1) {\r\n        this.selectedOrders.push(orderId);\r\n      } else {\r\n        this.selectedOrders.splice(index, 1);\r\n      }\r\n    },\r\n    batchExport() {\r\n      if (this.selectedOrders.length === 0) {\r\n        uni.showToast({\r\n          title: '请先选择订单',\r\n          icon: 'none'\r\n        });\r\n        return;\r\n      }\r\n      \r\n      uni.showToast({\r\n        title: '导出功能开发中',\r\n        icon: 'none'\r\n      });\r\n    },\r\n    batchPrint() {\r\n      if (this.selectedOrders.length === 0) {\r\n        uni.showToast({\r\n          title: '请先选择订单',\r\n          icon: 'none'\r\n        });\r\n        return;\r\n      }\r\n      \r\n      uni.showToast({\r\n        title: '打印功能开发中',\r\n        icon: 'none'\r\n      });\r\n    },\r\n    batchProcess() {\r\n      if (this.selectedOrders.length === 0) {\r\n        uni.showToast({\r\n          title: '请先选择订单',\r\n          icon: 'none'\r\n        });\r\n        return;\r\n      }\r\n      \r\n      uni.showModal({\r\n        title: '批量处理',\r\n        content: `确认处理选中的 ${this.selectedOrders.length} 个订单？`,\r\n        success: (res) => {\r\n          if (res.confirm) {\r\n            uni.showToast({\r\n              title: '批量处理功能开发中',\r\n              icon: 'none'\r\n            });\r\n          }\r\n        }\r\n      });\r\n    },\r\n    getStatusColor(status) {\r\n      const colors = {\r\n        pending: '#FF9800',\r\n        processing: '#2196F3',\r\n        completed: '#4CAF50',\r\n        cancelled: '#9E9E9E',\r\n        refunding: '#F44336'\r\n      };\r\n      return colors[status] || '#333333';\r\n    },\r\n    getStatusText(status) {\r\n      const texts = {\r\n        pending: '待处理',\r\n        processing: '处理中',\r\n        completed: '已完成',\r\n        cancelled: '已取消',\r\n        refunding: '退款中'\r\n      };\r\n      return texts[status] || '未知状态';\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style>\r\n.order-list-container {\r\n  min-height: 100vh;\r\n  background-color: #f5f7fa;\r\n  padding-bottom: 16px;\r\n}\r\n\r\n.navbar {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n  padding: 44px 16px 10px;\r\n  background: linear-gradient(135deg, #1677FF, #065DD2);\r\n  position: relative;\r\n  z-index: 100;\r\n}\r\n\r\n.navbar-back {\r\n  width: 40px;\r\n  height: 40px;\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.back-icon {\r\n  width: 12px;\r\n  height: 12px;\r\n  border-left: 2px solid #fff;\r\n  border-bottom: 2px solid #fff;\r\n  transform: rotate(45deg);\r\n}\r\n\r\n.navbar-title {\r\n  color: #fff;\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n  flex: 1;\r\n  text-align: center;\r\n}\r\n\r\n.navbar-right {\r\n  width: 40px;\r\n  height: 40px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: flex-end;\r\n}\r\n\r\n.search-icon {\r\n  font-size: 20px;\r\n  color: #fff;\r\n}\r\n\r\n.search-bar {\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 10px 16px;\r\n  background-color: #fff;\r\n  box-shadow: 0 2px 4px rgba(0,0,0,0.05);\r\n}\r\n\r\n.search-input {\r\n  flex: 1;\r\n  height: 36px;\r\n  border: 1px solid #ddd;\r\n  border-radius: 18px;\r\n  padding: 0 12px;\r\n  font-size: 14px;\r\n}\r\n\r\n.search-btn {\r\n  padding: 0 12px;\r\n  height: 36px;\r\n  background-color: #1677FF;\r\n  color: #fff;\r\n  border-radius: 18px;\r\n  margin-left: 8px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  font-size: 14px;\r\n}\r\n\r\n.cancel-btn {\r\n  padding: 0 12px;\r\n  height: 36px;\r\n  color: #666;\r\n  margin-left: 8px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  font-size: 14px;\r\n}\r\n\r\n.filter-section {\r\n  display: flex;\r\n  padding: 12px 16px;\r\n  background-color: #fff;\r\n  margin-top: 8px;\r\n  box-shadow: 0 2px 4px rgba(0,0,0,0.05);\r\n}\r\n\r\n.filter-scroll {\r\n  flex: 1;\r\n  white-space: nowrap;\r\n}\r\n\r\n.status-tag {\r\n  display: inline-block;\r\n  padding: 6px 12px;\r\n  margin-right: 8px;\r\n  background-color: #f0f0f0;\r\n  border-radius: 16px;\r\n  font-size: 12px;\r\n  color: #666;\r\n}\r\n\r\n.status-tag.active {\r\n  background-color: #e6f7ff;\r\n  color: #1677FF;\r\n  border: 1px solid #91caff;\r\n}\r\n\r\n.filter-button {\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 0 12px;\r\n  margin-left: 8px;\r\n  border-left: 1px solid #eee;\r\n}\r\n\r\n.filter-icon {\r\n  margin-right: 4px;\r\n}\r\n\r\n.sort-section {\r\n  display: flex;\r\n  background-color: #fff;\r\n  padding: 12px 16px;\r\n  margin-top: 1px;\r\n}\r\n\r\n.sort-option {\r\n  margin-right: 24px;\r\n  font-size: 14px;\r\n  color: #666;\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.sort-option.active {\r\n  color: #1677FF;\r\n  font-weight: 500;\r\n}\r\n\r\n.sort-icon {\r\n  margin-left: 4px;\r\n}\r\n\r\n.order-list {\r\n  padding: 16px;\r\n}\r\n\r\n.order-item {\r\n  background-color: #fff;\r\n  border-radius: 8px;\r\n  padding: 16px;\r\n  margin-bottom: 12px;\r\n  box-shadow: 0 2px 8px rgba(0,0,0,0.05);\r\n}\r\n\r\n.order-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  margin-bottom: 12px;\r\n  padding-bottom: 8px;\r\n  border-bottom: 1px solid #f0f0f0;\r\n}\r\n\r\n.order-number {\r\n  font-size: 14px;\r\n  color: #333;\r\n  font-weight: 500;\r\n}\r\n\r\n.order-status {\r\n  font-size: 14px;\r\n  font-weight: 500;\r\n}\r\n\r\n.order-info {\r\n  margin-bottom: 12px;\r\n}\r\n\r\n.customer-info, .time-info, .amount-info {\r\n  display: flex;\r\n  margin-bottom: 6px;\r\n}\r\n\r\n.label {\r\n  width: 70px;\r\n  color: #666;\r\n  font-size: 13px;\r\n}\r\n\r\n.value {\r\n  flex: 1;\r\n  font-size: 13px;\r\n  color: #333;\r\n}\r\n\r\n.price {\r\n  font-weight: 600;\r\n  color: #ff6a00;\r\n}\r\n\r\n.product-preview {\r\n  display: flex;\r\n  margin-bottom: 12px;\r\n}\r\n\r\n.product-image {\r\n  width: 50px;\r\n  height: 50px;\r\n  border-radius: 4px;\r\n  margin-right: 8px;\r\n  background-color: #f0f0f0;\r\n}\r\n\r\n.more-products {\r\n  width: 50px;\r\n  height: 50px;\r\n  border-radius: 4px;\r\n  background-color: rgba(0,0,0,0.05);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  font-size: 14px;\r\n  color: #666;\r\n}\r\n\r\n.order-actions {\r\n  display: flex;\r\n  justify-content: flex-end;\r\n}\r\n\r\n.action-btn {\r\n  padding: 6px 12px;\r\n  border-radius: 4px;\r\n  font-size: 13px;\r\n  margin-left: 8px;\r\n  background-color: #f0f0f0;\r\n  color: #333;\r\n}\r\n\r\n.action-btn.primary {\r\n  background-color: #1677FF;\r\n  color: #fff;\r\n}\r\n\r\n.load-more {\r\n  text-align: center;\r\n  padding: 16px 0;\r\n}\r\n\r\n.load-text {\r\n  font-size: 14px;\r\n  color: #666;\r\n}\r\n\r\n.empty-state {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  padding: 60px 0;\r\n}\r\n\r\n.empty-icon {\r\n  font-size: 48px;\r\n  margin-bottom: 16px;\r\n}\r\n\r\n.empty-text {\r\n  font-size: 16px;\r\n  color: #999;\r\n  margin-bottom: 16px;\r\n}\r\n\r\n.refresh-btn {\r\n  padding: 8px 24px;\r\n  background-color: #1677FF;\r\n  color: #fff;\r\n  border-radius: 4px;\r\n  font-size: 14px;\r\n}\r\n\r\n.batch-action-btn {\r\n  position: fixed;\r\n  right: 16px;\r\n  bottom: 80px;\r\n  width: 50px;\r\n  height: 50px;\r\n  border-radius: 25px;\r\n  background-color: #1677FF;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  box-shadow: 0 4px 12px rgba(0,0,0,0.15);\r\n  z-index: 100;\r\n}\r\n\r\n.batch-icon {\r\n  color: #fff;\r\n  font-size: 24px;\r\n}\r\n\r\n.batch-action-bar {\r\n  position: fixed;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background-color: #fff;\r\n  padding: 12px 16px;\r\n  box-shadow: 0 -2px 8px rgba(0,0,0,0.05);\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  z-index: 100;\r\n}\r\n\r\n.selection-info {\r\n  font-size: 14px;\r\n  color: #666;\r\n}\r\n\r\n.selected-count {\r\n  color: #1677FF;\r\n  font-weight: 600;\r\n}\r\n\r\n.batch-actions {\r\n  display: flex;\r\n}\r\n\r\n.batch-btn {\r\n  padding: 8px 16px;\r\n  border-radius: 4px;\r\n  font-size: 14px;\r\n  margin-left: 8px;\r\n  background-color: #f0f0f0;\r\n  color: #333;\r\n}\r\n\r\n.batch-btn.primary {\r\n  background-color: #1677FF;\r\n  color: #fff;\r\n}\r\n</style> ", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/subPackages/merchant-admin-order/pages/orders/list.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni"], "mappings": ";;AA+HA,MAAK,YAAU;AAAA,EACb,OAAO;AACL,WAAO;AAAA,MACL,eAAe;AAAA,MACf,eAAe;AAAA,MACf,eAAe;AAAA,MACf,aAAa;AAAA,MACb,eAAe;AAAA,MACf,WAAW;AAAA,MACX,gBAAgB,CAAE;AAAA,MAClB,eAAe;AAAA,MACf,eAAe;AAAA,QACb,EAAE,MAAM,MAAM,OAAO,MAAO;AAAA,QAC5B,EAAE,MAAM,OAAO,OAAO,UAAW;AAAA,QACjC,EAAE,MAAM,OAAO,OAAO,aAAc;AAAA,QACpC,EAAE,MAAM,OAAO,OAAO,YAAa;AAAA,QACnC,EAAE,MAAM,OAAO,OAAO,YAAa;AAAA,QACnC,EAAE,MAAM,OAAO,OAAO,YAAY;AAAA,MACnC;AAAA,MACD,aAAa;AAAA,QACX,EAAE,MAAM,QAAQ,OAAO,OAAQ;AAAA,QAC/B,EAAE,MAAM,QAAQ,OAAO,SAAU;AAAA,QACjC,EAAE,MAAM,QAAQ,OAAO,WAAW;AAAA,MACnC;AAAA,MACD,QAAQ;AAAA,QACN;AAAA,UACE,IAAI;AAAA,UACJ,SAAS;AAAA,UACT,QAAQ;AAAA,UACR,cAAc;AAAA,UACd,YAAY;AAAA,UACZ,aAAa;AAAA,UACb,UAAU;AAAA,YACR,EAAE,IAAI,QAAQ,MAAM,UAAU,OAAO,+BAAgC;AAAA,YACrE,EAAE,IAAI,QAAQ,MAAM,UAAU,OAAO,+BAA+B;AAAA,UACtE;AAAA,QACD;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,SAAS;AAAA,UACT,QAAQ;AAAA,UACR,cAAc;AAAA,UACd,YAAY;AAAA,UACZ,aAAa;AAAA,UACb,UAAU;AAAA,YACR,EAAE,IAAI,QAAQ,MAAM,QAAQ,OAAO,+BAAgC;AAAA,YACnE,EAAE,IAAI,QAAQ,MAAM,OAAO,OAAO,+BAAgC;AAAA,YAClE,EAAE,IAAI,QAAQ,MAAM,MAAM,OAAO,+BAA+B;AAAA,UAClE;AAAA,QACD;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,SAAS;AAAA,UACT,QAAQ;AAAA,UACR,cAAc;AAAA,UACd,YAAY;AAAA,UACZ,aAAa;AAAA,UACb,UAAU;AAAA,YACR,EAAE,IAAI,QAAQ,MAAM,UAAU,OAAO,+BAA+B;AAAA,UACtE;AAAA,QACD;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,SAAS;AAAA,UACT,QAAQ;AAAA,UACR,cAAc;AAAA,UACd,YAAY;AAAA,UACZ,aAAa;AAAA,UACb,UAAU;AAAA,YACR,EAAE,IAAI,QAAQ,MAAM,QAAQ,OAAO,+BAAgC;AAAA,YACnE,EAAE,IAAI,QAAQ,MAAM,QAAQ,OAAO,+BAA+B;AAAA,UACpE;AAAA,QACD;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,SAAS;AAAA,UACT,QAAQ;AAAA,UACR,cAAc;AAAA,UACd,YAAY;AAAA,UACZ,aAAa;AAAA,UACb,UAAU;AAAA,YACR,EAAE,IAAI,QAAQ,MAAM,SAAS,OAAO,+BAAgC;AAAA,YACpE,EAAE,IAAI,QAAQ,MAAM,MAAM,OAAO,gCAAiC;AAAA,YAClE,EAAE,IAAI,QAAQ,MAAM,OAAO,OAAO,gCAAiC;AAAA,YACnE,EAAE,IAAI,QAAQ,MAAM,OAAO,OAAO,gCAAgC;AAAA,UACpE;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACD;AAAA,EACD,UAAU;AAAA,IACR,iBAAiB;AACf,UAAI,SAAS,CAAC,GAAG,KAAK,MAAM;AAG5B,UAAI,KAAK,kBAAkB,OAAO;AAChC,iBAAS,OAAO,OAAO,WAAS,MAAM,WAAW,KAAK,aAAa;AAAA,MACrE;AAGA,UAAI,KAAK,eAAe;AACtB,cAAM,UAAU,KAAK,cAAc,YAAW;AAC9C,iBAAS,OAAO;AAAA,UAAO,WACrB,MAAM,QAAQ,cAAc,SAAS,OAAO,KAC5C,MAAM,aAAa,cAAc,SAAS,OAAO;AAAA;MAErD;AAGA,aAAO,KAAK,CAAC,GAAG,MAAM;AACpB,YAAI,gBAAgB;AAEpB,YAAI,KAAK,gBAAgB,QAAQ;AAC/B,0BAAgB,IAAI,KAAK,EAAE,UAAU,IAAI,IAAI,KAAK,EAAE,UAAU;AAAA,QAChE,WAAW,KAAK,gBAAgB,UAAU;AACxC,0BAAgB,WAAW,EAAE,WAAW,IAAI,WAAW,EAAE,WAAW;AAAA,QACtE,WAAW,KAAK,gBAAgB,YAAY;AAC1C,0BAAgB,EAAE,aAAa,cAAc,EAAE,YAAY;AAAA,QAC7D;AAEA,eAAO,KAAK,kBAAkB,QAAQ,gBAAgB,CAAC;AAAA,MACzD,CAAC;AAED,aAAO;AAAA,IACT;AAAA,EACD;AAAA,EACD,SAAS;AAAA,IACP,SAAS;AACPA,oBAAG,MAAC,aAAY;AAAA,IACjB;AAAA,IACD,aAAa;AACX,WAAK,gBAAgB;AAAA,IACtB;AAAA,IACD,eAAe;AACb,WAAK,gBAAgB;AACrB,WAAK,gBAAgB;AAAA,IACtB;AAAA,IACD,eAAe;AACbA,wGAAY,UAAU,KAAK,aAAa;AAAA,IAEzC;AAAA,IACD,eAAe,QAAQ;AACrB,WAAK,gBAAgB;AAAA,IACtB;AAAA,IACD,oBAAoB;AAClBA,oBAAAA,MAAI,gBAAgB;AAAA,QAClB,UAAU,CAAC,WAAW,WAAW,SAAS;AAAA,QAC1C,SAAS,CAAC,QAAQ;AAChBA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,UACR,CAAC;AAAA,QACH;AAAA,MACF,CAAC;AAAA,IACF;AAAA,IACD,WAAW,UAAU;AACnB,UAAI,KAAK,gBAAgB,UAAU;AAEjC,aAAK,gBAAgB,KAAK,kBAAkB,QAAQ,SAAS;AAAA,aACxD;AACL,aAAK,cAAc;AACnB,aAAK,gBAAgB;AAAA,MACvB;AAAA,IACD;AAAA,IACD,gBAAgB,SAAS;AACvBA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,eAAe,OAAO;AAAA,MAC7B,CAAC;AAAA,IACF;AAAA,IACD,YAAY,SAAS;AACnBA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,eAAe,OAAO;AAAA,MAC7B,CAAC;AAAA,IACF;AAAA,IACD,gBAAgB,SAAS;AACvBA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,MACR,CAAC;AAAA,IACF;AAAA,IACD,iBAAiB;AACfA,oBAAAA,MAAI,YAAY;AAAA,QACd,OAAO;AAAA,MACT,CAAC;AAGD,iBAAW,MAAM;AACfA,sBAAG,MAAC,YAAW;AAIf,aAAK,gBAAgB;AAErBA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACR,CAAC;AAAA,MACF,GAAE,GAAI;AAAA,IACR;AAAA,IACD,gBAAgB;AACdA,oBAAAA,MAAI,YAAY;AAAA,QACd,OAAO;AAAA,MACT,CAAC;AAGD,iBAAW,MAAM;AACfA,sBAAG,MAAC,YAAW;AAEfA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACR,CAAC;AAAA,MACF,GAAE,GAAI;AAAA,IACR;AAAA,IACD,kBAAkB;AAChB,WAAK,YAAY,CAAC,KAAK;AACvB,UAAI,CAAC,KAAK,WAAW;AACnB,aAAK,iBAAiB;MACxB;AAAA,IACD;AAAA,IACD,qBAAqB,SAAS;AAC5B,YAAM,QAAQ,KAAK,eAAe,QAAQ,OAAO;AACjD,UAAI,UAAU,IAAI;AAChB,aAAK,eAAe,KAAK,OAAO;AAAA,aAC3B;AACL,aAAK,eAAe,OAAO,OAAO,CAAC;AAAA,MACrC;AAAA,IACD;AAAA,IACD,cAAc;AACZ,UAAI,KAAK,eAAe,WAAW,GAAG;AACpCA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACR,CAAC;AACD;AAAA,MACF;AAEAA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,MACR,CAAC;AAAA,IACF;AAAA,IACD,aAAa;AACX,UAAI,KAAK,eAAe,WAAW,GAAG;AACpCA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACR,CAAC;AACD;AAAA,MACF;AAEAA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,MACR,CAAC;AAAA,IACF;AAAA,IACD,eAAe;AACb,UAAI,KAAK,eAAe,WAAW,GAAG;AACpCA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACR,CAAC;AACD;AAAA,MACF;AAEAA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,SAAS,WAAW,KAAK,eAAe,MAAM;AAAA,QAC9C,SAAS,CAAC,QAAQ;AAChB,cAAI,IAAI,SAAS;AACfA,0BAAAA,MAAI,UAAU;AAAA,cACZ,OAAO;AAAA,cACP,MAAM;AAAA,YACR,CAAC;AAAA,UACH;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACF;AAAA,IACD,eAAe,QAAQ;AACrB,YAAM,SAAS;AAAA,QACb,SAAS;AAAA,QACT,YAAY;AAAA,QACZ,WAAW;AAAA,QACX,WAAW;AAAA,QACX,WAAW;AAAA;AAEb,aAAO,OAAO,MAAM,KAAK;AAAA,IAC1B;AAAA,IACD,cAAc,QAAQ;AACpB,YAAM,QAAQ;AAAA,QACZ,SAAS;AAAA,QACT,YAAY;AAAA,QACZ,WAAW;AAAA,QACX,WAAW;AAAA,QACX,WAAW;AAAA;AAEb,aAAO,MAAM,MAAM,KAAK;AAAA,IAC1B;AAAA,EACF;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACzaA,GAAG,WAAW,eAAe;"}