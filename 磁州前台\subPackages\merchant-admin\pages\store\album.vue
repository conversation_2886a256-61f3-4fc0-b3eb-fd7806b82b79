<template>
  <view class="store-album-container">
    <!-- 顶部导航栏 -->
    <view class="header-area">
      <!-- 顶部安全区域 -->
      <view class="safe-area-top"></view>
      
      <!-- 自定义导航栏 -->
      <view class="custom-navbar">
        <view class="navbar-left" @click="goBack">
          <image src="/static/images/tabbar/最新返回键.png" class="back-icon" style="filter: brightness(0) invert(1);"></image>
        </view>
        <view class="navbar-title">店铺相册</view>
        <view class="navbar-right">
          <!-- 占位元素保持导航栏平衡 -->
        </view>
      </view>
    </view>
    
    <!-- 背景装饰 -->
    <view class="bg-decoration bg-circle-1"></view>
    <view class="bg-decoration bg-circle-2"></view>
    <view class="bg-decoration bg-circle-3"></view>
    
    <!-- 内容区域 -->
    <scroll-view class="content-scroll" scroll-y>
      <!-- 相册类别管理 -->
      <view class="album-section">
        <view class="section-header">
          <text class="section-title">相册类别</text>
          <view class="section-action" @click="showAddAlbumModal">
            <text class="action-icon">+</text>
            <text class="action-text">新建相册</text>
          </view>
        </view>
        
        <scroll-view class="album-categories" scroll-x show-scrollbar="false">
          <view class="category-tabs">
            <view 
              class="category-tab" 
              :class="{ active: currentCategory === -1 }"
              @click="switchCategory(-1)"
            >
              全部相册
              <text class="photo-count">({{getTotalPhotoCount()}})</text>
            </view>
            <view 
              class="category-tab" 
              :class="{ active: currentCategory === index }"
              v-for="(category, index) in albumCategories" 
              :key="index"
              @click="switchCategory(index)"
            >
              {{category.name}}
              <text class="photo-count">({{category.photos.length}})</text>
            </view>
          </view>
        </scroll-view>
      </view>
      
      <!-- 照片列表 -->
      <view class="photo-section">
        <view class="section-header">
          <text class="section-title">照片管理</text>
          <view class="section-action" @click="uploadPhotos">
            <text class="action-icon">+</text>
            <text class="action-text">上传照片</text>
          </view>
        </view>
        
        <view class="photo-grid" v-if="currentPhotos.length > 0">
          <view class="photo-item" v-for="(photo, index) in currentPhotos" :key="index">
            <image :src="photo.url" mode="aspectFill" class="photo-image" @click="previewPhoto(photo)"></image>
            <view class="photo-actions">
              <view class="action-btn delete-btn" @click="deletePhoto(photo)">
                <text class="action-text">删除</text>
              </view>
              <view class="action-btn cover-btn" @click="setCover(photo)" v-if="currentCategory !== -1">
                <text class="action-text">设为封面</text>
              </view>
            </view>
          </view>
        </view>
        
        <!-- 空状态 -->
        <view class="empty-state" v-else>
          <image class="empty-icon" src="/static/images/tabbar/无数据.png"></image>
          <text class="empty-text">暂无照片，快去添加吧</text>
        </view>
      </view>
      
      <!-- 底部安全区域 -->
      <view class="safe-area-bottom"></view>
    </scroll-view>
    
    <!-- 新建相册弹窗 -->
    <view class="modal-overlay" v-if="showModal" @click="hideModal"></view>
    <view class="album-modal" v-if="showModal">
      <view class="modal-header">
        <text class="modal-title">新建相册</text>
      </view>
      <view class="modal-content">
        <view class="form-item">
          <text class="form-label required">相册名称</text>
          <input type="text" class="form-input" v-model="newAlbumName" placeholder="请输入相册名称" maxlength="10" />
          <text class="input-counter">{{newAlbumName.length}}/10</text>
        </view>
        
        <view class="form-item">
          <text class="form-label">相册描述</text>
          <textarea class="form-textarea" v-model="newAlbumDesc" placeholder="请输入相册描述（选填）" maxlength="50" />
          <text class="input-counter">{{newAlbumDesc.length}}/50</text>
        </view>
      </view>
      <view class="modal-footer">
        <button class="modal-btn cancel" @click="hideModal">取消</button>
        <button class="modal-btn confirm" @click="createAlbum" :disabled="!newAlbumName.trim()">确认</button>
      </view>
    </view>
    
    <!-- 删除确认弹窗 -->
    <view class="modal-overlay" v-if="showDeleteModal"></view>
    <view class="delete-modal" v-if="showDeleteModal">
      <view class="modal-header">
        <text class="modal-title">删除照片</text>
      </view>
      <view class="modal-content">
        <view class="confirm-message">确定要删除这张照片吗？</view>
        <view class="confirm-warning">删除后无法恢复，请谨慎操作</view>
      </view>
      <view class="modal-footer">
        <button class="modal-btn cancel" @click="cancelDelete">取消</button>
        <button class="modal-btn confirm delete" @click="confirmDelete">删除</button>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      albumCategories: [
        {
          id: 1,
          name: '店内环境',
          cover: '/static/images/album-1.jpg',
          photos: [
            { id: 101, url: '/static/images/album-1.jpg', name: '店铺外观', uploadTime: '2023-10-15' },
            { id: 102, url: '/static/images/album-2.jpg', name: '前台区域', uploadTime: '2023-10-15' },
            { id: 103, url: '/static/images/album-3.jpg', name: '用餐区域', uploadTime: '2023-10-15' }
          ]
        },
        {
          id: 2,
          name: '招牌美食',
          cover: '/static/images/album-4.jpg',
          photos: [
            { id: 201, url: '/static/images/album-4.jpg', name: '特色小酥肉', uploadTime: '2023-10-15' },
            { id: 202, url: '/static/images/album-5.jpg', name: '农家小炒肉', uploadTime: '2023-10-15' }
          ]
        },
        {
          id: 3,
          name: '员工风采',
          cover: '/static/images/album-6.jpg',
          photos: [
            { id: 301, url: '/static/images/album-6.jpg', name: '团队合影', uploadTime: '2023-10-15' }
          ]
        }
      ],
      currentCategory: -1,
      showModal: false,
      newAlbumName: '',
      newAlbumDesc: '',
      showDeleteModal: false,
      photoToDelete: null,
      statusBarHeight: 20
    }
  },
  computed: {
    // 当前显示的照片列表
    currentPhotos() {
      if (this.currentCategory === -1) {
        // 全部照片
        const allPhotos = [];
        this.albumCategories.forEach(category => {
          category.photos.forEach(photo => {
            allPhotos.push({
              ...photo,
              categoryId: category.id,
              categoryName: category.name
            });
          });
        });
        return allPhotos;
      } else {
        // 特定分类的照片
        return this.albumCategories[this.currentCategory].photos;
      }
    }
  },
  onLoad() {
    // 页面加载完成后的处理
    this.setStatusBarHeight();
  },
  methods: {
    // 设置状态栏高度
    setStatusBarHeight() {
      // 获取系统信息设置状态栏高度
      uni.getSystemInfo({
        success: (res) => {
          this.statusBarHeight = res.statusBarHeight;
          // 将状态栏高度设置为CSS变量
          if (typeof document !== 'undefined') {
            document.documentElement.style.setProperty('--status-bar-height', `${this.statusBarHeight}px`);
          }
        }
      });
    },
    
    // 返回上一页
    goBack() {
      uni.navigateBack();
    },
    
    // 获取所有照片总数
    getTotalPhotoCount() {
      let count = 0;
      this.albumCategories.forEach(category => {
        count += category.photos.length;
      });
      return count;
    },
    
    // 切换分类
    switchCategory(index) {
      this.currentCategory = index;
    },
    
    // 显示添加相册弹窗
    showAddAlbumModal() {
      this.showModal = true;
      this.newAlbumName = '';
      this.newAlbumDesc = '';
    },
    
    // 隐藏弹窗
    hideModal() {
      this.showModal = false;
    },
    
    // 创建相册
    createAlbum() {
      if (!this.newAlbumName.trim()) {
        uni.showToast({
          title: '请输入相册名称',
          icon: 'none'
        });
        return;
      }
      
      // 创建新相册
      const newAlbum = {
        id: Date.now(),
        name: this.newAlbumName,
        cover: '',
        photos: []
      };
      
      this.albumCategories.push(newAlbum);
      this.hideModal();
      
      uni.showToast({
        title: '相册创建成功',
        icon: 'success'
      });
    },
    
    // 上传照片
    uploadPhotos() {
      if (this.albumCategories.length === 0) {
        uni.showToast({
          title: '请先创建相册',
          icon: 'none'
        });
        return;
      }
      
      uni.chooseImage({
        count: 9,
        sizeType: ['original', 'compressed'],
        sourceType: ['album', 'camera'],
        success: (res) => {
          // 检查是否选择了特定相册
          let targetCategoryIndex = this.currentCategory;
          
          // 如果是"全部相册"视图，需要选择一个目标相册
          if (targetCategoryIndex === -1) {
            // 这里应该弹出选择相册的UI，但为了演示简化，我们使用第一个相册
            targetCategoryIndex = 0;
          }
          
          const targetCategory = this.albumCategories[targetCategoryIndex];
          
          // 添加照片到相册
          res.tempFilePaths.forEach((path, index) => {
            const newPhoto = {
              id: Date.now() + index,
              url: path,
              name: '新上传图片',
              uploadTime: new Date().toISOString().split('T')[0]
            };
            
            targetCategory.photos.push(newPhoto);
            
            // 如果相册没有封面，设置第一张照片为封面
            if (!targetCategory.cover && index === 0) {
              targetCategory.cover = path;
            }
          });
          
          uni.showToast({
            title: '上传成功',
            icon: 'success'
          });
        }
      });
    },
    
    // 预览照片
    previewPhoto(photo) {
      const currentCategoryPhotos = this.currentPhotos;
      const urls = currentCategoryPhotos.map(item => item.url);
      
      uni.previewImage({
        urls: urls,
        current: photo.url
      });
    },
    
    // 删除照片
    deletePhoto(photo) {
      this.photoToDelete = photo;
      this.showDeleteModal = true;
    },
    
    // 取消删除
    cancelDelete() {
      this.showDeleteModal = false;
      this.photoToDelete = null;
    },
    
    // 确认删除
    confirmDelete() {
      if (this.photoToDelete) {
        if (this.currentCategory === -1) {
          // 在全部照片视图中删除
          this.albumCategories.forEach(category => {
            const index = category.photos.findIndex(photo => photo.id === this.photoToDelete.id);
            if (index !== -1) {
              // 如果删除的是封面照片，需要更新封面
              if (category.cover === this.photoToDelete.url) {
                if (category.photos.length > 1) {
                  // 有其他照片，设置第一张不同的照片为封面
                  const otherPhotos = category.photos.filter(p => p.id !== this.photoToDelete.id);
                  category.cover = otherPhotos[0].url;
                } else {
                  // 没有其他照片了
                  category.cover = '';
                }
              }
              
              category.photos.splice(index, 1);
            }
          });
        } else {
          // 在特定相册中删除
          const category = this.albumCategories[this.currentCategory];
          const index = category.photos.findIndex(photo => photo.id === this.photoToDelete.id);
          
          if (index !== -1) {
            // 如果删除的是封面照片，需要更新封面
            if (category.cover === this.photoToDelete.url) {
              if (category.photos.length > 1) {
                // 有其他照片，设置第一张不同的照片为封面
                const otherPhotos = category.photos.filter(p => p.id !== this.photoToDelete.id);
                category.cover = otherPhotos[0].url;
              } else {
                // 没有其他照片了
                category.cover = '';
              }
            }
            
            category.photos.splice(index, 1);
          }
        }
        
        this.showDeleteModal = false;
        this.photoToDelete = null;
        
        uni.showToast({
          title: '删除成功',
          icon: 'success'
        });
      }
    },
    
    // 设置相册封面
    setCover(photo) {
      const category = this.albumCategories[this.currentCategory];
      category.cover = photo.url;
      
      uni.showToast({
        title: '已设为封面',
        icon: 'success'
      });
    }
  }
}
</script>

<style lang="scss">
.store-album-container {
  min-height: 100vh;
  background-color: #F5F8FC;
  position: relative;
  padding-top: calc(110rpx + var(--status-bar-height, 20px));
}

/* 顶部导航栏样式 */
.header-area {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  background-color: #0A84FF;
  color: #fff;
}

.safe-area-top {
  height: var(--status-bar-height, 20px);
  width: 100%;
}

.custom-navbar {
  height: 110rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 30rpx;
}

.navbar-title {
  font-size: 36rpx;
  font-weight: 600;
}

.navbar-left, .navbar-right {
  width: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-icon {
  width: 48rpx;
  height: 48rpx;
}

/* 背景装饰样式 */
.bg-decoration {
  position: absolute;
  z-index: -1;
  border-radius: 50%;
  opacity: 0.07;
  background-color: #0A84FF;
}

.bg-circle-1 {
  top: -100rpx;
  right: -150rpx;
  width: 400rpx;
  height: 400rpx;
}

.bg-circle-2 {
  top: 20%;
  left: -200rpx;
  width: 500rpx;
  height: 500rpx;
}

.bg-circle-3 {
  bottom: 10%;
  right: -100rpx;
  width: 300rpx;
  height: 300rpx;
}

/* 内容区域 */
.content-scroll {
  padding: 30rpx;
}

/* 相册类别管理 */
.album-section, .photo-section {
  background-color: #FFFFFF;
  border-radius: 24rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  position: relative;
  padding-left: 20rpx;
}

.section-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 6rpx;
  width: 8rpx;
  height: 32rpx;
  background-color: #0A84FF;
  border-radius: 4rpx;
}

.section-action {
  display: flex;
  align-items: center;
  color: #0A84FF;
}

.action-icon {
  font-size: 28rpx;
  margin-right: 6rpx;
}

.action-text {
  font-size: 28rpx;
}

/* 相册类别列表 */
.album-categories {
  white-space: nowrap;
  margin: 0 -30rpx;
  padding: 0 30rpx;
}

.category-tabs {
  display: flex;
  padding: 20rpx 0;
}

.category-tab {
  padding: 16rpx 30rpx;
  font-size: 28rpx;
  color: #666;
  position: relative;
  border-radius: 30rpx;
  margin-right: 20rpx;
  background-color: #F5F8FC;
  transition: all 0.3s;
  flex-shrink: 0;
}

.category-tab.active {
  color: #FFFFFF;
  background-color: #0A84FF;
  font-weight: 500;
}

.photo-count {
  font-size: 24rpx;
  margin-left: 6rpx;
}

/* 照片网格 */
.photo-grid {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -10rpx;
}

.photo-item {
  width: calc(33.33% - 20rpx);
  margin: 10rpx;
  border-radius: 12rpx;
  overflow: hidden;
  position: relative;
  padding-bottom: calc(33.33% - 20rpx); /* 保持1:1的宽高比 */
}

.photo-image {
  position: absolute;
  width: 100%;
  height: 100%;
  object-fit: cover;
  top: 0;
  left: 0;
}

.photo-actions {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(to top, rgba(0,0,0,0.7), transparent);
  display: flex;
  padding: 40rpx 10rpx 10rpx;
}

.action-btn {
  flex: 1;
  display: flex;
  justify-content: center;
  padding: 6rpx 0;
}

.action-btn .action-text {
  font-size: 22rpx;
  color: #FFFFFF;
}

/* 空状态 */
.empty-state {
  padding: 100rpx 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.empty-icon {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 30rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}

/* 底部安全区域 */
.safe-area-bottom {
  height: 100rpx;
}

/* 弹窗样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  z-index: 999;
}

.album-modal, .delete-modal {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 80%;
  background-color: #FFFFFF;
  border-radius: 24rpx;
  overflow: hidden;
  z-index: 1000;
  box-shadow: 0 20rpx 40rpx rgba(0, 0, 0, 0.2);
}

.modal-header {
  padding: 30rpx;
  text-align: center;
  border-bottom: 2rpx solid #F2F2F7;
}

.modal-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.modal-content {
  padding: 30rpx;
}

.form-item {
  margin-bottom: 30rpx;
  position: relative;
}

.form-label {
  display: block;
  font-size: 28rpx;
  color: #333;
  margin-bottom: 16rpx;
}

.required::after {
  content: '*';
  color: #FF3B30;
  margin-left: 6rpx;
}

.form-input {
  width: 100%;
  height: 88rpx;
  background-color: #F5F8FC;
  border-radius: 12rpx;
  padding: 0 24rpx;
  font-size: 28rpx;
  color: #333;
  box-sizing: border-box;
}

.form-textarea {
  width: 100%;
  height: 160rpx;
  background-color: #F5F8FC;
  border-radius: 12rpx;
  padding: 20rpx 24rpx;
  font-size: 28rpx;
  color: #333;
  box-sizing: border-box;
}

.input-counter {
  position: absolute;
  right: 24rpx;
  bottom: 10rpx;
  font-size: 24rpx;
  color: #999;
}

/* 确认删除 */
.confirm-message {
  font-size: 28rpx;
  color: #333;
  text-align: center;
  margin-bottom: 20rpx;
}

.confirm-warning {
  font-size: 24rpx;
  color: #FF3B30;
  text-align: center;
}

.modal-footer {
  display: flex;
  border-top: 2rpx solid #F2F2F7;
}

.modal-btn {
  flex: 1;
  height: 100rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  background-color: transparent;
}

.modal-btn::after {
  border: none;
}

.cancel {
  color: #999;
  border-right: 1px solid #F2F2F7;
}

.confirm {
  color: #0A84FF;
  font-weight: 500;
}

.delete {
  color: #FF3B30;
}

/* 适配暗黑模式 */
@media (prefers-color-scheme: dark) {
  .store-album-container {
    background-color: #1C1C1E;
  }
  
  .album-section, 
  .photo-section, 
  .album-modal, 
  .delete-modal {
    background-color: #2C2C2E;
  }
  
  .section-title, 
  .modal-title, 
  .form-label, 
  .confirm-message {
    color: #FFFFFF;
  }
  
  .category-tab {
    background-color: #3A3A3C;
    color: #8E8E93;
  }
  
  .form-input, 
  .form-textarea {
    background-color: #3A3A3C;
    color: #FFFFFF;
  }
  
  .modal-header, 
  .modal-footer {
    border-color: #3A3A3C;
  }
}
</style> 