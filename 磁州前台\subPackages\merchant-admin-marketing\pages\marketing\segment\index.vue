<!-- 客户群体细分页面开始 -->
<template>
  <view class="segment-page">
    <!-- 自定义导航栏 -->
    <view class="navbar">
      <view class="navbar-back" @click="goBack">
        <view class="back-icon"></view>
      </view>
      <text class="navbar-title">客户群体细分</text>
      <view class="navbar-right">
        <!-- 已移除帮助按钮 -->
      </view>
    </view>
    
    <view class="content">
      <view class="header-card">
        <view class="title">精准划分客户群体</view>
        <view class="description">对客户进行多维度分析与分类，实现更精准的营销触达</view>
      </view>
      
      <!-- 基本统计信息 -->
      <view class="stats-section">
        <view class="stats-card">
          <view class="stats-value">5</view>
          <view class="stats-label">已创建细分</view>
        </view>
        <view class="stats-card">
          <view class="stats-value">3,256</view>
          <view class="stats-label">覆盖用户数</view>
        </view>
        <view class="stats-card">
          <view class="stats-value">12.8%</view>
          <view class="stats-label">转化率</view>
        </view>
      </view>
      
      <!-- 占位内容 -->
      <view class="placeholder-content">
        <view class="placeholder-text">客户群体细分功能开发中</view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      
    }
  },
  methods: {
    goBack() {
      uni.navigateBack();
    },
    showHelp() {
      uni.showToast({
        title: '客户群体细分帮助',
        icon: 'none'
      });
    }
  }
}
</script>

<style lang="scss" scoped>
.segment-page {
  min-height: 100vh;
  background-color: #f5f6fa;
  
  .navbar {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 30rpx;
    height: 180rpx; /* 固定高度包含状态栏和关闭按钮, +20rpx (10px) */
    padding-top: var(--status-bar-height);
    background: #1989FA;
    position: relative;
    width: 100%;
    box-sizing: border-box;
    z-index: 999;
    
    .navbar-back {
      width: 60rpx;
      height: 60rpx;
      display: flex;
      align-items: center;
      
      .back-icon {
        width: 20rpx;
        height: 20rpx;
        border-top: 3rpx solid #ffffff;
        border-left: 3rpx solid #ffffff;
        transform: rotate(-45deg);
      }
    }
    
    .navbar-title {
      font-size: 36rpx;
      font-weight: 500;
      color: #ffffff;
      position: absolute;
      left: 50%;
      top: 50%;
      transform: translate(-50%, 30%); /* 向下调整以对齐关闭键, 适应更高的导航栏 */
      white-space: nowrap;
    }
    
    .navbar-right {
      width: 60rpx;
      display: flex;
      justify-content: flex-end;
      
      .help-icon {
        width: 40rpx;
        height: 40rpx;
        border-radius: 50%;
        border: 2rpx solid #ffffff;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #ffffff;
        font-size: 28rpx;
      }
    }
  }
  
  .content {
    padding: 30rpx;
    padding-top: 15rpx;
  }
  
  .header-card {
    background: linear-gradient(135deg, #1989FA 0%, #39AFFD 100%);
    border-radius: 20rpx;
    padding: 40rpx 30rpx;
    color: #ffffff;
    margin-bottom: 30rpx;
    box-shadow: 0 10rpx 20rpx rgba(25, 137, 250, 0.1);
    
    .title {
      font-size: 36rpx;
      font-weight: bold;
      margin-bottom: 10rpx;
    }
    
    .description {
      font-size: 26rpx;
      opacity: 0.8;
    }
  }
  
  .stats-section {
    display: flex;
    justify-content: space-between;
    margin-bottom: 30rpx;
    
    .stats-card {
      background-color: #ffffff;
      border-radius: 16rpx;
      padding: 20rpx;
      width: 30%;
      box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
      text-align: center;
      
      .stats-value {
        font-size: 32rpx;
        font-weight: bold;
        color: #333333;
        margin-bottom: 6rpx;
      }
      
      .stats-label {
        font-size: 24rpx;
        color: #999999;
      }
    }
  }
  
  .placeholder-content {
    background-color: #ffffff;
    border-radius: 16rpx;
    padding: 60rpx 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
    
    .placeholder-text {
      font-size: 30rpx;
      color: #999999;
    }
  }
}
</style>
<!-- 客户群体细分页面结束 -->
