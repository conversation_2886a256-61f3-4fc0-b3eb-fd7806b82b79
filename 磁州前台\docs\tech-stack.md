# 后端技术栈

## 核心框架
- **应用框架**: Spring Boot 3.x + Spring Cloud
- **数据访问**: MyBatis Plus + JPA
- **缓存**: Redis 7.x + Caffeine
- **搜索**: Elasticsearch 8.x
- **消息队列**: RabbitMQ + Kafka
- **任务调度**: XXL-Job

## 数据存储
- **主数据库**: MySQL 8.0 (主从复制)
- **缓存数据库**: Redis Cluster
- **搜索引擎**: Elasticsearch Cluster
- **文件存储**: MinIO + OSS
- **日志存储**: ELK Stack

## 基础设施
- **容器化**: Docker + Kubernetes
- **服务网格**: Istio
- **监控**: Prometheus + Grafana
- **链路追踪**: <PERSON><PERSON><PERSON>
- **配置中心**: Nacos
- **注册中心**: Nacos

## 开发工具
- **API文档**: Swagger 3.0
- **代码生成**: MyBatis Generator
- **测试框架**: JUnit 5 + Mockito
- **构建工具**: Maven 3.8+
- **版本控制**: Git + GitLab