/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 移除阿里妈妈字体引用，改用系统默认字体 */
/* 移除原有阿里妈妈字体定义
$uni-font-family: 'AlimamaShuHeiTi';
$uni-font-family-text: 'AlimamaShuHeiTi', -apple-system, BlinkMacSystemFont, 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;
*/
body, html, #app, .index-container {
  font-family: "AlimamaShuHeiTi", -apple-system, BlinkMacSystemFont, "Helvetica Neue", "PingFang SC", "Microsoft YaHei", sans-serif;
}
.detail-container {
  min-height: 100vh;
  background-color: #F5F7FA;
  padding-bottom: 30rpx;
}

/* 导航栏样式 */
.navbar {
  position: -webkit-sticky;
  position: sticky;
  top: 0;
  left: 0;
  right: 0;
  background: linear-gradient(135deg, #A764CA, #6B0FBE);
  color: #fff;
  padding: 88rpx 32rpx 30rpx;
  display: flex;
  align-items: center;
  z-index: 100;
  box-shadow: 0 4rpx 20rpx rgba(107, 15, 190, 0.15);
}
.navbar-back {
  width: 72rpx;
  height: 72rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.back-icon {
  width: 24rpx;
  height: 24rpx;
  border-left: 4rpx solid #fff;
  border-bottom: 4rpx solid #fff;
  transform: rotate(45deg);
}
.navbar-title {
  flex: 1;
  text-align: center;
  font-size: 36rpx;
  font-weight: 600;
  letter-spacing: 1rpx;
}
.navbar-right {
  width: 72rpx;
  height: 72rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.help-icon {
  width: 48rpx;
  height: 48rpx;
  border-radius: 24rpx;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  font-weight: bold;
}

/* 基本信息卡片 */
.info-card {
  margin: 30rpx;
  background: #FFFFFF;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}
.distributor-header {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
}
.avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 60rpx;
  margin-right: 30rpx;
  background-color: #f5f5f5;
}
.info-content {
  flex: 1;
}
.name-wrap {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  margin-bottom: 16rpx;
}
.name {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-right: 16rpx;
}
.level-tag {
  font-size: 24rpx;
  color: #fff;
  padding: 4rpx 12rpx;
  border-radius: 20rpx;
  margin-right: 16rpx;
}
.status-tag {
  font-size: 24rpx;
  color: #fff;
  padding: 4rpx 12rpx;
  border-radius: 20rpx;
}
.status-active {
  background-color: #67C23A;
}
.status-disabled {
  background-color: #909399;
}
.status-pending {
  background-color: #E6A23C;
}
.status-rejected {
  background-color: #F56C6C;
}
.phone {
  font-size: 28rpx;
  color: #666;
}
.info-grid {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -10rpx;
  padding-bottom: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}
.info-item {
  width: 50%;
  padding: 10rpx;
  box-sizing: border-box;
}
.item-label {
  font-size: 26rpx;
  color: #999;
  margin-bottom: 8rpx;
  display: block;
}
.item-value {
  font-size: 28rpx;
  color: #333;
}
.action-btns {
  display: flex;
  justify-content: center;
  margin-top: 30rpx;
}
.action-btn {
  padding: 16rpx 30rpx;
  border-radius: 40rpx;
  font-size: 28rpx;
  margin: 0 16rpx;
}
.action-btn.approve {
  background-color: #67C23A;
  color: #fff;
}
.action-btn.reject {
  background-color: #F56C6C;
  color: #fff;
}
.action-btn.disable {
  background-color: #909399;
  color: #fff;
}
.action-btn.enable {
  background-color: #409EFF;
  color: #fff;
}
.action-btn.set-level {
  background-color: #6B0FBE;
  color: #fff;
}

/* 数据概览 */
.stats-card {
  margin: 30rpx;
  background: #FFFFFF;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}
.card-header {
  margin-bottom: 20rpx;
}
.card-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}
.stats-grid {
  display: flex;
  flex-wrap: wrap;
}
.stats-item {
  width: 50%;
  padding: 20rpx 0;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.stats-value {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
}
.stats-label {
  font-size: 26rpx;
  color: #999;
}

/* 选项卡 */
.tabs {
  display: flex;
  background: #FFFFFF;
  margin: 30rpx;
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}
.tab-item {
  flex: 1;
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  color: #666;
  position: relative;
}
.tab-item.active {
  color: #6B0FBE;
  font-weight: 600;
}
.tab-item.active::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 40rpx;
  height: 4rpx;
  background-color: #6B0FBE;
  border-radius: 2rpx;
}

/* 内容区域 */
.tab-content {
  margin: 0 30rpx 30rpx;
}
.placeholder {
  height: 300rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #FFFFFF;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}
.placeholder text {
  font-size: 28rpx;
  color: #999;
}

/* 订单列表 */
.order-list {
  margin-bottom: 30rpx;
}
.order-item {
  background: #FFFFFF;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}
.order-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20rpx;
}
.order-id {
  font-size: 28rpx;
  color: #333;
}
.order-status {
  font-size: 28rpx;
  color: #6B0FBE;
}
.product-info {
  display: flex;
  margin-bottom: 20rpx;
}
.product-image {
  width: 120rpx;
  height: 120rpx;
  border-radius: 10rpx;
  margin-right: 20rpx;
  background-color: #f5f5f5;
}
.product-detail {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
.product-name {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 10rpx;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}
.product-meta {
  display: flex;
  justify-content: space-between;
}
.product-price {
  font-size: 28rpx;
  color: #FF5722;
}
.product-quantity {
  font-size: 28rpx;
  color: #999;
}
.order-footer {
  padding-top: 20rpx;
  border-top: 1rpx solid #f0f0f0;
}
.order-time {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 10rpx;
}
.order-amount {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 10rpx;
}
.amount {
  font-size: 28rpx;
  color: #333;
  font-weight: 600;
}
.commission-info {
  font-size: 26rpx;
  color: #666;
  display: flex;
  align-items: center;
}
.commission {
  font-size: 28rpx;
  color: #FF5722;
  font-weight: 600;
  margin-right: 10rpx;
}
.commission-status {
  font-size: 24rpx;
  color: #fff;
  padding: 4rpx 12rpx;
  border-radius: 20rpx;
}
.status-paid {
  background-color: #67C23A;
}
.status-pending {
  background-color: #E6A23C;
}
.status-frozen {
  background-color: #409EFF;
}
.status-cancelled {
  background-color: #F56C6C;
}

/* 团队成员列表 */
.team-list {
  margin-bottom: 30rpx;
}
.team-item {
  background: #FFFFFF;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}
.member-info {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}
.member-avatar {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50rpx;
  margin-right: 20rpx;
  background-color: #f5f5f5;
}
.member-detail {
  flex: 1;
}
.member-name-wrap {
  display: flex;
  align-items: center;
  margin-bottom: 8rpx;
}
.member-name {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-right: 16rpx;
}
.member-level {
  font-size: 22rpx;
  color: #fff;
  padding: 4rpx 12rpx;
  border-radius: 20rpx;
}
.member-phone {
  font-size: 24rpx;
  color: #999;
}
.member-stats {
  display: flex;
  justify-content: space-between;
  padding: 20rpx 0;
  border-top: 1rpx solid #f0f0f0;
  border-bottom: 1rpx solid #f0f0f0;
  margin-bottom: 20rpx;
}
.member-stat {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.stat-label {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 8rpx;
}
.stat-value {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
}
.member-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.join-time {
  font-size: 24rpx;
  color: #999;
}
.arrow-right {
  width: 16rpx;
  height: 16rpx;
  border-top: 2rpx solid #999;
  border-right: 2rpx solid #999;
  transform: rotate(45deg);
}

/* 佣金记录列表 */
.commission-list {
  margin-bottom: 30rpx;
}
.commission-item {
  background: #FFFFFF;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}
.commission-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}
.commission-type {
  font-size: 24rpx;
  color: #fff;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
}
.type-order {
  background-color: #409EFF;
}
.type-withdraw {
  background-color: #67C23A;
}
.type-refund {
  background-color: #F56C6C;
}
.type-adjust {
  background-color: #E6A23C;
}
.type-other {
  background-color: #909399;
}
.commission-amount {
  font-size: 32rpx;
  font-weight: 600;
}
.commission-amount.income {
  color: #67C23A;
}
.commission-amount.expense {
  color: #F56C6C;
}
.commission-content {
  padding: 20rpx 0;
  border-top: 1rpx solid #f0f0f0;
  border-bottom: 1rpx solid #f0f0f0;
  margin-bottom: 20rpx;
}
.commission-desc {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 10rpx;
  display: block;
}
.commission-order {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 10rpx;
  display: block;
}
.commission-time {
  font-size: 24rpx;
  color: #999;
}
.commission-footer {
  display: flex;
  justify-content: flex-end;
}
.commission-status {
  font-size: 24rpx;
  color: #fff;
  padding: 4rpx 12rpx;
  border-radius: 20rpx;
}

/* 提现记录列表 */
.withdraw-list {
  margin-bottom: 30rpx;
}
.withdraw-item {
  background: #FFFFFF;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}
.withdraw-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20rpx;
}
.withdraw-id {
  font-size: 28rpx;
  color: #333;
}
.withdraw-status {
  font-size: 24rpx;
  color: #fff;
  padding: 4rpx 12rpx;
  border-radius: 20rpx;
}
.status-pending {
  background-color: #E6A23C;
}
.status-approved {
  background-color: #409EFF;
}
.status-rejected {
  background-color: #F56C6C;
}
.status-processing {
  background-color: #909399;
}
.status-completed {
  background-color: #67C23A;
}
.status-failed {
  background-color: #F56C6C;
}
.withdraw-content {
  padding: 20rpx 0;
  border-top: 1rpx solid #f0f0f0;
  border-bottom: 1rpx solid #f0f0f0;
  margin-bottom: 20rpx;
}
.withdraw-info {
  display: flex;
  justify-content: space-between;
  margin-bottom: 16rpx;
}
.withdraw-info:last-child {
  margin-bottom: 0;
}
.withdraw-label {
  font-size: 26rpx;
  color: #999;
}
.withdraw-amount,
.withdraw-fee,
.withdraw-actual {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
}
.withdraw-method,
.withdraw-account,
.withdraw-time,
.withdraw-remark {
  font-size: 26rpx;
  color: #666;
  max-width: 70%;
  text-align: right;
}
.withdraw-footer {
  display: flex;
  justify-content: flex-end;
}
.withdraw-btn {
  font-size: 24rpx;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  margin-left: 16rpx;
}
.withdraw-btn.approve {
  background-color: #67C23A;
  color: #fff;
}
.withdraw-btn.reject {
  background-color: #F56C6C;
  color: #fff;
}