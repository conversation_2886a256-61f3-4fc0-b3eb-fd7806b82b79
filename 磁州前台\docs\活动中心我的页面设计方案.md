# 活动中心"我的活动"页面设计方案

## 设计背景与定位

活动中心"我的活动"页面是同城信息平台中活动展示系统的子模块，专注于用户的活动参与体验。与平台主"我的"页面有明确的功能边界：

- **功能定位**：专注于活动参与、管理和数据展示，不涉及平台级的用户设置、钱包和分销系统
- **入口位置**：从平台主"我的"页面中的"活动中心"服务入口进入
- **返回机制**：页面顶部提供明确的返回按钮，随时可回到平台主"我的"页面
- **数据共享**：与平台主系统共享用户基础信息，但维护独立的活动相关数据

## 系统集成与路径规划

### 页面路径设计
- **页面路径**：`/subPackages/activity-showcase/pages/my/index`（需要创建该文件）
- **导航方式**：从平台主"我的"页面（`/pages/my/my.vue`）点击"活动中心"服务项进入活动中心首页，再通过底部标签栏进入"我的活动"页面
- **页面注册**：在`/subPackages/activity-showcase/pages.json`中已注册，但需要实现页面内容

### 与现有系统的关系
- **与主"我的"页面**：作为功能补充，不替代主"我的"页面的核心功能
- **与分销系统**：活动分享数据符合条件时会同步到分销系统，但不直接管理分销功能
- **与钱包系统**：活动消费和返利数据同步到钱包系统，但支付操作由钱包系统处理

## 设计风格与视觉语言

### 整体风格定位
- **高级感**：采用磁州生活网活动中心发现页面的粉红色系(#FF3B69至#FF7A9E)，结合磨砂玻璃效果
- **层次感**：多层卡片叠加，通过Z轴高度差异创造空间感
- **阴影感**：精细调校的阴影效果，模拟自然光源，营造立体感
- **立体感**：微妙的3D变换和倾斜效果，增强交互体验
- **苹果风格**：简约克制的设计语言，圆润的图标，流畅的动效
- **卡片设计**：所有卡片四角采用35度圆角，保持统一视觉语言

### 色彩系统
- **主色调**：与活动中心发现页面保持一致，使用粉红色系渐变(#FF3B69至#FF7A9E)
- **强调色**：活跃橙色(#FF9500)用于次要行动点
- **中性色**：纯净白色(#FFFFFF)背景搭配不同深度的灰色(#F2F2F7, #E5E5EA, #C7C7CC)
- **功能色**：成功绿(#34C759)、警示红(#FF3B30)、信息粉(#FF3B69)

### 排版系统
- **字体层级**：清晰的4级标题系统，搭配2种正文样式
- **字体选择**：iOS风格无衬线字体，保持良好可读性
- **行高与间距**：黄金比例排版，确保舒适阅读体验

## 核心设计理念
- 活动聚焦：所有功能和数据展示都围绕用户的活动参与体验
- 数据可视化：通过精美图表展示用户活动参与数据
- 无缝衔接：与平台其他模块保持一致的设计语言和交互方式
- 高级交互：采用微动效和手势操作提升用户体验

## 页面结构与视觉实现

### 1. 顶部导航与用户活动概览
- **高级导航栏**
  - 磨砂玻璃效果背景(backdrop-filter: blur(10px))
  - 渐变背景色(#FF3B69至#FF7A9E)，与发现页面保持一致
  - 返回按钮采用细线条SVG图标，白色描边
  - 标题使用SF Pro Display字体，18pt，白色，半粗体
  - 导航栏投下微妙阴影(box-shadow: 0 4px 6px rgba(255,59,105,0.15))

- **活动参与概览卡片**
  - 纯白卡片底色(#FFFFFF)，四角35度圆角(border-radius: 35px)
  - 卡片阴影效果(box-shadow: 0 8px 20px rgba(255,59,105,0.15))
  - 卡片内部采用网格布局，数据点间微妙分隔线
  - 活动类型分布采用粉红色系渐变色填充饼图
  - 环比增长数据配以微妙上升/下降动画
  - 卡片微弱的3D变换效果(transform: perspective(1000px) rotateX(2deg))

### 2. 活动管理中心
- **标签页设计**
  - 标签背景为半透明白色(rgba(255,255,255,0.9))
  - 活跃标签下方有3px粗细的粉红色指示条，带弹性动画
  - 标签切换时有平滑过渡动画
  - 标签文字采用SF Pro Text字体，15pt，深灰至粉红色渐变
  - 标签类型：进行中、已报名、已完成、已收藏

- **活动卡片设计**
  - 白色背景卡片，四角35度圆角
  - 多层次阴影效果：
    - 主阴影(box-shadow: 0 10px 30px rgba(0,0,0,0.08))
    - 环境光阴影(box-shadow: 0 0 0 1px rgba(0,0,0,0.02))
  - 卡片内图片区域采用8px圆角
  - 卡片悬停/点击状态有精细的缩放反馈(transform: scale(0.98))
  - 内容布局采用8pt网格系统，保持统一间距
  - 卡片右侧微妙倾斜(transform: rotate(0.5deg))增强立体感

### 3. 活动社交中心
- **拼团进度卡片**
  - 浅粉色背景(#FFF0F5)与深粉色强调(#FF3B69)
  - 进度条采用渐变填充效果
  - 头像采用重叠布局，带有2px白色描边
  - 微妙的脉冲动画提示实时更新
  - 卡片右上角45度折角效果，增强立体感

- **活动好友面板**
  - 磨砂玻璃效果背景(backdrop-filter: blur(8px))
  - 好友头像采用投影设计，增强层次感
  - 滑动时的视差效果，背景移动速度慢于前景
  - 好友活动动态采用时间轴设计，左侧彩色时间线

### 4. 活动数据与分析
- **数据可视化卡片**
  - 纯白背景，四角35度圆角
  - 图表采用粉红色系渐变填充，柔和阴影
  - 交互式数据点，点击展开详情浮层
  - 图表加载时的优雅动画效果
  - 卡片内微妙的光泽效果(background: linear-gradient(135deg, #fff 0%, #f9f9f9 100%))

- **日历视图设计**
  - 日期网格采用微妙的3D效果
  - 有活动的日期使用粉红色圆点标记
  - 当前日期带有脉冲动画效果
  - 滑动切换月份时的3D翻页效果
  - 日历底部有柔和阴影，增强浮动感

### 5. 活动工具箱
- **工具图标设计**
  - 采用线性SVG图标，2px描边
  - 图标背景为柔和渐变色圆形
  - 点击时的水波纹扩散效果
  - 图标周围有微妙光晕，增强立体感
  - 工具名称使用SF Pro Text字体，13pt，深灰色

- **返回主页面按钮**
  - 半透明粉红色背景(rgba(255,59,105,0.9))
  - 白色文字，SF Pro Text字体，15pt，中等粗细
  - 按钮四角35度圆角，与整体设计保持一致
  - 点击时的缩放反馈(transform: scale(0.95))
  - 柔和阴影效果(box-shadow: 0 5px 15px rgba(255,59,105,0.3))

## 交互设计与微动效

### 精细动效系统
- **页面转场**：优雅的推入/推出效果，配合透明度渐变
- **卡片交互**：轻触时的缩放与阴影变化，增强触感反馈
- **滚动效果**：基于滚动位置的视差效果，增强深度感
- **加载状态**：精心设计的骨架屏与脉冲加载动画
- **数据更新**：数字变化时的滚动效果，而非直接跳变

### 手势交互系统
- **下拉刷新**：自定义下拉指示器，带有弹性动效
- **左右滑动**：标签页间的平滑切换，带有动量效应
- **长按预览**：长按活动卡片弹出预览浮层，带有模糊背景
- **双指缩放**：图表区域支持缩放查看详情

### 状态反馈系统
- **触摸状态**：所有可交互元素有明确的按下状态
- **加载状态**：优雅的加载动画，避免空白等待
- **成功/失败**：操作结果的动画反馈，如分享成功的庆祝动效
- **空状态**：精心设计的空状态插图，引导用户采取行动

## 技术实现亮点

### 1. 高级UI组件
```vue
<ActivityStatusCard 
  :activity="item"
  :showCountdown="true"
  :enableGestures="true"
  :cardStyle="{
    borderRadius: '35px',
    boxShadow: '0 10px 30px rgba(0,0,0,0.08), 0 0 0 1px rgba(0,0,0,0.02)',
    background: 'linear-gradient(135deg, #fff 0%, #f9f9f9 100%)'
  }"
  @action="handleCardAction"
/>
```

```vue
<ActivityDataChart
  :chartType="'radar'"
  :userData="userActivityData"
  :averageData="averageActivityData"
  :theme="{
    gradient: true,
    colors: ['#FF3B69', '#FF7A9E'],
    shadow: true,
    animation: true
  }"
/>
```

### 2. 智能交互
- 下拉刷新（带有精美动画）
- 左右滑动切换标签页
- 卡片滑动操作（左滑分享，右滑收藏）
- 长按预览活动详情

### 3. 性能优化
- 虚拟列表渲染（处理大量活动数据）
- 图片懒加载与预加载
- 数据缓存策略
- 页面状态保持

### 4. 动效设计
- 微交互动效（按钮点击、切换状态）
- 页面转场动画
- 数据加载骨架屏
- 活动状态变更动画

## 代码实现示例

```vue
<template>
  <view class="activity-my-container">
    <!-- 自定义导航栏 - 强调返回到平台主"我的"页面 -->
    <view class="custom-navbar" :style="{ 
      backdropFilter: 'blur(10px)', 
      background: 'linear-gradient(135deg, #FF3B69 0%, #FF7A9E 100%)',
      boxShadow: '0 4px 6px rgba(255,59,105,0.15)'
    }">
      <view class="navbar-content">
        <view class="back-btn" @click="backToMainMyPage">
          <svg class="icon" viewBox="0 0 24 24" width="24" height="24">
            <path d="M19 12H5M12 19l-7-7 7-7" stroke="#FFFFFF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
          </svg>
        </view>
        <view class="navbar-title">我的活动</view>
        <view class="navbar-right">
          <view class="filter-btn" @click="showActivityFilter">
            <svg class="icon" viewBox="0 0 24 24" width="24" height="24">
              <path d="M22 3H2l8 9.46V19l4 2v-8.54L22 3z" stroke="#FFFFFF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
            </svg>
          </view>
        </view>
      </view>
    </view>

    <!-- 内容区域 -->
    <scroll-view 
      class="content-scroll" 
      scroll-y 
      refresher-enabled
      :refresher-triggered="isRefreshing"
      @refresherrefresh="onRefresh"
      @scrolltolower="loadMore"
    >
      <!-- 活动参与概览卡片 -->
      <ActivityOverviewCard 
        :activityStats="activityStats"
        :showChart="true"
        :cardStyle="{
          borderRadius: '35px',
          boxShadow: '0 8px 20px rgba(255,59,105,0.15)',
          background: '#FFFFFF',
          transform: 'perspective(1000px) rotateX(2deg)'
        }"
      />

      <!-- 活动管理标签页 -->
      <view class="tabs-container" :style="{
        background: 'rgba(255,255,255,0.9)',
        borderRadius: '35px 35px 0 0'
      }">
        <view 
          v-for="(tab, index) in tabs" 
          :key="index"
          class="tab-item"
          :class="{ active: currentTab === index }"
          @click="switchTab(index)"
        >
          <text class="tab-text">{{ tab.name }}</text>
          <view class="tab-indicator" v-if="currentTab === index" :style="{
            background: 'linear-gradient(90deg, #FF3B69 0%, #FF7A9E 100%)'
          }"></view>
        </view>
      </view>

      <!-- 活动列表区域 -->
      <swiper class="activities-swiper" :current="currentTab" @change="onSwiperChange">
        <swiper-item v-for="(tab, tabIndex) in tabs" :key="tabIndex">
          <scroll-view class="tab-content" scroll-y>
            <view class="activity-list">
              <ActivityStatusCard 
                v-for="(activity, index) in getActivitiesByStatus(tab.status)"
                :key="activity.id"
                :activity="activity"
                :showCountdown="tab.status === 'ongoing'"
                :cardStyle="{
                  borderRadius: '35px',
                  boxShadow: '0 10px 30px rgba(0,0,0,0.08), 0 0 0 1px rgba(0,0,0,0.02)',
                  background: 'linear-gradient(135deg, #fff 0%, #f9f9f9 100%)',
                  transform: index % 2 === 0 ? 'rotate(0.5deg)' : 'rotate(-0.5deg)'
                }"
                @click="viewActivityDetail(activity)"
                @share="shareActivity(activity)"
                @cancel="cancelActivity(activity)"
              />
              
              <!-- 空状态 -->
              <view class="empty-state" v-if="getActivitiesByStatus(tab.status).length === 0">
                <image class="empty-image" :src="tab.emptyImage"></image>
                <text class="empty-text">{{ tab.emptyText }}</text>
                <view class="action-btn" @click="navigateTo('/subPackages/activity-showcase/pages/index/index')" :style="{
                  background: 'linear-gradient(135deg, #FF3B69 0%, #FF7A9E 100%)',
                  borderRadius: '35px',
                  boxShadow: '0 5px 15px rgba(255,59,105,0.3)'
                }">
                  <text>{{ tab.actionText }}</text>
                </view>
              </view>
            </view>
          </scroll-view>
        </swiper-item>
      </swiper>

      <!-- 活动社交中心 -->
      <ActivitySocialPanel 
        :groupBuyActivities="groupBuyActivities"
        :activityFriends="activityFriends"
        :panelStyle="{
          borderRadius: '35px',
          boxShadow: '0 10px 30px rgba(0,0,0,0.08)',
          background: '#FFF0F5'
        }"
        @invite="inviteFriend"
      />

      <!-- 活动数据分析 -->
      <ActivityAnalysisPanel 
        :analysisData="activityAnalysisData"
        :chartStyle="{
          borderRadius: '35px',
          boxShadow: '0 8px 20px rgba(255,59,105,0.15)',
          background: 'linear-gradient(135deg, #fff 0%, #f9f9f9 100%)'
        }"
        @viewDetail="navigateTo('/subPackages/activity-showcase/pages/analysis/detail')"
      />

      <!-- 活动日历视图 -->
      <ActivityCalendarPanel 
        :activityEvents="activityEvents"
        :calendarStyle="{
          borderRadius: '35px',
          boxShadow: '0 10px 30px rgba(0,0,0,0.08)',
          background: '#FFFFFF'
        }"
        @selectDate="viewDateActivities"
        @viewCalendar="navigateTo('/subPackages/activity-showcase/pages/calendar')"
      />
      
      <!-- 活动工具箱 -->
      <view class="activity-toolbox" :style="{
        borderRadius: '35px',
        boxShadow: '0 8px 20px rgba(0,0,0,0.08)',
        background: 'linear-gradient(135deg, #fff 0%, #f9f9f9 100%)'
      }">
        <view class="toolbox-header">
          <text class="toolbox-title">活动工具</text>
        </view>
        <view class="tool-grid">
          <view class="tool-item" @click="navigateTo('/subPackages/activity-showcase/pages/reminders')">
            <view class="tool-icon reminder-icon" :style="{
              background: 'linear-gradient(135deg, #FF9500 0%, #FF3B30 100%)',
              borderRadius: '50%',
              boxShadow: '0 5px 15px rgba(255,59,48,0.2)'
            }">
              <svg class="icon" viewBox="0 0 24 24" width="24" height="24">
                <path d="M18 8A6 6 0 0 0 6 8c0 7-3 9-3 9h18s-3-2-3-9" stroke="#FFFFFF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
                <path d="M13.73 21a2 2 0 0 1-3.46 0" stroke="#FFFFFF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
              </svg>
            </view>
            <text class="tool-name">活动提醒</text>
          </view>
          
          <view class="tool-item" @click="navigateTo('/subPackages/activity-showcase/pages/share-records')">
            <view class="tool-icon share-icon" :style="{
              background: 'linear-gradient(135deg, #FF3B69 0%, #FF7A9E 100%)',
              borderRadius: '50%',
              boxShadow: '0 5px 15px rgba(255,59,105,0.2)'
            }">
              <svg class="icon" viewBox="0 0 24 24" width="24" height="24">
                <circle cx="18" cy="5" r="3" stroke="#FFFFFF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></circle>
                <circle cx="6" cy="12" r="3" stroke="#FFFFFF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></circle>
                <circle cx="18" cy="19" r="3" stroke="#FFFFFF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></circle>
                <line x1="8.59" y1="13.51" x2="15.42" y2="17.49" stroke="#FFFFFF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></line>
                <line x1="15.41" y1="6.51" x2="8.59" y2="10.49" stroke="#FFFFFF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></line>
              </svg>
            </view>
            <text class="tool-name">分享工具</text>
          </view>
          
          <view class="tool-item" @click="navigateTo('/subPackages/activity-showcase/pages/favorites')">
            <view class="tool-icon favorite-icon" :style="{
              background: 'linear-gradient(135deg, #FF9500 0%, #FFCC00 100%)',
              borderRadius: '50%',
              boxShadow: '0 5px 15px rgba(255,149,0,0.2)'
            }">
              <svg class="icon" viewBox="0 0 24 24" width="24" height="24">
                <path d="M19 21l-7-5-7 5V5a2 2 0 0 1 2-2h10a2 2 0 0 1 2 2z" stroke="#FFFFFF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
              </svg>
            </view>
            <text class="tool-name">收藏夹</text>
          </view>
          
          <view class="tool-item" @click="navigateToMainSettings">
            <view class="tool-icon help-icon" :style="{
              background: 'linear-gradient(135deg, #34C759 0%, #30D158 100%)',
              borderRadius: '50%',
              boxShadow: '0 5px 15px rgba(52,199,89,0.2)'
            }">
              <svg class="icon" viewBox="0 0 24 24" width="24" height="24">
                <circle cx="12" cy="12" r="10" stroke="#FFFFFF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></circle>
                <path d="M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3" stroke="#FFFFFF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
                <line x1="12" y1="17" x2="12.01" y2="17" stroke="#FFFFFF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></line>
              </svg>
            </view>
            <text class="tool-name">帮助</text>
          </view>
        </view>
      </view>
      
      <!-- 返回主我的页面按钮 -->
      <view class="back-to-main-btn" @click="backToMainMyPage" :style="{
        background: 'rgba(255,59,105,0.9)',
        borderRadius: '35px',
        boxShadow: '0 5px 15px rgba(255,59,105,0.3)'
      }">
        <text>返回个人中心</text>
      </view>
      
      <!-- 底部安全区域 -->
      <view class="safe-area-bottom"></view>
    </scroll-view>
  </view>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';

// 返回主"我的"页面
const backToMainMyPage = () => {
  uni.navigateBack();
};

// 导航到平台主设置页面
const navigateToMainSettings = () => {
  uni.navigateTo({
    url: '/pages/my/settings'
  });
};

// 标签页数据
const tabs = ref([
  { name: '进行中', status: 'ongoing', emptyText: '暂无进行中的活动', actionText: '去参与活动', emptyImage: '/static/images/empty/empty-ongoing.png' },
  { name: '已报名', status: 'registered', emptyText: '暂无已报名的活动', actionText: '去浏览活动', emptyImage: '/static/images/empty/empty-registered.png' },
  { name: '已完成', status: 'completed', emptyText: '暂无已完成的活动', actionText: '去参与活动', emptyImage: '/static/images/empty/empty-completed.png' },
  { name: '已收藏', status: 'favorite', emptyText: '暂无收藏的活动', actionText: '去浏览活动', emptyImage: '/static/images/empty/empty-favorite.png' }
]);

// 当前选中的标签页
const currentTab = ref(0);

// 其他代码...
</script>
```

## 实施路径与建议

### 实施步骤
1. **创建页面文件**：在`/subPackages/activity-showcase/pages/my`目录下创建`index.vue`
2. **实现页面组件**：按设计方案实现页面UI和交互
3. **数据集成**：接入活动数据API，实现数据展示和交互
4. **导航集成**：确保从主"我的"页面可正确导航至本页面
5. **测试与优化**：进行兼容性测试和性能优化

### 需要创建的组件
1. **ActivityOverviewCard**：活动概览卡片组件
2. **ActivityStatusCard**：活动状态卡片组件
3. **ActivitySocialPanel**：活动社交面板组件
4. **ActivityAnalysisPanel**：活动数据分析面板组件
5. **ActivityCalendarPanel**：活动日历面板组件

### 与其他系统的集成点
1. **用户系统**：获取用户基本信息
2. **分销系统**：分享活动时可选择是否通过分销系统分享
3. **钱包系统**：活动支付和返利跳转到钱包系统处理
4. **消息系统**：活动通知与平台消息系统集成

通过以上设计，活动中心"我的活动"页面专注于提供优质的活动参与体验，同时与平台主"我的"页面形成明确的功能边界和无缝的数据衔接，避免功能重叠，提升整体用户体验。