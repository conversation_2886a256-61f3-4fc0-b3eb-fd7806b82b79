<template>
  <view class="records-container">
    <!-- 自定义导航栏 -->
    <view class="navbar">
      <view class="navbar-back" @tap="goBack">
        <view class="back-icon"></view>
      </view>
      <text class="navbar-title">领取记录</text>
      <view class="navbar-right">
        <view class="more-icon" @tap="showMoreOptions">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <circle cx="12" cy="12" r="1"></circle>
            <circle cx="19" cy="12" r="1"></circle>
            <circle cx="5" cy="12" r="1"></circle>
          </svg>
        </view>
      </view>
    </view>
    
    <!-- 筛选条件 -->
    <view class="filter-section">
      <view class="filter-tabs">
        <view 
          class="filter-tab" 
          :class="{ active: activeTab === 'all' }"
          @tap="switchTab('all')"
        >
          全部
        </view>
        <view 
          class="filter-tab" 
          :class="{ active: activeTab === 'unused' }"
          @tap="switchTab('unused')"
        >
          未使用
        </view>
        <view 
          class="filter-tab" 
          :class="{ active: activeTab === 'used' }"
          @tap="switchTab('used')"
        >
          已使用
        </view>
        <view 
          class="filter-tab" 
          :class="{ active: activeTab === 'expired' }"
          @tap="switchTab('expired')"
        >
          已过期
        </view>
      </view>
      
      <view class="search-filter">
        <view class="search-box">
          <text class="search-icon">🔍</text>
          <input 
            type="text" 
            class="search-input" 
            placeholder="搜索用户名/手机号" 
            v-model="searchKeyword"
            @confirm="searchRecords"
          />
          <text class="clear-icon" v-if="searchKeyword" @tap="clearSearch">×</text>
        </view>
        
        <view class="filter-button" @tap="openFilterPopup">
          <text class="filter-text">筛选</text>
          <text class="filter-icon">⌄</text>
        </view>
      </view>
      
      <view class="date-filter" v-if="showDateFilter">
        <view class="date-range">
          <view class="date-input" @tap="showStartDatePicker">
            <text class="date-label">开始日期:</text>
            <text class="date-value">{{ startDate || '请选择' }}</text>
          </view>
          <text class="date-separator">至</text>
          <view class="date-input" @tap="showEndDatePicker">
            <text class="date-label">结束日期:</text>
            <text class="date-value">{{ endDate || '请选择' }}</text>
          </view>
        </view>
        <view class="date-actions">
          <button class="date-reset" @tap="resetDateFilter">重置</button>
          <button class="date-confirm" @tap="applyDateFilter">确定</button>
        </view>
      </view>
    </view>
    
    <!-- 统计信息 -->
    <view class="stats-section">
      <view class="stats-item">
        <text class="stats-value">{{ totalRecords }}</text>
        <text class="stats-label">总领取</text>
      </view>
      <view class="stats-item">
        <text class="stats-value">{{ usedCount }}</text>
        <text class="stats-label">已使用</text>
      </view>
      <view class="stats-item">
        <text class="stats-value">{{ unusedCount }}</text>
        <text class="stats-label">未使用</text>
      </view>
      <view class="stats-item">
        <text class="stats-value">{{ expiredCount }}</text>
        <text class="stats-label">已过期</text>
      </view>
    </view>
    
    <!-- 记录列表 -->
    <view class="records-list" v-if="records.length > 0">
      <view class="record-item" v-for="(record, index) in records" :key="index">
        <view class="record-header">
          <view class="user-info">
            <image class="user-avatar" :src="record.userAvatar" mode="aspectFill"></image>
            <view class="user-details">
              <text class="user-name">{{ record.userName }}</text>
              <text class="user-phone">{{ maskPhone(record.userPhone) }}</text>
            </view>
          </view>
          <view class="record-status" :class="getStatusClass(record.status)">
            {{ getStatusText(record.status) }}
          </view>
        </view>
        
        <view class="record-body">
          <view class="coupon-info">
            <text class="coupon-title">{{ record.couponTitle }}</text>
            <view class="coupon-value">
              <text class="value-symbol">¥</text>
              <text class="value-amount">{{ record.couponValue }}</text>
              <text class="value-condition">满{{ record.minSpend }}元可用</text>
            </view>
          </view>
          
          <view class="record-times">
            <view class="time-item">
              <text class="time-label">领取时间：</text>
              <text class="time-value">{{ record.claimTime }}</text>
            </view>
            <view class="time-item" v-if="record.useTime">
              <text class="time-label">使用时间：</text>
              <text class="time-value">{{ record.useTime }}</text>
            </view>
            <view class="time-item">
              <text class="time-label">有效期至：</text>
              <text class="time-value">{{ record.expireTime }}</text>
            </view>
          </view>
        </view>
        
        <view class="record-footer">
          <view class="record-source">
            <text class="source-label">来源：</text>
            <text class="source-value">{{ record.source }}</text>
          </view>
          <view class="record-actions">
            <button class="action-button detail" @tap="viewUserDetail(record)">用户详情</button>
            <button class="action-button message" @tap="sendMessage(record)">发送消息</button>
          </view>
        </view>
      </view>
      
      <!-- 加载更多 -->
      <view class="load-more" v-if="hasMoreData">
        <text class="load-text" @tap="loadMoreRecords">加载更多</text>
      </view>
      <view class="load-more" v-else>
        <text class="load-end">没有更多数据了</text>
      </view>
    </view>
    
    <!-- 空状态 -->
    <view class="empty-state" v-else>
      <image class="empty-image" src="/static/images/empty-records.png" mode="aspectFit"></image>
      <text class="empty-text">暂无领取记录</text>
      <text class="empty-tip">可尝试调整筛选条件</text>
    </view>
    
    <!-- 筛选弹窗 -->
    <view class="filter-popup" v-if="showFilterPopup">
      <view class="popup-mask" @tap="hideFilterPopup"></view>
      <view class="popup-content">
        <view class="popup-header">
          <text class="popup-title">筛选条件</text>
          <view class="popup-close" @tap="hideFilterPopup">×</view>
        </view>
        
        <view class="filter-options">
          <view class="filter-group">
            <text class="group-title">时间范围</text>
            <view class="time-options">
              <view 
                class="time-option" 
                :class="{ active: timeRange === 'today' }"
                @tap="selectTimeRange('today')"
              >今日</view>
              <view 
                class="time-option" 
                :class="{ active: timeRange === 'yesterday' }"
                @tap="selectTimeRange('yesterday')"
              >昨日</view>
              <view 
                class="time-option" 
                :class="{ active: timeRange === 'thisWeek' }"
                @tap="selectTimeRange('thisWeek')"
              >本周</view>
              <view 
                class="time-option" 
                :class="{ active: timeRange === 'thisMonth' }"
                @tap="selectTimeRange('thisMonth')"
              >本月</view>
              <view 
                class="time-option" 
                :class="{ active: timeRange === 'custom' }"
                @tap="selectTimeRange('custom')"
              >自定义</view>
            </view>
          </view>
          
          <view class="filter-group">
            <text class="group-title">领取渠道</text>
            <view class="channel-options">
              <view 
                class="channel-option" 
                :class="{ active: selectedChannels.includes('all') }"
                @tap="toggleChannel('all')"
              >全部渠道</view>
              <view 
                class="channel-option" 
                :class="{ active: selectedChannels.includes('scan') }"
                @tap="toggleChannel('scan')"
              >扫码领取</view>
              <view 
                class="channel-option" 
                :class="{ active: selectedChannels.includes('share') }"
                @tap="toggleChannel('share')"
              >分享领取</view>
              <view 
                class="channel-option" 
                :class="{ active: selectedChannels.includes('activity') }"
                @tap="toggleChannel('activity')"
              >活动领取</view>
              <view 
                class="channel-option" 
                :class="{ active: selectedChannels.includes('manual') }"
                @tap="toggleChannel('manual')"
              >手动发放</view>
            </view>
          </view>
          
          <view class="filter-group">
            <text class="group-title">排序方式</text>
            <view class="sort-options">
              <view 
                class="sort-option" 
                :class="{ active: sortBy === 'timeDesc' }"
                @tap="setSortOption('timeDesc')"
              >时间降序</view>
              <view 
                class="sort-option" 
                :class="{ active: sortBy === 'timeAsc' }"
                @tap="setSortOption('timeAsc')"
              >时间升序</view>
            </view>
          </view>
        </view>
        
        <view class="filter-actions">
          <button class="action-reset" @tap="resetFilters">重置</button>
          <button class="action-confirm" @tap="applyFilters">确定</button>
        </view>
      </view>
    </view>
    
    <!-- 日期选择器 -->
    <uni-calendar 
      v-if="showDatePicker"
      :insert="false"
      :start-date="'2020-01-01'"
      :end-date="getCurrentDate()"
      @confirm="onDateSelect"
      @close="closeDatePicker"
    />
  </view>
</template>

<script>
import { ref, reactive, computed, onMounted } from 'vue';

export default {
  setup() {
    // 状态变量
    const couponId = ref('');
    const activeTab = ref('all');
    const searchKeyword = ref('');
    const startDate = ref('');
    const endDate = ref('');
    const showDateFilter = ref(false);
    const showFilterPopup = ref(false);
    const showDatePicker = ref(false);
    const currentDatePicker = ref(''); // 'start' or 'end'
    const timeRange = ref('thisMonth');
    const selectedChannels = ref(['all']);
    const sortBy = ref('timeDesc');
    const currentPage = ref(1);
    const pageSize = ref(10);
    const hasMoreData = ref(true);
    
    // 记录数据
    const records = ref([
      {
        id: 1,
        userName: '张三',
        userAvatar: '/static/images/avatar-1.jpg',
        userPhone: '13812345678',
        couponTitle: '新客专享优惠',
        couponValue: 10,
        minSpend: 100,
        claimTime: '2023-04-20 14:30:25',
        useTime: '2023-04-22 18:45:12',
        expireTime: '2023-05-20 23:59:59',
        status: 'used',
        source: '扫码领取'
      },
      {
        id: 2,
        userName: '李四',
        userAvatar: '/static/images/avatar-2.jpg',
        userPhone: '13987654321',
        couponTitle: '新客专享优惠',
        couponValue: 10,
        minSpend: 100,
        claimTime: '2023-04-20 15:45:36',
        useTime: '',
        expireTime: '2023-05-20 23:59:59',
        status: 'unused',
        source: '分享领取'
      },
      {
        id: 3,
        userName: '王五',
        userAvatar: '/static/images/avatar-3.jpg',
        userPhone: '13765432198',
        couponTitle: '新客专享优惠',
        couponValue: 10,
        minSpend: 100,
        claimTime: '2023-04-21 09:20:48',
        useTime: '',
        expireTime: '2023-05-20 23:59:59',
        status: 'unused',
        source: '活动领取'
      },
      {
        id: 4,
        userName: '赵六',
        userAvatar: '/static/images/avatar-4.jpg',
        userPhone: '13698765432',
        couponTitle: '新客专享优惠',
        couponValue: 10,
        minSpend: 100,
        claimTime: '2023-03-15 11:30:22',
        useTime: '',
        expireTime: '2023-04-15 23:59:59',
        status: 'expired',
        source: '手动发放'
      },
      {
        id: 5,
        userName: '钱七',
        userAvatar: '/static/images/avatar-5.jpg',
        userPhone: '13567891234',
        couponTitle: '新客专享优惠',
        couponValue: 10,
        minSpend: 100,
        claimTime: '2023-04-22 16:40:15',
        useTime: '2023-04-23 12:30:45',
        expireTime: '2023-05-22 23:59:59',
        status: 'used',
        source: '扫码领取'
      }
    ]);
    
    // 计算属性
    const totalRecords = computed(() => records.value.length);
    const usedCount = computed(() => records.value.filter(r => r.status === 'used').length);
    const unusedCount = computed(() => records.value.filter(r => r.status === 'unused').length);
    const expiredCount = computed(() => records.value.filter(r => r.status === 'expired').length);
    
    // 方法
    function goBack() {
      uni.navigateBack();
    }
    
    function showMoreOptions() {
      uni.showActionSheet({
        itemList: ['导出数据', '刷新'],
        success: (res) => {
          switch(res.tapIndex) {
            case 0:
              exportData();
              break;
            case 1:
              refreshRecords();
              break;
          }
        }
      });
    }
    
    function exportData() {
      uni.showLoading({
        title: '导出中...'
      });
      
      setTimeout(() => {
        uni.hideLoading();
        uni.showToast({
          title: '导出成功',
          icon: 'success'
        });
      }, 1500);
    }
    
    function refreshRecords() {
      uni.showLoading({
        title: '刷新中...'
      });
      
      // 模拟刷新数据
      setTimeout(() => {
        uni.hideLoading();
        uni.showToast({
          title: '刷新成功',
          icon: 'success'
        });
      }, 800);
    }
    
    function switchTab(tab) {
      activeTab.value = tab;
      // 根据选项卡筛选数据
      loadRecords();
    }
    
    function searchRecords() {
      if (!searchKeyword.value.trim()) {
        return;
      }
      
      // 执行搜索
      uni.showLoading({
        title: '搜索中...'
      });
      
      // 模拟搜索
      setTimeout(() => {
        uni.hideLoading();
      }, 500);
    }
    
    function clearSearch() {
      searchKeyword.value = '';
      // 重置搜索结果
      loadRecords();
    }
    
    function openFilterPopup() {
      showFilterPopup.value = true;
    }
    
    function hideFilterPopup() {
      showFilterPopup.value = false;
    }
    
    function selectTimeRange(range) {
      timeRange.value = range;
      if (range === 'custom') {
        showDateFilter.value = true;
      } else {
        showDateFilter.value = false;
        // 根据选择的时间范围设置开始和结束日期
        setDateRangeByTimeRange(range);
      }
    }
    
    function setDateRangeByTimeRange(range) {
      const now = new Date();
      let start = new Date();
      let end = new Date();
      
      switch(range) {
        case 'today':
          start.setHours(0, 0, 0, 0);
          break;
        case 'yesterday':
          start.setDate(now.getDate() - 1);
          start.setHours(0, 0, 0, 0);
          end.setDate(now.getDate() - 1);
          end.setHours(23, 59, 59, 999);
          break;
        case 'thisWeek':
          const dayOfWeek = now.getDay() || 7; // 如果是0（周日）则设为7
          start.setDate(now.getDate() - dayOfWeek + 1);
          start.setHours(0, 0, 0, 0);
          break;
        case 'thisMonth':
          start.setDate(1);
          start.setHours(0, 0, 0, 0);
          break;
        default:
          // 默认不设置日期范围
          startDate.value = '';
          endDate.value = '';
          return;
      }
      
      startDate.value = formatDate(start);
      endDate.value = formatDate(end);
    }
    
    function formatDate(date) {
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      return `${year}-${month}-${day}`;
    }
    
    function showStartDatePicker() {
      currentDatePicker.value = 'start';
      showDatePicker.value = true;
    }
    
    function showEndDatePicker() {
      currentDatePicker.value = 'end';
      showDatePicker.value = true;
    }
    
    function onDateSelect(e) {
      const selectedDate = e.fulldate;
      if (currentDatePicker.value === 'start') {
        startDate.value = selectedDate;
      } else {
        endDate.value = selectedDate;
      }
      closeDatePicker();
    }
    
    function closeDatePicker() {
      showDatePicker.value = false;
    }
    
    function resetDateFilter() {
      startDate.value = '';
      endDate.value = '';
    }
    
    function applyDateFilter() {
      showDateFilter.value = false;
      // 应用日期筛选
      loadRecords();
    }
    
    function toggleChannel(channel) {
      if (channel === 'all') {
        if (selectedChannels.value.includes('all')) {
          selectedChannels.value = [];
        } else {
          selectedChannels.value = ['all'];
        }
      } else {
        // 如果选择了具体渠道，移除"全部"选项
        const allIndex = selectedChannels.value.indexOf('all');
        if (allIndex !== -1) {
          selectedChannels.value.splice(allIndex, 1);
        }
        
        const index = selectedChannels.value.indexOf(channel);
        if (index === -1) {
          selectedChannels.value.push(channel);
        } else {
          selectedChannels.value.splice(index, 1);
        }
        
        // 如果没有选择任何渠道，默认选择"全部"
        if (selectedChannels.value.length === 0) {
          selectedChannels.value = ['all'];
        }
      }
    }
    
    function setSortOption(sort) {
      sortBy.value = sort;
    }
    
    function resetFilters() {
      timeRange.value = 'thisMonth';
      selectedChannels.value = ['all'];
      sortBy.value = 'timeDesc';
      startDate.value = '';
      endDate.value = '';
      showDateFilter.value = false;
    }
    
    function applyFilters() {
      hideFilterPopup();
      // 应用筛选条件
      loadRecords();
    }
    
    function loadRecords() {
      // 重置分页
      currentPage.value = 1;
      hasMoreData.value = true;
      
      // 模拟加载数据
      uni.showLoading({
        title: '加载中...'
      });
      
      // 这里应该根据筛选条件调用API获取数据
      setTimeout(() => {
        uni.hideLoading();
      }, 500);
    }
    
    function loadMoreRecords() {
      if (!hasMoreData.value) return;
      
      currentPage.value += 1;
      
      uni.showLoading({
        title: '加载中...'
      });
      
      // 模拟加载更多数据
      setTimeout(() => {
        uni.hideLoading();
        
        // 假设没有更多数据了
        if (currentPage.value >= 3) {
          hasMoreData.value = false;
        }
      }, 500);
    }
    
    function maskPhone(phone) {
      if (!phone) return '';
      return phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2');
    }
    
    function getStatusClass(status) {
      switch(status) {
        case 'used':
          return 'status-used';
        case 'unused':
          return 'status-unused';
        case 'expired':
          return 'status-expired';
        default:
          return '';
      }
    }
    
    function getStatusText(status) {
      switch(status) {
        case 'used':
          return '已使用';
        case 'unused':
          return '未使用';
        case 'expired':
          return '已过期';
        default:
          return '未知状态';
      }
    }
    
    function viewUserDetail(record) {
      uni.showToast({
        title: `查看用户：${record.userName}`,
        icon: 'none'
      });
    }
    
    function sendMessage(record) {
      uni.showToast({
        title: `发送消息给：${record.userName}`,
        icon: 'none'
      });
    }
    
    function getCurrentDate() {
      const now = new Date();
      return formatDate(now);
    }
    
    function loadCouponRecords(id) {
      couponId.value = id;
      
      // 模拟加载数据
      uni.showLoading({
        title: '加载中...'
      });
      
      setTimeout(() => {
        uni.hideLoading();
      }, 500);
    }
    
    onMounted(() => {
      const pages = getCurrentPages();
      const currentPage = pages[pages.length - 1];
      const options = currentPage.$page?.options || {};
      
      if (options.id) {
        loadCouponRecords(options.id);
      }
    });
    
    return {
      activeTab,
      searchKeyword,
      startDate,
      endDate,
      showDateFilter,
      showFilterPopup,
      showDatePicker,
      timeRange,
      selectedChannels,
      sortBy,
      records,
      totalRecords,
      usedCount,
      unusedCount,
      expiredCount,
      hasMoreData,
      
      goBack,
      showMoreOptions,
      switchTab,
      searchRecords,
      clearSearch,
      showFilterPopup,
      hideFilterPopup,
      selectTimeRange,
      showStartDatePicker,
      showEndDatePicker,
      onDateSelect,
      closeDatePicker,
      resetDateFilter,
      applyDateFilter,
      toggleChannel,
      setSortOption,
      resetFilters,
      applyFilters,
      loadMoreRecords,
      maskPhone,
      getStatusClass,
      getStatusText,
      viewUserDetail,
      sendMessage,
      getCurrentDate
    };
  }
}
</script>

<style lang="scss">
.records-container {
  min-height: 100vh;
  background-color: #F5F7FA;
}

/* 导航栏样式 */
.navbar {
  position: sticky;
  top: 0;
  left: 0;
  right: 0;
  background: linear-gradient(135deg, #FF9966, #FF5E62);
  color: #fff;
  padding: 44px 16px 15px;
  display: flex;
  align-items: center;
  z-index: 100;
  box-shadow: 0 2px 10px rgba(255, 94, 98, 0.15);
}

.navbar-back {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-icon {
  width: 12px;
  height: 12px;
  border-left: 2px solid #fff;
  border-bottom: 2px solid #fff;
  transform: rotate(45deg);
}

.navbar-title {
  flex: 1;
  text-align: center;
  font-size: 18px;
  font-weight: 600;
  letter-spacing: 0.5px;
}

.navbar-right {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.more-icon {
  width: 24px;
  height: 24px;
  color: #fff;
}

/* 筛选条件样式 */
.filter-section {
  background: #fff;
  padding: 15px;
  margin-bottom: 10px;
  box-shadow: 0 1px 5px rgba(0, 0, 0, 0.05);
}

.filter-tabs {
  display: flex;
  border-bottom: 1px solid #f0f0f0;
  padding-bottom: 10px;
  margin-bottom: 15px;
}

.filter-tab {
  flex: 1;
  text-align: center;
  font-size: 14px;
  color: #666;
  padding: 8px 0;
  position: relative;
}

.filter-tab.active {
  color: #FF5E62;
  font-weight: 600;
}

.filter-tab.active::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 20px;
  height: 3px;
  background: #FF5E62;
  border-radius: 3px;
}

.search-filter {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.search-box {
  flex: 1;
  display: flex;
  align-items: center;
  background: #f5f5f5;
  border-radius: 20px;
  padding: 8px 15px;
  margin-right: 10px;
}

.search-icon {
  font-size: 16px;
  color: #999;
  margin-right: 5px;
}

.search-input {
  flex: 1;
  font-size: 14px;
  color: #333;
  height: 20px;
  line-height: 20px;
}

.clear-icon {
  font-size: 18px;
  color: #999;
  padding: 0 5px;
}

.filter-button {
  display: flex;
  align-items: center;
  background: #f5f5f5;
  border-radius: 20px;
  padding: 8px 15px;
}

.filter-text {
  font-size: 14px;
  color: #666;
}

.filter-icon {
  font-size: 14px;
  color: #666;
  margin-left: 3px;
}

.date-filter {
  background: #f9f9f9;
  border-radius: 8px;
  padding: 15px;
  margin-top: 10px;
}

.date-range {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}

.date-input {
  flex: 1;
  background: #fff;
  border: 1px solid #eee;
  border-radius: 6px;
  padding: 8px 12px;
  display: flex;
  flex-direction: column;
}

.date-separator {
  margin: 0 10px;
  color: #999;
}

.date-label {
  font-size: 12px;
  color: #999;
  margin-bottom: 3px;
}

.date-value {
  font-size: 14px;
  color: #333;
}

.date-actions {
  display: flex;
  justify-content: flex-end;
}

.date-reset {
  background: #f5f5f5;
  border: none;
  border-radius: 6px;
  color: #666;
  font-size: 14px;
  padding: 6px 15px;
  margin-right: 10px;
}

.date-confirm {
  background: #FF5E62;
  border: none;
  border-radius: 6px;
  color: #fff;
  font-size: 14px;
  padding: 6px 15px;
}

/* 统计信息样式 */
.stats-section {
  display: flex;
  background: #fff;
  padding: 15px;
  margin-bottom: 10px;
  box-shadow: 0 1px 5px rgba(0, 0, 0, 0.05);
}

.stats-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.stats-value {
  font-size: 18px;
  font-weight: bold;
  color: #FF5E62;
  margin-bottom: 5px;
}

.stats-label {
  font-size: 12px;
  color: #999;
}

/* 记录列表样式 */
.records-list {
  padding: 10px 15px;
}

.record-item {
  background: #fff;
  border-radius: 10px;
  padding: 15px;
  margin-bottom: 15px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.record-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.user-info {
  display: flex;
  align-items: center;
}

.user-avatar {
  width: 40px;
  height: 40px;
  border-radius: 20px;
  margin-right: 10px;
}

.user-details {
  display: flex;
  flex-direction: column;
}

.user-name {
  font-size: 15px;
  font-weight: 600;
  color: #333;
  margin-bottom: 3px;
}

.user-phone {
  font-size: 12px;
  color: #999;
}

.record-status {
  padding: 4px 10px;
  border-radius: 12px;
  font-size: 12px;
  color: white;
}

.status-used {
  background: #34C759;
}

.status-unused {
  background: #FF9500;
}

.status-expired {
  background: #8E8E93;
}

.record-body {
  border-top: 1px dashed #eee;
  border-bottom: 1px dashed #eee;
  padding: 15px 0;
  margin-bottom: 15px;
}

.coupon-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.coupon-title {
  font-size: 15px;
  color: #333;
  font-weight: 500;
}

.coupon-value {
  display: flex;
  align-items: baseline;
}

.value-symbol {
  font-size: 14px;
  color: #FF5E62;
}

.value-amount {
  font-size: 20px;
  font-weight: bold;
  color: #FF5E62;
  margin: 0 2px;
}

.value-condition {
  font-size: 12px;
  color: #999;
}

.record-times {
  
}

.time-item {
  display: flex;
  margin-bottom: 8px;
}

.time-item:last-child {
  margin-bottom: 0;
}

.time-label {
  font-size: 13px;
  color: #999;
  width: 80px;
}

.time-value {
  font-size: 13px;
  color: #333;
  flex: 1;
}

.record-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.record-source {
  display: flex;
  align-items: center;
}

.source-label {
  font-size: 13px;
  color: #999;
}

.source-value {
  font-size: 13px;
  color: #333;
}

.record-actions {
  display: flex;
}

.action-button {
  background: none;
  border: 1px solid #ddd;
  border-radius: 15px;
  font-size: 12px;
  padding: 4px 10px;
  margin-left: 10px;
}

.action-button.detail {
  color: #007AFF;
  border-color: #007AFF;
}

.action-button.message {
  color: #FF5E62;
  border-color: #FF5E62;
}

/* 加载更多样式 */
.load-more {
  text-align: center;
  padding: 15px 0;
}

.load-text {
  font-size: 14px;
  color: #007AFF;
}

.load-end {
  font-size: 14px;
  color: #999;
}

/* 空状态样式 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 0;
}

.empty-image {
  width: 120px;
  height: 120px;
  margin-bottom: 15px;
}

.empty-text {
  font-size: 16px;
  color: #666;
  margin-bottom: 8px;
}

.empty-tip {
  font-size: 14px;
  color: #999;
}

/* 筛选弹窗样式 */
.filter-popup {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 999;
}

.popup-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
}

.popup-content {
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #fff;
  border-top-left-radius: 16px;
  border-top-right-radius: 16px;
  padding: 15px;
  max-height: 70vh;
  overflow-y: auto;
}

.popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.popup-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.popup-close {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  color: #999;
}

.filter-options {
  margin-bottom: 20px;
}

.filter-group {
  margin-bottom: 20px;
}

.group-title {
  font-size: 15px;
  color: #333;
  font-weight: 500;
  margin-bottom: 12px;
}

.time-options, .channel-options {
  display: flex;
  flex-wrap: wrap;
}

.time-option, .channel-option, .sort-option {
  background: #f5f5f5;
  border-radius: 20px;
  padding: 8px 15px;
  margin-right: 10px;
  margin-bottom: 10px;
  font-size: 13px;
  color: #666;
}

.time-option.active, .channel-option.active, .sort-option.active {
  background: rgba(255, 94, 98, 0.1);
  color: #FF5E62;
}

.sort-options {
  display: flex;
}

.filter-actions {
  display: flex;
  padding: 15px 0;
}

.action-reset {
  flex: 1;
  background: #f5f5f5;
  border: none;
  border-radius: 25px;
  color: #666;
  font-size: 15px;
  padding: 10px;
  margin-right: 15px;
}

.action-confirm {
  flex: 1;
  background: linear-gradient(135deg, #FF9966, #FF5E62);
  border: none;
  border-radius: 25px;
  color: #fff;
  font-size: 15px;
  padding: 10px;
}
</style>