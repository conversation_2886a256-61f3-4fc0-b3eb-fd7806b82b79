{"version": 3, "file": "index.js", "sources": ["mock/news/index.js"], "sourcesContent": ["// 导出新闻模块所有API\r\nexport { fetchNewsDetail, fetchMoreComments } from './newsDetail';\r\nexport { fetchNewsList } from './newsList';\r\n\r\n// 导出新闻分类\r\nexport const newsCategories = [\r\n  { id: 0, name: '全部' },\r\n  { id: 1, name: '政务资讯' },\r\n  { id: 2, name: '便民信息' },\r\n  { id: 3, name: '活动通知' },\r\n  { id: 4, name: '招聘信息' },\r\n  { id: 5, name: '房产资讯' }\r\n]; "], "names": [], "mappings": ";AAKY,MAAC,iBAAiB;AAAA,EAC5B,EAAE,IAAI,GAAG,MAAM,KAAM;AAAA,EACrB,EAAE,IAAI,GAAG,MAAM,OAAQ;AAAA,EACvB,EAAE,IAAI,GAAG,MAAM,OAAQ;AAAA,EACvB,EAAE,IAAI,GAAG,MAAM,OAAQ;AAAA,EACvB,EAAE,IAAI,GAAG,MAAM,OAAQ;AAAA,EACvB,EAAE,IAAI,GAAG,MAAM,OAAQ;AACzB;;"}