"use strict";
const common_vendor = require("../../common/vendor.js");
const common_assets = require("../../common/assets.js");
const _sfc_main = {
  data() {
    return {
      statusBarHeight: 20,
      currentTab: 0,
      tabs: [
        { name: "全部" },
        { name: "待付款" },
        { name: "待发货" },
        { name: "待收货" },
        { name: "已完成" }
      ],
      ordersList: [],
      page: 1,
      limit: 10,
      loading: false,
      refreshing: false,
      loadAll: false
    };
  },
  onLoad() {
    const sysInfo = common_vendor.index.getSystemInfoSync();
    this.statusBarHeight = sysInfo.statusBarHeight;
    this.loadOrders();
  },
  methods: {
    // 返回上一页
    goBack() {
      common_vendor.index.navigateBack();
    },
    // 切换选项卡
    switchTab(index) {
      if (this.currentTab !== index) {
        this.currentTab = index;
        this.refresh();
      }
    },
    // 获取状态文字
    getStatusText(status) {
      switch (status) {
        case 1:
          return "待付款";
        case 2:
          return "待发货";
        case 3:
          return "待收货";
        case 4:
          return "已完成";
        case 5:
          return "已取消";
        default:
          return "未知状态";
      }
    },
    // 加载订单数据
    loadOrders() {
      if (this.loading || this.loadAll)
        return;
      this.loading = true;
      setTimeout(() => {
        ({
          page: this.page,
          limit: this.limit,
          status: this.currentTab === 0 ? "" : this.currentTab
        });
        const mockData = this.getMockOrdersData();
        let filteredData = mockData;
        if (this.currentTab > 0) {
          filteredData = mockData.filter((item) => item.status === this.currentTab);
        }
        if (this.page === 1) {
          this.ordersList = filteredData;
        } else {
          this.ordersList = [...this.ordersList, ...filteredData];
        }
        this.loadAll = true;
        this.loading = false;
        this.refreshing = false;
        if (!this.loadAll) {
          this.page++;
        }
      }, 800);
    },
    // 刷新
    refresh() {
      this.refreshing = true;
      this.page = 1;
      this.loadAll = false;
      this.loadOrders();
    },
    // 加载更多
    loadMore() {
      this.loadOrders();
    },
    // 查看订单详情
    viewOrderDetail(order) {
      common_vendor.index.navigateTo({
        url: `/pages/groupbuy/order-detail?id=${order.id}`
      });
    },
    // 取消订单
    cancelOrder(order) {
      common_vendor.index.showModal({
        title: "提示",
        content: "确定要取消该订单吗？",
        success: (res) => {
          if (res.confirm) {
            common_vendor.index.showLoading({
              title: "处理中..."
            });
            setTimeout(() => {
              common_vendor.index.hideLoading();
              const index = this.ordersList.findIndex((item) => item.id === order.id);
              if (index !== -1) {
                this.ordersList[index].status = 5;
              }
              common_vendor.index.showToast({
                title: "订单已取消",
                icon: "success"
              });
            }, 500);
          }
        }
      });
    },
    // 支付订单
    payOrder(order) {
      common_vendor.index.navigateTo({
        url: `/pages/pay/index?orderId=${order.id}&amount=${order.totalAmount}`
      });
    },
    // 查看配送
    checkDelivery(order) {
      common_vendor.index.showToast({
        title: "暂无配送信息",
        icon: "none"
      });
    },
    // 确认收货
    confirmReceive(order) {
      common_vendor.index.showModal({
        title: "提示",
        content: "确认已收到商品吗？",
        success: (res) => {
          if (res.confirm) {
            common_vendor.index.showLoading({
              title: "处理中..."
            });
            setTimeout(() => {
              common_vendor.index.hideLoading();
              const index = this.ordersList.findIndex((item) => item.id === order.id);
              if (index !== -1) {
                this.ordersList[index].status = 4;
              }
              common_vendor.index.showToast({
                title: "确认收货成功",
                icon: "success"
              });
            }, 500);
          }
        }
      });
    },
    // 评价
    writeReview(order) {
      common_vendor.index.navigateTo({
        url: `/pages/review/write?orderId=${order.id}`
      });
    },
    // 删除订单
    deleteOrder(order) {
      common_vendor.index.showModal({
        title: "提示",
        content: "确定要删除该订单吗？",
        success: (res) => {
          if (res.confirm) {
            common_vendor.index.showLoading({
              title: "处理中..."
            });
            setTimeout(() => {
              common_vendor.index.hideLoading();
              const index = this.ordersList.findIndex((item) => item.id === order.id);
              if (index !== -1) {
                this.ordersList.splice(index, 1);
              }
              common_vendor.index.showToast({
                title: "删除成功",
                icon: "success"
              });
            }, 500);
          }
        }
      });
    },
    // 联系商家
    contactShop(order) {
      common_vendor.index.makePhoneCall({
        phoneNumber: order.shopPhone || "10086",
        fail: () => {
          common_vendor.index.showToast({
            title: "拨打电话失败",
            icon: "none"
          });
        }
      });
    },
    // 前往团购页面
    goToGroupBuy() {
      common_vendor.index.navigateTo({
        url: "/subPackages/activity-showcase/pages/group-buy/index"
      });
    },
    // 模拟订单数据
    getMockOrdersData() {
      return [
        {
          id: "1001",
          shopName: "磁州烧饼店",
          shopAvatar: "/static/images/shop/shop1.jpg",
          shopPhone: "13812345678",
          goodsName: "正宗磁州烧饼 买二送一",
          goodsImage: "/static/images/product/food1.jpg",
          goodsSpec: "原味 3个装",
          price: 19.9,
          count: 1,
          totalAmount: 19.9,
          status: 1,
          // 待付款
          createTime: "2023-09-15 14:30"
        },
        {
          id: "1002",
          shopName: "水果鲜生",
          shopAvatar: "/static/images/shop/shop2.jpg",
          shopPhone: "13998765432",
          goodsName: "精品水果礼盒 新鲜当季水果",
          goodsImage: "/static/images/product/food2.jpg",
          goodsSpec: "精品混合装 3kg",
          price: 59.9,
          count: 1,
          totalAmount: 59.9,
          status: 2,
          // 待发货
          createTime: "2023-09-14 10:15"
        },
        {
          id: "1003",
          shopName: "老北京小吃",
          shopAvatar: "/static/images/shop/shop3.jpg",
          shopPhone: "13756781234",
          goodsName: "手工制作老北京糖葫芦",
          goodsImage: "/static/images/product/food3.jpg",
          goodsSpec: "山楂味 10串",
          price: 15.9,
          count: 2,
          totalAmount: 31.8,
          status: 3,
          // 待收货
          createTime: "2023-09-13 16:45"
        },
        {
          id: "1004",
          shopName: "磁州特色小吃",
          shopAvatar: "/static/images/shop/shop4.jpg",
          shopPhone: "13612345678",
          goodsName: "特色小吃套餐 多种口味",
          goodsImage: "/static/images/product/food4.jpg",
          goodsSpec: "经典6件套",
          price: 39.9,
          count: 1,
          totalAmount: 39.9,
          status: 4,
          // 已完成
          createTime: "2023-09-10 09:20"
        },
        {
          id: "1005",
          shopName: "磁州烧饼店",
          shopAvatar: "/static/images/shop/shop1.jpg",
          shopPhone: "13812345678",
          goodsName: "正宗磁州烧饼 买二送一",
          goodsImage: "/static/images/product/food1.jpg",
          goodsSpec: "芝麻味 3个装",
          price: 21.9,
          count: 1,
          totalAmount: 21.9,
          status: 5,
          // 已取消
          createTime: "2023-09-08 11:30"
        }
      ];
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_assets._imports_0$5,
    b: common_vendor.o((...args) => $options.goBack && $options.goBack(...args)),
    c: $data.statusBarHeight + "px",
    d: common_vendor.f($data.tabs, (tab, index, i0) => {
      return {
        a: common_vendor.t(tab.name),
        b: index,
        c: $data.currentTab === index ? 1 : "",
        d: common_vendor.o(($event) => $options.switchTab(index), index)
      };
    }),
    e: $data.statusBarHeight + 44 + "px",
    f: $data.ordersList.length > 0
  }, $data.ordersList.length > 0 ? {
    g: common_vendor.f($data.ordersList, (item, k0, i0) => {
      return common_vendor.e({
        a: item.shopAvatar,
        b: common_vendor.t(item.shopName),
        c: common_vendor.t($options.getStatusText(item.status)),
        d: common_vendor.n("status-" + item.status),
        e: item.goodsImage,
        f: common_vendor.t(item.goodsName),
        g: item.goodsSpec
      }, item.goodsSpec ? {
        h: common_vendor.t(item.goodsSpec)
      } : {}, {
        i: common_vendor.t(item.price),
        j: common_vendor.t(item.count),
        k: common_vendor.t(item.createTime),
        l: common_vendor.t(item.count),
        m: common_vendor.t(item.totalAmount),
        n: item.status === 1
      }, item.status === 1 ? {
        o: common_vendor.o(($event) => $options.cancelOrder(item), item.id)
      } : {}, {
        p: item.status === 1
      }, item.status === 1 ? {
        q: common_vendor.o(($event) => $options.payOrder(item), item.id)
      } : {}, {
        r: item.status === 2
      }, item.status === 2 ? {
        s: common_vendor.o(($event) => $options.checkDelivery(item), item.id)
      } : {}, {
        t: item.status === 3
      }, item.status === 3 ? {
        v: common_vendor.o(($event) => $options.confirmReceive(item), item.id)
      } : {}, {
        w: item.status === 4
      }, item.status === 4 ? {
        x: common_vendor.o(($event) => $options.writeReview(item), item.id)
      } : {}, {
        y: item.status === 4 || item.status === 5
      }, item.status === 4 || item.status === 5 ? {
        z: common_vendor.o(($event) => $options.deleteOrder(item), item.id)
      } : {}, {
        A: common_vendor.o(($event) => $options.contactShop(item), item.id),
        B: item.id,
        C: common_vendor.o(($event) => $options.viewOrderDetail(item), item.id)
      });
    }),
    h: common_assets._imports_1$15
  } : {}, {
    i: $data.ordersList.length === 0 && !$data.loading
  }, $data.ordersList.length === 0 && !$data.loading ? {
    j: common_assets._imports_2$10,
    k: common_vendor.o((...args) => $options.goToGroupBuy && $options.goToGroupBuy(...args))
  } : {}, {
    l: $data.loading && !$data.refreshing
  }, $data.loading && !$data.refreshing ? {} : {}, {
    m: $data.loadAll && $data.ordersList.length > 0
  }, $data.loadAll && $data.ordersList.length > 0 ? {} : {}, {
    n: common_vendor.o((...args) => $options.loadMore && $options.loadMore(...args)),
    o: $data.refreshing,
    p: common_vendor.o((...args) => $options.refresh && $options.refresh(...args))
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/user/group-orders.js.map
