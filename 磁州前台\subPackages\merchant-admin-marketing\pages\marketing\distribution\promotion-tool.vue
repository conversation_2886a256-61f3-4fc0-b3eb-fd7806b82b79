<template>
  <view class="promotion-container">
    <!-- 自定义导航栏 -->
    <view class="navbar">
      <view class="navbar-back" @click="goBack">
        <view class="back-icon"></view>
      </view>
      <text class="navbar-title">推广海报</text>
      <view class="navbar-right">
        <view class="help-icon" @click="showHelp">?</view>
      </view>
    </view>
    
    <!-- 海报预览 -->
    <view class="poster-preview-section">
      <view class="preview-card" :style="{ backgroundColor: selectedTheme.bgColor }">
        <image class="poster-image" :src="currentPoster" mode="aspectFit"></image>
        <view class="preview-footer">
          <text class="preview-text" :style="{ color: selectedTheme.textColor }">预览效果</text>
        </view>
      </view>
      
      <view class="action-buttons">
        <button class="action-button save" @click="savePoster">
          <view class="button-icon">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M21 15V19C21 19.5304 20.7893 20.0391 20.4142 20.4142C20.0391 20.7893 19.5304 21 19 21H5C4.46957 21 3.96086 20.7893 3.58579 20.4142C3.21071 20.0391 3 19.5304 3 19V15" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              <path d="M7 10L12 15L17 10" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              <path d="M12 15V3" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
          </view>
          <text>保存到相册</text>
        </button>
        
        <button class="action-button share" @click="sharePoster">
          <view class="button-icon">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <circle cx="18" cy="5" r="3" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              <circle cx="6" cy="12" r="3" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              <circle cx="18" cy="19" r="3" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              <line x1="8.59" y1="13.51" x2="15.42" y2="17.49" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              <line x1="15.41" y1="6.51" x2="8.59" y2="10.49" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
          </view>
          <text>分享海报</text>
        </button>
      </view>
    </view>
    
    <!-- 自定义选项 -->
    <view class="customization-section">
      <!-- 海报模板选择 -->
      <view class="option-group">
        <text class="option-title">海报模板</text>
        <scroll-view scroll-x class="template-scroll">
          <view class="template-list">
            <view 
              v-for="(poster, index) in posterTemplates" 
              :key="index" 
              class="template-item"
              :class="{ active: currentPosterIndex === index }"
              @click="selectPoster(index)"
            >
              <image class="template-thumb" :src="poster.thumb" mode="aspectFill"></image>
            </view>
          </view>
        </scroll-view>
      </view>
      
      <!-- 主题颜色选择 -->
      <view class="option-group">
        <text class="option-title">主题颜色</text>
        <view class="theme-options">
          <view 
            v-for="(theme, index) in themeColors" 
            :key="index" 
            class="theme-option" 
            :class="{ active: selectedTheme === theme }"
            :style="{ backgroundColor: theme.bgColor }"
            @click="selectTheme(theme)"
          >
            <view class="theme-check" v-if="selectedTheme === theme">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M20 6L9 17L4 12" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 海报类型选择 -->
      <view class="option-group">
        <text class="option-title">海报类型</text>
        <view class="type-options">
          <view 
            v-for="(type, index) in posterTypes" 
            :key="index" 
            class="type-option" 
            :class="{ active: selectedType === type.value }"
            @click="selectType(type.value)"
          >
            <view class="option-icon" v-html="type.icon"></view>
            <text class="option-name">{{type.name}}</text>
          </view>
        </view>
      </view>
      
      <!-- 其他选项 -->
      <view class="option-group">
        <view class="toggle-option">
          <text class="toggle-label">显示店铺LOGO</text>
          <switch :checked="showLogo" @change="toggleLogo" color="#6B0FBE" />
        </view>
        
        <view class="toggle-option">
          <text class="toggle-label">显示推广员ID</text>
          <switch :checked="showId" @change="toggleId" color="#6B0FBE" />
        </view>
        
        <view class="toggle-option">
          <text class="toggle-label">添加推广文案</text>
          <switch :checked="showPromoText" @change="togglePromoText" color="#6B0FBE" />
        </view>
      </view>
      
      <!-- 自定义文案 -->
      <view class="option-group" v-if="showPromoText">
        <text class="option-title">自定义文案</text>
        <input 
          class="custom-input" 
          type="text" 
          v-model="customText" 
          placeholder="输入自定义文案（最多20字）" 
          maxlength="20"
        />
      </view>
    </view>
    
    <!-- 历史记录 -->
    <view class="history-section">
      <view class="section-header">
        <text class="section-title">历史记录</text>
        <text class="clear-history" @click="clearHistory">清空</text>
      </view>
      
      <scroll-view class="history-scroll" scroll-x>
        <view class="history-list">
          <view 
            v-for="(item, index) in historyPosters" 
            :key="index" 
            class="history-item"
            @click="loadHistoryPoster(item)"
          >
            <image class="history-image" :src="item.image" mode="aspectFill"></image>
            <text class="history-date">{{item.date}}</text>
          </view>
        </view>
      </scroll-view>
    </view>
  </view>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue';
import promotionService from '@/utils/promotionService';

// 推广参数
const promotionParams = reactive({
  type: 'product', // 默认类型
  id: '',
  title: '',
  image: '',
  extraParams: {} // 存储额外参数
});

// 店铺信息
const storeInfo = reactive({
  name: '磁州同城生活',
  slogan: '本地生活好物优选',
  logo: '/static/images/logo.png'
});

// 分销员ID
const distributorId = ref('D88652');

// 海报模板
const posterTemplates = ref([]);

// 默认海报模板
const defaultTemplates = [
  {
    name: '商品推广',
    thumb: '/static/images/distribution/poster-thumb-1.png',
    url: '/static/images/distribution/poster-1.png',
    type: 'product'
  },
  {
    name: '店铺推广',
    thumb: '/static/images/distribution/poster-thumb-2.png',
    url: '/static/images/distribution/poster-2.png',
    type: 'store'
  },
  {
    name: '活动推广',
    thumb: '/static/images/distribution/poster-thumb-3.png',
    url: '/static/images/distribution/poster-3.png',
    type: 'activity'
  },
  {
    name: '会员推广',
    thumb: '/static/images/distribution/poster-thumb-4.png',
    url: '/static/images/distribution/poster-4.png',
    type: 'member'
  },
  {
    name: '节日主题',
    thumb: '/static/images/distribution/poster-thumb-5.png',
    url: '/static/images/distribution/poster-5.png',
    type: 'general'
  }
];

// 特定类型的模板
const typeTemplates = {
  'carpool': [
    {
      name: '拼车专用',
      thumb: '/static/images/distribution/carpool-thumb-1.png',
      url: '/static/images/distribution/carpool-1.png',
      type: 'carpool'
    }
  ],
  'secondhand': [
    {
      name: '二手商品专用',
      thumb: '/static/images/distribution/secondhand-thumb-1.png',
      url: '/static/images/distribution/secondhand-1.png',
      type: 'secondhand'
    }
  ],
  'house': [
    {
      name: '房屋租售专用',
      thumb: '/static/images/distribution/house-thumb-1.png',
      url: '/static/images/distribution/house-1.png',
      type: 'house'
    }
  ],
  'service': [
    {
      name: '服务推广专用',
      thumb: '/static/images/distribution/service-thumb-1.png',
      url: '/static/images/distribution/service-1.png',
      type: 'service'
    }
  ],
  'community': [
    {
      name: '社区信息专用',
      thumb: '/static/images/distribution/community-thumb-1.png',
      url: '/static/images/distribution/community-1.png',
      type: 'community'
    }
  ]
};

// 历史记录
const historyPosters = reactive([
  { image: '/static/images/distribution/poster-1.png', date: '2023-04-25' },
  { image: '/static/images/distribution/poster-2.png', date: '2023-04-20' },
  { image: '/static/images/distribution/poster-3.png', date: '2023-04-15' },
  { image: '/static/images/distribution/poster-4.png', date: '2023-04-10' }
]);

// 海报类型
const posterTypes = [
  {
    name: '店铺',
    value: 'store',
    icon: '<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M3 9L12 2L21 9V20C21 20.5304 20.7893 21.0391 20.4142 21.4142C20.0391 21.7893 19.5304 22 19 22H5C4.46957 22 3.96086 21.7893 3.58579 21.4142C3.21071 21.0391 3 20.5304 3 20V9Z" stroke="#6B0FBE" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/><path d="M9 22V12H15V22" stroke="#6B0FBE" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/></svg>'
  },
  {
    name: '商品',
    value: 'product',
    icon: '<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M20 7L12 3L4 7M20 7V17L12 21M20 7L12 11M12 21L4 17V7M12 21V11M4 7L12 11" stroke="#6B0FBE" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/></svg>'
  },
  {
    name: '活动',
    value: 'activity',
    icon: '<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M12 8V12L15 15M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z" stroke="#6B0FBE" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/></svg>'
  },
  {
    name: '会员',
    value: 'member',
    icon: '<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M20 21V19C20 17.9391 19.5786 16.9217 18.8284 16.1716C18.0783 15.4214 17.0609 15 16 15H8C6.93913 15 5.92172 15.4214 5.17157 16.1716C4.42143 16.9217 4 17.9391 4 19V21" stroke="#6B0FBE" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/><path d="M12 11C14.2091 11 16 9.20914 16 7C16 4.79086 14.2091 3 12 3C9.79086 3 8 4.79086 8 7C8 9.20914 9.79086 11 12 11Z" stroke="#6B0FBE" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/></svg>'
  }
];

// 主题颜色
const themeColors = [
  { bgColor: '#FFFFFF', textColor: '#333333' },
  { bgColor: '#6B0FBE', textColor: '#FFFFFF' },
  { bgColor: '#A764CA', textColor: '#FFFFFF' },
  { bgColor: '#F5F7FA', textColor: '#333333' },
  { bgColor: '#FFE8F0', textColor: '#FF3B30' },
  { bgColor: '#E8F8FF', textColor: '#007AFF' }
];

// 状态变量
const currentPosterIndex = ref(0);
const selectedType = ref('product');
const selectedTheme = ref(themeColors[0]);
const showLogo = ref(true);
const showId = ref(true);
const showPromoText = ref(false);
const customText = ref('');

// 当前海报
const currentPoster = computed(() => {
  if (posterTemplates.value.length === 0) return '';
  return posterTemplates.value[currentPosterIndex.value].url;
});

// 页面加载
onMounted(() => {
  // 获取页面参数
  const pages = getCurrentPages();
  const currentPage = pages[pages.length - 1];
  const options = currentPage.options || {};
  
  // 解析推广参数
  parsePromotionParams(options);
  
  // 加载适合的海报模板
  loadPosterTemplates();
  
  // 生成推广内容
  generatePromotionContent();
});

// 解析推广参数
const parsePromotionParams = (options) => {
  // 设置基本参数
  promotionParams.type = options.type || 'product';
  promotionParams.id = options.id || '';
  promotionParams.title = options.title ? decodeURIComponent(options.title) : '';
  promotionParams.image = options.image ? decodeURIComponent(options.image) : '';
  
  // 设置额外参数
  const extraParams = {};
  Object.keys(options).forEach(key => {
    if (!['type', 'id', 'title', 'image'].includes(key)) {
      extraParams[key] = decodeURIComponent(options[key] || '');
    }
  });
  promotionParams.extraParams = extraParams;
  
  // 设置默认选中类型
  selectedType.value = promotionParams.type;
};

// 加载海报模板
const loadPosterTemplates = () => {
  // 获取特定类型的模板
  const typeSpecificTemplates = typeTemplates[promotionParams.type] || [];
  
  // 合并模板
  posterTemplates.value = [
    ...typeSpecificTemplates,
    ...defaultTemplates.filter(template => 
      template.type === 'general' || template.type === promotionParams.type
    )
  ];
  
  // 如果没有模板，使用默认模板
  if (posterTemplates.value.length === 0) {
    posterTemplates.value = defaultTemplates;
  }
};

// 生成推广内容
const generatePromotionContent = () => {
  // 根据不同类型生成不同的推广内容
  switch(promotionParams.type) {
    case 'carpool':
      customText.value = `【${promotionParams.title}】${promotionParams.extraParams.departure}→${promotionParams.extraParams.destination}，出发时间：${promotionParams.extraParams.departureTime}，车费：¥${promotionParams.extraParams.price}，剩余座位：${promotionParams.extraParams.seats}个`;
      break;
      
    case 'secondhand':
      customText.value = `【${promotionParams.title}】${promotionParams.extraParams.condition || ''}，原价：¥${promotionParams.extraParams.originalPrice || ''}，现价：¥${promotionParams.extraParams.price || ''}`;
      break;
      
    case 'house':
      customText.value = `【${promotionParams.title}】${promotionParams.extraParams.location || ''}，${promotionParams.extraParams.area || ''}㎡，${promotionParams.extraParams.roomType || ''}，${promotionParams.extraParams.rentType || ''}：¥${promotionParams.extraParams.price || ''}`;
      break;
      
    case 'service':
      customText.value = `【${promotionParams.title}】${promotionParams.extraParams.category || ''}，${promotionParams.extraParams.merchantName || ''}提供，价格：¥${promotionParams.extraParams.price || ''}`;
      break;
      
    case 'product':
      customText.value = `【${promotionParams.title}】${promotionParams.extraParams.category || ''}，价格：¥${promotionParams.extraParams.price || ''}`;
      break;
      
    case 'content':
      customText.value = `【${promotionParams.title}】${promotionParams.extraParams.summary || ''}`;
      break;
      
    case 'job':
      customText.value = `【${promotionParams.title}】${promotionParams.extraParams.company || ''}招聘，薪资：${promotionParams.extraParams.salary || ''}，地点：${promotionParams.extraParams.location || ''}`;
      break;
      
    case 'activity':
      customText.value = `【${promotionParams.title}】时间：${promotionParams.extraParams.startTime || ''}，地点：${promotionParams.extraParams.location || ''}，主办方：${promotionParams.extraParams.organizer || ''}`;
      break;
      
    case 'community':
      customText.value = `【${promotionParams.title}】${promotionParams.extraParams.category || ''}，地点：${promotionParams.extraParams.location || ''}，联系方式：${promotionParams.extraParams.contact || ''}`;
      break;
      
    default:
      customText.value = `【${promotionParams.title}】`;
  }
  
  // 显示推广文案
  showPromoText.value = true;
};

// 选择海报模板
const selectPoster = (index) => {
  currentPosterIndex.value = index;
};

// 选择主题颜色
const selectTheme = (theme) => {
  selectedTheme.value = theme;
};

// 选择海报类型
const selectType = (type) => {
  selectedType.value = type;
};

// 切换显示店铺LOGO
const toggleLogo = (e) => {
  showLogo.value = e.detail.value;
};

// 切换显示推广员ID
const toggleId = (e) => {
  showId.value = e.detail.value;
};

// 切换显示推广文案
const togglePromoText = (e) => {
  showPromoText.value = e.detail.value;
};

// 保存海报到相册
const savePoster = () => {
  uni.showLoading({
    title: '保存中...'
  });
  
  setTimeout(() => {
    uni.hideLoading();
    uni.showToast({
      title: '保存成功',
      icon: 'success'
    });
    
    // 记录到历史记录
    const now = new Date();
    const dateStr = `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}-${String(now.getDate()).padStart(2, '0')}`;
    historyPosters.unshift({
      image: currentPoster.value,
      date: dateStr
    });
  }, 1500);
};

// 分享海报
const sharePoster = () => {
  uni.showActionSheet({
    itemList: ['分享给朋友', '分享到朋友圈'],
    success: (res) => {
      uni.showToast({
        title: '分享成功',
        icon: 'success'
      });
      
      // 记录分享事件
      const shareType = res.tapIndex === 0 ? 'friend' : 'timeline';
      logShareEvent(shareType);
    }
  });
};

// 记录分享事件
const logShareEvent = (shareType) => {
  console.log('记录分享事件', {
    type: promotionParams.type,
    id: promotionParams.id,
    shareType
  });
};

// 加载历史海报
const loadHistoryPoster = (item) => {
  // 实际应用中应该加载历史海报的配置
    uni.showToast({
    title: '加载历史海报',
      icon: 'none'
    });
};

// 清空历史记录
const clearHistory = () => {
  uni.showModal({
    title: '提示',
    content: '确定要清空历史记录吗？',
    success: (res) => {
      if (res.confirm) {
        historyPosters.splice(0, historyPosters.length);
        uni.showToast({
          title: '已清空',
          icon: 'success'
        });
      }
    }
  });
};

// 返回上一页
const goBack = () => {
  uni.navigateBack();
};

// 显示帮助
const showHelp = () => {
  uni.showModal({
    title: '推广海报使用帮助',
    content: '选择喜欢的海报模板，自定义颜色和文案，生成精美海报进行分享推广。',
    showCancel: false
  });
};
</script>

<style lang="scss">
.promotion-container {
  min-height: 100vh;
  background-color: #F5F7FA;
  padding-bottom: env(safe-area-inset-bottom);
}

/* 导航栏样式 */
.navbar {
  position: sticky;
  top: 0;
  left: 0;
  right: 0;
  background: linear-gradient(135deg, #A764CA, #6B0FBE);
  color: #fff;
  padding: 44px 16px 15px;
  display: flex;
  align-items: center;
  z-index: 100;
  box-shadow: 0 2px 10px rgba(107, 15, 190, 0.15);
}

.navbar-back {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-icon {
  width: 12px;
  height: 12px;
  border-left: 2px solid #fff;
  border-bottom: 2px solid #fff;
  transform: rotate(45deg);
}

.navbar-title {
  flex: 1;
  text-align: center;
  font-size: 18px;
  font-weight: 600;
  letter-spacing: 0.5px;
}

.navbar-right {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.help-icon {
  width: 24px;
  height: 24px;
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: bold;
}

/* 海报预览部分 */
.poster-preview-section {
  margin: 16px;
}

.preview-card {
  background-color: #FFFFFF;
  border-radius: 20px;
  padding: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  margin-bottom: 16px;
  overflow: hidden;
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.poster-image {
  width: 100%;
  max-width: 300px;
  height: 400px;
  border-radius: 10px;
}

.preview-footer {
  margin-top: 12px;
  text-align: center;
}

.preview-text {
  font-size: 14px;
  color: #666;
}

.action-buttons {
  display: flex;
  gap: 12px;
}

.action-button {
  flex: 1;
  height: 44px;
  border-radius: 22px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: 500;
  border: none;
}

.action-button.save {
  background: linear-gradient(135deg, #34C759, #32D74B);
  color: #FFFFFF;
}

.action-button.share {
  background: linear-gradient(135deg, #A764CA, #6B0FBE);
  color: #FFFFFF;
}

.button-icon {
  margin-right: 6px;
}

/* 自定义选项部分 */
.customization-section {
  margin: 16px;
  background-color: #FFFFFF;
  border-radius: 20px;
  padding: 20px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.04);
}

.option-group {
  margin-bottom: 20px;
}

.option-group:last-child {
  margin-bottom: 0;
}

.option-title {
  font-size: 15px;
  font-weight: 600;
  color: #333;
  margin-bottom: 12px;
  display: block;
}

.template-scroll {
  width: 100%;
  white-space: nowrap;
}

.template-list {
  display: flex;
  padding-bottom: 8px;
}

.template-item {
  width: 80px;
  height: 120px;
  margin-right: 12px;
  border-radius: 10px;
  overflow: hidden;
  border: 2px solid transparent;
  position: relative;
  transition: all 0.3s ease;
  flex-shrink: 0;
}

.template-item.active {
  border-color: #6B0FBE;
  transform: scale(1.05);
}

.template-thumb {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.type-options {
  display: flex;
  justify-content: space-between;
}

.type-option {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 12px 0;
  border-radius: 12px;
  transition: all 0.3s ease;
}

.type-option.active {
  background-color: rgba(107, 15, 190, 0.05);
}

.option-icon {
  margin-bottom: 8px;
}

.option-name {
  font-size: 12px;
  color: #666;
}

.type-option.active .option-name {
  color: #6B0FBE;
  font-weight: 500;
}

.theme-options {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
}

.theme-option {
  width: 40px;
  height: 40px;
  border-radius: 20px;
  border: 2px solid transparent;
  position: relative;
  transition: all 0.3s ease;
}

.theme-option.active {
  border-color: #6B0FBE;
  transform: scale(1.05);
  box-shadow: 0 2px 8px rgba(107, 15, 190, 0.2);
}

.theme-check {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.toggle-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #F0F0F0;
}

.toggle-option:last-child {
  border-bottom: none;
}

.toggle-label {
  font-size: 14px;
  color: #333;
}

.custom-input {
  width: 100%;
  height: 44px;
  background-color: #F5F7FA;
  border-radius: 12px;
  padding: 0 16px;
  font-size: 14px;
  color: #333;
  border: 1px solid transparent;
}

.custom-input:focus {
  border-color: #6B0FBE;
  background-color: #FFFFFF;
}

/* 历史记录部分 */
.history-section {
  margin: 16px;
  background-color: #FFFFFF;
  border-radius: 20px;
  padding: 20px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.04);
  margin-bottom: 32px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.section-title {
  font-size: 15px;
  font-weight: 600;
  color: #333;
}

.clear-history {
  font-size: 14px;
  color: #FF3B30;
}

.history-scroll {
  width: 100%;
}

.history-list {
  display: flex;
  padding-bottom: 8px;
}

.history-item {
  width: 80px;
  margin-right: 12px;
  flex-shrink: 0;
}

.history-item:last-child {
  margin-right: 0;
}

.history-image {
  width: 80px;
  height: 120px;
  border-radius: 10px;
  border: 1px solid #EEEEEE;
  margin-bottom: 6px;
}

.history-date {
  font-size: 10px;
  color: #999;
  text-align: center;
  display: block;
}
</style>