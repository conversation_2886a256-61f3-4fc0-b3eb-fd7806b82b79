
.detail-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f5f5;
}

/* 自定义导航栏 */
.custom-navbar {
  background: linear-gradient(135deg, #0066FF, #0052CC);
  height: 88rpx;
  padding-top: 44px; /* 状态栏高度 */
  display: flex;
  align-items: center;
  position: fixed; /* 改为固定定位 */
  top: 0;
  left: 0;
  right: 0;
  padding-bottom: 10rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 82, 204, 0.2);
  z-index: 100; /* 提高z-index确保在最上层 */
}
.navbar-left, .navbar-right {
  width: 32px;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.back-icon, .more-icon {
  width: 24px;
  height: 24px;
}
.navbar-title {
  color: #FFFFFF;
  font-size: 18px;
  font-weight: 600;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* 内容区域 */
.detail-content {
  flex: 1;
  box-sizing: border-box;
}

/* 标签 */
.header-tag-container {
  padding: 15rpx 30rpx 10rpx;
  margin-top: 0;
}
.header-tag {
  display: inline-flex;
  align-items: center;
  padding: 4px 12px;
  border-radius: 16px;
  font-size: 14px;
  color: #ffffff;
}
.car-to-people {
  background-color: #1989fa;
}
.people-to-car {
  background-color: #ff6b00;
}
.verified-tag {
  margin-left: 8px;
  padding: 2px 6px;
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 10px;
  font-size: 12px;
}

/* 行程信息 */
.route-card {
  margin: 0 30rpx 30rpx;
  padding: 20rpx;
  background-color: #ffffff;
  border-radius: 16rpx;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}
.route-points {
  position: relative;
}
.route-point {
  display: flex;
  align-items: center;
  padding: 10px 0;
}
.point-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  margin-right: 10px;
}
.start {
  background-color: #1989fa;
}
.end {
  background-color: #ff6b00;
}
.point-info {
  flex: 1;
}
.point-name {
  font-size: 16px;
  font-weight: 500;
  color: #333;
}
.point-address {
  font-size: 12px;
  color: #999;
  margin-top: 2px;
}
.route-divider {
  padding-left: 6px;
  margin: 5px 0;
  height: 30px;
  position: relative;
}
.divider-line {
  position: absolute;
  left: 6px;
  top: 0;
  bottom: 0;
  width: 1px;
  background-color: #ddd;
}
.divider-info {
  margin-left: 20px;
}
.divider-text {
  font-size: 12px;
  color: #999;
}

/* 行程详情 */
.trip-details-card, .info-card, .user-card, .car-card, .remark-card, .disclaimer-card {
  margin: 0 30rpx 30rpx;
  padding: 20rpx;
  background-color: #ffffff;
  border-radius: 16rpx;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}
.section-title {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 10px;
  display: block;
}
.trip-details-content {
  margin-top: 10px;
}
.trip-details-item {
  display: flex;
  margin-bottom: 10px;
}
.trip-details-label {
  width: 80px;
  color: #666;
  font-size: 14px;
}
.trip-details-value {
  flex: 1;
  color: #333;
  font-size: 14px;
}

/* 信息卡片 */
.info-item-new {
  display: flex;
  justify-content: space-between;
  padding: 10px 0;
  border-bottom: 1px solid #f0f0f0;
}
.info-item-new:last-child {
  border-bottom: none;
}
.info-label-new {
  color: #666;
  font-size: 14px;
}
.info-value-new {
  color: #333;
  font-size: 14px;
  font-weight: 500;
}
.price-value {
  color: #ff6b00;
}

/* 用户信息 */
.user-info {
  display: flex;
  align-items: center;
  margin-top: 10px;
}
.user-avatar {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  margin-right: 15px;
}
.user-details {
  flex: 1;
}
.user-name-row {
  display: flex;
  align-items: center;
}
.user-name {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-right: 10px;
}
.user-badges {
  display: flex;
}
.user-badge {
  padding: 2px 6px;
  border-radius: 10px;
  font-size: 12px;
  margin-right: 5px;
}
.verified {
  background-color: #1989fa;
  color: #fff;
}
.premium {
  background-color: #ff6b00;
  color: #fff;
}
.user-meta {
  font-size: 12px;
  color: #999;
  margin-top: 5px;
}

/* 司机评分 */
.driver-rating {
  margin-top: 15px;
  padding-top: 15px;
  border-top: 1px solid #f0f0f0;
}
.rating-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}
.rating-title {
  font-size: 14px;
  font-weight: 500;
  color: #333;
}
.rating-count {
  font-size: 12px;
  color: #999;
}
.rating-stars {
  display: flex;
  align-items: center;
}
.star-container {
  position: relative;
  width: 100px;
  height: 20px;
  margin-right: 10px;
}
.star-bg {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('../../../static/images/tabbar/star-bg.png') repeat-x;
  background-size: 20px 20px;
}
.star-fill {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  background: url('../../../static/images/tabbar/star-fill.png') repeat-x;
  background-size: 20px 20px;
}
.rating-value {
  font-size: 16px;
  font-weight: 500;
  color: #ff6b00;
}
.rating-tags {
  display: flex;
  flex-wrap: wrap;
  margin-top: 10px;
}
.rating-tag {
  padding: 4px 8px;
  background-color: #f5f5f5;
  border-radius: 4px;
  font-size: 12px;
  color: #666;
  margin-right: 8px;
  margin-bottom: 8px;
}

/* 车辆信息 */
.car-info {
  margin-top: 10px;
}
.car-item {
  display: flex;
  margin-bottom: 10px;
}
.car-label {
  width: 80px;
  color: #666;
  font-size: 14px;
}
.car-value {
  flex: 1;
  color: #333;
  font-size: 14px;
}

/* 补充说明 */
.remark-content {
  font-size: 14px;
  color: #333;
  line-height: 1.5;
}

/* 免责声明 */
.disclaimer-content {
  display: flex;
  align-items: flex-start;
}
.disclaimer-icon {
  width: 20px;
  height: 20px;
  margin-right: 10px;
}
.disclaimer-icon image {
  width: 100%;
  height: 100%;
}
.disclaimer-text {
  flex: 1;
  font-size: 12px;
  color: #999;
  line-height: 1.5;
}

/* 发布时间 */
.publish-time-card {
  margin: 0 30rpx 30rpx;
  padding: 10rpx 15rpx;
}
.publish-time {
  display: flex;
  flex-direction: column;
  align-items: center;
}
.publish-time text {
  font-size: 12px;
  color: #999;
  line-height: 1.5;
}

/* 底部操作栏 */
.bottom-actions {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #fff;
  display: flex;
  padding: 10px 15px;
  box-shadow: 0 -1px 3px rgba(0, 0, 0, 0.1);
  z-index: 99;
}
.action-group {
  display: flex;
  flex: 1;
}
.action-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 0;
  background-color: transparent;
  flex: 1;
  height: 60px;
  line-height: 1;
}
.action-btn::after {
  border: none;
}
.action-btn image {
  width: 24px;
  height: 24px;
  margin-bottom: 5px;
}
.action-btn text {
  font-size: 12px;
  color: #666;
}
.call-btn-container {
  width: 120px;
  margin-left: 10px;
}
.call-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 60px;
  background-color: #1989fa;
  color: #fff;
  border-radius: 4px;
  font-size: 16px;
  line-height: 1;
}
.call-btn::after {
  border: none;
}
.call-btn image {
  width: 20px;
  height: 20px;
  margin-right: 5px;
}
.safe-area-bottom {
  height: env(safe-area-inset-bottom, 0);
  background-color: #fff;
}
.carpool-detail-wrapper {
  padding: 24rpx;
  padding-top: 144px; /* 增加顶部内边距，确保内容不被导航栏遮挡 */
}
