<template>
    <view class="promotion-text-container">
      <!-- 自定义导航栏 -->
      <view class="navbar">
        <view class="navbar-back" @click="goBack">
          <view class="back-icon"></view>
        </view>
        <text class="navbar-title">推广文案</text>
        <view class="navbar-right">
          <view class="history-icon" @click="showHistory">
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <circle cx="12" cy="12" r="10" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              <polyline points="12 6 12 12 16 14" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
          </view>
        </view>
      </view>
      
      <!-- 类型选择 -->
      <view class="type-selection-section">
        <view class="section-header">
          <text class="section-title">选择文案类型</text>
        </view>
        
        <view class="type-tabs">
          <view 
            v-for="(type, index) in textTypes" 
            :key="index" 
            class="type-tab" 
            :class="{ active: selectedTextType === type.value }"
            @click="selectTextType(type.value)"
          >
            <text>{{type.name}}</text>
          </view>
        </view>
      </view>
      
      <!-- 文案展示与复制 -->
      <view class="text-content-section">
        <view class="section-header">
          <text class="section-title">推荐文案</text>
          <view class="refresh-btn" @click="refreshTexts">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M23 4V10H17" stroke="#6B0FBE" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              <path d="M1 20V14H7" stroke="#6B0FBE" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              <path d="M3.51 9.00001C4.01608 7.56619 4.87951 6.28684 6.01303 5.27531C7.14655 4.26377 8.51648 3.55584 9.99906 3.22428C11.4816 2.89272 13.0223 2.9485 14.4773 3.38487C15.9323 3.82125 17.2511 4.62335 18.3 5.70001L23 10M1 14L5.7 18.3C6.74889 19.3767 8.06775 20.1788 9.52275 20.6151C10.9777 21.0515 12.5184 21.1073 14.0009 20.7757C15.4835 20.4442 16.8534 19.7362 17.987 18.7247C19.1205 17.7132 19.9839 16.4338 20.49 15" stroke="#6B0FBE" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
            <text>换一批</text>
          </view>
        </view>
        
        <scroll-view class="text-scroll" scroll-y>
          <view class="text-list">
            <view 
              v-for="(item, index) in filteredPromotionTexts" 
              :key="index" 
              class="text-item"
              @click="previewText(item)"
            >
              <view class="text-content">
                <text class="text-body">{{item.content}}</text>
                <view class="text-tags">
                  <text class="text-tag" v-for="(tag, tagIndex) in item.tags" :key="tagIndex">{{tag}}</text>
                </view>
              </view>
              <button class="copy-btn" @click.stop="copyText(item.content)">复制</button>
            </view>
          </view>
        </scroll-view>
      </view>
      
      <!-- 文案编辑 -->
      <view v-if="showEditor" class="text-editor-section">
        <view class="section-header">
          <text class="section-title">自定义文案</text>
          <view class="close-editor" @click="closeEditor">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M18 6L6 18" stroke="#6B0FBE" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              <path d="M6 6L18 18" stroke="#6B0FBE" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
          </view>
        </view>
        
        <textarea 
          class="text-textarea" 
          v-model="customTextContent" 
          placeholder="输入您的自定义推广文案..." 
          maxlength="200"
        ></textarea>
        
        <view class="template-tags">
          <text class="template-tag" @click="insertTag('{店铺名}')">店铺名</text>
          <text class="template-tag" @click="insertTag('{商品名}')">商品名</text>
          <text class="template-tag" @click="insertTag('{价格}')">价格</text>
          <text class="template-tag" @click="insertTag('{优惠}')">优惠</text>
          <text class="template-tag" @click="insertTag('{活动时间}')">活动时间</text>
        </view>
        
        <view class="editor-actions">
          <button class="action-button cancel" @click="closeEditor">取消</button>
          <button class="action-button save" @click="saveCustomText">保存</button>
        </view>
      </view>
      
      <!-- 底部操作栏 -->
      <view class="bottom-actions">
        <button class="create-button" @click="showEditor = true">
          <view class="button-icon">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M12 5V19" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              <path d="M5 12H19" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
          </view>
          <text>创建自定义文案</text>
        </button>
      </view>
      
      <!-- 文案预览弹窗 -->
      <view class="preview-modal" v-if="showPreview" @click="closePreview">
        <view class="preview-content" @click.stop>
          <view class="preview-header">
            <text class="preview-title">文案预览</text>
            <view class="close-preview" @click="closePreview">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M18 6L6 18" stroke="#6B0FBE" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M6 6L18 18" stroke="#6B0FBE" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </view>
          </view>
          
          <view class="preview-body">
            <text class="preview-text">{{currentPreviewText}}</text>
          </view>
          
          <view class="preview-actions">
            <button class="preview-action copy" @click="copyPreviewText">复制文案</button>
            <button class="preview-action share" @click="sharePreviewText">立即分享</button>
          </view>
        </view>
      </view>
    </view>
  </template>
  
  <script setup>
  import { ref, reactive, computed } from 'vue';
  
  // 文案类型
  const textTypes = [
    { name: '全部', value: 'all' },
    { name: '商品推广', value: 'product' },
    { name: '店铺推广', value: 'store' },
    { name: '活动推广', value: 'activity' },
    { name: '节日营销', value: 'festival' }
  ];
  
  // 推广文案
  const promotionTexts = reactive([
    {
      content: '👏【超值优惠】限时开启！{店铺名}年中大促，全场低至5折起，更有满减优惠等你来拿！我已经下单了，真的很划算，推荐你也来看看~',
      tags: ['商品推广', '优惠活动'],
      type: 'product'
    },
    {
      content: '🔥爆品推荐：{商品名}只要{价格}元！这家店的东西品质真心不错，我已经买过好几次了，用着很满意，分享给你也囤一波！',
      tags: ['商品推广', '爆品'],
      type: 'product'
    },
    {
      content: '✨新店开业大吉！{店铺名}正式开业啦！开业期间全场商品8.8折，前100名下单还送精美礼品一份！快来抢购吧！',
      tags: ['店铺推广', '开业'],
      type: 'store'
    },
    {
      content: '💝我的好友开了一家网店，主营{店铺名}，质量和服务都很好，现在下单有优惠，推荐给大家！',
      tags: ['店铺推广', '好友'],
      type: 'store'
    },
    {
      content: '🎁{活动时间}，{店铺名}举办超级福利活动！抽奖、秒杀、满减，好礼不断！我已经参与了，你也快来吧！',
      tags: ['活动推广', '抽奖'],
      type: 'activity'
    },
    {
      content: '📢重磅消息：{店铺名}年中大促即将开启，全场商品低至3折，更有神秘大礼等你来拿！扫码进店不容错过！',
      tags: ['活动推广', '折扣'],
      type: 'activity'
    },
    {
      content: '🎄圣诞特惠！{店铺名}圣诞节大促，精选好物5折起，还有圣诞限定礼盒！一年仅此一次的折扣，快来选购吧！',
      tags: ['节日营销', '圣诞节'],
      type: 'festival'
    },
    {
      content: '🧧新年好礼！{店铺名}新春大礼包已经准备好啦！下单即送新年红包，满{价格}再送限量款新年礼品！提前备年货，欢乐过新年！',
      tags: ['节日营销', '新年'],
      type: 'festival'
    }
  ]);
  
  // 状态变量
  const selectedTextType = ref('all');
  const showEditor = ref(false);
  const customTextContent = ref('');
  const showPreview = ref(false);
  const currentPreviewText = ref('');
  
  // 计算属性
  const filteredPromotionTexts = computed(() => {
    if (selectedTextType.value === 'all') {
      return promotionTexts;
    } else {
      return promotionTexts.filter(item => item.type === selectedTextType.value);
    }
  });
  
  // 方法
  const goBack = () => {
    uni.navigateBack();
  };
  
  const showHistory = () => {
    uni.navigateTo({
      url: '/subPackages/merchant-admin-marketing/pages/marketing/distribution/promotion-history'
    });
  };
  
  const selectTextType = (type) => {
    selectedTextType.value = type;
  };
  
  const refreshTexts = () => {
    uni.showToast({
      title: '已更新文案内容',
      icon: 'success',
      duration: 1500
    });
    
    // 实际应用中这里可以调用接口获取新的文案推荐
  };
  
  const previewText = (item) => {
    currentPreviewText.value = item.content;
    showPreview.value = true;
  };
  
  const closePreview = () => {
    showPreview.value = false;
  };
  
  const copyText = (text) => {
    uni.setClipboardData({
      data: text,
      success: () => {
        uni.showToast({
          title: '文案已复制',
          icon: 'success'
        });
      }
    });
  };
  
  const copyPreviewText = () => {
    copyText(currentPreviewText.value);
    closePreview();
  };
  
  const sharePreviewText = () => {
    uni.showShareMenu({
      withShareTicket: true,
      menus: ['shareAppMessage', 'shareTimeline']
    });
  };
  
  const closeEditor = () => {
    showEditor.value = false;
    customTextContent.value = '';
  };
  
  const insertTag = (tag) => {
    customTextContent.value += tag;
  };
  
  const saveCustomText = () => {
    if (!customTextContent.value.trim()) {
      uni.showToast({
        title: '请输入文案内容',
        icon: 'none'
      });
      return;
    }
    
    // 保存自定义文案
    promotionTexts.unshift({
      content: customTextContent.value,
      tags: ['自定义'],
      type: selectedTextType.value === 'all' ? 'store' : selectedTextType.value
    });
    
    uni.showToast({
      title: '文案已保存',
      icon: 'success'
    });
    
    closeEditor();
  };
  </script>
  
  <style lang="scss">
  .promotion-text-container {
    min-height: 100vh;
    background-color: #F5F7FA;
    padding-bottom: calc(90px + env(safe-area-inset-bottom));
  }
  
  /* 导航栏样式 */
  .navbar {
    position: sticky;
    top: 0;
    left: 0;
    right: 0;
    background: linear-gradient(135deg, #A764CA, #6B0FBE);
    color: #fff;
    padding: 44px 16px 15px;
    display: flex;
    align-items: center;
    z-index: 100;
    box-shadow: 0 2px 10px rgba(107, 15, 190, 0.15);
  }
  
  .navbar-back {
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  
  .back-icon {
    width: 12px;
    height: 12px;
    border-left: 2px solid #fff;
    border-bottom: 2px solid #fff;
    transform: rotate(45deg);
  }
  
  .navbar-title {
    flex: 1;
    text-align: center;
    font-size: 18px;
    font-weight: 600;
    letter-spacing: 0.5px;
  }
  
  .navbar-right {
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  
  .history-icon {
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  
  /* 类型选择部分 */
  .type-selection-section {
    margin: 16px;
    background-color: #FFFFFF;
    border-radius: 20px;
    padding: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.04);
  }
  
  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
  }
  
  .section-title {
    font-size: 16px;
    font-weight: 600;
    color: #333;
  }
  
  .type-tabs {
    display: flex;
    flex-wrap: nowrap;
    overflow-x: auto;
    gap: 10px;
    padding-bottom: 5px;
  }
  
  .type-tab {
    padding: 8px 16px;
    border-radius: 16px;
    font-size: 14px;
    background-color: #F0F0F0;
    color: #666;
    white-space: nowrap;
    transition: all 0.3s ease;
  }
  
  .type-tab.active {
    background-color: #6B0FBE;
    color: #FFFFFF;
    font-weight: 500;
  }
  
  /* 文案内容部分 */
  .text-content-section {
    margin: 16px;
    background-color: #FFFFFF;
    border-radius: 20px;
    padding: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.04);
  }
  
  .refresh-btn {
    display: flex;
    align-items: center;
    background-color: rgba(107, 15, 190, 0.05);
    border-radius: 16px;
    padding: 6px 12px;
  }
  
  .refresh-btn text {
    font-size: 14px;
    color: #6B0FBE;
    margin-left: 6px;
  }
  
  .text-scroll {
    max-height: 400px;
  }
  
  .text-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
  }
  
  .text-item {
    display: flex;
    background-color: #F8F9FC;
    border-radius: 12px;
    padding: 12px;
    position: relative;
  }
  
  .text-content {
    flex: 1;
    margin-right: 10px;
  }
  
  .text-body {
    font-size: 14px;
    color: #333;
    line-height: 1.5;
    margin-bottom: 8px;
  }
  
  .text-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 6px;
  }
  
  .text-tag {
    font-size: 10px;
    color: #6B0FBE;
    background-color: rgba(107, 15, 190, 0.05);
    padding: 2px 8px;
    border-radius: 10px;
  }
  
  .copy-btn {
    width: 60px;
    height: 30px;
    border-radius: 15px;
    background: linear-gradient(135deg, #A764CA, #6B0FBE);
    color: #FFFFFF;
    font-size: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: none;
    align-self: center;
  }
  
  /* 文案编辑部分 */
  .text-editor-section {
    position: fixed;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #FFFFFF;
    border-top-left-radius: 20px;
    border-top-right-radius: 20px;
    padding: 20px;
    z-index: 1000;
    box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.08);
    animation: slideUp 0.3s ease;
  }
  
  @keyframes slideUp {
    from {
      transform: translateY(100%);
    }
    to {
      transform: translateY(0);
    }
  }
  
  .close-editor {
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  
  .text-textarea {
    width: 100%;
    height: 120px;
    background-color: #F5F7FA;
    border-radius: 12px;
    padding: 12px;
    font-size: 14px;
    color: #333;
    border: 1px solid transparent;
    margin-bottom: 16px;
  }
  
  .text-textarea:focus {
    border-color: #6B0FBE;
    background-color: #FFFFFF;
  }
  
  .template-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-bottom: 16px;
  }
  
  .template-tag {
    font-size: 12px;
    color: #6B0FBE;
    background-color: rgba(107, 15, 190, 0.05);
    padding: 6px 12px;
    border-radius: 12px;
    border: 1px solid rgba(107, 15, 190, 0.2);
  }
  
  .editor-actions {
    display: flex;
    gap: 12px;
  }
  
  .action-button {
    flex: 1;
    height: 44px;
    border-radius: 22px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    font-weight: 500;
    border: none;
  }
  
  .action-button.cancel {
    background-color: #F0F0F0;
    color: #666;
  }
  
  .action-button.save {
    background: linear-gradient(135deg, #A764CA, #6B0FBE);
    color: #FFFFFF;
  }
  
  /* 底部操作栏 */
  .bottom-actions {
    position: fixed;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #FFFFFF;
    padding: 16px;
    box-shadow: 0 -4px 10px rgba(0, 0, 0, 0.05);
    z-index: 99;
    padding-bottom: calc(16px + env(safe-area-inset-bottom));
  }
  
  .create-button {
    width: 100%;
    height: 48px;
    background: linear-gradient(135deg, #A764CA, #6B0FBE);
    border-radius: 24px;
    color: #FFFFFF;
    font-size: 16px;
    font-weight: 600;
    display: flex;
    align-items: center;
    justify-content: center;
    border: none;
    box-shadow: 0 4px 12px rgba(107, 15, 190, 0.2);
  }
  
  .button-icon {
    margin-right: 6px;
  }
  
  /* 预览弹窗 */
  .preview-modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1001;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
  }
  
  .preview-content {
    width: 100%;
    max-width: 320px;
    background-color: #FFFFFF;
    border-radius: 20px;
    overflow: hidden;
    animation: zoomIn 0.3s ease;
  }
  
  @keyframes zoomIn {
    from {
      transform: scale(0.8);
      opacity: 0;
    }
    to {
      transform: scale(1);
      opacity: 1;
    }
  }
  
  .preview-header {
    padding: 16px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1px solid #F0F0F0;
  }
  
  .preview-title {
    font-size: 16px;
    font-weight: 600;
    color: #333;
  }
  
  .close-preview {
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  
  .preview-body {
    padding: 20px;
    max-height: 300px;
    overflow-y: auto;
  }
  
  .preview-text {
    font-size: 15px;
    color: #333;
    line-height: 1.6;
  }
  
  .preview-actions {
    display: flex;
    border-top: 1px solid #F0F0F0;
  }
  
  .preview-action {
    flex: 1;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 15px;
    font-weight: 500;
    border: none;
    background-color: transparent;
  }
  
  .preview-action.copy {
    color: #666;
    border-right: 1px solid #F0F0F0;
  }
  
  .preview-action.share {
    color: #6B0FBE;
  }
  </style>