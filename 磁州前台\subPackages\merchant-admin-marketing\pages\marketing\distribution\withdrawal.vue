<template>
  <view class="withdrawal-container">
    <!-- 自定义导航栏 -->
    <view class="navbar">
      <view class="navbar-back" @click="goBack">
        <view class="back-icon"></view>
      </view>
      <text class="navbar-title">提现设置</text>
      <view class="navbar-right">
        <view class="help-icon" @click="showHelp">?</view>
      </view>
    </view>
    
    <!-- 提现规则卡片 -->
    <view class="settings-card">
      <view class="card-header">
        <text class="card-title">提现规则</text>
      </view>
      
      <view class="form-item">
        <text class="form-label">最低提现金额</text>
        <view class="form-input-group">
          <text class="input-prefix">¥</text>
          <input type="digit" v-model="minWithdrawalAmount" class="form-input" placeholder="请输入金额" />
        </view>
      </view>
      
      <view class="form-item">
        <text class="form-label">提现手续费</text>
        <view class="form-input-group">
          <input type="digit" v-model="withdrawalFee" class="form-input" placeholder="请输入比例" />
          <text class="input-suffix">%</text>
        </view>
      </view>
      
      <view class="form-item">
        <text class="form-label">提现到账时间</text>
        <view class="form-select" @click="showTimeOptions">
          <text class="select-value">{{timeOptions[selectedTime]}}</text>
          <view class="arrow-icon down"></view>
        </view>
      </view>
      
      <view class="form-item">
        <text class="form-label">自动审核</text>
        <view class="form-switch">
          <switch :checked="autoApprove" @change="toggleAutoApprove" color="#6B0FBE" />
        </view>
      </view>
    </view>
    
    <!-- 提现方式卡片 -->
    <view class="methods-card">
      <view class="card-header">
        <text class="card-title">提现方式</text>
      </view>
      
      <view class="method-item" v-for="(method, index) in withdrawalMethods" :key="index">
        <view class="method-left">
          <view class="method-icon" :class="`method-${method.id}`">
            <image class="method-image" :src="method.icon" mode="aspectFit"></image>
          </view>
          <text class="method-name">{{method.name}}</text>
        </view>
        <view class="method-switch">
          <switch :checked="method.enabled" @change="(e) => toggleMethod(index, e)" color="#6B0FBE" />
        </view>
      </view>
      
      <view class="method-tip">
        <text class="tip-text">至少需要开启一种提现方式</text>
      </view>
    </view>
    
    <!-- 提现说明卡片 -->
    <view class="description-card">
      <view class="card-header">
        <text class="card-title">提现说明</text>
      </view>
      
      <view class="form-item">
        <textarea v-model="withdrawalDescription" class="form-textarea" placeholder="请输入提现说明，将在用户提现页面显示"></textarea>
      </view>
    </view>
    
    <!-- 保存按钮 -->
    <view class="button-container">
      <button class="save-button" @click="saveSettings">保存设置</button>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted } from 'vue';

// 最低提现金额
const minWithdrawalAmount = ref('50');

// 提现手续费
const withdrawalFee = ref('0');

// 提现到账时间选项
const timeOptions = {
  'realtime': '实时到账',
  'T1': 'T+1天到账',
  'T2': 'T+2天到账',
  'T3': 'T+3天到账'
};

// 选中的到账时间
const selectedTime = ref('T1');

// 是否自动审核
const autoApprove = ref(false);

// 提现方式
const withdrawalMethods = ref([
  {
    id: 'wechat',
    name: '微信零钱',
    icon: '/static/images/payment/wechat.png',
    enabled: true
  },
  {
    id: 'alipay',
    name: '支付宝',
    icon: '/static/images/payment/alipay.png',
    enabled: true
  },
  {
    id: 'bank',
    name: '银行卡',
    icon: '/static/images/payment/bank.png',
    enabled: false
  }
]);

// 提现说明
const withdrawalDescription = ref('1. 提现金额必须大于等于最低提现金额\n2. 提现申请提交后将在1-3个工作日内处理\n3. 如有疑问，请联系客服');

// 页面加载
onMounted(() => {
  // 获取提现设置
  getWithdrawalSettings();
});

// 获取提现设置
const getWithdrawalSettings = () => {
  // 这里应该从API获取设置
  // 暂时使用模拟数据
};

// 显示到账时间选项
const showTimeOptions = () => {
  const options = Object.values(timeOptions);
  
  uni.showActionSheet({
    itemList: options,
    success: (res) => {
      const keys = Object.keys(timeOptions);
      selectedTime.value = keys[res.tapIndex];
    }
  });
};

// 切换自动审核
const toggleAutoApprove = (e) => {
  autoApprove.value = e.detail.value;
};

// 切换提现方式
const toggleMethod = (index, e) => {
  withdrawalMethods.value[index].enabled = e.detail.value;
  
  // 检查是否至少有一种提现方式开启
  const enabledMethods = withdrawalMethods.value.filter(method => method.enabled);
  if (enabledMethods.length === 0) {
    uni.showToast({
      title: '至少需要开启一种提现方式',
      icon: 'none'
    });
    withdrawalMethods.value[index].enabled = true;
  }
};

// 保存设置
const saveSettings = () => {
  // 验证输入
  if (!minWithdrawalAmount.value || Number(minWithdrawalAmount.value) <= 0) {
    uni.showToast({
      title: '请输入有效的最低提现金额',
      icon: 'none'
    });
    return;
  }
  
  if (!withdrawalFee.value || Number(withdrawalFee.value) < 0 || Number(withdrawalFee.value) > 100) {
    uni.showToast({
      title: '请输入有效的提现手续费比例',
      icon: 'none'
    });
    return;
  }
  
  // 这里应该调用API保存设置
  uni.showLoading({
    title: '保存中...'
  });
  
  setTimeout(() => {
    uni.hideLoading();
    uni.showToast({
      title: '保存成功',
      icon: 'success'
    });
    
    // 返回上一页
    setTimeout(() => {
      uni.navigateBack();
    }, 1500);
  }, 1000);
};

// 返回上一页
const goBack = () => {
  uni.navigateBack();
};

// 显示帮助
const showHelp = () => {
  uni.showModal({
    title: '提现设置帮助',
    content: '您可以设置分销员的提现规则，包括最低提现金额、手续费、到账时间和提现方式等。',
    showCancel: false
  });
};
</script>

<style lang="scss">
.withdrawal-container {
  min-height: 100vh;
  background-color: #F5F7FA;
  padding-bottom: env(safe-area-inset-bottom);
}

/* 导航栏样式 */
.navbar {
  position: sticky;
  top: 0;
  left: 0;
  right: 0;
  background: linear-gradient(135deg, #A764CA, #6B0FBE);
  color: #fff;
  padding: 44px 16px 15px;
  display: flex;
  align-items: center;
  z-index: 100;
  box-shadow: 0 2px 10px rgba(107, 15, 190, 0.15);
}

.navbar-back {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-icon {
  width: 12px;
  height: 12px;
  border-left: 2px solid #fff;
  border-bottom: 2px solid #fff;
  transform: rotate(45deg);
}

.navbar-title {
  flex: 1;
  text-align: center;
  font-size: 18px;
  font-weight: 600;
  letter-spacing: 0.5px;
}

.navbar-right {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.help-icon {
  width: 24px;
  height: 24px;
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: bold;
}

/* 卡片样式 */
.settings-card, .methods-card, .description-card {
  margin: 16px;
  background-color: #FFFFFF;
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.04);
}

.card-header {
  padding: 16px;
  border-bottom: 1px solid #F0F0F0;
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

/* 表单项样式 */
.form-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #F0F0F0;
}

.form-item:last-child {
  border-bottom: none;
}

.form-label {
  font-size: 15px;
  color: #333;
}

.form-input-group {
  display: flex;
  align-items: center;
  background-color: #F5F7FA;
  border-radius: 8px;
  padding: 0 12px;
  height: 36px;
}

.input-prefix {
  font-size: 15px;
  color: #333;
  margin-right: 4px;
}

.input-suffix {
  font-size: 15px;
  color: #333;
  margin-left: 4px;
}

.form-input {
  height: 36px;
  width: 80px;
  font-size: 15px;
  color: #333;
  text-align: right;
}

.form-select {
  display: flex;
  align-items: center;
  background-color: #F5F7FA;
  border-radius: 8px;
  padding: 0 12px;
  height: 36px;
}

.select-value {
  font-size: 14px;
  color: #333;
  margin-right: 8px;
}

.arrow-icon {
  width: 8px;
  height: 8px;
  border-top: 2px solid #CCCCCC;
  border-right: 2px solid #CCCCCC;
  transform: rotate(45deg);
}

.arrow-icon.down {
  transform: rotate(135deg);
}

.form-switch {
  height: 36px;
  display: flex;
  align-items: center;
}

.form-textarea {
  width: 100%;
  height: 120px;
  background-color: #F5F7FA;
  border-radius: 8px;
  padding: 12px;
  font-size: 14px;
  color: #333;
}

/* 提现方式样式 */
.method-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #F0F0F0;
}

.method-left {
  display: flex;
  align-items: center;
}

.method-icon {
  width: 40px;
  height: 40px;
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  background-color: #F5F7FA;
}

.method-image {
  width: 24px;
  height: 24px;
}

.method-name {
  font-size: 15px;
  color: #333;
}

.method-switch {
  height: 36px;
  display: flex;
  align-items: center;
}

.method-tip {
  padding: 0 16px 16px;
}

.tip-text {
  font-size: 12px;
  color: #999;
}

/* 按钮样式 */
.button-container {
  margin: 24px 16px;
}

.save-button {
  height: 44px;
  border-radius: 22px;
  background: linear-gradient(135deg, #A764CA, #6B0FBE);
  color: #FFFFFF;
  font-size: 16px;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
}
</style>