{"version": 3, "file": "houseRent.js", "sources": ["mock/publish/houseRent.js"], "sourcesContent": ["// 房屋出租详情模拟数据\r\nexport const houseRentDetail = {\r\n  id: 'rent12345',\r\n  title: '市中心精装两室一厅出租',\r\n  price: '1800元/月',\r\n  tags: ['两室一厅', '精装修', '拎包入住', '交通便利', '家电齐全'],\r\n  publishTime: Date.now() - 86400000 * 2, // 2天前\r\n  images: [\r\n    '/static/images/house1.jpg',\r\n    '/static/images/house2.jpg',\r\n    '/static/images/house3.jpg',\r\n    '/static/images/house4.jpg'\r\n  ],\r\n  houseType: '2室1厅1卫',\r\n  area: '85平方米',\r\n  floor: '6楼/18楼',\r\n  orientation: '南北通透',\r\n  decoration: '精装修',\r\n  community: '阳光花园小区',\r\n  address: '磁县中心城区建设路与振兴路交叉口',\r\n  rentType: '整租',\r\n  paymentMethod: '押一付三',\r\n  availableDate: '随时入住',\r\n  features: ['家电齐全', '拎包入住', '南北通透', '采光好', '交通便利', '配套齐全', '有电梯', '有暖气'],\r\n  facilities: ['冰箱', '洗衣机', '电视', '热水器', '空调', '宽带', '衣柜', '床', '沙发', '餐桌'],\r\n  description: '房屋位于市中心地段，交通便利，周边配套设施齐全，有超市、医院、学校等。\\n\\n小区环境优美，绿化率高，安保严格，居住舒适安全。\\n\\n房屋南北通透，采光好，通风良好。家具家电齐全，拎包即可入住。\\n\\n适合小家庭或白领租住，看房方便，欢迎随时联系。',\r\n  requirements: '要求租户爱护房屋设施，不能养宠物，不能进行装修改造，不能从事违法活动。',\r\n  surroundings: {\r\n    traffic: '距离公交站200米，有10路、23路、35路公交车',\r\n    education: '周边有磁县第一小学、磁县第三中学',\r\n    medical: '附近有磁县人民医院',\r\n    shopping: '周边有万家超市、农贸市场',\r\n    entertainment: '附近有健身房、影院'\r\n  },\r\n  publisher: {\r\n    id: 'user12345',\r\n    name: '王先生',\r\n    avatar: '/static/images/avatar.png',\r\n    type: '个人房东',\r\n    isVerified: true\r\n  },\r\n  contact: {\r\n    name: '王先生',\r\n    phone: '13987654321'\r\n  }\r\n};\r\n\r\n// 相关房源推荐数据\r\nexport const relatedHouses = [\r\n  {\r\n    id: 'house001',\r\n    title: '阳光花园一室一厅精装修',\r\n    price: '1200元/月',\r\n    area: '50平方米',\r\n    houseType: '1室1厅1卫',\r\n    image: '/static/images/house1.jpg',\r\n    tags: ['精装修', '家电齐全', '拎包入住']\r\n  },\r\n  {\r\n    id: 'house002',\r\n    title: '市中心三室两厅出租',\r\n    price: '2500元/月',\r\n    area: '120平方米',\r\n    houseType: '3室2厅2卫',\r\n    image: '/static/images/house2.jpg',\r\n    tags: ['南北通透', '交通便利', '配套齐全']\r\n  },\r\n  {\r\n    id: 'house003',\r\n    title: '学区房两室一厅出租',\r\n    price: '1600元/月',\r\n    area: '75平方米',\r\n    houseType: '2室1厅1卫',\r\n    image: '/static/images/house3.jpg',\r\n    tags: ['学区房', '精装修', '拎包入住']\r\n  }\r\n];\r\n\r\n// 获取房屋出租详情的API函数\r\nexport const fetchHouseRentDetail = (id) => {\r\n  return new Promise((resolve) => {\r\n    setTimeout(() => {\r\n      resolve(houseRentDetail);\r\n    }, 300);\r\n  });\r\n};\r\n\r\n// 获取相关房源的API函数\r\nexport const fetchRelatedHouses = () => {\r\n  return new Promise((resolve) => {\r\n    setTimeout(() => {\r\n      resolve(relatedHouses);\r\n    }, 300);\r\n  });\r\n}; "], "names": [], "mappings": ";AACO,MAAM,kBAAkB;AAAA,EAC7B,IAAI;AAAA,EACJ,OAAO;AAAA,EACP,OAAO;AAAA,EACP,MAAM,CAAC,QAAQ,OAAO,QAAQ,QAAQ,MAAM;AAAA,EAC5C,aAAa,KAAK,IAAK,IAAG,QAAW;AAAA;AAAA,EACrC,QAAQ;AAAA,IACN;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACD;AAAA,EACD,WAAW;AAAA,EACX,MAAM;AAAA,EACN,OAAO;AAAA,EACP,aAAa;AAAA,EACb,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,SAAS;AAAA,EACT,UAAU;AAAA,EACV,eAAe;AAAA,EACf,eAAe;AAAA,EACf,UAAU,CAAC,QAAQ,QAAQ,QAAQ,OAAO,QAAQ,QAAQ,OAAO,KAAK;AAAA,EACtE,YAAY,CAAC,MAAM,OAAO,MAAM,OAAO,MAAM,MAAM,MAAM,KAAK,MAAM,IAAI;AAAA,EACxE,aAAa;AAAA,EACb,cAAc;AAAA,EACd,cAAc;AAAA,IACZ,SAAS;AAAA,IACT,WAAW;AAAA,IACX,SAAS;AAAA,IACT,UAAU;AAAA,IACV,eAAe;AAAA,EAChB;AAAA,EACD,WAAW;AAAA,IACT,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,MAAM;AAAA,IACN,YAAY;AAAA,EACb;AAAA,EACD,SAAS;AAAA,IACP,MAAM;AAAA,IACN,OAAO;AAAA,EACR;AACH;AAGO,MAAM,gBAAgB;AAAA,EAC3B;AAAA,IACE,IAAI;AAAA,IACJ,OAAO;AAAA,IACP,OAAO;AAAA,IACP,MAAM;AAAA,IACN,WAAW;AAAA,IACX,OAAO;AAAA,IACP,MAAM,CAAC,OAAO,QAAQ,MAAM;AAAA,EAC7B;AAAA,EACD;AAAA,IACE,IAAI;AAAA,IACJ,OAAO;AAAA,IACP,OAAO;AAAA,IACP,MAAM;AAAA,IACN,WAAW;AAAA,IACX,OAAO;AAAA,IACP,MAAM,CAAC,QAAQ,QAAQ,MAAM;AAAA,EAC9B;AAAA,EACD;AAAA,IACE,IAAI;AAAA,IACJ,OAAO;AAAA,IACP,OAAO;AAAA,IACP,MAAM;AAAA,IACN,WAAW;AAAA,IACX,OAAO;AAAA,IACP,MAAM,CAAC,OAAO,OAAO,MAAM;AAAA,EAC5B;AACH;AAGY,MAAC,uBAAuB,CAAC,OAAO;AAC1C,SAAO,IAAI,QAAQ,CAAC,YAAY;AAC9B,eAAW,MAAM;AACf,cAAQ,eAAe;AAAA,IACxB,GAAE,GAAG;AAAA,EACV,CAAG;AACH;AAGY,MAAC,qBAAqB,MAAM;AACtC,SAAO,IAAI,QAAQ,CAAC,YAAY;AAC9B,eAAW,MAAM;AACf,cAAQ,aAAa;AAAA,IACtB,GAAE,GAAG;AAAA,EACV,CAAG;AACH;;;"}