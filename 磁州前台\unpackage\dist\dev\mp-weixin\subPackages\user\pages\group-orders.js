"use strict";
const common_vendor = require("../../../common/vendor.js");
const common_assets = require("../../../common/assets.js");
const _sfc_main = {
  __name: "group-orders",
  setup(__props) {
    const statusBarHeight = common_vendor.ref(20);
    const currentTab = common_vendor.ref(0);
    const tabs = common_vendor.ref([
      { name: "全部" },
      { name: "待付款" },
      { name: "待发货" },
      { name: "待收货" },
      { name: "已完成" }
    ]);
    const ordersList = common_vendor.ref([]);
    const page = common_vendor.ref(1);
    const limit = common_vendor.ref(10);
    const loading = common_vendor.ref(false);
    const refreshing = common_vendor.ref(false);
    const loadAll = common_vendor.ref(false);
    common_vendor.onMounted(() => {
      const sysInfo = common_vendor.index.getSystemInfoSync();
      statusBarHeight.value = sysInfo.statusBarHeight;
      loadOrders();
    });
    const goBack = () => {
      common_vendor.index.navigateBack();
    };
    const switchTab = (index) => {
      if (currentTab.value !== index) {
        currentTab.value = index;
        refresh();
      }
    };
    const getStatusText = (status) => {
      switch (status) {
        case 1:
          return "待付款";
        case 2:
          return "待发货";
        case 3:
          return "待收货";
        case 4:
          return "已完成";
        case 5:
          return "已取消";
        default:
          return "未知状态";
      }
    };
    const loadOrders = () => {
      if (loading.value || loadAll.value)
        return;
      loading.value = true;
      setTimeout(() => {
        ({
          page: page.value,
          limit: limit.value,
          status: currentTab.value === 0 ? "" : currentTab.value
        });
        const mockData = getMockOrdersData();
        let filteredData = mockData;
        if (currentTab.value > 0) {
          filteredData = mockData.filter((item) => item.status === currentTab.value);
        }
        if (page.value === 1) {
          ordersList.value = filteredData;
        } else {
          ordersList.value = [...ordersList.value, ...filteredData];
        }
        loadAll.value = true;
        loading.value = false;
        refreshing.value = false;
        if (!loadAll.value) {
          page.value++;
        }
      }, 800);
    };
    const refresh = () => {
      refreshing.value = true;
      page.value = 1;
      loadAll.value = false;
      loadOrders();
    };
    const loadMore = () => {
      loadOrders();
    };
    const viewOrderDetail = (order) => {
      common_vendor.index.navigateTo({
        url: `/pages/groupbuy/order-detail?id=${order.id}`
      });
    };
    const cancelOrder = (order) => {
      common_vendor.index.showModal({
        title: "提示",
        content: "确定要取消该订单吗？",
        success: (res) => {
          if (res.confirm) {
            common_vendor.index.showLoading({
              title: "处理中..."
            });
            setTimeout(() => {
              common_vendor.index.hideLoading();
              const index = ordersList.value.findIndex((item) => item.id === order.id);
              if (index !== -1) {
                ordersList.value[index].status = 5;
              }
              common_vendor.index.showToast({
                title: "订单已取消",
                icon: "success"
              });
            }, 500);
          }
        }
      });
    };
    const payOrder = (order) => {
      common_vendor.index.navigateTo({
        url: `/pages/pay/index?orderId=${order.id}&amount=${order.totalAmount}`
      });
    };
    const checkDelivery = (order) => {
      common_vendor.index.showToast({
        title: "暂无配送信息",
        icon: "none"
      });
    };
    const confirmReceive = (order) => {
      common_vendor.index.showModal({
        title: "提示",
        content: "确认已收到商品吗？",
        success: (res) => {
          if (res.confirm) {
            common_vendor.index.showLoading({
              title: "处理中..."
            });
            setTimeout(() => {
              common_vendor.index.hideLoading();
              const index = ordersList.value.findIndex((item) => item.id === order.id);
              if (index !== -1) {
                ordersList.value[index].status = 4;
              }
              common_vendor.index.showToast({
                title: "确认收货成功",
                icon: "success"
              });
            }, 500);
          }
        }
      });
    };
    const writeReview = (order) => {
      common_vendor.index.navigateTo({
        url: `/pages/review/write?orderId=${order.id}`
      });
    };
    const deleteOrder = (order) => {
      common_vendor.index.showModal({
        title: "提示",
        content: "确定要删除该订单吗？",
        success: (res) => {
          if (res.confirm) {
            common_vendor.index.showLoading({
              title: "处理中..."
            });
            setTimeout(() => {
              common_vendor.index.hideLoading();
              const index = ordersList.value.findIndex((item) => item.id === order.id);
              if (index !== -1) {
                ordersList.value.splice(index, 1);
              }
              common_vendor.index.showToast({
                title: "删除成功",
                icon: "success"
              });
            }, 500);
          }
        }
      });
    };
    const contactShop = (order) => {
      common_vendor.index.makePhoneCall({
        phoneNumber: order.shopPhone || "10086",
        fail: () => {
          common_vendor.index.showToast({
            title: "拨打电话失败",
            icon: "none"
          });
        }
      });
    };
    const goToGroupBuy = () => {
      common_vendor.index.navigateTo({
        url: "/subPackages/activity-showcase/pages/group-buy/index"
      });
    };
    const getMockOrdersData = () => {
      return [
        {
          id: "1001",
          shopName: "磁州烧饼店",
          shopAvatar: "/static/images/shop/shop1.jpg",
          shopPhone: "13812345678",
          goodsName: "正宗磁州烧饼 买二送一",
          goodsImage: "/static/images/product/food1.jpg",
          goodsSpec: "原味 3个装",
          price: 19.9,
          count: 1,
          totalAmount: 19.9,
          status: 1,
          // 待付款
          createTime: "2023-09-15 14:30"
        },
        {
          id: "1002",
          shopName: "水果鲜生",
          shopAvatar: "/static/images/shop/shop2.jpg",
          shopPhone: "13998765432",
          goodsName: "精品水果礼盒 新鲜当季水果",
          goodsImage: "/static/images/product/food2.jpg",
          goodsSpec: "精品混合装 3kg",
          price: 59.9,
          count: 1,
          totalAmount: 59.9,
          status: 2,
          // 待发货
          createTime: "2023-09-14 10:15"
        },
        {
          id: "1003",
          shopName: "老北京小吃",
          shopAvatar: "/static/images/shop/shop3.jpg",
          shopPhone: "13756781234",
          goodsName: "手工制作老北京糖葫芦",
          goodsImage: "/static/images/product/food3.jpg",
          goodsSpec: "山楂味 10串",
          price: 15.9,
          count: 2,
          totalAmount: 31.8,
          status: 3,
          // 待收货
          createTime: "2023-09-13 16:45"
        },
        {
          id: "1004",
          shopName: "磁州特色小吃",
          shopAvatar: "/static/images/shop/shop4.jpg",
          shopPhone: "13612345678",
          goodsName: "特色小吃套餐 多种口味",
          goodsImage: "/static/images/product/food4.jpg",
          goodsSpec: "经典6件套",
          price: 39.9,
          count: 1,
          totalAmount: 39.9,
          status: 4,
          // 已完成
          createTime: "2023-09-10 09:20"
        },
        {
          id: "1005",
          shopName: "磁州烧饼店",
          shopAvatar: "/static/images/shop/shop1.jpg",
          shopPhone: "13812345678",
          goodsName: "正宗磁州烧饼 买二送一",
          goodsImage: "/static/images/product/food1.jpg",
          goodsSpec: "芝麻味 3个装",
          price: 21.9,
          count: 1,
          totalAmount: 21.9,
          status: 5,
          // 已取消
          createTime: "2023-09-08 11:30"
        }
      ];
    };
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_assets._imports_0$5,
        b: common_vendor.o(goBack),
        c: statusBarHeight.value + "px",
        d: common_vendor.f(tabs.value, (tab, index, i0) => {
          return {
            a: common_vendor.t(tab.name),
            b: index,
            c: currentTab.value === index ? 1 : "",
            d: common_vendor.o(($event) => switchTab(index), index)
          };
        }),
        e: statusBarHeight.value + 44 + "px",
        f: ordersList.value.length > 0
      }, ordersList.value.length > 0 ? {
        g: common_vendor.f(ordersList.value, (item, k0, i0) => {
          return common_vendor.e({
            a: item.shopAvatar,
            b: common_vendor.t(item.shopName),
            c: common_vendor.t(getStatusText(item.status)),
            d: common_vendor.n("status-" + item.status),
            e: item.goodsImage,
            f: common_vendor.t(item.goodsName),
            g: item.goodsSpec
          }, item.goodsSpec ? {
            h: common_vendor.t(item.goodsSpec)
          } : {}, {
            i: common_vendor.t(item.price),
            j: common_vendor.t(item.count),
            k: common_vendor.t(item.createTime),
            l: common_vendor.t(item.count),
            m: common_vendor.t(item.totalAmount),
            n: item.status === 1
          }, item.status === 1 ? {
            o: common_vendor.o(($event) => cancelOrder(item), item.id)
          } : {}, {
            p: item.status === 1
          }, item.status === 1 ? {
            q: common_vendor.o(($event) => payOrder(item), item.id)
          } : {}, {
            r: item.status === 2
          }, item.status === 2 ? {
            s: common_vendor.o(($event) => checkDelivery(), item.id)
          } : {}, {
            t: item.status === 3
          }, item.status === 3 ? {
            v: common_vendor.o(($event) => confirmReceive(item), item.id)
          } : {}, {
            w: item.status === 4
          }, item.status === 4 ? {
            x: common_vendor.o(($event) => writeReview(item), item.id)
          } : {}, {
            y: item.status === 4 || item.status === 5
          }, item.status === 4 || item.status === 5 ? {
            z: common_vendor.o(($event) => deleteOrder(item), item.id)
          } : {}, {
            A: common_vendor.o(($event) => contactShop(item), item.id),
            B: item.id,
            C: common_vendor.o(($event) => viewOrderDetail(item), item.id)
          });
        }),
        h: common_assets._imports_1$15
      } : {}, {
        i: ordersList.value.length === 0 && !loading.value
      }, ordersList.value.length === 0 && !loading.value ? {
        j: common_assets._imports_2$10,
        k: common_vendor.o(goToGroupBuy)
      } : {}, {
        l: loading.value && !refreshing.value
      }, loading.value && !refreshing.value ? {} : {}, {
        m: loadAll.value && ordersList.value.length > 0
      }, loadAll.value && ordersList.value.length > 0 ? {} : {}, {
        n: common_vendor.o(loadMore),
        o: refreshing.value,
        p: common_vendor.o(refresh)
      });
    };
  }
};
wx.createPage(_sfc_main);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/subPackages/user/pages/group-orders.js.map
