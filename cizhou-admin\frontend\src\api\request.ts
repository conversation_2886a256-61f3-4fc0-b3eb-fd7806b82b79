import axios from 'axios'
import type { AxiosInstance, AxiosRequestConfig, AxiosResponse, AxiosError } from 'axios'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useUserStore } from '@/stores/user'
import { useAppStore } from '@/stores/app'
import { getToken, removeToken, isTokenExpired } from '@/utils/auth'
import router from '@/router'

// 响应数据接口
export interface ApiResponse<T = any> {
  code: number
  message: string
  data: T
  timestamp: number
  traceId?: string
}

// 请求配置接口
export interface RequestConfig extends AxiosRequestConfig {
  skipAuth?: boolean // 跳过认证
  skipErrorHandler?: boolean // 跳过错误处理
  showLoading?: boolean // 显示加载状态
  loadingText?: string // 加载文本
}

// 创建axios实例
const createAxiosInstance = (): AxiosInstance => {
  const instance = axios.create({
    baseURL: import.meta.env.VITE_API_BASE_URL || '/api',
    timeout: 30000,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })

  return instance
}

// 请求拦截器
const setupRequestInterceptor = (instance: AxiosInstance): void => {
  instance.interceptors.request.use(
    (config: any) => {
      const appStore = useAppStore()
      
      // 显示加载状态
      if (config.showLoading) {
        appStore.setGlobalLoading(true)
      }
      
      // 添加认证token
      if (!config.skipAuth) {
        const token = getToken()
        if (token) {
          // 检查token是否即将过期
          if (isTokenExpired(token)) {
            // Token即将过期，尝试刷新
            const userStore = useUserStore()
            userStore.refreshToken().catch(() => {
              // 刷新失败，跳转到登录页
              userStore.logout()
              router.push('/login')
            })
          } else {
            config.headers.Authorization = `Bearer ${token}`
          }
        }
      }
      
      // 添加请求ID用于追踪
      config.headers['X-Request-ID'] = generateRequestId()
      
      // 添加时间戳防止缓存
      if (config.method === 'get') {
        config.params = {
          ...config.params,
          _t: Date.now()
        }
      }
      
      return config
    },
    (error: AxiosError) => {
      const appStore = useAppStore()
      appStore.setGlobalLoading(false)
      
      console.error('请求拦截器错误:', error)
      return Promise.reject(error)
    }
  )
}

// 响应拦截器
const setupResponseInterceptor = (instance: AxiosInstance): void => {
  instance.interceptors.response.use(
    (response: AxiosResponse<ApiResponse>) => {
      const appStore = useAppStore()
      appStore.setGlobalLoading(false)
      
      const { data } = response
      
      // 检查业务状态码
      if (data.code === 200) {
        return response
      } else if (data.code === 401) {
        // 未认证，清除token并跳转到登录页
        handleUnauthorized()
        return Promise.reject(new Error(data.message || '未认证'))
      } else if (data.code === 403) {
        // 无权限
        ElMessage.error(data.message || '无权限访问')
        return Promise.reject(new Error(data.message || '无权限访问'))
      } else {
        // 其他业务错误
        if (!response.config.skipErrorHandler) {
          ElMessage.error(data.message || '请求失败')
        }
        return Promise.reject(new Error(data.message || '请求失败'))
      }
    },
    (error: AxiosError) => {
      const appStore = useAppStore()
      appStore.setGlobalLoading(false)
      
      // 处理网络错误
      if (!error.response) {
        ElMessage.error('网络连接失败，请检查网络设置')
        return Promise.reject(error)
      }
      
      const { status, data } = error.response as AxiosResponse<ApiResponse>
      
      switch (status) {
        case 400:
          ElMessage.error(data?.message || '请求参数错误')
          break
        case 401:
          handleUnauthorized()
          break
        case 403:
          ElMessage.error('无权限访问')
          break
        case 404:
          ElMessage.error('请求的资源不存在')
          break
        case 500:
          ElMessage.error('服务器内部错误')
          break
        case 502:
          ElMessage.error('网关错误')
          break
        case 503:
          ElMessage.error('服务暂时不可用')
          break
        case 504:
          ElMessage.error('网关超时')
          break
        default:
          ElMessage.error(data?.message || `请求失败 (${status})`)
      }
      
      return Promise.reject(error)
    }
  )
}

// 处理未认证情况
const handleUnauthorized = (): void => {
  const userStore = useUserStore()
  
  ElMessageBox.confirm(
    '登录状态已过期，请重新登录',
    '系统提示',
    {
      confirmButtonText: '重新登录',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    userStore.logout().then(() => {
      router.push('/login')
    })
  }).catch(() => {
    // 用户取消，也需要清除token
    removeToken()
  })
}

// 生成请求ID
const generateRequestId = (): string => {
  return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
}

// 创建请求实例
const request = createAxiosInstance()

// 设置拦截器
setupRequestInterceptor(request)
setupResponseInterceptor(request)

// 封装请求方法
export const http = {
  get<T = any>(url: string, config?: RequestConfig): Promise<ApiResponse<T>> {
    return request.get(url, config).then(res => res.data)
  },
  
  post<T = any>(url: string, data?: any, config?: RequestConfig): Promise<ApiResponse<T>> {
    return request.post(url, data, config).then(res => res.data)
  },
  
  put<T = any>(url: string, data?: any, config?: RequestConfig): Promise<ApiResponse<T>> {
    return request.put(url, data, config).then(res => res.data)
  },
  
  delete<T = any>(url: string, config?: RequestConfig): Promise<ApiResponse<T>> {
    return request.delete(url, config).then(res => res.data)
  },
  
  patch<T = any>(url: string, data?: any, config?: RequestConfig): Promise<ApiResponse<T>> {
    return request.patch(url, data, config).then(res => res.data)
  },
  
  upload<T = any>(url: string, formData: FormData, config?: RequestConfig): Promise<ApiResponse<T>> {
    return request.post(url, formData, {
      ...config,
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    }).then(res => res.data)
  },
  
  download(url: string, config?: RequestConfig): Promise<Blob> {
    return request.get(url, {
      ...config,
      responseType: 'blob'
    }).then(res => res.data)
  }
}

export default http
