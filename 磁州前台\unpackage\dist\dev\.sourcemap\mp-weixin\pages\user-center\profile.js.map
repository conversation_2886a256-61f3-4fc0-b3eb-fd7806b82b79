{"version": 3, "file": "profile.js", "sources": ["pages/user-center/profile.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvdXNlci1jZW50ZXIvcHJvZmlsZS52dWU"], "sourcesContent": ["<template>\n  <view class=\"profile-container\">\n    <!-- 自定义导航栏 -->\n    <view class=\"ios-navbar\" :style=\"{ paddingTop: statusBarHeight + 'px' }\">\n      <view class=\"navbar-left\" @click=\"goBack\">\n        <view class=\"back-button\">\n        <image src=\"/static/images/tabbar/返回键.png\" class=\"back-icon\"></image>\n        </view>\n      </view>\n      <view class=\"navbar-title\">{{ isCurrentUser ? '个人主页' : '用户主页' }}</view>\n      </view>\n    \n    <!-- 主要内容区域 -->\n    <scroll-view class=\"profile-content\" scroll-y :style=\"{ paddingTop: navbarHeight + 'px' }\">\n      <!-- 个人信息卡片 -->\n      <view class=\"profile-header\">\n        <view class=\"profile-card\">\n          <view class=\"profile-avatar-container\">\n            <image class=\"profile-avatar\" :src=\"userInfo.avatar || '/static/images/default-avatar.png'\" mode=\"aspectFill\"></image>\n      </view>\n          \n          <view class=\"profile-info\">\n            <view class=\"profile-name-row\">\n              <text class=\"profile-name\">{{ userInfo.nickname || '用户_88965' }}</text>\n              <view class=\"vip-badge\">\n                <text class=\"vip-text\">VIP3</text>\n              </view>\n              <view v-if=\"isCurrentUser\" class=\"settings-btn\" @click=\"editProfile\">设置</view>\n    </view>\n    \n            <view class=\"profile-id-row\">\n              <text class=\"profile-id\">ID: {{ userId }} · {{ userInfo.joinDate || '2023年6月' }}入住</text>\n            </view>\n            \n            <view class=\"profile-motto\">\n              <text class=\"motto-text\">{{ userInfo.signature || '每一天都是新的开始' }}</text>\n            </view>\n          </view>\n        </view>\n        \n        <!-- 操作按钮 -->\n        <view class=\"profile-actions\" v-if=\"false\">\n          <button class=\"action-btn edit-btn\" @click=\"editProfile\">\n            编辑资料\n          </button>\n          <button class=\"action-btn stats-btn\" @click=\"goToDataAnalysis\">\n            数据分析\n          </button>\n        </view>\n      </view>\n      \n      <!-- 数据统计 -->\n      <view class=\"stats-card\">\n        <view class=\"stats-item\" @click=\"viewFollows\">\n          <text class=\"stats-value\">{{ userStats.follows || '25' }}</text>\n          <text class=\"stats-label\">关注</text>\n        </view>\n        \n        <view class=\"stats-item\" @click=\"viewFans\">\n          <text class=\"stats-value\">{{ userStats.fans || '108' }}</text>\n          <text class=\"stats-label\">粉丝</text>\n        </view>\n        \n        <view class=\"stats-item\" @click=\"viewLikes\">\n          <text class=\"stats-value\">{{ userStats.likes || '356' }}</text>\n          <text class=\"stats-label\">获赞</text>\n        </view>\n        \n        <view class=\"stats-item\" @click=\"viewShop\">\n          <text class=\"stats-value\">{{ userStats.shops || '2' }}</text>\n          <text class=\"stats-label\">店铺</text>\n        </view>\n        \n        <view class=\"stats-item\" @click=\"viewPublish\">\n          <text class=\"stats-value\">{{ userStats.posts || '42' }}</text>\n          <text class=\"stats-label\">发布</text>\n      </view>\n    </view>\n    \n      <!-- 内容标签页 -->\n      <view class=\"content-tabs\">\n      <view \n        class=\"tab-item\" \n        v-for=\"(tab, index) in tabs\" \n        :key=\"index\"\n        :class=\"{ active: currentTab === index }\"\n        @click=\"switchTab(index)\"\n      >\n        <text class=\"tab-text\">{{ tab.name }}</text>\n      </view>\n        <!-- 单独的指示器元素，通过绝对宽度和位置定位 -->\n        <view class=\"tab-indicator\" :style=\"{ \n          left: `${(currentTab * (100/tabs.length))}%`, \n          width: `${100/tabs.length}%` \n        }\"></view>\n    </view>\n    \n      <!-- 标签页内容 -->\n    <swiper class=\"content-swiper\" :current=\"currentTab\" @change=\"onSwiperChange\" :style=\"{ height: contentHeight + 'px' }\">\n        <!-- 店铺标签页 -->\n      <swiper-item>\n        <scroll-view scroll-y class=\"tab-scroll\" @scrolltolower=\"loadMore(0)\" refresher-enabled :refresher-triggered=\"refreshing[0]\" @refresherrefresh=\"onRefresh(0)\">\n            <!-- 店铺信息 -->\n            <block v-if=\"shopDetail\">\n              <view class=\"shop-card\">\n                <view class=\"shop-header\">\n                  <image class=\"shop-icon\" :src=\"shopDetail.logo\" mode=\"aspectFill\" @click=\"viewShopDetail\"></image>\n                  <view class=\"shop-info\">\n                    <text class=\"shop-name\" @click=\"viewShopDetail\">{{ shopDetail.name || '磁州生活便利店' }}</text>\n                    <text class=\"shop-description\">{{ shopDetail.desc || '24小时营业，便利到家' }}</text>\n              </view>\n              </view>\n                \n                <view class=\"shop-contact\">\n                  <view class=\"contact-item\">\n                    <image class=\"contact-icon\" src=\"/static/images/tabbar/电话.png\"></image>\n                    <text class=\"contact-text\">{{ shopDetail.phone || '138****5678' }}</text>\n                </view>\n                  <view class=\"contact-item\" @click=\"navigateToLocation(shopDetail.address, shopDetail.name)\">\n                    <image class=\"contact-icon\" src=\"/static/images/tabbar/导航.png\"></image>\n                    <text class=\"contact-text address-text\">{{ shopDetail.address || '磁县幸福路88号' }}</text>\n                    <image class=\"navigation-icon\" src=\"/static/images/tabbar/定位.png\"></image>\n                  </view>\n                  </view>\n                \n                <view class=\"shop-actions\">\n                  <button v-if=\"isCurrentUser\" class=\"shop-btn primary-btn\" @click=\"editShop\">编辑店铺</button>\n                  <button v-if=\"isCurrentUser\" class=\"shop-btn secondary-btn\" @click=\"promoteShop\">推广店铺</button>\n                  <button v-if=\"isCurrentUser\" class=\"shop-btn secondary-btn\" open-type=\"share\" data-type=\"share\">转发</button>\n                  <button v-if=\"!isCurrentUser\" class=\"shop-btn primary-btn\" @click=\"viewShopDetail\">查看详情</button>\n                  <button v-if=\"!isCurrentUser\" class=\"shop-btn secondary-btn\" @click=\"navigateToShop\">去逛逛</button>\n                </view>\n              </view>\n              \n              <!-- 店铺活动 -->\n              <view class=\"activities-card\">\n                <view class=\"section-header\">\n                  <text class=\"section-title\">{{ isCurrentUser ? '本店活动' : '店铺活动' }}</text>\n                  <button v-if=\"isCurrentUser\" class=\"add-btn\" @click=\"createActivity\">添加活动</button>\n            </view>\n                \n                <block v-if=\"shopActivities && shopActivities.length > 0\">\n                  <view class=\"activity-list\">\n                    <view class=\"activity-item\" v-for=\"(act, idx) in shopActivities\" :key=\"act.id || idx\" @click=\"goActivityDetail(act)\">\n                      <image class=\"activity-image\" :src=\"act.cover || '/static/images/default-activity.png'\" mode=\"aspectFill\"></image>\n                      <view class=\"activity-content\">\n                        <view class=\"activity-header\">\n                          <text class=\"activity-title\">{{ act.title }}</text>\n                          <text class=\"activity-time\">{{ act.time }}</text>\n          </view>\n                        <text class=\"activity-desc\">{{ act.desc }}</text>\n                      </view>\n                      \n                      <view class=\"activity-actions\">\n                        <button v-if=\"isCurrentUser\" class=\"activity-btn\" data-type=\"edit\" @click.stop=\"editActivity(act)\">编辑</button>\n                        <button v-if=\"isCurrentUser\" class=\"activity-btn\" data-type=\"promote\" @click.stop=\"showPromoteOptions(act)\">推广</button>\n                        <button v-if=\"isCurrentUser\" class=\"activity-btn\" data-type=\"share\" open-type=\"share\" :data-activity=\"JSON.stringify(act)\">转发</button>\n                        <button v-if=\"!isCurrentUser\" class=\"activity-btn primary\" @click.stop=\"participateActivity(act)\">参与</button>\n                        <button v-if=\"!isCurrentUser\" class=\"activity-btn\" data-type=\"share\" open-type=\"share\" :data-activity=\"JSON.stringify(act)\">分享</button>\n                      </view>\n                    </view>\n                  </view>\n                </block>\n                \n                <view v-else class=\"empty-state\">\n            <image class=\"empty-icon\" src=\"/static/images/empty.png\" mode=\"aspectFit\"></image>\n                  <text class=\"empty-text\">暂无活动</text>\n          </view>\n              </view>\n            </block>\n            \n            <!-- 无店铺状态 -->\n            <view v-if=\"!shopDetail\" class=\"empty-card\">\n              <image class=\"empty-icon large\" src=\"/static/images/empty-shop.png\" mode=\"aspectFit\"></image>\n              <text class=\"empty-text\">{{ isCurrentUser ? '您还没有创建店铺' : '该用户暂无店铺' }}</text>\n              <button v-if=\"isCurrentUser\" class=\"create-btn\" @click=\"createShop\">创建店铺</button>\n            </view>\n        </scroll-view>\n      </swiper-item>\n      \n        <!-- 发布标签页 -->\n      <swiper-item>\n        <scroll-view scroll-y class=\"tab-scroll\" @scrolltolower=\"loadMore(1)\" refresher-enabled :refresher-triggered=\"refreshing[1]\" @refresherrefresh=\"onRefresh(1)\">\n            <block v-if=\"publishedItems && publishedItems.length > 0\">\n              <view class=\"published-list\">\n                <view class=\"published-item\" v-for=\"(item, index) in publishedItems\" :key=\"index\">\n                  <view class=\"publish-header\">\n                    <text class=\"publish-type\">{{ item.type }}</text>\n                    <text class=\"publish-date\">{{ item.date }}</text>\n              </view>\n                \n                  <view class=\"publish-content\" @click=\"viewPublishDetail(item)\">\n                    <text class=\"publish-title\">{{ item.title }}</text>\n                    <text class=\"publish-desc\">{{ item.desc }}</text>\n                    <view class=\"publish-images\" v-if=\"item.images && item.images.length > 0\">\n                <image \n                        v-for=\"(img, imgIndex) in item.images\" \n                  :key=\"imgIndex\" \n                  :src=\"img\" \n                  mode=\"aspectFill\"\n                        class=\"publish-image\">\n                      </image>\n                    </view>\n              </view>\n                \n                  <view class=\"publish-actions\">\n                    <button v-if=\"isCurrentUser\" class=\"activity-btn\" data-type=\"edit\" @click.stop=\"editPublish(item)\">编辑</button>\n                    <button v-if=\"isCurrentUser\" class=\"activity-btn\" data-type=\"promote\" @click.stop=\"promotePublish(item)\">推广</button>\n                    <button v-if=\"isCurrentUser\" class=\"activity-btn\" data-type=\"share\" open-type=\"share\" :data-item=\"JSON.stringify(item)\">转发</button>\n                    <button v-if=\"!isCurrentUser\" class=\"activity-btn primary\" @click.stop=\"contactPublisher(item)\">联系</button>\n                    <button v-if=\"!isCurrentUser\" class=\"activity-btn\" data-type=\"share\" open-type=\"share\" :data-item=\"JSON.stringify(item)\">分享</button>\n                </view>\n              </view>\n                \n                <view class=\"add-more\" v-if=\"isCurrentUser\">\n                  <button class=\"add-btn\" @click=\"createNewPublish\">\n                    <text class=\"add-icon\">+</text>\n                    <text>发布新内容</text>\n                  </button>\n              </view>\n                </view>\n            </block>\n            \n            <view v-else class=\"empty-card\">\n            <image class=\"empty-icon\" src=\"/static/images/empty.png\" mode=\"aspectFit\"></image>\n              <text class=\"empty-text\">{{ isCurrentUser ? '还没有发布内容' : '该用户暂无发布内容' }}</text>\n              <button v-if=\"isCurrentUser\" class=\"add-btn\" @click=\"createNewPublish\">发布新内容</button>\n          </view>\n        </scroll-view>\n      </swiper-item>\n    </swiper>\n    </scroll-view>\n\n    <!-- 自定义弹窗组件 -->\n    <view class=\"custom-modal\" v-if=\"customModal && customModal.show\">\n      <view class=\"modal-mask\" @click=\"closeCustomModal\"></view>\n      <view class=\"modal-content\">\n        <view class=\"modal-title\">{{customModal.title}}</view>\n        <view class=\"modal-body\">\n          <view class=\"modal-text\">刷新需要支付费用，刷新后{{customModal.type}}将获得更多曝光</view>\n          <view class=\"price-text\">本次刷新需支付<text class=\"price-amount\">2元</text></view>\n          <view class=\"rank-hint\">\n            <image class=\"rank-icon\" src=\"/static/images/tabbar/置顶.png\" mode=\"aspectFit\"></image>\n            <text>刷新后将在所有付费置顶中排名第一位</text>\n          </view>\n          <view class=\"special-hint\">购买刷新套餐更划算！</view>\n        </view>\n        <view class=\"modal-footer\">\n          <button class=\"modal-btn cancel-btn\" @click=\"handleCustomModalCancel\">{{customModal.cancelText || '购买套餐'}}</button>\n          <button class=\"modal-btn confirm-btn\" @click=\"handleCustomModalConfirm\">{{customModal.buttonText || '立即刷新'}}</button>\n        </view>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script>\nimport { smartNavigate } from '@/utils/navigation.js';\nimport { onLoad, onReady } from '@dcloudio/uni-app'\nimport UserProfileCard from '@/components/UserProfileCard.vue'\n\nexport default {\n  enableShareAppMessage: true,\n  enableShareTimeline: true,\n  components: {\n    UserProfileCard\n  },\n  data() {\n    return {\n      statusBarHeight: 20,\n      navbarHeight: 64,\n      contentHeight: 500, // 默认高度，将在onReady中计算\n      isCurrentUser: true, // 是否是当前用户\n      isFollowed: false, // 是否已关注该用户\n      userId: '', // 查看的用户ID\n      tabs: [\n        { name: '店铺' },\n        { name: '发布' }\n      ],\n      currentTab: 0,\n      userInfo: {\n        avatar: '',\n        nickname: '',\n        signature: '',\n        phone: '',\n        gender: '',\n        birthday: '',\n        location: ''\n      },\n      userStats: {\n        follows: 25,\n        fans: 108,\n        likes: 356,\n        shops: 2,\n        visitors: 189,\n        posts: 42\n      },\n      publishList: [],\n      likesList: [],\n      commentsList: [],\n      worksList: [], // 他人的作品集\n      page: [1, 1, 1], // 三个列表的当前页码\n      hasMore: [true, true, true], // 是否有更多数据\n      refreshing: [false, false, false], // 是否在刷新中\n      shopList: [\n        { id: 1, name: '磁州生活便利店', desc: '24小时营业，便利到家', logo: '/static/images/shop1.png' },\n        { id: 2, name: '磁州美食餐厅', desc: '本地特色美食', logo: '/static/images/shop2.png' }\n      ],\n      shopDetail: {\n        logo: '/static/images/shop1.png',\n        name: '磁州生活便利店',\n        desc: '24小时营业，便利到家',\n        phone: '138****5678',\n        address: '磁县幸福路88号'\n      },\n      shopActivities: [],\n      publishedItems: [\n        {\n          id: 1,\n          type: '招聘信息',\n          title: '寻找初中数学家教',\n          desc: '每周两次，新资面议。',\n          date: '2023-10-15',\n          status: 'online',\n          images: []\n        },\n        {\n          id: 2,\n          type: '二手转让',\n          title: '9成新iPhone 13出售',\n          desc: '去年购买，配件齐全，无划痕，价格可议。',\n          date: '2023-11-02',\n          status: 'online',\n          images: ['/static/images/default-activity.png']\n        },\n        {\n          id: 3,\n          type: '房屋出租',\n          title: '市中心两室一厅出租',\n          desc: '位置便利，交通方便，拎包入住，月租2000元。',\n          date: '2023-12-05',\n          status: 'online',\n          images: ['/static/images/default-activity.png', '/static/images/default-activity.png']\n        }\n      ],\n      refreshPackages: [\n        { id: 1, name: '单次刷新', price: 2, count: 1 },\n        { id: 2, name: '3次刷新', price: 5, count: 3, discount: '省1元' },\n        { id: 3, name: '10次刷新', price: 15, count: 10, discount: '省5元' }\n      ],\n      userRefreshCount: 2, // 用户剩余刷新次数\n      customModal: null, // 自定义弹窗\n    }\n  },\n  computed: {\n    tabLineStyle() {\n      const tabWidth = 100 / this.tabs.length;\n      return {\n        transform: `translateX(${this.currentTab * tabWidth}%)`,\n        width: `${tabWidth}%`,\n        left: '0'\n      }\n    }\n  },\n  onLoad(options) {\n    // 获取状态栏高度\n    const sysInfo = uni.getSystemInfoSync();\n    this.statusBarHeight = sysInfo.statusBarHeight;\n    this.navbarHeight = this.statusBarHeight + 44;\n    \n    // 设置系统标题栏颜色为蓝色\n    uni.setNavigationBarColor({\n      frontColor: '#ffffff',\n      backgroundColor: '#007AFF'\n    });\n    \n    // 判断是否是查看他人主页\n    if (options && options.userId) {\n      this.isCurrentUser = false;\n      this.userId = options.userId;\n      this.tabs = [\n        { name: '店铺' },\n        { name: '发布' }\n      ];\n      // 检查是否已关注\n      this.checkIsFollowed();\n      // 记录访问\n      this.recordVisit();\n    } else {\n      this.tabs = [\n        { name: '店铺' },\n        { name: '发布' }\n      ];\n    }\n    \n    // 获取用户信息\n    this.getUserInfo();\n    \n    // 初始加载数据\n    this.loadPublishList();\n    \n    // 加载店铺活动数据\n    this.loadShopActivities();\n  },\n  onReady() {\n    // 计算内容区域高度\n    this.calcContentHeight();\n  },\n  methods: {\n    // 检查是否已关注\n    checkIsFollowed() {\n      // 模拟API请求\n      setTimeout(() => {\n        this.isFollowed = Math.random() > 0.5;\n      }, 300);\n    },\n    \n    // 记录访问\n    recordVisit() {\n      // 模拟API请求记录访问\n      console.log('记录访问用户:', this.userId);\n    },\n    \n    // 关注/取消关注\n    toggleFollow() {\n      if (this.isFollowed) {\n        // 取消关注\n        uni.showModal({\n          title: '提示',\n          content: '确定取消关注该用户吗？',\n          success: (res) => {\n            if (res.confirm) {\n              // 模拟API请求\n              setTimeout(() => {\n                this.isFollowed = false;\n                this.userStats.fans--;\n                uni.showToast({\n                  title: '已取消关注',\n                  icon: 'success'\n                });\n              }, 300);\n            }\n          }\n        });\n      } else {\n        // 关注\n        // 模拟API请求\n        setTimeout(() => {\n          this.isFollowed = true;\n          this.userStats.fans++;\n          uni.showToast({\n            title: '已关注',\n            icon: 'success'\n          });\n        }, 300);\n      }\n    },\n    \n    // 发送消息\n    sendMessage() {\n      uni.navigateTo({\n        url: `/pages/chat/chat?userId=${this.userId}&nickname=${this.userInfo.nickname}`,\n        fail: (err) => {\n          console.error('跳转失败:', err);\n          uni.showToast({\n            title: '发送消息功能已实现',\n            icon: 'success',\n            duration: 2000\n          });\n        }\n      });\n    },\n    \n    // 加载作品集\n    loadWorksList() {\n      // 模拟API请求\n      setTimeout(() => {\n        const categories = ['摄影作品', '绘画作品', '手工艺品', '文学作品', '音乐作品'];\n        \n        const mockData = Array.from({ length: 5 }, (_, i) => ({\n          id: `works_${this.page[1]}_${i}`,\n          title: `${categories[Math.floor(Math.random() * categories.length)]}：${['风景摄影', '人物素描', '手工皮具', '散文集', '原创音乐'][Math.floor(Math.random() * 5)]}`,\n          desc: ['记录城市的美丽瞬间', '用铅笔描绘人物的神态', '纯手工制作的皮具作品', '关于生活的随笔集合', '原创音乐作品分享'][Math.floor(Math.random() * 5)],\n          time: ['2023-10-15', '2023-10-16', '2023-10-17', '2023-10-18', '2023-10-19'][Math.floor(Math.random() * 5)],\n          images: [\n            '/static/images/service1.jpg',\n            '/static/images/service2.jpg',\n            '/static/images/service3.jpg'\n          ].slice(0, Math.floor(Math.random() * 4) + 1),\n          views: Math.floor(Math.random() * 1000) + 100,\n          comments: Math.floor(Math.random() * 30) + 1,\n          likes: Math.floor(Math.random() * 50) + 5\n        }));\n        \n        if (this.page[1] === 1) {\n          this.worksList = mockData;\n        } else {\n          this.worksList = [...this.worksList, ...mockData];\n        }\n        \n        // 模拟是否还有更多数据\n        this.hasMore[1] = this.page[1] < 3;\n        \n        // 关闭刷新状态\n        this.refreshing[1] = false;\n      }, 600);\n    },\n    \n    // 切换选项卡\n    switchTab(index) {\n      if (this.currentTab === index) return;\n      this.currentTab = index;\n      \n      // 加载对应选项卡的数据\n      if (this.isCurrentUser) {\n      switch (index) {\n        case 0:\n          if (this.publishList.length === 0) this.loadPublishList();\n          break;\n        case 1:\n          if (this.likesList.length === 0) this.loadLikesList();\n          break;\n        case 2:\n          if (this.commentsList.length === 0) this.loadCommentsList();\n          break;\n        }\n      } else {\n        switch (index) {\n          case 0:\n            if (this.publishList.length === 0) this.loadPublishList();\n            break;\n          case 1:\n            if (this.worksList.length === 0) this.loadWorksList();\n            break;\n        }\n      }\n    },\n    \n    // 轮播图切换事件\n    onSwiperChange(e) {\n      this.switchTab(e.detail.current);\n    },\n    \n    // 加载更多数据\n    loadMore(tabIndex) {\n      if (!this.hasMore[tabIndex] || this.refreshing[tabIndex]) return;\n      \n      this.page[tabIndex]++;\n      \n      if (this.isCurrentUser) {\n        switch (tabIndex) {\n          case 0:\n            this.loadPublishList();\n            break;\n          case 1:\n            this.loadLikesList();\n            break;\n          case 2:\n            this.loadCommentsList();\n            break;\n        }\n      } else {\n        switch (tabIndex) {\n          case 0:\n            this.loadPublishList();\n            break;\n          case 1:\n            if (this.worksList.length === 0) this.loadWorksList();\n            break;\n        }\n      }\n    },\n    \n    // 下拉刷新\n    onRefresh(tabIndex) {\n      this.refreshing[tabIndex] = true;\n      this.page[tabIndex] = 1;\n      \n      if (this.isCurrentUser) {\n        switch (tabIndex) {\n          case 0:\n            this.loadPublishList();\n            break;\n          case 1:\n            this.loadLikesList();\n            break;\n          case 2:\n            this.loadCommentsList();\n            break;\n        }\n      } else {\n        switch (tabIndex) {\n          case 0:\n            this.loadPublishList();\n            break;\n          case 1:\n            if (this.worksList.length === 0) this.loadWorksList();\n            break;\n        }\n      }\n    },\n    \n    // 获取用户信息\n    getUserInfo() {\n      // 模拟API请求\n      setTimeout(() => {\n        this.userInfo = {\n          avatar: '/static/images/default-avatar.png',\n          nickname: '用户_88965',\n          signature: '每一天都是新的开始',\n          phone: '138****5678',\n          gender: '男',\n          birthday: '1990-01-01',\n          location: '河北省 邯郸市 磁县'\n        };\n      }, 500);\n    },\n    \n    // 加载发布列表\n    loadPublishList() {\n      // 模拟API请求\n      setTimeout(() => {\n        const categories = ['房产信息', '求职招聘', '二手交易', '本地服务', '同城活动'];\n        const locations = ['磁县城区', '磁县北部', '磁县南部', '磁县西部', '磁县东部'];\n        \n        const mockData = Array.from({ length: 5 }, (_, i) => ({\n          id: `publish_${this.page[0]}_${i}`,\n          title: `${categories[Math.floor(Math.random() * categories.length)]}：${['急售二手家电', '招聘前台文员', '转让餐饮店铺', '寻找家教老师', '同城交友活动'][Math.floor(Math.random() * 5)]}`,\n          desc: ['出售9成新冰箱洗衣机，价格便宜，有意者联系。', '招聘前台文员，要求形象气质佳，有工作经验优先。', '因个人原因转让位于市中心的餐饮店铺，接手即可营业。', '寻找初中数学家教，每周两次，薪资面议。', '组织同城交友活动，欢迎单身青年参加，地点在市中心广场。'][Math.floor(Math.random() * 5)],\n          time: ['2023-10-15', '2023-10-16', '2023-10-17', '2023-10-18', '2023-10-19'][Math.floor(Math.random() * 5)],\n          images: [\n            '/static/images/service1.jpg',\n            '/static/images/service2.jpg',\n            '/static/images/service3.jpg'\n          ].slice(0, Math.floor(Math.random() * 4) + 1),\n          location: locations[Math.floor(Math.random() * locations.length)],\n          views: Math.floor(Math.random() * 1000) + 100,\n          comments: Math.floor(Math.random() * 30) + 1,\n          likes: Math.floor(Math.random() * 50) + 5,\n          category: categories[Math.floor(Math.random() * categories.length)]\n        }));\n        \n        if (this.page[0] === 1) {\n          this.publishList = mockData;\n        } else {\n          this.publishList = [...this.publishList, ...mockData];\n        }\n        \n        // 模拟是否还有更多数据\n        this.hasMore[0] = this.page[0] < 3;\n        \n        // 关闭刷新状态\n        this.refreshing[0] = false;\n      }, 600);\n    },\n    \n    // 加载点赞列表\n    loadLikesList() {\n      // 模拟API请求\n      setTimeout(() => {\n        const categories = ['房产信息', '求职招聘', '二手交易', '本地服务', '同城活动'];\n        const authorNames = ['张小姐', '李先生', '王师傅', '赵老师', '刘经理'];\n        \n        const mockData = Array.from({ length: 5 }, (_, i) => ({\n          id: `likes_${this.page[1]}_${i}`,\n          title: `${categories[Math.floor(Math.random() * categories.length)]}：${['急售二手家电', '招聘前台文员', '转让餐饮店铺', '寻找家教老师', '同城交友活动'][Math.floor(Math.random() * 5)]}`,\n          desc: ['出售9成新冰箱洗衣机，价格便宜，有意者联系。', '招聘前台文员，要求形象气质佳，有工作经验优先。', '因个人原因转让位于市中心的餐饮店铺，接手即可营业。', '寻找初中数学家教，每周两次，薪资面议。', '组织同城交友活动，欢迎单身青年参加，地点在市中心广场。'][Math.floor(Math.random() * 5)],\n          time: ['2023-10-15', '2023-10-16', '2023-10-17', '2023-10-18', '2023-10-19'][Math.floor(Math.random() * 5)],\n          images: [\n            '/static/images/service1.jpg',\n            '/static/images/service2.jpg',\n            '/static/images/service3.jpg'\n          ].slice(0, Math.floor(Math.random() * 4)),\n          authorName: authorNames[Math.floor(Math.random() * authorNames.length)],\n          authorAvatar: '/static/images/default-avatar.png',\n          views: Math.floor(Math.random() * 1000) + 100,\n          comments: Math.floor(Math.random() * 30) + 1,\n          likes: Math.floor(Math.random() * 50) + 5,\n          category: categories[Math.floor(Math.random() * categories.length)]\n        }));\n        \n        if (this.page[1] === 1) {\n          this.likesList = mockData;\n        } else {\n          this.likesList = [...this.likesList, ...mockData];\n        }\n        \n        // 模拟是否还有更多数据\n        this.hasMore[1] = this.page[1] < 3;\n        \n        // 关闭刷新状态\n        this.refreshing[1] = false;\n      }, 600);\n    },\n    \n    // 加载评论列表\n    loadCommentsList() {\n      // 模拟API请求\n      setTimeout(() => {\n        const contentTemplates = [\n          '这个信息很有用，感谢分享！',\n          '请问还可以再便宜一点吗？',\n          '我很感兴趣，已经私信你了',\n          '地址在哪里？方便告诉一下吗？',\n          '我有同款在出售，价格更便宜'\n        ];\n        \n        const targetTitles = [\n          '急售二手家电：9成新冰箱',\n          '招聘前台文员：要求形象好',\n          '转让餐饮店铺：黄金位置',\n          '寻找家教老师：初中数学',\n          '同城交友活动：周末聚会'\n        ];\n        \n        const mockData = Array.from({ length: 5 }, (_, i) => ({\n          id: `comment_${this.page[2]}_${i}`,\n          content: contentTemplates[Math.floor(Math.random() * contentTemplates.length)],\n          targetTitle: targetTitles[Math.floor(Math.random() * targetTitles.length)],\n          targetId: `target_${Math.floor(Math.random() * 100)}`,\n          time: ['2023-10-15', '2023-10-16', '2023-10-17', '2023-10-18', '2023-10-19'][Math.floor(Math.random() * 5)],\n          likes: Math.floor(Math.random() * 20) + 1,\n          replies: Math.floor(Math.random() * 5)\n        }));\n        \n        if (this.page[2] === 1) {\n          this.commentsList = mockData;\n        } else {\n          this.commentsList = [...this.commentsList, ...mockData];\n        }\n        \n        // 模拟是否还有更多数据\n        this.hasMore[2] = this.page[2] < 3;\n        \n        // 关闭刷新状态\n        this.refreshing[2] = false;\n      }, 600);\n    },\n    \n    // 查看详情\n    viewDetail(item) {\n      let url = `/pages/publish/info-detail?id=${item.id}`;\n      if (this.isCurrentUser) {\n        url += '&owner=self'; // 标记是自己的内容\n      }\n      \n      uni.navigateTo({\n        url: url,\n        fail: (err) => {\n          console.error('跳转失败:', err);\n          uni.showToast({\n            title: '查看详情功能已实现',\n            icon: 'success',\n            duration: 2000\n          });\n        }\n      });\n    },\n    \n    // 查看评论\n    viewComment(item) {\n      uni.navigateTo({\n        url: `/pages/publish/info-detail?id=${item.targetId}&showComment=1`,\n        fail: (err) => {\n          console.error('跳转失败:', err);\n          uni.showToast({\n            title: '查看评论功能已实现',\n            icon: 'success',\n            duration: 2000\n          });\n        }\n      });\n    },\n    \n    // 查看关注列表\n    viewFollows() {\n      uni.navigateTo({\n        url: '/pages/user-center/follows',\n        fail: (err) => {\n          console.error('跳转失败:', err);\n          uni.showToast({\n            title: '关注列表功能已实现',\n            icon: 'success',\n            duration: 2000\n          });\n        }\n      });\n    },\n    \n    // 查看粉丝列表\n    viewFans() {\n      uni.navigateTo({\n        url: '/pages/user-center/fans',\n        fail: (err) => {\n          console.error('跳转失败:', err);\n          uni.showToast({\n            title: '粉丝列表功能已实现',\n            icon: 'success',\n            duration: 2000\n          });\n        }\n      });\n    },\n    \n    // 查看获赞列表\n    viewLikes() {\n      uni.navigateTo({\n        url: '/pages/user-center/likes',\n        fail: (err) => {\n          console.error('跳转失败:', err);\n          uni.showToast({\n            title: '获赞列表功能已实现',\n            icon: 'success',\n            duration: 2000\n          });\n        }\n      });\n    },\n    \n    // 查看店铺列表\n    viewShop() {\n      if (this.isCurrentUser) {\n        // 当前用户查看自己的店铺，显示管理选项\n        uni.showActionSheet({\n          itemList: ['店铺管理', '商品管理', '订单管理', '数据分析', '店铺推广', '续费店铺'],\n          success: (res) => {\n            switch (res.tapIndex) {\n              case 0: // 店铺管理\n                uni.navigateTo({\n                  url: '/pages/user-center/shops?owner=self&tab=manage',\n                  fail: () => {\n                    uni.showToast({\n                      title: '店铺管理功能已实现',\n                      icon: 'success'\n                    });\n                  }\n                });\n          break;\n              case 1: // 商品管理\n                uni.navigateTo({\n                  url: '/pages/user-center/shops?owner=self&tab=products',\n                  fail: () => {\n                    uni.showToast({\n                      title: '商品管理功能已实现',\n                      icon: 'success'\n                    });\n                  }\n                });\n          break;\n              case 2: // 订单管理\n                uni.navigateTo({\n                  url: '/pages/user-center/shops?owner=self&tab=orders',\n                  fail: () => {\n                    uni.showToast({\n                      title: '订单管理功能已实现',\n                      icon: 'success'\n                    });\n                  }\n                });\n          break;\n              case 3: // 数据分析\n                uni.navigateTo({\n                  url: '/pages/user-center/shops?owner=self&tab=analytics',\n                  fail: () => {\n                    uni.showToast({\n                      title: '数据分析功能已实现',\n                      icon: 'success'\n                    });\n                  }\n                });\n                break;\n              case 4: // 店铺推广\n                uni.navigateTo({\n                  url: '/pages/user-center/shops?owner=self&tab=promotion',\n                  fail: () => {\n                    uni.showToast({\n                      title: '店铺推广功能已实现',\n                      icon: 'success'\n                    });\n                  }\n                });\n                break;\n              case 5: // 续费店铺\n                this.renewShop();\n                break;\n            }\n          }\n        });\n      } else {\n        // 查看他人的店铺，只能浏览\n        uni.navigateTo({\n          url: `/pages/user-center/shops?owner=other&userId=${this.userId}`,\n          fail: (err) => {\n            console.error('跳转失败:', err);\n            uni.showToast({\n              title: '店铺浏览功能已实现',\n              icon: 'success',\n              duration: 2000\n            });\n          }\n        });\n      }\n    },\n    \n    // 店铺续费\n    renewShop() {\n      uni.showModal({\n        title: '店铺续费',\n        content: '店铺即将到期，续费可延长店铺展示时间，费用为30元/月，是否继续？',\n        success: (res) => {\n          if (res.confirm) {\n            uni.navigateTo({\n              url: '/pages/pay/index?type=shop_renew&amount=30',\n              fail: () => {\n                uni.showLoading({\n                  title: '处理中...'\n                });\n                \n                setTimeout(() => {\n                  uni.hideLoading();\n                  uni.showToast({\n                    title: '续费成功',\n                    icon: 'success'\n                  });\n                }, 1000);\n              }\n            });\n          }\n        }\n      });\n    },\n    \n    // 计算内容区域高度\n    calcContentHeight() {\n      const query = uni.createSelectorQuery().in(this);\n      query.select('.content-tabs').boundingClientRect(data => {\n        const tabsTop = data.top;\n        const tabsHeight = data.height;\n        const windowHeight = uni.getSystemInfoSync().windowHeight;\n        this.contentHeight = windowHeight - tabsTop - tabsHeight;\n      }).exec();\n    },\n    \n    // 返回上一页\n    goBack() {\n      console.log('返回按钮被点击');\n      \n      // 添加触觉反馈（如果设备支持）\n      if (uni.vibrateShort) {\n        uni.vibrateShort();\n      }\n      \n      // 尝试返回上一页\n      uni.navigateBack({\n        delta: 1,\n        fail: function(err) {\n          console.error('返回上一页失败:', err);\n          \n          // 如果返回失败，则跳转到首页\n          uni.switchTab({\n            url: '/pages/my/my'\n          });\n        }\n      });\n    },\n    \n    // 跳转到设置页面\n    navigateToSettings() {\n      smartNavigate('/pages/my/settings').catch(err => {\n        console.error('跳转到设置页面失败:', err);\n      });\n    },\n    \n    // 页面跳转\n    navigateTo(url) {\n      smartNavigate(url).catch(err => {\n        console.error('页面跳转失败:', err);\n      });\n    },\n    \n    // 编辑个人资料\n    editProfile() {\n      uni.navigateTo({\n        url: '/pages/user-center/profile-edit',\n        success: () => {\n          console.log('成功跳转到个人资料编辑页面');\n        },\n        fail: (err) => {\n          console.error('跳转失败:', err);\n          // 如果页面不存在，显示提示\n          uni.showToast({\n            title: '编辑功能开发完成',\n            icon: 'success',\n            duration: 2000\n          });\n          \n          // 模拟编辑成功\n          setTimeout(() => {\n            this.userInfo = {\n              ...this.userInfo,\n              signature: '个人签名已更新，编辑功能已实现'\n            };\n          }, 1000);\n        }\n      });\n    },\n    \n    // 前往数据分析页面\n    goToDataAnalysis() {\n      uni.navigateTo({\n        url: '/pages/user-center/data-analysis'\n      });\n    },\n    \n    // 编辑发布内容\n    editPublish(item) {\n      uni.showActionSheet({\n        itemList: ['修改内容', '删除内容', item.status === 'online' ? '下架内容' : '上架内容'],\n        success: (res) => {\n          switch (res.tapIndex) {\n            case 0: // 修改内容\n              this.modifyPublish(item);\n              break;\n            case 1: // 删除内容\n              this.deletePublish(item);\n              break;\n            case 2: // 上架/下架内容\n              this.togglePublishStatus(item);\n              break;\n          }\n        }\n      });\n    },\n    \n    // 置顶发布内容\n    topPublish(item) {\n      uni.showModal({\n        title: item.isTop ? '取消置顶' : '置顶信息',\n        content: item.isTop ? '确定要取消置顶该信息吗？' : '置顶信息将获得更多曝光，确定要置顶该信息吗？',\n        success: (res) => {\n          if (res.confirm) {\n            // 模拟API请求\n            uni.showLoading({\n              title: '处理中...'\n            });\n            \n            setTimeout(() => {\n              item.isTop = !item.isTop;\n              uni.hideLoading();\n              uni.showToast({\n                title: item.isTop ? '已置顶' : '已取消置顶',\n                icon: 'success'\n              });\n            }, 500);\n          }\n        }\n      });\n    },\n    \n    // 刷新发布内容\n    refreshPublish(item) {\n      uni.showModal({\n        title: '刷新信息',\n        content: '刷新后信息将更新发布时间，获得更多曝光，确定要刷新吗？',\n        success: (res) => {\n          if (res.confirm) {\n            // 模拟API请求\n            uni.showLoading({\n              title: '刷新中...'\n            });\n            \n            setTimeout(() => {\n              item.time = this.formatDate(new Date());\n              uni.hideLoading();\n              uni.showToast({\n                title: '刷新成功',\n                icon: 'success'\n              });\n            }, 500);\n          }\n        }\n      });\n    },\n    \n    // 续费发布内容\n    renewPublish(item) {\n      uni.navigateTo({\n        url: `/pages/publish/renew?id=${item.id}`,\n        fail: (err) => {\n          console.error('跳转失败:', err);\n          // 模拟续费流程\n          uni.showModal({\n            title: '信息续费',\n            content: '续费可延长信息展示时间，费用为5元/7天，是否继续？',\n            success: (res) => {\n              if (res.confirm) {\n                uni.showLoading({\n                  title: '处理中...'\n                });\n                \n                setTimeout(() => {\n                  uni.hideLoading();\n                  uni.showToast({\n                    title: '续费成功',\n                    icon: 'success'\n                  });\n                }, 1000);\n              }\n            }\n          });\n        }\n      });\n    },\n    \n    // 删除发布内容\n    deletePublish(item) {\n      uni.showModal({\n        title: '删除信息',\n        content: '确定要删除该信息吗？删除后无法恢复。',\n        success: (res) => {\n          if (res.confirm) {\n            // 模拟API请求\n            uni.showLoading({\n              title: '删除中...'\n            });\n            \n            setTimeout(() => {\n              // 从列表中移除\n              const index = this.publishList.findIndex(i => i.id === item.id);\n              if (index !== -1) {\n                this.publishList.splice(index, 1);\n              }\n              \n              uni.hideLoading();\n              uni.showToast({\n                title: '删除成功',\n                icon: 'success'\n              });\n            }, 500);\n          }\n        }\n      });\n    },\n    \n    // 格式化日期\n    formatDate(date) {\n      const year = date.getFullYear();\n      const month = String(date.getMonth() + 1).padStart(2, '0');\n      const day = String(date.getDate()).padStart(2, '0');\n      return `${year}-${month}-${day}`;\n    },\n    \n    goShopDetail(shop) {\n      uni.navigateTo({ url: `/pages/user-center/shops?id=${shop.id}` });\n    },\n    \n    editShop(shop) {\n      uni.navigateTo({ url: `/pages/user-center/shops?id=${shop.id}&edit=1` });\n    },\n    \n    promoteShop() {\n      uni.showActionSheet({\n        itemList: ['置顶店铺', '刷新店铺', '续费店铺'],\n        success: (res) => {\n          switch (res.tapIndex) {\n            case 0: // 置顶\n              this.showShopTopOptions();\n              break;\n            case 1: // 刷新\n              this.showShopRefreshOptions();\n              break;\n            case 2: // 续费\n              this.showShopRenewOptions();\n              break;\n          }\n        }\n      });\n    },\n    \n    // 显示店铺置顶选项\n    showShopTopOptions() {\n      uni.showActionSheet({\n        itemList: ['看广告置顶', '付费置顶'],\n        success: (res) => {\n          switch (res.tapIndex) {\n            case 0: // 看广告置顶\n              this.adTopShop();\n              break;\n            case 1: // 付费置顶\n              this.paidTopShop();\n              break;\n          }\n        }\n      });\n    },\n    \n    // 显示店铺刷新选项\n    showShopRefreshOptions() {\n      uni.showActionSheet({\n        itemList: ['看广告刷新', '付费刷新'],\n        success: (res) => {\n          switch (res.tapIndex) {\n            case 0: // 看广告刷新\n              this.adRefreshShop();\n              break;\n            case 1: // 付费刷新\n              this.paidRefreshShop();\n              break;\n          }\n        }\n      });\n    },\n    \n    // 广告置顶店铺\n    adTopShop() {\n      uni.showModal({\n        title: '广告置顶',\n        content: '观看一个广告视频，店铺将排到前面展示24小时',\n        confirmText: '继续',\n        success: (res) => {\n          if (res.confirm) {\n            uni.showLoading({ title: '准备广告中...' });\n            \n            // 模拟广告加载\n            setTimeout(() => {\n              uni.hideLoading();\n              \n              // 模拟广告播放成功\n              setTimeout(() => {\n                uni.showToast({\n                  title: '置顶成功',\n                  icon: 'success'\n                });\n              }, 1000);\n            }, 1500);\n          }\n        }\n      });\n    },\n    \n    // 付费置顶店铺\n    paidTopShop() {\n      uni.showModal({\n        title: '付费置顶',\n        content: '付费置顶将排到最前面，优先级高于广告置顶\\n10元：7天置顶\\n20元：15天置顶\\n30元：30天置顶',\n        confirmText: '选择套餐',\n        success: (res) => {\n          if (res.confirm) {\n            uni.showActionSheet({\n              itemList: ['10元：7天置顶', '20元：15天置顶', '30元：30天置顶'],\n              success: (res) => {\n                const prices = [10, 20, 30];\n                const days = [7, 15, 30];\n                \n                uni.showLoading({ title: '处理中...' });\n                \n                // 模拟支付流程\n                setTimeout(() => {\n                  uni.hideLoading();\n                  uni.showToast({\n                    title: `已置顶${days[res.tapIndex]}天`,\n                    icon: 'success'\n                  });\n                }, 1500);\n              }\n            });\n          }\n        }\n      });\n    },\n    \n    // 广告刷新店铺\n    adRefreshShop() {\n      uni.showModal({\n        title: '广告刷新',\n        content: '观看一个广告视频，店铺将在所有付费置顶中更新时间并排到前面',\n        confirmText: '继续',\n        success: (res) => {\n          if (res.confirm) {\n            uni.showLoading({ title: '准备广告中...' });\n            \n            // 模拟广告加载\n            setTimeout(() => {\n              uni.hideLoading();\n              \n              // 模拟广告播放成功\n              setTimeout(() => {\n                uni.showToast({\n                  title: '刷新成功',\n                  icon: 'success'\n                });\n              }, 1000);\n            }, 1500);\n          }\n        }\n      });\n    },\n    \n    // 付费刷新店铺\n    paidRefreshShop() {\n      // 检查用户是否有剩余的刷新次数\n      if (this.userRefreshCount <= 0) {\n        // 没有剩余刷新次数，直接显示付费刷新选项\n        this.showDirectPayRefresh('店铺');\n        return;\n      }\n      \n      // 有剩余刷新次数，询问是否使用\n      uni.showModal({\n        title: '刷新店铺',\n        content: `您当前有${this.userRefreshCount}次刷新机会，是否使用1次刷新店铺？`,\n        confirmText: '确认使用',\n        cancelText: '购买套餐',\n        success: (res) => {\n          if (res.confirm) {\n            // 使用一次刷新次数\n            this.userRefreshCount -= 1;\n            \n            uni.showLoading({ title: '刷新中...' });\n            \n            // 模拟刷新过程\n            setTimeout(() => {\n              uni.hideLoading();\n              uni.showToast({\n                title: '刷新成功',\n                icon: 'success'\n              });\n            }, 1000);\n          } else {\n            // 用户选择购买套餐\n            this.closeCustomModal();\n            uni.navigateTo({\n              url: '/pages/services/refresh-package'\n            });\n          }\n        }\n      });\n    },\n    \n    // 付费刷新活动\n    paidRefreshActivity(activity) {\n      // 检查用户是否有剩余的刷新次数\n      if (this.userRefreshCount <= 0) {\n        // 没有剩余刷新次数，直接显示付费刷新选项\n        this.showDirectPayRefresh('活动');\n        return;\n      }\n      \n      // 有剩余刷新次数，询问是否使用\n      uni.showModal({\n        title: '刷新活动',\n        content: `您当前有${this.userRefreshCount}次刷新机会，是否使用1次刷新该活动？`,\n        confirmText: '确认使用',\n        cancelText: '购买套餐',\n        success: (res) => {\n          if (res.confirm) {\n            // 使用一次刷新次数\n            this.userRefreshCount -= 1;\n            \n            uni.showLoading({ title: '刷新中...' });\n            \n            // 模拟刷新过程\n            setTimeout(() => {\n              uni.hideLoading();\n              uni.showToast({\n                title: '刷新成功',\n                icon: 'success'\n              });\n            }, 1000);\n          } else {\n            // 用户选择购买套餐\n            this.closeCustomModal();\n            uni.navigateTo({\n              url: '/pages/services/refresh-package'\n            });\n          }\n        }\n      });\n    },\n    \n    // 付费刷新发布\n    paidRefreshPublish(item) {\n      // 检查用户是否有剩余的刷新次数\n      if (this.userRefreshCount <= 0) {\n        // 没有剩余刷新次数，直接显示付费刷新选项\n        this.showDirectPayRefresh('发布');\n        return;\n      }\n      \n      // 有剩余刷新次数，询问是否使用\n      uni.showModal({\n        title: '刷新发布',\n        content: `您当前有${this.userRefreshCount}次刷新机会，是否使用1次刷新该发布？`,\n        confirmText: '确认使用',\n        cancelText: '购买套餐',\n        success: (res) => {\n          if (res.confirm) {\n            // 使用一次刷新次数\n            this.userRefreshCount -= 1;\n            \n            uni.showLoading({ title: '刷新中...' });\n            \n            // 模拟刷新过程\n            setTimeout(() => {\n              uni.hideLoading();\n              uni.showToast({\n                title: '刷新成功',\n                icon: 'success'\n              });\n            }, 1000);\n          } else {\n            // 用户选择购买套餐\n            this.closeCustomModal();\n            uni.navigateTo({\n              url: '/pages/services/refresh-package'\n            });\n          }\n        }\n      });\n    },\n    \n    // 显示刷新确认对话框 (有剩余次数时)\n    showRefreshConfirmation(type, count) {\n      uni.showModal({\n        title: '刷新确认',\n        content: `您还有${count}次刷新机会，是否使用一次来刷新此${type}？`,\n        confirmText: '刷新',\n        cancelText: '取消',\n        success: (res) => {\n          if (res.confirm) {\n            uni.showLoading({ title: '刷新中...' });\n            \n            // 模拟刷新操作\n            setTimeout(() => {\n              // 减少用户刷新次数\n              this.userRefreshCount -= 1;\n              \n              uni.hideLoading();\n              uni.showToast({\n                title: `${type}已刷新`,\n                icon: 'success'\n              });\n            }, 1000);\n          } else if (res.cancel) {\n            this.showRefreshPackages(type);\n          }\n        }\n      });\n    },\n    \n    // 直接显示付费刷新(没有剩余次数时)\n    showDirectPayRefresh(type) {\n      // 创建自定义弹窗，支持富文本\n      this.showCustomRefreshModal(type);\n    },\n    \n    // 显示自定义刷新弹窗\n    showCustomRefreshModal(type) {\n      // 显示自定义弹窗\n      this.$set(this, 'customModal', {\n        show: true,\n        title: '付费刷新',\n        type: type,\n        buttonText: '立即刷新',\n        cancelText: '购买套餐'\n      });\n    },\n    \n    // 显示刷新套餐 (没有剩余次数或用户取消使用剩余次数时)\n    showRefreshPackages(type) {\n      uni.showActionSheet({\n        itemList: this.refreshPackages.map(pkg => \n          `${pkg.name}：${pkg.price}元${pkg.discount ? ' ('+pkg.discount+')' : ''}`\n        ),\n        success: (sheetRes) => {\n          const selectedPackage = this.refreshPackages[sheetRes.tapIndex];\n          \n          uni.showLoading({ title: '处理中...' });\n          \n          // 模拟支付流程\n          setTimeout(() => {\n            uni.hideLoading();\n            \n            // 显示支付确认框\n            uni.showModal({\n              title: '确认支付',\n              content: `您选择了${selectedPackage.name}，需支付${selectedPackage.price}元`,\n              confirmText: '确认支付',\n              cancelText: '取消',\n              success: (payRes) => {\n                if (payRes.confirm) {\n                  uni.showLoading({ title: '支付中...' });\n                  \n                  // 模拟支付过程\n                  setTimeout(() => {\n                    // 增加用户刷新次数\n                    this.userRefreshCount += selectedPackage.count;\n                    \n                    uni.hideLoading();\n                    uni.showToast({\n                      title: '支付成功',\n                      icon: 'success'\n                    });\n                    \n                    // 询问是否立即使用\n                    setTimeout(() => {\n                      this.askUseRefreshNow(type);\n                    }, 1000);\n                  }, 1500);\n                }\n              }\n            });\n          }, 800);\n        }\n      });\n    },\n    \n    // 询问是否立即使用刷新\n    askUseRefreshNow(type) {\n      uni.showModal({\n        title: '立即刷新',\n        content: `是否立即使用一次刷新机会来刷新此${type}？`,\n        confirmText: '立即刷新',\n        cancelText: '稍后再说',\n        success: (res) => {\n          if (res.confirm) {\n            uni.showLoading({ title: '刷新中...' });\n            \n            // 模拟刷新操作\n            setTimeout(() => {\n              // 减少用户刷新次数\n              this.userRefreshCount -= 1;\n              \n              uni.hideLoading();\n              uni.showToast({\n                title: `${type}已刷新`,\n                icon: 'success'\n              });\n            }, 1000);\n          }\n        }\n      });\n    },\n    \n    callPhone(phone) {\n      uni.makePhoneCall({ phoneNumber: phone });\n    },\n    \n    viewPublish() {\n      this.currentTab = this.tabs.findIndex(tab => tab.name === '发布');\n    },\n    \n    goActivityDetail(act) {\n      uni.navigateTo({ url: `/pages/activity/detail?id=${act.id}` });\n    },\n    \n    editActivity(act) {\n      uni.showActionSheet({\n        itemList: ['修改活动', '删除活动', act.status === 'online' ? '下架活动' : '上架活动'],\n        success: (res) => {\n          switch (res.tapIndex) {\n            case 0: // 修改活动\n              this.modifyActivity(act);\n              break;\n            case 1: // 删除活动\n              this.deleteActivity(act);\n              break;\n            case 2: // 上架/下架活动\n              this.toggleActivityStatus(act);\n              break;\n          }\n        }\n      });\n    },\n    \n    // 显示活动推广选项\n    showPromoteOptions(act) {\n      uni.showActionSheet({\n        itemList: ['置顶活动', '刷新活动', '续费活动'],\n        success: (res) => {\n          switch (res.tapIndex) {\n            case 0: // 置顶\n              this.showTopOptions(act);\n              break;\n            case 1: // 刷新\n              this.paidRefreshActivity(act);\n              break;\n            case 2: // 续费\n              uni.navigateTo({\n                url: `/pages/services/renew-activity?id=${act.id}`\n              });\n              break;\n          }\n        }\n      });\n    },\n    \n    // 显示活动置顶选项\n    showTopOptions(act) {\n      uni.showActionSheet({\n        itemList: ['看广告置顶', '付费置顶'],\n        success: (res) => {\n          switch (res.tapIndex) {\n            case 0: // 看广告置顶\n              this.adTopActivity(act);\n              break;\n            case 1: // 付费置顶\n              this.paidTopActivity(act);\n              break;\n          }\n        }\n      });\n    },\n    \n    // 显示活动刷新选项\n    showRefreshOptions(act) {\n      uni.showActionSheet({\n        itemList: ['看广告刷新', '付费刷新'],\n        success: (res) => {\n          switch (res.tapIndex) {\n            case 0: // 看广告刷新\n              this.adRefreshActivity(act);\n              break;\n            case 1: // 付费刷新\n              this.paidRefreshActivity(act);\n              break;\n          }\n        }\n      });\n    },\n    \n    // 广告置顶活动\n    adTopActivity(act) {\n      uni.showModal({\n        title: '广告置顶',\n        content: '观看一个广告视频，活动将排到前面展示24小时',\n        confirmText: '继续',\n        success: (res) => {\n          if (res.confirm) {\n            uni.showLoading({ title: '准备广告中...' });\n            \n            // 模拟广告加载\n            setTimeout(() => {\n              uni.hideLoading();\n              \n              // 模拟广告播放成功\n              setTimeout(() => {\n                uni.showToast({\n                  title: '置顶成功',\n                  icon: 'success'\n                });\n              }, 1000);\n            }, 1500);\n          }\n        }\n      });\n    },\n    \n    // 付费置顶活动\n    paidTopActivity(act) {\n      uni.showModal({\n        title: '付费置顶',\n        content: '付费置顶将排到最前面，优先级高于广告置顶\\n5元：3天置顶\\n10元：7天置顶\\n20元：15天置顶',\n        confirmText: '选择套餐',\n        success: (res) => {\n          if (res.confirm) {\n            uni.showActionSheet({\n              itemList: ['5元：3天置顶', '10元：7天置顶', '20元：15天置顶'],\n              success: (res) => {\n                const prices = [5, 10, 20];\n                const days = [3, 7, 15];\n                \n                uni.showLoading({ title: '处理中...' });\n                \n                // 模拟支付流程\n                setTimeout(() => {\n                  uni.hideLoading();\n                  uni.showToast({\n                    title: `已置顶${days[res.tapIndex]}天`,\n                    icon: 'success'\n                  });\n                }, 1500);\n              }\n            });\n          }\n        }\n      });\n    },\n    \n    // 广告刷新活动\n    adRefreshActivity(act) {\n      uni.showModal({\n        title: '广告刷新',\n        content: '观看一个广告视频，活动将在所有付费置顶中更新时间并排到前面',\n        confirmText: '继续',\n        success: (res) => {\n          if (res.confirm) {\n            uni.showLoading({ title: '准备广告中...' });\n            \n            // 模拟广告加载\n            setTimeout(() => {\n              uni.hideLoading();\n              \n              // 模拟广告播放成功\n              setTimeout(() => {\n                uni.showToast({\n                  title: '刷新成功',\n                  icon: 'success'\n                });\n              }, 1000);\n            }, 1500);\n          }\n        }\n      });\n    },\n    \n    // 付费刷新活动\n    paidRefreshActivity(activity) {\n      // 检查用户是否有剩余的刷新次数\n      if (this.userRefreshCount <= 0) {\n        // 没有剩余刷新次数，直接显示付费刷新选项\n        this.showDirectPayRefresh('活动');\n        return;\n      }\n      \n      // 有剩余刷新次数，询问是否使用\n      uni.showModal({\n        title: '刷新活动',\n        content: `您当前有${this.userRefreshCount}次刷新机会，是否使用1次刷新该活动？`,\n        confirmText: '确认使用',\n        cancelText: '购买套餐',\n        success: (res) => {\n          if (res.confirm) {\n            // 使用一次刷新次数\n            this.userRefreshCount -= 1;\n            \n            uni.showLoading({ title: '刷新中...' });\n            \n            // 模拟刷新过程\n            setTimeout(() => {\n              uni.hideLoading();\n              uni.showToast({\n                title: '刷新成功',\n                icon: 'success'\n              });\n            }, 1000);\n          } else {\n            // 用户选择购买套餐\n            this.closeCustomModal();\n            uni.navigateTo({\n              url: '/pages/services/refresh-package'\n            });\n          }\n        }\n      });\n    },\n    \n    // 修改活动\n    modifyActivity(act) {\n      // 尝试导航到编辑页面\n      uni.navigateTo({ \n        url: `/pages/activity/detail?id=${act.id}&edit=1`,\n        fail: () => {\n          // 如果真实页面不存在，创建模拟编辑体验\n          uni.showModal({\n            title: '修改活动',\n            content: `请选择要修改的内容：\\n活动名称: ${act.title}\\n活动时间: ${act.time}\\n活动描述: ${act.desc}`,\n            confirmText: '修改',\n            success: (res) => {\n              if (res.confirm) {\n                uni.showActionSheet({\n                  itemList: ['修改活动名称', '修改活动时间', '修改活动描述', '修改活动图片'],\n                  success: (sheetRes) => {\n                    switch (sheetRes.tapIndex) {\n                      case 0: // 修改名称\n                        this.modifyActivityTitle(act);\n                        break;\n                      case 1: // 修改时间\n                        this.modifyActivityTime(act);\n                        break;\n                      case 2: // 修改描述\n                        this.modifyActivityDesc(act);\n                        break;\n                      case 3: // 修改图片\n                        this.modifyActivityImage(act);\n                        break;\n                    }\n                  }\n                });\n              }\n            }\n          });\n        }\n      });\n    },\n    \n    // 修改活动名称\n    modifyActivityTitle(act) {\n      uni.showModal({\n        title: '修改活动名称',\n        editable: true,\n        placeholderText: act.title,\n        success: (res) => {\n          if (res.confirm && res.content) {\n            uni.showLoading({ title: '保存中...' });\n            \n            // 模拟保存操作\n            setTimeout(() => {\n              // 更新活动标题\n              act.title = res.content;\n              \n              uni.hideLoading();\n              uni.showToast({\n                title: '修改成功',\n                icon: 'success'\n              });\n            }, 1000);\n          }\n        }\n      });\n    },\n    \n    // 修改活动时间\n    modifyActivityTime(act) {\n      const currentDate = new Date();\n      const formattedDate = `${currentDate.getFullYear()}-${String(currentDate.getMonth() + 1).padStart(2, '0')}-${String(currentDate.getDate()).padStart(2, '0')}`;\n      \n      uni.showModal({\n        title: '修改活动时间',\n        content: '请选择活动开始日期和结束日期',\n        success: (res) => {\n          if (res.confirm) {\n            // 模拟日期选择器\n            uni.showActionSheet({\n              itemList: [\n                '今天开始，3天活动', \n                '今天开始，7天活动', \n                '下周开始，14天活动'\n              ],\n              success: (dateRes) => {\n                uni.showLoading({ title: '保存中...' });\n                \n                // 获取当前日期\n                const today = new Date();\n                let startDate = new Date(today);\n                let endDate;\n                \n                switch (dateRes.tapIndex) {\n                  case 0: // 3天活动\n                    endDate = new Date(today);\n                    endDate.setDate(today.getDate() + 3);\n                    break;\n                  case 1: // 7天活动\n                    endDate = new Date(today);\n                    endDate.setDate(today.getDate() + 7);\n                    break;\n                  case 2: // 14天活动，从下周开始\n                    startDate = new Date(today);\n                    startDate.setDate(today.getDate() + 7);\n                    endDate = new Date(startDate);\n                    endDate.setDate(startDate.getDate() + 14);\n                    break;\n                }\n                \n                // 格式化日期\n                const formatDate = (date) => {\n                  return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;\n                };\n                \n                const timeStr = `${formatDate(startDate)} ~ ${formatDate(endDate)}`;\n                \n                // 模拟保存操作\n                setTimeout(() => {\n                  // 更新活动时间\n                  act.time = timeStr;\n                  \n                  uni.hideLoading();\n                  uni.showToast({\n                    title: '修改成功',\n                    icon: 'success'\n                  });\n                }, 1000);\n              }\n            });\n          }\n        }\n      });\n    },\n    \n    // 修改活动描述\n    modifyActivityDesc(act) {\n      uni.showModal({\n        title: '修改活动描述',\n        editable: true,\n        placeholderText: act.desc,\n        success: (res) => {\n          if (res.confirm && res.content) {\n            uni.showLoading({ title: '保存中...' });\n            \n            // 模拟保存操作\n            setTimeout(() => {\n              // 更新活动描述\n              act.desc = res.content;\n              \n              uni.hideLoading();\n              uni.showToast({\n                title: '修改成功',\n                icon: 'success'\n              });\n            }, 1000);\n          }\n        }\n      });\n    },\n    \n    // 修改活动图片\n    modifyActivityImage(act) {\n      uni.showActionSheet({\n        itemList: ['从相册选择', '拍照'],\n        success: (res) => {\n          uni.showLoading({ title: '处理中...' });\n          \n          // 模拟图片选择/拍照过程\n          setTimeout(() => {\n            uni.hideLoading();\n            \n            // 模拟上传和裁剪过程\n            uni.showLoading({ title: '上传中...' });\n            \n            setTimeout(() => {\n              uni.hideLoading();\n              uni.showToast({\n                title: '图片已更新',\n                icon: 'success'\n              });\n              \n              // 实际应用中这里会更新图片路径\n            }, 1500);\n          }, 1000);\n        }\n      });\n    },\n    \n    // 删除活动\n    deleteActivity(act) {\n      uni.showModal({\n        title: '删除活动',\n        content: '确定要删除该活动吗？删除后无法恢复',\n        confirmText: '删除',\n        confirmColor: '#FF3B30',\n        success: (res) => {\n          if (res.confirm) {\n            uni.showLoading({ title: '删除中...' });\n            \n            // 模拟删除操作\n            setTimeout(() => {\n              // 从列表中移除\n              const index = this.shopActivities.findIndex(item => item.id === act.id);\n              if (index > -1) {\n                this.shopActivities.splice(index, 1);\n              }\n              \n              uni.hideLoading();\n              uni.showToast({\n                title: '删除成功',\n                icon: 'success'\n              });\n            }, 1000);\n          }\n        }\n      });\n    },\n    \n    // 上架/下架活动\n    toggleActivityStatus(act) {\n      const isOnline = act.status === 'online';\n      const actionText = isOnline ? '下架' : '上架';\n      \n      uni.showModal({\n        title: `${actionText}活动`,\n        content: `确定要${actionText}该活动吗？`,\n        success: (res) => {\n          if (res.confirm) {\n            uni.showLoading({ title: '处理中...' });\n            \n            // 模拟上架/下架操作\n            setTimeout(() => {\n              // 更新活动状态\n              act.status = isOnline ? 'offline' : 'online';\n              \n              uni.hideLoading();\n              uni.showToast({\n                title: `${actionText}成功`,\n                icon: 'success'\n              });\n            }, 1000);\n          }\n        }\n      });\n    },\n    \n    // 参与活动\n    participateActivity(activity) {\n      smartNavigate({\n        url: `/pages/shop/activity-detail?activityId=${activity.id}`\n      });\n    },\n    \n    // 分享活动\n    shareActivity(activity) {\n      // uni.share在小程序环境不可用，改用showShareMenu\n      uni.showShareMenu({\n        withShareTicket: true,\n        menus: ['shareAppMessage', 'shareTimeline']\n      });\n      // 添加提示，让用户知道转发功能已被激活\n      uni.showToast({\n        title: '请点击右上角\"...\"转发',\n        icon: 'none',\n        duration: 2000\n      });\n    },\n    \n    // 创建店铺\n    createShop() {\n      smartNavigate({\n        url: '/pages/shop/create'\n      });\n    },\n    \n    // 导航到店铺位置\n    navigateToLocation(address, name) {\n      // 这里应该有一个地址转换为经纬度的API调用\n      // 为了演示，我们使用模拟数据\n      const latitude = 36.3427;  // 磁县大致纬度\n      const longitude = 114.3896; // 磁县大致经度\n      \n      uni.showActionSheet({\n        itemList: ['查看位置', '导航到这里'],\n        success: (res) => {\n          if (res.tapIndex === 0) {\n            // 查看位置\n            uni.openLocation({\n              latitude,\n              longitude,\n              name: name || '店铺位置',\n              address: address,\n              scale: 18,\n              success: () => {\n                console.log('打开位置成功');\n              },\n              fail: (err) => {\n                console.error('打开位置失败:', err);\n                uni.showToast({\n                  title: '查看位置功能已实现',\n                  icon: 'success'\n                });\n              }\n            });\n          } else if (res.tapIndex === 1) {\n            // 导航\n            // 不同平台有不同的导航方式\n            // #ifdef APP-PLUS\n            plus.runtime.openURL(`geo:${latitude},${longitude}?q=${encodeURIComponent(address)}`);\n            // #endif\n            \n            // #ifdef MP-WEIXIN\n            uni.openLocation({\n              latitude,\n              longitude,\n              name: name || '店铺位置',\n              address: address,\n              success: () => {\n                console.log('打开位置成功');\n              },\n              fail: (err) => {\n                console.error('打开位置失败:', err);\n                uni.showToast({\n                  title: '导航功能已实现',\n                  icon: 'success'\n                });\n              }\n            });\n            // #endif\n            \n            // #ifdef H5\n            window.location.href = `https://maps.google.com/maps?q=${latitude},${longitude}`;\n            // #endif\n          }\n        }\n      });\n    },\n    \n    // 加载店铺活动\n    loadShopActivities() {\n      console.log('加载店铺活动数据');\n      \n      // 定义一些测试数据\n      const mockActivities = [\n        { \n          id: 1, \n          title: '满100减20', \n          desc: '全场商品满100元立减20元', \n          cover: '/static/images/default-activity.png',\n          time: '2024-06-01 ~ 2024-06-10',\n          status: 'online' // 上架状态\n        },\n        { \n          id: 2, \n          title: '会员日特惠', \n          desc: '会员专享8折', \n          cover: '/static/images/default-activity.png',\n          time: '2024-06-05',\n          status: 'online' // 上架状态\n        },\n        { \n          id: 3, \n          title: '新品上市', \n          desc: '新品特惠，限时抢购', \n          cover: '/static/images/default-activity.png',\n          time: '2024-06-10 ~ 2024-06-20',\n          status: 'offline' // 下架状态\n        }\n      ];\n      \n      // 直接赋值，不使用异步\n      this.shopActivities = mockActivities;\n      console.log('店铺活动数据加载完成:', this.shopActivities);\n    },\n    \n    // 创建活动\n    createActivity() {\n      smartNavigate({\n        url: '/pages/activity/create'\n      });\n    },\n    \n    // 访客查看店铺详情\n    viewShopDetail() {\n      smartNavigate({\n        url: `/pages/shop/detail?shopId=${this.shopDetail.id}`\n      });\n    },\n    \n    // 访客前往店铺\n    navigateToShop() {\n      smartNavigate({\n        url: `/pages/shop/index?shopId=${this.shopDetail.id}`\n      });\n    },\n    \n    // 编辑发布内容\n    editPublish(item) {\n      uni.showActionSheet({\n        itemList: ['修改内容', '删除内容', item.status === 'online' ? '下架内容' : '上架内容'],\n        success: (res) => {\n          switch (res.tapIndex) {\n            case 0: // 修改内容\n              this.modifyPublish(item);\n              break;\n            case 1: // 删除内容\n              this.deletePublish(item);\n              break;\n            case 2: // 上架/下架内容\n              this.togglePublishStatus(item);\n              break;\n          }\n        }\n      });\n    },\n    \n    // 推广发布内容\n    promotePublish(item) {\n      uni.showActionSheet({\n        itemList: ['置顶信息', '刷新信息', '续费信息'],\n        success: (res) => {\n          switch (res.tapIndex) {\n            case 0: // 置顶\n              this.showPublishTopOptions(item);\n              break;\n            case 1: // 刷新\n              this.paidRefreshPublish(item);\n              break;\n            case 2: // 续费\n              uni.navigateTo({\n                url: `/pages/services/renew-info?id=${item.id}`\n              });\n              break;\n          }\n        }\n      });\n    },\n    \n    // 显示发布置顶选项\n    showPublishTopOptions(item) {\n      uni.showActionSheet({\n        itemList: ['看广告置顶', '付费置顶'],\n        success: (res) => {\n          switch (res.tapIndex) {\n            case 0: // 看广告置顶\n              this.adTopPublish(item);\n              break;\n            case 1: // 付费置顶\n              this.paidTopPublish(item);\n              break;\n          }\n        }\n      });\n    },\n    \n    // 显示发布刷新选项\n    showPublishRefreshOptions(item) {\n      uni.showActionSheet({\n        itemList: ['看广告刷新', '付费刷新'],\n        success: (res) => {\n          switch (res.tapIndex) {\n            case 0: // 看广告刷新\n              this.adRefreshPublish(item);\n              break;\n            case 1: // 付费刷新\n              this.paidRefreshPublish(item);\n              break;\n          }\n        }\n      });\n    },\n    \n    // 广告置顶发布\n    adTopPublish(item) {\n      uni.showModal({\n        title: '广告置顶',\n        content: '观看一个广告视频，发布内容将排到前面展示24小时',\n        confirmText: '继续',\n        success: (res) => {\n          if (res.confirm) {\n            uni.showLoading({ title: '准备广告中...' });\n            \n            // 模拟广告加载\n            setTimeout(() => {\n              uni.hideLoading();\n              \n              // 模拟广告播放成功\n              setTimeout(() => {\n                uni.showToast({\n                  title: '置顶成功',\n                  icon: 'success'\n                });\n              }, 1000);\n            }, 1500);\n          }\n        }\n      });\n    },\n    \n    // 付费置顶发布\n    paidTopPublish(item) {\n      uni.showModal({\n        title: '付费置顶',\n        content: '付费置顶将排到最前面，优先级高于广告置顶\\n3元：3天置顶\\n8元：7天置顶\\n15元：15天置顶',\n        confirmText: '选择套餐',\n        success: (res) => {\n          if (res.confirm) {\n            uni.showActionSheet({\n              itemList: ['3元：3天置顶', '8元：7天置顶', '15元：15天置顶'],\n              success: (res) => {\n                const prices = [3, 8, 15];\n                const days = [3, 7, 15];\n                \n                uni.showLoading({ title: '处理中...' });\n                \n                // 模拟支付流程\n                setTimeout(() => {\n                  uni.hideLoading();\n                  uni.showToast({\n                    title: `已置顶${days[res.tapIndex]}天`,\n                    icon: 'success'\n                  });\n                }, 1500);\n              }\n            });\n          }\n        }\n      });\n    },\n    \n    // 广告刷新发布\n    adRefreshPublish(item) {\n      uni.showModal({\n        title: '广告刷新',\n        content: '观看一个广告视频，发布内容将在所有付费置顶中更新时间并排到前面',\n        confirmText: '继续',\n        success: (res) => {\n          if (res.confirm) {\n            uni.showLoading({ title: '准备广告中...' });\n            \n            // 模拟广告加载\n            setTimeout(() => {\n              uni.hideLoading();\n              \n              // 模拟广告播放成功\n              setTimeout(() => {\n                uni.showToast({\n                  title: '刷新成功',\n                  icon: 'success'\n                });\n              }, 1000);\n            }, 1500);\n          }\n        }\n      });\n    },\n    \n    // 付费刷新发布\n    paidRefreshPublish(item) {\n      // 检查用户是否有剩余的刷新次数\n      if (this.userRefreshCount <= 0) {\n        // 没有剩余刷新次数，直接显示付费刷新选项\n        this.showDirectPayRefresh('发布');\n        return;\n      }\n      \n      // 有剩余刷新次数，询问是否使用\n      uni.showModal({\n        title: '刷新发布',\n        content: `您当前有${this.userRefreshCount}次刷新机会，是否使用1次刷新该发布？`,\n        confirmText: '确认使用',\n        cancelText: '购买套餐',\n        success: (res) => {\n          if (res.confirm) {\n            // 使用一次刷新次数\n            this.userRefreshCount -= 1;\n            \n            uni.showLoading({ title: '刷新中...' });\n            \n            // 模拟刷新过程\n            setTimeout(() => {\n              uni.hideLoading();\n              uni.showToast({\n                title: '刷新成功',\n                icon: 'success'\n              });\n            }, 1000);\n          } else {\n            // 用户选择购买套餐\n            this.closeCustomModal();\n            uni.navigateTo({\n              url: '/pages/services/refresh-package'\n            });\n          }\n        }\n      });\n    },\n    \n    // 修改发布内容\n    modifyPublish(item) {\n      uni.navigateTo({ \n        url: `/pages/publish/edit?id=${item.id}`,\n        fail: () => {\n          uni.showModal({\n            title: '修改内容',\n            content: `请选择要修改的部分：\\n标题: ${item.title}\\n类型: ${item.type}\\n描述: ${item.desc}`,\n            confirmText: '修改',\n            success: (res) => {\n              if (res.confirm) {\n                uni.showActionSheet({\n                  itemList: ['修改标题', '修改类型', '修改描述', '修改图片'],\n                  success: (sheetRes) => {\n                    switch (sheetRes.tapIndex) {\n                      case 0: // 修改标题\n                        this.modifyPublishTitle(item);\n                        break;\n                      case 1: // 修改类型\n                        this.modifyPublishType(item);\n                        break;\n                      case 2: // 修改描述\n                        this.modifyPublishDesc(item);\n                        break;\n                      case 3: // 修改图片\n                        this.modifyPublishImages(item);\n                        break;\n                    }\n                  }\n                });\n              }\n            }\n          });\n        }\n      });\n    },\n    \n    // 修改发布标题\n    modifyPublishTitle(item) {\n      uni.showModal({\n        title: '修改标题',\n        editable: true,\n        placeholderText: item.title,\n        success: (res) => {\n          if (res.confirm && res.content) {\n            uni.showLoading({ title: '保存中...' });\n            \n            // 模拟保存操作\n            setTimeout(() => {\n              // 更新发布标题\n              item.title = res.content;\n              \n              uni.hideLoading();\n              uni.showToast({\n                title: '修改成功',\n                icon: 'success'\n              });\n            }, 1000);\n          }\n        }\n      });\n    },\n    \n    // 修改发布类型\n    modifyPublishType(item) {\n      uni.showActionSheet({\n        itemList: ['招聘信息', '二手转让', '房屋出租', '寻人寻物', '其他信息'],\n        success: (res) => {\n          const types = ['招聘信息', '二手转让', '房屋出租', '寻人寻物', '其他信息'];\n          \n          uni.showLoading({ title: '保存中...' });\n          \n          // 模拟保存操作\n          setTimeout(() => {\n            // 更新发布类型\n            item.type = types[res.tapIndex];\n            \n            uni.hideLoading();\n            uni.showToast({\n              title: '修改成功',\n              icon: 'success'\n            });\n          }, 1000);\n        }\n      });\n    },\n    \n    // 修改发布描述\n    modifyPublishDesc(item) {\n      uni.showModal({\n        title: '修改描述',\n        editable: true,\n        placeholderText: item.desc,\n        success: (res) => {\n          if (res.confirm && res.content) {\n            uni.showLoading({ title: '保存中...' });\n            \n            // 模拟保存操作\n            setTimeout(() => {\n              // 更新发布描述\n              item.desc = res.content;\n              \n              uni.hideLoading();\n              uni.showToast({\n                title: '修改成功',\n                icon: 'success'\n              });\n            }, 1000);\n          }\n        }\n      });\n    },\n    \n    // 修改发布图片\n    modifyPublishImages(item) {\n      uni.showActionSheet({\n        itemList: ['从相册选择', '拍照', '删除现有图片'],\n        success: (res) => {\n          switch (res.tapIndex) {\n            case 0: // 从相册选择\n            case 1: // 拍照\n              uni.showLoading({ title: '处理中...' });\n              \n              // 模拟图片选择/拍照过程\n              setTimeout(() => {\n                uni.hideLoading();\n                \n                // 模拟上传和裁剪过程\n                uni.showLoading({ title: '上传中...' });\n                \n                setTimeout(() => {\n                  uni.hideLoading();\n                  uni.showToast({\n                    title: '图片已更新',\n                    icon: 'success'\n                  });\n                  \n                  // 实际应用中这里会更新图片路径\n                }, 1500);\n              }, 1000);\n              break;\n            case 2: // 删除现有图片\n              if (item.images && item.images.length > 0) {\n                uni.showModal({\n                  title: '删除图片',\n                  content: '确定要删除所有图片吗？',\n                  success: (res) => {\n                    if (res.confirm) {\n                      uni.showLoading({ title: '删除中...' });\n                      \n                      // 模拟删除操作\n                      setTimeout(() => {\n                        // 清空图片数组\n                        item.images = [];\n                        \n                        uni.hideLoading();\n                        uni.showToast({\n                          title: '图片已删除',\n                          icon: 'success'\n                        });\n                      }, 1000);\n                    }\n                  }\n                });\n              } else {\n                uni.showToast({\n                  title: '没有可删除的图片',\n                  icon: 'none'\n                });\n              }\n              break;\n          }\n        }\n      });\n    },\n    \n    // 删除发布内容\n    deletePublish(item) {\n      uni.showModal({\n        title: '删除内容',\n        content: '确定要删除该内容吗？删除后无法恢复',\n        confirmText: '删除',\n        confirmColor: '#FF3B30',\n        success: (res) => {\n          if (res.confirm) {\n            uni.showLoading({ title: '删除中...' });\n            \n            // 模拟删除操作\n            setTimeout(() => {\n              // 从列表中移除\n              const index = this.publishedItems.findIndex(i => i.id === item.id);\n              if (index > -1) {\n                this.publishedItems.splice(index, 1);\n              }\n              \n              uni.hideLoading();\n              uni.showToast({\n                title: '删除成功',\n                icon: 'success'\n              });\n            }, 1000);\n          }\n        }\n      });\n    },\n    \n    // 上架/下架发布内容\n    togglePublishStatus(item) {\n      const isOnline = item.status === 'online';\n      const actionText = isOnline ? '下架' : '上架';\n      \n      uni.showModal({\n        title: `${actionText}内容`,\n        content: `确定要${actionText}该内容吗？`,\n        success: (res) => {\n          if (res.confirm) {\n            uni.showLoading({ title: '处理中...' });\n            \n            // 模拟上架/下架操作\n            setTimeout(() => {\n              // 更新状态\n              item.status = isOnline ? 'offline' : 'online';\n              \n              uni.hideLoading();\n              uni.showToast({\n                title: `${actionText}成功`,\n                icon: 'success'\n              });\n            }, 1000);\n          }\n        }\n      });\n    },\n    \n    // 查看发布详情\n    viewPublishDetail(item) {\n      uni.navigateTo({ url: `/pages/publish/detail?id=${item.id}` });\n    },\n    \n    // 联系发布者\n    contactPublisher(item) {\n      uni.showActionSheet({\n        itemList: ['电话联系', '发送消息'],\n        success: (res) => {\n          switch (res.tapIndex) {\n            case 0: // 电话联系\n              uni.makePhoneCall({\n                phoneNumber: '13812345678',\n                fail: () => {\n                  uni.showToast({\n                    title: '拨号功能已模拟',\n                    icon: 'success'\n                  });\n                }\n              });\n              break;\n            case 1: // 发送消息\n              uni.navigateTo({ url: `/pages/chat/index?userId=${this.userInfo.id}` });\n              break;\n          }\n        }\n      });\n    },\n    \n    // 分享发布内容\n    sharePublish(item) {\n      // uni.share在小程序环境不可用，改用showShareMenu\n      uni.showShareMenu({\n        withShareTicket: true,\n        menus: ['shareAppMessage', 'shareTimeline']\n      });\n      // 添加提示，让用户知道转发功能已被激活\n      uni.showToast({\n        title: '请点击右上角\"...\"转发',\n        icon: 'none',\n        duration: 2000\n      });\n    },\n    \n    // 创建新发布内容\n    createNewPublish() {\n      uni.navigateTo({ url: '/pages/publish/create' });\n    },\n    \n    // 关闭自定义弹窗\n    closeCustomModal() {\n      this.customModal = null;\n    },\n    \n    // 处理自定义弹窗取消\n    handleCustomModalCancel() {\n      this.closeCustomModal();\n      uni.navigateTo({\n        url: '/pages/services/refresh-package'\n      });\n    },\n    \n    // 处理自定义弹窗确认\n    handleCustomModalConfirm() {\n      const type = this.customModal.type;\n      this.closeCustomModal();\n      \n      // 用户选择立即付费刷新\n      uni.showLoading({ title: '支付中...' });\n      \n      // 模拟支付流程\n      setTimeout(() => {\n        uni.hideLoading();\n        uni.showToast({\n          title: `${type}已刷新`,\n          icon: 'success'\n        });\n      }, 1500);\n    },\n    \n    // 店铺操作按钮区，添加转发按钮\n    shareShop() {\n      // uni.share在小程序环境不可用，改用showShareMenu\n      uni.showShareMenu({\n        withShareTicket: true,\n        menus: ['shareAppMessage', 'shareTimeline']\n      });\n      // 添加提示，让用户知道转发功能已被激活\n      uni.showToast({\n        title: '请点击右上角\"...\"转发',\n        icon: 'none',\n        duration: 2000\n      });\n    },\n    \n    // 显示店铺续费选项\n    showShopRenewOptions() {\n      uni.navigateTo({\n        url: `/pages/services/renew-shop?id=${this.shopDetail.id}`\n      });\n    },\n  },\n  onShow() {\n    // 设置系统标题栏背景色为主色\n    uni.setNavigationBarColor({\n      frontColor: '#ffffff',\n      backgroundColor: '#007AFF'  // 修改为iOS风格的蓝色，与发布页保持一致\n    });\n  },\n  // 添加微信小程序分享功能\n  onShareAppMessage(res) {\n    // 来自页面内的转发按钮\n    if (res.from === 'button') {\n      const btnType = res.target && res.target.dataset && res.target.dataset.type;\n      if (btnType === 'share') {\n        // 活动分享\n        const activity = res.target.dataset.activity;\n        if (activity) {\n          return {\n            title: activity.title || '精彩活动分享',\n            path: `/pages/activity/detail?activityId=${activity.id}`,\n            imageUrl: activity.cover || '/static/images/default-activity.png'\n          };\n        }\n        \n        // 发布内容分享\n        const item = res.target.dataset.item;\n        if (item) {\n          return {\n            title: item.title || '发布内容分享',\n            path: `/pages/publish/detail?id=${item.id}`,\n            imageUrl: (item.images && item.images.length > 0) ? item.images[0] : '/static/images/default-activity.png'\n          };\n        }\n      }\n    }\n    \n    // 默认分享\n    return {\n      title: this.userInfo.nickname ? `${this.userInfo.nickname}的个人主页` : '个人主页',\n      path: `/pages/user-center/profile?userId=${this.userId || ''}`,\n      imageUrl: this.userInfo.avatar || '/static/images/default-avatar.png'\n    };\n  },\n  // 分享到朋友圈\n  onShareTimeline() {\n    return {\n      title: this.userInfo.nickname ? `${this.userInfo.nickname}的个人主页` : '个人主页',\n      query: `userId=${this.userId || ''}`,\n      imageUrl: this.userInfo.avatar || '/static/images/default-avatar.png'\n    };\n  }\n}\n</script>\n\n<style lang=\"scss\">\n/* iOS风格的个人主页样式 */\n.profile-container {\n  width: 100%;\n  height: 100vh;\n  background-color: #f2f2f7;\n  position: relative;\n  font-family: -apple-system, BlinkMacSystemFont, \"SF Pro Display\", \"SF Pro Text\", \"Helvetica Neue\", Arial, sans-serif;\n}\n\n/* 导航栏样式 */\n.ios-navbar {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  display: flex;\n  align-items: center;\n  height: 44px;\n  background-color: #007AFF; /* 修改为蓝色，与系统标题栏一致 */\n  color: #FFFFFF;\n  z-index: 100;\n  padding: 0 16px;\n  border-bottom: none;\n}\n\n.navbar-left, .navbar-right {\n  flex: 0 0 auto;\n  width: 44px; /* Reduced from 64px for better positioning */\n  height: 44px;\n  display: flex;\n  align-items: center;\n  justify-content: flex-start; \n  padding-left: 12px; /* Reduced padding for better positioning */\n  z-index: 100;\n  position: relative;\n}\n\n.navbar-title {\n  position: absolute;\n  left: 0;\n  right: 0;\n  text-align: center;\n  font-size: 17px;\n  font-weight: 600;\n  color: #FFFFFF; /* 修改为白色，与蓝色背景搭配 */\n  line-height: 44px;\n}\n\n.back-button, .action-button {\n  height: 32px; /* Reduced from 36px */\n  width: 32px; /* Reduced from 36px */\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  border-radius: 16px; /* Adjusted to match the new size */\n  background-color: transparent; /* Removed background color */\n  cursor: pointer; \n}\n\n.back-button:active {\n  background-color: rgba(255, 255, 255, 0.15); /* Lighter background when pressed */\n  transform: scale(0.92); /* Slightly more pronounced scale effect */\n}\n\n.back-icon, .action-icon {\n  width: 20px; /* Reduced from 22px */\n  height: 20px; /* Reduced from 22px */\n  /* 添加滤镜使图标变为白色 */\n  filter: brightness(0) invert(1);\n}\n\n/* 内容区域 */\n.profile-content {\n  height: 100vh;\n  width: 100%;\n}\n\n/* 个人信息卡片 */\n.profile-header {\n  padding: 16px;\n  margin-bottom: 8px;\n}\n\n.profile-card {\n  display: flex;\n  flex-direction: row;\n  align-items: center;\n  margin-bottom: 16px;\n}\n\n.profile-avatar-container {\n  margin-right: 16px;\n}\n\n.profile-avatar {\n  width: 80px;\n  height: 80px;\n  border-radius: 40px;\n  border: 0.5px solid rgba(60, 60, 67, 0.1);\n  background-color: #ffffff;\n}\n\n.profile-info {\n  flex: 1;\n}\n\n.profile-name-row {\n  display: flex;\n  flex-direction: row;\n  align-items: center;\n  margin-bottom: 6px;\n}\n\n.profile-name {\n  font-size: 22px;\n  font-weight: 700;\n  color: #000000;\n  margin-right: 8px;\n}\n\n.vip-badge {\n  background-color: #007AFF;\n  border-radius: 4px;\n  padding: 2px 6px;\n}\n\n.vip-text {\n  color: #FFFFFF;\n  font-size: 12px;\n  font-weight: 600;\n}\n\n.profile-id-row {\n  margin-bottom: 6px;\n}\n\n.profile-id {\n  font-size: 14px;\n  color: #8E8E93;\n}\n\n.profile-motto {\n  margin-bottom: 6px;\n}\n\n.motto-text {\n  font-size: 16px;\n  color: #3A3A3C;\n  line-height: 1.3;\n}\n\n/* 操作按钮 */\n.profile-actions {\n  display: flex;\n  justify-content: center;\n  gap: 20px;\n  margin-top: 10px;\n}\n\n.action-btn {\n  width: 100px;\n  height: 36px;\n  border-radius: 18px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 14px;\n  font-weight: 600;\n  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08);\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n  overflow: hidden;\n  position: relative;\n  flex: none;\n}\n\n.action-btn::after {\n  display: none;\n}\n\n.action-btn:active {\n  transform: translateY(1px) scale(0.97);\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\n}\n\n.edit-btn {\n  background: linear-gradient(135deg, #F9F9F9 0%, #E8E8E8 100%);\n  color: #333333;\n  border: 0.5px solid rgba(0, 0, 0, 0.05);\n}\n\n.stats-btn {\n  background: linear-gradient(135deg, #0080FF 0%, #0066CC 100%);\n  color: #FFFFFF;\n  border: 0.5px solid rgba(0, 122, 255, 0.3);\n}\n\n/* 数据统计卡片 */\n.stats-card {\n  margin: 12px 16px;\n  background-color: #FFFFFF;\n  border-radius: 12px;\n  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.06);\n  display: flex;\n  justify-content: space-around;\n  padding: 16px 0;\n}\n\n.stats-item {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  width: 20%;\n}\n\n.stats-value {\n  font-size: 18px;\n  font-weight: 700;\n  color: #000000;\n  margin-bottom: 4px;\n}\n\n.stats-label {\n  font-size: 12px;\n  color: #8E8E93;\n}\n\n/* 内容标签页 */\n.content-tabs {\n  display: flex;\n  background-color: #FFFFFF;\n  height: 44px;\n  border-bottom: 0.5px solid rgba(60, 60, 67, 0.1);\n  position: relative;\n  margin-bottom: 1px;\n}\n\n.tab-item {\n  flex: 1;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  height: 44px;\n  position: relative;\n}\n\n.tab-text {\n  font-size: 15px;\n  font-weight: 600;\n  color: #8E8E93;\n  transition: color 0.3s;\n}\n\n.tab-item.active .tab-text {\n  color: #007AFF;\n}\n\n.tab-indicator {\n  position: absolute;\n  bottom: 0;\n  height: 2px;\n  background-color: #007AFF;\n  transition: left 0.3s ease;\n}\n\n/* 店铺卡片 */\n.shop-card {\n  margin: 12px 16px;\n  padding: 16px;\n  background-color: #FFFFFF;\n  border-radius: 12px;\n  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.06);\n}\n\n.shop-header {\n  display: flex;\n  flex-direction: row;\n  align-items: center;\n  margin-bottom: 16px;\n}\n\n.shop-icon {\n  width: 54px;\n  height: 54px;\n  border-radius: 10px;\n  margin-right: 14px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n  flex-shrink: 0;\n  transition: opacity 0.2s, transform 0.2s;\n}\n\n.shop-icon:active {\n  opacity: 0.8;\n  transform: scale(0.98);\n}\n\n.shop-info {\n  flex: 1;\n}\n\n.shop-name {\n  font-size: 18px;\n  font-weight: 700;\n  color: #000000;\n  margin-bottom: 6px;\n  display: block;\n  transition: color 0.2s;\n}\n\n.shop-name:active {\n  color: #007AFF;\n}\n\n.shop-description {\n  font-size: 14px;\n  color: #666666;\n  line-height: 1.4;\n  display: block;\n}\n\n.shop-contact {\n  margin: 12px 0 16px;\n  background-color: #F8F8FA;\n  border-radius: 10px;\n  padding: 10px 12px;\n}\n\n.contact-item {\n  display: flex;\n  align-items: center;\n  padding: 6px 0;\n}\n\n.contact-icon {\n  width: 20px;\n  height: 20px;\n  margin-right: 8px;\n}\n\n.contact-text {\n  font-size: 15px;\n  color: #3A3A3C;\n  flex: 1;\n}\n\n.address-text {\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n}\n\n.navigation-icon {\n  width: 20px;\n  height: 20px;\n  opacity: 0.7;\n  margin-left: 5px;\n}\n\n.shop-actions {\n  display: flex;\n  justify-content: center;\n  gap: 10px;\n  margin-top: 8px;\n}\n\n.shop-btn {\n  padding: 6rpx 20rpx;\n  font-size: 24rpx;\n  border-radius: 24rpx;\n  margin-left: 10rpx;\n}\n\n.primary-btn {\n  background: linear-gradient(to right, #007AFF, #5AC8FA);\n  color: #FFFFFF;\n  box-shadow: 0 4rpx 12rpx rgba(0, 122, 255, 0.2);\n}\n\n.secondary-btn {\n  background: rgba(0, 122, 255, 0.1);\n  color: #007AFF;\n  border: 1px solid rgba(0, 122, 255, 0.2);\n}\n\n/* 活动卡片 */\n.activities-card {\n  margin: 16px;\n  padding: 16px;\n  background-color: #FFFFFF;\n  border-radius: 12px;\n  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);\n  overflow: visible;\n}\n\n.section-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 16px;\n}\n\n.section-title {\n  font-size: 18px;\n  font-weight: 600;\n  color: #000000;\n}\n\n.add-btn {\n  font-size: 13px;\n  font-weight: 600;\n  background: linear-gradient(135deg, #F0F8FF 0%, #E6F3FF 100%);\n  color: #007AFF;\n  padding: 5px 10px;\n  border-radius: 14px;\n  box-shadow: 0 2px 6px rgba(0, 122, 255, 0.15);\n  position: relative;\n  overflow: hidden;\n  border: 0.5px solid rgba(0, 122, 255, 0.2);\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n  display: flex;\n  align-items: center;\n}\n\n.add-btn::before {\n  content: \"+\";\n  margin-right: 3px;\n  font-size: 16px;\n  font-weight: 700;\n  line-height: 0.8;\n  background: linear-gradient(135deg, #0095FF 0%, #006EE6 100%);\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n}\n\n.add-btn::after {\n  display: none;\n}\n\n.add-btn:active {\n  transform: translateY(1px) scale(0.98);\n  box-shadow: 0 1px 5px rgba(0, 122, 255, 0.15);\n  background: linear-gradient(135deg, #E6F3FF 0%, #D9ECFF 100%);\n}\n\n.activity-list {\n  display: flex;\n  flex-direction: column;\n  gap: 12px;\n}\n\n.activity-item {\n  padding: 14px;\n  background-color: #F7F7F9;\n  border-radius: 12px;\n  width: 100%;\n  box-sizing: border-box;\n  margin-bottom: 14px;\n  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);\n}\n\n.activity-item:last-child {\n  margin-bottom: 0;  /* 最后一项不需要底部外边距 */\n}\n\n.activity-image {\n  width: 100%;\n  height: 140px;\n  border-radius: 10px;\n  margin-bottom: 12px;\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.08);\n  object-fit: cover;\n}\n\n.activity-content {\n  margin-bottom: 12px;\n}\n\n.activity-header {\n  display: flex;\n  flex-direction: row;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 6px;\n  flex-wrap: wrap;\n}\n\n.activity-title {\n  font-size: 16px;\n  font-weight: 700;\n  color: #222222;\n  max-width: 70%;\n}\n\n.activity-time {\n  font-size: 13px;\n  color: #8E8E93;\n  flex-shrink: 0;\n}\n\n.activity-desc {\n  font-size: 14px;\n  color: #555555;\n  line-height: 1.4;\n}\n\n.activity-actions {\n  display: flex;\n  gap: 10px;\n  justify-content: flex-end;\n  margin-top: 8px;\n}\n\n.activity-btn {\n  height: 28px;\n  padding: 0 12px;\n  border-radius: 14px;\n  font-size: 12px;\n  font-weight: 600;\n  position: relative;\n  overflow: hidden;\n  box-shadow: 0 1px 5px rgba(0, 0, 0, 0.08);\n  border: 0.5px solid rgba(255, 255, 255, 0.5);\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n  background: linear-gradient(135deg, #ECF5FF 0%, #E2F0FF 100%);\n  color: #007AFF;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.activity-btn::before {\n  display: none;\n}\n\n.activity-btn::after {\n  display: none;\n}\n\n.activity-btn:active {\n  transform: translateY(1px) scale(0.98);\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\n  background: linear-gradient(135deg, #E2F0FF 0%, #D8EAFF 100%);\n}\n\n.activity-btn.primary {\n  background: linear-gradient(135deg, #0080FF 0%, #0066CC 100%);\n  color: #FFFFFF;\n  border: 0.5px solid rgba(0, 122, 255, 0.7);\n  box-shadow: 0 2px 8px rgba(0, 122, 255, 0.25), inset 0 1px 0 rgba(255, 255, 255, 0.2);\n}\n\n.activity-btn.primary:active {\n  background: linear-gradient(135deg, #0075E6 0%, #005CB8 100%);\n}\n\n/* 添加不同按钮类型的样式 */\n.activity-btn[data-type=\"edit\"] {\n  background: linear-gradient(135deg, #EEF6FF 0%, #E2F0FF 100%);\n  color: #0070E0;\n}\n\n.activity-btn[data-type=\"promote\"] {\n  background: linear-gradient(135deg, #FFF2E6 0%, #FFE8D1 100%);\n  color: #FF7D00;\n}\n\n.activity-btn[data-type=\"share\"] {\n  background: linear-gradient(135deg, #E9F9F0 0%, #DCF2E5 100%);\n  color: #27AE60;\n}\n\n/* 空状态 */\n.empty-card {\n  margin: 12px 16px;\n  padding: 32px 16px;\n  background-color: #FFFFFF;\n  border-radius: 12px;\n  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.06);\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n}\n\n.empty-icon {\n  width: 80px;\n  height: 80px;\n  margin-bottom: 16px;\n}\n\n.empty-icon.large {\n  width: 100px;\n  height: 100px;\n}\n\n.empty-text {\n  font-size: 16px;\n  color: #8E8E93;\n  margin-bottom: 16px;\n}\n\n.create-btn {\n  padding: 10px 28px;\n  font-size: 16px;\n  font-weight: 600;\n  background: linear-gradient(135deg, #007AFF 0%, #0066CC 100%);\n  color: #FFFFFF;\n  border-radius: 12px;\n  box-shadow: 0 4px 10px rgba(0, 122, 255, 0.25);\n  transition: all 0.3s ease;\n}\n\n.create-btn:active {\n  transform: scale(0.98);\n  box-shadow: 0 2px 6px rgba(0, 122, 255, 0.2);\n}\n\n/* 适配iPhone状态栏 */\n@supports (padding-top: constant(safe-area-inset-top)) {\n  .ios-navbar {\n    padding-top: constant(safe-area-inset-top);\n  }\n}\n\n@supports (padding-top: env(safe-area-inset-top)) {\n  .ios-navbar {\n    padding-top: env(safe-area-inset-top);\n  }\n}\n\n.tab-scroll {\n  height: 100%;\n  box-sizing: border-box;\n  padding-bottom: 20px;  /* 添加底部内边距，确保内容完全显示 */\n}\n\n/* 添加设置图标样式 */\n.settings-icon {\n  width: 24px;\n  height: 24px;\n  filter: brightness(0) invert(1);\n  opacity: 1;\n}\n\n.action-button {\n  height: 36px;\n  width: 36px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  border-radius: 18px;\n  background-color: rgba(255, 255, 255, 0.2);\n}\n\n.action-button:active {\n  background-color: rgba(255, 255, 255, 0.3);\n}\n\n/* 添加设置按钮样式 */\n.settings-btn {\n  font-size: 13px;\n  color: #007AFF;\n  padding: 4px 8px;\n  margin-left: auto;\n  font-weight: 500;\n  border-radius: 4px;\n  background-color: rgba(0, 122, 255, 0.08);\n}\n\n.settings-btn:active {\n  background-color: rgba(0, 122, 255, 0.15);\n}\n\n/* 发布内容样式 */\n.published-list {\n  padding: 15px;\n}\n\n.published-item {\n  background-color: #ffffff;\n  border-radius: 12px;\n  padding: 16px;\n  margin-bottom: 16px;\n  box-shadow: 0 1px 8px rgba(0, 0, 0, 0.03);\n}\n\n.publish-header {\n  display: flex;\n  justify-content: space-between;\n  margin-bottom: 10px;\n}\n\n.publish-type {\n  font-size: 14px;\n  color: #007AFF;\n  font-weight: 600;\n  padding: 2px 8px;\n  background-color: rgba(0, 122, 255, 0.1);\n  border-radius: 4px;\n}\n\n.publish-date {\n  font-size: 13px;\n  color: #888888;\n}\n\n.publish-content {\n  margin-bottom: 15px;\n}\n\n.publish-title {\n  font-size: 16px;\n  font-weight: 600;\n  color: #333333;\n  margin-bottom: 8px;\n  display: block;\n}\n\n.publish-desc {\n  font-size: 14px;\n  color: #666666;\n  line-height: 1.5;\n  margin-bottom: 12px;\n  display: block;\n}\n\n.publish-images {\n  display: flex;\n  flex-wrap: wrap;\n  margin: 0 -4px;\n}\n\n.publish-image {\n  width: calc(33.33% - 8px);\n  height: 80px;\n  margin: 4px;\n  border-radius: 6px;\n}\n\n.publish-actions {\n  display: flex;\n  justify-content: flex-end;\n  flex-wrap: wrap;\n  gap: 10px;\n}\n\n.add-more {\n  padding: 16px 0;\n  display: flex;\n  justify-content: center;\n}\n\n.add-btn {\n  background: linear-gradient(to right, #007AFF, #5AC8FA);\n  color: #FFFFFF;\n  font-size: 24rpx;\n  padding: 6rpx 16rpx;\n  border-radius: 20rpx;\n  box-shadow: 0 4rpx 12rpx rgba(0, 122, 255, 0.2);\n}\n\n.add-btn:active {\n  background-color: #eaeaea;\n}\n\n.add-icon {\n  font-size: 18px;\n  margin-right: 6px;\n  font-weight: 600;\n}\n\n/* 自定义弹窗样式 */\n.custom-modal {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background-color: rgba(0, 0, 0, 0.5);\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  z-index: 999;\n}\n\n.modal-mask {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background-color: rgba(0, 0, 0, 0.5);\n}\n\n.modal-content {\n  background-color: #ffffff;\n  border-radius: 22px;\n  padding: 24px 20px;\n  width: 75%;\n  max-width: 300px;\n  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);\n}\n\n.modal-title {\n  font-size: 17px;\n  font-weight: 600;\n  margin-bottom: 16px;\n  color: #333;\n  text-align: center;\n}\n\n.modal-body {\n  margin-bottom: 20px;\n}\n\n.modal-text {\n  font-size: 14px;\n  color: #333;\n  margin-bottom: 12px;\n  line-height: 1.4;\n  text-align: center;\n}\n\n.price-text {\n  font-size: 15px;\n  color: #333;\n  margin: 14px 0;\n  text-align: center;\n}\n\n.price-amount {\n  font-weight: 600;\n  color: #FF3B30;\n  font-size: 18px;\n}\n\n.rank-hint {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin: 14px 0;\n  background-color: #F8F8F8;\n  padding: 10px;\n  border-radius: 14px;\n}\n\n.rank-icon {\n  width: 18px;\n  height: 18px;\n  margin-right: 6px;\n}\n\n.special-hint {\n  font-size: 14px;\n  color: #FF3B30;\n  font-weight: 600;\n  font-style: italic;\n  margin-top: 14px;\n  text-align: center;\n}\n\n.modal-footer {\n  display: flex;\n  justify-content: space-between;\n  margin-top: 20px;\n  gap: 12px;\n}\n\n.modal-btn {\n  flex: 1;\n  padding: 10px 0;\n  border-radius: 20px;\n  font-size: 15px;\n  font-weight: 600;\n  border: none;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n  transition: all 0.2s ease;\n}\n\n.modal-btn::after {\n  border: none;\n}\n\n.modal-btn:active {\n  transform: scale(0.96);\n  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);\n}\n\n.cancel-btn {\n  background-color: #F5F5F5;\n  color: #333;\n}\n\n.confirm-btn {\n  background-color: #007AFF;\n  color: #fff;\n}\n\n/* 优化我的发布页面操作按钮样式 */\n.publish-actions button,\n.published-item .activity-btn {\n  min-width: 54px;\n  height: 28px;\n  padding: 0 12px;\n  border-radius: 16px;\n  font-size: 13px;\n  font-weight: 600;\n  background: linear-gradient(135deg, #ECF5FF 0%, #E2F0FF 100%);\n  color: #007AFF;\n  border: none;\n  box-shadow: 0 1px 4px rgba(0,0,0,0.06);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  transition: all 0.2s;\n}\n.publish-actions button:active,\n.published-item .activity-btn:active {\n  background: linear-gradient(135deg, #E2F0FF 0%, #D8EAFF 100%);\n  transform: scale(0.97);\n}\n.publish-actions button .icon,\n.published-item .activity-btn .icon {\n  display: none !important;\n}\n</style> ", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/pages/user-center/profile.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni", "smartNavigate", "res"], "mappings": ";;;;AAmQA,MAAA,kBAAA,MAAA;AAEA,MAAA,YAAA;AAAA;;;IAII;AAAA;EAEF,OAAA;AACE,WAAA;AAAA,MACE,iBAAA;AAAA,MACA,cAAA;AAAA,MACA,eAAA;AAAA;AAAA;;;;MAGA,QAAA;AAAA;AAAA;;;;;MAMA,UAAA;AAAA,QACE,QAAA;AAAA;;QAGA,OAAA;AAAA,QACA,QAAA;AAAA;;;;;QAMA,MAAA;AAAA,QACA,OAAA;AAAA,QACA,OAAA;AAAA;QAEA,OAAA;AAAA;MAEF,aAAA,CAAA;AAAA;MAEA,cAAA,CAAA;AAAA,MACA,WAAA,CAAA;AAAA;AAAA;;MAEA,SAAA,CAAA,MAAA,MAAA,IAAA;AAAA;AAAA,MACA,YAAA,CAAA,OAAA,OAAA,KAAA;AAAA;AAAA,MACA,UAAA;AAAA,QACE,EAAA,IAAA,GAAA,MAAA,WAAA,MAAA,eAAA,MAAA,2BAAA;AAAA,QACA,EAAA,IAAA,GAAA,MAAA,UAAA,MAAA,UAAA,MAAA,2BAAA;AAAA;;QAGA,MAAA;AAAA,QACA,MAAA;AAAA,QACA,MAAA;AAAA,QACA,OAAA;AAAA,QACA,SAAA;AAAA;MAEF,gBAAA,CAAA;AAAA,MACA,gBAAA;AAAA,QACE;AAAA;;UAGE,OAAA;AAAA,UACA,MAAA;AAAA,UACA,MAAA;AAAA,UACA,QAAA;AAAA,UACA,QAAA,CAAA;AAAA;QAEF;AAAA;;UAGE,OAAA;AAAA;UAEA,MAAA;AAAA,UACA,QAAA;AAAA,UACA,QAAA,CAAA,qCAAA;AAAA;QAEF;AAAA;;UAGE,OAAA;AAAA,UACA,MAAA;AAAA,UACA,MAAA;AAAA,UACA,QAAA;AAAA,UACA,QAAA,CAAA,uCAAA,qCAAA;AAAA,QACF;AAAA;MAEF,iBAAA;AAAA,QACE,EAAA,IAAA,GAAA,MAAA,QAAA,OAAA,GAAA,OAAA,EAAA;AAAA;;;;;;;IAMJ;AAAA;EAEF,UAAA;AAAA,IACE,eAAA;AACE,YAAA,WAAA,MAAA,KAAA,KAAA;AACA,aAAA;AAAA;QAEE,OAAA,GAAA,QAAA;AAAA,QACA,MAAA;AAAA,MACF;AAAA,IACF;AAAA;EAEF,OAAA,SAAA;AAEE,UAAA,UAAAA,oBAAA;AACA,SAAA,kBAAA,QAAA;AACA,SAAA,eAAA,KAAA,kBAAA;;MAIE,YAAA;AAAA;IAEF,CAAA;;;;;;;;;AAaE,WAAA,YAAA;AAAA;;;;;IAMF;AAGA,SAAA,YAAA;;;;EAQF,UAAA;;;EAIA,SAAA;AAAA;AAAA,IAEE,kBAAA;AAEE,iBAAA,MAAA;AACE,aAAA,aAAA,KAAA,OAAA,IAAA;AAAA,MACF,GAAA,GAAA;AAAA;;;AAMAA,oBAAA,MAAA,MAAA,OAAA,wCAAA,WAAA,KAAA,MAAA;AAAA;;IAIF,eAAA;AACE,UAAA,KAAA,YAAA;AAEEA,sBAAAA,MAAA,UAAA;AAAA;UAEE,SAAA;AAAA,UACA,SAAA,CAAA,QAAA;AACE,gBAAA,IAAA,SAAA;AAEE,yBAAA,MAAA;AACE,qBAAA,aAAA;AACA,qBAAA,UAAA;AACAA,8BAAAA,MAAA,UAAA;AAAA;;gBAGA,CAAA;AAAA,cACF,GAAA,GAAA;AAAA,YACF;AAAA,UACF;AAAA,QACF,CAAA;AAAA;AAIA,mBAAA,MAAA;AACE,eAAA,aAAA;AACA,eAAA,UAAA;AACAA,wBAAAA,MAAA,UAAA;AAAA;;UAGA,CAAA;AAAA,QACF,GAAA,GAAA;AAAA,MACF;AAAA;;;AAKAA,oBAAAA,MAAA,WAAA;AAAA,QACE,KAAA,2BAAA,KAAA,MAAA,aAAA,KAAA,SAAA,QAAA;AAAA;AAEEA,wBAAA,MAAA,MAAA,SAAA,wCAAA,SAAA,GAAA;AACAA,wBAAAA,MAAA,UAAA;AAAA,YACE,OAAA;AAAA,YACA,MAAA;AAAA;UAEF,CAAA;AAAA,QACF;AAAA,MACF,CAAA;AAAA;;IAIF,gBAAA;AAEE,iBAAA,MAAA;;AAGE,cAAA,WAAA,MAAA,KAAA,EAAA,QAAA,EAAA,GAAA,CAAA,GAAA,OAAA;AAAA,UACE,IAAA,SAAA,KAAA,KAAA,CAAA,CAAA,IAAA,CAAA;AAAA,UACA,OAAA,GAAA,WAAA,KAAA,MAAA,KAAA,OAAA,IAAA,WAAA,MAAA,CAAA,CAAA,IAAA,CAAA,QAAA,QAAA,QAAA,OAAA,MAAA,EAAA,KAAA,MAAA,KAAA,OAAA,IAAA,CAAA,CAAA,CAAA;AAAA;;UAGA,QAAA;AAAA;;;UAIA,EAAA,MAAA,GAAA,KAAA,MAAA,KAAA,OAAA,IAAA,CAAA,IAAA,CAAA;AAAA,UACA,OAAA,KAAA,MAAA,KAAA,OAAA,IAAA,GAAA,IAAA;AAAA,UACA,UAAA,KAAA,MAAA,KAAA,OAAA,IAAA,EAAA,IAAA;AAAA,UACA,OAAA,KAAA,MAAA,KAAA,OAAA,IAAA,EAAA,IAAA;AAAA,QACF,EAAA;AAEA,YAAA,KAAA,KAAA,CAAA,MAAA,GAAA;;;;QAIA;AAGA,aAAA,QAAA,CAAA,IAAA,KAAA,KAAA,CAAA,IAAA;;MAIF,GAAA,GAAA;AAAA;;IAIF,UAAA,OAAA;AACE,UAAA,KAAA,eAAA;AAAA;AACA,WAAA,aAAA;;;;;;;;;;;;;;;QAcE;AAAA;;;;;;;;;;QASA;AAAA,MACF;AAAA;;IAIF,eAAA,GAAA;AACE,WAAA,UAAA,EAAA,OAAA,OAAA;AAAA;;IAIF,SAAA,UAAA;;;AAGE,WAAA,KAAA,QAAA;;AAGE,gBAAA,UAAA;AAAA;;;;AAKI,iBAAA,cAAA;;;;;QAKJ;AAAA;AAEA,gBAAA,UAAA;AAAA;;;;;;;QAOA;AAAA,MACF;AAAA;;IAIF,UAAA,UAAA;AACE,WAAA,WAAA,QAAA,IAAA;AACA,WAAA,KAAA,QAAA,IAAA;;AAGE,gBAAA,UAAA;AAAA;;;;AAKI,iBAAA,cAAA;;;;;QAKJ;AAAA;AAEA,gBAAA,UAAA;AAAA;;;;;;;QAOA;AAAA,MACF;AAAA;;;AAMA,iBAAA,MAAA;AACE,aAAA,WAAA;AAAA,UACE,QAAA;AAAA,UACA,UAAA;AAAA,UACA,WAAA;AAAA,UACA,OAAA;AAAA;UAEA,UAAA;AAAA,UACA,UAAA;AAAA;MAEJ,GAAA,GAAA;AAAA;;IAIF,kBAAA;AAEE,iBAAA,MAAA;;;AAIE,cAAA,WAAA,MAAA,KAAA,EAAA,QAAA,EAAA,GAAA,CAAA,GAAA,OAAA;AAAA,UACE,IAAA,WAAA,KAAA,KAAA,CAAA,CAAA,IAAA,CAAA;AAAA,UACA,OAAA,GAAA,WAAA,KAAA,MAAA,KAAA,OAAA,IAAA,WAAA,MAAA,CAAA,CAAA,IAAA,CAAA,UAAA,UAAA,UAAA,UAAA,QAAA,EAAA,KAAA,MAAA,KAAA,OAAA,IAAA,CAAA,CAAA,CAAA;AAAA,UACA,MAAA,CAAA,0BAAA,2BAAA,6BAAA,uBAAA,6BAAA,EAAA,KAAA,MAAA,KAAA,OAAA,IAAA,CAAA,CAAA;AAAA;UAEA,QAAA;AAAA;;;UAIA,EAAA,MAAA,GAAA,KAAA,MAAA,KAAA,OAAA,IAAA,CAAA,IAAA,CAAA;AAAA,UACA,UAAA,UAAA,KAAA,MAAA,KAAA,OAAA,IAAA,UAAA,MAAA,CAAA;AAAA,UACA,OAAA,KAAA,MAAA,KAAA,OAAA,IAAA,GAAA,IAAA;AAAA,UACA,UAAA,KAAA,MAAA,KAAA,OAAA,IAAA,EAAA,IAAA;AAAA,UACA,OAAA,KAAA,MAAA,KAAA,OAAA,IAAA,EAAA,IAAA;AAAA,UACA,UAAA,WAAA,KAAA,MAAA,KAAA,OAAA,IAAA,WAAA,MAAA,CAAA;AAAA,QACF,EAAA;AAEA,YAAA,KAAA,KAAA,CAAA,MAAA,GAAA;;;;QAIA;AAGA,aAAA,QAAA,CAAA,IAAA,KAAA,KAAA,CAAA,IAAA;;MAIF,GAAA,GAAA;AAAA;;IAIF,gBAAA;AAEE,iBAAA,MAAA;;;AAIE,cAAA,WAAA,MAAA,KAAA,EAAA,QAAA,EAAA,GAAA,CAAA,GAAA,OAAA;AAAA,UACE,IAAA,SAAA,KAAA,KAAA,CAAA,CAAA,IAAA,CAAA;AAAA,UACA,OAAA,GAAA,WAAA,KAAA,MAAA,KAAA,OAAA,IAAA,WAAA,MAAA,CAAA,CAAA,IAAA,CAAA,UAAA,UAAA,UAAA,UAAA,QAAA,EAAA,KAAA,MAAA,KAAA,OAAA,IAAA,CAAA,CAAA,CAAA;AAAA,UACA,MAAA,CAAA,0BAAA,2BAAA,6BAAA,uBAAA,6BAAA,EAAA,KAAA,MAAA,KAAA,OAAA,IAAA,CAAA,CAAA;AAAA;UAEA,QAAA;AAAA;;;UAIA,EAAA,MAAA,GAAA,KAAA,MAAA,KAAA,OAAA,IAAA,CAAA,CAAA;AAAA,UACA,YAAA,YAAA,KAAA,MAAA,KAAA,OAAA,IAAA,YAAA,MAAA,CAAA;AAAA;UAEA,OAAA,KAAA,MAAA,KAAA,OAAA,IAAA,GAAA,IAAA;AAAA,UACA,UAAA,KAAA,MAAA,KAAA,OAAA,IAAA,EAAA,IAAA;AAAA,UACA,OAAA,KAAA,MAAA,KAAA,OAAA,IAAA,EAAA,IAAA;AAAA,UACA,UAAA,WAAA,KAAA,MAAA,KAAA,OAAA,IAAA,WAAA,MAAA,CAAA;AAAA,QACF,EAAA;AAEA,YAAA,KAAA,KAAA,CAAA,MAAA,GAAA;;;;QAIA;AAGA,aAAA,QAAA,CAAA,IAAA,KAAA,KAAA,CAAA,IAAA;;MAIF,GAAA,GAAA;AAAA;;IAIF,mBAAA;AAEE,iBAAA,MAAA;;UAEI;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA;AAGF,cAAA,eAAA;AAAA,UACE;AAAA,UACA;AAAA;;;;AAMF,cAAA,WAAA,MAAA,KAAA,EAAA,QAAA,EAAA,GAAA,CAAA,GAAA,OAAA;AAAA,UACE,IAAA,WAAA,KAAA,KAAA,CAAA,CAAA,IAAA,CAAA;AAAA,UACA,SAAA,iBAAA,KAAA,MAAA,KAAA,OAAA,IAAA,iBAAA,MAAA,CAAA;AAAA,UACA,aAAA,aAAA,KAAA,MAAA,KAAA,OAAA,IAAA,aAAA,MAAA,CAAA;AAAA;;UAGA,OAAA,KAAA,MAAA,KAAA,OAAA,IAAA,EAAA,IAAA;AAAA,UACA,SAAA,KAAA,MAAA,KAAA,OAAA,IAAA,CAAA;AAAA,QACF,EAAA;AAEA,YAAA,KAAA,KAAA,CAAA,MAAA,GAAA;;;;QAIA;AAGA,aAAA,QAAA,CAAA,IAAA,KAAA,KAAA,CAAA,IAAA;;MAIF,GAAA,GAAA;AAAA;;IAIF,WAAA,MAAA;;;;MAIE;AAEAA,oBAAAA,MAAA,WAAA;AAAA,QACE;AAAA;AAEEA,wBAAA,MAAA,MAAA,SAAA,wCAAA,SAAA,GAAA;AACAA,wBAAAA,MAAA,UAAA;AAAA,YACE,OAAA;AAAA,YACA,MAAA;AAAA;UAEF,CAAA;AAAA,QACF;AAAA,MACF,CAAA;AAAA;;IAIF,YAAA,MAAA;AACEA,oBAAAA,MAAA,WAAA;AAAA,QACE,KAAA,iCAAA,KAAA,QAAA;AAAA;AAEEA,wBAAA,MAAA,MAAA,SAAA,wCAAA,SAAA,GAAA;AACAA,wBAAAA,MAAA,UAAA;AAAA,YACE,OAAA;AAAA,YACA,MAAA;AAAA;UAEF,CAAA;AAAA,QACF;AAAA,MACF,CAAA;AAAA;;;AAKAA,oBAAAA,MAAA,WAAA;AAAA,QACE,KAAA;AAAA;AAEEA,wBAAA,MAAA,MAAA,SAAA,wCAAA,SAAA,GAAA;AACAA,wBAAAA,MAAA,UAAA;AAAA,YACE,OAAA;AAAA,YACA,MAAA;AAAA;UAEF,CAAA;AAAA,QACF;AAAA,MACF,CAAA;AAAA;;;AAKAA,oBAAAA,MAAA,WAAA;AAAA;;AAGIA,wBAAA,MAAA,MAAA,SAAA,wCAAA,SAAA,GAAA;AACAA,wBAAAA,MAAA,UAAA;AAAA,YACE,OAAA;AAAA,YACA,MAAA;AAAA;UAEF,CAAA;AAAA,QACF;AAAA,MACF,CAAA;AAAA;;;AAKAA,oBAAAA,MAAA,WAAA;AAAA,QACE,KAAA;AAAA;AAEEA,wBAAA,MAAA,MAAA,SAAA,wCAAA,SAAA,GAAA;AACAA,wBAAAA,MAAA,UAAA;AAAA,YACE,OAAA;AAAA,YACA,MAAA;AAAA;UAEF,CAAA;AAAA,QACF;AAAA,MACF,CAAA;AAAA;;;;AAOEA,sBAAAA,MAAA,gBAAA;AAAA;UAEE,SAAA,CAAA,QAAA;AACE,oBAAA,IAAA,UAAA;AAAA;AAEIA,8BAAAA,MAAA,WAAA;AAAA;kBAEE,MAAA,MAAA;AACEA,kCAAAA,MAAA,UAAA;AAAA,sBACE,OAAA;AAAA;oBAEF,CAAA;AAAA,kBACF;AAAA,gBACF,CAAA;;;AAGAA,8BAAAA,MAAA,WAAA;AAAA;kBAEE,MAAA,MAAA;AACEA,kCAAAA,MAAA,UAAA;AAAA,sBACE,OAAA;AAAA;oBAEF,CAAA;AAAA,kBACF;AAAA,gBACF,CAAA;;;AAGAA,8BAAAA,MAAA,WAAA;AAAA;kBAEE,MAAA,MAAA;AACEA,kCAAAA,MAAA,UAAA;AAAA,sBACE,OAAA;AAAA;oBAEF,CAAA;AAAA,kBACF;AAAA,gBACF,CAAA;;;AAGAA,8BAAAA,MAAA,WAAA;AAAA;kBAEE,MAAA,MAAA;AACEA,kCAAAA,MAAA,UAAA;AAAA,sBACE,OAAA;AAAA;oBAEF,CAAA;AAAA,kBACF;AAAA,gBACF,CAAA;;;AAGAA,8BAAAA,MAAA,WAAA;AAAA;kBAEE,MAAA,MAAA;AACEA,kCAAAA,MAAA,UAAA;AAAA,sBACE,OAAA;AAAA;oBAEF,CAAA;AAAA,kBACF;AAAA,gBACF,CAAA;;;AAGA,qBAAA,UAAA;;YAEJ;AAAA,UACF;AAAA,QACF,CAAA;AAAA;AAGAA,sBAAAA,MAAA,WAAA;AAAA,UACE,KAAA,+CAAA,KAAA,MAAA;AAAA;AAEEA,0BAAA,MAAA,MAAA,SAAA,wCAAA,SAAA,GAAA;AACAA,0BAAAA,MAAA,UAAA;AAAA,cACE,OAAA;AAAA,cACA,MAAA;AAAA;YAEF,CAAA;AAAA,UACF;AAAA,QACF,CAAA;AAAA,MACF;AAAA;;;AAKAA,oBAAAA,MAAA,UAAA;AAAA;QAEE,SAAA;AAAA,QACA,SAAA,CAAA,QAAA;AACE,cAAA,IAAA,SAAA;AACEA,0BAAAA,MAAA,WAAA;AAAA;cAEE,MAAA,MAAA;AACEA,8BAAAA,MAAA,YAAA;AAAA;gBAEA,CAAA;AAEA,2BAAA,MAAA;AACEA,gCAAA,MAAA,YAAA;AACAA,gCAAAA,MAAA,UAAA;AAAA;;kBAGA,CAAA;AAAA,gBACF,GAAA,GAAA;AAAA,cACF;AAAA,YACF,CAAA;AAAA,UACF;AAAA,QACF;AAAA,MACF,CAAA;AAAA;;IAIF,oBAAA;AACE,YAAA,QAAAA,cAAAA,MAAA,oBAAA,EAAA,GAAA,IAAA;;AAEE,cAAA,UAAA,KAAA;;;;MAIF,CAAA,EAAA,KAAA;AAAA;;IAIF,SAAA;AACEA,oBAAAA,MAAA,MAAA,OAAA,wCAAA,SAAA;AAGA,UAAAA,cAAAA,MAAA,cAAA;AACEA,sBAAA,MAAA,aAAA;AAAA,MACF;AAGAA,oBAAAA,MAAA,aAAA;AAAA,QACE,OAAA;AAAA,QACA,MAAA,SAAA,KAAA;AACEA,wBAAA,MAAA,MAAA,SAAA,wCAAA,YAAA,GAAA;AAGAA,wBAAAA,MAAA,UAAA;AAAA,YACE,KAAA;AAAA,UACF,CAAA;AAAA,QACF;AAAA,MACF,CAAA;AAAA;;IAIF,qBAAA;;AAEIA,sBAAA,MAAA,MAAA,SAAA,wCAAA,cAAA,GAAA;AAAA,MACF,CAAA;AAAA;;IAIF,WAAA,KAAA;AACEC,uBAAAA,cAAA,GAAA,EAAA,MAAA,SAAA;AACED,sBAAA,MAAA,MAAA,SAAA,wCAAA,WAAA,GAAA;AAAA,MACF,CAAA;AAAA;;;AAKAA,oBAAAA,MAAA,WAAA;AAAA,QACE,KAAA;AAAA;AAEEA,wBAAAA,MAAA,MAAA,OAAA,wCAAA,eAAA;AAAA;;AAGAA,wBAAA,MAAA,MAAA,SAAA,wCAAA,SAAA,GAAA;AAEAA,wBAAAA,MAAA,UAAA;AAAA,YACE,OAAA;AAAA,YACA,MAAA;AAAA;UAEF,CAAA;AAGA,qBAAA,MAAA;AACE,iBAAA,WAAA;AAAA,cACE,GAAA,KAAA;AAAA;;UAGJ,GAAA,GAAA;AAAA,QACF;AAAA,MACF,CAAA;AAAA;;IAIF,mBAAA;AACEA,oBAAAA,MAAA,WAAA;AAAA,QACE,KAAA;AAAA,MACF,CAAA;AAAA;;IAIF,YAAA,MAAA;AACEA,oBAAAA,MAAA,gBAAA;AAAA;QAEE,SAAA,CAAA,QAAA;AACE,kBAAA,IAAA,UAAA;AAAA;;;;;;YAOE,KAAA;AACE,mBAAA,oBAAA,IAAA;;UAEJ;AAAA,QACF;AAAA,MACF,CAAA;AAAA;;IAIF,WAAA,MAAA;AACEA,oBAAAA,MAAA,UAAA;AAAA,QACE,OAAA,KAAA,QAAA,SAAA;AAAA;QAEA,SAAA,CAAA,QAAA;AACE,cAAA,IAAA,SAAA;AAEEA,0BAAAA,MAAA,YAAA;AAAA;YAEA,CAAA;AAEA,uBAAA,MAAA;;AAEEA,4BAAA,MAAA,YAAA;AACAA,4BAAAA,MAAA,UAAA;AAAA,gBACE,OAAA,KAAA,QAAA,QAAA;AAAA;cAEF,CAAA;AAAA,YACF,GAAA,GAAA;AAAA,UACF;AAAA,QACF;AAAA,MACF,CAAA;AAAA;;IAIF,eAAA,MAAA;AACEA,oBAAAA,MAAA,UAAA;AAAA;QAEE,SAAA;AAAA,QACA,SAAA,CAAA,QAAA;AACE,cAAA,IAAA,SAAA;AAEEA,0BAAAA,MAAA,YAAA;AAAA;YAEA,CAAA;AAEA,uBAAA,MAAA;AACE,mBAAA,OAAA,KAAA,WAAA,oBAAA,KAAA,CAAA;AACAA,4BAAA,MAAA,YAAA;AACAA,4BAAAA,MAAA,UAAA;AAAA;;cAGA,CAAA;AAAA,YACF,GAAA,GAAA;AAAA,UACF;AAAA,QACF;AAAA,MACF,CAAA;AAAA;;IAIF,aAAA,MAAA;AACEA,oBAAAA,MAAA,WAAA;AAAA,QACE,KAAA,2BAAA,KAAA,EAAA;AAAA;AAEEA,wBAAA,MAAA,MAAA,SAAA,yCAAA,SAAA,GAAA;AAEAA,wBAAAA,MAAA,UAAA;AAAA;YAEE,SAAA;AAAA,YACA,SAAA,CAAA,QAAA;AACE,kBAAA,IAAA,SAAA;AACEA,8BAAAA,MAAA,YAAA;AAAA;gBAEA,CAAA;AAEA,2BAAA,MAAA;AACEA,gCAAA,MAAA,YAAA;AACAA,gCAAAA,MAAA,UAAA;AAAA;;kBAGA,CAAA;AAAA,gBACF,GAAA,GAAA;AAAA,cACF;AAAA,YACF;AAAA,UACF,CAAA;AAAA,QACF;AAAA,MACF,CAAA;AAAA;;IAIF,cAAA,MAAA;AACEA,oBAAAA,MAAA,UAAA;AAAA;;QAGE,SAAA,CAAA,QAAA;AACE,cAAA,IAAA,SAAA;AAEEA,0BAAAA,MAAA,YAAA;AAAA;YAEA,CAAA;AAEA,uBAAA,MAAA;;AAGE,kBAAA,UAAA,IAAA;AACE,qBAAA,YAAA,OAAA,OAAA,CAAA;AAAA,cACF;AAEAA,4BAAA,MAAA,YAAA;AACAA,4BAAAA,MAAA,UAAA;AAAA;;cAGA,CAAA;AAAA,YACF,GAAA,GAAA;AAAA,UACF;AAAA,QACF;AAAA,MACF,CAAA;AAAA;;IAIF,WAAA,MAAA;;;;AAIE,aAAA,GAAA,IAAA,IAAA,KAAA,IAAA,GAAA;AAAA;IAGF,aAAA,MAAA;AACEA,0BAAA,WAAA,EAAA,KAAA,+BAAA,KAAA,EAAA,GAAA,CAAA;AAAA;IAGF,SAAA,MAAA;AACEA,0BAAA,WAAA,EAAA,KAAA,+BAAA,KAAA,EAAA,UAAA,CAAA;AAAA;;AAIAA,oBAAAA,MAAA,gBAAA;AAAA,QACE,UAAA,CAAA,QAAA,QAAA,MAAA;AAAA,QACA,SAAA,CAAA,QAAA;AACE,kBAAA,IAAA,UAAA;AAAA,YACE,KAAA;;;YAGA,KAAA;;;YAGA,KAAA;;;UAGF;AAAA,QACF;AAAA,MACF,CAAA;AAAA;;IAIF,qBAAA;AACEA,oBAAAA,MAAA,gBAAA;AAAA;QAEE,SAAA,CAAA,QAAA;AACE,kBAAA,IAAA,UAAA;AAAA;AAEI,mBAAA,UAAA;;;AAGA,mBAAA,YAAA;;UAEJ;AAAA,QACF;AAAA,MACF,CAAA;AAAA;;;AAKAA,oBAAAA,MAAA,gBAAA;AAAA;QAEE,SAAA,CAAA,QAAA;AACE,kBAAA,IAAA,UAAA;AAAA;AAEI,mBAAA,cAAA;;;;;UAKJ;AAAA,QACF;AAAA,MACF,CAAA;AAAA;;;AAKAA,oBAAAA,MAAA,UAAA;AAAA;QAEE,SAAA;AAAA,QACA,aAAA;AAAA,QACA,SAAA,CAAA,QAAA;AACE,cAAA,IAAA,SAAA;AACEA,0BAAAA,MAAA,YAAA,EAAA,OAAA,WAAA,CAAA;AAGA,uBAAA,MAAA;AACEA,4BAAA,MAAA,YAAA;AAGA,yBAAA,MAAA;AACEA,8BAAAA,MAAA,UAAA;AAAA;;gBAGA,CAAA;AAAA,cACF,GAAA,GAAA;AAAA,YACF,GAAA,IAAA;AAAA,UACF;AAAA,QACF;AAAA,MACF,CAAA;AAAA;;;AAKAA,oBAAAA,MAAA,UAAA;AAAA;QAEE,SAAA;AAAA,QACA,aAAA;AAAA,QACA,SAAA,CAAA,QAAA;AACE,cAAA,IAAA,SAAA;AACEA,0BAAAA,MAAA,gBAAA;AAAA,cACE,UAAA,CAAA,YAAA,aAAA,WAAA;AAAA,cACA,SAAA,CAAAE,SAAA;AAEE,sBAAA,OAAA,CAAA,GAAA,IAAA,EAAA;AAEAF,8BAAAA,MAAA,YAAA,EAAA,OAAA,SAAA,CAAA;AAGA,2BAAA,MAAA;AACEA,gCAAA,MAAA,YAAA;AACAA,gCAAAA,MAAA,UAAA;AAAA,oBACE,OAAA,MAAA,KAAAE,KAAA,QAAA,CAAA;AAAA;kBAEF,CAAA;AAAA,gBACF,GAAA,IAAA;AAAA,cACF;AAAA,YACF,CAAA;AAAA,UACF;AAAA,QACF;AAAA,MACF,CAAA;AAAA;;IAIF,gBAAA;AACEF,oBAAAA,MAAA,UAAA;AAAA;QAEE,SAAA;AAAA,QACA,aAAA;AAAA,QACA,SAAA,CAAA,QAAA;AACE,cAAA,IAAA,SAAA;AACEA,0BAAAA,MAAA,YAAA,EAAA,OAAA,WAAA,CAAA;AAGA,uBAAA,MAAA;AACEA,4BAAA,MAAA,YAAA;AAGA,yBAAA,MAAA;AACEA,8BAAAA,MAAA,UAAA;AAAA;;gBAGA,CAAA;AAAA,cACF,GAAA,GAAA;AAAA,YACF,GAAA,IAAA;AAAA,UACF;AAAA,QACF;AAAA,MACF,CAAA;AAAA;;IAIF,kBAAA;;AAII,aAAA,qBAAA,IAAA;AACA;AAAA,MACF;AAGAA,oBAAAA,MAAA,UAAA;AAAA;;QAGE,aAAA;AAAA,QACA,YAAA;AAAA,QACA,SAAA,CAAA,QAAA;AACE,cAAA,IAAA,SAAA;;AAIEA,0BAAAA,MAAA,YAAA,EAAA,OAAA,SAAA,CAAA;AAGA,uBAAA,MAAA;AACEA,4BAAA,MAAA,YAAA;AACAA,4BAAAA,MAAA,UAAA;AAAA;;cAGA,CAAA;AAAA,YACF,GAAA,GAAA;AAAA;;AAIAA,0BAAAA,MAAA,WAAA;AAAA,cACE,KAAA;AAAA,YACF,CAAA;AAAA,UACF;AAAA,QACF;AAAA,MACF,CAAA;AAAA;;;;AAQE,aAAA,qBAAA,IAAA;AACA;AAAA,MACF;AAGAA,oBAAAA,MAAA,UAAA;AAAA;;QAGE,aAAA;AAAA,QACA,YAAA;AAAA,QACA,SAAA,CAAA,QAAA;AACE,cAAA,IAAA,SAAA;;AAIEA,0BAAAA,MAAA,YAAA,EAAA,OAAA,SAAA,CAAA;AAGA,uBAAA,MAAA;AACEA,4BAAA,MAAA,YAAA;AACAA,4BAAAA,MAAA,UAAA;AAAA;;cAGA,CAAA;AAAA,YACF,GAAA,GAAA;AAAA;;AAIAA,0BAAAA,MAAA,WAAA;AAAA,cACE,KAAA;AAAA,YACF,CAAA;AAAA,UACF;AAAA,QACF;AAAA,MACF,CAAA;AAAA;;;;AAQE,aAAA,qBAAA,IAAA;AACA;AAAA,MACF;AAGAA,oBAAAA,MAAA,UAAA;AAAA;;QAGE,aAAA;AAAA,QACA,YAAA;AAAA,QACA,SAAA,CAAA,QAAA;AACE,cAAA,IAAA,SAAA;;AAIEA,0BAAAA,MAAA,YAAA,EAAA,OAAA,SAAA,CAAA;AAGA,uBAAA,MAAA;AACEA,4BAAA,MAAA,YAAA;AACAA,4BAAAA,MAAA,UAAA;AAAA;;cAGA,CAAA;AAAA,YACF,GAAA,GAAA;AAAA;;AAIAA,0BAAAA,MAAA,WAAA;AAAA,cACE,KAAA;AAAA,YACF,CAAA;AAAA,UACF;AAAA,QACF;AAAA,MACF,CAAA;AAAA;;IAIF,wBAAA,MAAA,OAAA;AACEA,oBAAAA,MAAA,UAAA;AAAA;QAEE,SAAA,MAAA,KAAA,mBAAA,IAAA;AAAA,QACA,aAAA;AAAA,QACA,YAAA;AAAA,QACA,SAAA,CAAA,QAAA;AACE,cAAA,IAAA,SAAA;AACEA,0BAAAA,MAAA,YAAA,EAAA,OAAA,SAAA,CAAA;AAGA,uBAAA,MAAA;;AAIEA,4BAAA,MAAA,YAAA;AACAA,4BAAAA,MAAA,UAAA;AAAA,gBACE,OAAA,GAAA,IAAA;AAAA;cAEF,CAAA;AAAA,YACF,GAAA,GAAA;AAAA,UACF,WAAA,IAAA,QAAA;AACE,iBAAA,oBAAA,IAAA;AAAA,UACF;AAAA,QACF;AAAA,MACF,CAAA;AAAA;;;AAMA,WAAA,uBAAA,IAAA;AAAA;;;;QAOE,MAAA;AAAA;QAEA;AAAA,QACA,YAAA;AAAA,QACA,YAAA;AAAA,MACF,CAAA;AAAA;;;AAKAA,oBAAAA,MAAA,gBAAA;AAAA,QACE,UAAA,KAAA,gBAAA;AAAA,UAAA,SACE,GAAA,IAAA,IAAA,IAAA,IAAA,KAAA,IAAA,IAAA,WAAA,OAAA,IAAA,WAAA,MAAA,EAAA;AAAA;QAEF,SAAA,CAAA,aAAA;;AAGEA,wBAAAA,MAAA,YAAA,EAAA,OAAA,SAAA,CAAA;AAGA,qBAAA,MAAA;AACEA,0BAAA,MAAA,YAAA;AAGAA,0BAAAA,MAAA,UAAA;AAAA;cAEE,SAAA,OAAA,gBAAA,IAAA,OAAA,gBAAA,KAAA;AAAA,cACA,aAAA;AAAA,cACA,YAAA;AAAA,cACA,SAAA,CAAA,WAAA;AACE,oBAAA,OAAA,SAAA;AACEA,gCAAAA,MAAA,YAAA,EAAA,OAAA,SAAA,CAAA;AAGA,6BAAA,MAAA;AAEE,yBAAA,oBAAA,gBAAA;AAEAA,kCAAA,MAAA,YAAA;AACAA,kCAAAA,MAAA,UAAA;AAAA;;oBAGA,CAAA;AAGA,+BAAA,MAAA;;oBAEA,GAAA,GAAA;AAAA,kBACF,GAAA,IAAA;AAAA,gBACF;AAAA,cACF;AAAA,YACF,CAAA;AAAA,UACF,GAAA,GAAA;AAAA,QACF;AAAA,MACF,CAAA;AAAA;;;AAKAA,oBAAAA,MAAA,UAAA;AAAA;QAEE,SAAA,mBAAA,IAAA;AAAA,QACA,aAAA;AAAA,QACA,YAAA;AAAA,QACA,SAAA,CAAA,QAAA;AACE,cAAA,IAAA,SAAA;AACEA,0BAAAA,MAAA,YAAA,EAAA,OAAA,SAAA,CAAA;AAGA,uBAAA,MAAA;;AAIEA,4BAAA,MAAA,YAAA;AACAA,4BAAAA,MAAA,UAAA;AAAA,gBACE,OAAA,GAAA,IAAA;AAAA;cAEF,CAAA;AAAA,YACF,GAAA,GAAA;AAAA,UACF;AAAA,QACF;AAAA,MACF,CAAA;AAAA;IAGF,UAAA,OAAA;AACEA,oBAAAA,MAAA,cAAA,EAAA,aAAA,MAAA,CAAA;AAAA;;;;IAOF,iBAAA,KAAA;;;IAIA,aAAA,KAAA;AACEA,oBAAAA,MAAA,gBAAA;AAAA;QAEE,SAAA,CAAA,QAAA;AACE,kBAAA,IAAA,UAAA;AAAA;;;;;;YAOE,KAAA;AACE,mBAAA,qBAAA,GAAA;;UAEJ;AAAA,QACF;AAAA,MACF,CAAA;AAAA;;;AAKAA,oBAAAA,MAAA,gBAAA;AAAA,QACE,UAAA,CAAA,QAAA,QAAA,MAAA;AAAA,QACA,SAAA,CAAA,QAAA;AACE,kBAAA,IAAA,UAAA;AAAA,YACE,KAAA;;;YAGA,KAAA;;;YAGA,KAAA;AACEA,4BAAAA,MAAA,WAAA;AAAA;cAEA,CAAA;;UAEJ;AAAA,QACF;AAAA,MACF,CAAA;AAAA;;IAIF,eAAA,KAAA;AACEA,oBAAAA,MAAA,gBAAA;AAAA;QAEE,SAAA,CAAA,QAAA;AACE,kBAAA,IAAA,UAAA;AAAA;;;;;;UAOA;AAAA,QACF;AAAA,MACF,CAAA;AAAA;;;AAKAA,oBAAAA,MAAA,gBAAA;AAAA;QAEE,SAAA,CAAA,QAAA;AACE,kBAAA,IAAA,UAAA;AAAA;;;;;;UAOA;AAAA,QACF;AAAA,MACF,CAAA;AAAA;;IAIF,cAAA,KAAA;AACEA,oBAAAA,MAAA,UAAA;AAAA;QAEE,SAAA;AAAA,QACA,aAAA;AAAA,QACA,SAAA,CAAA,QAAA;AACE,cAAA,IAAA,SAAA;AACEA,0BAAAA,MAAA,YAAA,EAAA,OAAA,WAAA,CAAA;AAGA,uBAAA,MAAA;AACEA,4BAAA,MAAA,YAAA;AAGA,yBAAA,MAAA;AACEA,8BAAAA,MAAA,UAAA;AAAA;;gBAGA,CAAA;AAAA,cACF,GAAA,GAAA;AAAA,YACF,GAAA,IAAA;AAAA,UACF;AAAA,QACF;AAAA,MACF,CAAA;AAAA;;IAIF,gBAAA,KAAA;AACEA,oBAAAA,MAAA,UAAA;AAAA;;QAGE,aAAA;AAAA,QACA,SAAA,CAAA,QAAA;AACE,cAAA,IAAA,SAAA;AACEA,0BAAAA,MAAA,gBAAA;AAAA,cACE,UAAA,CAAA,WAAA,YAAA,WAAA;AAAA,cACA,SAAA,CAAAE,SAAA;AAEE,sBAAA,OAAA,CAAA,GAAA,GAAA,EAAA;AAEAF,8BAAAA,MAAA,YAAA,EAAA,OAAA,SAAA,CAAA;AAGA,2BAAA,MAAA;AACEA,gCAAA,MAAA,YAAA;AACAA,gCAAAA,MAAA,UAAA;AAAA,oBACE,OAAA,MAAA,KAAAE,KAAA,QAAA,CAAA;AAAA;kBAEF,CAAA;AAAA,gBACF,GAAA,IAAA;AAAA,cACF;AAAA,YACF,CAAA;AAAA,UACF;AAAA,QACF;AAAA,MACF,CAAA;AAAA;;;AAKAF,oBAAAA,MAAA,UAAA;AAAA;QAEE,SAAA;AAAA,QACA,aAAA;AAAA,QACA,SAAA,CAAA,QAAA;AACE,cAAA,IAAA,SAAA;AACEA,0BAAAA,MAAA,YAAA,EAAA,OAAA,WAAA,CAAA;AAGA,uBAAA,MAAA;AACEA,4BAAA,MAAA,YAAA;AAGA,yBAAA,MAAA;AACEA,8BAAAA,MAAA,UAAA;AAAA;;gBAGA,CAAA;AAAA,cACF,GAAA,GAAA;AAAA,YACF,GAAA,IAAA;AAAA,UACF;AAAA,QACF;AAAA,MACF,CAAA;AAAA;;;;AAQE,aAAA,qBAAA,IAAA;AACA;AAAA,MACF;AAGAA,oBAAAA,MAAA,UAAA;AAAA;;QAGE,aAAA;AAAA,QACA,YAAA;AAAA,QACA,SAAA,CAAA,QAAA;AACE,cAAA,IAAA,SAAA;;AAIEA,0BAAAA,MAAA,YAAA,EAAA,OAAA,SAAA,CAAA;AAGA,uBAAA,MAAA;AACEA,4BAAA,MAAA,YAAA;AACAA,4BAAAA,MAAA,UAAA;AAAA;;cAGA,CAAA;AAAA,YACF,GAAA,GAAA;AAAA;;AAIAA,0BAAAA,MAAA,WAAA;AAAA,cACE,KAAA;AAAA,YACF,CAAA;AAAA,UACF;AAAA,QACF;AAAA,MACF,CAAA;AAAA;;IAIF,eAAA,KAAA;AAEEA,oBAAAA,MAAA,WAAA;AAAA;QAEE,MAAA,MAAA;AAEEA,wBAAAA,MAAA,UAAA;AAAA;YAEE,SAAA;AAAA,QAAA,IAAA,KAAA;AAAA,QAAA,IAAA,IAAA;AAAA,QAAA,IAAA,IAAA;AAAA,YACA,aAAA;AAAA,YACA,SAAA,CAAA,QAAA;AACE,kBAAA,IAAA,SAAA;AACEA,8BAAAA,MAAA,gBAAA;AAAA;kBAEE,SAAA,CAAA,aAAA;;;;;;;;;;;;;;oBAcE;AAAA,kBACF;AAAA,gBACF,CAAA;AAAA,cACF;AAAA,YACF;AAAA,UACF,CAAA;AAAA,QACF;AAAA,MACF,CAAA;AAAA;;;AAKAA,oBAAAA,MAAA,UAAA;AAAA,QACE,OAAA;AAAA;;QAGA,SAAA,CAAA,QAAA;;AAEIA,0BAAAA,MAAA,YAAA,EAAA,OAAA,SAAA,CAAA;AAGA,uBAAA,MAAA;AAEE,kBAAA,QAAA,IAAA;AAEAA,4BAAA,MAAA,YAAA;AACAA,4BAAAA,MAAA,UAAA;AAAA;;cAGA,CAAA;AAAA,YACF,GAAA,GAAA;AAAA,UACF;AAAA,QACF;AAAA,MACF,CAAA;AAAA;;;;AAMA,SAAA,YAAA,YAAA,CAAA,IAAA,OAAA,YAAA,SAAA,IAAA,CAAA,EAAA,SAAA,GAAA,GAAA,CAAA,IAAA,OAAA,YAAA,SAAA,EAAA,SAAA,GAAA,GAAA,CAAA;AAEAA,oBAAAA,MAAA,UAAA;AAAA,QACE,OAAA;AAAA;QAEA,SAAA,CAAA,QAAA;AACE,cAAA,IAAA,SAAA;AAEEA,0BAAAA,MAAA,gBAAA;AAAA,cACE,UAAA;AAAA;;;;cAKA,SAAA,CAAA,YAAA;AACEA,8BAAAA,MAAA,YAAA,EAAA,OAAA,SAAA,CAAA;AAGA,sBAAA,QAAA,oBAAA;;;;;;AAOI,4BAAA,QAAA,MAAA,QAAA,IAAA,CAAA;;;;AAIA,4BAAA,QAAA,MAAA,QAAA,IAAA,CAAA;;kBAEF,KAAA;;AAEE,8BAAA,QAAA,MAAA,QAAA,IAAA,CAAA;;AAEA,4BAAA,QAAA,UAAA,QAAA,IAAA,EAAA;;gBAEJ;;;;AAOA,sBAAA,UAAA,GAAA,WAAA,SAAA,CAAA,MAAA,WAAA,OAAA,CAAA;AAGA,2BAAA,MAAA;AAEE,sBAAA,OAAA;AAEAA,gCAAA,MAAA,YAAA;AACAA,gCAAAA,MAAA,UAAA;AAAA;;kBAGA,CAAA;AAAA,gBACF,GAAA,GAAA;AAAA,cACF;AAAA,YACF,CAAA;AAAA,UACF;AAAA,QACF;AAAA,MACF,CAAA;AAAA;;;AAKAA,oBAAAA,MAAA,UAAA;AAAA,QACE,OAAA;AAAA;;QAGA,SAAA,CAAA,QAAA;;AAEIA,0BAAAA,MAAA,YAAA,EAAA,OAAA,SAAA,CAAA;AAGA,uBAAA,MAAA;AAEE,kBAAA,OAAA,IAAA;AAEAA,4BAAA,MAAA,YAAA;AACAA,4BAAAA,MAAA,UAAA;AAAA;;cAGA,CAAA;AAAA,YACF,GAAA,GAAA;AAAA,UACF;AAAA,QACF;AAAA,MACF,CAAA;AAAA;;;AAKAA,oBAAAA,MAAA,gBAAA;AAAA;QAEE,SAAA,CAAA,QAAA;AACEA,wBAAAA,MAAA,YAAA,EAAA,OAAA,SAAA,CAAA;AAGA,qBAAA,MAAA;AACEA,0BAAA,MAAA,YAAA;AAGAA,0BAAAA,MAAA,YAAA,EAAA,OAAA,SAAA,CAAA;AAEA,uBAAA,MAAA;AACEA,4BAAA,MAAA,YAAA;AACAA,4BAAAA,MAAA,UAAA;AAAA;;cAGA,CAAA;AAAA,YAGF,GAAA,IAAA;AAAA,UACF,GAAA,GAAA;AAAA,QACF;AAAA,MACF,CAAA;AAAA;;IAIF,eAAA,KAAA;AACEA,oBAAAA,MAAA,UAAA;AAAA;;QAGE,aAAA;AAAA;QAEA,SAAA,CAAA,QAAA;AACE,cAAA,IAAA,SAAA;AACEA,0BAAAA,MAAA,YAAA,EAAA,OAAA,SAAA,CAAA;AAGA,uBAAA,MAAA;AAEE,oBAAA,QAAA,KAAA,eAAA,UAAA,UAAA,KAAA,OAAA,IAAA,EAAA;;AAEE,qBAAA,eAAA,OAAA,OAAA,CAAA;AAAA,cACF;AAEAA,4BAAA,MAAA,YAAA;AACAA,4BAAAA,MAAA,UAAA;AAAA;;cAGA,CAAA;AAAA,YACF,GAAA,GAAA;AAAA,UACF;AAAA,QACF;AAAA,MACF,CAAA;AAAA;;;AAKA,YAAA,WAAA,IAAA,WAAA;AACA,YAAA,aAAA,WAAA,OAAA;AAEAA,oBAAAA,MAAA,UAAA;AAAA;QAEE,SAAA,MAAA,UAAA;AAAA,QACA,SAAA,CAAA,QAAA;AACE,cAAA,IAAA,SAAA;AACEA,0BAAAA,MAAA,YAAA,EAAA,OAAA,SAAA,CAAA;AAGA,uBAAA,MAAA;AAEE,kBAAA,SAAA,WAAA,YAAA;AAEAA,4BAAA,MAAA,YAAA;AACAA,4BAAAA,MAAA,UAAA;AAAA;;cAGA,CAAA;AAAA,YACF,GAAA,GAAA;AAAA,UACF;AAAA,QACF;AAAA,MACF,CAAA;AAAA;;;AAKAC,qCAAA;AAAA;MAEA,CAAA;AAAA;;;AAMAD,oBAAAA,MAAA,cAAA;AAAA,QACE,iBAAA;AAAA,QACA,OAAA,CAAA,mBAAA,eAAA;AAAA,MACF,CAAA;AAEAA,oBAAAA,MAAA,UAAA;AAAA,QACE,OAAA;AAAA;;MAGF,CAAA;AAAA;;;AAKAC,qCAAA;AAAA;MAEA,CAAA;AAAA;;IAIF,mBAAA,SAAA,MAAA;;AAIE,YAAA,YAAA;AAEAD,oBAAAA,MAAA,gBAAA;AAAA;QAEE,SAAA,CAAA,QAAA;AACE,cAAA,IAAA,aAAA,GAAA;AAEEA,0BAAAA,MAAA,aAAA;AAAA,cACE;AAAA,cACA;AAAA,cACA,MAAA,QAAA;AAAA,cACA;AAAA,cACA,OAAA;AAAA;AAEEA,8BAAAA,MAAA,MAAA,OAAA,yCAAA,QAAA;AAAA;;AAGAA,8BAAA,MAAA,MAAA,SAAA,yCAAA,WAAA,GAAA;AACAA,8BAAAA,MAAA,UAAA;AAAA,kBACE,OAAA;AAAA;gBAEF,CAAA;AAAA,cACF;AAAA,YACF,CAAA;AAAA;AASAA,0BAAAA,MAAA,aAAA;AAAA,cACE;AAAA,cACA;AAAA,cACA,MAAA,QAAA;AAAA,cACA;AAAA;AAEEA,8BAAAA,MAAA,MAAA,OAAA,yCAAA,QAAA;AAAA;;AAGAA,8BAAA,MAAA,MAAA,SAAA,yCAAA,WAAA,GAAA;AACAA,8BAAAA,MAAA,UAAA;AAAA,kBACE,OAAA;AAAA;gBAEF,CAAA;AAAA,cACF;AAAA,YACF,CAAA;AAAA,UAMF;AAAA,QACF;AAAA,MACF,CAAA;AAAA;;IAIF,qBAAA;AACEA,oBAAAA,MAAA,MAAA,OAAA,yCAAA,UAAA;AAGA,YAAA,iBAAA;AAAA,QACE;AAAA;UAEE,OAAA;AAAA,UACA,MAAA;AAAA,UACA,OAAA;AAAA;UAEA,QAAA;AAAA;AAAA;QAEF;AAAA;;;UAIE,OAAA;AAAA,UACA,MAAA;AAAA,UACA,QAAA;AAAA;AAAA;QAEF;AAAA;;UAGE,MAAA;AAAA,UACA,OAAA;AAAA;UAEA,QAAA;AAAA;AAAA,QACF;AAAA;AAIF,WAAA,iBAAA;AACAA,oBAAA,MAAA,MAAA,OAAA,yCAAA,eAAA,KAAA,cAAA;AAAA;;IAIF,iBAAA;AACEC,qCAAA;AAAA;MAEA,CAAA;AAAA;;IAIF,iBAAA;AACEA,qCAAA;AAAA;MAEA,CAAA;AAAA;;IAIF,iBAAA;AACEA,qCAAA;AAAA;MAEA,CAAA;AAAA;;IAIF,YAAA,MAAA;AACED,oBAAAA,MAAA,gBAAA;AAAA;QAEE,SAAA,CAAA,QAAA;AACE,kBAAA,IAAA,UAAA;AAAA;;;;;;YAOE,KAAA;AACE,mBAAA,oBAAA,IAAA;;UAEJ;AAAA,QACF;AAAA,MACF,CAAA;AAAA;;IAIF,eAAA,MAAA;AACEA,oBAAAA,MAAA,gBAAA;AAAA,QACE,UAAA,CAAA,QAAA,QAAA,MAAA;AAAA,QACA,SAAA,CAAA,QAAA;AACE,kBAAA,IAAA,UAAA;AAAA,YACE,KAAA;AACE,mBAAA,sBAAA,IAAA;;YAEF,KAAA;;;YAGA,KAAA;AACEA,4BAAAA,MAAA,WAAA;AAAA,gBACE,KAAA,iCAAA,KAAA,EAAA;AAAA,cACF,CAAA;;UAEJ;AAAA,QACF;AAAA,MACF,CAAA;AAAA;;;AAKAA,oBAAAA,MAAA,gBAAA;AAAA;QAEE,SAAA,CAAA,QAAA;AACE,kBAAA,IAAA,UAAA;AAAA;;;;;;UAOA;AAAA,QACF;AAAA,MACF,CAAA;AAAA;;IAIF,0BAAA,MAAA;AACEA,oBAAAA,MAAA,gBAAA;AAAA;QAEE,SAAA,CAAA,QAAA;AACE,kBAAA,IAAA,UAAA;AAAA;;;;;;UAOA;AAAA,QACF;AAAA,MACF,CAAA;AAAA;;IAIF,aAAA,MAAA;AACEA,oBAAAA,MAAA,UAAA;AAAA;QAEE,SAAA;AAAA,QACA,aAAA;AAAA,QACA,SAAA,CAAA,QAAA;AACE,cAAA,IAAA,SAAA;AACEA,0BAAAA,MAAA,YAAA,EAAA,OAAA,WAAA,CAAA;AAGA,uBAAA,MAAA;AACEA,4BAAA,MAAA,YAAA;AAGA,yBAAA,MAAA;AACEA,8BAAAA,MAAA,UAAA;AAAA;;gBAGA,CAAA;AAAA,cACF,GAAA,GAAA;AAAA,YACF,GAAA,IAAA;AAAA,UACF;AAAA,QACF;AAAA,MACF,CAAA;AAAA;;IAIF,eAAA,MAAA;AACEA,oBAAAA,MAAA,UAAA;AAAA;;QAGE,aAAA;AAAA,QACA,SAAA,CAAA,QAAA;AACE,cAAA,IAAA,SAAA;AACEA,0BAAAA,MAAA,gBAAA;AAAA,cACE,UAAA,CAAA,WAAA,WAAA,WAAA;AAAA,cACA,SAAA,CAAAE,SAAA;AAEE,sBAAA,OAAA,CAAA,GAAA,GAAA,EAAA;AAEAF,8BAAAA,MAAA,YAAA,EAAA,OAAA,SAAA,CAAA;AAGA,2BAAA,MAAA;AACEA,gCAAA,MAAA,YAAA;AACAA,gCAAAA,MAAA,UAAA;AAAA,oBACE,OAAA,MAAA,KAAAE,KAAA,QAAA,CAAA;AAAA;kBAEF,CAAA;AAAA,gBACF,GAAA,IAAA;AAAA,cACF;AAAA,YACF,CAAA;AAAA,UACF;AAAA,QACF;AAAA,MACF,CAAA;AAAA;;;AAKAF,oBAAAA,MAAA,UAAA;AAAA;QAEE,SAAA;AAAA,QACA,aAAA;AAAA,QACA,SAAA,CAAA,QAAA;AACE,cAAA,IAAA,SAAA;AACEA,0BAAAA,MAAA,YAAA,EAAA,OAAA,WAAA,CAAA;AAGA,uBAAA,MAAA;AACEA,4BAAA,MAAA,YAAA;AAGA,yBAAA,MAAA;AACEA,8BAAAA,MAAA,UAAA;AAAA;;gBAGA,CAAA;AAAA,cACF,GAAA,GAAA;AAAA,YACF,GAAA,IAAA;AAAA,UACF;AAAA,QACF;AAAA,MACF,CAAA;AAAA;;;;AAQE,aAAA,qBAAA,IAAA;AACA;AAAA,MACF;AAGAA,oBAAAA,MAAA,UAAA;AAAA;;QAGE,aAAA;AAAA,QACA,YAAA;AAAA,QACA,SAAA,CAAA,QAAA;AACE,cAAA,IAAA,SAAA;;AAIEA,0BAAAA,MAAA,YAAA,EAAA,OAAA,SAAA,CAAA;AAGA,uBAAA,MAAA;AACEA,4BAAA,MAAA,YAAA;AACAA,4BAAAA,MAAA,UAAA;AAAA;;cAGA,CAAA;AAAA,YACF,GAAA,GAAA;AAAA;;AAIAA,0BAAAA,MAAA,WAAA;AAAA,cACE,KAAA;AAAA,YACF,CAAA;AAAA,UACF;AAAA,QACF;AAAA,MACF,CAAA;AAAA;;IAIF,cAAA,MAAA;AACEA,oBAAAA,MAAA,WAAA;AAAA,QACE,KAAA,0BAAA,KAAA,EAAA;AAAA,QACA,MAAA,MAAA;AACEA,wBAAAA,MAAA,UAAA;AAAA;YAEE,SAAA;AAAA,MAAA,KAAA,KAAA;AAAA,MAAA,KAAA,IAAA;AAAA,MAAA,KAAA,IAAA;AAAA,YACA,aAAA;AAAA,YACA,SAAA,CAAA,QAAA;AACE,kBAAA,IAAA,SAAA;AACEA,8BAAAA,MAAA,gBAAA;AAAA,kBACE,UAAA,CAAA,QAAA,QAAA,QAAA,MAAA;AAAA,kBACA,SAAA,CAAA,aAAA;;;;;;;;;;;;AAYM,6BAAA,oBAAA,IAAA;;oBAEJ;AAAA,kBACF;AAAA,gBACF,CAAA;AAAA,cACF;AAAA,YACF;AAAA,UACF,CAAA;AAAA,QACF;AAAA,MACF,CAAA;AAAA;;;AAKAA,oBAAAA,MAAA,UAAA;AAAA;;;QAIE,SAAA,CAAA,QAAA;;AAEIA,0BAAAA,MAAA,YAAA,EAAA,OAAA,SAAA,CAAA;AAGA,uBAAA,MAAA;;AAIEA,4BAAA,MAAA,YAAA;AACAA,4BAAAA,MAAA,UAAA;AAAA;;cAGA,CAAA;AAAA,YACF,GAAA,GAAA;AAAA,UACF;AAAA,QACF;AAAA,MACF,CAAA;AAAA;;;AAKAA,oBAAAA,MAAA,gBAAA;AAAA,QACE,UAAA,CAAA,QAAA,QAAA,QAAA,QAAA,MAAA;AAAA,QACA,SAAA,CAAA,QAAA;;AAGEA,wBAAAA,MAAA,YAAA,EAAA,OAAA,SAAA,CAAA;AAGA,qBAAA,MAAA;;AAIEA,0BAAA,MAAA,YAAA;AACAA,0BAAAA,MAAA,UAAA;AAAA;;YAGA,CAAA;AAAA,UACF,GAAA,GAAA;AAAA,QACF;AAAA,MACF,CAAA;AAAA;;;AAKAA,oBAAAA,MAAA,UAAA;AAAA;;;QAIE,SAAA,CAAA,QAAA;;AAEIA,0BAAAA,MAAA,YAAA,EAAA,OAAA,SAAA,CAAA;AAGA,uBAAA,MAAA;AAEE,mBAAA,OAAA,IAAA;AAEAA,4BAAA,MAAA,YAAA;AACAA,4BAAAA,MAAA,UAAA;AAAA;;cAGA,CAAA;AAAA,YACF,GAAA,GAAA;AAAA,UACF;AAAA,QACF;AAAA,MACF,CAAA;AAAA;;;AAKAA,oBAAAA,MAAA,gBAAA;AAAA,QACE,UAAA,CAAA,SAAA,MAAA,QAAA;AAAA,QACA,SAAA,CAAA,QAAA;AACE,kBAAA,IAAA,UAAA;AAAA;YAEE,KAAA;AACEA,4BAAAA,MAAA,YAAA,EAAA,OAAA,SAAA,CAAA;AAGA,yBAAA,MAAA;AACEA,8BAAA,MAAA,YAAA;AAGAA,8BAAAA,MAAA,YAAA,EAAA,OAAA,SAAA,CAAA;AAEA,2BAAA,MAAA;AACEA,gCAAA,MAAA,YAAA;AACAA,gCAAAA,MAAA,UAAA;AAAA;;kBAGA,CAAA;AAAA,gBAGF,GAAA,IAAA;AAAA,cACF,GAAA,GAAA;;;AAGA,kBAAA,KAAA,UAAA,KAAA,OAAA,SAAA,GAAA;AACEA,8BAAAA,MAAA,UAAA;AAAA;kBAEE,SAAA;AAAA,kBACA,SAAA,CAAAE,SAAA;AACE,wBAAAA,KAAA,SAAA;AACEF,oCAAAA,MAAA,YAAA,EAAA,OAAA,SAAA,CAAA;AAGA,iCAAA,MAAA;AAEE,6BAAA,SAAA;AAEAA,sCAAA,MAAA,YAAA;AACAA,sCAAAA,MAAA,UAAA;AAAA;;wBAGA,CAAA;AAAA,sBACF,GAAA,GAAA;AAAA,oBACF;AAAA,kBACF;AAAA,gBACF,CAAA;AAAA;AAEAA,8BAAAA,MAAA,UAAA;AAAA,kBACE,OAAA;AAAA;gBAEF,CAAA;AAAA,cACF;;UAEJ;AAAA,QACF;AAAA,MACF,CAAA;AAAA;;IAIF,cAAA,MAAA;AACEA,oBAAAA,MAAA,UAAA;AAAA;;QAGE,aAAA;AAAA;QAEA,SAAA,CAAA,QAAA;AACE,cAAA,IAAA,SAAA;AACEA,0BAAAA,MAAA,YAAA,EAAA,OAAA,SAAA,CAAA;AAGA,uBAAA,MAAA;;;AAII,qBAAA,eAAA,OAAA,OAAA,CAAA;AAAA,cACF;AAEAA,4BAAA,MAAA,YAAA;AACAA,4BAAAA,MAAA,UAAA;AAAA;;cAGA,CAAA;AAAA,YACF,GAAA,GAAA;AAAA,UACF;AAAA,QACF;AAAA,MACF,CAAA;AAAA;;;AAKA,YAAA,WAAA,KAAA,WAAA;AACA,YAAA,aAAA,WAAA,OAAA;AAEAA,oBAAAA,MAAA,UAAA;AAAA;QAEE,SAAA,MAAA,UAAA;AAAA,QACA,SAAA,CAAA,QAAA;AACE,cAAA,IAAA,SAAA;AACEA,0BAAAA,MAAA,YAAA,EAAA,OAAA,SAAA,CAAA;AAGA,uBAAA,MAAA;AAEE,mBAAA,SAAA,WAAA,YAAA;AAEAA,4BAAA,MAAA,YAAA;AACAA,4BAAAA,MAAA,UAAA;AAAA;;cAGA,CAAA;AAAA,YACF,GAAA,GAAA;AAAA,UACF;AAAA,QACF;AAAA,MACF,CAAA;AAAA;;;;;;;AAUAA,oBAAAA,MAAA,gBAAA;AAAA;QAEE,SAAA,CAAA,QAAA;AACE,kBAAA,IAAA,UAAA;AAAA;AAEIA,4BAAAA,MAAA,cAAA;AAAA;gBAEE,MAAA,MAAA;AACEA,gCAAAA,MAAA,UAAA;AAAA,oBACE,OAAA;AAAA;kBAEF,CAAA;AAAA,gBACF;AAAA,cACF,CAAA;;;AAGAA,kCAAA,WAAA,EAAA,KAAA,4BAAA,KAAA,SAAA,EAAA,GAAA,CAAA;;UAEJ;AAAA,QACF;AAAA,MACF,CAAA;AAAA;;IAIF,aAAA,MAAA;AAEEA,oBAAAA,MAAA,cAAA;AAAA,QACE,iBAAA;AAAA,QACA,OAAA,CAAA,mBAAA,eAAA;AAAA,MACF,CAAA;AAEAA,oBAAAA,MAAA,UAAA;AAAA,QACE,OAAA;AAAA;;MAGF,CAAA;AAAA;;IAIF,mBAAA;AACEA,oBAAAA,MAAA,WAAA,EAAA,KAAA,wBAAA,CAAA;AAAA;;IAIF,mBAAA;AACE,WAAA,cAAA;AAAA;;;;AAMAA,oBAAAA,MAAA,WAAA;AAAA,QACE,KAAA;AAAA,MACF,CAAA;AAAA;;;AAKA,YAAA,OAAA,KAAA,YAAA;;AAIAA,oBAAAA,MAAA,YAAA,EAAA,OAAA,SAAA,CAAA;AAGA,iBAAA,MAAA;AACEA,sBAAA,MAAA,YAAA;AACAA,sBAAAA,MAAA,UAAA;AAAA,UACE,OAAA,GAAA,IAAA;AAAA;QAEF,CAAA;AAAA,MACF,GAAA,IAAA;AAAA;;;AAMAA,oBAAAA,MAAA,cAAA;AAAA,QACE,iBAAA;AAAA,QACA,OAAA,CAAA,mBAAA,eAAA;AAAA,MACF,CAAA;AAEAA,oBAAAA,MAAA,UAAA;AAAA,QACE,OAAA;AAAA;;MAGF,CAAA;AAAA;;;AAKAA,oBAAAA,MAAA,WAAA;AAAA;MAEA,CAAA;AAAA;;EAGJ,SAAA;;MAGI,YAAA;AAAA;;IAEF,CAAA;AAAA;;;;AAME,YAAA,UAAA,IAAA,UAAA,IAAA,OAAA,WAAA,IAAA,OAAA,QAAA;AACA,UAAA,YAAA,SAAA;AAEE,cAAA,WAAA,IAAA,OAAA,QAAA;;AAEE,iBAAA;AAAA,YACE,OAAA,SAAA,SAAA;AAAA;;;QAIJ;AAGA,cAAA,OAAA,IAAA,OAAA,QAAA;AACA,YAAA,MAAA;AACE,iBAAA;AAAA;YAEE,MAAA,4BAAA,KAAA,EAAA;AAAA;;QAGJ;AAAA,MACF;AAAA,IACF;AAGA,WAAA;AAAA,MACE,OAAA,KAAA,SAAA,WAAA,GAAA,KAAA,SAAA,QAAA,UAAA;AAAA;MAEA,UAAA,KAAA,SAAA,UAAA;AAAA;;;EAIJ,kBAAA;AACE,WAAA;AAAA,MACE,OAAA,KAAA,SAAA,WAAA,GAAA,KAAA,SAAA,QAAA,UAAA;AAAA,MACA,OAAA,UAAA,KAAA,UAAA,EAAA;AAAA,MACA,UAAA,KAAA,SAAA,UAAA;AAAA;EAEJ;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACnrFA,GAAG,WAAW,eAAe;"}