"use strict";
const common_vendor = require("../../../../../common/vendor.js");
const common_assets = require("../../../../../common/assets.js");
if (!Array) {
  const _component_path = common_vendor.resolveComponent("path");
  const _component_svg = common_vendor.resolveComponent("svg");
  (_component_path + _component_svg)();
}
const _sfc_main = {
  __name: "index",
  setup(__props) {
    const address = common_vendor.ref({
      id: "1",
      name: "张三",
      phone: "138****1234",
      province: "北京市",
      city: "北京市",
      district: "朝阳区",
      detail: "三里屯SOHO 5号楼2单元801"
    });
    const orderShops = common_vendor.ref([]);
    const paymentType = common_vendor.ref("wechat");
    const paymentOptions = common_vendor.ref([
      {
        type: "wechat",
        name: "微信支付",
        icon: "/static/images/payment/wechat.png"
      },
      {
        type: "alipay",
        name: "支付宝",
        icon: "/static/images/payment/alipay.png"
      },
      {
        type: "balance",
        name: "余额支付",
        icon: "/static/images/payment/balance.png"
      }
    ]);
    const deliveryOptions = common_vendor.ref([
      {
        type: "express",
        name: "快递配送"
      },
      {
        type: "self",
        name: "到店自提"
      }
    ]);
    const goBack = () => {
      common_vendor.index.navigateBack();
    };
    const selectAddress = () => {
      common_vendor.index.navigateTo({
        url: "/subPackages/activity-showcase/pages/my/address"
      });
    };
    const selectDelivery = (shop, type) => {
      shop.deliveryType = type;
    };
    const selectCoupon = (shop) => {
      common_vendor.index.showToast({
        title: "优惠券功能开发中",
        icon: "none"
      });
    };
    const selectPayment = (type) => {
      paymentType.value = type;
    };
    const getTotalQuantity = (shop) => {
      return shop.items.reduce((total, item) => total + item.quantity, 0);
    };
    const getShopTotal = (shop) => {
      return shop.items.reduce((total, item) => total + item.price * item.quantity, 0);
    };
    const getProductTotal = () => {
      return orderShops.value.reduce((total, shop) => {
        return total + getShopTotal(shop);
      }, 0);
    };
    const getDeliveryFee = () => {
      return orderShops.value.reduce((total, shop) => {
        return total + (shop.deliveryFee || 0);
      }, 0);
    };
    const getDiscountAmount = () => {
      return orderShops.value.reduce((total, shop) => {
        return total + (shop.coupon ? shop.coupon.amount : 0);
      }, 0);
    };
    const getTotalAmount = () => {
      return getProductTotal() + getDeliveryFee() - getDiscountAmount();
    };
    const submitOrder = () => {
      if (!address.value) {
        common_vendor.index.showToast({
          title: "请选择收货地址",
          icon: "none"
        });
        return;
      }
      common_vendor.index.showLoading({
        title: "提交订单中..."
      });
      setTimeout(() => {
        common_vendor.index.hideLoading();
        const orderId = "ORDER_" + Date.now();
        common_vendor.index.navigateTo({
          url: `/subPackages/activity-showcase/pages/payment/index?orderId=${orderId}&amount=${getTotalAmount()}`
        });
      }, 1500);
    };
    const initOrderData = (fromCart) => {
      orderShops.value = [
        {
          id: "shop1",
          name: "Apple授权专卖店",
          logo: "https://via.placeholder.com/100",
          deliveryType: "express",
          deliveryFee: 10,
          remark: "",
          coupon: {
            id: "coupon1",
            name: "满5000减300",
            amount: 300
          },
          items: [
            {
              id: "1",
              name: "iPhone 14 Pro 深空黑 256G",
              specs: "颜色：深空黑；内存：256G",
              price: 7999,
              quantity: 1,
              image: "https://via.placeholder.com/300x300"
            }
          ]
        },
        {
          id: "shop2",
          name: "华为官方旗舰店",
          logo: "https://via.placeholder.com/100",
          deliveryType: "express",
          deliveryFee: 0,
          remark: "",
          coupon: null,
          items: [
            {
              id: "3",
              name: "华为Mate 50 Pro 曜金黑 512G",
              specs: "颜色：曜金黑；内存：512G",
              price: 6999,
              quantity: 1,
              image: "https://via.placeholder.com/300x300"
            }
          ]
        }
      ];
    };
    common_vendor.onMounted(() => {
      const query = common_vendor.index.getSystemInfoSync().platform === "devtools" ? { from: "cart" } : common_vendor.index.$u.route.query;
      query.from === "cart";
      initOrderData();
    });
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_vendor.p({
          d: "M19 12H5M12 19l-7-7 7-7",
          stroke: "#FFFFFF",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        b: common_vendor.p({
          viewBox: "0 0 24 24",
          width: "24",
          height: "24"
        }),
        c: common_vendor.o(goBack),
        d: address.value
      }, address.value ? {
        e: common_vendor.t(address.value.name),
        f: common_vendor.t(address.value.phone),
        g: common_vendor.t(address.value.province),
        h: common_vendor.t(address.value.city),
        i: common_vendor.t(address.value.district),
        j: common_vendor.t(address.value.detail),
        k: common_vendor.p({
          d: "M9 18l6-6-6-6",
          stroke: "#999999",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        l: common_vendor.p({
          viewBox: "0 0 24 24",
          width: "24",
          height: "24"
        })
      } : {
        m: common_vendor.p({
          d: "M9 18l6-6-6-6",
          stroke: "#999999",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        n: common_vendor.p({
          viewBox: "0 0 24 24",
          width: "24",
          height: "24"
        })
      }, {
        o: common_assets._imports_0$65,
        p: common_vendor.o(selectAddress),
        q: common_vendor.f(orderShops.value, (shop, shopIndex, i0) => {
          return {
            a: shop.logo,
            b: common_vendor.t(shop.name),
            c: common_vendor.f(shop.items, (item, itemIndex, i1) => {
              return {
                a: item.image,
                b: common_vendor.t(item.name),
                c: common_vendor.t(item.specs),
                d: common_vendor.t(item.price.toFixed(2)),
                e: common_vendor.t(item.quantity),
                f: itemIndex
              };
            }),
            d: common_vendor.f(deliveryOptions.value, (option, index, i1) => {
              return {
                a: common_vendor.t(option.name),
                b: index,
                c: shop.deliveryType === option.type ? 1 : "",
                d: common_vendor.o(($event) => selectDelivery(shop, option.type), index)
              };
            }),
            e: shop.remark,
            f: common_vendor.o(($event) => shop.remark = $event.detail.value, shopIndex),
            g: common_vendor.t(shop.coupon ? `${shop.coupon.name}` : "暂无可用优惠券"),
            h: "c2e1be1d-7-" + i0 + "," + ("c2e1be1d-6-" + i0),
            i: "c2e1be1d-6-" + i0,
            j: common_vendor.o(($event) => selectCoupon(), shopIndex),
            k: common_vendor.t(getTotalQuantity(shop)),
            l: common_vendor.t(getShopTotal(shop).toFixed(2)),
            m: shopIndex
          };
        }),
        r: common_vendor.p({
          d: "M9 18l6-6-6-6",
          stroke: "#999999",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        s: common_vendor.p({
          viewBox: "0 0 24 24",
          width: "16",
          height: "16"
        }),
        t: common_vendor.f(paymentOptions.value, (option, index, i0) => {
          return common_vendor.e({
            a: option.icon,
            b: common_vendor.t(option.name),
            c: paymentType.value === option.type
          }, paymentType.value === option.type ? {
            d: "c2e1be1d-9-" + i0 + "," + ("c2e1be1d-8-" + i0),
            e: common_vendor.p({
              d: "M20 6L9 17l-5-5",
              stroke: "#FFFFFF",
              ["stroke-width"]: "2",
              ["stroke-linecap"]: "round",
              ["stroke-linejoin"]: "round"
            }),
            f: "c2e1be1d-8-" + i0,
            g: common_vendor.p({
              viewBox: "0 0 24 24",
              width: "16",
              height: "16"
            })
          } : {}, {
            h: index,
            i: paymentType.value === option.type ? 1 : "",
            j: common_vendor.o(($event) => selectPayment(option.type), index)
          });
        }),
        v: common_vendor.t(getProductTotal().toFixed(2)),
        w: common_vendor.t(getDeliveryFee().toFixed(2)),
        x: common_vendor.t(getDiscountAmount().toFixed(2)),
        y: common_vendor.t(getTotalAmount().toFixed(2)),
        z: common_vendor.o(submitOrder)
      });
    };
  }
};
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-c2e1be1d"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../../../.sourcemap/mp-weixin/subPackages/activity-showcase/pages/orders/confirm/index.js.map
