{"version": 3, "file": "message-center.js", "sources": ["carpool-package/pages/carpool/my/message-center.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/Y2FycG9vbC1wYWNrYWdlXHBhZ2VzXGNhcnBvb2xcbXlcbWVzc2FnZS1jZW50ZXIudnVl"], "sourcesContent": ["<template>\n  <view class=\"message-center-container\">\n    <!-- 自定义导航栏 -->\n    <view class=\"custom-header\" :style=\"{ paddingTop: statusBarHeight + 'px' }\">\n      <view class=\"header-content\">\n        <view class=\"left-action\" @click=\"goBack\">\n          <image src=\"/static/images/tabbar/最新返回键.png\" class=\"action-icon back-icon\"></image>\n        </view>\n        <view class=\"title-area\">\n          <text class=\"page-title\">消息中心</text>\n        </view>\n        <view class=\"right-action\">\n          <!-- 预留空间 -->\n        </view>\n      </view>\n    </view>\n    \n    <!-- 消息类型 Tab -->\n    <view class=\"message-tabs\">\n      <view \n        class=\"tab-item\" \n        v-for=\"(tab, index) in tabs\" \n        :key=\"index\"\n        :class=\"{ active: currentTab === index }\"\n        @click=\"switchTab(index)\"\n      >\n        <text class=\"tab-text\">{{tab}}</text>\n        <view class=\"active-line\" v-if=\"currentTab === index\"></view>\n      </view>\n    </view>\n    \n    <!-- 消息列表 -->\n    <scroll-view class=\"scrollable-content\" scroll-y @scrolltolower=\"loadMore\" refresher-enabled @refresherrefresh=\"onRefresh\" :refresher-triggered=\"isRefreshing\">\n      <!-- 系统消息列表 -->\n      <view class=\"message-list\" v-if=\"currentTab === 0\">\n        <view class=\"message-item system\" v-for=\"(item, index) in systemMessages\" :key=\"item.id\" @click=\"readMessage(item)\">\n          <view class=\"unread-indicator\" v-if=\"!item.isRead\"></view>\n          <view class=\"message-icon-wrapper\">\n            <image src=\"/static/images/icons/system-message.png\" mode=\"aspectFit\" class=\"message-icon\"></image>\n          </view>\n          <view class=\"message-content\">\n            <view class=\"message-header\">\n              <text class=\"message-title\">{{item.title}}</text>\n              <text class=\"message-time\">{{item.time}}</text>\n            </view>\n            <text class=\"message-brief\">{{item.content}}</text>\n          </view>\n        </view>\n        \n        <!-- 空状态 -->\n        <view class=\"empty-state\" v-if=\"systemMessages.length === 0 && !isLoading\">\n          <image src=\"/static/images/empty/no-message.png\" mode=\"aspectFit\" class=\"empty-image\"></image>\n          <text class=\"empty-text\">暂无系统消息</text>\n        </view>\n      </view>\n      \n      <!-- 拼车消息列表 -->\n      <view class=\"message-list\" v-if=\"currentTab === 1\">\n        <view class=\"message-item carpool\" v-for=\"(item, index) in carpoolMessages\" :key=\"item.id\" @click=\"readMessage(item)\">\n          <view class=\"unread-indicator\" v-if=\"!item.isRead\"></view>\n          <view class=\"message-icon-wrapper\">\n            <image src=\"/static/images/icons/carpool-message.png\" mode=\"aspectFit\" class=\"message-icon\"></image>\n          </view>\n          <view class=\"message-content\">\n            <view class=\"message-header\">\n              <text class=\"message-title\">{{item.title}}</text>\n              <text class=\"message-time\">{{item.time}}</text>\n            </view>\n            <text class=\"message-brief\">{{item.content}}</text>\n            \n            <!-- 拼车相关信息 -->\n            <view class=\"carpool-info\" v-if=\"item.carpoolInfo\">\n              <view class=\"route-brief\">\n                <text class=\"route-text\">{{item.carpoolInfo.startPoint}} → {{item.carpoolInfo.endPoint}}</text>\n                <text class=\"departure-time\">{{item.carpoolInfo.departureTime}}</text>\n              </view>\n            </view>\n          </view>\n        </view>\n        \n        <!-- 空状态 -->\n        <view class=\"empty-state\" v-if=\"carpoolMessages.length === 0 && !isLoading\">\n          <image src=\"/static/images/empty/no-message.png\" mode=\"aspectFit\" class=\"empty-image\"></image>\n          <text class=\"empty-text\">暂无拼车消息</text>\n        </view>\n      </view>\n      \n      <!-- 加载状态 -->\n      <view class=\"loading-state\" v-if=\"isLoading && !isRefreshing\">\n        <text class=\"loading-text\">加载中...</text>\n      </view>\n      \n      <!-- 到底提示 -->\n      <view class=\"list-bottom\" v-if=\"messageList.length > 0 && !hasMore\">\n        <text class=\"bottom-text\">— 已经到底啦 —</text>\n      </view>\n    </scroll-view>\n    \n    <!-- 底部悬浮清空按钮 -->\n    <view class=\"float-clear-btn\" @click=\"showClearConfirm\" v-if=\"messageList.length > 0\">\n      <text class=\"float-clear-text\">清空消息</text>\n    </view>\n\n    <!-- 消息详情弹窗 -->\n    <view class=\"popup-mask\" v-if=\"showMessageDetail\" @click=\"closeMessageDetail\"></view>\n    <view class=\"popup-container\" v-if=\"showMessageDetail\">\n      <view class=\"popup-header\">\n        <text class=\"popup-title\">{{currentMessage.title}}</text>\n        <view class=\"popup-close\" @click=\"closeMessageDetail\">×</view>\n      </view>\n      <scroll-view class=\"popup-content\" scroll-y>\n        <view class=\"message-time-display\">{{currentMessage.time}}</view>\n        <view class=\"message-full-content\">\n          <text class=\"content-text\">{{currentMessage.fullContent || currentMessage.content}}</text>\n        </view>\n        \n        <!-- 拼车详情信息 -->\n        <view class=\"carpool-detail\" v-if=\"currentMessage.carpoolInfo\">\n          <view class=\"detail-title\">相关拼车信息</view>\n          <view class=\"route-info\">\n            <view class=\"route-points\">\n              <view class=\"start-point\">\n                <view class=\"point-marker start\"></view>\n                <text class=\"point-text\">{{currentMessage.carpoolInfo.startPoint}}</text>\n              </view>\n              <view class=\"route-line\"></view>\n              <view class=\"end-point\">\n                <view class=\"point-marker end\"></view>\n                <text class=\"point-text\">{{currentMessage.carpoolInfo.endPoint}}</text>\n              </view>\n            </view>\n            <view class=\"trip-info\">\n              <view class=\"info-item\">\n                <image src=\"/static/images/icons/calendar.png\" mode=\"aspectFit\" class=\"info-icon\"></image>\n                <text class=\"info-text\">{{currentMessage.carpoolInfo.departureTime}}</text>\n              </view>\n              <view class=\"info-item\">\n                <image src=\"/static/images/icons/people.png\" mode=\"aspectFit\" class=\"info-icon\"></image>\n                <text class=\"info-text\">{{currentMessage.carpoolInfo.seatCount}}个座位</text>\n              </view>\n              <view class=\"info-item\" v-if=\"currentMessage.carpoolInfo.price\">\n                <image src=\"/static/images/icons/price.png\" mode=\"aspectFit\" class=\"info-icon\"></image>\n                <text class=\"info-text price\">¥{{currentMessage.carpoolInfo.price}}/人</text>\n              </view>\n            </view>\n          </view>\n        </view>\n      </scroll-view>\n      \n      <view class=\"popup-footer\">\n        <button class=\"popup-button delete\" @click=\"deleteCurrentMessage\">删除消息</button>\n        <button class=\"popup-button confirm\" @click=\"viewCarpoolDetail\" v-if=\"currentMessage.carpoolInfo\">查看详情</button>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script setup>\nimport { ref, computed, onMounted } from 'vue'\n\n// 状态栏高度\nconst statusBarHeight = ref(20);\n\n// 数据定义\nconst tabs = ref(['系统消息', '拼车消息'])\nconst currentTab = ref(0)\nconst messageList = ref([])\nconst page = ref(1)\nconst pageSize = ref(10)\nconst hasMore = ref(true)\nconst isLoading = ref(false)\nconst isRefreshing = ref(false)\nconst showMessageDetail = ref(false)\nconst currentMessage = ref({})\n\n// 计算属性\nconst systemMessages = computed(() => {\n  return messageList.value.filter(msg => msg.type === 'system')\n})\n\nconst carpoolMessages = computed(() => {\n  return messageList.value.filter(msg => msg.type === 'carpool')\n})\n\n// 加载数据\nconst loadData = () => {\n  if (isLoading.value) return\n  isLoading.value = true\n  \n  // 模拟数据加载\n  setTimeout(() => {\n    // 模拟数据\n    const mockData = [\n      {\n        id: '5001',\n        type: 'system',\n        title: '系统通知',\n        content: '您的账号已完成实名认证，现在可以发布拼车信息了。',\n        fullContent: '尊敬的用户：\\n\\n恭喜您！您的账号已通过实名认证审核，现在您可以使用平台的全部功能，包括发布拼车信息、查看联系方式等。\\n\\n为了保障平台用户的安全，我们对所有用户进行严格的身份审核。感谢您的配合与支持！\\n\\n如有任何疑问，请联系客服。',\n        time: '2023-10-15 16:30',\n        isRead: true\n      },\n      {\n        id: '5002',\n        type: 'system',\n        title: '活动通知',\n        content: '新用户专享优惠：首次发布拼车信息可获得置顶券一张。',\n        time: '2023-10-14 10:15',\n        isRead: false\n      },\n      {\n        id: '5003',\n        type: 'carpool',\n        title: '拼车申请',\n        content: '用户\"王先生\"申请加入您发布的拼车行程。',\n        time: '2023-10-13 14:20',\n        isRead: false,\n        carpoolInfo: {\n          id: '1001',\n          startPoint: '磁县公交站',\n          endPoint: '邯郸东站',\n          departureTime: '2023-10-16 08:30',\n          seatCount: 3,\n          price: 15\n        }\n      },\n      {\n        id: '5004',\n        type: 'carpool',\n        title: '行程提醒',\n        content: '您有一个拼车行程即将开始，请提前做好准备。',\n        time: '2023-10-12 18:30',\n        isRead: true,\n        carpoolInfo: {\n          id: '1002',\n          startPoint: '磁县老城区',\n          endPoint: '邯郸高新区',\n          departureTime: '2023-10-13 07:30',\n          seatCount: 4,\n          price: 10\n        }\n      }\n    ]\n    \n    if (page.value === 1) {\n      messageList.value = mockData\n    } else {\n      messageList.value = [...messageList.value, ...mockData]\n    }\n    \n    // 模拟没有更多数据\n    if (page.value >= 2) {\n      hasMore.value = false\n    }\n    \n    isLoading.value = false\n    isRefreshing.value = false\n  }, 1000)\n}\n\n// 加载更多\nconst loadMore = () => {\n  if (!hasMore.value || isLoading.value) return\n  page.value++\n  loadData()\n}\n\n// 下拉刷新\nconst onRefresh = () => {\n  isRefreshing.value = true\n  page.value = 1\n  hasMore.value = true\n  loadData()\n}\n\n// 返回上一页\nconst goBack = () => {\n  uni.navigateBack()\n}\n\n// 切换标签页\nconst switchTab = (index) => {\n  currentTab.value = index\n}\n\n// 阅读消息\nconst readMessage = (message) => {\n  // 标记为已读\n  if (!message.isRead) {\n    messageList.value = messageList.value.map(item => {\n      if (item.id === message.id) {\n        return { ...item, isRead: true }\n      }\n      return item\n    })\n  }\n  \n  // 显示详情\n  currentMessage.value = message\n  showMessageDetail.value = true\n}\n\n// 关闭消息详情\nconst closeMessageDetail = () => {\n  showMessageDetail.value = false\n}\n\n// 删除当前消息\nconst deleteCurrentMessage = () => {\n  uni.showModal({\n    title: '提示',\n    content: '确定要删除此条消息吗？',\n    success: (res) => {\n      if (res.confirm) {\n        // 删除消息\n        messageList.value = messageList.value.filter(item => item.id !== currentMessage.value.id)\n        closeMessageDetail()\n        \n        uni.showToast({\n          title: '删除成功',\n          icon: 'success'\n        })\n      }\n    }\n  })\n}\n\n// 查看拼车详情\nconst viewCarpoolDetail = () => {\n  if (!currentMessage.value.carpoolInfo) return\n  \n  uni.navigateTo({\n    url: `/carpool-package/pages/carpool/detail/index?id=${currentMessage.value.carpoolInfo.id}`\n  })\n  \n  closeMessageDetail()\n}\n\n// 显示清空确认\nconst showClearConfirm = () => {\n  uni.showModal({\n    title: '提示',\n    content: `确定要清空${currentTab.value === 0 ? '系统' : '拼车'}消息吗？此操作不可恢复。`,\n    success: (res) => {\n      if (res.confirm) {\n        clearMessages()\n      }\n    }\n  })\n}\n\n// 清空消息\nconst clearMessages = () => {\n  const messageType = currentTab.value === 0 ? 'system' : 'carpool'\n  \n  // 清空当前类型的消息\n  messageList.value = messageList.value.filter(item => item.type !== messageType)\n  \n  uni.showToast({\n    title: '已清空',\n    icon: 'success'\n  })\n}\n\n// 生命周期钩子\nonMounted(() => {\n  // 获取状态栏高度\n  const sysInfo = uni.getSystemInfoSync();\n  statusBarHeight.value = sysInfo.statusBarHeight || 20;\n  \n  // 获取页面参数\n  const pages = getCurrentPages()\n  const currentPage = pages[pages.length - 1]\n  const options = currentPage.$page?.options || {}\n  \n  // 如果有指定tab参数，则切换到对应tab\n  if (options.tab) {\n    currentTab.value = parseInt(options.tab) || 0\n  }\n  \n  loadData()\n})\n\n// 暴露方法给外部访问\ndefineExpose({\n  loadData,\n  switchTab\n})\n</script>\n\n<style lang=\"scss\">\n.message-center-container {\n  display: flex;\n  flex-direction: column;\n  min-height: 100vh;\n  background-color: #F5F7FA;\n}\n\n/* 导航栏样式 */\n.custom-header {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  z-index: 100;\n  background-color: #1677FF;\n}\n\n.header-content {\n  height: 44px;\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  padding: 0 12px;\n}\n\n.left-action, .right-action {\n  width: 44px;\n  height: 44px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.action-icon {\n  width: 24px;\n  height: 24px;\n}\n\n.back-icon {\n  width: 24px;\n  height: 24px;\n  /* 图标是黑色的，需要转为白色 */\n  filter: brightness(0) invert(1);\n}\n\n.title-area {\n  flex: 1;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.page-title {\n  font-size: 18px;\n  font-weight: 600;\n  color: #FFFFFF;\n  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);\n}\n\n.clear-text {\n  color: #FFFFFF;\n  font-size: 14px;\n}\n\n/* 消息类型Tab */\n.message-tabs {\n  position: fixed;\n  top: calc(44px + var(--status-bar-height));\n  left: 0;\n  right: 0;\n  height: 44px;\n  background-color: #FFFFFF;\n  display: flex;\n  z-index: 99;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);\n}\n\n.tab-item {\n  flex: 1;\n  height: 44px;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  position: relative;\n}\n\n.tab-text {\n  font-size: 15px;\n  color: #666666;\n}\n\n.tab-item.active .tab-text {\n  color: #1677FF;\n  font-weight: 500;\n}\n\n.active-line {\n  position: absolute;\n  bottom: 0;\n  width: 20px;\n  height: 3px;\n  background-color: #1677FF;\n  border-radius: 1.5px;\n}\n\n/* 内容区域 */\n.scrollable-content {\n  flex: 1;\n  margin-top: calc(88px + var(--status-bar-height));\n  padding: 8px 0; /* 减少左右内边距，确保卡片圆角完全显示 */\n}\n\n/* 消息列表 */\n.message-list {\n  display: flex;\n  flex-direction: column;\n  gap: 14px; /* 增加卡片之间的间距 */\n  padding: 6px 0; /* 添加上下内边距 */\n}\n\n.message-item {\n  position: relative;\n  background-color: #FFFFFF;\n  border-radius: 16px; /* 增加圆角 */\n  overflow: hidden;\n  padding: 18px 20px; /* 调整内边距 */\n  box-shadow: 0 3px 12px rgba(0, 0, 0, 0.06); /* 增强阴影效果 */\n  display: flex;\n  align-items: flex-start;\n  gap: 12px;\n  width: 80%; /* 再次缩短卡片宽度 */\n  margin: 0 auto; /* 居中显示 */\n}\n\n.unread-indicator {\n  position: absolute;\n  top: 12px;\n  right: 12px;\n  width: 8px;\n  height: 8px;\n  border-radius: 4px;\n  background-color: #FF5722;\n}\n\n.message-icon-wrapper {\n  width: 40px;\n  height: 40px;\n  border-radius: 20px;\n  background-color: rgba(22, 119, 255, 0.1);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.message-item.system .message-icon-wrapper {\n  background-color: rgba(22, 119, 255, 0.1);\n}\n\n.message-item.carpool .message-icon-wrapper {\n  background-color: rgba(255, 87, 34, 0.1);\n}\n\n.message-icon {\n  width: 24px;\n  height: 24px;\n}\n\n.message-content {\n  flex: 1;\n}\n\n.message-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 8px;\n}\n\n.message-title {\n  font-size: 16px;\n  font-weight: 500;\n  color: #333333;\n}\n\n.message-time {\n  font-size: 12px;\n  color: #999999;\n}\n\n.message-brief {\n  font-size: 14px;\n  color: #666666;\n  line-height: 1.5;\n  display: -webkit-box;\n  -webkit-box-orient: vertical;\n  -webkit-line-clamp: 2;\n  overflow: hidden;\n}\n\n/* 拼车信息 */\n.carpool-info {\n  margin-top: 12px;\n  padding-top: 12px;\n  border-top: 1px dashed #EEEEEE;\n}\n\n.route-brief {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.route-text {\n  font-size: 14px;\n  color: #333333;\n  font-weight: 500;\n}\n\n.departure-time {\n  font-size: 12px;\n  color: #1677FF;\n}\n\n/* 空状态 */\n.empty-state {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: 40px 0;\n}\n\n.empty-image {\n  width: 120px;\n  height: 120px;\n  margin-bottom: 16px;\n}\n\n.empty-text {\n  font-size: 16px;\n  color: #999999;\n}\n\n/* 加载状态 */\n.loading-state {\n  padding: 16px 0;\n  text-align: center;\n}\n\n.loading-text {\n  font-size: 14px;\n  color: #999999;\n}\n\n/* 列表底部 */\n.list-bottom {\n  padding: 16px 0;\n  text-align: center;\n}\n\n.bottom-text {\n  font-size: 14px;\n  color: #999999;\n}\n\n/* 弹窗样式 */\n.popup-mask {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background-color: rgba(0, 0, 0, 0.5);\n  z-index: 1000;\n}\n\n.popup-container {\n  position: fixed;\n  left: 50%;\n  top: 50%;\n  transform: translate(-50%, -50%);\n  width: 90%;\n  max-height: 80vh;\n  display: flex;\n  flex-direction: column;\n  background-color: #FFFFFF;\n  border-radius: 12px;\n  overflow: hidden;\n  z-index: 1001;\n}\n\n.popup-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 16px;\n  border-bottom: 1px solid #EEEEEE;\n}\n\n.popup-title {\n  font-size: 18px;\n  font-weight: 500;\n  color: #333333;\n}\n\n.popup-close {\n  font-size: 24px;\n  color: #999999;\n  padding: 0 8px;\n}\n\n.popup-content {\n  flex: 1;\n  padding: 16px;\n  overflow-y: auto;\n}\n\n.message-time-display {\n  font-size: 12px;\n  color: #999999;\n  text-align: center;\n  margin-bottom: 16px;\n}\n\n.message-full-content {\n  padding-bottom: 16px;\n}\n\n.content-text {\n  font-size: 14px;\n  color: #333333;\n  line-height: 1.6;\n  white-space: pre-wrap;\n}\n\n/* 拼车详情 */\n.carpool-detail {\n  margin-top: 16px;\n  padding-top: 16px;\n  border-top: 1px solid #EEEEEE;\n}\n\n.detail-title {\n  font-size: 16px;\n  font-weight: 500;\n  color: #333333;\n  margin-bottom: 12px;\n}\n\n.route-info {\n  display: flex;\n  flex-direction: column;\n  gap: 16px;\n  background-color: #F8FAFB;\n  padding: 12px;\n  border-radius: 8px;\n}\n\n.route-points {\n  display: flex;\n  flex-direction: column;\n  gap: 6px;\n}\n\n.start-point, .end-point {\n  display: flex;\n  align-items: center;\n  gap: 10px;\n}\n\n.point-marker {\n  width: 12px;\n  height: 12px;\n  border-radius: 50%;\n}\n\n.start {\n  background-color: #1677FF;\n}\n\n.end {\n  background-color: #FF5722;\n}\n\n.route-line {\n  width: 2px;\n  height: 20px;\n  background-color: #DDDDDD;\n  margin-left: 5px;\n}\n\n.point-text {\n  font-size: 16px;\n  color: #333333;\n}\n\n.trip-info {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 16px;\n  margin-top: 10px;\n}\n\n.info-item {\n  display: flex;\n  align-items: center;\n  gap: 6px;\n}\n\n.info-icon {\n  width: 16px;\n  height: 16px;\n}\n\n.info-text {\n  font-size: 14px;\n  color: #666666;\n}\n\n.price {\n  color: #FF5722;\n  font-weight: 500;\n}\n\n.popup-footer {\n  display: flex;\n  border-top: 1px solid #EEEEEE;\n}\n\n.popup-button {\n  flex: 1;\n  height: 50px;\n  line-height: 50px;\n  text-align: center;\n  font-size: 16px;\n}\n\n.popup-button.delete {\n  color: #FF5722;\n  border-right: 1px solid #EEEEEE;\n}\n\n.popup-button.confirm {\n  color: #1677FF;\n  font-weight: 500;\n}\n\n/* 底部悬浮清空按钮 */\n.float-clear-btn {\n  position: fixed;\n  bottom: 30px;\n  right: 20px;\n  background: linear-gradient(135deg, #FF5722, #FF7043);\n  height: 44px;\n  padding: 0 20px;\n  border-radius: 22px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  box-shadow: 0 4px 12px rgba(255, 87, 34, 0.3);\n  z-index: 99;\n}\n\n.clear-icon {\n  width: 20px;\n  height: 20px;\n  margin-right: 6px;\n  filter: brightness(0) invert(1); /* 将图标变为白色 */\n}\n\n.float-clear-text {\n  color: #FFFFFF;\n  font-size: 14px;\n  font-weight: 500;\n}\n</style> ", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/carpool-package/pages/carpool/my/message-center.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "computed", "uni", "onMounted", "MiniProgramPage"], "mappings": ";;;;;;AAiKA,UAAM,kBAAkBA,cAAAA,IAAI,EAAE;AAG9B,UAAM,OAAOA,cAAG,IAAC,CAAC,QAAQ,MAAM,CAAC;AACjC,UAAM,aAAaA,cAAG,IAAC,CAAC;AACxB,UAAM,cAAcA,cAAG,IAAC,EAAE;AAC1B,UAAM,OAAOA,cAAG,IAAC,CAAC;AACDA,kBAAG,IAAC,EAAE;AACvB,UAAM,UAAUA,cAAG,IAAC,IAAI;AACxB,UAAM,YAAYA,cAAG,IAAC,KAAK;AAC3B,UAAM,eAAeA,cAAG,IAAC,KAAK;AAC9B,UAAM,oBAAoBA,cAAG,IAAC,KAAK;AACnC,UAAM,iBAAiBA,cAAG,IAAC,EAAE;AAG7B,UAAM,iBAAiBC,cAAQ,SAAC,MAAM;AACpC,aAAO,YAAY,MAAM,OAAO,SAAO,IAAI,SAAS,QAAQ;AAAA,IAC9D,CAAC;AAED,UAAM,kBAAkBA,cAAQ,SAAC,MAAM;AACrC,aAAO,YAAY,MAAM,OAAO,SAAO,IAAI,SAAS,SAAS;AAAA,IAC/D,CAAC;AAGD,UAAM,WAAW,MAAM;AACrB,UAAI,UAAU;AAAO;AACrB,gBAAU,QAAQ;AAGlB,iBAAW,MAAM;AAEf,cAAM,WAAW;AAAA,UACf;AAAA,YACE,IAAI;AAAA,YACJ,MAAM;AAAA,YACN,OAAO;AAAA,YACP,SAAS;AAAA,YACT,aAAa;AAAA,YACb,MAAM;AAAA,YACN,QAAQ;AAAA,UACT;AAAA,UACD;AAAA,YACE,IAAI;AAAA,YACJ,MAAM;AAAA,YACN,OAAO;AAAA,YACP,SAAS;AAAA,YACT,MAAM;AAAA,YACN,QAAQ;AAAA,UACT;AAAA,UACD;AAAA,YACE,IAAI;AAAA,YACJ,MAAM;AAAA,YACN,OAAO;AAAA,YACP,SAAS;AAAA,YACT,MAAM;AAAA,YACN,QAAQ;AAAA,YACR,aAAa;AAAA,cACX,IAAI;AAAA,cACJ,YAAY;AAAA,cACZ,UAAU;AAAA,cACV,eAAe;AAAA,cACf,WAAW;AAAA,cACX,OAAO;AAAA,YACR;AAAA,UACF;AAAA,UACD;AAAA,YACE,IAAI;AAAA,YACJ,MAAM;AAAA,YACN,OAAO;AAAA,YACP,SAAS;AAAA,YACT,MAAM;AAAA,YACN,QAAQ;AAAA,YACR,aAAa;AAAA,cACX,IAAI;AAAA,cACJ,YAAY;AAAA,cACZ,UAAU;AAAA,cACV,eAAe;AAAA,cACf,WAAW;AAAA,cACX,OAAO;AAAA,YACR;AAAA,UACF;AAAA,QACF;AAED,YAAI,KAAK,UAAU,GAAG;AACpB,sBAAY,QAAQ;AAAA,QAC1B,OAAW;AACL,sBAAY,QAAQ,CAAC,GAAG,YAAY,OAAO,GAAG,QAAQ;AAAA,QACvD;AAGD,YAAI,KAAK,SAAS,GAAG;AACnB,kBAAQ,QAAQ;AAAA,QACjB;AAED,kBAAU,QAAQ;AAClB,qBAAa,QAAQ;AAAA,MACtB,GAAE,GAAI;AAAA,IACT;AAGA,UAAM,WAAW,MAAM;AACrB,UAAI,CAAC,QAAQ,SAAS,UAAU;AAAO;AACvC,WAAK;AACL,eAAU;AAAA,IACZ;AAGA,UAAM,YAAY,MAAM;AACtB,mBAAa,QAAQ;AACrB,WAAK,QAAQ;AACb,cAAQ,QAAQ;AAChB,eAAU;AAAA,IACZ;AAGA,UAAM,SAAS,MAAM;AACnBC,oBAAAA,MAAI,aAAc;AAAA,IACpB;AAGA,UAAM,YAAY,CAAC,UAAU;AAC3B,iBAAW,QAAQ;AAAA,IACrB;AAGA,UAAM,cAAc,CAAC,YAAY;AAE/B,UAAI,CAAC,QAAQ,QAAQ;AACnB,oBAAY,QAAQ,YAAY,MAAM,IAAI,UAAQ;AAChD,cAAI,KAAK,OAAO,QAAQ,IAAI;AAC1B,mBAAO,EAAE,GAAG,MAAM,QAAQ,KAAM;AAAA,UACjC;AACD,iBAAO;AAAA,QACb,CAAK;AAAA,MACF;AAGD,qBAAe,QAAQ;AACvB,wBAAkB,QAAQ;AAAA,IAC5B;AAGA,UAAM,qBAAqB,MAAM;AAC/B,wBAAkB,QAAQ;AAAA,IAC5B;AAGA,UAAM,uBAAuB,MAAM;AACjCA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,SAAS;AAAA,QACT,SAAS,CAAC,QAAQ;AAChB,cAAI,IAAI,SAAS;AAEf,wBAAY,QAAQ,YAAY,MAAM,OAAO,UAAQ,KAAK,OAAO,eAAe,MAAM,EAAE;AACxF,+BAAoB;AAEpBA,0BAAAA,MAAI,UAAU;AAAA,cACZ,OAAO;AAAA,cACP,MAAM;AAAA,YAChB,CAAS;AAAA,UACF;AAAA,QACF;AAAA,MACL,CAAG;AAAA,IACH;AAGA,UAAM,oBAAoB,MAAM;AAC9B,UAAI,CAAC,eAAe,MAAM;AAAa;AAEvCA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,kDAAkD,eAAe,MAAM,YAAY,EAAE;AAAA,MAC9F,CAAG;AAED,yBAAoB;AAAA,IACtB;AAGA,UAAM,mBAAmB,MAAM;AAC7BA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,SAAS,QAAQ,WAAW,UAAU,IAAI,OAAO,IAAI;AAAA,QACrD,SAAS,CAAC,QAAQ;AAChB,cAAI,IAAI,SAAS;AACf,0BAAe;AAAA,UAChB;AAAA,QACF;AAAA,MACL,CAAG;AAAA,IACH;AAGA,UAAM,gBAAgB,MAAM;AAC1B,YAAM,cAAc,WAAW,UAAU,IAAI,WAAW;AAGxD,kBAAY,QAAQ,YAAY,MAAM,OAAO,UAAQ,KAAK,SAAS,WAAW;AAE9EA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,MACV,CAAG;AAAA,IACH;AAGAC,kBAAAA,UAAU,MAAM;;AAEd,YAAM,UAAUD,oBAAI;AACpB,sBAAgB,QAAQ,QAAQ,mBAAmB;AAGnD,YAAM,QAAQ,gBAAiB;AAC/B,YAAM,cAAc,MAAM,MAAM,SAAS,CAAC;AAC1C,YAAM,YAAU,iBAAY,UAAZ,mBAAmB,YAAW,CAAE;AAGhD,UAAI,QAAQ,KAAK;AACf,mBAAW,QAAQ,SAAS,QAAQ,GAAG,KAAK;AAAA,MAC7C;AAED,eAAU;AAAA,IACZ,CAAC;AAGD,aAAa;AAAA,MACX;AAAA,MACA;AAAA,IACF,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AClYD,GAAG,WAAWE,SAAe;"}