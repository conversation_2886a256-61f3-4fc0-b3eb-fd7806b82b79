{"version": 3, "file": "redpacket-marketing-overview.js", "sources": ["subPackages/merchant-admin-marketing/pages/marketing/redpacket/redpacket-marketing-overview.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/c3ViUGFja2FnZXNcbWVyY2hhbnQtYWRtaW4tbWFya2V0aW5nXHBhZ2VzXG1hcmtldGluZ1xyZWRwYWNrZXRccmVkcGFja2V0LW1hcmtldGluZy1vdmVydmlldy52dWU"], "sourcesContent": ["<template>\n  <view class=\"page-container\">\n    <!-- 自定义导航栏 -->\n    <view class=\"navbar\">\n      <view class=\"navbar-back\" @click=\"goBack\">\n        <view class=\"back-icon\"></view>\n      </view>\n      <text class=\"navbar-title\">红包营销概述实践</text>\n      <view class=\"navbar-right\">\n        <view class=\"share-icon\" @click=\"shareGuide\">\n          <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\">\n            <circle cx=\"18\" cy=\"5\" r=\"3\"></circle>\n            <circle cx=\"6\" cy=\"12\" r=\"3\"></circle>\n            <circle cx=\"18\" cy=\"19\" r=\"3\"></circle>\n            <line x1=\"8.59\" y1=\"13.51\" x2=\"15.42\" y2=\"17.49\"></line>\n            <line x1=\"15.41\" y1=\"6.51\" x2=\"8.59\" y2=\"10.49\"></line>\n          </svg>\n        </view>\n      </view>\n    </view>\n    \n    <!-- 页面内容 -->\n    <scroll-view scroll-y class=\"content-scroll\">\n      <!-- 页面头部 -->\n      <view class=\"page-header\">\n        <view class=\"header-bg\" style=\"background: linear-gradient(135deg, #6EE7B7, #3B82F6);\">\n          <text class=\"header-title\">了解如何有效使用红包</text>\n          <text class=\"header-subtitle\">提高转化率的营销秘籍</text>\n        </view>\n      </view>\n      \n      <!-- 内容部分 -->\n      <view class=\"content-section\">\n        <view class=\"section-title\">\n          <view class=\"title-icon\" style=\"background-color: rgba(110, 231, 183, 0.2);\">\n            <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"#6EE7B7\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\">\n              <path d=\"M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z\"></path>\n              <polyline points=\"3.27 6.96 12 12.01 20.73 6.96\"></polyline>\n              <line x1=\"12\" y1=\"22.08\" x2=\"12\" y2=\"12\"></line>\n            </svg>\n          </view>\n          <text class=\"title-text\">红包营销的基本原理</text>\n        </view>\n        \n        <view class=\"content-text\">\n          <text>红包营销是一种基于用户心理的营销手段，通过发放现金或优惠券形式的红包，激发用户参与热情，提高转化率。红包营销利用了人们对免费和意外惊喜的心理需求，能够有效提升品牌曝光和用户粘性。</text>\n        </view>\n        \n        <view class=\"content-image\">\n          <image src=\"/static/images/redpacket-principle.png\" mode=\"widthFix\"></image>\n        </view>\n        \n        <view class=\"section-title\">\n          <view class=\"title-icon\" style=\"background-color: rgba(110, 231, 183, 0.2);\">\n            <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"#6EE7B7\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\">\n              <polyline points=\"22 12 18 12 15 21 9 3 6 12 2 12\"></polyline>\n            </svg>\n          </view>\n          <text class=\"title-text\">红包营销的效果分析</text>\n        </view>\n        \n        <view class=\"data-cards\">\n          <view class=\"data-card\">\n            <text class=\"data-value\">68%</text>\n            <text class=\"data-label\">用户转化率提升</text>\n          </view>\n          <view class=\"data-card\">\n            <text class=\"data-value\">3.5倍</text>\n            <text class=\"data-label\">用户停留时间增长</text>\n          </view>\n          <view class=\"data-card\">\n            <text class=\"data-value\">42%</text>\n            <text class=\"data-label\">复购率提升</text>\n          </view>\n          <view class=\"data-card\">\n            <text class=\"data-value\">56%</text>\n            <text class=\"data-label\">分享率提升</text>\n          </view>\n        </view>\n        \n        <view class=\"section-title\">\n          <view class=\"title-icon\" style=\"background-color: rgba(110, 231, 183, 0.2);\">\n            <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"#6EE7B7\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\">\n              <path d=\"M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2\"></path>\n              <circle cx=\"9\" cy=\"7\" r=\"4\"></circle>\n              <path d=\"M23 21v-2a4 4 0 0 0-3-3.87\"></path>\n              <path d=\"M16 3.13a4 4 0 0 1 0 7.75\"></path>\n            </svg>\n          </view>\n          <text class=\"title-text\">红包营销的应用场景</text>\n        </view>\n        \n        <view class=\"scenario-list\">\n          <view class=\"scenario-item\">\n            <view class=\"scenario-icon\" style=\"background-color: rgba(110, 231, 183, 0.2);\">\n              <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"#6EE7B7\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\">\n                <path d=\"M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2\"></path>\n                <circle cx=\"12\" cy=\"7\" r=\"4\"></circle>\n              </svg>\n            </view>\n            <view class=\"scenario-content\">\n              <text class=\"scenario-title\">新客获取</text>\n              <text class=\"scenario-desc\">通过发放新人红包，吸引潜在用户注册，降低获客成本</text>\n            </view>\n          </view>\n          \n          <view class=\"scenario-item\">\n            <view class=\"scenario-icon\" style=\"background-color: rgba(110, 231, 183, 0.2);\">\n              <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"#6EE7B7\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\">\n                <rect x=\"3\" y=\"4\" width=\"18\" height=\"18\" rx=\"2\" ry=\"2\"></rect>\n                <line x1=\"16\" y1=\"2\" x2=\"16\" y2=\"6\"></line>\n                <line x1=\"8\" y1=\"2\" x2=\"8\" y2=\"6\"></line>\n                <line x1=\"3\" y1=\"10\" x2=\"21\" y2=\"10\"></line>\n              </svg>\n            </view>\n            <view class=\"scenario-content\">\n              <text class=\"scenario-title\">节日营销</text>\n              <text class=\"scenario-desc\">在重要节日发放红包，提升用户好感度和品牌认知</text>\n            </view>\n          </view>\n          \n          <view class=\"scenario-item\">\n            <view class=\"scenario-icon\" style=\"background-color: rgba(110, 231, 183, 0.2);\">\n              <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"#6EE7B7\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\">\n                <path d=\"M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2\"></path>\n                <circle cx=\"9\" cy=\"7\" r=\"4\"></circle>\n                <path d=\"M23 21v-2a4 4 0 0 0-3-3.87\"></path>\n                <path d=\"M16 3.13a4 4 0 0 1 0 7.75\"></path>\n              </svg>\n            </view>\n            <view class=\"scenario-content\">\n              <text class=\"scenario-title\">社交裂变</text>\n              <text class=\"scenario-desc\">鼓励用户分享红包给好友，实现低成本获客</text>\n            </view>\n          </view>\n          \n          <view class=\"scenario-item\">\n            <view class=\"scenario-icon\" style=\"background-color: rgba(110, 231, 183, 0.2);\">\n              <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"#6EE7B7\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\">\n                <path d=\"M22 11.08V12a10 10 0 1 1-5.93-9.14\"></path>\n                <polyline points=\"22 4 12 14.01 9 11.01\"></polyline>\n              </svg>\n            </view>\n            <view class=\"scenario-content\">\n              <text class=\"scenario-title\">促进复购</text>\n              <text class=\"scenario-desc\">向已购买用户发放红包，鼓励再次消费</text>\n            </view>\n          </view>\n        </view>\n        \n        <view class=\"section-title\">\n          <view class=\"title-icon\" style=\"background-color: rgba(110, 231, 183, 0.2);\">\n            <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"#6EE7B7\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\">\n              <circle cx=\"12\" cy=\"12\" r=\"10\"></circle>\n              <path d=\"M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3\"></path>\n              <line x1=\"12\" y1=\"17\" x2=\"12.01\" y2=\"17\"></line>\n            </svg>\n          </view>\n          <text class=\"title-text\">红包营销的最佳实践</text>\n        </view>\n        \n        <view class=\"practice-list\">\n          <view class=\"practice-item\">\n            <text class=\"practice-number\">01</text>\n            <view class=\"practice-content\">\n              <text class=\"practice-title\">明确营销目标</text>\n              <text class=\"practice-desc\">在开始红包活动前，明确活动目标是获客、促活还是转化，针对不同目标设计不同的红包策略。</text>\n            </view>\n          </view>\n          \n          <view class=\"practice-item\">\n            <text class=\"practice-number\">02</text>\n            <view class=\"practice-content\">\n              <text class=\"practice-title\">精准用户定向</text>\n              <text class=\"practice-desc\">根据用户画像和行为数据，向最有价值的用户群体投放红包，提高转化效率。</text>\n            </view>\n          </view>\n          \n          <view class=\"practice-item\">\n            <text class=\"practice-number\">03</text>\n            <view class=\"practice-content\">\n              <text class=\"practice-title\">设计合理金额</text>\n              <text class=\"practice-desc\">根据商品价格和预期转化率，设计具有吸引力且经济合理的红包金额。</text>\n            </view>\n          </view>\n          \n          <view class=\"practice-item\">\n            <text class=\"practice-number\">04</text>\n            <view class=\"practice-content\">\n              <text class=\"practice-title\">设置使用门槛</text>\n              <text class=\"practice-desc\">合理设置红包使用门槛，既能刺激消费又能保证商家利润。</text>\n            </view>\n          </view>\n          \n          <view class=\"practice-item\">\n            <text class=\"practice-number\">05</text>\n            <view class=\"practice-content\">\n              <text class=\"practice-title\">数据追踪与优化</text>\n              <text class=\"practice-desc\">实时监控红包活动数据，根据效果及时调整策略，优化投放效果。</text>\n            </view>\n          </view>\n        </view>\n        \n        <view class=\"section-title\">\n          <view class=\"title-icon\" style=\"background-color: rgba(110, 231, 183, 0.2);\">\n            <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"#6EE7B7\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\">\n              <path d=\"M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z\"></path>\n              <polyline points=\"14 2 14 8 20 8\"></polyline>\n              <line x1=\"16\" y1=\"13\" x2=\"8\" y2=\"13\"></line>\n              <line x1=\"16\" y1=\"17\" x2=\"8\" y2=\"17\"></line>\n              <polyline points=\"10 9 9 9 8 9\"></polyline>\n            </svg>\n          </view>\n          <text class=\"title-text\">案例分析</text>\n        </view>\n        \n        <view class=\"case-study\">\n          <view class=\"case-header\">\n            <text class=\"case-title\">某餐饮品牌新店开业红包案例</text>\n          </view>\n          <view class=\"case-content\">\n            <text class=\"case-desc\">某连锁餐饮品牌在新店开业期间，通过发放\"满50减20\"的红包，并鼓励用户分享给好友，好友领取后双方各得5元红包。活动期间，新店日均客流量提升了65%，新客获取成本降低了40%，社交媒体曝光增加了3倍。</text>\n          </view>\n          <view class=\"case-results\">\n            <view class=\"result-item\">\n              <text class=\"result-label\">客流提升</text>\n              <text class=\"result-value\">65%</text>\n            </view>\n            <view class=\"result-item\">\n              <text class=\"result-label\">获客成本降低</text>\n              <text class=\"result-value\">40%</text>\n            </view>\n            <view class=\"result-item\">\n              <text class=\"result-label\">社媒曝光增加</text>\n              <text class=\"result-value\">3倍</text>\n            </view>\n          </view>\n        </view>\n      </view>\n      \n      <!-- 联系我们 -->\n      <view class=\"contact-section\">\n        <text class=\"contact-title\">需要更多帮助？</text>\n        <text class=\"contact-desc\">如果您对红包营销有任何疑问，请联系我们的客服团队</text>\n        <button class=\"contact-btn\" @click=\"contactService\">联系客服</button>\n      </view>\n    </scroll-view>\n  </view>\n</template>\n\n<script>\nexport default {\n  data() {\n    return {\n      \n    }\n  },\n  methods: {\n    goBack() {\n      uni.navigateBack();\n    },\n    shareGuide() {\n      uni.showActionSheet({\n        itemList: ['分享给好友', '分享到朋友圈', '复制链接'],\n        success: function(res) {\n          uni.showToast({\n            title: '分享成功',\n            icon: 'success'\n          });\n        }\n      });\n    },\n    contactService() {\n      uni.makePhoneCall({\n        phoneNumber: '************'\n      });\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.page-container {\n  display: flex;\n  flex-direction: column;\n  min-height: 100vh;\n  background-color: #f5f5f5;\n}\n\n/* 导航栏样式 */\n.navbar {\n  display: flex;\n  align-items: center;\n  height: 44px;\n  background-color: #fff;\n  padding: 0 15px;\n  position: relative;\n}\n\n.navbar-back {\n  width: 24px;\n  height: 24px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.back-icon {\n  width: 12px;\n  height: 12px;\n  border-top: 2px solid #333;\n  border-left: 2px solid #333;\n  transform: rotate(-45deg);\n}\n\n.navbar-title {\n  flex: 1;\n  text-align: center;\n  font-size: 17px;\n  font-weight: 600;\n  color: #333;\n}\n\n.navbar-right {\n  width: 24px;\n  height: 24px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.share-icon {\n  color: #333;\n}\n\n/* 内容滚动区 */\n.content-scroll {\n  flex: 1;\n}\n\n/* 页面头部 */\n.page-header {\n  height: 180px;\n  position: relative;\n  overflow: hidden;\n}\n\n.header-bg {\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n  padding: 20px;\n}\n\n.header-title {\n  font-size: 24px;\n  font-weight: 700;\n  color: #fff;\n  margin-bottom: 10px;\n}\n\n.header-subtitle {\n  font-size: 16px;\n  color: rgba(255, 255, 255, 0.8);\n}\n\n/* 内容部分 */\n.content-section {\n  padding: 20px 15px;\n  background-color: #fff;\n  border-radius: 15px 15px 0 0;\n  margin-top: -20px;\n  position: relative;\n  z-index: 1;\n}\n\n.section-title {\n  display: flex;\n  align-items: center;\n  margin-bottom: 15px;\n  margin-top: 25px;\n}\n\n.section-title:first-child {\n  margin-top: 0;\n}\n\n.title-icon {\n  width: 36px;\n  height: 36px;\n  border-radius: 18px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-right: 10px;\n}\n\n.title-text {\n  font-size: 18px;\n  font-weight: 600;\n  color: #333;\n}\n\n.content-text {\n  font-size: 15px;\n  color: #666;\n  line-height: 1.6;\n  margin-bottom: 15px;\n}\n\n.content-image {\n  width: 100%;\n  border-radius: 8px;\n  overflow: hidden;\n  margin-bottom: 20px;\n}\n\n.content-image image {\n  width: 100%;\n}\n\n/* 数据卡片 */\n.data-cards {\n  display: flex;\n  flex-wrap: wrap;\n  margin: 0 -5px 20px;\n}\n\n.data-card {\n  width: calc(50% - 10px);\n  margin: 5px;\n  background-color: #f9f9f9;\n  border-radius: 8px;\n  padding: 15px;\n  text-align: center;\n}\n\n.data-value {\n  font-size: 20px;\n  font-weight: 700;\n  color: #6EE7B7;\n  display: block;\n  margin-bottom: 5px;\n}\n\n.data-label {\n  font-size: 13px;\n  color: #666;\n}\n\n/* 场景列表 */\n.scenario-list {\n  margin-bottom: 20px;\n}\n\n.scenario-item {\n  display: flex;\n  margin-bottom: 15px;\n  padding: 15px;\n  background-color: #f9f9f9;\n  border-radius: 8px;\n}\n\n.scenario-icon {\n  width: 40px;\n  height: 40px;\n  border-radius: 20px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-right: 15px;\n}\n\n.scenario-content {\n  flex: 1;\n}\n\n.scenario-title {\n  font-size: 16px;\n  font-weight: 600;\n  color: #333;\n  margin-bottom: 5px;\n  display: block;\n}\n\n.scenario-desc {\n  font-size: 14px;\n  color: #666;\n  line-height: 1.4;\n}\n\n/* 最佳实践 */\n.practice-list {\n  margin-bottom: 20px;\n}\n\n.practice-item {\n  display: flex;\n  margin-bottom: 15px;\n}\n\n.practice-number {\n  font-size: 18px;\n  font-weight: 700;\n  color: #6EE7B7;\n  margin-right: 15px;\n}\n\n.practice-content {\n  flex: 1;\n}\n\n.practice-title {\n  font-size: 16px;\n  font-weight: 600;\n  color: #333;\n  margin-bottom: 5px;\n  display: block;\n}\n\n.practice-desc {\n  font-size: 14px;\n  color: #666;\n  line-height: 1.4;\n}\n\n/* 案例分析 */\n.case-study {\n  background-color: #f9f9f9;\n  border-radius: 8px;\n  padding: 15px;\n  margin-bottom: 20px;\n}\n\n.case-header {\n  margin-bottom: 10px;\n}\n\n.case-title {\n  font-size: 16px;\n  font-weight: 600;\n  color: #333;\n}\n\n.case-content {\n  margin-bottom: 15px;\n}\n\n.case-desc {\n  font-size: 14px;\n  color: #666;\n  line-height: 1.5;\n}\n\n.case-results {\n  display: flex;\n  justify-content: space-between;\n  border-top: 1px solid #eee;\n  padding-top: 15px;\n}\n\n.result-item {\n  text-align: center;\n  flex: 1;\n}\n\n.result-label {\n  font-size: 12px;\n  color: #999;\n  display: block;\n  margin-bottom: 5px;\n}\n\n.result-value {\n  font-size: 18px;\n  font-weight: 600;\n  color: #6EE7B7;\n}\n\n/* 联系我们 */\n.contact-section {\n  margin: 0 15px 30px;\n  padding: 20px;\n  background-color: #fff;\n  border-radius: 10px;\n  text-align: center;\n  box-shadow: 0 2px 8px rgba(0,0,0,0.05);\n}\n\n.contact-title {\n  font-size: 16px;\n  font-weight: 600;\n  color: #333;\n  display: block;\n  margin-bottom: 10px;\n}\n\n.contact-desc {\n  font-size: 14px;\n  color: #666;\n  display: block;\n  margin-bottom: 15px;\n}\n\n.contact-btn {\n  background-color: #6EE7B7;\n  color: #fff;\n  border: none;\n  border-radius: 20px;\n  padding: 8px 20px;\n  font-size: 14px;\n}\n</style>", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/subPackages/merchant-admin-marketing/pages/marketing/redpacket/redpacket-marketing-overview.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni"], "mappings": ";;;AA2PA,MAAK,YAAU;AAAA,EACb,OAAO;AACL,WAAO,CAEP;AAAA,EACD;AAAA,EACD,SAAS;AAAA,IACP,SAAS;AACPA,oBAAG,MAAC,aAAY;AAAA,IACjB;AAAA,IACD,aAAa;AACXA,oBAAAA,MAAI,gBAAgB;AAAA,QAClB,UAAU,CAAC,SAAS,UAAU,MAAM;AAAA,QACpC,SAAS,SAAS,KAAK;AACrBA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,UACR,CAAC;AAAA,QACH;AAAA,MACF,CAAC;AAAA,IACF;AAAA,IACD,iBAAiB;AACfA,oBAAAA,MAAI,cAAc;AAAA,QAChB,aAAa;AAAA,MACf,CAAC;AAAA,IACH;AAAA,EACF;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACrRA,GAAG,WAAW,eAAe;"}