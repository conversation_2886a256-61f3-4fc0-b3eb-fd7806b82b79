/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 移除阿里妈妈字体引用，改用系统默认字体 */
/* 移除原有阿里妈妈字体定义
$uni-font-family: 'AlimamaShuHeiTi';
$uni-font-family-text: 'AlimamaShuHeiTi', -apple-system, BlinkMacSystemFont, 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;
*/
body, html, #app, .index-container {
  font-family: "AlimamaShuHeiTi", -apple-system, BlinkMacSystemFont, "Helvetica Neue", "PingFang SC", "Microsoft YaHei", sans-serif;
}
.records-container {
  min-height: 100vh;
  background-color: #F5F7FA;
  padding-bottom: 20px;
}

/* 导航栏样式 */
.navbar {
  position: -webkit-sticky;
  position: sticky;
  top: 0;
  left: 0;
  right: 0;
  background: linear-gradient(135deg, #e67e22, #d35400);
  color: #fff;
  padding: 44px 16px 15px;
  display: flex;
  align-items: center;
  z-index: 100;
  box-shadow: 0 4px 20px rgba(230, 126, 34, 0.2);
}
.navbar-back {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.back-icon {
  width: 12px;
  height: 12px;
  border-left: 2px solid #fff;
  border-bottom: 2px solid #fff;
  transform: rotate(45deg);
}
.navbar-title {
  flex: 1;
  text-align: center;
  font-size: 18px;
  font-weight: 600;
  letter-spacing: 0.5px;
}
.navbar-right {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.help-icon {
  width: 24px;
  height: 24px;
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: bold;
}

/* 筛选区域样式 */
.filter-area {
  background: #FFFFFF;
  padding: 20px;
  margin-bottom: 15px;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.08);
}
.filter-tabs {
  display: flex;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  padding-bottom: 12px;
  margin-bottom: 18px;
}
.filter-tab {
  margin-right: 25px;
  padding: 6px 0;
  font-size: 15px;
  color: #666;
  position: relative;
  transition: color 0.2s;
}
.filter-tab.active {
  color: #e67e22;
  font-weight: 600;
}
.filter-tab.active::after {
  content: "";
  position: absolute;
  bottom: -13px;
  left: 0;
  right: 0;
  height: 3px;
  background: #e67e22;
  border-radius: 1.5px;
}
.filter-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.date-filter {
  display: flex;
  align-items: center;
  background: #F5F7FA;
  border-radius: 18px;
  padding: 8px 14px;
  transition: background-color 0.2s;
}
.date-filter:active {
  background: #EBEEF2;
}
.date-text {
  font-size: 14px;
  color: #666;
  margin-right: 8px;
  font-weight: 500;
}
.date-arrow {
  width: 8px;
  height: 8px;
  border-top: 2px solid #666;
  border-right: 2px solid #666;
  transform: rotate(135deg);
}
.search-btn {
  width: 36px;
  height: 36px;
  border-radius: 18px;
  background: #F5F7FA;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #666;
  transition: background-color 0.2s;
}
.search-btn:active {
  background: #EBEEF2;
}

/* 记录列表样式 */
.records-list {
  background: #FFFFFF;
  margin: 0 15px;
  border-radius: 20px;
  padding: 20px;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.08);
}
.empty-records {
  padding: 50px 0;
  text-align: center;
}
.empty-text {
  font-size: 14px;
  color: #999;
}
.record-item {
  display: flex;
  align-items: center;
  padding: 16px 0;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}
.record-item:last-child {
  border-bottom: none;
}
.record-type {
  width: 60px;
  height: 28px;
  border-radius: 14px;
  font-size: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 14px;
  font-weight: 500;
}
.type-group {
  background-color: rgba(39, 174, 96, 0.15);
  color: #27ae60;
}
.type-coupon {
  background-color: rgba(41, 128, 185, 0.15);
  color: #2980b9;
}
.type-flash {
  background-color: rgba(231, 76, 60, 0.15);
  color: #e74c3c;
}
.record-content {
  flex: 1;
}
.record-main {
  margin-bottom: 4px;
}
.record-title {
  font-size: 15px;
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
}
.record-code {
  font-size: 13px;
  color: #777;
}
.record-info {
  display: flex;
  font-size: 13px;
  color: #777;
  margin-top: 6px;
}
.record-user {
  margin-right: 12px;
}
.record-status {
  font-size: 13px;
  font-weight: 500;
}
.status-success {
  color: #27ae60;
}
.status-pending {
  color: #e67e22;
}
.status-failed {
  color: #e74c3c;
}

/* 加载更多样式 */
.load-more {
  text-align: center;
  padding: 20px 0;
}
.load-text {
  font-size: 15px;
  color: #2980b9;
  font-weight: 500;
  padding: 10px 20px;
  border-radius: 18px;
  background: rgba(41, 128, 185, 0.1);
  display: inline-block;
  transition: background-color 0.2s;
}
.load-text:active {
  background: rgba(41, 128, 185, 0.15);
}

/* 搜索弹窗样式 */
.search-popup {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: #FFFFFF;
  z-index: 999;
}
.search-header {
  padding: 44px 15px 15px;
  display: flex;
  align-items: center;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
}
.search-input-box {
  flex: 1;
  position: relative;
  margin-right: 15px;
}
.search-input {
  width: 100%;
  height: 40px;
  background: #F5F7FA;
  border-radius: 20px;
  padding: 0 18px;
  font-size: 15px;
  color: #333;
  border: 1px solid rgba(0, 0, 0, 0.05);
}
.clear-btn {
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  width: 18px;
  height: 18px;
  border-radius: 9px;
  background: #C7C7CC;
  color: #FFFFFF;
  font-size: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.cancel-btn {
  font-size: 15px;
  color: #2980b9;
  font-weight: 500;
  padding: 8px 12px;
  border-radius: 16px;
}
.cancel-btn:active {
  background: rgba(41, 128, 185, 0.1);
}

/* 记录详情弹窗样式 */
.detail-popup {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
  z-index: 999;
  display: flex;
  align-items: center;
  justify-content: center;
}
.popup-content {
  width: 85%;
  background: #fff;
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.3);
}
.popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 18px 20px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  background: #f8f8f8;
}
.popup-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}
.popup-close {
  font-size: 22px;
  color: #777;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 18px;
}
.popup-close:active {
  background: rgba(0, 0, 0, 0.05);
}
.detail-info {
  padding: 20px;
  position: relative;
}
.detail-status {
  position: absolute;
  top: 20px;
  right: 20px;
  font-size: 15px;
  font-weight: 600;
}
.detail-item {
  display: flex;
  margin-bottom: 16px;
}
.detail-item:last-child {
  margin-bottom: 0;
}
.detail-label {
  width: 90px;
  font-size: 15px;
  color: #777;
}
.detail-value {
  flex: 1;
  font-size: 15px;
  color: #333;
  font-weight: 500;
}
.close-btn {
  width: 100%;
  height: 56px;
  line-height: 56px;
  text-align: center;
  font-size: 17px;
  font-weight: 500;
  color: #2980b9;
  border: none;
  border-top: 1px solid rgba(0, 0, 0, 0.05);
  border-radius: 0;
  background: #fff;
}