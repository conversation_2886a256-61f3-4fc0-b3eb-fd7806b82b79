/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 移除阿里妈妈字体引用，改用系统默认字体 */
/* 移除原有阿里妈妈字体定义
$uni-font-family: 'AlimamaShuHeiTi';
$uni-font-family-text: 'AlimamaShuHeiTi', -apple-system, BlinkMacSystemFont, 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;
*/
body.data-v-28a7193a, html.data-v-28a7193a, #app.data-v-28a7193a, .index-container.data-v-28a7193a {
  font-family: "AlimamaShuHeiTi", -apple-system, BlinkMacSystemFont, "Helvetica Neue", "PingFang SC", "Microsoft YaHei", sans-serif;
}
.team-container.data-v-28a7193a {
  min-height: 100vh;
  background-color: #f5f7fa;
  padding-bottom: 30rpx;
}
.stats-card.data-v-28a7193a {
  margin: 30rpx;
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}
.stats-header.data-v-28a7193a {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}
.stats-title.data-v-28a7193a {
  font-size: 30rpx;
  font-weight: 500;
  color: #333333;
}
.stats-time.data-v-28a7193a {
  font-size: 24rpx;
  color: #999999;
}
.stats-grid.data-v-28a7193a {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  grid-gap: 30rpx;
}
.stats-item.data-v-28a7193a {
  display: flex;
  flex-direction: column;
  align-items: center;
  background-color: #f8f8f8;
  padding: 20rpx 0;
  border-radius: 12rpx;
}
.stats-num.data-v-28a7193a {
  font-size: 36rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 10rpx;
}
.stats-label.data-v-28a7193a {
  font-size: 24rpx;
  color: #999999;
}
.tab-section.data-v-28a7193a {
  margin: 30rpx;
  background-color: #ffffff;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}
.tab-header.data-v-28a7193a {
  display: flex;
  border-bottom: 1px solid #f0f0f0;
}
.tab-item.data-v-28a7193a {
  flex: 1;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  color: #666666;
  position: relative;
}
.tab-item.active.data-v-28a7193a {
  color: #1677FF;
  font-weight: 500;
}
.tab-item.active.data-v-28a7193a::after {
  content: "";
  position: absolute;
  left: 50%;
  bottom: 0;
  transform: translateX(-50%);
  width: 40rpx;
  height: 4rpx;
  background-color: #1677FF;
  border-radius: 2rpx;
}
.tab-info.data-v-28a7193a {
  padding: 20rpx;
  font-size: 24rpx;
  color: #999999;
  text-align: center;
}
.team-list.data-v-28a7193a {
  padding: 0 30rpx;
}
.team-item.data-v-28a7193a {
  display: flex;
  align-items: center;
  padding: 30rpx;
  background-color: #ffffff;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}
.member-avatar.data-v-28a7193a {
  margin-right: 20rpx;
}
.member-avatar image.data-v-28a7193a {
  width: 90rpx;
  height: 90rpx;
  border-radius: 50%;
}
.member-info.data-v-28a7193a {
  flex: 1;
}
.member-name-row.data-v-28a7193a {
  display: flex;
  align-items: center;
  margin-bottom: 10rpx;
}
.member-name.data-v-28a7193a {
  font-size: 30rpx;
  font-weight: 500;
  color: #333333;
  margin-right: 15rpx;
}
.member-level.data-v-28a7193a {
  font-size: 20rpx;
  color: #ff9500;
  background-color: #fff7e6;
  padding: 4rpx 10rpx;
  border-radius: 8rpx;
}
.member-data.data-v-28a7193a {
  font-size: 24rpx;
  color: #999999;
}
.member-right.data-v-28a7193a {
  text-align: right;
}
.member-contribution.data-v-28a7193a {
  font-size: 26rpx;
  color: #ff6000;
  font-weight: 500;
  margin-bottom: 6rpx;
}
.member-fans.data-v-28a7193a {
  font-size: 22rpx;
  color: #999999;
}
.empty-state.data-v-28a7193a {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 100rpx 0;
}
.empty-state image.data-v-28a7193a {
  width: 240rpx;
  height: 240rpx;
  margin-bottom: 30rpx;
}
.empty-state text.data-v-28a7193a {
  font-size: 28rpx;
  color: #999999;
  margin-bottom: 40rpx;
}
.empty-state .share-btn.data-v-28a7193a {
  width: 300rpx;
  height: 80rpx;
  line-height: 80rpx;
  background: linear-gradient(135deg, #1677FF, #0E5FD8);
  color: #ffffff;
  font-size: 28rpx;
  border-radius: 40rpx;
}
.load-more.data-v-28a7193a, .load-end.data-v-28a7193a {
  text-align: center;
  font-size: 26rpx;
  color: #999999;
  padding: 30rpx 0;
}