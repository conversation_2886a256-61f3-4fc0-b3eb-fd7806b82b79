{"version": 3, "file": "index.js", "sources": ["subPackages/distribution/pages/index.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/c3ViUGFja2FnZXNcZGlzdHJpYnV0aW9uXHBhZ2VzXGluZGV4LnZ1ZQ"], "sourcesContent": ["<template>\r\n  <view class=\"distribution-center\">\r\n    <!-- 自定义导航栏 -->\r\n    <view class=\"navbar\">\r\n      <view class=\"navbar-back\" @click=\"goBack\">\r\n        <view class=\"back-icon\"></view>\r\n      </view>\r\n      <text class=\"navbar-title\">我要赚钱</text>\r\n      <view class=\"navbar-right\">\r\n        <view class=\"help-icon\" @click=\"showHelp\">?</view>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 用户信息卡片 -->\r\n    <view class=\"user-card\">\r\n      <view class=\"user-info\">\r\n        <image class=\"user-avatar\" :src=\"userInfo.avatar || '/static/images/default-avatar.png'\" mode=\"aspectFill\"></image>\r\n        <view class=\"user-details\">\r\n          <text class=\"user-name\">{{userInfo.nickname || '游客'}}</text>\r\n          <view class=\"user-level\">\r\n            <text class=\"level-tag\">{{distributorInfo.level || '普通用户'}}</text>\r\n            <text class=\"invite-code\" v-if=\"distributorInfo.inviteCode\">邀请码: {{distributorInfo.inviteCode}}</text>\r\n          </view>\r\n        </view>\r\n      </view>\r\n      <view class=\"user-status\" v-if=\"!isDistributor\">\r\n        <button class=\"apply-btn\" @click=\"navigateTo('/subPackages/distribution/pages/apply')\">申请成为分销员</button>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 数据概览卡片 -->\r\n    <view class=\"stats-card\" v-if=\"isDistributor\">\r\n      <view class=\"stats-header\">\r\n        <text class=\"stats-title\">收益概览</text>\r\n        <view class=\"stats-action\" @click=\"navigateTo('/subPackages/distribution/pages/commission')\">\r\n          <text class=\"action-text\">佣金明细</text>\r\n          <view class=\"action-arrow\"></view>\r\n        </view>\r\n      </view>\r\n      \r\n      <view class=\"stats-content\">\r\n        <view class=\"stats-item main\">\r\n          <text class=\"item-value\">¥{{formatCommission(distributorInfo.totalCommission)}}</text>\r\n          <text class=\"item-label\">累计佣金</text>\r\n        </view>\r\n        \r\n        <view class=\"stats-row\">\r\n          <view class=\"stats-item\">\r\n            <text class=\"item-value\">¥{{formatCommission(distributorInfo.availableCommission)}}</text>\r\n            <text class=\"item-label\">可提现</text>\r\n          </view>\r\n          \r\n          <view class=\"stats-item\">\r\n            <text class=\"item-value\">¥{{formatCommission(distributorInfo.pendingCommission)}}</text>\r\n            <text class=\"item-label\">待结算</text>\r\n          </view>\r\n          \r\n          <view class=\"stats-item\">\r\n            <text class=\"item-value\">¥{{formatCommission(distributorInfo.withdrawnCommission)}}</text>\r\n            <text class=\"item-label\">已提现</text>\r\n          </view>\r\n        </view>\r\n      </view>\r\n      \r\n      <view class=\"stats-footer\">\r\n        <button class=\"withdraw-btn\" @click=\"navigateTo('/subPackages/distribution/pages/withdraw')\">立即提现</button>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 功能菜单 -->\r\n    <view class=\"menu-section\" v-if=\"isDistributor\">\r\n      <view class=\"menu-grid\">\r\n        <view class=\"menu-item\" @click=\"navigateTo('/subPackages/distribution/pages/products')\">\r\n          <view class=\"menu-icon products\"></view>\r\n          <text class=\"menu-name\">分销商品</text>\r\n        </view>\r\n        \r\n        <view class=\"menu-item\" @click=\"navigateTo('/subPackages/distribution/pages/team')\">\r\n          <view class=\"menu-icon team\"></view>\r\n          <text class=\"menu-name\">我的团队</text>\r\n        </view>\r\n        \r\n        <view class=\"menu-item\" @click=\"navigateTo('/subPackages/distribution/pages/promotion')\">\r\n          <view class=\"menu-icon promotion\"></view>\r\n          <text class=\"menu-name\">推广工具</text>\r\n        </view>\r\n        \r\n        <view class=\"menu-item\" @click=\"navigateTo('/subPackages/distribution/pages/commission')\">\r\n          <view class=\"menu-icon commission\"></view>\r\n          <text class=\"menu-name\">佣金明细</text>\r\n        </view>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 推荐商品卡片 -->\r\n    <view class=\"products-section\" v-if=\"isDistributor\">\r\n      <view class=\"section-header\">\r\n        <text class=\"section-title\">推荐分销商品</text>\r\n        <view class=\"section-action\" @click=\"navigateTo('/subPackages/distribution/pages/products')\">\r\n          <text class=\"action-text\">查看全部</text>\r\n          <view class=\"action-arrow\"></view>\r\n        </view>\r\n      </view>\r\n      \r\n      <view class=\"products-list\">\r\n        <view class=\"product-card\" v-for=\"(product, index) in recommendedProducts\" :key=\"index\" @click=\"navigateToProduct(product)\">\r\n          <image class=\"product-image\" :src=\"product.image\" mode=\"aspectFill\"></image>\r\n          <view class=\"product-info\">\r\n            <text class=\"product-name\">{{product.name}}</text>\r\n            <view class=\"product-price-row\">\r\n              <text class=\"product-price\">¥{{product.price}}</text>\r\n              <text class=\"commission-tag\">赚¥{{product.commission}}</text>\r\n            </view>\r\n          </view>\r\n        </view>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 分销员申请引导 -->\r\n    <view class=\"apply-guide\" v-if=\"!isDistributor\">\r\n      <view class=\"guide-header\">\r\n        <text class=\"guide-title\">成为分销员的好处</text>\r\n      </view>\r\n      \r\n      <view class=\"guide-content\">\r\n        <view class=\"benefit-item\">\r\n          <view class=\"benefit-icon income\"></view>\r\n          <view class=\"benefit-info\">\r\n            <text class=\"benefit-title\">额外收入</text>\r\n            <text class=\"benefit-desc\">分享商品赚取佣金，轻松获得额外收入</text>\r\n          </view>\r\n        </view>\r\n        \r\n        <view class=\"benefit-item\">\r\n          <view class=\"benefit-icon tools\"></view>\r\n          <view class=\"benefit-info\">\r\n            <text class=\"benefit-title\">专业工具</text>\r\n            <text class=\"benefit-desc\">获得专业推广工具，提高转化率</text>\r\n          </view>\r\n        </view>\r\n        \r\n        <view class=\"benefit-item\">\r\n          <view class=\"benefit-icon team\"></view>\r\n          <view class=\"benefit-info\">\r\n            <text class=\"benefit-title\">团队收益</text>\r\n            <text class=\"benefit-desc\">发展下级分销员，获得团队佣金</text>\r\n          </view>\r\n        </view>\r\n      </view>\r\n      \r\n      <view class=\"guide-footer\">\r\n        <button class=\"apply-btn large\" @click=\"navigateTo('/subPackages/distribution/pages/apply')\">立即申请成为分销员</button>\r\n      </view>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script setup>\r\nimport { ref, reactive, onMounted } from 'vue';\r\nimport distributionService from '@/utils/distributionService';\r\n\r\n// 用户信息\r\nconst userInfo = reactive({\r\n  userId: '1001', // 模拟数据，实际应从用户系统获取\r\n  nickname: '张三',\r\n  avatar: '/static/images/default-avatar.png'\r\n});\r\n\r\n// 分销员信息\r\nconst distributorInfo = reactive({\r\n  level: '普通分销员',\r\n  totalCommission: 0,\r\n  availableCommission: 0,\r\n  pendingCommission: 0,\r\n  withdrawnCommission: 0,\r\n  inviteCode: ''\r\n});\r\n\r\n// 是否是分销员\r\nconst isDistributor = ref(false);\r\n\r\n// 推荐商品\r\nconst recommendedProducts = ref([]);\r\n\r\n// 页面加载\r\nonMounted(async () => {\r\n  // 检查是否是分销员\r\n  await checkDistributorStatus();\r\n  \r\n  // 获取推荐商品\r\n  if (isDistributor.value) {\r\n    await getRecommendedProducts();\r\n  }\r\n});\r\n\r\n// 检查分销员状态\r\nconst checkDistributorStatus = async () => {\r\n  try {\r\n    // 检查是否是分销员\r\n    isDistributor.value = await distributionService.isDistributor({\r\n      userId: userInfo.userId\r\n    });\r\n    \r\n    // 如果是分销员，获取分销员信息\r\n    if (isDistributor.value) {\r\n      const info = await distributionService.getDistributorInfo({\r\n        userId: userInfo.userId\r\n      });\r\n      \r\n      if (info) {\r\n        Object.assign(distributorInfo, info);\r\n      }\r\n    }\r\n  } catch (error) {\r\n    console.error('检查分销员状态失败', error);\r\n    uni.showToast({\r\n      title: '获取分销员信息失败',\r\n      icon: 'none'\r\n    });\r\n  }\r\n};\r\n\r\n// 获取推荐商品\r\nconst getRecommendedProducts = async () => {\r\n  try {\r\n    const result = await distributionService.getDistributableProducts({\r\n      page: 1,\r\n      pageSize: 4,\r\n      sortBy: 'commission'\r\n    });\r\n    \r\n    recommendedProducts.value = result.list || [];\r\n  } catch (error) {\r\n    console.error('获取推荐商品失败', error);\r\n  }\r\n};\r\n\r\n// 格式化佣金\r\nconst formatCommission = (amount) => {\r\n  return distributionService.formatCommission(amount);\r\n};\r\n\r\n// 页面导航\r\nconst navigateTo = (url) => {\r\n  uni.navigateTo({ url });\r\n};\r\n\r\n// 导航到商品详情\r\nconst navigateToProduct = (product) => {\r\n  uni.navigateTo({\r\n    url: `/pages/product/detail?id=${product.id}&isDistribution=true`\r\n  });\r\n};\r\n\r\n// 返回上一页\r\nconst goBack = () => {\r\n  uni.navigateBack();\r\n};\r\n\r\n// 显示帮助\r\nconst showHelp = () => {\r\n  uni.showModal({\r\n    title: '我要赚钱帮助',\r\n    content: '在我要赚钱，您可以查看佣金收益、管理团队、使用推广工具以及提现佣金等。',\r\n    showCancel: false\r\n  });\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.distribution-center {\r\n  min-height: 100vh;\r\n  background-color: #F5F7FA;\r\n  padding-bottom: 30rpx;\r\n}\r\n\r\n/* 导航栏样式 */\r\n.navbar {\r\n  position: sticky;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  background: linear-gradient(135deg, #A764CA, #6B0FBE);\r\n  color: #fff;\r\n  padding: 88rpx 32rpx 30rpx;\r\n  display: flex;\r\n  align-items: center;\r\n  z-index: 100;\r\n  box-shadow: 0 4rpx 20rpx rgba(107, 15, 190, 0.15);\r\n}\r\n\r\n.navbar-back {\r\n  width: 72rpx;\r\n  height: 72rpx;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.back-icon {\r\n  width: 24rpx;\r\n  height: 24rpx;\r\n  border-left: 4rpx solid #fff;\r\n  border-bottom: 4rpx solid #fff;\r\n  transform: rotate(45deg);\r\n}\r\n\r\n.navbar-title {\r\n  flex: 1;\r\n  text-align: center;\r\n  font-size: 36rpx;\r\n  font-weight: 600;\r\n  letter-spacing: 1rpx;\r\n}\r\n\r\n.navbar-right {\r\n  width: 72rpx;\r\n  height: 72rpx;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.help-icon {\r\n  width: 48rpx;\r\n  height: 48rpx;\r\n  border-radius: 24rpx;\r\n  background: rgba(255, 255, 255, 0.2);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  font-size: 28rpx;\r\n  font-weight: bold;\r\n}\r\n\r\n/* 用户卡片样式 */\r\n.user-card {\r\n  margin: 30rpx;\r\n  background: #FFFFFF;\r\n  border-radius: 20rpx;\r\n  padding: 30rpx;\r\n  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n.user-info {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.user-avatar {\r\n  width: 120rpx;\r\n  height: 120rpx;\r\n  border-radius: 60rpx;\r\n  margin-right: 20rpx;\r\n  border: 2rpx solid #f0f0f0;\r\n}\r\n\r\n.user-details {\r\n  flex: 1;\r\n}\r\n\r\n.user-name {\r\n  font-size: 36rpx;\r\n  font-weight: 600;\r\n  color: #333;\r\n  margin-bottom: 10rpx;\r\n}\r\n\r\n.user-level {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.level-tag {\r\n  background: linear-gradient(135deg, #A764CA, #6B0FBE);\r\n  color: #fff;\r\n  font-size: 24rpx;\r\n  padding: 6rpx 16rpx;\r\n  border-radius: 20rpx;\r\n  margin-right: 16rpx;\r\n}\r\n\r\n.invite-code {\r\n  font-size: 24rpx;\r\n  color: #666;\r\n}\r\n\r\n.user-status {\r\n  margin-top: 20rpx;\r\n}\r\n\r\n.apply-btn {\r\n  background: linear-gradient(135deg, #A764CA, #6B0FBE);\r\n  color: #fff;\r\n  border: none;\r\n  border-radius: 40rpx;\r\n  font-size: 28rpx;\r\n  padding: 12rpx 30rpx;\r\n  line-height: 1.5;\r\n}\r\n\r\n.apply-btn.large {\r\n  font-size: 32rpx;\r\n  padding: 20rpx 40rpx;\r\n  width: 80%;\r\n}\r\n\r\n/* 数据概览卡片 */\r\n.stats-card {\r\n  margin: 30rpx;\r\n  background: #FFFFFF;\r\n  border-radius: 20rpx;\r\n  padding: 30rpx;\r\n  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n.stats-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 30rpx;\r\n}\r\n\r\n.stats-title {\r\n  font-size: 32rpx;\r\n  font-weight: 600;\r\n  color: #333;\r\n}\r\n\r\n.stats-action {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.action-text {\r\n  font-size: 26rpx;\r\n  color: #666;\r\n}\r\n\r\n.action-arrow {\r\n  width: 16rpx;\r\n  height: 16rpx;\r\n  border-right: 2rpx solid #666;\r\n  border-top: 2rpx solid #666;\r\n  transform: rotate(45deg);\r\n  margin-left: 10rpx;\r\n}\r\n\r\n.stats-content {\r\n  margin-bottom: 30rpx;\r\n}\r\n\r\n.stats-item {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.stats-item.main {\r\n  margin-bottom: 30rpx;\r\n}\r\n\r\n.stats-row {\r\n  display: flex;\r\n  justify-content: space-between;\r\n}\r\n\r\n.item-value {\r\n  font-size: 32rpx;\r\n  font-weight: 600;\r\n  color: #333;\r\n  margin-bottom: 8rpx;\r\n}\r\n\r\n.stats-item.main .item-value {\r\n  font-size: 48rpx;\r\n  color: #6B0FBE;\r\n}\r\n\r\n.item-label {\r\n  font-size: 24rpx;\r\n  color: #666;\r\n}\r\n\r\n.stats-footer {\r\n  display: flex;\r\n  justify-content: center;\r\n}\r\n\r\n.withdraw-btn {\r\n  background: linear-gradient(135deg, #FF9500, #FF5722);\r\n  color: #fff;\r\n  border: none;\r\n  border-radius: 40rpx;\r\n  font-size: 28rpx;\r\n  padding: 12rpx 60rpx;\r\n  line-height: 1.5;\r\n}\r\n\r\n/* 功能菜单 */\r\n.menu-section {\r\n  margin: 30rpx;\r\n}\r\n\r\n.menu-grid {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  background: #FFFFFF;\r\n  border-radius: 20rpx;\r\n  overflow: hidden;\r\n  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n.menu-item {\r\n  width: 25%;\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  padding: 30rpx 0;\r\n}\r\n\r\n.menu-icon {\r\n  width: 80rpx;\r\n  height: 80rpx;\r\n  margin-bottom: 16rpx;\r\n  border-radius: 40rpx;\r\n}\r\n\r\n.menu-icon.products {\r\n  background-color: #FF9500;\r\n}\r\n\r\n.menu-icon.team {\r\n  background-color: #409EFF;\r\n}\r\n\r\n.menu-icon.promotion {\r\n  background-color: #67C23A;\r\n}\r\n\r\n.menu-icon.commission {\r\n  background-color: #E6A23C;\r\n}\r\n\r\n.menu-name {\r\n  font-size: 26rpx;\r\n  color: #333;\r\n}\r\n\r\n/* 推荐商品 */\r\n.products-section {\r\n  margin: 30rpx;\r\n}\r\n\r\n.section-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 20rpx;\r\n}\r\n\r\n.section-title {\r\n  font-size: 32rpx;\r\n  font-weight: 600;\r\n  color: #333;\r\n}\r\n\r\n.section-action {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.products-list {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  margin: 0 -10rpx;\r\n}\r\n\r\n.product-card {\r\n  width: calc(50% - 20rpx);\r\n  margin: 10rpx;\r\n  background: #FFFFFF;\r\n  border-radius: 16rpx;\r\n  overflow: hidden;\r\n  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n.product-image {\r\n  width: 100%;\r\n  height: 300rpx;\r\n  background-color: #f5f5f5;\r\n}\r\n\r\n.product-info {\r\n  padding: 20rpx;\r\n}\r\n\r\n.product-name {\r\n  font-size: 28rpx;\r\n  color: #333;\r\n  margin-bottom: 16rpx;\r\n  white-space: nowrap;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n}\r\n\r\n.product-price-row {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.product-price {\r\n  font-size: 32rpx;\r\n  font-weight: 600;\r\n  color: #FF5722;\r\n}\r\n\r\n.commission-tag {\r\n  font-size: 24rpx;\r\n  color: #6B0FBE;\r\n  background-color: rgba(107, 15, 190, 0.1);\r\n  padding: 4rpx 12rpx;\r\n  border-radius: 20rpx;\r\n}\r\n\r\n/* 分销员申请引导 */\r\n.apply-guide {\r\n  margin: 30rpx;\r\n  background: #FFFFFF;\r\n  border-radius: 20rpx;\r\n  padding: 30rpx;\r\n  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n.guide-header {\r\n  margin-bottom: 30rpx;\r\n}\r\n\r\n.guide-title {\r\n  font-size: 32rpx;\r\n  font-weight: 600;\r\n  color: #333;\r\n  text-align: center;\r\n}\r\n\r\n.guide-content {\r\n  margin-bottom: 30rpx;\r\n}\r\n\r\n.benefit-item {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-bottom: 30rpx;\r\n}\r\n\r\n.benefit-icon {\r\n  width: 80rpx;\r\n  height: 80rpx;\r\n  margin-right: 20rpx;\r\n  border-radius: 40rpx;\r\n}\r\n\r\n.benefit-icon.income {\r\n  background-color: #FF9500;\r\n}\r\n\r\n.benefit-icon.tools {\r\n  background-color: #67C23A;\r\n}\r\n\r\n.benefit-icon.team {\r\n  background-color: #409EFF;\r\n}\r\n\r\n.benefit-info {\r\n  flex: 1;\r\n}\r\n\r\n.benefit-title {\r\n  font-size: 28rpx;\r\n  font-weight: 600;\r\n  color: #333;\r\n  margin-bottom: 8rpx;\r\n}\r\n\r\n.benefit-desc {\r\n  font-size: 24rpx;\r\n  color: #666;\r\n}\r\n\r\n.guide-footer {\r\n  display: flex;\r\n  justify-content: center;\r\n  margin-top: 40rpx;\r\n}\r\n</style> ", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/subPackages/distribution/pages/index.vue'\nwx.createPage(MiniProgramPage)"], "names": ["reactive", "ref", "onMounted", "distributionService", "uni", "MiniProgramPage"], "mappings": ";;;;;;AAkKA,UAAM,WAAWA,cAAAA,SAAS;AAAA,MACxB,QAAQ;AAAA;AAAA,MACR,UAAU;AAAA,MACV,QAAQ;AAAA,IACV,CAAC;AAGD,UAAM,kBAAkBA,cAAAA,SAAS;AAAA,MAC/B,OAAO;AAAA,MACP,iBAAiB;AAAA,MACjB,qBAAqB;AAAA,MACrB,mBAAmB;AAAA,MACnB,qBAAqB;AAAA,MACrB,YAAY;AAAA,IACd,CAAC;AAGD,UAAM,gBAAgBC,cAAAA,IAAI,KAAK;AAG/B,UAAM,sBAAsBA,cAAAA,IAAI,CAAA,CAAE;AAGlCC,kBAAAA,UAAU,YAAY;AAEpB,YAAM,uBAAsB;AAG5B,UAAI,cAAc,OAAO;AACvB,cAAM,uBAAsB;AAAA,MAC7B;AAAA,IACH,CAAC;AAGD,UAAM,yBAAyB,YAAY;AACzC,UAAI;AAEF,sBAAc,QAAQ,MAAMC,0BAAmB,oBAAC,cAAc;AAAA,UAC5D,QAAQ,SAAS;AAAA,QACvB,CAAK;AAGD,YAAI,cAAc,OAAO;AACvB,gBAAM,OAAO,MAAMA,0BAAmB,oBAAC,mBAAmB;AAAA,YACxD,QAAQ,SAAS;AAAA,UACzB,CAAO;AAED,cAAI,MAAM;AACR,mBAAO,OAAO,iBAAiB,IAAI;AAAA,UACpC;AAAA,QACF;AAAA,MACF,SAAQ,OAAO;AACdC,sBAAc,MAAA,MAAA,SAAA,mDAAA,aAAa,KAAK;AAChCA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AAAA,MACF;AAAA,IACH;AAGA,UAAM,yBAAyB,YAAY;AACzC,UAAI;AACF,cAAM,SAAS,MAAMD,0BAAmB,oBAAC,yBAAyB;AAAA,UAChE,MAAM;AAAA,UACN,UAAU;AAAA,UACV,QAAQ;AAAA,QACd,CAAK;AAED,4BAAoB,QAAQ,OAAO,QAAQ,CAAA;AAAA,MAC5C,SAAQ,OAAO;AACdC,sBAAA,MAAA,MAAA,SAAA,mDAAc,YAAY,KAAK;AAAA,MAChC;AAAA,IACH;AAGA,UAAM,mBAAmB,CAAC,WAAW;AACnC,aAAOD,0BAAmB,oBAAC,iBAAiB,MAAM;AAAA,IACpD;AAGA,UAAM,aAAa,CAAC,QAAQ;AAC1BC,oBAAAA,MAAI,WAAW,EAAE,IAAG,CAAE;AAAA,IACxB;AAGA,UAAM,oBAAoB,CAAC,YAAY;AACrCA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,4BAA4B,QAAQ,EAAE;AAAA,MAC/C,CAAG;AAAA,IACH;AAGA,UAAM,SAAS,MAAM;AACnBA,oBAAG,MAAC,aAAY;AAAA,IAClB;AAGA,UAAM,WAAW,MAAM;AACrBA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,SAAS;AAAA,QACT,YAAY;AAAA,MAChB,CAAG;AAAA,IACH;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACzQA,GAAG,WAAWC,SAAe;"}