{"version": 3, "file": "points-acceleration.js", "sources": ["subPackages/merchant-admin-marketing/pages/marketing/member/points-acceleration.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/c3ViUGFja2FnZXNcbWVyY2hhbnQtYWRtaW4tbWFya2V0aW5nXHBhZ2VzXG1hcmtldGluZ1xtZW1iZXJccG9pbnRzLWFjY2VsZXJhdGlvbi52dWU"], "sourcesContent": ["<!-- 积分加速页面开始 -->\r\n<template>\r\n  <view class=\"points-acceleration-container\">\r\n    <!-- 页面标题区域 -->\r\n    <view class=\"page-header\">\r\n      <view class=\"title-section\">\r\n        <text class=\"page-title\">积分加速</text>\r\n        <text class=\"page-subtitle\">管理会员购物积分加速规则</text>\r\n      </view>\r\n      \r\n      <!-- 添加规则按钮 -->\r\n      <view class=\"add-rule-btn\" @click=\"showAddRuleModal\">\r\n        <text class=\"btn-text\">添加规则</text>\r\n        <text class=\"btn-icon\">+</text>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 积分加速说明 -->\r\n    <view class=\"info-card\">\r\n      <view class=\"info-header\">\r\n        <text class=\"info-title\">什么是积分加速？</text>\r\n      </view>\r\n      <view class=\"info-body\">\r\n        <text class=\"info-text\">积分加速是指会员在购物时可以获得比普通用户更多的积分奖励。您可以为不同等级的会员设置不同的积分加速倍率，提高会员的购物积极性和忠诚度。</text>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 积分加速规则列表 -->\r\n    <view class=\"rules-list\">\r\n      <view v-if=\"rules.length === 0\" class=\"empty-tip\">\r\n        <image class=\"empty-icon\" src=\"/static/images/empty-data.svg\"></image>\r\n        <text class=\"empty-text\">暂无积分加速规则，点击\"添加规则\"创建</text>\r\n      </view>\r\n      \r\n      <view v-else class=\"rule-cards\">\r\n        <view v-for=\"(rule, index) in rules\" :key=\"index\" class=\"rule-card\">\r\n          <view class=\"rule-card-header\" :style=\"{ backgroundColor: rule.color }\">\r\n            <view class=\"rule-name\">{{ rule.name }}</view>\r\n            <view class=\"rule-actions\">\r\n              <text class=\"action-btn edit\" @click=\"editRule(rule)\">编辑</text>\r\n              <text class=\"action-btn delete\" @click=\"confirmDeleteRule(rule)\">删除</text>\r\n            </view>\r\n          </view>\r\n          <view class=\"rule-card-body\">\r\n            <view class=\"rule-info-item\">\r\n              <text class=\"info-label\">规则图标：</text>\r\n              <image class=\"rule-icon\" :src=\"rule.icon\" mode=\"aspectFit\"></image>\r\n            </view>\r\n            <view class=\"rule-info-item\">\r\n              <text class=\"info-label\">适用等级：</text>\r\n              <view class=\"level-tags\">\r\n                <text \r\n                  v-for=\"(level, idx) in rule.applicableLevels\" \r\n                  :key=\"idx\" \r\n                  class=\"level-tag\"\r\n                >{{ level }}</text>\r\n              </view>\r\n            </view>\r\n            <view class=\"rule-info-item\">\r\n              <text class=\"info-label\">加速倍率：</text>\r\n              <text class=\"info-value\">{{ rule.multiplier }}倍</text>\r\n            </view>\r\n            <view class=\"rule-info-item\">\r\n              <text class=\"info-label\">适用范围：</text>\r\n              <text class=\"info-value\">{{ rule.scope }}</text>\r\n            </view>\r\n            <view class=\"rule-info-item\">\r\n              <text class=\"info-label\">规则说明：</text>\r\n              <text class=\"info-value\">{{ rule.description || '暂无规则说明' }}</text>\r\n            </view>\r\n          </view>\r\n        </view>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 添加/编辑规则弹窗 -->\r\n    <uni-popup ref=\"ruleFormPopup\" type=\"center\">\r\n      <view class=\"rule-form-popup\">\r\n        <view class=\"popup-header\">\r\n          <text class=\"popup-title\">{{ isEditing ? '编辑规则' : '添加规则' }}</text>\r\n          <text class=\"popup-close\" @click=\"closeRuleModal\">×</text>\r\n        </view>\r\n        <view class=\"popup-body\">\r\n          <view class=\"form-item\">\r\n            <text class=\"form-label\">规则名称</text>\r\n            <input class=\"form-input\" v-model=\"ruleForm.name\" placeholder=\"请输入规则名称\" />\r\n          </view>\r\n          <view class=\"form-item\">\r\n            <text class=\"form-label\">规则颜色</text>\r\n            <view class=\"color-picker\">\r\n              <view \r\n                v-for=\"(color, idx) in colorOptions\" \r\n                :key=\"idx\" \r\n                class=\"color-option\"\r\n                :class=\"{ active: ruleForm.color === color }\"\r\n                :style=\"{ backgroundColor: color }\"\r\n                @click=\"ruleForm.color = color\"\r\n              ></view>\r\n            </view>\r\n          </view>\r\n          <view class=\"form-item\">\r\n            <text class=\"form-label\">规则图标</text>\r\n            <view class=\"icon-upload\">\r\n              <image v-if=\"ruleForm.icon\" class=\"preview-icon\" :src=\"ruleForm.icon\" mode=\"aspectFit\"></image>\r\n              <view v-else class=\"upload-btn\" @click=\"chooseIcon\">\r\n                <text class=\"upload-icon\">+</text>\r\n                <text class=\"upload-text\">上传图标</text>\r\n              </view>\r\n            </view>\r\n          </view>\r\n          <view class=\"form-item\">\r\n            <text class=\"form-label\">加速倍率</text>\r\n            <view class=\"form-input-group\">\r\n              <input class=\"form-input\" type=\"digit\" v-model=\"ruleForm.multiplier\" placeholder=\"请输入加速倍率\" />\r\n              <text class=\"input-suffix\">倍</text>\r\n            </view>\r\n          </view>\r\n          <view class=\"form-item\">\r\n            <text class=\"form-label\">适用范围</text>\r\n            <picker class=\"form-picker\" :range=\"ruleScopes\" @change=\"onScopeChange\">\r\n              <view class=\"picker-value\">{{ ruleForm.scope || '请选择适用范围' }}</view>\r\n            </picker>\r\n          </view>\r\n          <view class=\"form-item\">\r\n            <text class=\"form-label\">适用等级</text>\r\n            <view class=\"level-checkboxes\">\r\n              <view \r\n                v-for=\"(level, idx) in memberLevels\" \r\n                :key=\"idx\" \r\n                class=\"level-checkbox\"\r\n                :class=\"{ active: isLevelSelected(level.name) }\"\r\n                @click=\"toggleLevelSelection(level.name)\"\r\n              >\r\n                <text class=\"checkbox-icon\">{{ isLevelSelected(level.name) ? '✓' : '' }}</text>\r\n                <text class=\"checkbox-label\">{{ level.name }}</text>\r\n              </view>\r\n            </view>\r\n          </view>\r\n          <view class=\"form-item\">\r\n            <text class=\"form-label\">规则说明</text>\r\n            <textarea class=\"form-textarea\" v-model=\"ruleForm.description\" placeholder=\"请输入规则说明\"></textarea>\r\n          </view>\r\n        </view>\r\n        <view class=\"popup-footer\">\r\n          <button class=\"cancel-btn\" @click=\"closeRuleModal\">取消</button>\r\n          <button class=\"confirm-btn\" @click=\"saveRuleForm\">确认</button>\r\n        </view>\r\n      </view>\r\n    </uni-popup>\r\n    \r\n    <!-- 删除确认弹窗 -->\r\n    <uni-popup ref=\"deleteConfirmPopup\" type=\"dialog\">\r\n      <uni-popup-dialog\r\n        type=\"warning\"\r\n        title=\"删除确认\"\r\n        content=\"确定要删除该积分加速规则吗？删除后将无法恢复。\"\r\n        :before-close=\"true\"\r\n        @confirm=\"deleteRule\"\r\n        @close=\"closeDeleteConfirm\"\r\n      ></uni-popup-dialog>\r\n    </uni-popup>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  data() {\r\n    return {\r\n      rules: [], // 积分加速规则列表\r\n      ruleForm: {\r\n        id: '',\r\n        name: '',\r\n        color: '#1E90FF', // 默认蓝色\r\n        icon: '',\r\n        multiplier: '',\r\n        scope: '',\r\n        applicableLevels: [],\r\n        description: ''\r\n      },\r\n      isEditing: false, // 是否为编辑模式\r\n      currentRuleId: null, // 当前编辑的规则ID\r\n      colorOptions: [\r\n        '#FF6B22', // 橙色\r\n        '#8A2BE2', // 紫色\r\n        '#1E90FF', // 道奇蓝\r\n        '#32CD32', // 酸橙绿\r\n        '#FFD700', // 金色\r\n        '#FF69B4', // 热粉红\r\n        '#20B2AA', // 浅海绿\r\n        '#FF8C00'  // 深橙色\r\n      ],\r\n      ruleScopes: [\r\n        '全部商品',\r\n        '指定分类商品',\r\n        '指定商品',\r\n        '会员专享商品'\r\n      ],\r\n      memberLevels: [] // 会员等级列表\r\n    };\r\n  },\r\n  onLoad() {\r\n    this.fetchRules();\r\n    this.fetchMemberLevels();\r\n  },\r\n  methods: {\r\n    // 获取积分加速规则列表\r\n    fetchRules() {\r\n      // 模拟数据，实际项目中应从API获取\r\n      this.rules = [\r\n        {\r\n          id: '1',\r\n          name: '银卡会员积分加速',\r\n          color: '#C0C0C0',\r\n          icon: '/static/images/points-silver.svg',\r\n          multiplier: '1.2',\r\n          scope: '全部商品',\r\n          applicableLevels: ['银卡会员'],\r\n          description: '银卡会员购物可获得1.2倍积分奖励'\r\n        },\r\n        {\r\n          id: '2',\r\n          name: '金卡会员积分加速',\r\n          color: '#FFD700',\r\n          icon: '/static/images/points-gold.svg',\r\n          multiplier: '1.5',\r\n          scope: '全部商品',\r\n          applicableLevels: ['金卡会员'],\r\n          description: '金卡会员购物可获得1.5倍积分奖励'\r\n        },\r\n        {\r\n          id: '3',\r\n          name: '钻石会员积分加速',\r\n          color: '#B9F2FF',\r\n          icon: '/static/images/points-diamond.svg',\r\n          multiplier: '2',\r\n          scope: '全部商品',\r\n          applicableLevels: ['钻石会员'],\r\n          description: '钻石会员购物可获得2倍积分奖励'\r\n        }\r\n      ];\r\n    },\r\n    \r\n    // 获取会员等级列表\r\n    fetchMemberLevels() {\r\n      // 模拟数据，实际项目中应从API获取\r\n      this.memberLevels = [\r\n        {\r\n          id: '1',\r\n          name: '普通会员'\r\n        },\r\n        {\r\n          id: '2',\r\n          name: '银卡会员'\r\n        },\r\n        {\r\n          id: '3',\r\n          name: '金卡会员'\r\n        },\r\n        {\r\n          id: '4',\r\n          name: '钻石会员'\r\n        }\r\n      ];\r\n    },\r\n    \r\n    // 显示添加规则弹窗\r\n    showAddRuleModal() {\r\n      this.isEditing = false;\r\n      this.ruleForm = {\r\n        id: '',\r\n        name: '',\r\n        color: '#1E90FF',\r\n        icon: '',\r\n        multiplier: '',\r\n        scope: '',\r\n        applicableLevels: [],\r\n        description: ''\r\n      };\r\n      this.$refs.ruleFormPopup.open();\r\n    },\r\n    \r\n    // 编辑规则\r\n    editRule(rule) {\r\n      this.isEditing = true;\r\n      this.currentRuleId = rule.id;\r\n      this.ruleForm = JSON.parse(JSON.stringify(rule)); // 深拷贝\r\n      this.$refs.ruleFormPopup.open();\r\n    },\r\n    \r\n    // 关闭规则表单弹窗\r\n    closeRuleModal() {\r\n      this.$refs.ruleFormPopup.close();\r\n    },\r\n    \r\n    // 保存规则表单\r\n    saveRuleForm() {\r\n      // 表单验证\r\n      if (!this.ruleForm.name) {\r\n        uni.showToast({\r\n          title: '请输入规则名称',\r\n          icon: 'none'\r\n        });\r\n        return;\r\n      }\r\n      \r\n      if (!this.ruleForm.multiplier) {\r\n        uni.showToast({\r\n          title: '请输入加速倍率',\r\n          icon: 'none'\r\n        });\r\n        return;\r\n      }\r\n      \r\n      if (!this.ruleForm.scope) {\r\n        uni.showToast({\r\n          title: '请选择适用范围',\r\n          icon: 'none'\r\n        });\r\n        return;\r\n      }\r\n      \r\n      if (this.ruleForm.applicableLevels.length === 0) {\r\n        uni.showToast({\r\n          title: '请选择适用等级',\r\n          icon: 'none'\r\n        });\r\n        return;\r\n      }\r\n      \r\n      // 保存数据\r\n      if (this.isEditing) {\r\n        // 编辑现有规则\r\n        const index = this.rules.findIndex(item => item.id === this.currentRuleId);\r\n        if (index !== -1) {\r\n          this.rules.splice(index, 1, JSON.parse(JSON.stringify(this.ruleForm)));\r\n        }\r\n      } else {\r\n        // 添加新规则\r\n        this.ruleForm.id = Date.now().toString(); // 生成临时ID\r\n        this.rules.push(JSON.parse(JSON.stringify(this.ruleForm)));\r\n      }\r\n      \r\n      // 关闭弹窗\r\n      this.closeRuleModal();\r\n      \r\n      // 提示保存成功\r\n      uni.showToast({\r\n        title: this.isEditing ? '规则修改成功' : '规则添加成功'\r\n      });\r\n    },\r\n    \r\n    // 确认删除规则\r\n    confirmDeleteRule(rule) {\r\n      this.currentRuleId = rule.id;\r\n      this.$refs.deleteConfirmPopup.open();\r\n    },\r\n    \r\n    // 删除规则\r\n    deleteRule() {\r\n      const index = this.rules.findIndex(item => item.id === this.currentRuleId);\r\n      if (index !== -1) {\r\n        this.rules.splice(index, 1);\r\n      }\r\n      \r\n      this.$refs.deleteConfirmPopup.close();\r\n      \r\n      uni.showToast({\r\n        title: '规则删除成功'\r\n      });\r\n    },\r\n    \r\n    // 关闭删除确认弹窗\r\n    closeDeleteConfirm() {\r\n      this.$refs.deleteConfirmPopup.close();\r\n    },\r\n    \r\n    // 选择图标\r\n    chooseIcon() {\r\n      uni.chooseImage({\r\n        count: 1,\r\n        success: (res) => {\r\n          this.ruleForm.icon = res.tempFilePaths[0];\r\n        }\r\n      });\r\n    },\r\n    \r\n    // 选择范围变更\r\n    onScopeChange(e) {\r\n      const index = e.detail.value;\r\n      this.ruleForm.scope = this.ruleScopes[index];\r\n    },\r\n    \r\n    // 判断等级是否被选中\r\n    isLevelSelected(levelName) {\r\n      return this.ruleForm.applicableLevels.includes(levelName);\r\n    },\r\n    \r\n    // 切换等级选择\r\n    toggleLevelSelection(levelName) {\r\n      const index = this.ruleForm.applicableLevels.indexOf(levelName);\r\n      if (index === -1) {\r\n        this.ruleForm.applicableLevels.push(levelName);\r\n      } else {\r\n        this.ruleForm.applicableLevels.splice(index, 1);\r\n      }\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.points-acceleration-container {\r\n  padding: 20rpx;\r\n  background-color: #f5f5f5;\r\n  min-height: 100vh;\r\n}\r\n\r\n.page-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 20rpx;\r\n}\r\n\r\n.title-section {\r\n  .page-title {\r\n    font-size: 36rpx;\r\n    font-weight: bold;\r\n    color: #333;\r\n  }\r\n  \r\n  .page-subtitle {\r\n    font-size: 24rpx;\r\n    color: #666;\r\n    margin-top: 6rpx;\r\n  }\r\n}\r\n\r\n.add-rule-btn {\r\n  display: flex;\r\n  align-items: center;\r\n  background-color: #4A00E0;\r\n  color: #fff;\r\n  padding: 12rpx 24rpx;\r\n  border-radius: 8rpx;\r\n  \r\n  .btn-text {\r\n    font-size: 28rpx;\r\n  }\r\n  \r\n  .btn-icon {\r\n    font-size: 32rpx;\r\n    margin-left: 8rpx;\r\n  }\r\n}\r\n\r\n.info-card {\r\n  background-color: #fff;\r\n  border-radius: 12rpx;\r\n  margin-bottom: 20rpx;\r\n  overflow: hidden;\r\n  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);\r\n  \r\n  .info-header {\r\n    padding: 20rpx 24rpx;\r\n    border-bottom: 1rpx solid #f0f0f0;\r\n    \r\n    .info-title {\r\n      font-size: 30rpx;\r\n      font-weight: bold;\r\n      color: #333;\r\n    }\r\n  }\r\n  \r\n  .info-body {\r\n    padding: 20rpx 24rpx;\r\n    \r\n    .info-text {\r\n      font-size: 28rpx;\r\n      color: #666;\r\n      line-height: 1.6;\r\n    }\r\n  }\r\n}\r\n\r\n.rules-list {\r\n  .empty-tip {\r\n    display: flex;\r\n    flex-direction: column;\r\n    align-items: center;\r\n    justify-content: center;\r\n    padding: 100rpx 0;\r\n    background-color: #fff;\r\n    border-radius: 12rpx;\r\n    \r\n    .empty-icon {\r\n      width: 160rpx;\r\n      height: 160rpx;\r\n      margin-bottom: 20rpx;\r\n    }\r\n    \r\n    .empty-text {\r\n      font-size: 28rpx;\r\n      color: #999;\r\n    }\r\n  }\r\n}\r\n\r\n.rule-cards {\r\n  .rule-card {\r\n    background-color: #fff;\r\n    border-radius: 12rpx;\r\n    margin-bottom: 20rpx;\r\n    overflow: hidden;\r\n    box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);\r\n    \r\n    .rule-card-header {\r\n      display: flex;\r\n      justify-content: space-between;\r\n      align-items: center;\r\n      padding: 20rpx 24rpx;\r\n      color: #fff;\r\n      \r\n      .rule-name {\r\n        font-size: 32rpx;\r\n        font-weight: bold;\r\n      }\r\n      \r\n      .rule-actions {\r\n        display: flex;\r\n        \r\n        .action-btn {\r\n          font-size: 24rpx;\r\n          padding: 6rpx 16rpx;\r\n          border-radius: 30rpx;\r\n          margin-left: 16rpx;\r\n          \r\n          &.edit {\r\n            background-color: rgba(255, 255, 255, 0.3);\r\n          }\r\n          \r\n          &.delete {\r\n            background-color: rgba(255, 255, 255, 0.3);\r\n          }\r\n        }\r\n      }\r\n    }\r\n    \r\n    .rule-card-body {\r\n      padding: 24rpx;\r\n      \r\n      .rule-info-item {\r\n        display: flex;\r\n        margin-bottom: 16rpx;\r\n        \r\n        .info-label {\r\n          width: 160rpx;\r\n          font-size: 28rpx;\r\n          color: #666;\r\n        }\r\n        \r\n        .info-value {\r\n          flex: 1;\r\n          font-size: 28rpx;\r\n          color: #333;\r\n        }\r\n        \r\n        .rule-icon {\r\n          width: 60rpx;\r\n          height: 60rpx;\r\n        }\r\n        \r\n        .level-tags {\r\n          display: flex;\r\n          flex-wrap: wrap;\r\n          \r\n          .level-tag {\r\n            font-size: 24rpx;\r\n            color: #4A00E0;\r\n            background-color: rgba(74, 0, 224, 0.1);\r\n            padding: 6rpx 16rpx;\r\n            border-radius: 6rpx;\r\n            margin-right: 12rpx;\r\n            margin-bottom: 12rpx;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.rule-form-popup {\r\n  width: 650rpx;\r\n  background-color: #fff;\r\n  border-radius: 12rpx;\r\n  overflow: hidden;\r\n  \r\n  .popup-header {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    padding: 24rpx;\r\n    border-bottom: 1rpx solid #eee;\r\n    \r\n    .popup-title {\r\n      font-size: 32rpx;\r\n      font-weight: bold;\r\n      color: #333;\r\n    }\r\n    \r\n    .popup-close {\r\n      font-size: 40rpx;\r\n      color: #999;\r\n    }\r\n  }\r\n  \r\n  .popup-body {\r\n    padding: 24rpx;\r\n    max-height: 60vh;\r\n    overflow-y: auto;\r\n    \r\n    .form-item {\r\n      margin-bottom: 24rpx;\r\n      \r\n      .form-label {\r\n        display: block;\r\n        font-size: 28rpx;\r\n        color: #333;\r\n        margin-bottom: 12rpx;\r\n      }\r\n      \r\n      .form-input {\r\n        width: 100%;\r\n        height: 80rpx;\r\n        border: 1rpx solid #ddd;\r\n        border-radius: 8rpx;\r\n        padding: 0 20rpx;\r\n        font-size: 28rpx;\r\n        box-sizing: border-box;\r\n      }\r\n      \r\n      .form-input-group {\r\n        display: flex;\r\n        align-items: center;\r\n        \r\n        .form-input {\r\n          flex: 1;\r\n        }\r\n        \r\n        .input-suffix {\r\n          margin-left: 10rpx;\r\n          font-size: 28rpx;\r\n          color: #333;\r\n        }\r\n      }\r\n      \r\n      .form-textarea {\r\n        width: 100%;\r\n        height: 160rpx;\r\n        border: 1rpx solid #ddd;\r\n        border-radius: 8rpx;\r\n        padding: 20rpx;\r\n        font-size: 28rpx;\r\n        box-sizing: border-box;\r\n      }\r\n      \r\n      .form-picker {\r\n        width: 100%;\r\n        height: 80rpx;\r\n        border: 1rpx solid #ddd;\r\n        border-radius: 8rpx;\r\n        padding: 0 20rpx;\r\n        font-size: 28rpx;\r\n        box-sizing: border-box;\r\n        display: flex;\r\n        align-items: center;\r\n        \r\n        .picker-value {\r\n          color: #333;\r\n        }\r\n      }\r\n      \r\n      .color-picker {\r\n        display: flex;\r\n        flex-wrap: wrap;\r\n        \r\n        .color-option {\r\n          width: 60rpx;\r\n          height: 60rpx;\r\n          border-radius: 50%;\r\n          margin-right: 20rpx;\r\n          margin-bottom: 20rpx;\r\n          position: relative;\r\n          \r\n          &.active::after {\r\n            content: '✓';\r\n            position: absolute;\r\n            top: 50%;\r\n            left: 50%;\r\n            transform: translate(-50%, -50%);\r\n            color: #fff;\r\n            font-size: 32rpx;\r\n          }\r\n        }\r\n      }\r\n      \r\n      .icon-upload {\r\n        .preview-icon {\r\n          width: 100rpx;\r\n          height: 100rpx;\r\n          border-radius: 8rpx;\r\n        }\r\n        \r\n        .upload-btn {\r\n          width: 100rpx;\r\n          height: 100rpx;\r\n          border: 1rpx dashed #ddd;\r\n          border-radius: 8rpx;\r\n          display: flex;\r\n          flex-direction: column;\r\n          align-items: center;\r\n          justify-content: center;\r\n          \r\n          .upload-icon {\r\n            font-size: 40rpx;\r\n            color: #999;\r\n            margin-bottom: 4rpx;\r\n          }\r\n          \r\n          .upload-text {\r\n            font-size: 20rpx;\r\n            color: #999;\r\n          }\r\n        }\r\n      }\r\n      \r\n      .level-checkboxes {\r\n        display: flex;\r\n        flex-wrap: wrap;\r\n        \r\n        .level-checkbox {\r\n          display: flex;\r\n          align-items: center;\r\n          margin-right: 30rpx;\r\n          margin-bottom: 20rpx;\r\n          \r\n          .checkbox-icon {\r\n            width: 40rpx;\r\n            height: 40rpx;\r\n            border: 1rpx solid #ddd;\r\n            border-radius: 8rpx;\r\n            display: flex;\r\n            align-items: center;\r\n            justify-content: center;\r\n            margin-right: 10rpx;\r\n            color: #fff;\r\n            font-size: 24rpx;\r\n          }\r\n          \r\n          &.active .checkbox-icon {\r\n            background-color: #4A00E0;\r\n            border-color: #4A00E0;\r\n          }\r\n          \r\n          .checkbox-label {\r\n            font-size: 28rpx;\r\n            color: #333;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n  \r\n  .popup-footer {\r\n    display: flex;\r\n    border-top: 1rpx solid #eee;\r\n    \r\n    button {\r\n      flex: 1;\r\n      height: 90rpx;\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: center;\r\n      font-size: 32rpx;\r\n      border-radius: 0;\r\n      \r\n      &.cancel-btn {\r\n        background-color: #f5f5f5;\r\n        color: #666;\r\n      }\r\n      \r\n      &.confirm-btn {\r\n        background-color: #4A00E0;\r\n        color: #fff;\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n<!-- 积分加速页面结束 --> ", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/subPackages/merchant-admin-marketing/pages/marketing/member/points-acceleration.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni"], "mappings": ";;;AAqKA,MAAK,YAAU;AAAA,EACb,OAAO;AACL,WAAO;AAAA,MACL,OAAO,CAAE;AAAA;AAAA,MACT,UAAU;AAAA,QACR,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,OAAO;AAAA;AAAA,QACP,MAAM;AAAA,QACN,YAAY;AAAA,QACZ,OAAO;AAAA,QACP,kBAAkB,CAAE;AAAA,QACpB,aAAa;AAAA,MACd;AAAA,MACD,WAAW;AAAA;AAAA,MACX,eAAe;AAAA;AAAA,MACf,cAAc;AAAA,QACZ;AAAA;AAAA,QACA;AAAA;AAAA,QACA;AAAA;AAAA,QACA;AAAA;AAAA,QACA;AAAA;AAAA,QACA;AAAA;AAAA,QACA;AAAA;AAAA,QACA;AAAA;AAAA,MACD;AAAA,MACD,YAAY;AAAA,QACV;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACD;AAAA,MACD,cAAc;;;EAEjB;AAAA,EACD,SAAS;AACP,SAAK,WAAU;AACf,SAAK,kBAAiB;AAAA,EACvB;AAAA,EACD,SAAS;AAAA;AAAA,IAEP,aAAa;AAEX,WAAK,QAAQ;AAAA,QACX;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,OAAO;AAAA,UACP,MAAM;AAAA,UACN,YAAY;AAAA,UACZ,OAAO;AAAA,UACP,kBAAkB,CAAC,MAAM;AAAA,UACzB,aAAa;AAAA,QACd;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,OAAO;AAAA,UACP,MAAM;AAAA,UACN,YAAY;AAAA,UACZ,OAAO;AAAA,UACP,kBAAkB,CAAC,MAAM;AAAA,UACzB,aAAa;AAAA,QACd;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,OAAO;AAAA,UACP,MAAM;AAAA,UACN,YAAY;AAAA,UACZ,OAAO;AAAA,UACP,kBAAkB,CAAC,MAAM;AAAA,UACzB,aAAa;AAAA,QACf;AAAA;IAEH;AAAA;AAAA,IAGD,oBAAoB;AAElB,WAAK,eAAe;AAAA,QAClB;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,QACP;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,QACP;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,QACP;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,QACR;AAAA;IAEH;AAAA;AAAA,IAGD,mBAAmB;AACjB,WAAK,YAAY;AACjB,WAAK,WAAW;AAAA,QACd,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,OAAO;AAAA,QACP,MAAM;AAAA,QACN,YAAY;AAAA,QACZ,OAAO;AAAA,QACP,kBAAkB,CAAE;AAAA,QACpB,aAAa;AAAA;AAEf,WAAK,MAAM,cAAc;IAC1B;AAAA;AAAA,IAGD,SAAS,MAAM;AACb,WAAK,YAAY;AACjB,WAAK,gBAAgB,KAAK;AAC1B,WAAK,WAAW,KAAK,MAAM,KAAK,UAAU,IAAI,CAAC;AAC/C,WAAK,MAAM,cAAc;IAC1B;AAAA;AAAA,IAGD,iBAAiB;AACf,WAAK,MAAM,cAAc;IAC1B;AAAA;AAAA,IAGD,eAAe;AAEb,UAAI,CAAC,KAAK,SAAS,MAAM;AACvBA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACR,CAAC;AACD;AAAA,MACF;AAEA,UAAI,CAAC,KAAK,SAAS,YAAY;AAC7BA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACR,CAAC;AACD;AAAA,MACF;AAEA,UAAI,CAAC,KAAK,SAAS,OAAO;AACxBA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACR,CAAC;AACD;AAAA,MACF;AAEA,UAAI,KAAK,SAAS,iBAAiB,WAAW,GAAG;AAC/CA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACR,CAAC;AACD;AAAA,MACF;AAGA,UAAI,KAAK,WAAW;AAElB,cAAM,QAAQ,KAAK,MAAM,UAAU,UAAQ,KAAK,OAAO,KAAK,aAAa;AACzE,YAAI,UAAU,IAAI;AAChB,eAAK,MAAM,OAAO,OAAO,GAAG,KAAK,MAAM,KAAK,UAAU,KAAK,QAAQ,CAAC,CAAC;AAAA,QACvE;AAAA,aACK;AAEL,aAAK,SAAS,KAAK,KAAK,IAAG,EAAG;AAC9B,aAAK,MAAM,KAAK,KAAK,MAAM,KAAK,UAAU,KAAK,QAAQ,CAAC,CAAC;AAAA,MAC3D;AAGA,WAAK,eAAc;AAGnBA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO,KAAK,YAAY,WAAW;AAAA,MACrC,CAAC;AAAA,IACF;AAAA;AAAA,IAGD,kBAAkB,MAAM;AACtB,WAAK,gBAAgB,KAAK;AAC1B,WAAK,MAAM,mBAAmB;IAC/B;AAAA;AAAA,IAGD,aAAa;AACX,YAAM,QAAQ,KAAK,MAAM,UAAU,UAAQ,KAAK,OAAO,KAAK,aAAa;AACzE,UAAI,UAAU,IAAI;AAChB,aAAK,MAAM,OAAO,OAAO,CAAC;AAAA,MAC5B;AAEA,WAAK,MAAM,mBAAmB;AAE9BA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,MACT,CAAC;AAAA,IACF;AAAA;AAAA,IAGD,qBAAqB;AACnB,WAAK,MAAM,mBAAmB;IAC/B;AAAA;AAAA,IAGD,aAAa;AACXA,oBAAAA,MAAI,YAAY;AAAA,QACd,OAAO;AAAA,QACP,SAAS,CAAC,QAAQ;AAChB,eAAK,SAAS,OAAO,IAAI,cAAc,CAAC;AAAA,QAC1C;AAAA,MACF,CAAC;AAAA,IACF;AAAA;AAAA,IAGD,cAAc,GAAG;AACf,YAAM,QAAQ,EAAE,OAAO;AACvB,WAAK,SAAS,QAAQ,KAAK,WAAW,KAAK;AAAA,IAC5C;AAAA;AAAA,IAGD,gBAAgB,WAAW;AACzB,aAAO,KAAK,SAAS,iBAAiB,SAAS,SAAS;AAAA,IACzD;AAAA;AAAA,IAGD,qBAAqB,WAAW;AAC9B,YAAM,QAAQ,KAAK,SAAS,iBAAiB,QAAQ,SAAS;AAC9D,UAAI,UAAU,IAAI;AAChB,aAAK,SAAS,iBAAiB,KAAK,SAAS;AAAA,aACxC;AACL,aAAK,SAAS,iBAAiB,OAAO,OAAO,CAAC;AAAA,MAChD;AAAA,IACF;AAAA,EACF;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACtZA,GAAG,WAAW,eAAe;"}