/**
 * 权限管理工具
 */

// 检查位置权限
export const checkLocationPermission = () => {
  return new Promise((resolve) => {
    uni.getSetting({
      success: (res) => {
        // 检查位置权限状态
        if (res.authSetting['scope.userLocation'] === true) {
          // 已授权
          resolve('authorized');
        } else if (res.authSetting['scope.userLocation'] === false) {
          // 已拒绝
          resolve('denied');
        } else {
          // 未设置过（首次使用）
          resolve('not_determined');
        }
      },
      fail: () => {
        // 获取设置失败，返回未确定状态
        resolve('not_determined');
      }
    });
  });
};

// 请求位置权限
export const requestLocationPermission = () => {
  return new Promise((resolve, reject) => {
    uni.authorize({
      scope: 'scope.userLocation',
      success: () => {
        resolve('authorized');
      },
      fail: (err) => {
        // 用户拒绝授权
        reject({
          errMsg: '用户拒绝位置授权',
          errCode: 'AUTH_DENIED',
          originalError: err
        });
      }
    });
  });
};

// 打开设置页面
export const openSettings = () => {
  return new Promise((resolve, reject) => {
    uni.openSetting({
      success: (res) => {
        resolve(res.authSetting);
      },
      fail: (err) => {
        reject(err);
      }
    });
  });
};

// 检查并请求通知权限
export const checkNotificationPermission = () => {
  return new Promise((resolve, reject) => {
    // #ifdef MP-WEIXIN
    uni.getSetting({
      withSubscriptions: true,
      success: (res) => {
        if (res.authSetting['scope.userNotification']) {
          resolve(true);
        } else {
          uni.authorize({
            scope: 'scope.userNotification',
            success: () => {
              resolve(true);
            },
            fail: (err) => {
              reject({
                errMsg: '用户拒绝授权通知',
                errCode: 'AUTH_FAILED',
                originalError: err
              });
            }
          });
        }
      },
      fail: reject
    });
    // #endif
    
    // #ifdef APP-PLUS || H5
    // App或H5环境下通知逻辑
    resolve(true);
    // #endif
  });
};

// 检查用户信息权限
export const checkUserInfoPermission = () => {
  return new Promise((resolve, reject) => {
    // #ifdef MP-WEIXIN
    uni.getSetting({
      success: (res) => {
        if (res.authSetting['scope.userInfo']) {
          resolve(true);
        } else {
          reject({
            errMsg: '未授权用户信息',
            errCode: 'NO_AUTH'
          });
        }
      },
      fail: reject
    });
    // #endif
    
    // #ifdef APP-PLUS || H5
    // App或H5环境下用户信息逻辑
    resolve(true);
    // #endif
  });
}; 