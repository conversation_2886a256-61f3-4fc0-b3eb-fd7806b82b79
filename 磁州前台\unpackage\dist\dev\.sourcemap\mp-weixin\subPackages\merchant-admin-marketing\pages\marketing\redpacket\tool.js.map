{"version": 3, "file": "tool.js", "sources": ["subPackages/merchant-admin-marketing/pages/marketing/redpacket/tool.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/c3ViUGFja2FnZXNcbWVyY2hhbnQtYWRtaW4tbWFya2V0aW5nXHBhZ2VzXG1hcmtldGluZ1xyZWRwYWNrZXRcdG9vbC52dWU"], "sourcesContent": ["<template>\n  <view class=\"page-container\">\n    <!-- 自定义导航栏 -->\n    <view class=\"navbar\">\n      <view class=\"navbar-back\" @click=\"goBack\">\n        <view class=\"back-icon\"></view>\n      </view>\n      <text class=\"navbar-title\">红包营销工具</text>\n      <view class=\"navbar-right\">\n        <view class=\"settings-icon\" @click=\"navigateTo('/subPackages/merchant-admin-marketing/pages/marketing/redpacket/settings')\">\n          <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\">\n            <circle cx=\"12\" cy=\"12\" r=\"3\"></circle>\n            <path d=\"M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1-1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z\"></path>\n          </svg>\n        </view>\n      </view>\n    </view>\n    \n    <!-- 数据概览 -->\n    <view class=\"overview-section\">\n      <view class=\"overview-header\">\n        <text class=\"section-title\">红包数据概览</text>\n        <view class=\"date-picker\" @click=\"showDatePicker\">\n          <text class=\"date-text\">{{dateRange}}</text>\n          <view class=\"date-icon\">\n            <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\">\n              <rect x=\"3\" y=\"4\" width=\"18\" height=\"18\" rx=\"2\" ry=\"2\"></rect>\n              <line x1=\"16\" y1=\"2\" x2=\"16\" y2=\"6\"></line>\n              <line x1=\"8\" y1=\"2\" x2=\"8\" y2=\"6\"></line>\n              <line x1=\"3\" y1=\"10\" x2=\"21\" y2=\"10\"></line>\n            </svg>\n          </view>\n        </view>\n      </view>\n      \n      <view class=\"stats-cards\">\n        <view class=\"stats-card\">\n          <text class=\"stats-value\">{{stats.totalSent}}</text>\n          <text class=\"stats-label\">发放红包数</text>\n        </view>\n        <view class=\"stats-card\">\n          <text class=\"stats-value\">{{stats.totalAmount}}元</text>\n          <text class=\"stats-label\">发放金额</text>\n        </view>\n        <view class=\"stats-card\">\n          <text class=\"stats-value\">{{stats.totalReceived}}</text>\n          <text class=\"stats-label\">领取红包数</text>\n        </view>\n        <view class=\"stats-card\">\n          <text class=\"stats-value\">{{stats.conversionRate}}%</text>\n          <text class=\"stats-label\">转化率</text>\n        </view>\n      </view>\n      \n      <view class=\"chart-container\">\n        <view class=\"chart-header\">\n          <text class=\"chart-title\">红包领取趋势</text>\n          <view class=\"chart-legend\">\n            <view class=\"legend-item\">\n              <view class=\"legend-color\" style=\"background-color: #FF4D4F;\"></view>\n              <text class=\"legend-text\">发放数量</text>\n            </view>\n            <view class=\"legend-item\">\n              <view class=\"legend-color\" style=\"background-color: #52C41A;\"></view>\n              <text class=\"legend-text\">领取数量</text>\n            </view>\n          </view>\n        </view>\n        <view class=\"chart-placeholder\">\n          <!-- 这里放置实际的图表组件 -->\n          <view class=\"chart-mock\">\n            <view class=\"chart-column\" v-for=\"(item, index) in chartData\" :key=\"index\">\n              <view class=\"column-received\" :style=\"{height: item.received + '%'}\"></view>\n              <view class=\"column-sent\" :style=\"{height: item.sent + '%'}\"></view>\n              <text class=\"column-label\">{{item.date}}</text>\n            </view>\n          </view>\n        </view>\n      </view>\n    </view>\n    \n    <!-- 营销工具 -->\n    <view class=\"tools-section\">\n      <view class=\"section-header\">\n        <text class=\"section-title\">红包营销工具</text>\n      </view>\n      \n      <view class=\"tools-grid\">\n        <view class=\"tool-item\" @click=\"navigateTo('/subPackages/merchant-admin-marketing/pages/marketing/redpacket/create')\">\n          <view class=\"tool-icon\" style=\"background-color: rgba(255, 77, 79, 0.1);\">\n            <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"#FF4D4F\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\">\n              <rect x=\"3\" y=\"3\" width=\"18\" height=\"18\" rx=\"2\" ry=\"2\"></rect>\n              <line x1=\"12\" y1=\"8\" x2=\"12\" y2=\"16\"></line>\n              <line x1=\"8\" y1=\"12\" x2=\"16\" y2=\"12\"></line>\n            </svg>\n          </view>\n          <text class=\"tool-name\">创建红包</text>\n          <text class=\"tool-desc\">快速创建红包活动</text>\n        </view>\n        \n        <view class=\"tool-item\" @click=\"navigateTo('/subPackages/merchant-admin-marketing/pages/marketing/redpacket/mass-sending')\">\n          <view class=\"tool-icon\" style=\"background-color: rgba(24, 144, 255, 0.1);\">\n            <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"#1890FF\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\">\n              <path d=\"M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2\"></path>\n              <circle cx=\"9\" cy=\"7\" r=\"4\"></circle>\n              <path d=\"M23 21v-2a4 4 0 0 0-3-3.87\"></path>\n              <path d=\"M16 3.13a4 4 0 0 1 0 7.75\"></path>\n            </svg>\n          </view>\n          <text class=\"tool-name\">红包群发</text>\n          <text class=\"tool-desc\">批量发送红包</text>\n        </view>\n        \n        <view class=\"tool-item\" @click=\"navigateTo('/subPackages/merchant-admin-marketing/pages/marketing/redpacket/red-rain')\">\n          <view class=\"tool-icon\" style=\"background-color: rgba(250, 173, 20, 0.1);\">\n            <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"#FAAD14\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\">\n              <path d=\"M20 16.2A4.5 4.5 0 0 0 17.5 8h-1.8A7 7 0 1 0 4 14.9\"></path>\n              <path d=\"M16 14v6\"></path>\n              <path d=\"M8 14v6\"></path>\n              <path d=\"M12 16v6\"></path>\n            </svg>\n          </view>\n          <text class=\"tool-name\">红包雨</text>\n          <text class=\"tool-desc\">限时抢红包活动</text>\n        </view>\n        \n        <view class=\"tool-item\" @click=\"navigateTo('/subPackages/merchant-admin-marketing/pages/marketing/redpacket/fission')\">\n          <view class=\"tool-icon\" style=\"background-color: rgba(114, 46, 209, 0.1);\">\n            <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"#722ED1\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\">\n              <circle cx=\"12\" cy=\"12\" r=\"10\"></circle>\n              <path d=\"M8 14s1.5 2 4 2 4-2 4-2\"></path>\n              <line x1=\"9\" y1=\"9\" x2=\"9.01\" y2=\"9\"></line>\n              <line x1=\"15\" y1=\"9\" x2=\"15.01\" y2=\"9\"></line>\n            </svg>\n          </view>\n          <text class=\"tool-name\">裂变红包</text>\n          <text class=\"tool-desc\">分享获取更多红包</text>\n        </view>\n        \n        <view class=\"tool-item\" @click=\"navigateTo('/subPackages/merchant-admin-marketing/pages/marketing/redpacket/template')\">\n          <view class=\"tool-icon\" style=\"background-color: rgba(82, 196, 26, 0.1);\">\n            <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"#52C41A\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\">\n              <path d=\"M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z\"></path>\n              <polyline points=\"14 2 14 8 20 8\"></polyline>\n              <line x1=\"16\" y1=\"13\" x2=\"8\" y2=\"13\"></line>\n              <line x1=\"16\" y1=\"17\" x2=\"8\" y2=\"17\"></line>\n              <polyline points=\"10 9 9 9 8 9\"></polyline>\n            </svg>\n          </view>\n          <text class=\"tool-name\">红包模板</text>\n          <text class=\"tool-desc\">管理红包样式模板</text>\n        </view>\n        \n        <view class=\"tool-item\" @click=\"navigateTo('/subPackages/merchant-admin-marketing/pages/marketing/redpacket/data-analysis')\">\n          <view class=\"tool-icon\" style=\"background-color: rgba(245, 34, 45, 0.1);\">\n            <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"#F5222D\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\">\n              <line x1=\"18\" y1=\"20\" x2=\"18\" y2=\"10\"></line>\n              <line x1=\"12\" y1=\"20\" x2=\"12\" y2=\"4\"></line>\n              <line x1=\"6\" y1=\"20\" x2=\"6\" y2=\"14\"></line>\n            </svg>\n          </view>\n          <text class=\"tool-name\">红包数据</text>\n          <text class=\"tool-desc\">查看红包数据分析</text>\n        </view>\n      </view>\n    </view>\n    \n    <!-- 最近活动 -->\n    <view class=\"recent-section\">\n      <view class=\"section-header\">\n        <text class=\"section-title\">最近活动</text>\n        <view class=\"view-all\" @click=\"navigateTo('/subPackages/merchant-admin-marketing/pages/marketing/redpacket/data-analysis')\">\n          <text class=\"view-all-text\">查看全部</text>\n          <view class=\"arrow-icon\">\n            <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"14\" height=\"14\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\">\n              <polyline points=\"9 18 15 12 9 6\"></polyline>\n            </svg>\n          </view>\n        </view>\n      </view>\n      \n      <view class=\"activity-list\">\n        <view class=\"activity-item\" v-for=\"(item, index) in recentActivities\" :key=\"index\" @click=\"navigateTo('/subPackages/merchant-admin-marketing/pages/marketing/redpacket/detail?id=' + item.id)\">\n          <view class=\"activity-icon\" :style=\"{backgroundColor: item.iconBg}\">\n            <view class=\"icon-inner\" :style=\"{color: item.iconColor}\">{{item.icon}}</view>\n          </view>\n          <view class=\"activity-content\">\n            <view class=\"activity-header\">\n              <text class=\"activity-name\">{{item.name}}</text>\n              <text class=\"activity-status\" :class=\"'status-' + item.status\">{{item.statusText}}</text>\n            </view>\n            <view class=\"activity-info\">\n              <text class=\"activity-time\">{{item.time}}</text>\n              <text class=\"activity-count\">发放: {{item.sentCount}} | 领取: {{item.receivedCount}}</text>\n            </view>\n          </view>\n        </view>\n      </view>\n    </view>\n    \n    <!-- 营销攻略 -->\n    <view class=\"strategy-section\">\n      <view class=\"section-header\">\n        <text class=\"section-title\">营销攻略</text>\n      </view>\n      \n      <view class=\"strategy-list\">\n        <view class=\"strategy-item\" @click=\"navigateTo('/subPackages/merchant-admin-marketing/pages/marketing/redpacket/guide')\">\n          <view class=\"strategy-icon\" style=\"background-color: rgba(255, 77, 79, 0.1);\">\n            <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"#FF4D4F\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\">\n              <circle cx=\"12\" cy=\"12\" r=\"10\"></circle>\n              <line x1=\"12\" y1=\"16\" x2=\"12\" y2=\"12\"></line>\n              <line x1=\"12\" y1=\"8\" x2=\"12.01\" y2=\"8\"></line>\n            </svg>\n          </view>\n          <view class=\"strategy-content\">\n            <text class=\"strategy-title\">红包营销指南</text>\n            <text class=\"strategy-desc\">了解红包营销的基本原理与最佳实践</text>\n          </view>\n          <view class=\"strategy-arrow\"></view>\n        </view>\n        \n        <view class=\"strategy-item\">\n          <view class=\"strategy-icon\" style=\"background-color: rgba(24, 144, 255, 0.1);\">\n            <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"#1890FF\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\">\n              <polygon points=\"12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2\"></polygon>\n            </svg>\n          </view>\n          <view class=\"strategy-content\">\n            <text class=\"strategy-title\">节日红包攻略</text>\n            <text class=\"strategy-desc\">节日期间红包营销的策略与技巧</text>\n          </view>\n          <view class=\"strategy-arrow\"></view>\n        </view>\n        \n        <view class=\"strategy-item\">\n          <view class=\"strategy-icon\" style=\"background-color: rgba(82, 196, 26, 0.1);\">\n            <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"#52C41A\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\">\n              <path d=\"M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2\"></path>\n              <circle cx=\"9\" cy=\"7\" r=\"4\"></circle>\n              <path d=\"M23 21v-2a4 4 0 0 0-3-3.87\"></path>\n              <path d=\"M16 3.13a4 4 0 0 1 0 7.75\"></path>\n            </svg>\n          </view>\n          <view class=\"strategy-content\">\n            <text class=\"strategy-title\">用户裂变秘籍</text>\n            <text class=\"strategy-desc\">如何利用红包实现用户快速增长</text>\n          </view>\n          <view class=\"strategy-arrow\"></view>\n        </view>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script>\nexport default {\n  data() {\n    return {\n      dateRange: '近7天',\n      stats: {\n        totalSent: 1258,\n        totalAmount: 3652.50,\n        totalReceived: 986,\n        conversionRate: 78.4\n      },\n      chartData: [\n        { date: '周一', sent: 60, received: 45 },\n        { date: '周二', sent: 70, received: 55 },\n        { date: '周三', sent: 80, received: 65 },\n        { date: '周四', sent: 90, received: 75 },\n        { date: '周五', sent: 100, received: 85 },\n        { date: '周六', sent: 85, received: 70 },\n        { date: '周日', sent: 75, received: 60 }\n      ],\n      recentActivities: [\n        {\n          id: 1,\n          name: '新人专享红包',\n          status: 'active',\n          statusText: '进行中',\n          time: '2023-06-01 ~ 2023-06-30',\n          sentCount: 356,\n          receivedCount: 289,\n          icon: '新',\n          iconBg: 'rgba(255, 77, 79, 0.1)',\n          iconColor: '#FF4D4F'\n        },\n        {\n          id: 2,\n          name: '618购物节红包雨',\n          status: 'active',\n          statusText: '进行中',\n          time: '2023-06-10 ~ 2023-06-18',\n          sentCount: 520,\n          receivedCount: 412,\n          icon: '雨',\n          iconBg: 'rgba(24, 144, 255, 0.1)',\n          iconColor: '#1890FF'\n        },\n        {\n          id: 3,\n          name: '五一劳动节红包',\n          status: 'ended',\n          statusText: '已结束',\n          time: '2023-05-01 ~ 2023-05-05',\n          sentCount: 382,\n          receivedCount: 285,\n          icon: '节',\n          iconBg: 'rgba(82, 196, 26, 0.1)',\n          iconColor: '#52C41A'\n        }\n      ]\n    }\n  },\n  methods: {\n    goBack() {\n      uni.navigateBack();\n    },\n    showDatePicker() {\n      uni.showActionSheet({\n        itemList: ['今日', '近7天', '近30天', '自定义'],\n        success: (res) => {\n          const options = ['今日', '近7天', '近30天', '自定义'];\n          this.dateRange = options[res.tapIndex];\n          \n          if (res.tapIndex === 3) {\n            // 打开日期选择器\n            uni.showToast({\n              title: '打开日期选择器',\n              icon: 'none'\n            });\n          } else {\n            // 加载对应时间段的数据\n            this.loadData(options[res.tapIndex]);\n          }\n        }\n      });\n    },\n    loadData(timeRange) {\n      // 模拟加载数据\n      uni.showLoading({\n        title: '加载中...'\n      });\n      \n      setTimeout(() => {\n        uni.hideLoading();\n        uni.showToast({\n          title: `已加载${timeRange}数据`,\n          icon: 'success'\n        });\n      }, 500);\n    },\n    navigateTo(url) {\n      uni.navigateTo({\n        url: url\n      });\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.page-container {\n  display: flex;\n  flex-direction: column;\n  min-height: 100vh;\n  background-color: #f5f5f5;\n}\n\n/* 导航栏样式 */\n.navbar {\n  display: flex;\n  align-items: center;\n  height: 44px;\n  background-color: #fff;\n  padding: 0 15px;\n  position: relative;\n}\n\n.navbar-back {\n  width: 24px;\n  height: 24px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.back-icon {\n  width: 12px;\n  height: 12px;\n  border-top: 2px solid #333;\n  border-left: 2px solid #333;\n  transform: rotate(-45deg);\n}\n\n.navbar-title {\n  flex: 1;\n  text-align: center;\n  font-size: 17px;\n  font-weight: 600;\n  color: #333;\n}\n\n.navbar-right {\n  width: 24px;\n  height: 24px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.settings-icon {\n  color: #333;\n}\n\n/* 公共样式 */\n.section-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 15px;\n}\n\n.section-title {\n  font-size: 16px;\n  font-weight: 600;\n  color: #333;\n}\n\n/* 数据概览样式 */\n.overview-section {\n  padding: 15px;\n  background-color: #fff;\n  margin-bottom: 10px;\n}\n\n.date-picker {\n  display: flex;\n  align-items: center;\n  padding: 5px 10px;\n  background-color: #f5f5f5;\n  border-radius: 15px;\n}\n\n.date-text {\n  font-size: 12px;\n  color: #666;\n  margin-right: 5px;\n}\n\n.date-icon {\n  color: #666;\n}\n\n.stats-cards {\n  display: flex;\n  flex-wrap: wrap;\n  margin: 0 -5px;\n}\n\n.stats-card {\n  width: calc(50% - 10px);\n  margin: 5px;\n  background-color: #f9f9f9;\n  border-radius: 8px;\n  padding: 15px;\n  text-align: center;\n}\n\n.stats-value {\n  font-size: 20px;\n  font-weight: 600;\n  color: #333;\n  display: block;\n  margin-bottom: 5px;\n}\n\n.stats-label {\n  font-size: 12px;\n  color: #999;\n}\n\n.chart-container {\n  margin-top: 15px;\n  padding-top: 15px;\n  border-top: 1px solid #f0f0f0;\n}\n\n.chart-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 15px;\n}\n\n.chart-title {\n  font-size: 14px;\n  font-weight: 600;\n  color: #333;\n}\n\n.chart-legend {\n  display: flex;\n}\n\n.legend-item {\n  display: flex;\n  align-items: center;\n  margin-left: 10px;\n}\n\n.legend-color {\n  width: 8px;\n  height: 8px;\n  border-radius: 4px;\n  margin-right: 5px;\n}\n\n.legend-text {\n  font-size: 12px;\n  color: #999;\n}\n\n.chart-placeholder {\n  height: 160px;\n  position: relative;\n}\n\n.chart-mock {\n  position: absolute;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  top: 0;\n  display: flex;\n  justify-content: space-between;\n  align-items: flex-end;\n}\n\n.chart-column {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  height: 100%;\n  position: relative;\n}\n\n.column-sent {\n  width: 6px;\n  background-color: rgba(255, 77, 79, 0.7);\n  border-radius: 3px 3px 0 0;\n  margin-bottom: 2px;\n}\n\n.column-received {\n  width: 6px;\n  background-color: rgba(82, 196, 26, 0.7);\n  border-radius: 3px 3px 0 0;\n}\n\n.column-label {\n  position: absolute;\n  bottom: -20px;\n  font-size: 10px;\n  color: #999;\n}\n\n/* 营销工具样式 */\n.tools-section {\n  padding: 15px;\n  background-color: #fff;\n  margin-bottom: 10px;\n}\n\n.tools-grid {\n  display: flex;\n  flex-wrap: wrap;\n  margin: 0 -5px;\n}\n\n.tool-item {\n  width: calc(33.33% - 10px);\n  margin: 5px;\n  padding: 15px 10px;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  background-color: #fff;\n  border-radius: 8px;\n  box-shadow: 0 2px 8px rgba(0,0,0,0.05);\n}\n\n.tool-icon {\n  width: 48px;\n  height: 48px;\n  border-radius: 24px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-bottom: 10px;\n}\n\n.tool-name {\n  font-size: 14px;\n  font-weight: 600;\n  color: #333;\n  margin-bottom: 5px;\n}\n\n.tool-desc {\n  font-size: 12px;\n  color: #999;\n  text-align: center;\n}\n\n/* 最近活动样式 */\n.recent-section {\n  padding: 15px;\n  background-color: #fff;\n  margin-bottom: 10px;\n}\n\n.view-all {\n  display: flex;\n  align-items: center;\n}\n\n.view-all-text {\n  font-size: 12px;\n  color: #999;\n  margin-right: 5px;\n}\n\n.arrow-icon {\n  color: #999;\n}\n\n.activity-list {\n  margin: 0 -15px;\n}\n\n.activity-item {\n  display: flex;\n  align-items: center;\n  padding: 15px;\n  border-bottom: 1px solid #f0f0f0;\n}\n\n.activity-item:last-child {\n  border-bottom: none;\n}\n\n.activity-icon {\n  width: 40px;\n  height: 40px;\n  border-radius: 20px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-right: 15px;\n}\n\n.icon-inner {\n  font-size: 14px;\n  font-weight: 600;\n}\n\n.activity-content {\n  flex: 1;\n}\n\n.activity-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 5px;\n}\n\n.activity-name {\n  font-size: 15px;\n  font-weight: 600;\n  color: #333;\n}\n\n.activity-status {\n  font-size: 12px;\n  padding: 2px 6px;\n  border-radius: 10px;\n}\n\n.status-active {\n  background-color: rgba(82, 196, 26, 0.1);\n  color: #52C41A;\n}\n\n.status-ended {\n  background-color: rgba(153, 153, 153, 0.1);\n  color: #999;\n}\n\n.activity-info {\n  display: flex;\n  justify-content: space-between;\n}\n\n.activity-time, .activity-count {\n  font-size: 12px;\n  color: #999;\n}\n\n/* 营销攻略样式 */\n.strategy-section {\n  padding: 15px;\n  background-color: #fff;\n  margin-bottom: 20px;\n}\n\n.strategy-list {\n  margin: 0 -15px;\n}\n\n.strategy-item {\n  display: flex;\n  align-items: center;\n  padding: 15px;\n  border-bottom: 1px solid #f0f0f0;\n}\n\n.strategy-item:last-child {\n  border-bottom: none;\n}\n\n.strategy-icon {\n  width: 40px;\n  height: 40px;\n  border-radius: 20px;\n  margin-right: 10px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.strategy-content {\n  flex: 1;\n}\n\n.strategy-title {\n  font-size: 14px;\n  font-weight: 600;\n  color: #333;\n  margin-bottom: 5px;\n}\n\n.strategy-desc {\n  font-size: 12px;\n  color: #666;\n}\n\n.strategy-arrow {\n  width: 12px;\n  height: 12px;\n  border-top: 1px solid #ccc;\n  border-right: 1px solid #ccc;\n  transform: rotate(45deg);\n}\n</style>", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/subPackages/merchant-admin-marketing/pages/marketing/redpacket/tool.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni"], "mappings": ";;AAgQA,MAAK,YAAU;AAAA,EACb,OAAO;AACL,WAAO;AAAA,MACL,WAAW;AAAA,MACX,OAAO;AAAA,QACL,WAAW;AAAA,QACX,aAAa;AAAA,QACb,eAAe;AAAA,QACf,gBAAgB;AAAA,MACjB;AAAA,MACD,WAAW;AAAA,QACT,EAAE,MAAM,MAAM,MAAM,IAAI,UAAU,GAAI;AAAA,QACtC,EAAE,MAAM,MAAM,MAAM,IAAI,UAAU,GAAI;AAAA,QACtC,EAAE,MAAM,MAAM,MAAM,IAAI,UAAU,GAAI;AAAA,QACtC,EAAE,MAAM,MAAM,MAAM,IAAI,UAAU,GAAI;AAAA,QACtC,EAAE,MAAM,MAAM,MAAM,KAAK,UAAU,GAAI;AAAA,QACvC,EAAE,MAAM,MAAM,MAAM,IAAI,UAAU,GAAI;AAAA,QACtC,EAAE,MAAM,MAAM,MAAM,IAAI,UAAU,GAAG;AAAA,MACtC;AAAA,MACD,kBAAkB;AAAA,QAChB;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,QAAQ;AAAA,UACR,YAAY;AAAA,UACZ,MAAM;AAAA,UACN,WAAW;AAAA,UACX,eAAe;AAAA,UACf,MAAM;AAAA,UACN,QAAQ;AAAA,UACR,WAAW;AAAA,QACZ;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,QAAQ;AAAA,UACR,YAAY;AAAA,UACZ,MAAM;AAAA,UACN,WAAW;AAAA,UACX,eAAe;AAAA,UACf,MAAM;AAAA,UACN,QAAQ;AAAA,UACR,WAAW;AAAA,QACZ;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,QAAQ;AAAA,UACR,YAAY;AAAA,UACZ,MAAM;AAAA,UACN,WAAW;AAAA,UACX,eAAe;AAAA,UACf,MAAM;AAAA,UACN,QAAQ;AAAA,UACR,WAAW;AAAA,QACb;AAAA,MACF;AAAA,IACF;AAAA,EACD;AAAA,EACD,SAAS;AAAA,IACP,SAAS;AACPA,oBAAG,MAAC,aAAY;AAAA,IACjB;AAAA,IACD,iBAAiB;AACfA,oBAAAA,MAAI,gBAAgB;AAAA,QAClB,UAAU,CAAC,MAAM,OAAO,QAAQ,KAAK;AAAA,QACrC,SAAS,CAAC,QAAQ;AAChB,gBAAM,UAAU,CAAC,MAAM,OAAO,QAAQ,KAAK;AAC3C,eAAK,YAAY,QAAQ,IAAI,QAAQ;AAErC,cAAI,IAAI,aAAa,GAAG;AAEtBA,0BAAAA,MAAI,UAAU;AAAA,cACZ,OAAO;AAAA,cACP,MAAM;AAAA,YACR,CAAC;AAAA,iBACI;AAEL,iBAAK,SAAS,QAAQ,IAAI,QAAQ,CAAC;AAAA,UACrC;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACF;AAAA,IACD,SAAS,WAAW;AAElBA,oBAAAA,MAAI,YAAY;AAAA,QACd,OAAO;AAAA,MACT,CAAC;AAED,iBAAW,MAAM;AACfA,sBAAG,MAAC,YAAW;AACfA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO,MAAM,SAAS;AAAA,UACtB,MAAM;AAAA,QACR,CAAC;AAAA,MACF,GAAE,GAAG;AAAA,IACP;AAAA,IACD,WAAW,KAAK;AACdA,oBAAAA,MAAI,WAAW;AAAA,QACb;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACtWA,GAAG,WAAW,eAAe;"}