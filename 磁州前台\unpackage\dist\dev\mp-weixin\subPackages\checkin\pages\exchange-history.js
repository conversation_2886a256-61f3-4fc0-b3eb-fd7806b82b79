"use strict";
const common_vendor = require("../../../common/vendor.js");
const common_assets = require("../../../common/assets.js");
const _sfc_main = {
  __name: "exchange-history",
  setup(__props) {
    const statusBarHeight = common_vendor.ref(20);
    const currentTab = common_vendor.ref(0);
    const isRefreshing = common_vendor.ref(false);
    const showDetailPopup = common_vendor.ref(false);
    const selectedRecord = common_vendor.ref({});
    const tabs = ["全部", "待发货", "已发货", "使用中"];
    const statusText = {
      "checking": "审核中",
      "shipping": "待发货",
      "shipped": "已发货",
      "using": "使用中",
      "used": "已使用",
      "expired": "已过期"
    };
    const records = common_vendor.ref([
      {
        id: 1,
        name: "5元通用券",
        points: 100,
        time: "2023-11-25 14:30",
        status: "using",
        image: "/static/images/banner/coupon-1.png",
        orderNo: "E202311250001",
        validity: "2023-12-25前有效",
        type: "coupon"
      },
      {
        id: 2,
        name: "精美保温杯",
        points: 1200,
        time: "2023-11-22 09:15",
        status: "shipping",
        image: "/static/images/banner/product-1.png",
        orderNo: "E202311220003",
        address: "河北省邯郸市肥乡区北尹堡村1号",
        type: "product"
      },
      {
        id: 3,
        name: "10元外卖券",
        points: 180,
        time: "2023-11-20 18:45",
        status: "used",
        image: "/static/images/banner/coupon-2.png",
        orderNo: "E202311200007",
        type: "coupon"
      },
      {
        id: 4,
        name: "会员月卡",
        points: 500,
        time: "2023-11-15 10:22",
        status: "expired",
        image: "/static/images/banner/vip-1.png",
        orderNo: "E202311150012",
        type: "vip"
      },
      {
        id: 5,
        name: "限时抢购券",
        points: 250,
        time: "2023-11-10 16:30",
        status: "shipped",
        image: "/static/images/banner/coupon-3.png",
        orderNo: "E202311100025",
        logistics: "顺丰速运 SF1234567890",
        address: "河北省邯郸市肥乡区北尹堡村1号",
        type: "coupon"
      }
    ]);
    const filteredRecords = common_vendor.computed(() => {
      if (currentTab.value === 0) {
        return records.value;
      } else if (currentTab.value === 1) {
        return records.value.filter((record) => record.status === "shipping");
      } else if (currentTab.value === 2) {
        return records.value.filter((record) => record.status === "shipped");
      } else {
        return records.value.filter((record) => record.status === "using");
      }
    });
    common_vendor.onMounted(() => {
      const sysInfo = common_vendor.index.getSystemInfoSync();
      statusBarHeight.value = sysInfo.statusBarHeight || 20;
    });
    function switchTab(index) {
      currentTab.value = index;
    }
    function refreshRecords() {
      isRefreshing.value = true;
      setTimeout(() => {
        isRefreshing.value = false;
        common_vendor.index.showToast({
          title: "刷新成功",
          icon: "none"
        });
      }, 1500);
    }
    function showRecordDetail(record) {
      selectedRecord.value = { ...record };
      showDetailPopup.value = true;
    }
    function closeDetailPopup() {
      showDetailPopup.value = false;
    }
    function useProduct() {
      if (selectedRecord.value.type === "coupon") {
        common_vendor.index.showToast({
          title: "优惠券已复制到剪贴板",
          icon: "none"
        });
      } else if (selectedRecord.value.type === "vip") {
        common_vendor.index.showToast({
          title: "VIP权益已激活",
          icon: "success"
        });
      } else {
        common_vendor.index.showToast({
          title: "即将跳转到使用页面",
          icon: "none"
        });
      }
      closeDetailPopup();
    }
    function trackOrder() {
      common_vendor.index.showToast({
        title: "正在开发中，敬请期待",
        icon: "none"
      });
    }
    function confirmReceive() {
      common_vendor.index.showModal({
        title: "确认收货",
        content: "确认已收到商品？确认后无法撤销",
        success: (res) => {
          if (res.confirm) {
            const index = records.value.findIndex((item) => item.id === selectedRecord.value.id);
            if (index !== -1) {
              records.value[index].status = "using";
              selectedRecord.value.status = "using";
              common_vendor.index.showToast({
                title: "确认收货成功",
                icon: "success"
              });
            }
          }
        }
      });
    }
    function goBack() {
      common_vendor.index.navigateBack();
    }
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: statusBarHeight.value + 44 + "px",
        b: common_assets._imports_0$7,
        c: common_vendor.o(goBack),
        d: statusBarHeight.value + "px",
        e: common_vendor.f(tabs, (tab, index, i0) => {
          return {
            a: common_vendor.t(tab),
            b: currentTab.value === index ? 1 : "",
            c: index,
            d: common_vendor.o(($event) => switchTab(index), index)
          };
        }),
        f: filteredRecords.value.length > 0
      }, filteredRecords.value.length > 0 ? {
        g: common_vendor.f(filteredRecords.value, (record, index, i0) => {
          return {
            a: record.image,
            b: common_vendor.t(record.name),
            c: common_vendor.t(record.time),
            d: common_vendor.t(statusText[record.status]),
            e: common_vendor.n("status-" + record.status),
            f: common_vendor.t(record.points),
            g: index,
            h: common_vendor.o(($event) => showRecordDetail(record), index)
          };
        })
      } : {
        h: common_assets._imports_1$45
      }, {
        i: common_vendor.o(refreshRecords),
        j: isRefreshing.value,
        k: showDetailPopup.value
      }, showDetailPopup.value ? common_vendor.e({
        l: common_vendor.o(closeDetailPopup),
        m: selectedRecord.value.image,
        n: common_vendor.t(selectedRecord.value.name),
        o: common_vendor.t(selectedRecord.value.points),
        p: common_vendor.t(selectedRecord.value.fullTime || selectedRecord.value.time),
        q: common_vendor.t(selectedRecord.value.orderNo),
        r: common_vendor.t(statusText[selectedRecord.value.status]),
        s: common_vendor.n("status-" + selectedRecord.value.status),
        t: selectedRecord.value.status === "using"
      }, selectedRecord.value.status === "using" ? {
        v: common_vendor.t(selectedRecord.value.validity)
      } : {}, {
        w: selectedRecord.value.status === "shipping" || selectedRecord.value.status === "shipped"
      }, selectedRecord.value.status === "shipping" || selectedRecord.value.status === "shipped" ? {
        x: common_vendor.t(selectedRecord.value.address)
      } : {}, {
        y: selectedRecord.value.status === "shipped"
      }, selectedRecord.value.status === "shipped" ? {
        z: common_vendor.t(selectedRecord.value.logistics)
      } : {}, {
        A: selectedRecord.value.status === "using"
      }, selectedRecord.value.status === "using" ? {
        B: common_vendor.o(useProduct)
      } : {}, {
        C: selectedRecord.value.status === "shipping"
      }, selectedRecord.value.status === "shipping" ? {
        D: common_vendor.o(trackOrder)
      } : {}, {
        E: selectedRecord.value.status === "shipped"
      }, selectedRecord.value.status === "shipped" ? {
        F: common_vendor.o(confirmReceive)
      } : {}, {
        G: common_vendor.o(closeDetailPopup)
      }) : {}, {
        H: statusBarHeight.value + 44 + "px"
      });
    };
  }
};
wx.createPage(_sfc_main);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/subPackages/checkin/pages/exchange-history.js.map
