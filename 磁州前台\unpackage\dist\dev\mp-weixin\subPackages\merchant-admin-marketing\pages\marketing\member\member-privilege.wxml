<view class="member-privilege-container data-v-99eee3fc"><view class="page-header data-v-99eee3fc"><view class="title-section data-v-99eee3fc"><text class="page-title data-v-99eee3fc">会员特权</text><text class="page-subtitle data-v-99eee3fc">管理店铺会员专属特权</text></view><view class="add-privilege-btn data-v-99eee3fc" bindtap="{{a}}"><text class="btn-text data-v-99eee3fc">添加特权</text><text class="btn-icon data-v-99eee3fc">+</text></view></view><view class="privilege-shortcuts data-v-99eee3fc"><view class="shortcut-item data-v-99eee3fc" bindtap="{{b}}"><view class="shortcut-icon data-v-99eee3fc" style="background-color:#FF6B22"><text class="icon-text data-v-99eee3fc">折</text></view><text class="shortcut-name data-v-99eee3fc">会员折扣</text></view><view class="shortcut-item data-v-99eee3fc" bindtap="{{c}}"><view class="shortcut-icon data-v-99eee3fc" style="background-color:#1E90FF"><text class="icon-text data-v-99eee3fc">积</text></view><text class="shortcut-name data-v-99eee3fc">积分加速</text></view><view class="shortcut-item data-v-99eee3fc" bindtap="{{d}}"><view class="shortcut-icon data-v-99eee3fc" style="background-color:#32CD32"><text class="icon-text data-v-99eee3fc">送</text></view><text class="shortcut-name data-v-99eee3fc">免费配送</text></view><view class="shortcut-item data-v-99eee3fc" bindtap="{{e}}"><view class="shortcut-icon data-v-99eee3fc" style="background-color:#FF69B4"><text class="icon-text data-v-99eee3fc">礼</text></view><text class="shortcut-name data-v-99eee3fc">生日礼包</text></view><view class="shortcut-item data-v-99eee3fc" bindtap="{{f}}"><view class="shortcut-icon data-v-99eee3fc" style="background-color:#8A2BE2"><text class="icon-text data-v-99eee3fc">客</text></view><text class="shortcut-name data-v-99eee3fc">专属客服</text></view></view><view class="privilege-list data-v-99eee3fc"><view wx:if="{{g}}" class="empty-tip data-v-99eee3fc"><image class="empty-icon data-v-99eee3fc" src="{{h}}"></image><text class="empty-text data-v-99eee3fc">暂无会员特权，点击"添加特权"创建</text></view><view wx:else class="privilege-cards data-v-99eee3fc"><view wx:for="{{i}}" wx:for-item="privilege" wx:key="j" class="privilege-card data-v-99eee3fc"><view class="privilege-card-header data-v-99eee3fc" style="{{'background-color:' + privilege.d}}"><view class="privilege-name data-v-99eee3fc">{{privilege.a}}</view><view class="privilege-actions data-v-99eee3fc"><text class="action-btn edit data-v-99eee3fc" bindtap="{{privilege.b}}">编辑</text><text class="action-btn delete data-v-99eee3fc" bindtap="{{privilege.c}}">删除</text></view></view><view class="privilege-card-body data-v-99eee3fc"><view class="privilege-info-item data-v-99eee3fc"><text class="info-label data-v-99eee3fc">特权图标：</text><image class="privilege-icon data-v-99eee3fc" src="{{privilege.e}}" mode="aspectFit"></image></view><view class="privilege-info-item data-v-99eee3fc"><text class="info-label data-v-99eee3fc">适用等级：</text><view class="level-tags data-v-99eee3fc"><text wx:for="{{privilege.f}}" wx:for-item="level" wx:key="b" class="level-tag data-v-99eee3fc">{{level.a}}</text></view></view><view class="privilege-info-item data-v-99eee3fc"><text class="info-label data-v-99eee3fc">特权类型：</text><text class="info-value data-v-99eee3fc">{{privilege.g}}</text></view><view class="privilege-info-item data-v-99eee3fc"><text class="info-label data-v-99eee3fc">特权价值：</text><text class="info-value data-v-99eee3fc">{{privilege.h}}</text></view><view class="privilege-info-item data-v-99eee3fc"><text class="info-label data-v-99eee3fc">特权说明：</text><text class="info-value data-v-99eee3fc">{{privilege.i}}</text></view></view></view></view></view><uni-popup wx:if="{{D}}" class="r data-v-99eee3fc" u-s="{{['d']}}" u-r="privilegeFormPopup" u-i="99eee3fc-0" bind:__l="__l" u-p="{{D}}"><view class="privilege-form-popup data-v-99eee3fc"><view class="popup-header data-v-99eee3fc"><text class="popup-title data-v-99eee3fc">{{j}}</text><text class="popup-close data-v-99eee3fc" bindtap="{{k}}">×</text></view><view class="popup-body data-v-99eee3fc"><view class="form-item data-v-99eee3fc"><text class="form-label data-v-99eee3fc">特权名称</text><input class="form-input data-v-99eee3fc" placeholder="请输入特权名称" value="{{l}}" bindinput="{{m}}"/></view><view class="form-item data-v-99eee3fc"><text class="form-label data-v-99eee3fc">特权颜色</text><view class="color-picker data-v-99eee3fc"><view wx:for="{{n}}" wx:for-item="color" wx:key="a" class="{{['color-option', 'data-v-99eee3fc', color.b && 'active']}}" style="{{'background-color:' + color.c}}" bindtap="{{color.d}}"></view></view></view><view class="form-item data-v-99eee3fc"><text class="form-label data-v-99eee3fc">特权图标</text><view class="icon-upload data-v-99eee3fc"><image wx:if="{{o}}" class="preview-icon data-v-99eee3fc" src="{{p}}" mode="aspectFit"></image><view wx:else class="upload-btn data-v-99eee3fc" bindtap="{{q}}"><text class="upload-icon data-v-99eee3fc">+</text><text class="upload-text data-v-99eee3fc">上传图标</text></view></view></view><view class="form-item data-v-99eee3fc"><text class="form-label data-v-99eee3fc">特权类型</text><picker class="form-picker data-v-99eee3fc" range="{{s}}" bindchange="{{t}}"><view class="picker-value data-v-99eee3fc">{{r}}</view></picker></view><view class="form-item data-v-99eee3fc"><text class="form-label data-v-99eee3fc">特权价值</text><input class="form-input data-v-99eee3fc" placeholder="请输入特权价值，如折扣力度、赠送数量等" value="{{v}}" bindinput="{{w}}"/></view><view class="form-item data-v-99eee3fc"><text class="form-label data-v-99eee3fc">适用等级</text><view class="level-checkboxes data-v-99eee3fc"><view wx:for="{{x}}" wx:for-item="level" wx:key="c" class="{{['level-checkbox', 'data-v-99eee3fc', level.d && 'active']}}" bindtap="{{level.e}}"><text class="checkbox-icon data-v-99eee3fc">{{level.a}}</text><text class="checkbox-label data-v-99eee3fc">{{level.b}}</text></view></view></view><view class="form-item data-v-99eee3fc"><text class="form-label data-v-99eee3fc">特权说明</text><block wx:if="{{r0}}"><textarea class="form-textarea data-v-99eee3fc" placeholder="请输入特权说明" value="{{y}}" bindinput="{{z}}"></textarea></block></view></view><view class="popup-footer data-v-99eee3fc"><button class="cancel-btn data-v-99eee3fc" bindtap="{{A}}">取消</button><button class="confirm-btn data-v-99eee3fc" bindtap="{{B}}">确认</button></view></view></uni-popup><uni-popup wx:if="{{I}}" class="r data-v-99eee3fc" u-s="{{['d']}}" u-r="deleteConfirmPopup" u-i="99eee3fc-1" bind:__l="__l" u-p="{{I}}"><uni-popup-dialog wx:if="{{G}}" class="data-v-99eee3fc" bindconfirm="{{E}}" bindclose="{{F}}" u-i="99eee3fc-2,99eee3fc-1" bind:__l="__l" u-p="{{G}}"></uni-popup-dialog></uni-popup></view>