<view class="shops-container data-v-9c228401"><view class="custom-navbar data-v-9c228401"><view class="navbar-bg data-v-9c228401"></view><view class="navbar-content data-v-9c228401"><view class="back-btn data-v-9c228401" bindtap="{{c}}"><svg wx:if="{{b}}" u-s="{{['d']}}" class="icon data-v-9c228401" u-i="9c228401-0" bind:__l="__l" u-p="{{b}}"><path wx:if="{{a}}" class="data-v-9c228401" u-i="9c228401-1,9c228401-0" bind:__l="__l" u-p="{{a}}"></path></svg></view><view class="navbar-title data-v-9c228401">商铺列表</view><view class="navbar-right data-v-9c228401"><view class="search-btn data-v-9c228401" bindtap="{{g}}"><svg wx:if="{{f}}" u-s="{{['d']}}" class="icon data-v-9c228401" u-i="9c228401-2" bind:__l="__l" u-p="{{f}}"><circle wx:if="{{d}}" class="data-v-9c228401" u-i="9c228401-3,9c228401-2" bind:__l="__l" u-p="{{d}}"></circle><path wx:if="{{e}}" class="data-v-9c228401" u-i="9c228401-4,9c228401-2" bind:__l="__l" u-p="{{e}}"></path></svg></view></view></view></view><view class="search-filter-bar data-v-9c228401"><view class="search-box data-v-9c228401" bindtap="{{k}}"><svg wx:if="{{j}}" u-s="{{['d']}}" class="search-icon data-v-9c228401" u-i="9c228401-5" bind:__l="__l" u-p="{{j}}"><circle wx:if="{{h}}" class="data-v-9c228401" u-i="9c228401-6,9c228401-5" bind:__l="__l" u-p="{{h}}"></circle><path wx:if="{{i}}" class="data-v-9c228401" u-i="9c228401-7,9c228401-5" bind:__l="__l" u-p="{{i}}"></path></svg><text class="search-placeholder data-v-9c228401">搜索商铺名称、类别</text></view><view class="filter-options data-v-9c228401"><view class="{{['filter-option', 'data-v-9c228401', n && 'active']}}" bindtap="{{o}}"><text class="data-v-9c228401">距离</text><svg wx:if="{{m}}" u-s="{{['d']}}" class="sort-icon data-v-9c228401" u-i="9c228401-8" bind:__l="__l" u-p="{{m}}"><path wx:if="{{l}}" class="data-v-9c228401" u-i="9c228401-9,9c228401-8" bind:__l="__l" u-p="{{l}}"></path></svg></view><view class="{{['filter-option', 'data-v-9c228401', r && 'active']}}" bindtap="{{s}}"><text class="data-v-9c228401">评分</text><svg wx:if="{{q}}" u-s="{{['d']}}" class="sort-icon data-v-9c228401" u-i="9c228401-10" bind:__l="__l" u-p="{{q}}"><path wx:if="{{p}}" class="data-v-9c228401" u-i="9c228401-11,9c228401-10" bind:__l="__l" u-p="{{p}}"></path></svg></view><view class="{{['filter-option', 'data-v-9c228401', w && 'active']}}" bindtap="{{x}}"><text class="data-v-9c228401">销量</text><svg wx:if="{{v}}" u-s="{{['d']}}" class="sort-icon data-v-9c228401" u-i="9c228401-12" bind:__l="__l" u-p="{{v}}"><path wx:if="{{t}}" class="data-v-9c228401" u-i="9c228401-13,9c228401-12" bind:__l="__l" u-p="{{t}}"></path></svg></view><view class="filter-option data-v-9c228401" bindtap="{{A}}"><text class="data-v-9c228401">筛选</text><svg wx:if="{{z}}" u-s="{{['d']}}" class="filter-icon data-v-9c228401" u-i="9c228401-14" bind:__l="__l" u-p="{{z}}"><path wx:if="{{y}}" class="data-v-9c228401" u-i="9c228401-15,9c228401-14" bind:__l="__l" u-p="{{y}}"></path></svg></view></view></view><scroll-view class="category-scroll data-v-9c228401" scroll-x><view class="category-list data-v-9c228401"><view wx:for="{{B}}" wx:for-item="category" wx:key="b" class="{{['category-item', 'data-v-9c228401', category.c && 'active']}}" bindtap="{{category.d}}"><text class="data-v-9c228401">{{category.a}}</text></view></view></scroll-view><scroll-view class="shops-scroll data-v-9c228401" scroll-y refresher-enabled refresher-triggered="{{T}}" bindrefresherrefresh="{{U}}" bindscrolltolower="{{V}}"><view class="shops-list data-v-9c228401"><view wx:for="{{C}}" wx:for-item="shop" wx:key="z" class="shop-card data-v-9c228401" bindtap="{{shop.A}}"><image src="{{shop.a}}" class="shop-logo data-v-9c228401" mode="aspectFill"></image><view class="shop-info data-v-9c228401"><view class="shop-header data-v-9c228401"><text class="shop-name data-v-9c228401">{{shop.b}}</text><view class="shop-rating data-v-9c228401"><text class="rating-value data-v-9c228401">{{shop.c}}</text><svg wx:if="{{E}}" u-s="{{['d']}}" class="star-icon data-v-9c228401" u-i="{{shop.e}}" bind:__l="__l" u-p="{{E}}"><polygon wx:if="{{D}}" class="data-v-9c228401" u-i="{{shop.d}}" bind:__l="__l" u-p="{{D}}"></polygon></svg></view></view><view class="shop-meta data-v-9c228401"><view class="meta-item data-v-9c228401"><svg wx:if="{{H}}" u-s="{{['d']}}" class="meta-icon data-v-9c228401" u-i="{{shop.h}}" bind:__l="__l" u-p="{{H}}"><path wx:if="{{F}}" class="data-v-9c228401" u-i="{{shop.f}}" bind:__l="__l" u-p="{{F}}"></path><path wx:if="{{G}}" class="data-v-9c228401" u-i="{{shop.g}}" bind:__l="__l" u-p="{{G}}"></path></svg><text class="meta-text data-v-9c228401">{{shop.i}}</text></view><view class="meta-item data-v-9c228401"><svg wx:if="{{K}}" u-s="{{['d']}}" class="meta-icon data-v-9c228401" u-i="{{shop.l}}" bind:__l="__l" u-p="{{K}}"><path wx:if="{{I}}" class="data-v-9c228401" u-i="{{shop.j}}" bind:__l="__l" u-p="{{I}}"></path><circle wx:if="{{J}}" class="data-v-9c228401" u-i="{{shop.k}}" bind:__l="__l" u-p="{{J}}"></circle></svg><text class="meta-text data-v-9c228401">{{shop.m}}</text></view><view class="meta-item data-v-9c228401"><svg wx:if="{{O}}" u-s="{{['d']}}" class="meta-icon data-v-9c228401" u-i="{{shop.q}}" bind:__l="__l" u-p="{{O}}"><path wx:if="{{L}}" class="data-v-9c228401" u-i="{{shop.n}}" bind:__l="__l" u-p="{{L}}"></path><circle wx:if="{{M}}" class="data-v-9c228401" u-i="{{shop.o}}" bind:__l="__l" u-p="{{M}}"></circle><path wx:if="{{N}}" class="data-v-9c228401" u-i="{{shop.p}}" bind:__l="__l" u-p="{{N}}"></path></svg><text class="meta-text data-v-9c228401">月售{{shop.r}}+</text></view></view><view class="shop-tags data-v-9c228401"><view wx:for="{{shop.s}}" wx:for-item="tag" wx:key="b" class="shop-tag data-v-9c228401">{{tag.a}}</view><view wx:if="{{shop.t}}" class="more-tag data-v-9c228401">+{{shop.v}}</view></view><view wx:if="{{shop.w}}" class="shop-promotion data-v-9c228401"><view class="promotion-tag data-v-9c228401">{{shop.x}}</view><text class="promotion-text data-v-9c228401">{{shop.y}}</text></view></view></view></view><view wx:if="{{P}}" class="loading-more data-v-9c228401"><view class="loading-spinner data-v-9c228401"></view><text class="loading-text data-v-9c228401">加载中...</text></view><view wx:if="{{Q}}" class="empty-state data-v-9c228401"><image class="empty-image data-v-9c228401" src="{{R}}"></image><text class="empty-text data-v-9c228401">暂无相关商铺</text><view class="action-btn data-v-9c228401" bindtap="{{S}}" style="{{'background:' + 'linear-gradient(135deg, #FF9500 0%, #FFCC00 100%)' + ';' + ('border-radius:' + '35px') + ';' + ('box-shadow:' + '0 5px 15px rgba(255,149,0,0.3)')}}"><text class="data-v-9c228401">重置筛选</text></view></view></scroll-view><uni-popup wx:if="{{ag}}" class="r data-v-9c228401" u-s="{{['d']}}" u-r="filterPopup" u-i="9c228401-28" bind:__l="__l" u-p="{{ag}}"><view class="filter-popup data-v-9c228401"><view class="filter-header data-v-9c228401"><text class="filter-title data-v-9c228401">筛选</text><view class="filter-close data-v-9c228401" bindtap="{{Z}}"><svg wx:if="{{Y}}" u-s="{{['d']}}" class="icon data-v-9c228401" u-i="9c228401-29,9c228401-28" bind:__l="__l" u-p="{{Y}}"><line wx:if="{{W}}" class="data-v-9c228401" u-i="9c228401-30,9c228401-29" bind:__l="__l" u-p="{{W}}"></line><line wx:if="{{X}}" class="data-v-9c228401" u-i="9c228401-31,9c228401-29" bind:__l="__l" u-p="{{X}}"></line></svg></view></view><view class="filter-content data-v-9c228401"><view class="filter-section data-v-9c228401"><text class="section-title data-v-9c228401">商铺分类</text><view class="filter-options data-v-9c228401"><view wx:for="{{aa}}" wx:for-item="category" wx:key="b" class="{{['filter-option', 'data-v-9c228401', category.c && 'active']}}" bindtap="{{category.d}}">{{category.a}}</view></view></view><view class="filter-section data-v-9c228401"><text class="section-title data-v-9c228401">商铺评分</text><view class="filter-options data-v-9c228401"><view wx:for="{{ab}}" wx:for-item="option" wx:key="b" class="{{['filter-option', 'data-v-9c228401', option.c && 'active']}}" bindtap="{{option.d}}">{{option.a}}</view></view></view><view class="filter-section data-v-9c228401"><text class="section-title data-v-9c228401">特色服务</text><view class="filter-options data-v-9c228401"><view wx:for="{{ac}}" wx:for-item="option" wx:key="b" class="{{['filter-option', 'data-v-9c228401', option.c && 'active']}}" bindtap="{{option.d}}">{{option.a}}</view></view></view></view><view class="filter-footer data-v-9c228401"><view class="filter-reset data-v-9c228401" bindtap="{{ad}}"><text class="data-v-9c228401">重置</text></view><view class="filter-apply data-v-9c228401" bindtap="{{ae}}"><text class="data-v-9c228401">确定</text></view></view></view></uni-popup></view>