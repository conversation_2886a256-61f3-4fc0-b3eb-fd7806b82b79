/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 移除阿里妈妈字体引用，改用系统默认字体 */
/* 移除原有阿里妈妈字体定义
$uni-font-family: 'AlimamaShuHeiTi';
$uni-font-family-text: 'AlimamaShuHeiTi', -apple-system, BlinkMacSystemFont, 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;
*/
body.data-v-1398ae02, html.data-v-1398ae02, #app.data-v-1398ae02, .index-container.data-v-1398ae02 {
  font-family: "AlimamaShuHeiTi", -apple-system, BlinkMacSystemFont, "Helvetica Neue", "PingFang SC", "Microsoft YaHei", sans-serif;
}
.reviews-container.data-v-1398ae02 {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #F2F2F7;
}

/* 自定义导航栏 */
.custom-navbar.data-v-1398ae02 {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: calc(var(--status-bar-height, 25px) + 62px);
  width: 100%;
  z-index: 100;
}
.custom-navbar .navbar-bg.data-v-1398ae02 {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #FF3B69 0%, #FF7A9E 100%);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  box-shadow: 0 4px 6px rgba(255, 59, 105, 0.15);
}
.custom-navbar .navbar-content.data-v-1398ae02 {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 100%;
  padding: 0 30rpx;
  padding-top: var(--status-bar-height, 25px);
  box-sizing: border-box;
}
.custom-navbar .navbar-left.data-v-1398ae02, .custom-navbar .navbar-right.data-v-1398ae02 {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 80rpx;
  height: 80rpx;
}
.custom-navbar .navbar-title.data-v-1398ae02 {
  font-size: 36rpx;
  font-weight: 600;
  color: #FFFFFF;
  letter-spacing: 0.5px;
}
.custom-navbar .icon.data-v-1398ae02 {
  width: 48rpx;
  height: 48rpx;
}

/* 内容区域 */
.content-scroll.data-v-1398ae02 {
  flex: 1;
  margin-top: calc(var(--status-bar-height, 25px) + 62px);
}

/* 商品信息 */
.product-info.data-v-1398ae02 {
  display: flex;
  align-items: center;
  padding: 20rpx 30rpx;
  background-color: #FFFFFF;
}
.product-info .product-image.data-v-1398ae02 {
  width: 120rpx;
  height: 120rpx;
  border-radius: 8rpx;
}
.product-info .product-detail.data-v-1398ae02 {
  flex: 1;
  margin-left: 20rpx;
}
.product-info .product-detail .product-name.data-v-1398ae02 {
  font-size: 28rpx;
  color: #333333;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}
.product-info .product-detail .product-price.data-v-1398ae02 {
  font-size: 28rpx;
  color: #FF3B69;
  font-weight: 600;
  margin-top: 10rpx;
}

/* 评价统计 */
.review-stats.data-v-1398ae02 {
  margin-top: 20rpx;
  padding: 20rpx 30rpx;
  background-color: #FFFFFF;
}
.review-stats .stats-header.data-v-1398ae02 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}
.review-stats .stats-header .stats-title.data-v-1398ae02 {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
}
.review-stats .stats-header .positive-rate.data-v-1398ae02 {
  font-size: 28rpx;
  color: #FF3B69;
}
.review-stats .tag-list.data-v-1398ae02 {
  display: flex;
  flex-wrap: wrap;
}
.review-stats .tag-list .tag-item.data-v-1398ae02 {
  margin-right: 20rpx;
  margin-bottom: 20rpx;
  padding: 10rpx 20rpx;
  border-radius: 30rpx;
  font-size: 24rpx;
  color: #666666;
  background-color: #F2F2F7;
}
.review-stats .tag-list .tag-item.active.data-v-1398ae02 {
  color: #FFFFFF;
  background: linear-gradient(135deg, #FF3B69 0%, #FF7A9E 100%);
}

/* 评价列表 */
.review-list.data-v-1398ae02 {
  margin-top: 20rpx;
  background-color: #FFFFFF;
}
.review-item.data-v-1398ae02 {
  padding: 30rpx;
  border-bottom: 1rpx solid #F2F2F7;
}
.review-item.data-v-1398ae02:last-child {
  border-bottom: none;
}
.review-item .review-header.data-v-1398ae02 {
  display: flex;
  align-items: center;
}
.review-item .review-header .user-avatar.data-v-1398ae02 {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
}
.review-item .review-header .user-info.data-v-1398ae02 {
  flex: 1;
  margin-left: 20rpx;
}
.review-item .review-header .user-info .user-name.data-v-1398ae02 {
  font-size: 28rpx;
  color: #333333;
}
.review-item .review-header .user-info .review-time.data-v-1398ae02 {
  font-size: 24rpx;
  color: #999999;
  margin-top: 4rpx;
}
.review-item .review-header .rating.data-v-1398ae02 {
  display: flex;
}
.review-item .review-header .rating .star.data-v-1398ae02 {
  width: 24rpx;
  height: 24rpx;
  margin-left: 4rpx;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23E0E0E0'%3E%3Cpath d='M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z'/%3E%3C/svg%3E");
  background-size: contain;
  background-repeat: no-repeat;
}
.review-item .review-header .rating .star.filled.data-v-1398ae02 {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23FF9500'%3E%3Cpath d='M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z'/%3E%3C/svg%3E");
}
.review-item .review-content.data-v-1398ae02 {
  margin-top: 20rpx;
  font-size: 28rpx;
  color: #333333;
  line-height: 1.6;
}
.review-item .review-images.data-v-1398ae02 {
  display: flex;
  flex-wrap: wrap;
  margin-top: 20rpx;
}
.review-item .review-images .review-image.data-v-1398ae02 {
  width: 160rpx;
  height: 160rpx;
  margin-right: 10rpx;
  margin-bottom: 10rpx;
  border-radius: 8rpx;
}
.review-item .review-specs.data-v-1398ae02 {
  margin-top: 20rpx;
  font-size: 24rpx;
  color: #999999;
}
.review-item .shop-reply.data-v-1398ae02 {
  margin-top: 20rpx;
  padding: 20rpx;
  background-color: #F9F9F9;
  border-radius: 8rpx;
}
.review-item .shop-reply .reply-title.data-v-1398ae02 {
  font-size: 24rpx;
  color: #666666;
  font-weight: 600;
}
.review-item .shop-reply .reply-content.data-v-1398ae02 {
  margin-top: 10rpx;
  font-size: 24rpx;
  color: #666666;
  line-height: 1.6;
}
.review-item .review-actions.data-v-1398ae02 {
  display: flex;
  justify-content: flex-end;
  margin-top: 20rpx;
}
.review-item .review-actions .action-item.data-v-1398ae02 {
  display: flex;
  align-items: center;
  padding: 10rpx;
}
.review-item .review-actions .action-item .icon.data-v-1398ae02 {
  color: #999999;
  margin-right: 6rpx;
}
.review-item .review-actions .action-item .icon.active.data-v-1398ae02 {
  color: #FF3B69;
}
.review-item .review-actions .action-item text.data-v-1398ae02 {
  font-size: 24rpx;
  color: #999999;
}

/* 空评价 */
.empty-reviews.data-v-1398ae02 {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
  background-color: #FFFFFF;
  margin-top: 20rpx;
}
.empty-reviews .empty-icon.data-v-1398ae02 {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 30rpx;
}
.empty-reviews .empty-text.data-v-1398ae02 {
  font-size: 28rpx;
  color: #999999;
}

/* 底部安全区域 */
.safe-area-bottom.data-v-1398ae02 {
  height: 34px;
  /* iOS 安全区域高度 */
}