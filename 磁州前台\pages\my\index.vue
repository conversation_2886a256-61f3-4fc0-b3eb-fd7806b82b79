<!-- 添加返利系统入口 -->
<view class="menu-section">
  <view class="section-title">推广与返利</view>
  <view class="menu-grid">
    <view class="menu-item" @click="navigateToCashback">
      <view class="menu-icon-wrap" style="background: linear-gradient(135deg, #FF2D55 0%, #FF9500 100%);">
        <svg class="menu-icon" viewBox="0 0 24 24" width="24" height="24">
          <path fill="#FFFFFF" d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1.41 16.09V20h-2.67v-1.93c-1.71-.36-3.16-1.46-3.27-3.4h1.96c.1.93.82 1.62 2.02 1.62 1.19 0 1.74-.65 1.74-1.27 0-.65-.58-1.1-1.72-1.35l-1.57-.35c-1.57-.35-2.87-1.23-2.87-2.96 0-1.72 1.4-2.84 3.04-3.22V5h2.67v1.95c1.86.45 2.79 1.86 2.85 3.39H14.3c-.05-1.11-.64-1.75-2.05-1.75-1.09 0-1.71.65-1.71 1.24 0 .61.5 1.04 1.71 1.29l1.6.37c1.77.4 2.9 1.23 2.9 3.01 0 1.77-1.34 2.91-3.34 3.31z"/>
        </svg>
      </view>
      <text class="menu-text">返利商城</text>
    </view>
    <view class="menu-item" @click="navigateToWallet">
      <view class="menu-icon-wrap" style="background: linear-gradient(135deg, #007AFF 0%, #5AC8FA 100%);">
        <svg class="menu-icon" viewBox="0 0 24 24" width="24" height="24">
          <path fill="#FFFFFF" d="M21 18v1c0 1.1-.9 2-2 2H5c-1.11 0-2-.9-2-2V5c0-1.1.89-2 2-2h14c1.1 0 2 .9 2 2v1h-9c-1.11 0-2 .9-2 2v8c0 1.1.89 2 2 2h9zm-9-2h10V8H12v8zm4-2.5c-.83 0-1.5-.67-1.5-1.5s.67-1.5 1.5-1.5 1.5.67 1.5 1.5-.67 1.5-1.5 1.5z"/>
        </svg>
      </view>
      <text class="menu-text">我的钱包</text>
    </view>
    <view class="menu-item" @click="navigateToOrders">
      <view class="menu-icon-wrap" style="background: linear-gradient(135deg, #34C759 0%, #30D158 100%);">
        <svg class="menu-icon" viewBox="0 0 24 24" width="24" height="24">
          <path fill="#FFFFFF" d="M19 3h-4.18C14.4 1.84 13.3 1 12 1c-1.3 0-2.4.84-2.82 2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-7 0c.55 0 1 .45 1 1s-.45 1-1 1-1-.45-1-1 .45-1 1-1zm-2 14l-4-4 1.41-1.41L10 14.17l6.59-6.59L18 9l-8 8z"/>
        </svg>
      </view>
      <text class="menu-text">返利订单</text>
    </view>
    <view class="menu-item" @click="navigateToInvite">
      <view class="menu-icon-wrap" style="background: linear-gradient(135deg, #AF52DE 0%, #9F44D3 100%);">
        <svg class="menu-icon" viewBox="0 0 24 24" width="24" height="24">
          <path fill="#FFFFFF" d="M16 11c1.66 0 2.99-1.34 2.99-3S17.66 5 16 5c-1.66 0-3 1.34-3 3s1.34 3 3 3zm-8 0c1.66 0 2.99-1.34 2.99-3S9.66 5 8 5C6.34 5 5 6.34 5 8s1.34 3 3 3zm0 2c-2.33 0-7 1.17-7 3.5V19h14v-2.5c0-2.33-4.67-3.5-7-3.5zm8 0c-.29 0-.62.02-.97.05 1.16.84 1.97 1.97 1.97 3.45V19h6v-2.5c0-2.33-4.67-3.5-7-3.5z"/>
        </svg>
      </view>
      <text class="menu-text">邀请好友</text>
    </view>
  </view>
</view>

<script>
export default {
  // ... existing code ...
  
  methods: {
    // ... existing methods ...
    
    // 添加返利系统相关方法
    navigateToCashback() {
      uni.navigateTo({
        url: '/subPackages/cashback/pages/index/index'
      });
    },
    navigateToWallet() {
      uni.navigateTo({
        url: '/subPackages/cashback/pages/wallet/index'
      });
    },
    navigateToOrders() {
      uni.navigateTo({
        url: '/subPackages/cashback/pages/orders/index'
      });
    },
    navigateToInvite() {
      uni.navigateTo({
        url: '/subPackages/cashback/pages/invite/index'
      });
    }
  }
};
</script>

<style lang="scss">
// ... existing styles ...

// 添加返利系统相关样式
.menu-section {
  background-color: #FFFFFF;
  border-radius: 20rpx;
  padding: 30rpx;
  margin: 20rpx;
  box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.05);
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #1C1C1E;
  margin-bottom: 20rpx;
}

.menu-grid {
  display: flex;
  flex-wrap: wrap;
}

.menu-item {
  width: 25%;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 20rpx;
}

.menu-icon-wrap {
  width: 90rpx;
  height: 90rpx;
  border-radius: 45rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 10rpx;
}

.menu-icon {
  width: 50rpx;
  height: 50rpx;
}

.menu-text {
  font-size: 24rpx;
  color: #1C1C1E;
}
</style> 