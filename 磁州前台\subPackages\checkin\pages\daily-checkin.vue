<template>
  <view class="checkin-container">
    <!-- 自定义导航栏 -->
    <view class="navbar">
      <view class="navbar-left">
        <view class="back-button" @tap="goBack">
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M15 18L9 12L15 6" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
        </view>
      </view>
      <view class="navbar-title">
        <text class="title-text">每日签到</text>
      </view>
      <view class="navbar-right">
        <!-- 占位 -->
      </view>
    </view>
    
    <!-- 页面内容区域 -->
    <scroll-view scroll-y class="content-area">
      <!-- 签到卡片 -->
      <view class="checkin-card">
        <view class="card-header">
          <text class="card-title">每日签到</text>
          <text class="card-subtitle">连续签到{{continuousDays}}天</text>
        </view>
        
        <view class="calendar-section">
          <view class="month-header">
            <text class="month-text">{{currentYear}}年{{currentMonth}}月</text>
          </view>
          <view class="weekdays">
            <text class="weekday" v-for="day in ['日', '一', '二', '三', '四', '五', '六']" :key="day">{{day}}</text>
          </view>
          <view class="days-grid">
            <view class="day-item" v-for="(day, index) in calendarDays" :key="index"
                  :class="{
                    'empty': !day.date,
                    'checked': day.checked,
                    'today': day.isToday,
                    'disabled': day.disabled
                  }">
              <text class="day-number" v-if="day.date">{{day.date}}</text>
              <view class="checked-mark" v-if="day.checked"></view>
            </view>
          </view>
        </view>
        
        <view class="checkin-button-container">
          <view class="checkin-button" :class="{'disabled': hasCheckedToday}" @tap="doCheckin">
            <text>{{hasCheckedToday ? '今日已签到' : '立即签到'}}</text>
            <text class="points-text" v-if="!hasCheckedToday">+{{checkinPoints}}积分</text>
          </view>
        </view>
      </view>
      
      <!-- 签到规则 -->
      <view class="rules-card">
        <view class="card-header">
          <text class="card-title">签到规则</text>
        </view>
        <view class="rules-content">
          <view class="rule-item">
            <text class="rule-number">1.</text>
            <text class="rule-text">每日签到可获得{{checkinPoints}}积分</text>
          </view>
          <view class="rule-item">
            <text class="rule-number">2.</text>
            <text class="rule-text">连续签到7天可额外获得{{weeklyBonus}}积分</text>
          </view>
          <view class="rule-item">
            <text class="rule-number">3.</text>
            <text class="rule-text">连续签到30天可额外获得{{monthlyBonus}}积分</text>
          </view>
          <view class="rule-item">
            <text class="rule-number">4.</text>
            <text class="rule-text">每日签到时间为00:00-23:59</text>
          </view>
        </view>
      </view>
      
      <!-- 签到记录 -->
      <view class="records-card">
        <view class="card-header">
          <text class="card-title">签到记录</text>
        </view>
        <view class="records-list">
          <view class="record-item" v-for="(record, index) in checkinRecords" :key="index">
            <view class="record-date">
              <text class="date-text">{{record.date}}</text>
              <text class="time-text">{{record.time}}</text>
            </view>
            <view class="record-points">
              <text class="points-value">+{{record.points}}</text>
              <text class="points-label">积分</text>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 底部空间 -->
      <view class="bottom-space"></view>
    </scroll-view>
  </view>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue';

// 签到相关数据
const checkinPoints = ref(10); // 每日签到积分
const weeklyBonus = ref(30);   // 连续签到7天奖励
const monthlyBonus = ref(100); // 连续签到30天奖励
const continuousDays = ref(3); // 连续签到天数
const hasCheckedToday = ref(false); // 今天是否已签到

// 日历相关
const currentDate = new Date();
const currentYear = ref(currentDate.getFullYear());
const currentMonth = ref(currentDate.getMonth() + 1);
const calendarDays = ref([]);

// 生成日历数据
const generateCalendar = () => {
  const year = currentYear.value;
  const month = currentMonth.value;
  const firstDay = new Date(year, month - 1, 1).getDay(); // 本月第一天是周几
  const lastDate = new Date(year, month, 0).getDate(); // 本月最后一天是几号
  
  const days = [];
  
  // 填充本月第一天之前的空白
  for (let i = 0; i < firstDay; i++) {
    days.push({ date: null });
  }
  
  // 填充本月的日期
  const today = new Date();
  const isCurrentMonth = today.getFullYear() === year && today.getMonth() + 1 === month;
  const todayDate = today.getDate();
  
  for (let i = 1; i <= lastDate; i++) {
    const isToday = isCurrentMonth && i === todayDate;
    const isPast = isCurrentMonth ? i < todayDate : new Date(year, month - 1, i) < today;
    
    days.push({
      date: i,
      isToday,
      disabled: !isPast && !isToday,
      checked: isPast || (isToday && hasCheckedToday.value)
    });
  }
  
  // 确保日历总是显示6行
  const totalCells = 42;
  const remainingCells = totalCells - days.length;
  
  for (let i = 0; i < remainingCells; i++) {
    days.push({ date: null });
  }
  
  calendarDays.value = days;
};

// 签到记录
const checkinRecords = ref([
  {
    date: '2023-12-20',
    time: '08:15',
    points: 10
  },
  {
    date: '2023-12-19',
    time: '09:23',
    points: 10
  },
  {
    date: '2023-12-18',
    time: '07:45',
    points: 10
  },
  {
    date: '2023-12-15',
    time: '12:30',
    points: 10
  },
  {
    date: '2023-12-14',
    time: '08:05',
    points: 10
  }
]);

// 执行签到
const doCheckin = () => {
  if (hasCheckedToday.value) {
    uni.showToast({
      title: '今日已签到',
      icon: 'none'
    });
    return;
  }
  
  // 模拟签到请求
  uni.showLoading({
    title: '签到中...'
  });
  
  setTimeout(() => {
    uni.hideLoading();
    
    // 更新签到状态
    hasCheckedToday.value = true;
    continuousDays.value += 1;
    
    // 更新日历
    const todayIndex = calendarDays.value.findIndex(day => day.isToday);
    if (todayIndex !== -1) {
      calendarDays.value[todayIndex].checked = true;
    }
    
    // 添加签到记录
    const now = new Date();
    const dateStr = `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}-${String(now.getDate()).padStart(2, '0')}`;
    const timeStr = `${String(now.getHours()).padStart(2, '0')}:${String(now.getMinutes()).padStart(2, '0')}`;
    
    checkinRecords.value.unshift({
      date: dateStr,
      time: timeStr,
      points: checkinPoints.value
    });
    
    // 显示签到成功
    uni.showToast({
      title: `签到成功，获得${checkinPoints.value}积分`,
      icon: 'success'
    });
    
    // 检查是否有额外奖励
    if (continuousDays.value === 7) {
      setTimeout(() => {
        uni.showToast({
          title: `连续签到7天，额外获得${weeklyBonus.value}积分`,
          icon: 'success'
        });
      }, 1500);
    } else if (continuousDays.value === 30) {
      setTimeout(() => {
        uni.showToast({
          title: `连续签到30天，额外获得${monthlyBonus.value}积分`,
          icon: 'success'
        });
      }, 1500);
    }
    
    // 发送签到数据到服务器
    // 实际应用中应该调用API
  }, 1000);
};

// 返回上一页
const goBack = () => {
  uni.navigateBack();
};

// 页面加载
onMounted(() => {
  generateCalendar();
});
</script>

<style lang="scss">
/* 页面容器 */
.checkin-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #F5F7FA;
  position: relative;
}

/* 导航栏样式 */
.navbar {
  position: sticky;
  top: 0;
  left: 0;
  right: 0;
  background: linear-gradient(135deg, #FF7600, #FF3C00);
  color: white;
  padding: 48px 20px 16px;
  display: flex;
  align-items: center;
  z-index: 100;
  box-shadow: 0 2px 8px rgba(255, 120, 0, 0.15);
}

.navbar-left {
  width: 40px;
}

.back-button {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.navbar-title {
  flex: 1;
  text-align: center;
}

.title-text {
  font-size: 18px;
  font-weight: 600;
}

.navbar-right {
  width: 40px;
}

/* 内容区域 */
.content-area {
  flex: 1;
  padding: 16px;
  box-sizing: border-box;
  height: calc(100vh - 80px);
}

/* 签到卡片 */
.checkin-card {
  background-color: #FFFFFF;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 16px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.card-header {
  margin-bottom: 16px;
}

.card-title {
  font-size: 18px;
  font-weight: 600;
  color: #333333;
}

.card-subtitle {
  font-size: 14px;
  color: #FF7600;
  margin-top: 4px;
  display: block;
}

/* 日历部分 */
.calendar-section {
  margin-bottom: 20px;
}

.month-header {
  text-align: center;
  margin-bottom: 12px;
}

.month-text {
  font-size: 16px;
  font-weight: 500;
  color: #333333;
}

.weekdays {
  display: flex;
  margin-bottom: 8px;
}

.weekday {
  flex: 1;
  text-align: center;
  font-size: 14px;
  color: #999999;
}

.days-grid {
  display: flex;
  flex-wrap: wrap;
}

.day-item {
  width: 14.28%;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.day-number {
  font-size: 14px;
  color: #333333;
}

.empty {
  visibility: hidden;
}

.checked {
  position: relative;
}

.checked .day-number {
  color: #FFFFFF;
}

.checked::before {
  content: '';
  position: absolute;
  width: 30px;
  height: 30px;
  border-radius: 15px;
  background-color: #FF7600;
  z-index: -1;
}

.today:not(.checked) {
  position: relative;
}

.today:not(.checked) .day-number {
  color: #FF7600;
  font-weight: 600;
}

.today:not(.checked)::before {
  content: '';
  position: absolute;
  width: 30px;
  height: 30px;
  border-radius: 15px;
  border: 1px solid #FF7600;
  z-index: -1;
}

.disabled .day-number {
  color: #CCCCCC;
}

/* 签到按钮 */
.checkin-button-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.checkin-button {
  width: 200px;
  height: 50px;
  background: linear-gradient(135deg, #FF7600, #FF3C00);
  border-radius: 25px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 16px;
  font-weight: 500;
  box-shadow: 0 4px 12px rgba(255, 120, 0, 0.3);
}

.checkin-button.disabled {
  background: #CCCCCC;
  box-shadow: none;
}

.points-text {
  font-size: 12px;
  margin-top: 2px;
}

/* 规则卡片 */
.rules-card {
  background-color: #FFFFFF;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 16px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.rules-content {
  margin-top: 12px;
}

.rule-item {
  display: flex;
  margin-bottom: 8px;
}

.rule-number {
  color: #FF7600;
  margin-right: 8px;
}

.rule-text {
  color: #666666;
  flex: 1;
}

/* 记录卡片 */
.records-card {
  background-color: #FFFFFF;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 16px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.records-list {
  margin-top: 12px;
}

.record-item {
  display: flex;
  justify-content: space-between;
  padding: 12px 0;
  border-bottom: 1px solid #F0F0F0;
}

.record-item:last-child {
  border-bottom: none;
}

.record-date {
  display: flex;
  flex-direction: column;
}

.date-text {
  font-size: 14px;
  color: #333333;
  margin-bottom: 4px;
}

.time-text {
  font-size: 12px;
  color: #999999;
}

.record-points {
  display: flex;
  align-items: center;
}

.points-value {
  font-size: 16px;
  font-weight: 600;
  color: #FF7600;
  margin-right: 4px;
}

.points-label {
  font-size: 12px;
  color: #999999;
}

/* 底部空间 */
.bottom-space {
  height: 20px;
}
</style> 