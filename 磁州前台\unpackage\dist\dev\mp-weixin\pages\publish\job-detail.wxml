<view class="detail-container job-detail-container"><view class="custom-navbar" style="{{'padding-top:' + c}}"><view class="navbar-left" bindtap="{{b}}"><image src="{{a}}" class="back-icon"></image></view><view class="navbar-title">招聘信息详情</view><view class="navbar-right"></view></view><button id="shareButton" class="hidden-share-btn" open-type="share"></button><canvas canvas-id="posterCanvas" class="poster-canvas" style="width:600px;height:900px;position:fixed;top:-9999px;left:-9999px"></canvas><view class="float-poster-btn" bindtap="{{e}}"><image src="{{d}}" class="poster-icon"></image><text class="poster-text">海报</text></view><view class="detail-wrapper job-detail-wrapper"><view class="content-card company-card"><view class="section-title">公司信息</view><view class="publisher-header"><view class="avatar-container"><image src="{{f}}" mode="aspectFill" class="avatar-image"></image></view><view class="publisher-info"><text class="publisher-name">{{g}}</text><view class="publisher-meta"><text class="meta-text">{{h}}</text><text class="meta-text">{{i}}</text><view wx:if="{{j}}" class="meta-text"><text class="iconfont icon-verified"></text><text>已认证</text></view></view></view></view></view><view class="content-card job-card"><view class="job-header"><view class="title-row"><text class="main-title">{{k}}</text><text class="price-text">{{l}}</text></view><view class="meta-info"><view class="tag-group"><view wx:for="{{m}}" wx:for-item="tag" wx:key="b" class="info-tag">{{tag.a}}</view></view><text class="publish-time">发布于 {{n}}</text></view></view><view class="basic-info"><view class="info-item"><text class="info-label">工作地点</text><text class="info-value">{{o}}</text></view><view class="info-item"><text class="info-label">工作经验</text><text class="info-value">{{p}}</text></view><view class="info-item"><text class="info-label">学历要求</text><text class="info-value">{{q}}</text></view><view class="info-item"><text class="info-label">招聘人数</text><text class="info-value">{{r}}人</text></view></view></view><view class="content-card description-card"><view class="section-title">职位描述</view><view class="description-content"><rich-text nodes="{{s}}" class="description-text"></rich-text></view></view><view class="content-card responsibility-card"><view class="section-title">岗位职责</view><view class="job-responsibility"><view wx:for="{{t}}" wx:for-item="item" wx:key="b" class="list-item"><text class="list-dot">•</text><text class="list-text">{{item.a}}</text></view></view></view><view class="content-card requirement-card"><view class="section-title">任职要求</view><view class="job-requirement"><view wx:for="{{v}}" wx:for-item="item" wx:key="b" class="list-item"><text class="list-dot">•</text><text class="list-text">{{item.a}}</text></view></view></view><view class="content-card benefits-card"><view class="section-title">福利待遇</view><view class="job-benefits"><view wx:for="{{w}}" wx:for-item="benefit" wx:key="b" class="benefit-tag"><text class="benefit-icon iconfont icon-benefit"></text><text class="benefit-text">{{benefit.a}}</text></view></view></view><view class="content-card location-card"><view class="section-title">工作地址</view><view class="location-content" bindtap="{{y}}"><text class="iconfont icon-location"></text><text class="location-text">{{x}}</text><text class="iconfont icon-right location-arrow"></text></view><view class="location-map"><image src="{{z}}" mode="aspectFill" class="map-preview"></image></view></view><view class="content-card contact-card"><view class="section-title">招聘方联系方式</view><view class="contact-content"><view class="contact-item"><text class="contact-label">联系人</text><text class="contact-value">{{A}}</text></view><view class="contact-item"><text class="contact-label">电话</text><text class="contact-value contact-phone" bindtap="{{C}}">{{B}}</text></view><view class="contact-tips"><text class="tips-icon iconfont icon-info"></text><text class="tips-text">请说明在"磁州生活网"看到的信息</text></view></view></view><view wx:if="{{D}}" class="content-card red-packet-card"><view class="section-title">红包福利</view><view class="red-packet-section"><view class="red-packet-container" bindtap="{{J}}"><view class="red-packet-blur-bg"></view><view class="red-packet-content"><view class="red-packet-left"><image class="red-packet-icon" src="{{E}}"></image><view class="red-packet-info"><view class="red-packet-title">{{F}}</view><view class="red-packet-desc"> 还剩{{G}}个，{{H}}</view></view></view><view class="red-packet-right"><view class="red-packet-amount"><text class="prefix">共</text> ¥{{I}}</view><view class="grab-btn">立即领取</view></view></view></view></view></view><view class="content-card related-jobs-card"><view class="collapsible-header"><view class="section-title">相关招聘推荐</view></view><view class="collapsible-content"><view class="related-jobs-list"><view wx:for="{{K}}" wx:for-item="job" wx:key="h" class="related-job-item" bindtap="{{job.i}}"><view class="job-item-content"><view class="job-item-left"><image class="company-logo" src="{{job.a}}" mode="aspectFill"></image></view><view class="job-item-middle"><text class="job-item-title">{{job.b}}</text><view class="job-item-company">{{job.c}}</view><view class="job-item-tags"><text wx:for="{{job.d}}" wx:for-item="tag" wx:key="b" class="job-item-tag">{{tag.a}}</text><text wx:if="{{job.e}}" class="job-item-tag-more">+{{job.f}}</text></view></view><view class="job-item-right"><text class="job-item-salary">{{job.g}}</text></view></view></view><view wx:if="{{L}}" class="empty-related-jobs"><image src="{{M}}" class="empty-image" mode="aspectFit"></image><text class="empty-text">暂无相关招聘</text></view></view><view wx:if="{{N}}" class="view-more-btn" catchtap="{{O}}"><text class="view-more-text">查看更多招聘信息</text><text class="view-more-icon iconfont icon-right"></text></view></view></view></view><view class="interaction-toolbar"><view class="toolbar-item" bindtap="{{Q}}"><image src="{{P}}" class="toolbar-icon"></image><text class="toolbar-text">首页</text></view><view class="{{['toolbar-item', T && 'active-toolbar-item']}}" bindtap="{{U}}"><image src="{{R}}" class="toolbar-icon"></image><text class="toolbar-text">{{S}}</text></view><button class="share-button toolbar-item" open-type="share"><image src="{{V}}" class="toolbar-icon"></image><text class="toolbar-text">分享</text></button><view class="toolbar-item" bindtap="{{X}}"><image src="{{W}}" class="toolbar-icon"></image><text class="toolbar-text">评论</text></view><view class="toolbar-item call-button" bindtap="{{Y}}"><view class="call-button-content"><text class="call-text">打电话</text><text class="call-subtitle">请说在磁州生活网看到的</text></view></view></view><view wx:if="{{Z}}" class="red-packet-popup"><view class="red-packet-popup-wrapper" catchtap="{{as}}"><view wx:if="{{aa}}" class="red-packet-task-modal"><view class="red-packet-modal-header"><text class="red-packet-modal-title">完成任务领取红包</text><text class="red-packet-modal-close" bindtap="{{ab}}">×</text></view><view class="red-packet-modal-amount">¥{{ac}}</view><view class="red-packet-task-list"><view wx:if="{{ad}}" class="{{['red-packet-task-item', aj && 'task-completed']}}"><view class="{{['task-icon-container', af && 'completed-icon-container']}}"><image class="task-icon" src="{{ae}}"></image></view><view class="task-info"><view class="task-title">转发分享</view><view class="task-desc">分享给好友，{{ag}}/10人查看</view></view><button class="{{['task-action-btn', ai && 'completed-btn']}}" open-type="share">{{ah}}</button></view></view><view class="{{['red-packet-get-btn', al && 'red-packet-get-btn-active']}}" bindtap="{{am}}">{{ak}}</view><view class="red-packet-tip"><text class="red-packet-tip-text">*完成以上任务即可领取红包</text></view></view><view wx:if="{{an}}" class="red-packet-success-modal"><image class="success-bg" src="{{ao}}" mode="aspectFill"></image><view class="success-content"><view class="success-title">恭喜您抢到红包</view><view class="success-amount">¥{{ap}}</view><view class="success-desc">红包已存入您的钱包</view><view class="success-btn-group"><view class="success-btn success-btn-wallet" bindtap="{{aq}}">查看我的钱包</view><view class="success-btn" bindtap="{{ar}}">关闭</view></view></view></view></view></view><view wx:if="{{at}}" class="comment-popup"><view class="comment-mask" bindtap="{{av}}"></view><view class="comment-container"><view class="comment-header"><text class="comment-title">发表评论</text><text class="comment-close" bindtap="{{aw}}">×</text></view><view class="comment-body"><block wx:if="{{r0}}"><textarea class="comment-textarea" placeholder="说点什么吧..." maxlength="200" auto-focus auto-height value="{{ax}}" bindinput="{{ay}}"></textarea></block><view class="comment-footer"><text class="comment-count">{{az}}/200</text><button class="comment-submit" bindtap="{{aA}}">发布</button></view></view></view></view></view>