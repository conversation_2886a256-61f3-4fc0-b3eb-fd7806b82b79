package com.cizhou.common.core.page;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 分页结果
 *
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Data
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PageResult<T> implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 数据列表
     */
    private List<T> list;

    /**
     * 总记录数
     */
    private Long total;

    /**
     * 当前页码
     */
    private Long page;

    /**
     * 每页大小
     */
    private Long size;

    /**
     * 总页数
     */
    private Long pages;

    /**
     * 是否有上一页
     */
    private Boolean hasPrevious;

    /**
     * 是否有下一页
     */
    private Boolean hasNext;

    /**
     * 是否为第一页
     */
    private Boolean isFirst;

    /**
     * 是否为最后一页
     */
    private Boolean isLast;

    public PageResult(List<T> list, Long total, Long page, Long size) {
        this.list = list;
        this.total = total;
        this.page = page;
        this.size = size;
        this.pages = (total + size - 1) / size;
        this.hasPrevious = page > 1;
        this.hasNext = page < pages;
        this.isFirst = page == 1;
        this.isLast = page.equals(pages);
    }

    /**
     * 创建分页结果
     */
    public static <T> PageResult<T> of(List<T> list, Long total, Long page, Long size) {
        return new PageResult<>(list, total, page, size);
    }

    /**
     * 创建空分页结果
     */
    public static <T> PageResult<T> empty(Long page, Long size) {
        return new PageResult<>(List.of(), 0L, page, size);
    }

    /**
     * 从MyBatis Plus的Page对象创建
     */
    public static <T> PageResult<T> of(com.baomidou.mybatisplus.extension.plugins.pagination.Page<T> page) {
        return new PageResult<>(
            page.getRecords(),
            page.getTotal(),
            page.getCurrent(),
            page.getSize()
        );
    }

    /**
     * 转换数据类型
     */
    public <R> PageResult<R> map(List<R> newList) {
        return new PageResult<>(newList, this.total, this.page, this.size);
    }
}
