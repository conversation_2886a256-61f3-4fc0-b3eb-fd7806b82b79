@echo off
chcp 65001
echo.
echo ==========================================
echo    磁州生活网前端项目启动
echo ==========================================
echo.

echo [1/3] 检查Node.js环境...
node -v
if %errorlevel% neq 0 (
    echo 错误：未找到Node.js环境，请先安装Node.js 16或更高版本
    pause
    exit /b 1
)

echo.
echo [2/3] 检查依赖包...
if not exist "node_modules" (
    echo 正在安装依赖包...
    npm install
    if %errorlevel% neq 0 (
        echo 错误：依赖包安装失败
        pause
        exit /b 1
    )
) else (
    echo 依赖包已存在，跳过安装
)

echo.
echo [3/3] 启动开发服务器...
echo 正在启动磁州生活网前端项目...
echo 这是一个uni-app项目，支持多端运行
echo.
echo 可用的启动命令：
echo - H5版本: npm run dev
echo - 微信小程序: npm run dev:mp-weixin
echo.
echo 正在启动H5版本...
echo 前端地址: http://localhost:3000 (通常)
echo 后端API: http://localhost:8080/api/v1
echo.
echo 按 Ctrl+C 停止服务
echo.

npm run dev

pause
