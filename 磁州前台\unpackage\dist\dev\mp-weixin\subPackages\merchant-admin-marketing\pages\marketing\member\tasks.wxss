/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 移除阿里妈妈字体引用，改用系统默认字体 */
/* 移除原有阿里妈妈字体定义
$uni-font-family: 'AlimamaShuHeiTi';
$uni-font-family-text: 'AlimamaShuHeiTi', -apple-system, BlinkMacSystemFont, 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;
*/
body, html, #app, .index-container {
  font-family: "AlimamaShuHeiTi", -apple-system, BlinkMacSystemFont, "Helvetica Neue", "PingFang SC", "Microsoft YaHei", sans-serif;
}
.tasks-container {
  min-height: 100vh;
  background-color: #F5F7FA;
  padding-bottom: env(safe-area-inset-bottom);
}

/* 导航栏样式 */
.navbar {
  position: -webkit-sticky;
  position: sticky;
  top: 0;
  left: 0;
  right: 0;
  background: linear-gradient(135deg, #F6D365, #FDA085);
  color: #fff;
  padding: 44px 16px 15px;
  display: flex;
  align-items: center;
  z-index: 100;
  box-shadow: 0 2px 10px rgba(246, 211, 101, 0.15);
}
.navbar-back {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.back-icon {
  width: 12px;
  height: 12px;
  border-left: 2px solid #fff;
  border-bottom: 2px solid #fff;
  transform: rotate(45deg);
}
.navbar-title {
  flex: 1;
  text-align: center;
  font-size: 18px;
  font-weight: 600;
  letter-spacing: 0.5px;
}
.navbar-right {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.help-icon {
  width: 24px;
  height: 24px;
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: bold;
}

/* 通用部分样式 */
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}
.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}
.add-btn {
  display: flex;
  align-items: center;
  background: #F6D365;
  border-radius: 15px;
  padding: 5px 12px;
  color: white;
}
.btn-text {
  font-size: 13px;
  margin-right: 5px;
}
.plus-icon {
  width: 12px;
  height: 12px;
  position: relative;
}
.plus-icon:before,
.plus-icon:after {
  content: "";
  position: absolute;
  background: white;
}
.plus-icon:before {
  width: 12px;
  height: 2px;
  top: 5px;
  left: 0;
}
.plus-icon:after {
  height: 12px;
  width: 2px;
  left: 5px;
  top: 0;
}

/* 概览部分样式 */
.overview-section {
  margin: 15px;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  padding: 15px;
}
.overview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}
.date-picker {
  display: flex;
  align-items: center;
  background: #F5F7FA;
  border-radius: 15px;
  padding: 5px 10px;
}
.date-text {
  font-size: 12px;
  color: #666;
  margin-right: 5px;
}
.date-icon {
  width: 8px;
  height: 8px;
  border-top: 2px solid #666;
  border-right: 2px solid #666;
  transform: rotate(135deg);
}

/* 统计卡片样式 */
.stats-cards {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -7.5px;
}
.stats-card {
  width: 50%;
  padding: 7.5px;
  box-sizing: border-box;
  position: relative;
}
.card-value {
  font-size: 20px;
  font-weight: 600;
  color: #333;
  margin-bottom: 5px;
  background: #F8FAFC;
  padding: 15px;
  border-radius: 10px;
  border-left: 3px solid #F6D365;
}
.card-label {
  font-size: 12px;
  color: #999;
  position: absolute;
  bottom: 20px;
  left: 25px;
}
.card-trend {
  position: absolute;
  bottom: 20px;
  right: 25px;
  display: flex;
  align-items: center;
  font-size: 12px;
}
.card-trend.up {
  color: #34C759;
}
.card-trend.down {
  color: #FF3B30;
}
.trend-arrow {
  width: 0;
  height: 0;
  margin-right: 3px;
}
.card-trend.up .trend-arrow {
  border-left: 4px solid transparent;
  border-right: 4px solid transparent;
  border-bottom: 6px solid #34C759;
}
.card-trend.down .trend-arrow {
  border-left: 4px solid transparent;
  border-right: 4px solid transparent;
  border-top: 6px solid #FF3B30;
}

/* 标签页样式 */
.tabs-section {
  margin: 15px 15px 0;
}
.tabs-scroll {
  white-space: nowrap;
}
.tabs {
  display: inline-flex;
  padding: 5px 0;
}
.tab-item {
  padding: 8px 16px;
  font-size: 14px;
  color: #666;
  margin-right: 10px;
  background: #fff;
  border-radius: 20px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
  transition: all 0.3s;
}
.tab-item.active {
  background: #F6D365;
  color: white;
  box-shadow: 0 2px 8px rgba(246, 211, 101, 0.3);
}

/* 任务列表样式 */
.tasks-section {
  margin: 15px;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  padding: 15px;
}
.tasks-list {
  margin-top: 10px;
}
.task-item {
  display: flex;
  align-items: center;
  padding: 15px;
  margin-bottom: 10px;
  background: #F8FAFC;
  border-radius: 10px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}
.task-icon {
  width: 48px;
  height: 48px;
  border-radius: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
}
.svg-icon {
  width: 28px;
  height: 28px;
}
.task-content {
  flex: 1;
}
.task-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 5px;
}
.task-name {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}
.task-status {
  font-size: 12px;
  padding: 2px 8px;
  border-radius: 10px;
}
.task-status.active {
  background: rgba(52, 199, 89, 0.1);
  color: #34C759;
}
.task-status.inactive {
  background: rgba(142, 142, 147, 0.1);
  color: #8E8E93;
}
.task-desc {
  font-size: 13px;
  color: #666;
  margin-bottom: 8px;
}
.task-meta {
  display: flex;
  align-items: center;
}
.task-reward {
  display: flex;
  align-items: center;
  margin-right: 15px;
}
.reward-label {
  font-size: 12px;
  color: #999;
}
.reward-value {
  font-size: 12px;
  color: #F6D365;
  font-weight: 500;
}
.task-period {
  display: flex;
  align-items: center;
}
.period-label {
  font-size: 12px;
  color: #999;
}
.period-value {
  font-size: 12px;
  color: #666;
}
.task-action {
  margin-left: 10px;
}

/* 浮动操作按钮 */
.floating-action-button {
  position: fixed;
  bottom: 30px;
  right: 30px;
  width: 56px;
  height: 56px;
  border-radius: 28px;
  background: linear-gradient(135deg, #F6D365, #FDA085);
  box-shadow: 0 4px 15px rgba(246, 211, 101, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 100;
}
.fab-icon {
  font-size: 28px;
  color: #fff;
  font-weight: 300;
  line-height: 1;
  margin-top: -2px;
}

/* 响应式调整 */
@media screen and (max-width: 375px) {
.stats-card {
    width: 100%;
}
}