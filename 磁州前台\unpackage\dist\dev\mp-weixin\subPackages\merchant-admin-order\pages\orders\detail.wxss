
.order-detail-container {
  min-height: 100vh;
  background-color: #f5f7fa;
  padding-bottom: 80px; /* 为底部操作栏留出空间 */
}
.navbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 44px 16px 10px;
  background: linear-gradient(135deg, #1677FF, #065DD2);
  position: relative;
  z-index: 100;
}
.navbar-back {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
}
.back-icon {
  width: 12px;
  height: 12px;
  border-left: 2px solid #fff;
  border-bottom: 2px solid #fff;
  transform: rotate(45deg);
}
.navbar-title {
  color: #fff;
  font-size: 18px;
  font-weight: 600;
  flex: 1;
  text-align: center;
}
.navbar-right {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}
.more-icon {
  color: #fff;
  font-size: 24px;
  font-weight: bold;
}
.status-card {
  margin: 16px;
  border-radius: 8px;
  background-color: #fff;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.05);
  overflow: hidden;
}
.status-pending {
  border-top: 4px solid #FF9800;
}
.status-processing {
  border-top: 4px solid #2196F3;
}
.status-completed {
  border-top: 4px solid #4CAF50;
}
.status-cancelled {
  border-top: 4px solid #9E9E9E;
}
.status-refunding {
  border-top: 4px solid #F44336;
}
.status-header {
  margin-bottom: 16px;
}
.status-text {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
  display: block;
}
.status-desc {
  font-size: 14px;
  color: #666;
}
.status-timeline {
  margin-top: 24px;
}
.timeline-item {
  display: flex;
  position: relative;
  padding-bottom: 16px;
}
.timeline-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background-color: #ddd;
  margin-top: 4px;
  margin-right: 12px;
  z-index: 1;
}
.timeline-item.completed .timeline-dot {
  background-color: #4CAF50;
}
.timeline-item.current .timeline-dot {
  background-color: #1677FF;
  box-shadow: 0 0 0 4px rgba(22, 119, 255, 0.2);
}
.timeline-line {
  position: absolute;
  left: 6px;
  top: 16px;
  bottom: 0;
  width: 1px;
  background-color: #ddd;
}
.timeline-content {
  flex: 1;
}
.timeline-title {
  font-size: 14px;
  color: #333;
  font-weight: 500;
  display: block;
}
.timeline-time {
  font-size: 12px;
  color: #999;
  margin-top: 2px;
  display: block;
}
.info-card {
  margin: 16px;
  border-radius: 8px;
  background-color: #fff;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.05);
}
.card-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 16px;
  position: relative;
  padding-left: 12px;
}
.card-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 2px;
  bottom: 2px;
  width: 4px;
  background-color: #1677FF;
  border-radius: 2px;
}
.info-item {
  display: flex;
  margin-bottom: 12px;
}
.info-label {
  width: 80px;
  color: #666;
  font-size: 14px;
}
.info-value {
  flex: 1;
  font-size: 14px;
  color: #333;
}
.payment-paid {
  color: #4CAF50;
}
.payment-unpaid {
  color: #F44336;
}
.customer-profile {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
}
.customer-avatar {
  width: 48px;
  height: 48px;
  border-radius: 24px;
  margin-right: 12px;
}
.customer-info {
  flex: 1;
}
.customer-name {
  font-size: 16px;
  color: #333;
  font-weight: 500;
  display: block;
}
.customer-id {
  font-size: 12px;
  color: #999;
  margin-top: 2px;
  display: block;
}
.contact-btn {
  padding: 6px 12px;
  background-color: #e6f7ff;
  color: #1677FF;
  border-radius: 4px;
  font-size: 14px;
}
.address-info {
  background-color: #f9f9f9;
  border-radius: 4px;
  padding: 12px;
}
.address-title {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  font-size: 14px;
  color: #666;
}
.address-icon {
  margin-right: 4px;
}
.address-name {
  font-size: 14px;
  color: #333;
  font-weight: 500;
  display: block;
  margin-bottom: 4px;
}
.address-detail {
  font-size: 14px;
  color: #666;
  line-height: 1.4;
}
.product-item {
  display: flex;
  margin-bottom: 16px;
  padding-bottom: 16px;
  border-bottom: 1px solid #f0f0f0;
}
.product-item:last-child {
  margin-bottom: 0;
  padding-bottom: 0;
  border-bottom: none;
}
.product-image {
  width: 80px;
  height: 80px;
  border-radius: 4px;
  margin-right: 12px;
  background-color: #f0f0f0;
}
.product-info {
  flex: 1;
}
.product-name {
  font-size: 16px;
  color: #333;
  margin-bottom: 4px;
  display: block;
}
.product-spec {
  font-size: 12px;
  color: #999;
  margin-bottom: 8px;
  display: block;
}
.product-price-wrap {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.product-price {
  font-size: 16px;
  color: #ff6a00;
  font-weight: 600;
}
.product-quantity {
  font-size: 14px;
  color: #999;
}
.amount-info {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px dashed #eee;
}
.amount-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
}
.amount-label {
  font-size: 14px;
  color: #666;
}
.amount-value {
  font-size: 14px;
  color: #333;
}
.amount-item.discount .amount-value {
  color: #ff6a00;
}
.amount-item.total {
  margin-top: 12px;
  padding-top: 12px;
  border-top: 1px solid #f0f0f0;
}
.amount-item.total .amount-label,
.amount-item.total .amount-value {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}
.remark-content {
  font-size: 14px;
  color: #666;
  line-height: 1.5;
}
.log-item {
  display: flex;
  margin-bottom: 12px;
}
.log-time {
  width: 140px;
  font-size: 12px;
  color: #999;
}
.log-content {
  flex: 1;
  font-size: 14px;
  color: #333;
}
.action-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  padding: 12px 16px;
  background-color: #fff;
  box-shadow: 0 -2px 8px rgba(0,0,0,0.05);
  z-index: 100;
}
.action-btn {
  flex: 1;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 6px;
  border-radius: 22px;
  font-size: 16px;
  background-color: #f0f0f0;
  color: #333;
}
.action-btn.primary {
  background-color: #1677FF;
  color: #fff;
}
