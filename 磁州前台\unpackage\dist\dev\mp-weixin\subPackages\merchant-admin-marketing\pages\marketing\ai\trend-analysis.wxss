/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 移除阿里妈妈字体引用，改用系统默认字体 */
/* 移除原有阿里妈妈字体定义
$uni-font-family: 'AlimamaShuHeiTi';
$uni-font-family-text: 'AlimamaShuHeiTi', -apple-system, BlinkMacSystemFont, 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;
*/
body, html, #app, .index-container {
  font-family: "AlimamaShuHeiTi", -apple-system, BlinkMacSystemFont, "Helvetica Neue", "PingFang SC", "Microsoft YaHei", sans-serif;
}
.trend-container {
  min-height: 100vh;
  background-color: #F5F7FA;
}
.navbar {
  position: -webkit-sticky;
  position: sticky;
  top: 0;
  left: 0;
  right: 0;
  background: linear-gradient(135deg, #6366F1, #4F46E5);
  color: #fff;
  padding: 44px 16px 15px;
  display: flex;
  align-items: center;
  z-index: 100;
  box-shadow: 0 2px 10px rgba(99, 102, 241, 0.15);
}
.navbar-back {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.back-icon {
  width: 12px;
  height: 12px;
  border-left: 2px solid #fff;
  border-bottom: 2px solid #fff;
  transform: rotate(45deg);
}
.navbar-title {
  flex: 1;
  text-align: center;
  font-size: 18px;
  font-weight: 600;
  letter-spacing: 0.5px;
}
.navbar-right {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.refresh-icon {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.page-content {
  height: calc(100vh - 77px);
  box-sizing: border-box;
  padding: 15px;
}

/* 数据概览部分样式 */
.overview-section {
  background-color: #FFFFFF;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  padding: 20px;
  margin-bottom: 15px;
}
.overview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}
.header-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}
.header-subtitle {
  font-size: 12px;
  color: #999;
  margin-top: 4px;
  display: block;
}
.date-selector {
  display: flex;
  align-items: center;
  background-color: #F5F7FA;
  padding: 6px 12px;
  border-radius: 16px;
}
.date-text {
  font-size: 12px;
  color: #666;
  margin-right: 5px;
}
.date-icon {
  display: flex;
  align-items: center;
}
.metrics-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 15px;
}
.metric-card {
  background-color: #F8FAFC;
  border-radius: 12px;
  padding: 15px;
  display: flex;
  align-items: flex-start;
}
.metric-icon {
  width: 40px;
  height: 40px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
}
.metric-icon.sales {
  background-color: rgba(99, 102, 241, 0.1);
}
.metric-icon.orders {
  background-color: rgba(255, 149, 0, 0.1);
}
.metric-icon.customers {
  background-color: rgba(52, 199, 89, 0.1);
}
.metric-icon.average {
  background-color: rgba(255, 59, 48, 0.1);
}
.metric-content {
  flex: 1;
}
.metric-label {
  font-size: 12px;
  color: #666;
  margin-bottom: 5px;
  display: block;
}
.metric-value-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.metric-value {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}
.metric-trend {
  display: flex;
  align-items: center;
}
.trend-icon {
  width: 0;
  height: 0;
  margin-right: 2px;
}
.metric-trend.up .trend-icon {
  border-left: 4px solid transparent;
  border-right: 4px solid transparent;
  border-bottom: 6px solid #34C759;
}
.metric-trend.down .trend-icon {
  border-left: 4px solid transparent;
  border-right: 4px solid transparent;
  border-top: 6px solid #FF3B30;
}
.trend-value {
  font-size: 12px;
}
.metric-trend.up .trend-value {
  color: #34C759;
}
.metric-trend.down .trend-value {
  color: #FF3B30;
}
.summary-section {
  margin: 15px;
  padding: 20px;
  background: #FFFFFF;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  position: relative;
}
.summary-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}
.summary-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}
.date-range {
  display: flex;
  align-items: center;
  background: #F5F7FA;
  border-radius: 15px;
  padding: 5px 10px;
}
.date-text {
  font-size: 12px;
  color: #666;
  margin-right: 5px;
}
.date-icon {
  width: 8px;
  height: 8px;
  border-top: 2px solid #666;
  border-right: 2px solid #666;
  transform: rotate(135deg);
}
.summary-content {
  margin-bottom: 20px;
}
.summary-text {
  font-size: 14px;
  color: #666;
  line-height: 1.6;
}
.ai-badge {
  position: absolute;
  top: 20px;
  right: 20px;
  background: linear-gradient(135deg, #1989FA, #0D6EFD);
  border-radius: 15px;
  padding: 5px 10px;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.badge-text {
  font-size: 12px;
  font-weight: 600;
  color: #FFFFFF;
}
.badge-confidence {
  font-size: 10px;
  color: rgba(255, 255, 255, 0.8);
}
.chart-section, .profile-section, .recommendations-section {
  margin: 15px;
  padding: 20px;
  background: #FFFFFF;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}
.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}
.section-subtitle {
  font-size: 12px;
  color: #999;
  margin-top: 4px;
  display: block;
}
.filter-dropdown {
  display: flex;
  align-items: center;
  background: #F5F7FA;
  border-radius: 15px;
  padding: 5px 10px;
}
.selected-value {
  font-size: 12px;
  color: #666;
  margin-right: 5px;
}
.dropdown-arrow {
  width: 8px;
  height: 8px;
  border-top: 2px solid #666;
  border-right: 2px solid #666;
  transform: rotate(135deg);
}
.chart-container {
  height: 250px;
  position: relative;
}
.chart-placeholder {
  height: 100%;
  display: flex;
  flex-direction: column;
}
.chart-bars {
  flex: 1;
  display: flex;
  justify-content: space-around;
  align-items: flex-end;
  padding: 0 10px;
}
.chart-bar {
  width: 40px;
  background: linear-gradient(to top, #1989FA, #0D6EFD);
  border-radius: 5px 5px 0 0;
  position: relative;
  display: flex;
  justify-content: center;
}
.bar-label {
  position: absolute;
  bottom: -25px;
  font-size: 12px;
  color: #666;
}
.chart-legend {
  display: flex;
  justify-content: center;
  margin-top: 30px;
}
.legend-item {
  display: flex;
  align-items: center;
}
.legend-color {
  width: 12px;
  height: 12px;
  background: linear-gradient(to right, #1989FA, #0D6EFD);
  border-radius: 2px;
  margin-right: 5px;
}
.legend-text {
  font-size: 12px;
  color: #666;
}
.profile-cards {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -7.5px;
}
.profile-card {
  width: 50%;
  padding: 7.5px;
  box-sizing: border-box;
  display: flex;
  align-items: flex-start;
}
.profile-icon {
  width: 40px;
  height: 40px;
  border-radius: 20px;
  margin-right: 10px;
  position: relative;
}
.profile-icon.gender-female {
  background-color: rgba(255, 105, 180, 0.1);
}
.profile-icon.gender-female::before {
  content: "♀";
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 24px;
  color: #FF69B4;
}
.profile-icon.gender-male {
  background-color: rgba(25, 137, 250, 0.1);
}
.profile-icon.gender-male::before {
  content: "♂";
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 24px;
  color: #1989FA;
}
.profile-content {
  flex: 1;
}
.profile-title {
  font-size: 14px;
  font-weight: 600;
  color: #333;
  margin-bottom: 5px;
}
.profile-value {
  font-size: 14px;
  color: #666;
  margin-bottom: 5px;
}
.trend-up {
  color: #34C759;
}
.trend-down {
  color: #FF3B30;
}
.profile-tags {
  display: flex;
  flex-wrap: wrap;
}
.profile-tag {
  font-size: 10px;
  color: #666;
  background: #F5F7FA;
  padding: 2px 6px;
  border-radius: 10px;
  margin-right: 5px;
  margin-bottom: 5px;
}
.recommendation-list {
  margin-top: 10px;
}
.recommendation-item {
  display: flex;
  align-items: flex-start;
  padding: 15px;
  margin-bottom: 15px;
  background: #F8FAFC;
  border-radius: 10px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.03);
}
.recommendation-icon {
  width: 40px;
  height: 40px;
  border-radius: 20px;
  margin-right: 15px;
  position: relative;
}
.recommendation-icon.stock {
  background-color: rgba(25, 137, 250, 0.1);
}
.recommendation-icon.stock::before {
  content: "📦";
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 20px;
}
.recommendation-icon.promotion {
  background-color: rgba(255, 149, 0, 0.1);
}
.recommendation-icon.promotion::before {
  content: "🏷️";
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 20px;
}
.recommendation-icon.product {
  background-color: rgba(52, 199, 89, 0.1);
}
.recommendation-icon.product::before {
  content: "🛍️";
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 20px;
}
.recommendation-content {
  flex: 1;
}
.recommendation-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 5px;
}
.recommendation-desc {
  font-size: 14px;
  color: #666;
  line-height: 1.5;
  margin-bottom: 10px;
}
.recommendation-actions {
  display: flex;
}
.action-btn {
  padding: 5px 15px;
  border-radius: 15px;
  font-size: 12px;
  margin-right: 10px;
  border: none;
}
.action-btn.apply {
  background: #1989FA;
  color: white;
}
.action-btn.ignore {
  background: #F5F7FA;
  color: #666;
}
.chart-tabs {
  display: flex;
  align-items: center;
}
.chart-tab {
  padding: 5px 10px;
  border-radius: 15px;
  background: #F5F7FA;
  margin-left: 5px;
  cursor: pointer;
}
.chart-tab.active {
  background: #1989FA;
  color: white;
}
.line-chart {
  position: relative;
  height: 180px;
  margin: 20px 0;
}
.chart-grid {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}
.grid-line {
  position: absolute;
  width: 100%;
  height: 1px;
  background: #E0E0E0;
}
.grid-line:nth-child(1) {
  top: 0;
}
.grid-line:nth-child(2) {
  top: 25%;
}
.grid-line:nth-child(3) {
  top: 50%;
}
.grid-line:nth-child(4) {
  top: 75%;
}
.grid-line:nth-child(5) {
  top: 100%;
}
.chart-line {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}
.legend-color.sales-line {
  background-color: #6366F1;
}
.legend-color.orders-line {
  background-color: #FF9500;
}
.x-axis {
  display: flex;
  justify-content: space-between;
  padding: 0 10px;
  margin-top: 10px;
}
.x-axis-label {
  font-size: 12px;
  color: #999;
  text-align: center;
  flex: 1;
}
.chart-stats {
  display: flex;
  justify-content: space-around;
  margin-top: 20px;
  padding-top: 15px;
  border-top: 1px solid #EEEEEE;
}
.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}
.stat-label {
  font-size: 12px;
  color: #666;
  margin-bottom: 5px;
}
.stat-value {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}