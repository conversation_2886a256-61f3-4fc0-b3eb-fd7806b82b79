{"version": 3, "file": "publishList.js", "sources": ["mock/service/publishList.js"], "sourcesContent": ["// 服务发布模拟数据\r\nexport const publishList = [\r\n  // 招聘信息数据 - 新增\r\n  {\r\n    id: 'job-001',\r\n    content: '招聘销售人员，底薪3000+提成，有社保，包吃住，有销售经验者优先',\r\n    category: '招聘信息',\r\n    subcategory: '销售',\r\n    area: '城区',\r\n    time: '2025-06-29 10:30',\r\n    views: 120,\r\n    price: '3000-8000元/月',\r\n    images: [\r\n      '/static/images/job/sales1.jpg',\r\n      '/static/images/job/sales2.jpg'\r\n    ],\r\n    contact: {\r\n      name: '王经理',\r\n      phone: '13888888888',\r\n      wechat: 'wangjl123'\r\n    }\r\n  },\r\n  {\r\n    id: 'job-002',\r\n    content: '餐厅招聘服务员数名，男女不限，有经验者优先，包吃住，月休4天',\r\n    category: '招聘信息',\r\n    subcategory: '服务员',\r\n    area: '磁州镇',\r\n    time: '2025-06-29 09:45',\r\n    views: 85,\r\n    price: '3500-4500元/月',\r\n    images: [\r\n      '/static/images/job/waiter1.jpg'\r\n    ],\r\n    contact: {\r\n      name: '李店长',\r\n      phone: '13777777777',\r\n      wechat: 'lidz456'\r\n    }\r\n  },\r\n  {\r\n    id: 'job-003',\r\n    content: '招聘电工2名，持证上岗，有3年以上工作经验，待遇从优',\r\n    category: '招聘信息',\r\n    subcategory: '技工',\r\n    area: '岳城镇',\r\n    time: '2025-06-29 08:20',\r\n    views: 65,\r\n    price: '5000-7000元/月',\r\n    images: [\r\n      '/static/images/job/electrician1.jpg',\r\n      '/static/images/job/electrician2.jpg'\r\n    ],\r\n    contact: {\r\n      name: '张总',\r\n      phone: '13666666666',\r\n      wechat: 'zhangzong789'\r\n    }\r\n  },\r\n  {\r\n    id: 'job-004',\r\n    content: '物流公司招聘货车司机，C1以上驾照，熟悉本地路线，薪资高',\r\n    category: '招聘信息',\r\n    subcategory: '司机',\r\n    area: '城区',\r\n    time: '2025-06-28 21:15',\r\n    views: 98,\r\n    price: '6000-8000元/月',\r\n    images: [\r\n      '/static/images/job/driver1.jpg'\r\n    ],\r\n    contact: {\r\n      name: '赵经理',\r\n      phone: '13555555555',\r\n      wechat: 'zhaojl135'\r\n    }\r\n  },\r\n  {\r\n    id: 'job-005',\r\n    content: '餐厅招聘厨师，有经验，能独立操作，包吃住，待遇从优',\r\n    category: '招聘信息',\r\n    subcategory: '厨师',\r\n    area: '讲武城镇',\r\n    time: '2025-06-28 20:30',\r\n    views: 72,\r\n    price: '5000-8000元/月',\r\n    images: [\r\n      '/static/images/job/chef1.jpg',\r\n      '/static/images/job/chef2.jpg'\r\n    ],\r\n    contact: {\r\n      name: '陈师傅',\r\n      phone: '13444444444',\r\n      wechat: 'chensf246'\r\n    }\r\n  },\r\n  {\r\n    id: 'publish-001',\r\n    content: '专业水电维修，经验丰富，价格合理，服务周到',\r\n    category: '维修服务',\r\n    subcategory: '水电维修',\r\n    area: '城区',\r\n    time: '2025-06-29 08:08',\r\n    views: 75,\r\n    price: '面议',\r\n    images: [\r\n      '/static/images/service/repair1.jpg',\r\n      '/static/images/service/repair2.jpg'\r\n    ],\r\n    contact: {\r\n      name: '刘师傅',\r\n      phone: '13800138000',\r\n      wechat: 'liushifu123'\r\n    }\r\n  },\r\n  {\r\n    id: 'publish-002',\r\n    content: '专业瓷砖铺设，有经验者优先，价格面议',\r\n    category: '装修服务',\r\n    subcategory: '瓷砖铺设',\r\n    area: '城区',\r\n    time: '2025-06-29 03:08',\r\n    views: 62,\r\n    price: '面议',\r\n    images: [\r\n      '/static/images/service/tile1.jpg',\r\n      '/static/images/service/tile2.jpg',\r\n      '/static/images/service/tile3.jpg'\r\n    ],\r\n    contact: {\r\n      name: '张师傅',\r\n      phone: '13900139000',\r\n      wechat: 'zhangshifu456'\r\n    }\r\n  },\r\n  {\r\n    id: 'publish-003',\r\n    content: '专业家政保洁，开荒保洁，日常保洁，钟点工，擦玻璃',\r\n    category: '家政服务',\r\n    subcategory: '保洁服务',\r\n    area: '磁州镇',\r\n    time: '2025-06-28 15:30',\r\n    views: 88,\r\n    price: '50元/小时',\r\n    images: [\r\n      '/static/images/service/cleaning1.jpg',\r\n      '/static/images/service/cleaning2.jpg'\r\n    ],\r\n    contact: {\r\n      name: '李阿姨',\r\n      phone: '13700137000',\r\n      wechat: 'liayi789'\r\n    }\r\n  },\r\n  {\r\n    id: 'publish-004',\r\n    content: '专业搬家服务，价格合理，服务周到，小型搬家，大型搬家均可',\r\n    category: '搬家拉货',\r\n    subcategory: '居民搬家',\r\n    area: '讲武城镇',\r\n    time: '2025-06-28 14:25',\r\n    views: 56,\r\n    price: '200元起',\r\n    images: [\r\n      '/static/images/service/moving1.jpg',\r\n      '/static/images/service/moving2.jpg',\r\n      '/static/images/service/moving3.jpg'\r\n    ],\r\n    contact: {\r\n      name: '王师傅',\r\n      phone: '13600136000',\r\n      wechat: 'wangshifu321'\r\n    }\r\n  },\r\n  {\r\n    id: 'publish-005',\r\n    content: '专业开锁换锁，汽车开锁，保险柜开锁，24小时服务',\r\n    category: '开锁换锁',\r\n    subcategory: '开锁服务',\r\n    area: '城区',\r\n    time: '2025-06-28 12:18',\r\n    views: 102,\r\n    price: '80元起',\r\n    images: [\r\n      '/static/images/service/lock1.jpg'\r\n    ],\r\n    contact: {\r\n      name: '赵师傅',\r\n      phone: '13500135000',\r\n      wechat: 'zhaoshifu654'\r\n    }\r\n  },\r\n  {\r\n    id: 'publish-006',\r\n    content: '专业上门按摩，推拿，足疗，中医理疗，舒缓疲劳',\r\n    category: '上门美容',\r\n    subcategory: '按摩推拿',\r\n    area: '岳城镇',\r\n    time: '2025-06-28 10:45',\r\n    views: 135,\r\n    price: '120元/小时',\r\n    images: [\r\n      '/static/images/service/massage1.jpg',\r\n      '/static/images/service/massage2.jpg'\r\n    ],\r\n    contact: {\r\n      name: '陈技师',\r\n      phone: '13400134000',\r\n      wechat: 'chenjishi987'\r\n    }\r\n  },\r\n  {\r\n    id: 'publish-007',\r\n    content: '专业上门家教，小学初中高中辅导，提分快，有经验',\r\n    category: '上门家教',\r\n    subcategory: '学科辅导',\r\n    area: '观台镇',\r\n    time: '2025-06-28 09:30',\r\n    views: 78,\r\n    price: '100元/小时',\r\n    images: [\r\n      '/static/images/service/tutor1.jpg',\r\n      '/static/images/service/tutor2.jpg'\r\n    ],\r\n    contact: {\r\n      name: '孙老师',\r\n      phone: '13300133000',\r\n      wechat: 'sunlaoshi246'\r\n    }\r\n  },\r\n  {\r\n    id: 'publish-008',\r\n    content: '专业宠物美容，洗澡，造型，修剪，疫苗接种，寄养',\r\n    category: '宠物服务',\r\n    subcategory: '宠物美容',\r\n    area: '白土镇',\r\n    time: '2025-06-28 08:15',\r\n    views: 92,\r\n    price: '80元起',\r\n    images: [\r\n      '/static/images/service/pet1.jpg',\r\n      '/static/images/service/pet2.jpg',\r\n      '/static/images/service/pet3.jpg'\r\n    ],\r\n    contact: {\r\n      name: '钱师傅',\r\n      phone: '13200132000',\r\n      wechat: 'qianshifu135'\r\n    }\r\n  },\r\n  {\r\n    id: 'publish-009',\r\n    content: '专业管道疏通，马桶疏通，下水道疏通，厨房疏通，快速上门',\r\n    category: '上门疏通',\r\n    subcategory: '管道疏通',\r\n    area: '黄沙镇',\r\n    time: '2025-06-28 07:20',\r\n    views: 68,\r\n    price: '60元起',\r\n    images: [\r\n      '/static/images/service/plumbing1.jpg',\r\n      '/static/images/service/plumbing2.jpg'\r\n    ],\r\n    contact: {\r\n      name: '周师傅',\r\n      phone: '13100131000',\r\n      wechat: 'zhoushifu579'\r\n    }\r\n  },\r\n  {\r\n    id: 'publish-010',\r\n    content: '专业家电安装，空调安装，电视安装，洗衣机安装，热水器安装',\r\n    category: '上门安装',\r\n    subcategory: '家电安装',\r\n    area: '城区',\r\n    time: '2025-06-28 06:10',\r\n    views: 110,\r\n    price: '100元起',\r\n    images: [\r\n      '/static/images/service/installation1.jpg',\r\n      '/static/images/service/installation2.jpg',\r\n      '/static/images/service/installation3.jpg'\r\n    ],\r\n    contact: {\r\n      name: '吴师傅',\r\n      phone: '13000130000',\r\n      wechat: 'wushifu468'\r\n    }\r\n  }\r\n];\r\n\r\n// 获取服务发布列表的API函数\r\nexport const fetchPublishList = (params = {}) => {\r\n  return new Promise((resolve) => {\r\n    setTimeout(() => {\r\n      let result = [...publishList];\r\n      \r\n      // 按类型筛选\r\n      if (params.category && params.category !== '全部分类') {\r\n        result = result.filter(item => item.category === params.category);\r\n      }\r\n      \r\n      // 按子类型筛选\r\n      if (params.subcategory) {\r\n        result = result.filter(item => item.subcategory === params.subcategory);\r\n      }\r\n      \r\n      // 按区域筛选\r\n      if (params.area && params.area !== '全部区域') {\r\n        result = result.filter(item => item.area === params.area);\r\n      }\r\n      \r\n      // 按关键词搜索\r\n      if (params.keyword) {\r\n        const keyword = params.keyword.toLowerCase();\r\n        result = result.filter(item => \r\n          item.content.toLowerCase().includes(keyword) || \r\n          item.category.toLowerCase().includes(keyword) || \r\n          (item.subcategory && item.subcategory.toLowerCase().includes(keyword))\r\n        );\r\n      }\r\n      \r\n      // 排序\r\n      if (params.sort) {\r\n        switch (params.sort) {\r\n          case '价格最低':\r\n            result.sort((a, b) => {\r\n              const priceA = parseFloat(a.price) || 999999;\r\n              const priceB = parseFloat(b.price) || 999999;\r\n              return priceA - priceB;\r\n            });\r\n            break;\r\n          case '价格最高':\r\n            result.sort((a, b) => {\r\n              const priceA = parseFloat(a.price) || 0;\r\n              const priceB = parseFloat(b.price) || 0;\r\n              return priceB - priceA;\r\n            });\r\n            break;\r\n          case '距离最近':\r\n            // 模拟距离排序，实际应该根据用户位置计算\r\n            result.sort((a, b) => {\r\n              // 这里简单地按区域字母排序作为示例\r\n              return a.area.localeCompare(b.area);\r\n            });\r\n            break;\r\n          default: // 默认排序 - 按时间\r\n            result.sort((a, b) => new Date(b.time) - new Date(a.time));\r\n        }\r\n      } else {\r\n        // 默认按时间排序\r\n        result.sort((a, b) => new Date(b.time) - new Date(a.time));\r\n      }\r\n      \r\n      // 分页处理\r\n      const page = params.page || 1;\r\n      const pageSize = params.pageSize || 10;\r\n      const start = (page - 1) * pageSize;\r\n      const end = start + pageSize;\r\n      const data = result.slice(start, end);\r\n      \r\n      // 返回数据和分页信息\r\n      resolve({\r\n        list: data,\r\n        total: result.length,\r\n        hasMore: end < result.length\r\n      });\r\n    }, 500);\r\n  });\r\n};\r\n\r\n// 获取服务发布详情的API函数\r\nexport const fetchPublishDetail = (id) => {\r\n  return new Promise((resolve) => {\r\n    setTimeout(() => {\r\n      const detail = publishList.find(item => item.id === id);\r\n      \r\n      if (detail) {\r\n        // 增加浏览量\r\n        detail.views += 1;\r\n      }\r\n      \r\n      resolve(detail || null);\r\n    }, 300);\r\n  });\r\n}; "], "names": [], "mappings": ";AACY,MAAC,cAAc;AAAA;AAAA,EAEzB;AAAA,IACE,IAAI;AAAA,IACJ,SAAS;AAAA,IACT,UAAU;AAAA,IACV,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,OAAO;AAAA,IACP,OAAO;AAAA,IACP,QAAQ;AAAA,MACN;AAAA,MACA;AAAA,IACD;AAAA,IACD,SAAS;AAAA,MACP,MAAM;AAAA,MACN,OAAO;AAAA,MACP,QAAQ;AAAA,IACT;AAAA,EACF;AAAA,EACD;AAAA,IACE,IAAI;AAAA,IACJ,SAAS;AAAA,IACT,UAAU;AAAA,IACV,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,OAAO;AAAA,IACP,OAAO;AAAA,IACP,QAAQ;AAAA,MACN;AAAA,IACD;AAAA,IACD,SAAS;AAAA,MACP,MAAM;AAAA,MACN,OAAO;AAAA,MACP,QAAQ;AAAA,IACT;AAAA,EACF;AAAA,EACD;AAAA,IACE,IAAI;AAAA,IACJ,SAAS;AAAA,IACT,UAAU;AAAA,IACV,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,OAAO;AAAA,IACP,OAAO;AAAA,IACP,QAAQ;AAAA,MACN;AAAA,MACA;AAAA,IACD;AAAA,IACD,SAAS;AAAA,MACP,MAAM;AAAA,MACN,OAAO;AAAA,MACP,QAAQ;AAAA,IACT;AAAA,EACF;AAAA,EACD;AAAA,IACE,IAAI;AAAA,IACJ,SAAS;AAAA,IACT,UAAU;AAAA,IACV,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,OAAO;AAAA,IACP,OAAO;AAAA,IACP,QAAQ;AAAA,MACN;AAAA,IACD;AAAA,IACD,SAAS;AAAA,MACP,MAAM;AAAA,MACN,OAAO;AAAA,MACP,QAAQ;AAAA,IACT;AAAA,EACF;AAAA,EACD;AAAA,IACE,IAAI;AAAA,IACJ,SAAS;AAAA,IACT,UAAU;AAAA,IACV,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,OAAO;AAAA,IACP,OAAO;AAAA,IACP,QAAQ;AAAA,MACN;AAAA,MACA;AAAA,IACD;AAAA,IACD,SAAS;AAAA,MACP,MAAM;AAAA,MACN,OAAO;AAAA,MACP,QAAQ;AAAA,IACT;AAAA,EACF;AAAA,EACD;AAAA,IACE,IAAI;AAAA,IACJ,SAAS;AAAA,IACT,UAAU;AAAA,IACV,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,OAAO;AAAA,IACP,OAAO;AAAA,IACP,QAAQ;AAAA,MACN;AAAA,MACA;AAAA,IACD;AAAA,IACD,SAAS;AAAA,MACP,MAAM;AAAA,MACN,OAAO;AAAA,MACP,QAAQ;AAAA,IACT;AAAA,EACF;AAAA,EACD;AAAA,IACE,IAAI;AAAA,IACJ,SAAS;AAAA,IACT,UAAU;AAAA,IACV,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,OAAO;AAAA,IACP,OAAO;AAAA,IACP,QAAQ;AAAA,MACN;AAAA,MACA;AAAA,MACA;AAAA,IACD;AAAA,IACD,SAAS;AAAA,MACP,MAAM;AAAA,MACN,OAAO;AAAA,MACP,QAAQ;AAAA,IACT;AAAA,EACF;AAAA,EACD;AAAA,IACE,IAAI;AAAA,IACJ,SAAS;AAAA,IACT,UAAU;AAAA,IACV,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,OAAO;AAAA,IACP,OAAO;AAAA,IACP,QAAQ;AAAA,MACN;AAAA,MACA;AAAA,IACD;AAAA,IACD,SAAS;AAAA,MACP,MAAM;AAAA,MACN,OAAO;AAAA,MACP,QAAQ;AAAA,IACT;AAAA,EACF;AAAA,EACD;AAAA,IACE,IAAI;AAAA,IACJ,SAAS;AAAA,IACT,UAAU;AAAA,IACV,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,OAAO;AAAA,IACP,OAAO;AAAA,IACP,QAAQ;AAAA,MACN;AAAA,MACA;AAAA,MACA;AAAA,IACD;AAAA,IACD,SAAS;AAAA,MACP,MAAM;AAAA,MACN,OAAO;AAAA,MACP,QAAQ;AAAA,IACT;AAAA,EACF;AAAA,EACD;AAAA,IACE,IAAI;AAAA,IACJ,SAAS;AAAA,IACT,UAAU;AAAA,IACV,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,OAAO;AAAA,IACP,OAAO;AAAA,IACP,QAAQ;AAAA,MACN;AAAA,IACD;AAAA,IACD,SAAS;AAAA,MACP,MAAM;AAAA,MACN,OAAO;AAAA,MACP,QAAQ;AAAA,IACT;AAAA,EACF;AAAA,EACD;AAAA,IACE,IAAI;AAAA,IACJ,SAAS;AAAA,IACT,UAAU;AAAA,IACV,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,OAAO;AAAA,IACP,OAAO;AAAA,IACP,QAAQ;AAAA,MACN;AAAA,MACA;AAAA,IACD;AAAA,IACD,SAAS;AAAA,MACP,MAAM;AAAA,MACN,OAAO;AAAA,MACP,QAAQ;AAAA,IACT;AAAA,EACF;AAAA,EACD;AAAA,IACE,IAAI;AAAA,IACJ,SAAS;AAAA,IACT,UAAU;AAAA,IACV,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,OAAO;AAAA,IACP,OAAO;AAAA,IACP,QAAQ;AAAA,MACN;AAAA,MACA;AAAA,IACD;AAAA,IACD,SAAS;AAAA,MACP,MAAM;AAAA,MACN,OAAO;AAAA,MACP,QAAQ;AAAA,IACT;AAAA,EACF;AAAA,EACD;AAAA,IACE,IAAI;AAAA,IACJ,SAAS;AAAA,IACT,UAAU;AAAA,IACV,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,OAAO;AAAA,IACP,OAAO;AAAA,IACP,QAAQ;AAAA,MACN;AAAA,MACA;AAAA,MACA;AAAA,IACD;AAAA,IACD,SAAS;AAAA,MACP,MAAM;AAAA,MACN,OAAO;AAAA,MACP,QAAQ;AAAA,IACT;AAAA,EACF;AAAA,EACD;AAAA,IACE,IAAI;AAAA,IACJ,SAAS;AAAA,IACT,UAAU;AAAA,IACV,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,OAAO;AAAA,IACP,OAAO;AAAA,IACP,QAAQ;AAAA,MACN;AAAA,MACA;AAAA,IACD;AAAA,IACD,SAAS;AAAA,MACP,MAAM;AAAA,MACN,OAAO;AAAA,MACP,QAAQ;AAAA,IACT;AAAA,EACF;AAAA,EACD;AAAA,IACE,IAAI;AAAA,IACJ,SAAS;AAAA,IACT,UAAU;AAAA,IACV,aAAa;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,OAAO;AAAA,IACP,OAAO;AAAA,IACP,QAAQ;AAAA,MACN;AAAA,MACA;AAAA,MACA;AAAA,IACD;AAAA,IACD,SAAS;AAAA,MACP,MAAM;AAAA,MACN,OAAO;AAAA,MACP,QAAQ;AAAA,IACT;AAAA,EACF;AACH;AAGY,MAAC,mBAAmB,CAAC,SAAS,OAAO;AAC/C,SAAO,IAAI,QAAQ,CAAC,YAAY;AAC9B,eAAW,MAAM;AACf,UAAI,SAAS,CAAC,GAAG,WAAW;AAG5B,UAAI,OAAO,YAAY,OAAO,aAAa,QAAQ;AACjD,iBAAS,OAAO,OAAO,UAAQ,KAAK,aAAa,OAAO,QAAQ;AAAA,MACjE;AAGD,UAAI,OAAO,aAAa;AACtB,iBAAS,OAAO,OAAO,UAAQ,KAAK,gBAAgB,OAAO,WAAW;AAAA,MACvE;AAGD,UAAI,OAAO,QAAQ,OAAO,SAAS,QAAQ;AACzC,iBAAS,OAAO,OAAO,UAAQ,KAAK,SAAS,OAAO,IAAI;AAAA,MACzD;AAGD,UAAI,OAAO,SAAS;AAClB,cAAM,UAAU,OAAO,QAAQ,YAAW;AAC1C,iBAAS,OAAO;AAAA,UAAO,UACrB,KAAK,QAAQ,cAAc,SAAS,OAAO,KAC3C,KAAK,SAAS,cAAc,SAAS,OAAO,KAC3C,KAAK,eAAe,KAAK,YAAY,cAAc,SAAS,OAAO;AAAA,QAC9E;AAAA,MACO;AAGD,UAAI,OAAO,MAAM;AACf,gBAAQ,OAAO,MAAI;AAAA,UACjB,KAAK;AACH,mBAAO,KAAK,CAAC,GAAG,MAAM;AACpB,oBAAM,SAAS,WAAW,EAAE,KAAK,KAAK;AACtC,oBAAM,SAAS,WAAW,EAAE,KAAK,KAAK;AACtC,qBAAO,SAAS;AAAA,YAC9B,CAAa;AACD;AAAA,UACF,KAAK;AACH,mBAAO,KAAK,CAAC,GAAG,MAAM;AACpB,oBAAM,SAAS,WAAW,EAAE,KAAK,KAAK;AACtC,oBAAM,SAAS,WAAW,EAAE,KAAK,KAAK;AACtC,qBAAO,SAAS;AAAA,YAC9B,CAAa;AACD;AAAA,UACF,KAAK;AAEH,mBAAO,KAAK,CAAC,GAAG,MAAM;AAEpB,qBAAO,EAAE,KAAK,cAAc,EAAE,IAAI;AAAA,YAChD,CAAa;AACD;AAAA,UACF;AACE,mBAAO,KAAK,CAAC,GAAG,MAAM,IAAI,KAAK,EAAE,IAAI,IAAI,IAAI,KAAK,EAAE,IAAI,CAAC;AAAA,QAC5D;AAAA,MACT,OAAa;AAEL,eAAO,KAAK,CAAC,GAAG,MAAM,IAAI,KAAK,EAAE,IAAI,IAAI,IAAI,KAAK,EAAE,IAAI,CAAC;AAAA,MAC1D;AAGD,YAAM,OAAO,OAAO,QAAQ;AAC5B,YAAM,WAAW,OAAO,YAAY;AACpC,YAAM,SAAS,OAAO,KAAK;AAC3B,YAAM,MAAM,QAAQ;AACpB,YAAM,OAAO,OAAO,MAAM,OAAO,GAAG;AAGpC,cAAQ;AAAA,QACN,MAAM;AAAA,QACN,OAAO,OAAO;AAAA,QACd,SAAS,MAAM,OAAO;AAAA,MAC9B,CAAO;AAAA,IACF,GAAE,GAAG;AAAA,EACV,CAAG;AACH;AAGY,MAAC,qBAAqB,CAAC,OAAO;AACxC,SAAO,IAAI,QAAQ,CAAC,YAAY;AAC9B,eAAW,MAAM;AACf,YAAM,SAAS,YAAY,KAAK,UAAQ,KAAK,OAAO,EAAE;AAEtD,UAAI,QAAQ;AAEV,eAAO,SAAS;AAAA,MACjB;AAED,cAAQ,UAAU,IAAI;AAAA,IACvB,GAAE,GAAG;AAAA,EACV,CAAG;AACH;;;;"}