{"version": 3, "file": "detail.js", "sources": ["subPackages/payment/pages/detail.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/c3ViUGFja2FnZXNccGF5bWVudFxwYWdlc1xkZXRhaWwudnVl"], "sourcesContent": ["<template>\r\n  <view class=\"wallet-detail-container\">\r\n    <!-- 自定义导航栏 -->\r\n    <view class=\"custom-navbar\" :style=\"{ paddingTop: statusBarHeight + 'px' }\">\r\n      <view class=\"navbar-left\" @click=\"goBack\">\r\n        <image src=\"/static/images/tabbar/返回键.png\" class=\"back-icon\"></image>\r\n      </view>\r\n      <view class=\"navbar-title\">钱包明细</view>\r\n      <view class=\"navbar-right\"></view>\r\n    </view>\r\n    \r\n    <!-- 账户余额卡片 -->\r\n    <view class=\"balance-card\" :style=\"{ marginTop: (navbarHeight + 10) + 'px' }\">\r\n      <view class=\"balance-title\">账户余额 (元)</view>\r\n      <view class=\"balance-amount\">{{ balanceInfo.amount.toFixed(2) }}</view>\r\n      <view class=\"balance-info\">\r\n        <view class=\"info-item\">\r\n          <view class=\"info-value\">{{ balanceInfo.totalIncome.toFixed(2) }}</view>\r\n          <view class=\"info-label\">总收入</view>\r\n        </view>\r\n        <view class=\"info-divider\"></view>\r\n        <view class=\"info-item\">\r\n          <view class=\"info-value\">{{ balanceInfo.totalExpense.toFixed(2) }}</view>\r\n          <view class=\"info-label\">总支出</view>\r\n        </view>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 交易类型筛选 -->\r\n    <view class=\"filter-tabs\">\r\n      <view \r\n        class=\"filter-tab\" \r\n        v-for=\"(tab, index) in tabs\" \r\n        :key=\"index\"\r\n        :class=\"{'active': activeTab === index}\"\r\n        @click=\"switchTab(index)\"\r\n      >\r\n        {{ tab.name }}\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 交易记录列表 -->\r\n    <view class=\"transaction-list\">\r\n      <view v-if=\"filteredTransactions.length > 0\">\r\n        <!-- 按月分组的交易记录 -->\r\n        <block v-for=\"(group, month) in groupedTransactions\" :key=\"month\">\r\n          <view class=\"month-header\">{{ month }}</view>\r\n          \r\n          <view class=\"transaction-item\" v-for=\"(item, index) in group\" :key=\"index\">\r\n            <view class=\"transaction-left\">\r\n              <view class=\"transaction-icon\" :class=\"getTransactionTypeClass(item.type)\">\r\n                <image :src=\"getTransactionTypeIcon(item.type)\" class=\"type-icon\"></image>\r\n              </view>\r\n            </view>\r\n            <view class=\"transaction-center\">\r\n              <view class=\"transaction-title\">{{ item.title }}</view>\r\n              <view class=\"transaction-time\">{{ item.time }}</view>\r\n            </view>\r\n            <view class=\"transaction-right\">\r\n              <view class=\"transaction-amount\" :class=\"{'income': item.type === 'income', 'expense': item.type === 'expense'}\">\r\n                {{ item.type === 'income' ? '+' : '-' }}{{ item.amount.toFixed(2) }}\r\n              </view>\r\n              <view class=\"transaction-status\">{{ item.status }}</view>\r\n            </view>\r\n          </view>\r\n        </block>\r\n        \r\n        <!-- 加载更多 -->\r\n        <view class=\"load-more\" v-if=\"hasMoreData\" @click=\"loadMoreData\">\r\n          <text>加载更多</text>\r\n        </view>\r\n        <view class=\"no-more\" v-else>\r\n          <text>没有更多数据了</text>\r\n        </view>\r\n      </view>\r\n      \r\n      <!-- 空状态 -->\r\n      <view v-else class=\"empty-view\">\r\n        <image class=\"empty-icon\" src=\"/static/images/empty.png\" mode=\"aspectFit\"></image>\r\n        <view class=\"empty-text\">暂无交易记录</view>\r\n      </view>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script setup>\r\nimport { ref, computed, onMounted } from 'vue';\r\n\r\n// 响应式状态\r\nconst statusBarHeight = ref(20);\r\nconst navbarHeight = ref(64);\r\nconst balanceInfo = ref({\r\n  amount: 158.50,\r\n  totalIncome: 200.00,\r\n  totalExpense: 41.50,\r\n  frozenAmount: 0.00\r\n});\r\nconst activeTab = ref(0);\r\nconst tabs = ref([\r\n  { name: '全部', type: 'all' },\r\n  { name: '收入', type: 'income' },\r\n  { name: '支出', type: 'expense' }\r\n]);\r\nconst transactions = ref([\r\n  {\r\n    id: 'tx001',\r\n    title: '充值',\r\n    time: '2023-11-05 14:30',\r\n    amount: 100.00,\r\n    type: 'income',\r\n    status: '已完成',\r\n    month: '2023年11月'\r\n  },\r\n  {\r\n    id: 'tx002',\r\n    title: '服务支付',\r\n    time: '2023-11-03 09:15',\r\n    amount: 35.00,\r\n    type: 'expense',\r\n    status: '已完成',\r\n    month: '2023年11月'\r\n  },\r\n  {\r\n    id: 'tx003',\r\n    title: '提现',\r\n    time: '2023-10-28 16:22',\r\n    amount: 50.00,\r\n    type: 'expense',\r\n    status: '已完成',\r\n    month: '2023年10月'\r\n  },\r\n  {\r\n    id: 'tx004',\r\n    title: '充值',\r\n    time: '2023-10-15 11:05',\r\n    amount: 100.00,\r\n    type: 'income',\r\n    status: '已完成',\r\n    month: '2023年10月'\r\n  },\r\n  {\r\n    id: 'tx005',\r\n    title: '任务收入',\r\n    time: '2023-09-30 18:45',\r\n    amount: 88.00,\r\n    type: 'income',\r\n    status: '已完成',\r\n    month: '2023年9月'\r\n  }\r\n]);\r\nconst page = ref(1);\r\nconst pageSize = ref(10);\r\nconst hasMoreData = ref(true);\r\n\r\n// 计算属性\r\n// 根据当前选中的标签筛选交易记录\r\nconst filteredTransactions = computed(() => {\r\n  if (activeTab.value === 0) {\r\n    return transactions.value;\r\n  } else {\r\n    const type = tabs.value[activeTab.value].type;\r\n    return transactions.value.filter(item => item.type === type);\r\n  }\r\n});\r\n\r\n// 按月份分组交易记录\r\nconst groupedTransactions = computed(() => {\r\n  const groups = {};\r\n  \r\n  filteredTransactions.value.forEach(item => {\r\n    if (!groups[item.month]) {\r\n      groups[item.month] = [];\r\n    }\r\n    groups[item.month].push(item);\r\n  });\r\n  \r\n  return groups;\r\n});\r\n\r\n// 方法\r\n// 返回上一页\r\nconst goBack = () => {\r\n  uni.navigateBack();\r\n};\r\n\r\n// 切换标签\r\nconst switchTab = (index) => {\r\n  activeTab.value = index;\r\n};\r\n\r\n// 获取钱包余额\r\nconst getWalletBalance = () => {\r\n  // 这里应该是从API获取钱包余额\r\n  // 模拟API请求\r\n  setTimeout(() => {\r\n    balanceInfo.value = {\r\n      amount: 158.50,\r\n      totalIncome: 200.00,\r\n      totalExpense: 41.50,\r\n      frozenAmount: 0.00\r\n    };\r\n  }, 500);\r\n};\r\n\r\n// 获取交易记录\r\nconst getTransactions = () => {\r\n  // 模拟API请求获取交易记录\r\n  // 实际应用中应该从服务器获取数据\r\n  setTimeout(() => {\r\n    // 如果是第一页，直接替换数据\r\n    if (page.value === 1) {\r\n      // 数据已经在data中初始化\r\n    } else {\r\n      // 如果是加载更多，追加数据\r\n      if (page.value >= 3) {\r\n        hasMoreData.value = false;\r\n      } else {\r\n        // 模拟加载更多数据\r\n        const moreData = [\r\n          {\r\n            id: 'tx006',\r\n            title: '服务支付',\r\n            time: '2023-09-22 10:30',\r\n            amount: 25.00,\r\n            type: 'expense',\r\n            status: '已完成',\r\n            month: '2023年9月'\r\n          },\r\n          {\r\n            id: 'tx007',\r\n            title: '充值',\r\n            time: '2023-09-15 16:40',\r\n            amount: 50.00,\r\n            type: 'income',\r\n            status: '已完成',\r\n            month: '2023年9月'\r\n          }\r\n        ];\r\n        transactions.value = [...transactions.value, ...moreData];\r\n      }\r\n    }\r\n  }, 500);\r\n};\r\n\r\n// 加载更多数据\r\nconst loadMoreData = () => {\r\n  if (!hasMoreData.value) return;\r\n  \r\n  page.value++;\r\n  getTransactions();\r\n};\r\n\r\n// 获取交易类型对应的图标\r\nconst getTransactionTypeIcon = (type) => {\r\n  const icons = {\r\n    'income': '/static/images/tabbar/收入.png',\r\n    'expense': '/static/images/tabbar/支出.png'\r\n  };\r\n  return icons[type] || icons['income'];\r\n};\r\n\r\n// 获取交易类型对应的样式类\r\nconst getTransactionTypeClass = (type) => {\r\n  return {\r\n    'income-icon': type === 'income',\r\n    'expense-icon': type === 'expense'\r\n  };\r\n};\r\n\r\n// 生命周期钩子\r\nonMounted(() => {\r\n  // 获取状态栏高度\r\n  const sysInfo = uni.getSystemInfoSync();\r\n  statusBarHeight.value = sysInfo.statusBarHeight;\r\n  navbarHeight.value = statusBarHeight.value + 44;\r\n  \r\n  // 获取钱包余额和交易记录\r\n  getWalletBalance();\r\n  getTransactions();\r\n});\r\n</script>\r\n\r\n<style>\r\n.wallet-detail-container {\r\n  min-height: 100vh;\r\n  background-color: #f5f7fa;\r\n  padding-bottom: 30rpx;\r\n}\r\n\r\n/* 自定义导航栏 */\r\n.custom-navbar {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  height: 44px;\r\n  background-color: #0052CC;\r\n  color: #fff;\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 0 15px;\r\n  z-index: 999;\r\n}\r\n\r\n.navbar-left {\r\n  width: 80rpx;\r\n  height: 60rpx;\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.back-icon {\r\n  width: 40rpx;\r\n  height: 40rpx;\r\n}\r\n\r\n.navbar-title {\r\n  flex: 1;\r\n  text-align: center;\r\n  font-size: 18px;\r\n  font-weight: 500;\r\n}\r\n\r\n.navbar-right {\r\n  width: 80rpx;\r\n}\r\n\r\n/* 余额卡片 */\r\n.balance-card {\r\n  background: linear-gradient(to right, #0052CC, #0066FF);\r\n  margin: 0 30rpx;\r\n  padding: 30rpx;\r\n  border-radius: 20rpx;\r\n  color: #fff;\r\n  overflow: hidden;\r\n  box-shadow: 0 8rpx 20rpx rgba(0, 82, 204, 0.2);\r\n}\r\n\r\n.balance-title {\r\n  font-size: 28rpx;\r\n  opacity: 0.9;\r\n  margin-bottom: 20rpx;\r\n}\r\n\r\n.balance-amount {\r\n  font-size: 60rpx;\r\n  font-weight: bold;\r\n  margin-bottom: 40rpx;\r\n}\r\n\r\n.balance-info {\r\n  display: flex;\r\n  justify-content: space-around;\r\n  align-items: center;\r\n  background-color: rgba(255, 255, 255, 0.1);\r\n  padding: 20rpx;\r\n  border-radius: 10rpx;\r\n}\r\n\r\n.info-item {\r\n  text-align: center;\r\n}\r\n\r\n.info-value {\r\n  font-size: 32rpx;\r\n  font-weight: 500;\r\n  margin-bottom: 10rpx;\r\n}\r\n\r\n.info-label {\r\n  font-size: 24rpx;\r\n  opacity: 0.8;\r\n}\r\n\r\n.info-divider {\r\n  width: 1px;\r\n  height: 60rpx;\r\n  background-color: rgba(255, 255, 255, 0.3);\r\n}\r\n\r\n/* 筛选标签 */\r\n.filter-tabs {\r\n  display: flex;\r\n  background-color: #fff;\r\n  margin: 30rpx;\r\n  border-radius: 20rpx;\r\n  overflow: hidden;\r\n  box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n.filter-tab {\r\n  flex: 1;\r\n  text-align: center;\r\n  padding: 30rpx 0;\r\n  font-size: 28rpx;\r\n  color: #666;\r\n  position: relative;\r\n}\r\n\r\n.filter-tab.active {\r\n  color: #0052CC;\r\n  font-weight: 500;\r\n}\r\n\r\n.filter-tab.active::after {\r\n  content: '';\r\n  position: absolute;\r\n  bottom: 0;\r\n  left: 50%;\r\n  transform: translateX(-50%);\r\n  width: 40rpx;\r\n  height: 4rpx;\r\n  background-color: #0052CC;\r\n  border-radius: 2rpx;\r\n}\r\n\r\n/* 交易记录列表 */\r\n.transaction-list {\r\n  margin: 0 30rpx;\r\n}\r\n\r\n.month-header {\r\n  font-size: 24rpx;\r\n  color: #999;\r\n  padding: 20rpx 0;\r\n}\r\n\r\n.transaction-item {\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 30rpx;\r\n  background-color: #fff;\r\n  margin-bottom: 20rpx;\r\n  border-radius: 10rpx;\r\n  box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n.transaction-left {\r\n  margin-right: 20rpx;\r\n}\r\n\r\n.transaction-icon {\r\n  width: 80rpx;\r\n  height: 80rpx;\r\n  border-radius: 40rpx;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.income-icon {\r\n  background-color: rgba(7, 193, 96, 0.1);\r\n}\r\n\r\n.expense-icon {\r\n  background-color: rgba(245, 108, 108, 0.1);\r\n}\r\n\r\n.type-icon {\r\n  width: 40rpx;\r\n  height: 40rpx;\r\n}\r\n\r\n.transaction-center {\r\n  flex: 1;\r\n}\r\n\r\n.transaction-title {\r\n  font-size: 28rpx;\r\n  color: #333;\r\n  margin-bottom: 10rpx;\r\n}\r\n\r\n.transaction-time {\r\n  font-size: 24rpx;\r\n  color: #999;\r\n}\r\n\r\n.transaction-right {\r\n  text-align: right;\r\n}\r\n\r\n.transaction-amount {\r\n  font-size: 32rpx;\r\n  font-weight: 500;\r\n  margin-bottom: 10rpx;\r\n}\r\n\r\n.income {\r\n  color: #07c160;\r\n}\r\n\r\n.expense {\r\n  color: #f56c6c;\r\n}\r\n\r\n.transaction-status {\r\n  font-size: 24rpx;\r\n  color: #999;\r\n}\r\n\r\n/* 加载更多 */\r\n.load-more, .no-more {\r\n  text-align: center;\r\n  padding: 30rpx 0;\r\n  font-size: 24rpx;\r\n  color: #999;\r\n}\r\n\r\n.load-more {\r\n  color: #0052CC;\r\n}\r\n\r\n/* 空状态 */\r\n.empty-view {\r\n  padding: 100rpx 0;\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.empty-icon {\r\n  width: 200rpx;\r\n  height: 200rpx;\r\n  margin-bottom: 20rpx;\r\n}\r\n\r\n.empty-text {\r\n  font-size: 28rpx;\r\n  color: #999;\r\n}\r\n</style>\r\n", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/subPackages/payment/pages/detail.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "computed", "uni", "onMounted", "MiniProgramPage"], "mappings": ";;;;;;AAyFA,UAAM,kBAAkBA,cAAAA,IAAI,EAAE;AAC9B,UAAM,eAAeA,cAAAA,IAAI,EAAE;AAC3B,UAAM,cAAcA,cAAAA,IAAI;AAAA,MACtB,QAAQ;AAAA,MACR,aAAa;AAAA,MACb,cAAc;AAAA,MACd,cAAc;AAAA,IAChB,CAAC;AACD,UAAM,YAAYA,cAAAA,IAAI,CAAC;AACvB,UAAM,OAAOA,cAAAA,IAAI;AAAA,MACf,EAAE,MAAM,MAAM,MAAM,MAAO;AAAA,MAC3B,EAAE,MAAM,MAAM,MAAM,SAAU;AAAA,MAC9B,EAAE,MAAM,MAAM,MAAM,UAAW;AAAA,IACjC,CAAC;AACD,UAAM,eAAeA,cAAAA,IAAI;AAAA,MACvB;AAAA,QACE,IAAI;AAAA,QACJ,OAAO;AAAA,QACP,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,OAAO;AAAA,MACR;AAAA,MACD;AAAA,QACE,IAAI;AAAA,QACJ,OAAO;AAAA,QACP,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,OAAO;AAAA,MACR;AAAA,MACD;AAAA,QACE,IAAI;AAAA,QACJ,OAAO;AAAA,QACP,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,OAAO;AAAA,MACR;AAAA,MACD;AAAA,QACE,IAAI;AAAA,QACJ,OAAO;AAAA,QACP,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,OAAO;AAAA,MACR;AAAA,MACD;AAAA,QACE,IAAI;AAAA,QACJ,OAAO;AAAA,QACP,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,OAAO;AAAA,MACR;AAAA,IACH,CAAC;AACD,UAAM,OAAOA,cAAAA,IAAI,CAAC;AACDA,kBAAG,IAAC,EAAE;AACvB,UAAM,cAAcA,cAAAA,IAAI,IAAI;AAI5B,UAAM,uBAAuBC,cAAQ,SAAC,MAAM;AAC1C,UAAI,UAAU,UAAU,GAAG;AACzB,eAAO,aAAa;AAAA,MACxB,OAAS;AACL,cAAM,OAAO,KAAK,MAAM,UAAU,KAAK,EAAE;AACzC,eAAO,aAAa,MAAM,OAAO,UAAQ,KAAK,SAAS,IAAI;AAAA,MAC5D;AAAA,IACH,CAAC;AAGD,UAAM,sBAAsBA,cAAQ,SAAC,MAAM;AACzC,YAAM,SAAS,CAAA;AAEf,2BAAqB,MAAM,QAAQ,UAAQ;AACzC,YAAI,CAAC,OAAO,KAAK,KAAK,GAAG;AACvB,iBAAO,KAAK,KAAK,IAAI;QACtB;AACD,eAAO,KAAK,KAAK,EAAE,KAAK,IAAI;AAAA,MAChC,CAAG;AAED,aAAO;AAAA,IACT,CAAC;AAID,UAAM,SAAS,MAAM;AACnBC,oBAAG,MAAC,aAAY;AAAA,IAClB;AAGA,UAAM,YAAY,CAAC,UAAU;AAC3B,gBAAU,QAAQ;AAAA,IACpB;AAGA,UAAM,mBAAmB,MAAM;AAG7B,iBAAW,MAAM;AACf,oBAAY,QAAQ;AAAA,UAClB,QAAQ;AAAA,UACR,aAAa;AAAA,UACb,cAAc;AAAA,UACd,cAAc;AAAA,QACpB;AAAA,MACG,GAAE,GAAG;AAAA,IACR;AAGA,UAAM,kBAAkB,MAAM;AAG5B,iBAAW,MAAM;AAEf,YAAI,KAAK,UAAU;AAAG;AAAA,aAEf;AAEL,cAAI,KAAK,SAAS,GAAG;AACnB,wBAAY,QAAQ;AAAA,UAC5B,OAAa;AAEL,kBAAM,WAAW;AAAA,cACf;AAAA,gBACE,IAAI;AAAA,gBACJ,OAAO;AAAA,gBACP,MAAM;AAAA,gBACN,QAAQ;AAAA,gBACR,MAAM;AAAA,gBACN,QAAQ;AAAA,gBACR,OAAO;AAAA,cACR;AAAA,cACD;AAAA,gBACE,IAAI;AAAA,gBACJ,OAAO;AAAA,gBACP,MAAM;AAAA,gBACN,QAAQ;AAAA,gBACR,MAAM;AAAA,gBACN,QAAQ;AAAA,gBACR,OAAO;AAAA,cACR;AAAA,YACX;AACQ,yBAAa,QAAQ,CAAC,GAAG,aAAa,OAAO,GAAG,QAAQ;AAAA,UACzD;AAAA,QACF;AAAA,MACF,GAAE,GAAG;AAAA,IACR;AAGA,UAAM,eAAe,MAAM;AACzB,UAAI,CAAC,YAAY;AAAO;AAExB,WAAK;AACL;IACF;AAGA,UAAM,yBAAyB,CAAC,SAAS;AACvC,YAAM,QAAQ;AAAA,QACZ,UAAU;AAAA,QACV,WAAW;AAAA,MACf;AACE,aAAO,MAAM,IAAI,KAAK,MAAM,QAAQ;AAAA,IACtC;AAGA,UAAM,0BAA0B,CAAC,SAAS;AACxC,aAAO;AAAA,QACL,eAAe,SAAS;AAAA,QACxB,gBAAgB,SAAS;AAAA,MAC7B;AAAA,IACA;AAGAC,kBAAAA,UAAU,MAAM;AAEd,YAAM,UAAUD,oBAAI;AACpB,sBAAgB,QAAQ,QAAQ;AAChC,mBAAa,QAAQ,gBAAgB,QAAQ;AAG7C;AACA;IACF,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACtRD,GAAG,WAAWE,SAAe;"}