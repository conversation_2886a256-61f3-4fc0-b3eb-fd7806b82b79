{"version": 3, "file": "detail.js", "sources": ["subPackages/activity-showcase/pages/flash-sale/detail.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/c3ViUGFja2FnZXNcYWN0aXZpdHktc2hvd2Nhc2VccGFnZXNcZmxhc2gtc2FsZVxkZXRhaWwudnVl"], "sourcesContent": ["<template>\r\n  <view class=\"flash-detail-container\">\r\n    <!-- 自定义导航栏 -->\r\n    <view class=\"custom-navbar\">\r\n      <view class=\"navbar-bg\"></view>\r\n      <view class=\"navbar-content\">\r\n        <view class=\"back-btn\" @click=\"goBack\">\r\n          <image class=\"back-icon\" src=\"/static/images/tabbar/最新返回键.png\" mode=\"aspectFit\"></image>\r\n        </view>\r\n        <view class=\"navbar-title\">秒杀详情</view>\r\n        <view class=\"navbar-right\"></view>\r\n      </view>\r\n    </view>\r\n\r\n    <!-- 加载状态 -->\r\n    <view class=\"loading-container\" v-if=\"loading\">\r\n      <view class=\"loading-spinner\"></view>\r\n      <text class=\"loading-text\">加载中...</text>\r\n    </view>\r\n\r\n    <block v-else>\r\n      <!-- 商品轮播图 -->\r\n      <swiper class=\"product-swiper\" \r\n        :indicator-dots=\"true\" \r\n        indicator-color=\"rgba(255,255,255,0.4)\"\r\n        indicator-active-color=\"#FFFFFF\"\r\n        :autoplay=\"true\" \r\n        :interval=\"4000\" \r\n        :duration=\"400\"\r\n        :style=\"{ marginTop: navbarHeight + 'px' }\">\r\n        <swiper-item v-for=\"(item, index) in product.images\" :key=\"index\">\r\n          <image :src=\"item\" mode=\"aspectFill\" class=\"swiper-image\"></image>\r\n        </swiper-item>\r\n      </swiper>\r\n\r\n      <!-- 商品基本信息卡片 -->\r\n      <view class=\"product-info-card\">\r\n        <!-- 倒计时区域 -->\r\n        <view class=\"countdown-section\">\r\n          <view class=\"countdown-label\">距结束</view>\r\n          <view class=\"countdown-timer\">\r\n            <text class=\"time-block\">{{ countdown.hours }}</text>\r\n            <text class=\"time-colon\">:</text>\r\n            <text class=\"time-block\">{{ countdown.minutes }}</text>\r\n            <text class=\"time-colon\">:</text>\r\n            <text class=\"time-block\">{{ countdown.seconds }}</text>\r\n          </view>\r\n        </view>\r\n        \r\n        <!-- 价格区域 -->\r\n        <view class=\"price-section\">\r\n          <view class=\"price-main\">\r\n            <text class=\"price-symbol\">¥</text>\r\n            <text class=\"price-value\">{{ product.flashPrice }}</text>\r\n            <view class=\"price-tag\">{{ discountPercent }}折</view>\r\n          </view>\r\n          <view class=\"original-price\">¥{{ product.originalPrice }}</view>\r\n        </view>\r\n        \r\n        <!-- 商品标题 -->\r\n        <view class=\"product-title-row\">\r\n          <view class=\"flash-tag\">秒杀</view>\r\n          <text class=\"product-title\">{{ product.title }}</text>\r\n        </view>\r\n        \r\n        <!-- 销量和库存 -->\r\n        <view class=\"sales-stock\">\r\n          <text class=\"sales-count\">已售{{ product.soldCount }}件</text>\r\n          <text class=\"stock-count\">剩余{{ product.stock }}件</text>\r\n        </view>\r\n        \r\n        <!-- 进度条 -->\r\n        <view class=\"progress-section\">\r\n          <view class=\"progress-bar\">\r\n            <view class=\"progress-fill\" :style=\"{ width: progressWidth }\"></view>\r\n          </view>\r\n          <view class=\"progress-text\">\r\n            <text>已抢{{ product.soldCount }}件，限量{{ product.totalCount }}件</text>\r\n          </view>\r\n        </view>\r\n        \r\n        <!-- 分销组件 - 醒目位置 -->\r\n        <distribution-section \r\n          :itemId=\"id\"\r\n          itemType=\"flash\"\r\n          :itemTitle=\"product.title\"\r\n          :itemPrice=\"product.flashPrice\"\r\n          :commissionRate=\"product.commissionRate || 15\"\r\n        />\r\n      </view>\r\n      \r\n      <!-- 商家信息卡片 -->\r\n      <view class=\"shop-card\" @click=\"goToShop\">\r\n        <view class=\"shop-info\">\r\n          <image :src=\"product.shopLogo\" class=\"shop-logo\"></image>\r\n          <view class=\"shop-details\">\r\n            <text class=\"shop-name\">{{ product.shopName }}</text>\r\n            <view class=\"shop-rating\">\r\n              <view class=\"rating-stars\">\r\n                <image v-for=\"i in 5\" :key=\"i\" \r\n                  :src=\"i <= Math.floor(product.shopRating) ? \r\n                    '/static/images/tabbar/星星-选中.png' : \r\n                    '/static/images/tabbar/星星-未选.png'\" \r\n                  class=\"star-icon\"></image>\r\n              </view>\r\n              <text class=\"rating-score\">{{ product.shopRating }}</text>\r\n            </view>\r\n          </view>\r\n        </view>\r\n        <view class=\"shop-action\">\r\n          <text>进店</text>\r\n          <text class=\"arrow-icon\">›</text>\r\n        </view>\r\n      </view>\r\n      \r\n      <!-- 商品详情卡片 -->\r\n      <view class=\"detail-card\">\r\n        <view class=\"detail-header\">\r\n          <text class=\"detail-title\">商品详情</text>\r\n        </view>\r\n        <view class=\"detail-content\">\r\n          <text class=\"detail-text\">{{ product.description }}</text>\r\n          <image v-for=\"(img, index) in product.detailImages\" \r\n            :key=\"index\" \r\n            :src=\"img\" \r\n            mode=\"widthFix\" \r\n            class=\"detail-image\"></image>\r\n        </view>\r\n      </view>\r\n      \r\n      <!-- 底部购买栏 -->\r\n      <view class=\"bottom-bar\">\r\n        <view class=\"bottom-left\">\r\n          <view class=\"action-btn\" @click=\"toggleFavorite\">\r\n            <image :src=\"isFavorite ? '/static/images/tabbar/收藏-选中.png' : '/static/images/tabbar/收藏.png'\" class=\"action-icon\"></image>\r\n            <text>收藏</text>\r\n          </view>\r\n          <view class=\"action-btn\" @click=\"contactService\">\r\n            <image src=\"/static/images/tabbar/客服.png\" class=\"action-icon\"></image>\r\n            <text>客服</text>\r\n          </view>\r\n          <view class=\"action-btn\" @click=\"share\">\r\n            <image src=\"/static/images/tabbar/分享.png\" class=\"action-icon\"></image>\r\n            <text>分享</text>\r\n          </view>\r\n        </view>\r\n        <view class=\"buy-buttons\">\r\n          <view class=\"buy-btn flash-buy\" @click=\"buyNow\">\r\n            <text class=\"buy-price\">¥{{ product.flashPrice }}</text>\r\n            <text class=\"buy-label\">立即抢购</text>\r\n          </view>\r\n        </view>\r\n      </view>\r\n    </block>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nimport DistributionSection from '@/components/distribution-section.vue';\r\n\r\nexport default {\r\n  components: {\r\n    DistributionSection\r\n  },\r\n  data() {\r\n    return {\r\n      id: null,\r\n      statusBarHeight: 20,\r\n      navbarHeight: 82,\r\n      loading: true,\r\n      product: {},\r\n      countdown: {\r\n        hours: '00',\r\n        minutes: '00',\r\n        seconds: '00'\r\n      },\r\n      progressWidth: '0%',\r\n      isFavorite: false,\r\n      timer: null // 用于存储定时器ID\r\n    }\r\n  },\r\n  computed: {\r\n    discountPercent() {\r\n      if (!this.product.flashPrice || !this.product.originalPrice) return '';\r\n      const discount = (parseFloat(this.product.flashPrice) / parseFloat(this.product.originalPrice)) * 10;\r\n      return discount.toFixed(1);\r\n    }\r\n  },\r\n  onLoad(options) {\r\n    if (options && options.id) {\r\n      this.id = options.id;\r\n    }\r\n    \r\n    // 获取状态栏高度\r\n    const systemInfo = uni.getSystemInfoSync();\r\n    this.statusBarHeight = systemInfo.statusBarHeight;\r\n    this.navbarHeight = this.statusBarHeight + 62; // 状态栏 + 标题栏高度\r\n    \r\n    // 模拟加载数据\r\n    setTimeout(() => {\r\n      this.loadProductDetail();\r\n    }, 500);\r\n  },\r\n  methods: {\r\n    // 返回上一页\r\n    goBack() {\r\n      uni.navigateBack();\r\n    },\r\n    \r\n    // 加载秒杀商品详情\r\n    loadProductDetail() {\r\n      // 模拟API加载数据\r\n      this.loading = true;\r\n      \r\n      // 在实际应用中，这里应该是从API获取数据\r\n      setTimeout(() => {\r\n        // 模拟数据\r\n        this.product = {\r\n          id: this.id || 1,\r\n          title: 'iPhone 13 Pro Max限时秒杀',\r\n          images: [\r\n            '/static/demo/product1.jpg',\r\n            '/static/demo/product2.jpg',\r\n            '/static/demo/product3.jpg'\r\n          ],\r\n          detailImages: [\r\n            '/static/demo/detail1.jpg',\r\n            '/static/demo/detail2.jpg'\r\n          ],\r\n          flashPrice: '7999',\r\n          originalPrice: '8999',\r\n          soldCount: 156,\r\n          totalCount: 200,\r\n          startTime: new Date(),\r\n          endTime: new Date(Date.now() + 24 * 60 * 60 * 1000),\r\n          description: '苹果最新旗舰手机限时特惠，A15仿生芯片，超视网膜XDR显示屏，专业级摄像系统，超瓷晶面板，IP68级防水，5G网络。',\r\n          shopName: '官方旗舰店',\r\n          shopLogo: '/static/demo/shop-logo.png',\r\n          shopRating: 4.9,\r\n          stock: 44,\r\n          commissionRate: 15\r\n        };\r\n        \r\n        this.loading = false;\r\n        this.startCountdown();\r\n        this.calculateProgress();\r\n      }, 1000);\r\n    },\r\n    \r\n    // 开始倒计时\r\n    startCountdown() {\r\n      const endTime = this.product.endTime;\r\n      const updateCountdown = () => {\r\n        const now = new Date();\r\n        const diff = endTime - now;\r\n        \r\n        if (diff <= 0) {\r\n          // 秒杀已结束\r\n          this.countdown = {\r\n            hours: '00',\r\n            minutes: '00',\r\n            seconds: '00'\r\n          };\r\n          clearInterval(this.timer);\r\n          return;\r\n        }\r\n        \r\n        // 计算小时、分钟和秒\r\n        const hours = Math.floor(diff / (1000 * 60 * 60));\r\n        const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));\r\n        const seconds = Math.floor((diff % (1000 * 60)) / 1000);\r\n        \r\n        // 格式化时间\r\n        this.countdown = {\r\n          hours: hours.toString().padStart(2, '0'),\r\n          minutes: minutes.toString().padStart(2, '0'),\r\n          seconds: seconds.toString().padStart(2, '0')\r\n        };\r\n      };\r\n      \r\n      // 立即更新一次\r\n      updateCountdown();\r\n      \r\n      // 每秒更新一次\r\n      this.timer = setInterval(updateCountdown, 1000);\r\n    },\r\n    \r\n    // 计算进度条宽度\r\n    calculateProgress() {\r\n      const soldPercent = (this.product.soldCount / this.product.totalCount) * 100;\r\n      this.progressWidth = soldPercent + '%';\r\n    },\r\n    \r\n    // 跳转到店铺\r\n    goToShop() {\r\n      uni.navigateTo({\r\n        url: `/pages/shop/detail?id=${this.product.shopId || 1}`\r\n      });\r\n    },\r\n    \r\n    // 联系客服\r\n    contactService() {\r\n      uni.showToast({\r\n        title: '正在连接客服...',\r\n        icon: 'none'\r\n      });\r\n    },\r\n    \r\n    // 切换收藏状态\r\n    toggleFavorite() {\r\n      this.isFavorite = !this.isFavorite;\r\n      uni.showToast({\r\n        title: this.isFavorite ? '收藏成功' : '已取消收藏',\r\n        icon: 'success'\r\n      });\r\n    },\r\n    \r\n    // 立即购买\r\n    buyNow() {\r\n      uni.showLoading({\r\n        title: '正在下单'\r\n      });\r\n      \r\n      // 模拟API请求\r\n      setTimeout(() => {\r\n        uni.hideLoading();\r\n        \r\n        // 跳转到支付页面\r\n        uni.navigateTo({\r\n          url: `/pages/pay/index?amount=${this.product.flashPrice}&title=${encodeURIComponent(this.product.title)}`\r\n        });\r\n      }, 800);\r\n    },\r\n    \r\n    // 分享\r\n    share() {\r\n      uni.showToast({\r\n        title: '分享功能开发中',\r\n        icon: 'none'\r\n      });\r\n    }\r\n  },\r\n  onUnload() {\r\n    // 清除定时器\r\n    if (this.timer) {\r\n      clearInterval(this.timer);\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.flash-detail-container {\r\n  display: flex;\r\n  flex-direction: column;\r\n  min-height: 100vh;\r\n  background-color: #F5F7FA;\r\n  padding-bottom: 120rpx; /* 为底部购买栏留出空间 */\r\n}\r\n\r\n/* 自定义导航栏 */\r\n.custom-navbar {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  height: calc(var(--status-bar-height, 25px) + 62px);\r\n  width: 100%;\r\n  z-index: 100;\r\n  \r\n  .navbar-bg {\r\n    position: absolute;\r\n    top: 0;\r\n    left: 0;\r\n    width: 100%;\r\n    height: 100%;\r\n    background: linear-gradient(135deg, #FF2C54 0%, #FF4F64 100%);\r\n  }\r\n  \r\n  .navbar-content {\r\n    position: relative;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n    height: 100%;\r\n    padding-top: var(--status-bar-height, 25px);\r\n    padding-left: 30rpx;\r\n    padding-right: 30rpx;\r\n    box-sizing: border-box;\r\n  }\r\n  \r\n  .back-btn {\r\n    width: 40rpx;\r\n    height: 40rpx;\r\n    margin-right: 10rpx;\r\n  }\r\n  \r\n  .back-icon {\r\n    width: 100%;\r\n    height: 100%;\r\n  }\r\n  \r\n  .navbar-title {\r\n    font-size: 18px;\r\n    font-weight: 600;\r\n    color: #FFFFFF;\r\n    position: absolute;\r\n    left: 50%;\r\n    transform: translateX(-50%);\r\n  }\r\n}\r\n\r\n/* 加载状态 */\r\n.loading-container {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  height: 100vh;\r\n  \r\n  .loading-spinner {\r\n    width: 80rpx;\r\n    height: 80rpx;\r\n    border: 6rpx solid #f3f3f3;\r\n    border-top: 6rpx solid #FF3B30;\r\n    border-radius: 50%;\r\n    animation: spin 1s linear infinite;\r\n    margin-bottom: 20rpx;\r\n  }\r\n  \r\n  .loading-text {\r\n    font-size: 28rpx;\r\n    color: #8E8E93;\r\n  }\r\n}\r\n\r\n@keyframes spin {\r\n  0% { transform: rotate(0deg); }\r\n  100% { transform: rotate(360deg); }\r\n}\r\n\r\n/* 商品轮播图 */\r\n.product-swiper {\r\n  width: 100%;\r\n  height: 750rpx; /* 轮播图高度 */\r\n  border-bottom-left-radius: 35rpx;\r\n  border-bottom-right-radius: 35rpx;\r\n  overflow: hidden;\r\n  box-shadow: 0 15rpx 35rpx rgba(0, 0, 0, 0.15);\r\n  margin-bottom: 30rpx;\r\n  \r\n  .swiper-image {\r\n    width: 100%;\r\n    height: 100%;\r\n  }\r\n}\r\n\r\n/* 商品基本信息卡片 */\r\n.product-info-card {\r\n  background-color: #FFFFFF;\r\n  border-radius: 35rpx;\r\n  padding: 30rpx;\r\n  box-shadow: 0 15rpx 35rpx rgba(0, 0, 0, 0.08);\r\n  margin: 0 30rpx 30rpx 30rpx;\r\n  transform: translateZ(0);\r\n  transition: transform 0.3s ease, box-shadow 0.3s ease;\r\n  \r\n  &:active {\r\n    transform: translateY(5rpx);\r\n    box-shadow: 0 10rpx 25rpx rgba(0, 0, 0, 0.06);\r\n  }\r\n\r\n  .countdown-section {\r\n    display: flex;\r\n    align-items: center;\r\n    margin-bottom: 20rpx;\r\n\r\n    .countdown-label {\r\n      font-size: 28rpx;\r\n      font-weight: 600;\r\n      color: #333333;\r\n      margin-right: 15rpx;\r\n    }\r\n\r\n    .countdown-timer {\r\n      display: flex;\r\n      align-items: center;\r\n    }\r\n\r\n    .time-block {\r\n      background-color: #FF3B30;\r\n      border-radius: 8rpx;\r\n      padding: 8rpx 12rpx;\r\n      font-size: 28rpx;\r\n      font-weight: 700;\r\n      color: #FFFFFF;\r\n      box-shadow: 0 4rpx 8rpx rgba(255, 59, 48, 0.3);\r\n    }\r\n\r\n    .time-colon {\r\n      font-size: 28rpx;\r\n      font-weight: 700;\r\n      color: #333333;\r\n      margin: 0 8rpx;\r\n    }\r\n  }\r\n\r\n  .price-section {\r\n    display: flex;\r\n    align-items: baseline;\r\n    margin-bottom: 20rpx;\r\n\r\n    .price-main {\r\n      display: flex;\r\n      align-items: baseline;\r\n      margin-right: 15rpx;\r\n    }\r\n\r\n    .price-symbol {\r\n      font-size: 36rpx;\r\n      font-weight: 700;\r\n      color: #FF3B30;\r\n      margin-right: 5rpx;\r\n    }\r\n\r\n    .price-value {\r\n      font-size: 56rpx;\r\n      font-weight: 800;\r\n      color: #FF3B30;\r\n      text-shadow: 0 2rpx 4rpx rgba(255, 59, 48, 0.2);\r\n    }\r\n\r\n    .price-tag {\r\n      background: linear-gradient(135deg, #FF3B30 0%, #FF6B6B 100%);\r\n      color: #FFFFFF;\r\n      font-size: 24rpx;\r\n      font-weight: 600;\r\n      padding: 6rpx 12rpx;\r\n      border-radius: 8rpx;\r\n      margin-left: 15rpx;\r\n      box-shadow: 0 4rpx 8rpx rgba(255, 59, 48, 0.2);\r\n    }\r\n\r\n    .original-price {\r\n      font-size: 28rpx;\r\n      color: #8E8E93;\r\n      text-decoration: line-through;\r\n    }\r\n  }\r\n\r\n  .product-title-row {\r\n    display: flex;\r\n    align-items: center;\r\n    margin-bottom: 20rpx;\r\n\r\n    .flash-tag {\r\n      background: linear-gradient(135deg, #FF3B30 0%, #FF6B6B 100%);\r\n      color: #FFFFFF;\r\n      font-size: 24rpx;\r\n      font-weight: 600;\r\n      padding: 6rpx 12rpx;\r\n      border-radius: 8rpx;\r\n      margin-right: 15rpx;\r\n      box-shadow: 0 4rpx 8rpx rgba(255, 59, 48, 0.2);\r\n    }\r\n\r\n    .product-title {\r\n      font-size: 36rpx;\r\n      font-weight: 700;\r\n      color: #333333;\r\n      flex: 1;\r\n      line-height: 1.4;\r\n    }\r\n  }\r\n\r\n  .sales-stock {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    margin-bottom: 20rpx;\r\n    font-size: 26rpx;\r\n    color: #8E8E93;\r\n  }\r\n\r\n  .progress-section {\r\n    margin-top: 20rpx;\r\n    margin-bottom: 30rpx;\r\n\r\n    .progress-bar {\r\n      width: 100%;\r\n      height: 12rpx;\r\n      background-color: #E0E0E0;\r\n      border-radius: 6rpx;\r\n      overflow: hidden;\r\n      margin-bottom: 10rpx;\r\n      box-shadow: inset 0 2rpx 4rpx rgba(0, 0, 0, 0.1);\r\n    }\r\n\r\n    .progress-fill {\r\n      height: 100%;\r\n      background: linear-gradient(to right, #FF3B30, #FF6B6B);\r\n      border-radius: 6rpx;\r\n      box-shadow: 0 2rpx 4rpx rgba(255, 59, 48, 0.2);\r\n    }\r\n\r\n    .progress-text {\r\n      font-size: 24rpx;\r\n      color: #8E8E93;\r\n      text-align: right;\r\n    }\r\n  }\r\n}\r\n\r\n/* 商家信息卡片 */\r\n.shop-card {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  background-color: #FFFFFF;\r\n  border-radius: 35rpx;\r\n  padding: 25rpx 30rpx;\r\n  margin: 0 30rpx 30rpx 30rpx;\r\n  box-shadow: 0 15rpx 35rpx rgba(0, 0, 0, 0.08);\r\n  transform: translateZ(0);\r\n  transition: transform 0.3s ease, box-shadow 0.3s ease;\r\n  \r\n  &:active {\r\n    transform: translateY(5rpx);\r\n    box-shadow: 0 10rpx 25rpx rgba(0, 0, 0, 0.06);\r\n  }\r\n  \r\n  .shop-info {\r\n    display: flex;\r\n    align-items: center;\r\n  }\r\n  \r\n  .shop-logo {\r\n    width: 80rpx;\r\n    height: 80rpx;\r\n    border-radius: 50%;\r\n    margin-right: 20rpx;\r\n    box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.1);\r\n  }\r\n  \r\n  .shop-details {\r\n    display: flex;\r\n    flex-direction: column;\r\n  }\r\n  \r\n  .shop-name {\r\n    font-size: 30rpx;\r\n    font-weight: 600;\r\n    color: #333333;\r\n    margin-bottom: 10rpx;\r\n  }\r\n  \r\n  .shop-rating {\r\n    display: flex;\r\n    align-items: center;\r\n  }\r\n  \r\n  .rating-stars {\r\n    display: flex;\r\n    margin-right: 10rpx;\r\n  }\r\n  \r\n  .star-icon {\r\n    width: 24rpx;\r\n    height: 24rpx;\r\n    margin-right: 4rpx;\r\n  }\r\n  \r\n  .rating-score {\r\n    font-size: 24rpx;\r\n    color: #FF9500;\r\n    font-weight: 600;\r\n  }\r\n  \r\n  .shop-action {\r\n    display: flex;\r\n    align-items: center;\r\n    font-size: 28rpx;\r\n    color: #8E8E93;\r\n    \r\n    .arrow-icon {\r\n      font-size: 32rpx;\r\n      margin-left: 5rpx;\r\n    }\r\n  }\r\n}\r\n\r\n/* 商品详情卡片 */\r\n.detail-card {\r\n  background-color: #FFFFFF;\r\n  border-radius: 35rpx;\r\n  padding: 30rpx;\r\n  margin: 0 30rpx 30rpx 30rpx;\r\n  box-shadow: 0 15rpx 35rpx rgba(0, 0, 0, 0.08);\r\n  \r\n  .detail-header {\r\n    margin-bottom: 20rpx;\r\n    border-bottom: 1rpx solid #EFEFEF;\r\n    padding-bottom: 20rpx;\r\n  }\r\n  \r\n  .detail-title {\r\n    font-size: 32rpx;\r\n    font-weight: 600;\r\n    color: #333333;\r\n  }\r\n  \r\n  .detail-content {\r\n    display: flex;\r\n    flex-direction: column;\r\n  }\r\n  \r\n  .detail-text {\r\n    font-size: 28rpx;\r\n    color: #666666;\r\n    line-height: 1.6;\r\n    margin-bottom: 20rpx;\r\n  }\r\n  \r\n  .detail-image {\r\n    width: 100%;\r\n    margin-bottom: 20rpx;\r\n    border-radius: 20rpx;\r\n    box-shadow: 0 8rpx 16rpx rgba(0, 0, 0, 0.05);\r\n  }\r\n}\r\n\r\n/* 底部购买栏 */\r\n.bottom-bar {\r\n  position: fixed;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  height: 100rpx;\r\n  background-color: #FFFFFF;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n  padding: 0 30rpx;\r\n  box-shadow: 0 -4rpx 12rpx rgba(0, 0, 0, 0.05);\r\n  z-index: 99;\r\n  \r\n  .bottom-left {\r\n    display: flex;\r\n    align-items: center;\r\n  }\r\n  \r\n  .action-btn {\r\n    display: flex;\r\n    flex-direction: column;\r\n    align-items: center;\r\n    margin-right: 30rpx;\r\n    \r\n    .action-icon {\r\n      width: 40rpx;\r\n      height: 40rpx;\r\n      margin-bottom: 5rpx;\r\n    }\r\n    \r\n    text {\r\n      font-size: 22rpx;\r\n      color: #8E8E93;\r\n    }\r\n  }\r\n  \r\n  .buy-buttons {\r\n    display: flex;\r\n  }\r\n  \r\n  .buy-btn {\r\n    display: flex;\r\n    flex-direction: column;\r\n    align-items: center;\r\n    justify-content: center;\r\n    width: 240rpx;\r\n    height: 80rpx;\r\n    border-radius: 40rpx;\r\n    box-shadow: 0 8rpx 16rpx rgba(255, 59, 48, 0.2);\r\n    transition: transform 0.3s ease, box-shadow 0.3s ease;\r\n    \r\n    &:active {\r\n      transform: translateY(5rpx);\r\n      box-shadow: 0 4rpx 8rpx rgba(255, 59, 48, 0.15);\r\n    }\r\n  }\r\n  \r\n  .flash-buy {\r\n    background: linear-gradient(135deg, #FF3B30 0%, #FF6B6B 100%);\r\n    \r\n    .buy-price {\r\n      font-size: 24rpx;\r\n      color: rgba(255, 255, 255, 0.9);\r\n      margin-bottom: 2rpx;\r\n    }\r\n    \r\n    .buy-label {\r\n      font-size: 28rpx;\r\n      font-weight: 600;\r\n      color: #FFFFFF;\r\n    }\r\n  }\r\n}\r\n</style> ", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/subPackages/activity-showcase/pages/flash-sale/detail.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni"], "mappings": ";;;AA8JA,MAAO,sBAAqB,MAAW;AAEvC,MAAK,YAAU;AAAA,EACb,YAAY;AAAA,IACV;AAAA,EACD;AAAA,EACD,OAAO;AACL,WAAO;AAAA,MACL,IAAI;AAAA,MACJ,iBAAiB;AAAA,MACjB,cAAc;AAAA,MACd,SAAS;AAAA,MACT,SAAS,CAAE;AAAA,MACX,WAAW;AAAA,QACT,OAAO;AAAA,QACP,SAAS;AAAA,QACT,SAAS;AAAA,MACV;AAAA,MACD,eAAe;AAAA,MACf,YAAY;AAAA,MACZ,OAAO;AAAA;AAAA,IACT;AAAA,EACD;AAAA,EACD,UAAU;AAAA,IACR,kBAAkB;AAChB,UAAI,CAAC,KAAK,QAAQ,cAAc,CAAC,KAAK,QAAQ;AAAe,eAAO;AACpE,YAAM,WAAY,WAAW,KAAK,QAAQ,UAAU,IAAI,WAAW,KAAK,QAAQ,aAAa,IAAK;AAClG,aAAO,SAAS,QAAQ,CAAC;AAAA,IAC3B;AAAA,EACD;AAAA,EACD,OAAO,SAAS;AACd,QAAI,WAAW,QAAQ,IAAI;AACzB,WAAK,KAAK,QAAQ;AAAA,IACpB;AAGA,UAAM,aAAaA,oBAAI;AACvB,SAAK,kBAAkB,WAAW;AAClC,SAAK,eAAe,KAAK,kBAAkB;AAG3C,eAAW,MAAM;AACf,WAAK,kBAAiB;AAAA,IACvB,GAAE,GAAG;AAAA,EACP;AAAA,EACD,SAAS;AAAA;AAAA,IAEP,SAAS;AACPA,oBAAG,MAAC,aAAY;AAAA,IACjB;AAAA;AAAA,IAGD,oBAAoB;AAElB,WAAK,UAAU;AAGf,iBAAW,MAAM;AAEf,aAAK,UAAU;AAAA,UACb,IAAI,KAAK,MAAM;AAAA,UACf,OAAO;AAAA,UACP,QAAQ;AAAA,YACN;AAAA,YACA;AAAA,YACA;AAAA,UACD;AAAA,UACD,cAAc;AAAA,YACZ;AAAA,YACA;AAAA,UACD;AAAA,UACD,YAAY;AAAA,UACZ,eAAe;AAAA,UACf,WAAW;AAAA,UACX,YAAY;AAAA,UACZ,WAAW,oBAAI,KAAM;AAAA,UACrB,SAAS,IAAI,KAAK,KAAK,QAAQ,KAAK,KAAK,KAAK,GAAI;AAAA,UAClD,aAAa;AAAA,UACb,UAAU;AAAA,UACV,UAAU;AAAA,UACV,YAAY;AAAA,UACZ,OAAO;AAAA,UACP,gBAAgB;AAAA;AAGlB,aAAK,UAAU;AACf,aAAK,eAAc;AACnB,aAAK,kBAAiB;AAAA,MACvB,GAAE,GAAI;AAAA,IACR;AAAA;AAAA,IAGD,iBAAiB;AACf,YAAM,UAAU,KAAK,QAAQ;AAC7B,YAAM,kBAAkB,MAAM;AAC5B,cAAM,MAAM,oBAAI;AAChB,cAAM,OAAO,UAAU;AAEvB,YAAI,QAAQ,GAAG;AAEb,eAAK,YAAY;AAAA,YACf,OAAO;AAAA,YACP,SAAS;AAAA,YACT,SAAS;AAAA;AAEX,wBAAc,KAAK,KAAK;AACxB;AAAA,QACF;AAGA,cAAM,QAAQ,KAAK,MAAM,QAAQ,MAAO,KAAK,GAAG;AAChD,cAAM,UAAU,KAAK,MAAO,QAAQ,MAAO,KAAK,OAAQ,MAAO,GAAG;AAClE,cAAM,UAAU,KAAK,MAAO,QAAQ,MAAO,MAAO,GAAI;AAGtD,aAAK,YAAY;AAAA,UACf,OAAO,MAAM,SAAQ,EAAG,SAAS,GAAG,GAAG;AAAA,UACvC,SAAS,QAAQ,SAAQ,EAAG,SAAS,GAAG,GAAG;AAAA,UAC3C,SAAS,QAAQ,SAAQ,EAAG,SAAS,GAAG,GAAG;AAAA;;AAK/C;AAGA,WAAK,QAAQ,YAAY,iBAAiB,GAAI;AAAA,IAC/C;AAAA;AAAA,IAGD,oBAAoB;AAClB,YAAM,cAAe,KAAK,QAAQ,YAAY,KAAK,QAAQ,aAAc;AACzE,WAAK,gBAAgB,cAAc;AAAA,IACpC;AAAA;AAAA,IAGD,WAAW;AACTA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,yBAAyB,KAAK,QAAQ,UAAU,CAAC;AAAA,MACxD,CAAC;AAAA,IACF;AAAA;AAAA,IAGD,iBAAiB;AACfA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,MACR,CAAC;AAAA,IACF;AAAA;AAAA,IAGD,iBAAiB;AACf,WAAK,aAAa,CAAC,KAAK;AACxBA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO,KAAK,aAAa,SAAS;AAAA,QAClC,MAAM;AAAA,MACR,CAAC;AAAA,IACF;AAAA;AAAA,IAGD,SAAS;AACPA,oBAAAA,MAAI,YAAY;AAAA,QACd,OAAO;AAAA,MACT,CAAC;AAGD,iBAAW,MAAM;AACfA,sBAAG,MAAC,YAAW;AAGfA,sBAAAA,MAAI,WAAW;AAAA,UACb,KAAK,2BAA2B,KAAK,QAAQ,UAAU,UAAU,mBAAmB,KAAK,QAAQ,KAAK,CAAC;AAAA,QACzG,CAAC;AAAA,MACF,GAAE,GAAG;AAAA,IACP;AAAA;AAAA,IAGD,QAAQ;AACNA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,MACR,CAAC;AAAA,IACH;AAAA,EACD;AAAA,EACD,WAAW;AAET,QAAI,KAAK,OAAO;AACd,oBAAc,KAAK,KAAK;AAAA,IAC1B;AAAA,EACF;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC3VA,GAAG,WAAW,eAAe;"}