
/* 拼团活动卡片特有样式 */
.group-buy-card.data-v-3b272413 {
  /* 继承基础卡片样式 */
}

/* 拼团特有信息区域 */
.group-buy-special.data-v-3b272413 {
  padding: 20rpx;
  background-color: rgba(52, 199, 89, 0.05);
  border-radius: 20rpx;
  margin-bottom: 20rpx;
}

/* 价格区域 */
.price-section.data-v-3b272413 {
  display: flex;
  align-items: baseline;
  margin-bottom: 20rpx;
}
.current-price.data-v-3b272413 {
  display: flex;
  align-items: baseline;
  color: #34c759;
  margin-right: 16rpx;
}
.price-symbol.data-v-3b272413 {
  font-size: 24rpx;
  font-weight: 500;
}
.price-value.data-v-3b272413 {
  font-size: 40rpx;
  font-weight: 700;
}
.original-price.data-v-3b272413 {
  display: flex;
  align-items: baseline;
  margin-right: 16rpx;
}
.price-label.data-v-3b272413 {
  font-size: 22rpx;
  color: #8e8e93;
  margin-right: 4rpx;
}
.price-through.data-v-3b272413 {
  font-size: 24rpx;
  color: #8e8e93;
  text-decoration: line-through;
}
.discount-tag.data-v-3b272413 {
  padding: 4rpx 10rpx;
  background-color: rgba(52, 199, 89, 0.1);
  border-radius: 10rpx;
}
.discount-value.data-v-3b272413 {
  font-size: 22rpx;
  color: #34c759;
  font-weight: 500;
}

/* 拼团进度 */
.group-progress.data-v-3b272413 {
  margin-bottom: 20rpx;
}
.progress-header.data-v-3b272413 {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10rpx;
}
.progress-title.data-v-3b272413 {
  font-size: 24rpx;
  color: #000000;
  font-weight: 500;
}
.progress-status.data-v-3b272413 {
  font-size: 24rpx;
  color: #34c759;
  font-weight: 500;
}
.progress-bar.data-v-3b272413 {
  height: 10rpx;
  background-color: rgba(52, 199, 89, 0.1);
  border-radius: 5rpx;
  overflow: hidden;
  margin-bottom: 10rpx;
}
.progress-inner.data-v-3b272413 {
  height: 100%;
  background-color: #34c759;
  border-radius: 5rpx;
  transition: width 0.3s ease;
}
.progress-tip.data-v-3b272413 {
  margin-top: 8rpx;
}
.tip-text.data-v-3b272413 {
  font-size: 22rpx;
  color: #ff9500;
}

/* 拼团成员 */
.group-members.data-v-3b272413 {
  margin-top: 16rpx;
}
.members-title.data-v-3b272413 {
  font-size: 24rpx;
  color: #000000;
  font-weight: 500;
  margin-bottom: 12rpx;
}
.members-avatars.data-v-3b272413 {
  display: flex;
  align-items: center;
}
.member-avatar-wrapper.data-v-3b272413 {
  position: relative;
  margin-right: 20rpx;
}
.member-avatar.data-v-3b272413 {
  width: 60rpx;
  height: 60rpx;
  border-radius: 30rpx;
  border: 2rpx solid #ffffff;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}
.team-leader-badge.data-v-3b272413 {
  position: absolute;
  bottom: -10rpx;
  left: 50%;
  transform: translateX(-50%);
  background-color: #ff9500;
  color: #ffffff;
  font-size: 18rpx;
  padding: 2rpx 8rpx;
  border-radius: 10rpx;
  white-space: nowrap;
}
.more-members.data-v-3b272413 {
  width: 60rpx;
  height: 60rpx;
  border-radius: 30rpx;
  background-color: #f2f2f7;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}
.more-members text.data-v-3b272413 {
  font-size: 20rpx;
  color: #8e8e93;
}
