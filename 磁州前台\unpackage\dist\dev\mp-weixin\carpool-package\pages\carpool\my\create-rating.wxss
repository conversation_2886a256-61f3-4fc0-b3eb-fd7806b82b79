/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 移除阿里妈妈字体引用，改用系统默认字体 */
/* 移除原有阿里妈妈字体定义
$uni-font-family: 'AlimamaShuHeiTi';
$uni-font-family-text: 'AlimamaShuHeiTi', -apple-system, BlinkMacSystemFont, 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;
*/
body, html, #app, .index-container {
  font-family: "AlimamaShuHeiTi", -apple-system, BlinkMacSystemFont, "Helvetica Neue", "PingFang SC", "Microsoft YaHei", sans-serif;
}
.rating-container {
  min-height: 100vh;
  background-color: #F5F8FC;
  position: relative;
  padding-top: calc(90rpx + var(--status-bar-height, 40px));
  padding-bottom: 40rpx;
}

/* 自定义导航栏 */
.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  background: linear-gradient(135deg, #0A84FF, #0040DD);
  z-index: 100;
  display: flex;
  flex-direction: column;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
  padding-top: var(--status-bar-height, 40px);
  /* 增加高度确保有足够空间 */
  min-height: 120rpx;
}
.navbar-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 90rpx;
  padding: 0 24rpx;
  /* 确保内容有足够的灵活性 */
  min-height: 90rpx;
  flex-wrap: nowrap;
}
.navbar-left {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.back-icon {
  width: 40rpx;
  height: 40rpx;
}
.navbar-title {
  font-size: 38rpx;
  font-weight: 600;
  color: #ffffff;
  letter-spacing: 1rpx;
  text-align: center;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}
.navbar-right {
  width: 60rpx;
  height: 60rpx;
}

/* 司机信息 */
.driver-info {
  margin: 20rpx 32rpx;
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 24rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);
}
.driver-avatar-container {
  width: 120rpx;
  height: 120rpx;
  border-radius: 60rpx;
  overflow: hidden;
  margin-bottom: 16rpx;
  border: 2rpx solid #F2F2F7;
}
.driver-avatar {
  width: 100%;
  height: 100%;
  border-radius: 50%;
}
.driver-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 8rpx;
}
.driver-car {
  font-size: 24rpx;
  color: #666666;
}

/* 行程信息 */
.trip-info {
  margin: 0 32rpx 32rpx;
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 24rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);
}
.trip-route {
  padding: 16rpx 0;
}
.route-point {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}
.point {
  width: 20rpx;
  height: 20rpx;
  border-radius: 10rpx;
  margin-right: 16rpx;
}
.point.start {
  background-color: #34C759;
}
.point.end {
  background-color: #FF3B30;
}
.point-text {
  font-size: 28rpx;
  color: #333333;
}
.route-line {
  width: 2rpx;
  height: 40rpx;
  background-color: #DDDDDD;
  margin-left: 9rpx;
  margin-bottom: 16rpx;
}
.trip-time, .trip-date {
  display: flex;
  margin-top: 8rpx;
}
.time-label, .date-label {
  font-size: 26rpx;
  color: #666666;
  width: 150rpx;
}
.time-value, .date-value {
  font-size: 26rpx;
  color: #333333;
  font-weight: 500;
}

/* 评分部分 */
.rating-section, .tags-section, .comment-section {
  margin: 0 32rpx 32rpx;
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 24rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);
}
.section-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 20rpx;
}
.star-rating {
  display: flex;
  justify-content: center;
  margin-bottom: 16rpx;
}
.star-item {
  padding: 0 10rpx;
}
.star-icon {
  width: 60rpx;
  height: 60rpx;
}
.rating-text {
  text-align: center;
  font-size: 26rpx;
  color: #666666;
}

/* 评价标签 */
.tags-container {
  display: flex;
  flex-wrap: wrap;
}
.tag-item {
  padding: 12rpx 24rpx;
  background-color: #F5F5F5;
  border-radius: 30rpx;
  margin-right: 16rpx;
  margin-bottom: 16rpx;
  font-size: 26rpx;
  color: #666666;
}
.tag-item.selected {
  background-color: #E1F0FF;
  color: #0A84FF;
  border: 1rpx solid #0A84FF;
}

/* 评价内容 */
.comment-input {
  width: 100%;
  height: 200rpx;
  background-color: #F5F5F5;
  border-radius: 8rpx;
  padding: 16rpx;
  font-size: 28rpx;
  box-sizing: border-box;
}
.word-count {
  text-align: right;
  font-size: 24rpx;
  color: #999999;
  margin-top: 8rpx;
}

/* 匿名评价 */
.anonymous-section {
  margin: 0 32rpx 32rpx;
  padding: 0 24rpx;
}
.checkbox-label {
  display: flex;
  align-items: center;
}
.anonymous-text {
  font-size: 28rpx;
  color: #666666;
}

/* 提交按钮 */
.submit-btn {
  width: calc(100% - 64rpx);
  height: 90rpx;
  background: linear-gradient(135deg, #0A84FF, #0040DD);
  color: #FFFFFF;
  font-size: 32rpx;
  font-weight: 500;
  border-radius: 45rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 32rpx;
}
.submit-btn[disabled] {
  background: #CCCCCC;
  color: #FFFFFF;
}
.safe-area-bottom {
  height: env(safe-area-inset-bottom);
  width: 100%;
}