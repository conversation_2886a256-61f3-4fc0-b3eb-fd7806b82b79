{"version": 3, "file": "index.js", "sources": ["subPackages/merchant-admin/pages/analysis/index.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/c3ViUGFja2FnZXNcbWVyY2hhbnQtYWRtaW5ccGFnZXNcYW5hbHlzaXNcaW5kZXgudnVl"], "sourcesContent": ["<template>\r\n  <view class=\"analysis-container\">\r\n    <!-- 自定义导航栏 -->\r\n    <view class=\"navbar\">\r\n      <view class=\"navbar-back\" @click=\"goBack\">\r\n        <view class=\"back-icon\"></view>\r\n      </view>\r\n      <text class=\"navbar-title\">分析洞察</text>\r\n      <view class=\"navbar-right\">\r\n        <view class=\"help-icon\">?</view>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 日期选择器 -->\r\n    <view class=\"date-selector-container\">\r\n      <view class=\"date-selector\" @click=\"showDatePicker\">\r\n        <text class=\"date-text\">{{currentDateRange}}</text>\r\n        <text class=\"date-icon\">📅</text>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 销售分析模块 -->\r\n    <view class=\"analysis-section\">\r\n      <view class=\"section-header\">\r\n        <text class=\"section-title\">销售分析</text>\r\n        <text class=\"section-more\" @click=\"navigateTo('./sales')\">查看更多</text>\r\n      </view>\r\n      \r\n      <view class=\"analysis-grid\">\r\n        <!-- 商品销量分析 -->\r\n        <view class=\"analysis-card\" @click=\"navigateTo('./sales/product')\">\r\n          <view class=\"card-icon sales-icon\">📊</view>\r\n          <text class=\"card-name\">商品销量分析</text>\r\n          <text class=\"card-desc\">热销品/滞销品识别</text>\r\n        </view>\r\n        \r\n        <!-- 时段分析 -->\r\n        <view class=\"analysis-card\" @click=\"navigateTo('./sales/time')\">\r\n          <view class=\"card-icon time-icon\">⏱️</view>\r\n          <text class=\"card-name\">时段分析</text>\r\n          <text class=\"card-desc\">销售高峰/低谷识别</text>\r\n        </view>\r\n        \r\n        <!-- 利润分析 -->\r\n        <view class=\"analysis-card\" @click=\"navigateTo('./sales/profit')\">\r\n          <view class=\"card-icon profit-icon\">💰</view>\r\n          <text class=\"card-name\">利润分析</text>\r\n          <text class=\"card-desc\">高利润/低利润商品分析</text>\r\n        </view>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 客户分析模块 -->\r\n    <view class=\"analysis-section\">\r\n      <view class=\"section-header\">\r\n        <text class=\"section-title\">客户分析</text>\r\n        <text class=\"section-more\" @click=\"navigateTo('./customer')\">查看更多</text>\r\n      </view>\r\n      \r\n      <view class=\"analysis-grid\">\r\n        <!-- 新老客户占比 -->\r\n        <view class=\"analysis-card\" @click=\"navigateTo('./customer/new-old')\">\r\n          <view class=\"card-icon customer-icon\">👥</view>\r\n          <text class=\"card-name\">新老客户占比</text>\r\n          <text class=\"card-desc\">客户构成分析</text>\r\n        </view>\r\n        \r\n        <!-- 客户生命周期分析 -->\r\n        <view class=\"analysis-card\" @click=\"navigateTo('./customer/lifecycle')\">\r\n          <view class=\"card-icon lifecycle-icon\">🔄</view>\r\n          <text class=\"card-name\">客户生命周期分析</text>\r\n          <text class=\"card-desc\">客户留存与流失</text>\r\n        </view>\r\n        \r\n        <!-- 客户价值分布 -->\r\n        <view class=\"analysis-card\" @click=\"navigateTo('./customer/value')\">\r\n          <view class=\"card-icon value-icon\">💎</view>\r\n          <text class=\"card-name\">客户价值分布</text>\r\n          <text class=\"card-desc\">高价值客户识别</text>\r\n        </view>\r\n        \r\n        <!-- 流失预警与召回建议 -->\r\n        <view class=\"analysis-card\" @click=\"navigateTo('./customer/churn')\">\r\n          <view class=\"card-icon churn-icon\">⚠️</view>\r\n          <text class=\"card-name\">流失预警与召回建议</text>\r\n          <text class=\"card-desc\">客户流失风险管理</text>\r\n        </view>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 营销效果分析 -->\r\n    <view class=\"analysis-section\">\r\n      <view class=\"section-header\">\r\n        <text class=\"section-title\">营销效果分析</text>\r\n        <text class=\"section-more\" @click=\"navigateTo('./marketing')\">查看更多</text>\r\n      </view>\r\n      \r\n      <view class=\"analysis-grid\">\r\n        <!-- 各类营销活动ROI对比 -->\r\n        <view class=\"analysis-card\" @click=\"navigateTo('./marketing/roi')\">\r\n          <view class=\"card-icon roi-icon\">📈</view>\r\n          <text class=\"card-name\">营销活动ROI对比</text>\r\n          <text class=\"card-desc\">投资回报率分析</text>\r\n        </view>\r\n        \r\n        <!-- 引流转化漏斗分析 -->\r\n        <view class=\"analysis-card\" @click=\"navigateTo('./marketing/funnel')\">\r\n          <view class=\"card-icon funnel-icon\">🔍</view>\r\n          <text class=\"card-name\">引流转化漏斗分析</text>\r\n          <text class=\"card-desc\">客户转化路径优化</text>\r\n        </view>\r\n        \r\n        <!-- 促销敏感度分析 -->\r\n        <view class=\"analysis-card\" @click=\"navigateTo('./marketing/sensitivity')\">\r\n          <view class=\"card-icon sensitivity-icon\">🎯</view>\r\n          <text class=\"card-name\">促销敏感度分析</text>\r\n          <text class=\"card-desc\">促销效果评估</text>\r\n        </view>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 竞争力分析 -->\r\n    <view class=\"analysis-section\">\r\n      <view class=\"section-header\">\r\n        <text class=\"section-title\">竞争力分析</text>\r\n        <text class=\"section-more\" @click=\"navigateTo('./competition')\">查看更多</text>\r\n      </view>\r\n      \r\n      <view class=\"analysis-grid\">\r\n        <!-- 行业对标分析 -->\r\n        <view class=\"analysis-card\" @click=\"navigateTo('./competition/industry')\">\r\n          <view class=\"card-icon industry-icon\">🏢</view>\r\n          <text class=\"card-name\">行业对标分析</text>\r\n          <text class=\"card-desc\">行业位置评估</text>\r\n        </view>\r\n        \r\n        <!-- 价格竞争力分析 -->\r\n        <view class=\"analysis-card\" @click=\"navigateTo('./competition/price')\">\r\n          <view class=\"card-icon price-icon\">💲</view>\r\n          <text class=\"card-name\">价格竞争力分析</text>\r\n          <text class=\"card-desc\">价格策略优化</text>\r\n        </view>\r\n        \r\n        <!-- 差异化优势识别 -->\r\n        <view class=\"analysis-card\" @click=\"navigateTo('./competition/advantage')\">\r\n          <view class=\"card-icon advantage-icon\">🌟</view>\r\n          <text class=\"card-name\">差异化优势识别</text>\r\n          <text class=\"card-desc\">特色优势分析</text>\r\n        </view>\r\n      </view>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  data() {\r\n    return {\r\n      currentDateRange: '2023-05-01 至 2023-05-31'\r\n    }\r\n  },\r\n  methods: {\r\n    goBack() {\r\n      uni.navigateBack();\r\n    },\r\n    navigateTo(url) {\r\n      uni.navigateTo({ url });\r\n    },\r\n    showDatePicker() {\r\n      uni.showToast({\r\n        title: '日期选择功能开发中',\r\n        icon: 'none'\r\n      });\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style>\r\n.analysis-container {\r\n  min-height: 100vh;\r\n  background-color: #f5f7fa;\r\n  padding-bottom: 20px;\r\n}\r\n\r\n.navbar {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n  padding: 44px 16px 10px;\r\n  background: linear-gradient(135deg, #1677FF, #065DD2);\r\n  position: relative;\r\n  z-index: 100;\r\n}\r\n\r\n.navbar-back {\r\n  width: 40px;\r\n  height: 40px;\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.back-icon {\r\n  width: 12px;\r\n  height: 12px;\r\n  border-left: 2px solid #fff;\r\n  border-bottom: 2px solid #fff;\r\n  transform: rotate(45deg);\r\n}\r\n\r\n.navbar-title {\r\n  color: #fff;\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n  flex: 1;\r\n  text-align: center;\r\n}\r\n\r\n.navbar-right {\r\n  width: 40px;\r\n  height: 40px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: flex-end;\r\n}\r\n\r\n.help-icon {\r\n  width: 24px;\r\n  height: 24px;\r\n  border-radius: 12px;\r\n  background: rgba(255, 255, 255, 0.2);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  color: #fff;\r\n  font-size: 14px;\r\n  font-weight: 600;\r\n}\r\n\r\n.date-selector-container {\r\n  padding: 16px;\r\n  display: flex;\r\n  justify-content: flex-end;\r\n}\r\n\r\n.date-selector {\r\n  display: flex;\r\n  align-items: center;\r\n  background-color: #fff;\r\n  padding: 8px 12px;\r\n  border-radius: 16px;\r\n  box-shadow: 0 2px 4px rgba(0,0,0,0.05);\r\n}\r\n\r\n.date-text {\r\n  font-size: 14px;\r\n  color: #666;\r\n  margin-right: 8px;\r\n}\r\n\r\n.date-icon {\r\n  font-size: 16px;\r\n}\r\n\r\n.analysis-section {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.section-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 0 16px;\r\n  margin-bottom: 12px;\r\n}\r\n\r\n.section-title {\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  color: #333;\r\n}\r\n\r\n.section-more {\r\n  font-size: 12px;\r\n  color: #1677FF;\r\n}\r\n\r\n.analysis-grid {\r\n  padding: 0 16px;\r\n  display: grid;\r\n  grid-template-columns: 1fr 1fr;\r\n  gap: 12px;\r\n}\r\n\r\n.analysis-card {\r\n  background-color: #fff;\r\n  border-radius: 8px;\r\n  padding: 16px;\r\n  box-shadow: 0 2px 8px rgba(0,0,0,0.05);\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n}\r\n\r\n.card-icon {\r\n  width: 48px;\r\n  height: 48px;\r\n  border-radius: 24px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  font-size: 24px;\r\n  margin-bottom: 12px;\r\n}\r\n\r\n.sales-icon, .roi-icon {\r\n  background-color: #e6f7ff;\r\n  color: #1677FF;\r\n}\r\n\r\n.time-icon, .funnel-icon {\r\n  background-color: #f6ffed;\r\n  color: #52c41a;\r\n}\r\n\r\n.profit-icon, .sensitivity-icon {\r\n  background-color: #fff7e6;\r\n  color: #fa8c16;\r\n}\r\n\r\n.customer-icon, .industry-icon {\r\n  background-color: #e6f7ff;\r\n  color: #1677FF;\r\n}\r\n\r\n.lifecycle-icon, .price-icon {\r\n  background-color: #f6ffed;\r\n  color: #52c41a;\r\n}\r\n\r\n.value-icon, .advantage-icon {\r\n  background-color: #fff7e6;\r\n  color: #fa8c16;\r\n}\r\n\r\n.churn-icon {\r\n  background-color: #fff1f0;\r\n  color: #f5222d;\r\n}\r\n\r\n.card-name {\r\n  font-size: 14px;\r\n  font-weight: 500;\r\n  color: #333;\r\n  margin-bottom: 4px;\r\n  text-align: center;\r\n}\r\n\r\n.card-desc {\r\n  font-size: 12px;\r\n  color: #999;\r\n  text-align: center;\r\n}\r\n</style> ", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/subPackages/merchant-admin/pages/analysis/index.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni"], "mappings": ";;AA2JA,MAAK,YAAU;AAAA,EACb,OAAO;AACL,WAAO;AAAA,MACL,kBAAkB;AAAA,IACpB;AAAA,EACD;AAAA,EACD,SAAS;AAAA,IACP,SAAS;AACPA,oBAAG,MAAC,aAAY;AAAA,IACjB;AAAA,IACD,WAAW,KAAK;AACdA,oBAAAA,MAAI,WAAW,EAAE,IAAE,CAAG;AAAA,IACvB;AAAA,IACD,iBAAiB;AACfA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,MACR,CAAC;AAAA,IACH;AAAA,EACF;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;AC9KA,GAAG,WAAW,eAAe;"}