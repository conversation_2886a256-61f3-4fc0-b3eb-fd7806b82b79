<!-- 消息详情页面开始 -->
<template>
  <view class="message-detail page-container">
    <!-- 消息头部 -->
    <view class="message-header card">
      <view class="title">{{ message.title }}</view>
      <view class="meta">
        <text class="time">{{ formatTime(message.createTime) }}</text>
        <text class="type">{{ message.type }}</text>
      </view>
    </view>
    
    <!-- 消息内容 -->
    <view class="message-content card">
      <rich-text :nodes="message.content"></rich-text>
      <view v-if="message.images?.length" class="image-list">
        <image 
          v-for="(img, index) in message.images" 
          :key="index"
          :src="img"
          mode="widthFix"
          @click="previewImage(index)"
        />
      </view>
    </view>
    
    <!-- 相关操作 -->
    <view class="message-actions card">
      <button class="btn-primary" @click="handleAction">
        {{ message.actionText || '查看详情' }}
      </button>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { formatTime } from '@/utils/date'

// 消息数据
const message = ref({
  title: '',
  content: '',
  type: '',
  createTime: new Date(),
  images: [],
  actionText: ''
})

// 获取消息详情
const getMessageDetail = async (id: string) => {
  try {
    // TODO: 调用接口获取消息详情
    message.value = {
      title: '活动通知',
      content: '您关注的活动即将开始...',
      type: '系统通知',
      createTime: new Date(),
      images: [],
      actionText: '立即查看'
    }
  } catch (error) {
    console.error('获取消息详情失败：', error)
  }
}

// 图片预览
const previewImage = (index: number) => {
  uni.previewImage({
    current: index,
    urls: message.value.images
  })
}

// 处理操作按钮点击
const handleAction = () => {
  // TODO: 根据消息类型处理不同操作
  uni.showToast({
    title: '操作成功',
    icon: 'success'
  })
}

onMounted(() => {
  const id = ''  // TODO: 从路由参数获取消息ID
  getMessageDetail(id)
})
</script>

<style lang="scss" scoped>
.message-detail {
  .message-header {
    .title {
      font-size: 36rpx;
      font-weight: 600;
      color: #333;
      margin-bottom: 16rpx;
    }
    
    .meta {
      display: flex;
      align-items: center;
      font-size: 24rpx;
      color: #999;
      
      .time {
        margin-right: 16rpx;
      }
      
      .type {
        background: #f0f0f0;
        padding: 4rpx 12rpx;
        border-radius: 4rpx;
      }
    }
  }
  
  .message-content {
    margin-top: 24rpx;
    
    :deep(rich-text) {
      font-size: 28rpx;
      line-height: 1.6;
      color: #666;
    }
    
    .image-list {
      margin-top: 24rpx;
      
      image {
        width: 100%;
        border-radius: 8rpx;
        margin-bottom: 16rpx;
      }
    }
  }
  
  .message-actions {
    margin-top: 24rpx;
    display: flex;
    justify-content: center;
  }
}
</style>
<!-- 消息详情页面结束 --> 