<template>
  <view class="job-seeking-container">
    <!-- 自定义导航栏 -->
    <view class="custom-navbar" :style="{ paddingTop: statusBarHeight + 'px' }">
      <view class="navbar-left" @click="goBack">
        <image src="/static/images/tabbar/返回键.png" class="back-icon"></image>
      </view>
      <view class="navbar-title">求职信息详情</view>
      <view class="navbar-right">
        <!-- 举报按钮已删除 -->
      </view>
    </view>
    
    <!-- 隐藏的分享按钮，用于自动触发 -->
    <button id="shareButton" class="hidden-share-btn" open-type="share"></button>
    
    <view class="job-seeking-wrapper">
      <!-- 求职基本信息卡片 -->
      <view class="content-card seeking-info-card">
        <view class="seeking-header">
          <view class="seeking-title">{{seekingData.title}}</view>
          <view class="seeking-salary">{{seekingData.salary}}</view>
          <view class="seeking-meta">
            <view class="seeking-tag-group">
              <view class="seeking-tag" v-for="(tag, index) in seekingData.tags" :key="index">{{tag}}</view>
            </view>
            <text class="seeking-publish-time">发布于 {{formatTime(seekingData.publishTime)}}</text>
          </view>
        </view>
        
        <!-- 基本信息 -->
        <view class="seeking-basic-info">
          <view class="info-item">
            <text class="info-label">求职者</text>
            <text class="info-value">{{seekingData.name}}</text>
          </view>
          <view class="info-item">
            <text class="info-label">年龄</text>
            <text class="info-value">{{seekingData.age}}岁</text>
          </view>
          <view class="info-item">
            <text class="info-label">学历</text>
            <text class="info-value">{{seekingData.education}}</text>
          </view>
          <view class="info-item">
            <text class="info-label">工作经验</text>
            <text class="info-value">{{seekingData.experience}}</text>
          </view>
        </view>
      </view>
      
      <!-- 求职意向 -->
      <view class="content-card seeking-intention-card">
        <view class="section-title">求职意向</view>
        <view class="intention-list">
          <view class="intention-item" v-for="(item, index) in seekingData.intentions" :key="index">
            <text class="intention-label">{{item.label}}</text>
            <text class="intention-value">{{item.value}}</text>
          </view>
        </view>
      </view>
      
      <!-- 工作经验 -->
      <view class="content-card work-experience-card">
        <view class="section-title">工作经验</view>
        <view class="experience-list">
          <view class="experience-item" v-for="(item, index) in seekingData.workExperience" :key="index">
            <view class="experience-header">
              <text class="experience-company">{{item.company}}</text>
              <text class="experience-time">{{item.time}}</text>
            </view>
            <view class="experience-position">{{item.position}}</view>
            <view class="experience-desc">{{item.description}}</view>
          </view>
        </view>
      </view>
      
      <!-- 教育背景 -->
      <view class="content-card education-card">
        <view class="section-title">教育背景</view>
        <view class="education-list">
          <view class="education-item">
              <view class="education-header">
              <text class="education-school">某大学</text>
              <text class="education-time">2018.09-2022.06</text>
              </view>
            <view class="education-major">专业: 行政管理</view>
            <view class="education-degree">学历: 本科</view>
            </view>
        </view>
      </view>
      
      <!-- 技能特长 -->
      <view class="content-card skills-card">
        <view class="section-title">技能特长</view>
        <view class="skills-content">
          <view class="skill-tag" v-for="(skill, index) in seekingData.skills" :key="index">{{skill}}</view>
        </view>
      </view>
      
      <!-- 自我评价 -->
      <view class="content-card self-evaluation-card">
        <view class="section-title">自我评价</view>
        <view class="evaluation-content">
          <text class="evaluation-text">{{seekingData.selfEvaluation}}</text>
        </view>
      </view>
      
      <!-- 联系方式 -->
      <view class="content-card contact-card">
        <view class="section-title">联系方式</view>
        <view class="contact-content">
          <view class="contact-item">
            <text class="contact-label">联系人</text>
            <text class="contact-value">{{seekingData.contact.name}}</text>
          </view>
          <view class="contact-item">
            <text class="contact-label">电话</text>
            <text class="contact-value contact-phone" @click="callPhone">{{seekingData.contact.phone}}</text>
          </view>
          <view class="contact-tips">
            <text class="tips-icon iconfont icon-info"></text>
            <text class="tips-text">请说明在"磁州生活网"看到的信息</text>
          </view>
        </view>
      </view>
      
      <!-- 举报卡片 -->
      <report-card></report-card>
      
      <!-- 相关求职推荐 -->
      <view class="content-card related-seekings-card">
        <!-- 标题栏 -->
        <view class="collapsible-header">
          <view class="section-title">相关求职推荐</view>
        </view>
        
        <!-- 内容区 -->
        <view class="collapsible-content">
          <!-- 简洁的求职列表 -->
          <view class="related-seekings-list">
            <view class="related-seeking-item" 
                 v-for="(seeking, index) in relatedSeekings.slice(0, 3)" 
                 :key="index" 
                 @click="navigateToSeekingDetail(seeking.id)">
              <view class="seeking-item-content">
                <view class="seeking-item-left">
                  <image class="seeker-avatar" :src="seeking.avatar" mode="aspectFill"></image>
                </view>
                <view class="seeking-item-middle">
                  <text class="seeking-item-title">{{seeking.title}}</text>
                  <view class="seeking-item-person">{{seeking.name}} | {{seeking.education}} | {{seeking.experience}}</view>
                  <view class="seeking-item-tags">
                    <text class="seeking-item-tag" v-for="(tag, tagIndex) in seeking.tags.slice(0, 2)" :key="tagIndex">{{tag}}</text>
                    <text class="seeking-item-tag-more" v-if="seeking.tags.length > 2">+{{seeking.tags.length - 2}}</text>
                  </view>
                </view>
                <view class="seeking-item-right">
                  <text class="seeking-item-salary">{{seeking.salary}}</text>
                </view>
              </view>
            </view>
            
            <!-- 暂无数据提示 -->
            <view class="empty-related-seekings" v-if="relatedSeekings.length === 0">
              <image src="/static/images/empty.png" class="empty-image" mode="aspectFit"></image>
              <text class="empty-text">暂无相关求职</text>
            </view>
          </view>
          
          <!-- 查看更多按钮 -->
          <view class="view-more-btn" v-if="relatedSeekings.length > 0" @click.stop="navigateToSeekingList">
            <text class="view-more-text">查看更多求职信息</text>
            <text class="view-more-icon iconfont icon-right"></text>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 底部操作栏 -->
    <view class="interaction-toolbar">
      <view class="toolbar-item" @click="goToHome">
        <image src="/static/images/tabbar/a首页.png" class="toolbar-icon"></image>
        <text class="toolbar-text">首页</text>
      </view>
      <view class="toolbar-item" @click="toggleCollect">
        <image src="/static/images/tabbar/a收藏.png" class="toolbar-icon"></image>
        <text class="toolbar-text">收藏</text>
      </view>
      <button class="share-button toolbar-item" open-type="share">
        <image src="/static/images/tabbar/a分享.png" class="toolbar-icon"></image>
        <text class="toolbar-text">分享</text>
      </button>
      <view class="toolbar-item" @click="openChat">
        <image src="/static/images/tabbar/a消息.png" class="toolbar-icon"></image>
        <text class="toolbar-text">私信</text>
      </view>
      <view class="toolbar-item call-button" @click="callPhone">
        <view class="call-button-content">
          <text class="call-text">打电话</text>
          <text class="call-subtitle">请说在磁州生活网看到的</text>
        </view>
      </view>
    </view>
    
    <!-- 悬浮海报按钮 -->
    <view class="float-poster-btn" @click="generateShareImage">
      <image src="/static/images/tabbar/海报.png" class="poster-icon"></image>
      <text class="poster-text">海报</text>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import ReportCard from '@/components/ReportCard.vue'

// 格式化时间
const formatTime = (timestamp) => {
  const date = new Date(timestamp);
  return `${date.getMonth() + 1}月${date.getDate()}日`;
};

// 定义解析教育背景函数
const parseEducation = (education) => {
  // 如果已经是对象或数组，直接返回
  if (typeof education !== 'string') {
    return education;
  }
  
  // 教育程度映射
  const educationMap = {
    '1': '小学',
    '2': '初中',
    '3': '高中',
    '4': '中专',
    '5': '大专',
    '6': '本科',
    '7': '硕士',
    '8': '博士'
  };
  
  // 如果是数字字符串，尝试从映射中获取
  if (!isNaN(education) && educationMap[education]) {
    return educationMap[education];
  }
  
  // 否则直接返回原字符串
  return education;
};

// 获取状态栏高度
const statusBarHeight = ref(20); // 默认值

// 返回上一页
const goBack = () => {
  uni.navigateBack();
};

// 响应式数据
const isCollected = ref(false);
const seekingData = ref({
  id: 'seeking12345',
  title: '求职行政文员',
  salary: '3000-4000元/月',
  tags: ['行政文员', '应届生', '可立即上岗'],
  publishTime: Date.now() - 86400000, // 1天前
  name: '张女士',
  age: 24,
  education: '本科',
  experience: '1年',
  intentions: [
    { label: '期望职位', value: '行政文员' },
    { label: '期望薪资', value: '3000-4000元/月' },
    { label: '期望城市', value: '磁县' },
    { label: '期望行业', value: '不限' }
  ],
  workExperience: [
    {
      company: '某科技有限公司',
      time: '2022.07-2023.06',
      position: '行政助理',
      description: '负责公司日常行政事务，包括文件整理、会议安排、接待来访等工作。'
    }
  ],
  educationInfo: [
    {
      school: '某大学',
      time: '2018.09-2022.06',
      major: '行政管理',
      degree: '本科'
    }
  ],
  skills: ['办公软件', '文档处理', '会议组织', '沟通协调'],
  selfEvaluation: '性格开朗，工作认真负责，有较强的沟通能力和团队协作精神。熟悉办公软件操作，能够独立完成日常行政工作。',
  contact: {
    name: '张女士',
    phone: '13912345678'
  }
});

// 相关求职列表功能
const relatedSeekings = ref([]);

// 加载相关求职信息
const loadRelatedSeekings = () => {
  // 这里可以调用API获取数据
  // 实际项目中应该根据当前求职信息的分类、标签等进行相关性匹配
  
  // 模拟数据
  setTimeout(() => {
    relatedSeekings.value = [
      {
        id: 'seeking001',
        title: '求职销售代表',
        salary: '4000-6000元/月',
        name: '李先生',
        avatar: '/static/images/avatar/男1.png',
        education: '大专',
        experience: '3年',
        tags: ['销售经验', '有车', '形象好']
      },
      {
        id: 'seeking002',
        title: '求职行政助理',
        salary: '3500-4500元/月',
        name: '王女士',
        avatar: '/static/images/avatar/女1.png',
        education: '本科',
        experience: '应届生',
        tags: ['办公软件熟练', '英语四级', '形象气质佳']
      },
      {
        id: 'seeking003',
        title: '求职前台文员',
        salary: '3000-4000元/月',
        name: '赵女士',
        avatar: '/static/images/avatar/女2.png',
        education: '大专',
        experience: '1年',
        tags: ['有相关经验', '普通话标准', '较强沟通能力']
      }
    ];
  }, 500);
};

// 跳转到求职详情页
const navigateToSeekingDetail = (id) => {
  // 避免重复跳转当前页面
  const pages = getCurrentPages();
  const currentPage = pages[pages.length - 1];
  const options = currentPage.options || {};
  
  if (id === options.id) {
    return;
  }
  
  uni.navigateTo({
    url: `/pages/publish/job-seeking-detail?id=${id}`
  });
};

// 跳转到求职列表页
const navigateToSeekingList = (e) => {
  if (e) e.stopPropagation();
  const seekingCategory = seekingData.value.tags?.[0] || '';
  uni.navigateTo({
    url: `/subPackages/service/pages/filter?type=job-seeking&title=${encodeURIComponent('求职信息')}&category=${encodeURIComponent(seekingCategory)}&active=resume`
  });
};

// 方法
const toggleCollect = () => {
  isCollected.value = !isCollected.value;
  if (isCollected.value) {
    uni.showToast({
      title: '收藏成功',
      icon: 'success'
    });
  }
};

const showShareOptions = () => {
  uni.showShareMenu({
    withShareTicket: true,
    menus: ['shareAppMessage', 'shareTimeline']
  });
};

const callPhone = () => {
  uni.makePhoneCall({
    phoneNumber: seekingData.value.contact.phone,
    fail: () => {
      uni.showToast({
        title: '拨打电话失败',
        icon: 'none'
      });
    }
  });
};

// 跳转到首页
const goToHome = () => {
  uni.switchTab({
    url: '/pages/index/index'
  });
};

// 打开私信聊天
const openChat = () => {
  if (!seekingData.value.contact || !seekingData.value.contact.id) {
    uni.showToast({
      title: '无法获取求职者信息',
      icon: 'none'
    });
    return;
  }
  
  // 跳转到聊天页面
  uni.navigateTo({
    url: `/pages/chat/index?userId=${seekingData.value.contact.id}&username=${encodeURIComponent(seekingData.value.contact.name || '求职者')}`
  });
};

// 生命周期钩子
onMounted(() => {
  // 修改页面标题
  uni.setNavigationBarTitle({
    title: '求职详情'
  });
  
  // 获取状态栏高度
  statusBarHeight.value = uni.getSystemInfoSync().statusBarHeight || 20;
  
  // 获取路由参数，并请求数据
  const pages = getCurrentPages();
  const currentPage = pages[pages.length - 1];
  const options = currentPage.options || {};
  
  console.log('求职详情页参数:', options);
  
  // 如果有ID参数，则根据ID获取详情
  if (options.id) {
    // 尝试从缓存中获取数据
    const cacheKey = `job_seeking_${options.id}`;
    const cachedData = uni.getStorageSync(cacheKey);
    
    if (cachedData) {
      console.log('从缓存获取求职数据:', cachedData);
      try {
        // 处理缓存的数据
        const parsedData = typeof cachedData === 'string' ? JSON.parse(cachedData) : cachedData;
        
        // 更新到响应式数据中
        seekingData.value = {
          ...seekingData.value,
          ...parsedData
        };
        
        // 确保education字段正确解析
        if (typeof seekingData.value.education === 'string') {
          seekingData.value.education = parseEducation(seekingData.value.education);
        }
        
        console.log('处理后的求职数据:', seekingData.value);
      } catch (e) {
        console.error('解析缓存数据失败:', e);
      }
    } else {
      // 从服务器获取数据
      console.log('从服务器获取求职详情:', options.id);
      // 这里是模拟数据，实际项目中应该调用API获取
      setTimeout(() => {
        // 假设这是从服务器获取的数据
        const serverData = {
          // 服务端返回的数据...
        };
        
        // 更新到响应式数据中
        seekingData.value = {
          ...seekingData.value,
          ...serverData
        };
        
        // 确保education字段正确解析
        if (typeof seekingData.value.education === 'string') {
          seekingData.value.education = parseEducation(seekingData.value.education);
        }
        
        // 保存到缓存
        uni.setStorageSync(cacheKey, JSON.stringify(seekingData.value));
      }, 100);
    }
  }
  
  // 触发自动分享按钮 - 在小程序环境中使用小程序API
  setTimeout(() => {
    // 小程序环境不支持document.getElementById，注释掉这部分代码
    // 或者使用uniapp提供的选择器API
    try {
      const query = uni.createSelectorQuery();
      query.select('#shareButton').boundingClientRect(data => {
        if (data) {
          console.log('获取到分享按钮元素');
          // 在小程序中可以使用其他方式触发分享
        }
      }).exec();
    } catch (err) {
      console.error('获取分享按钮失败:', err);
    }
  }, 500);
  
  // 加载相关求职推荐
  loadRelatedSeekings();
});

// 提供页面分享信息
const onShareAppMessage = () => {
  return {
    title: seekingData.value.title,
    path: `/pages/publish/job-seeking-detail?id=${seekingData.value.id}`
  };
};

defineExpose({
  onShareAppMessage
});

// 海报相关数据
const posterImagePath = ref('');
const showPosterFlag = ref(false);

// 生成海报的方法
const generateShareImage = () => {
  uni.showLoading({
    title: '生成中...'
  });
  
  // 创建海报数据对象
  const posterData = {
    title: seekingData.value.title,
    salary: seekingData.value.salary,
    name: seekingData.value.name,
    gender: '女',
    age: seekingData.value.age,
    education: seekingData.value.education,
    experience: seekingData.value.experience,
    phone: seekingData.value.contact.phone,
    skills: seekingData.value.skills ? seekingData.value.skills.substring(0, 60) + '...' : '',
    qrcode: '/static/images/tabbar/客服微信.png',
    logo: '/static/images/tabbar/求职.png',
    bgImage: '/static/images/banner/banner-1.png'
  };
  
  // #ifdef H5
  // H5环境不支持canvas绘制图片保存，提示用户
  setTimeout(() => {
    uni.hideLoading();
    uni.showModal({
      title: '提示',
      content: 'H5环境暂不支持保存海报，请使用App或小程序',
      showCancel: false
    });
  }, 1000);
  return;
  // #endif
  
  // 绘制海报
  const ctx = uni.createCanvasContext('posterCanvas');
  
  // 绘制背景
  ctx.save();
  ctx.drawImage(posterData.bgImage, 0, 0, 600, 400);
  // 添加半透明蒙层
  ctx.setFillStyle('rgba(0, 0, 0, 0.35)');
  ctx.fillRect(0, 0, 600, 900);
  ctx.restore();
  
  // 绘制白色卡片背景
  ctx.save();
  ctx.setFillStyle('#ffffff');
  ctx.fillRect(30, 280, 540, 550);
  ctx.restore();
  
  // 绘制Logo
  ctx.save();
  ctx.beginPath();
  ctx.arc(300, 200, 80, 0, 2 * Math.PI);
  ctx.setFillStyle('#ffffff');
  ctx.fill();
  // 在圆形内绘制Logo
  ctx.clip();
  ctx.drawImage(posterData.logo, 220, 120, 160, 160);
  ctx.restore();
  
  // 绘制期望岗位
  ctx.setFillStyle('#333333');
  ctx.setFontSize(32);
  ctx.setTextAlign('center');
  ctx.fillText(posterData.title, 300, 350);
  
  // 绘制期望薪资
  ctx.setFillStyle('#FF6B6B');
  ctx.setFontSize(28);
  ctx.fillText(posterData.salary, 300, 400);
  
  // 分割线
  ctx.beginPath();
  ctx.setStrokeStyle('#eeeeee');
  ctx.setLineWidth(2);
  ctx.moveTo(100, 430);
  ctx.lineTo(500, 430);
  ctx.stroke();
  
  // 绘制个人信息
  ctx.setFillStyle('#666666');
  ctx.setFontSize(24);
  ctx.setTextAlign('left');
  
  const personInfo = `${posterData.name} | ${posterData.gender} | ${posterData.age}岁`;
  ctx.fillText(personInfo, 80, 480);
  
  // 绘制学历和工作经验
  const eduExp = `${posterData.education} | ${posterData.experience}`;
  ctx.fillText(eduExp, 80, 520);
  
  // A wrap text function
  const wrapText = (ctx, text, x, y, maxWidth, lineHeight) => {
    if (text.length === 0) return;
    
    const words = text.split('');
    let line = '';
    let testLine = '';
    let lineCount = 0;
    
    for (let n = 0; n < words.length; n++) {
      testLine += words[n];
      const metrics = ctx.measureText(testLine);
      const testWidth = metrics.width;
      
      if (testWidth > maxWidth && n > 0) {
        ctx.fillText(line, x, y + (lineCount * lineHeight));
        line = words[n];
        testLine = words[n];
        lineCount++;
        
        if (lineCount >= 3) {
          line += '...';
          ctx.fillText(line, x, y + (lineCount * lineHeight));
          break;
        }
      } else {
        line = testLine;
      }
    }
    
    if (lineCount < 3) {
      ctx.fillText(line, x, y + (lineCount * lineHeight));
    }
  };
  
  // 绘制技能特长
  ctx.setFillStyle('#666666');
  ctx.fillText('技能特长:', 80, 560);
  wrapText(ctx, posterData.skills, 80, 600, 440, 35);
  
  // 绘制电话
  if (posterData.phone) {
    ctx.fillText('联系电话: ' + posterData.phone, 80, 680);
  }
  
  // 绘制小程序码
  ctx.drawImage(posterData.qrcode, 225, 720, 150, 150);
  
  // 提示文字
  ctx.setFillStyle('#999999');
  ctx.setFontSize(20);
  ctx.setTextAlign('center');
  ctx.fillText('长按识别二维码查看详情', 300, 880);
  
  // 应用平台Logo
  ctx.setFillStyle('#333333');
  ctx.setFontSize(24);
  ctx.fillText('磁县同城 - 求职信息', 300, 840);
  
  // 绘制完成，输出图片
  ctx.draw(false, () => {
    setTimeout(() => {
      // 延迟确保canvas已完成渲染
      uni.canvasToTempFilePath({
        canvasId: 'posterCanvas',
        success: (res) => {
          uni.hideLoading();
          showPosterModal(res.tempFilePath);
        },
        fail: (err) => {
          console.error('生成海报失败', err);
          uni.hideLoading();
          uni.showToast({
            title: '生成海报失败',
            icon: 'none'
          });
        }
      });
    }, 800);
  });
};

// 显示海报预览和保存选项
const showPosterModal = (posterPath) => {
  posterImagePath.value = posterPath;
  showPosterFlag.value = true;
  
  uni.showModal({
    title: '海报已生成',
    content: '海报已生成，是否保存到相册？',
    confirmText: '保存',
    success: (res) => {
      if (res.confirm) {
        savePosterToAlbum(posterPath);
      } else {
        // 预览图片
        uni.previewImage({
          urls: [posterPath],
          current: posterPath
        });
      }
    }
  });
};

// 保存海报到相册
const savePosterToAlbum = (posterPath) => {
  uni.showLoading({
    title: '正在保存...'
  });
  
  uni.saveImageToPhotosAlbum({
    filePath: posterPath,
    success: () => {
      uni.hideLoading();
      uni.showToast({
        title: '已保存到相册',
        icon: 'success'
      });
    },
    fail: (err) => {
      uni.hideLoading();
      console.error('保存失败', err);
      
      if (err.errMsg.indexOf('auth deny') > -1) {
        uni.showModal({
          title: '提示',
          content: '保存失败，请授权相册权限后重试',
          confirmText: '去设置',
          success: (res) => {
            if (res.confirm) {
              uni.openSetting();
            }
          }
        });
      } else {
        uni.showToast({
          title: '保存失败',
          icon: 'none'
        });
      }
    }
  });
};

// 举报相关
const showReportOptions = () => {
  uni.showActionSheet({
    itemList: ['虚假信息', '违法内容', '色情内容', '侵权投诉', '诱导欺骗', '其他问题'],
    success: (res) => {
      const reportReasonIndex = res.tapIndex;
      const reasons = ['虚假信息', '违法内容', '色情内容', '侵权投诉', '诱导欺骗', '其他问题'];
      showReportInputDialog(reasons[reportReasonIndex]);
    }
  });
};

const showReportInputDialog = (reason) => {
  // 检查是否登录
  const hasLogin = uni.getStorageSync('token') || false;
  if (!hasLogin) {
    uni.showModal({
      title: '提示',
      content: '请先登录后再进行举报',
      confirmText: '去登录',
      success: (res) => {
        if (res.confirm) {
          uni.navigateTo({
            url: '/pages/login/login'
          });
        }
      }
    });
    return;
  }
  
  uni.showModal({
    title: '举报内容',
    content: `您选择的举报原因是: ${reason}，请确认是否提交举报？`,
    confirmText: '确认举报',
    success: (res) => {
      if (res.confirm) {
        submitReport(reason, '');
      }
    }
  });
};

const submitReport = (reason, content) => {
  uni.showLoading({
    title: '提交中...'
  });
  
  // 这里模拟提交举报信息到服务器
  setTimeout(() => {
    uni.hideLoading();
    
    uni.showToast({
      title: '举报成功',
      icon: 'success'
    });
    
    // 实际开发中，这里应该调用API提交举报信息
    // const reportData = {
    //   type: 'jobSeeking',
    //   id: seekingData.value.id,
    //   reason: reason,
    //   content: content
    // };
    // submitReportAPI(reportData).then(() => {
    //   uni.showToast({
    //     title: '举报成功',
    //     icon: 'success'
    //   });
    // });
  }, 1500);
};
</script>

<style>
.job-seeking-container {
  min-height: 100vh;
  background-color: #f5f7fa;
  padding-bottom: 110rpx;
}

.job-seeking-wrapper {
  padding: 150rpx 20rpx 120rpx; /* 增加顶部内边距，为固定导航栏留出空间 */
}

/* 自定义导航栏样式 */
.custom-navbar {
  background: linear-gradient(135deg, #0066FF, #0052CC);
  height: 88rpx;
  padding-top: 44px; /* 状态栏高度 */
  display: flex;
  align-items: center;
  position: fixed; /* 改为固定定位 */
  top: 0;
  left: 0;
  right: 0;
  padding-bottom: 10rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 82, 204, 0.2);
  z-index: 100; /* 提高z-index确保在最上层 */
}

.navbar-left {
  width: 60rpx;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: transparent;
  border-radius: 50%;
}

.navbar-title {
  flex: 1;
  text-align: center;
  font-size: 16px;
  font-weight: 500;
  color: #FFFFFF; /* 修改为白色字体 */
}

.navbar-right {
  display: flex;
  align-items: center;
  padding-right: 20rpx;
}

/* 悬浮举报按钮样式已删除 */
/*
.report-btn {
  background-color: rgba(255, 255, 255, 0.8);
  border-radius: 30rpx;
  padding: 6rpx 16rpx;
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.1);
}

.report-text {
  font-size: 24rpx;
  color: #FF5151;
}
*/

.content-card {
  background-color: #fff;
  border-radius: 16rpx;
  margin-bottom: 24rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  margin-top: 20rpx; /* 添加与标题栏的间隙 */
}

/* 求职基本信息卡片 */
.seeking-title-row {
  display: none;
}

.seeking-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 16rpx;
}

.seeking-salary {
  font-size: 36rpx;
  font-weight: 600;
  color: #ff4d4f;
  margin-bottom: 16rpx;
}

.seeking-meta {
  margin-bottom: 24rpx;
}

.seeking-tag-group {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 12rpx;
}

.seeking-tag {
  font-size: 24rpx;
  color: #1890ff;
  background-color: rgba(24, 144, 255, 0.1);
  padding: 4rpx 16rpx;
  border-radius: 6rpx;
  margin-right: 16rpx;
  margin-bottom: 12rpx;
}

.seeking-publish-time {
  font-size: 24rpx;
  color: #999;
}

/* 基本信息 */
.seeking-basic-info {
  display: flex;
  flex-wrap: wrap;
  padding: 24rpx;
  background-color: #f9fafc;
  border-radius: 12rpx;
}

.info-item {
  width: 50%;
  padding: 12rpx 24rpx;
  box-sizing: border-box;
}

.info-label {
  font-size: 26rpx;
  color: #999;
  margin-bottom: 8rpx;
  display: block;
}

.info-value {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

/* 求职意向 */
.intention-list {
  display: flex;
  flex-direction: column;
}

.intention-item {
  display: flex;
  padding: 16rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.intention-item:last-child {
  border-bottom: none;
}

.intention-label {
  width: 160rpx;
  font-size: 28rpx;
  color: #999;
}

.intention-value {
  flex: 1;
  font-size: 28rpx;
  color: #333;
}

/* 工作经验 */
.experience-list {
  display: flex;
  flex-direction: column;
}

.experience-item {
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.experience-item:last-child {
  border-bottom: none;
}

.experience-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8rpx;
}

.experience-company {
  font-size: 30rpx;
  font-weight: 500;
  color: #333;
}

.experience-time {
  font-size: 24rpx;
  color: #999;
}

.experience-position {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 8rpx;
}

.experience-desc {
  font-size: 26rpx;
  color: #666;
  line-height: 1.6;
}

/* 教育背景 */
.education-list {
  display: flex;
  flex-direction: column;
}

.education-item {
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.education-item:last-child {
  border-bottom: none;
}

.education-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8rpx;
}

.education-school {
  font-size: 30rpx;
  font-weight: 500;
  color: #333;
}

.education-time {
  font-size: 24rpx;
  color: #999;
}

.education-major {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 8rpx;
}

.education-degree {
  font-size: 26rpx;
  color: #666;
}

/* 技能特长 */
.skills-content {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -8rpx;
}

.skill-tag {
  font-size: 26rpx;
  color: #1890ff;
  background-color: rgba(24, 144, 255, 0.1);
  padding: 8rpx 20rpx;
  border-radius: 6rpx;
  margin: 8rpx;
}

/* 自我评价 */
.evaluation-content {
  padding: 24rpx;
  background-color: #f9fafc;
  border-radius: 12rpx;
}

.evaluation-text {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
}

/* 相似求职 */
.similar-list {
  display: flex;
  flex-direction: column;
}

.similar-item {
  padding: 20rpx;
  background-color: #f9fafc;
  border-radius: 12rpx;
  margin-bottom: 16rpx;
}

.similar-seeking-info {
  display: flex;
  flex-direction: column;
}

.similar-seeking-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 8rpx;
}

.similar-seeking-salary {
  font-size: 28rpx;
  color: #ff4d4f;
  margin-bottom: 8rpx;
}

.similar-seeking-meta {
  font-size: 24rpx;
  color: #999;
}

/* 无数据提示 */
.no-data-tip {
  font-size: 28rpx;
  color: #999;
  text-align: center;
  padding: 30rpx 0;
}

/* 底部互动工具栏 */
.interaction-toolbar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  padding: 10rpx 10rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-top: 1rpx solid #f0f0f0;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  height: 120rpx;
  z-index: 100;
}

.toolbar-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 6rpx 0;
  margin: 0 4rpx;
}

.toolbar-icon {
  width: 44rpx;
  height: 44rpx;
  margin-bottom: 6rpx;
}

.toolbar-text {
  font-size: 22rpx;
  color: #666;
}

.share-button {
  background: transparent;
  border: none;
  margin: 0;
  padding: 0;
  line-height: normal;
  border-radius: 0;
  flex: 1;
}

.share-button::after {
  display: none;
}

.call-button {
  flex: 3; /* 占据更多空间，原来是2，现在改为3让按钮更长 */
  background: linear-gradient(135deg, #0052CC, #0066FF);
  height: 90rpx;
  margin: 0 0 0 10rpx;
  border-radius: 45rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 82, 204, 0.2);
}

.call-button-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
}

.call-text {
  color: #fff;
  font-size: 30rpx;
  font-weight: bold;
  line-height: 1.2;
}

.call-subtitle {
  color: rgba(255, 255, 255, 0.9);
  font-size: 20rpx;
  line-height: 1.2;
}

/* 隐藏原来的底部操作栏 */
.action-bar {
  display: none;
}

/* 隐藏的分享按钮 */
.hidden-share-btn {
  position: absolute;
  top: -9999rpx;
  left: -9999rpx;
  width: 0;
  height: 0;
  padding: 0;
  margin: 0;
  opacity: 0;
}

.back-icon {
  width: 36rpx;
  height: 36rpx;
  display: block;
}

/* 悬浮海报按钮 */
.float-poster-btn {
  position: fixed;
  right: 30rpx;
  bottom: 200rpx;
  width: 100rpx;
  height: 100rpx;
  background: rgba(240, 240, 240, 0.9);
  border-radius: 50%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  box-shadow: 0 6rpx 20rpx rgba(0,0,0,0.08);
  z-index: 90;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1rpx solid rgba(230, 230, 230, 0.6);
  transition: all 0.2s ease;
}

.float-poster-btn:active {
  transform: scale(0.95);
  box-shadow: 0 3rpx 10rpx rgba(0,0,0,0.08);
}

.poster-icon {
  width: 40rpx;
  height: 40rpx;
  margin-bottom: 4rpx;
}

.poster-text {
  font-size: 20rpx;
  color: #444;
  line-height: 1;
}

/* 区块标题样式优化 - 添加蓝色竖线 */
.section-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 20rpx;
  position: relative;
  padding-left: 16rpx;
}

.section-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 6rpx;
  width: 6rpx;
  height: 28rpx;
  background-color: #1890ff;
  border-radius: 3rpx;
}

/* 联系方式样式 - 电话显示为绿色，提示为黄色 */
.contact-phone {
  color: #52c41a; /* 绿色 */
  font-weight: 500;
}

.contact-tips {
  display: flex;
  align-items: center;
  margin-top: 16rpx;
  padding: 10rpx 16rpx;
  background-color: rgba(255, 152, 0, 0.1);
  border-radius: 8rpx;
}

.tips-icon {
  font-size: 24rpx;
  color: #ff9800; /* 黄色 */
  margin-right: 8rpx;
}

.tips-text {
  font-size: 24rpx;
  color: #ff9800; /* 黄色 */
}

/* 相关求职推荐样式 */
.related-seekings-card {
  margin-top: 12px;
  background-color: #fff;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

/* 可折叠标题栏样式 */
.collapsible-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  background-color: #fff;
  position: relative;
  cursor: pointer;
}

/* 相关求职列表样式 */
.related-seekings-list {
  margin-bottom: 12px;
}

.related-seeking-item {
  padding: 12px 0;
  border-bottom: 1px solid #f5f5f5;
}

.related-seeking-item:last-child {
  border-bottom: none;
}

.seeking-item-content {
  display: flex;
  align-items: center;
}

.seeking-item-left {
  margin-right: 12px;
}

.seeker-avatar {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  background-color: #f5f7fa;
}

.seeking-item-middle {
  flex: 1;
  overflow: hidden;
}

.seeking-item-title {
  font-size: 15px;
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.seeking-item-person {
  font-size: 13px;
  color: #666;
  margin-bottom: 6px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.seeking-item-tags {
  display: flex;
  flex-wrap: wrap;
}

.seeking-item-tag {
  font-size: 11px;
  color: #1890ff;
  background-color: rgba(24, 144, 255, 0.1);
  padding: 2px 6px;
  border-radius: 4px;
  margin-right: 6px;
}

.seeking-item-tag-more {
  font-size: 11px;
  color: #999;
}

.seeking-item-right {
  min-width: 80px;
  text-align: right;
}

.seeking-item-salary {
  font-size: 15px;
  font-weight: 500;
  color: #ff5252;
}

/* 查看更多按钮样式 */
.view-more-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 44px;
  background-color: #f7f9fc;
  border-radius: 8px;
  margin-top: 8px;
}

.view-more-text {
  font-size: 14px;
  color: #1890ff;
}

.view-more-icon {
  margin-left: 4px;
  font-size: 12px;
  color: #1890ff;
}

/* 折叠内容区样式 */
.collapsible-content {
  padding: 0 16px 16px;
  overflow: hidden;
}

/* 空数据提示样式 */
.empty-related-seekings {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 24px 0;
}

.empty-image {
  width: 80px;
  height: 80px;
  margin-bottom: 12px;
}

.empty-text {
  font-size: 14px;
  color: #999;
}
</style> 