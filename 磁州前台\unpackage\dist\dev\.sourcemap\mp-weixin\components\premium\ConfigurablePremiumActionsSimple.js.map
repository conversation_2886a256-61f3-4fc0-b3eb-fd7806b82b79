{"version": 3, "file": "ConfigurablePremiumActionsSimple.js", "sources": ["components/premium/ConfigurablePremiumActionsSimple.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniComponent:/QzovVXNlcnMvQWRtaW5pc3RyYXRvci9EZXNrdG9wL-aWsOW7uuaWh-S7tuWkuS_no4Hlt57liY3lj7AvY29tcG9uZW50cy9wcmVtaXVtL0NvbmZpZ3VyYWJsZVByZW1pdW1BY3Rpb25zU2ltcGxlLnZ1ZQ"], "sourcesContent": ["<template>\n  <view class=\"premium-actions-container\">\n    <!-- 直接显示模式 -->\n    <view class=\"direct-options\" v-if=\"showMode === 'direct'\">\n      <!-- 发布页面显示的选项 -->\n      <template v-if=\"pageType === 'publish'\">\n        <!-- 广告发布选项 -->\n        <view class=\"premium-option-card ad-option\" @click=\"selectDirectOption('publish', 'ad')\">\n          <view class=\"option-inner\">\n            <view class=\"option-left\">\n              <view class=\"option-icon-container\">\n                <image class=\"option-icon\" src=\"/static/images/premium/ad-publish.png\" mode=\"aspectFit\"></image>\n              </view>\n              <view class=\"option-content\">\n                <text class=\"option-title\">看广告发布</text>\n                <text class=\"option-desc\">看一个广告免费发布一天</text>\n              </view>\n            </view>\n            <view class=\"option-right\">\n              <view class=\"option-tag free-tag\">\n                <text>免费</text>\n              </view>\n            </view>\n          </view>\n        </view>\n        \n        <!-- 付费发布选项 -->\n        <view class=\"premium-option-card paid-option\" @click=\"selectDirectOption('publish', 'paid')\">\n          <view class=\"option-inner\">\n            <view class=\"option-left\">\n              <view class=\"option-icon-container paid-icon\">\n                <image class=\"option-icon\" src=\"/static/images/premium/paid-publish.png\" mode=\"aspectFit\"></image>\n              </view>\n              <view class=\"option-content\">\n                <text class=\"option-title\">付费发布</text>\n                <text class=\"option-desc\">3天/1周/1个月任选</text>\n              </view>\n            </view>\n            <view class=\"option-right\">\n              <view class=\"option-tag paid-tag\">\n                <text>付费</text>\n              </view>\n            </view>\n          </view>\n        </view>\n      </template>\n      \n      <!-- 置顶页面显示的选项 -->\n      <template v-else-if=\"pageType === 'top' || pageType === 'merchant_top' || pageType.endsWith('_top')\">\n        <!-- 广告置顶选项 -->\n        <view class=\"premium-option-card ad-option\" @click=\"selectDirectOption('top', 'ad')\">\n          <view class=\"option-inner\">\n            <view class=\"option-left\">\n              <view class=\"option-icon-container\">\n                <image class=\"option-icon\" src=\"/static/images/premium/ad-top.png\" mode=\"aspectFit\"></image>\n              </view>\n              <view class=\"option-content\">\n                <text class=\"option-title bold-title\">看广告置顶</text>\n                <text class=\"option-desc\" v-if=\"pageType.startsWith('merchant')\">免费置顶店铺2小时，排名次于付费</text>\n                <text class=\"option-desc\" v-else>免费置顶2小时，排名次于付费</text>\n              </view>\n            </view>\n            <view class=\"option-right\">\n              <view class=\"option-tag free-tag\">\n                <text>免费</text>\n              </view>\n            </view>\n          </view>\n        </view>\n        \n        <!-- 付费置顶选项 -->\n        <view class=\"premium-option-card paid-option\" @click=\"selectDirectOption('top', 'paid')\">\n          <view class=\"option-inner\">\n            <view class=\"option-left\">\n              <view class=\"option-icon-container paid-icon\">\n                <image class=\"option-icon\" src=\"/static/images/premium/paid-top.png\" mode=\"aspectFit\"></image>\n              </view>\n              <view class=\"option-content\">\n                <text class=\"option-title bold-title\">付费置顶</text>\n                <text class=\"option-desc\" v-if=\"pageType.startsWith('merchant')\">获得店铺优先展示位置，排名最靠前</text>\n                <text class=\"option-desc\" v-else>获得优先展示位置，排名最靠前</text>\n              </view>\n            </view>\n            <view class=\"option-right\">\n              <view class=\"option-tag paid-tag\">\n                <text>付费</text>\n              </view>\n            </view>\n          </view>\n        </view>\n      </template>\n      \n      <!-- 刷新页面显示的选项 -->\n      <template v-else-if=\"pageType === 'refresh' || pageType === 'merchant_refresh' || pageType.endsWith('_refresh')\">\n        <!-- 广告刷新选项 -->\n        <view class=\"premium-option-card ad-option\" @click=\"selectDirectOption('refresh', 'ad')\">\n          <view class=\"option-inner\">\n            <view class=\"option-left\">\n              <view class=\"option-icon-container\">\n                <image class=\"option-icon\" src=\"/static/images/premium/ad-refresh.png\" mode=\"aspectFit\"></image>\n              </view>\n              <view class=\"option-content\">\n                <text class=\"option-title\">看广告刷新</text>\n                <text class=\"option-desc\" v-if=\"pageType.startsWith('merchant')\">刷新店铺信息，提升排名到最新位置</text>\n                <text class=\"option-desc\" v-else>刷新信息排名到最新位置</text>\n              </view>\n            </view>\n            <view class=\"option-right\">\n              <view class=\"option-tag free-tag\">\n                <text>免费</text>\n              </view>\n            </view>\n          </view>\n        </view>\n        \n        <!-- 付费刷新选项 -->\n        <view class=\"premium-option-card paid-option\" @click=\"selectDirectOption('refresh', 'paid')\">\n          <view class=\"option-inner\">\n            <view class=\"option-left\">\n              <view class=\"option-icon-container paid-icon\">\n                <image class=\"option-icon\" src=\"/static/images/premium/paid-refresh.png\" mode=\"aspectFit\"></image>\n              </view>\n              <view class=\"option-content\">\n                <text class=\"option-title\">付费刷新</text>\n                <text class=\"option-desc\" v-if=\"pageType.startsWith('merchant')\">付费刷新店铺信息，提升排名</text>\n                <text class=\"option-desc\" v-else>付费刷新信息排名</text>\n              </view>\n            </view>\n            <view class=\"option-right\">\n              <view class=\"option-tag paid-tag\">\n                <text>付费</text>\n              </view>\n            </view>\n          </view>\n        </view>\n      </template>\n    </view>\n  </view>\n</template>\n\n<script setup>\nimport { ref, computed } from 'vue';\n\n// 组件属性\nconst props = defineProps({\n  pageType: {\n    type: String,\n    default: ''\n  },\n  showMode: {\n    type: String,\n    default: 'direct'\n  },\n  itemData: {\n    type: Object,\n    default: () => ({})\n  }\n});\n\n// 组件事件\nconst emit = defineEmits(['action-completed', 'action-cancelled']);\n\n// 直接选择选项\nconst selectDirectOption = (action, optionType) => {\n  console.log('选择操作:', action, optionType);\n  \n  // 创建选项对象\n  const option = {\n    title: `${optionType === 'ad' ? '看广告' : '付费'}${action === 'publish' ? '发布' : action === 'top' ? '置顶' : '刷新'}`,\n    type: optionType,\n    action: action\n  };\n  \n  // 模拟处理\n  setTimeout(() => {\n    emit('action-completed', {\n      action: action,\n      type: optionType,\n      data: option\n    });\n  }, 1000);\n};\n</script>\n\n<style lang=\"scss\" scoped>\n.premium-actions-container {\n  width: 100%;\n  margin: 20rpx 0;\n}\n\n.direct-options {\n  padding: 0;\n}\n\n.premium-option-card {\n  margin-bottom: 16px;\n  border-radius: 12px;\n  overflow: hidden;\n  position: relative;\n  background: white;\n  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);\n  transition: all 0.3s ease;\n}\n\n.premium-option-card:active {\n  transform: scale(0.98);\n}\n\n.option-inner {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  padding: 20rpx;\n}\n\n.option-left {\n  display: flex;\n  align-items: center;\n  flex: 1;\n}\n\n.option-icon-container {\n  width: 48rpx;\n  height: 48rpx;\n  border-radius: 12rpx;\n  background: linear-gradient(135deg, #4facfe, #00f2fe);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-right: 20rpx;\n}\n\n.option-icon-container.paid-icon {\n  background: linear-gradient(135deg, #ff9a9e, #fad0c4);\n}\n\n.option-icon {\n  width: 24rpx;\n  height: 24rpx;\n}\n\n.option-content {\n  flex: 1;\n}\n\n.option-title {\n  font-size: 32rpx;\n  font-weight: 600;\n  color: #333;\n  display: block;\n  margin-bottom: 8rpx;\n}\n\n.option-desc {\n  font-size: 26rpx;\n  color: #666;\n  display: block;\n}\n\n.option-right {\n  display: flex;\n  align-items: center;\n}\n\n.option-tag {\n  padding: 8rpx 16rpx;\n  border-radius: 20rpx;\n  font-size: 24rpx;\n  font-weight: 600;\n  text-align: center;\n}\n\n.free-tag {\n  background: linear-gradient(135deg, #4A90E2, #3B7DFC);\n  color: white;\n}\n\n.paid-tag {\n  background: linear-gradient(135deg, #FF6B6B, #FF8E53);\n  color: white;\n}\n</style>\n", "import Component from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/components/premium/ConfigurablePremiumActionsSimple.vue'\nwx.createComponent(Component)"], "names": ["uni"], "mappings": ";;;;;;;;;;;;;;;;;;;;;AAgKA,UAAM,OAAO;AAGb,UAAM,qBAAqB,CAAC,QAAQ,eAAe;AACjDA,oBAAA,MAAA,MAAA,OAAA,kEAAY,SAAS,QAAQ,UAAU;AAGvC,YAAM,SAAS;AAAA,QACb,OAAO,GAAG,eAAe,OAAO,QAAQ,IAAI,GAAG,WAAW,YAAY,OAAO,WAAW,QAAQ,OAAO,IAAI;AAAA,QAC3G,MAAM;AAAA,QACN;AAAA,MACJ;AAGE,iBAAW,MAAM;AACf,aAAK,oBAAoB;AAAA,UACvB;AAAA,UACA,MAAM;AAAA,UACN,MAAM;AAAA,QACZ,CAAK;AAAA,MACF,GAAE,GAAI;AAAA,IACT;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACpLA,GAAG,gBAAgB,SAAS;"}