{"version": 3, "file": "index.js", "sources": ["carpool-package/pages/carpool/detail/index.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/Y2FycG9vbC1wYWNrYWdlXHBhZ2VzXGNhcnBvb2xcZGV0YWlsXGluZGV4LnZ1ZQ"], "sourcesContent": ["<template>\n  <view class=\"detail-container\">\n    <!-- 自定义导航栏 -->\n    <view class=\"custom-header\" :style=\"{ paddingTop: statusBarHeight + 'px' }\">\n      <view class=\"header-content\">\n        <view class=\"left-action\" @click=\"goBack\">\n          <image src=\"/static/images/tabbar/最新返回键.png\" class=\"action-icon back-icon\"></image>\n        </view>\n        <view class=\"title-area\">\n          <text class=\"page-title\">拼车详情</text>\n        </view>\n        <view class=\"right-action\" @click=\"showActionSheet\">\n          <image src=\"/static/images/tabbar/more-vertical.png\" class=\"action-icon\"></image>\n        </view>\n      </view>\n    </view>\n    \n    <!-- 添加悬浮推广按钮 -->\n    <FloatPromotionButton \n      @click=\"showPromotionTool\" \n      :position=\"{right: '30rpx', bottom: '180rpx'}\"\n      size=\"100rpx\"\n    />\n    \n    <!-- 内容区域 -->\n    <scroll-view class=\"detail-content\" scroll-y>\n      <!-- 标签 -->\n      <view class=\"header-tag-container\">\n        <view class=\"header-tag\" :class=\"carpoolInfo.type\">\n          <text>{{typeText}}</text>\n          <view class=\"verified-tag\" v-if=\"carpoolInfo.isVerified\">\n            <view class=\"verified-icon\">\n              <svg width=\"14\" height=\"14\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n                <path d=\"M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41L9 16.17z\" fill=\"currentColor\" />\n              </svg>\n            </view>\n            <text>已认证</text>\n          </view>\n        </view>\n        </view>\n      \n      <!-- 信息头部 -->\n      <view class=\"detail-header\">\n        <!-- 行程信息 -->\n        <view class=\"route-card\">\n          <view class=\"route-points\">\n            <view class=\"route-point\">\n              <view class=\"point-dot start\"></view>\n              <view class=\"point-info\">\n                <text class=\"point-name\">{{carpoolInfo.startPoint}}</text>\n                <text class=\"point-address\" v-if=\"carpoolInfo.startAddress\">（{{carpoolInfo.startAddress}}）</text>\n              </view>\n            </view>\n            \n            <view class=\"route-divider\">\n              <view class=\"divider-line\"></view>\n              <view class=\"divider-info\">\n                <text class=\"divider-text\">约{{carpoolInfo.distance}}公里</text>\n              </view>\n            </view>\n            \n            <view class=\"route-point\">\n              <view class=\"point-dot end\"></view>\n              <view class=\"point-info\">\n                <text class=\"point-name\">{{carpoolInfo.endPoint}}</text>\n                <text class=\"point-address\" v-if=\"carpoolInfo.endAddress\">（{{carpoolInfo.endAddress}}）</text>\n              </view>\n            </view>\n          </view>\n        </view>\n        \n        <!-- 行程详情 -->\n        <view class=\"trip-details-card\">\n          <view class=\"trip-details-header\">\n            <text class=\"section-title\">行程详情</text>\n            </view>\n          \n          <view class=\"trip-details-content\">\n            <view class=\"trip-details-item\">\n              <text class=\"trip-details-label\">上车地点</text>\n              <text class=\"trip-details-value\">{{carpoolInfo.startPoint}}</text>\n            </view>\n            <view class=\"trip-details-item\">\n              <text class=\"trip-details-label\">下车地点</text>\n              <text class=\"trip-details-value\">{{carpoolInfo.endPoint}}</text>\n          </view>\n            <view class=\"trip-details-item\">\n              <text class=\"trip-details-label\">行程距离</text>\n              <text class=\"trip-details-value\">{{carpoolInfo.distance}}公里</text>\n            </view>\n            <view class=\"trip-details-item\">\n              <text class=\"trip-details-label\">预计用时</text>\n              <text class=\"trip-details-value\">约{{carpoolInfo.estimatedDuration}}小时</text>\n            </view>\n            <view class=\"trip-details-item\" v-if=\"carpoolInfo.tripNotes\">\n              <text class=\"trip-details-label\">备注</text>\n              <text class=\"trip-details-value\">{{carpoolInfo.tripNotes}}</text>\n          </view>\n            </view>\n            </view>\n        \n        <!-- 出发时间 -->\n        <view class=\"info-card\">\n          <view class=\"info-item-new\">\n            <text class=\"info-label-new\">出发时间</text>\n            <text class=\"info-value-new\">{{carpoolInfo.departureDate}} {{carpoolInfo.departureTime}}</text>\n          </view>\n          \n          <view class=\"info-item-new\" v-if=\"carpoolInfo.type === 'people-to-car'\">\n            <text class=\"info-label-new\">乘车人数</text>\n            <text class=\"info-value-new\">{{carpoolInfo.passengers}}人</text>\n            </view>\n          \n          <view class=\"info-item-new\" v-if=\"carpoolInfo.type === 'car-to-people'\">\n            <text class=\"info-label-new\">空余座位</text>\n            <text class=\"info-value-new\">{{carpoolInfo.availableSeats}}个</text>\n            </view>\n          \n          <view class=\"info-item-new\" v-if=\"carpoolInfo.price\">\n            <text class=\"info-label-new\">参考价格</text>\n            <text class=\"info-value-new price-value\">¥{{carpoolInfo.price}}</text>\n          </view>\n        </view>\n      </view>\n      \n      <!-- 用户信息 -->\n      <view class=\"user-card\">\n        <view class=\"user-header\">\n          <text class=\"section-title\">发布人信息</text>\n        </view>\n        \n        <view class=\"user-info\">\n          <image class=\"user-avatar\" :src=\"carpoolInfo.avatar\" mode=\"aspectFill\"></image>\n          <view class=\"user-details\">\n            <view class=\"user-name-row\">\n              <text class=\"user-name\">{{carpoolInfo.username}}</text>\n              <view class=\"user-badges\">\n                <view class=\"user-badge verified\" v-if=\"carpoolInfo.isVerified\">\n                  <view class=\"verified-icon-badge\">\n                    <svg width=\"12\" height=\"12\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n                      <path d=\"M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41L9 16.17z\" fill=\"currentColor\" />\n                    </svg>\n                  </view>\n                  <text>已认证</text>\n                </view>\n                <view class=\"user-badge premium\" v-if=\"carpoolInfo.isPremium\">置顶</view>\n              </view>\n            </view>\n            <text class=\"user-meta\">{{carpoolInfo.publishTime}} 发布</text>\n          </view>\n        </view>\n        \n        <!-- 司机评分 - 仅在车找人类型显示 -->\n        <view class=\"driver-rating\" v-if=\"carpoolInfo.type === 'car-to-people'\">\n          <view class=\"rating-header\">\n            <text class=\"rating-title\">司机评分</text>\n            <text class=\"rating-count\">{{carpoolInfo.ratingCount || 0}}人评价</text>\n          </view>\n          <view class=\"rating-stars\">\n            <view class=\"star-container\">\n              <view class=\"star-fill\" :style=\"{ width: (carpoolInfo.rating || 0) * 20 + '%' }\"></view>\n              <view class=\"star-bg\"></view>\n            </view>\n            <text class=\"rating-value\">{{carpoolInfo.rating || 0}}</text>\n          </view>\n          <view class=\"rating-tags\" v-if=\"carpoolInfo.ratingTags && carpoolInfo.ratingTags.length > 0\">\n            <view class=\"rating-tag\" v-for=\"(tag, index) in carpoolInfo.ratingTags\" :key=\"index\">\n              {{tag}}\n            </view>\n          </view>\n        </view>\n      </view>\n      \n      <!-- 车辆信息 -->\n      <view class=\"car-card\" v-if=\"carpoolInfo.type.includes('car-to')\">\n        <view class=\"car-header\">\n          <text class=\"section-title\">车辆信息</text>\n        </view>\n        \n        <view class=\"car-info\">\n          <view class=\"car-item\">\n            <text class=\"car-label\">车型</text>\n            <text class=\"car-value\">{{carpoolInfo.carModel}}</text>\n          </view>\n          <view class=\"car-item\">\n            <text class=\"car-label\">颜色</text>\n            <text class=\"car-value\">{{carpoolInfo.carColor}}</text>\n          </view>\n          <view class=\"car-item\">\n            <text class=\"car-label\">车牌</text>\n            <text class=\"car-value\">{{carpoolInfo.carPlate}}</text>\n          </view>\n        </view>\n      </view>\n      \n      <!-- 补充说明 -->\n      <view class=\"remark-card\" v-if=\"carpoolInfo.remark\">\n        <view class=\"remark-header\">\n          <text class=\"section-title\">补充说明</text>\n        </view>\n        \n        <view class=\"remark-content\">\n          <text>{{carpoolInfo.remark}}</text>\n        </view>\n      </view>\n      \n      <!-- 免责声明 -->\n      <view class=\"disclaimer-card\">\n        <view class=\"disclaimer-header\">\n          <text class=\"section-title\">免责声明</text>\n        </view>\n        <view class=\"disclaimer-content\">\n          <view class=\"disclaimer-icon\">\n            <image src=\"/static/images/tabbar/声明.png\" mode=\"aspectFit\"></image>\n          </view>\n          <text class=\"disclaimer-text\">本平台仅提供信息对接服务，不对拼车双方的行为提供任何担保，不承担任何法律责任。请用户自行甄别信息真实性，注意出行安全。使用本平台即表示您已同意以上条款。</text>\n        </view>\n      </view>\n      \n      <!-- 发布时间 -->\n      <view class=\"publish-time-card\">\n      <view class=\"publish-time\">\n        <text>发布时间：{{carpoolInfo.publishTime}}</text>\n        <text>信息有效期至：{{carpoolInfo.expiryTime}}</text>\n        </view>\n      </view>\n    </scroll-view>\n    \n    <!-- 底部操作栏 -->\n    <view class=\"bottom-actions\">\n      <view class=\"action-group\">\n        <button class=\"action-btn share\" open-type=\"share\">\n          <image src=\"/static/images/tabbar/a分享.png\" mode=\"aspectFit\"></image>\n          <text>转发</text>\n        </button>\n        <button class=\"action-btn favorite\" @click=\"toggleFavorite\">\n          <image :src=\"isFavorite ? '/static/images/tabbar/a收藏选中.png' : '/static/images/tabbar/a收藏.png'\" mode=\"aspectFit\"></image>\n          <text>{{isFavorite ? '已收藏' : '收藏'}}</text>\n        </button>\n        <button class=\"action-btn rate\" @click=\"rateDriver\">\n          <image src=\"/static/images/tabbar/a评价.png\" mode=\"aspectFit\"></image>\n          <text>评价</text>\n        </button>\n        <button class=\"action-btn message\" @click=\"sendMessage\">\n          <image src=\"/static/images/tabbar/a私信.png\" mode=\"aspectFit\"></image>\n          <text>私信</text>\n        </button>\n      </view>\n      <view class=\"call-btn-container\">\n        <button class=\"call-btn\" @click=\"callDriver\">\n          <image src=\"/static/images/tabbar/电话.png\" mode=\"aspectFit\" style=\"filter: brightness(0) invert(1);\"></image>\n          <text>联系司机</text>\n        </button>\n      </view>\n    </view>\n    \n    <!-- 安全区域 -->\n    <view class=\"safe-area-bottom\"></view>\n  </view>\n</template>\n\n<script setup>\nimport { ref, computed, onMounted } from 'vue';\nimport FloatPromotionButton from '@/components/FloatPromotionButton.vue';\n\n// 状态栏高度\nconst statusBarHeight = ref(20);\n\n// 拼车ID\nconst carpoolId = ref('');\n// 收藏状态\nconst isFavorite = ref(false);\n\n// 拼车信息\nconst carpoolInfo = ref({\n  id: 1,\n  type: 'car-to-people',\n  startPoint: '磁州城区-汽车站',\n  startAddress: '河北省邯郸市磁县磁州镇汽车站',\n  endPoint: '邯郸火车站',\n  endAddress: '河北省邯郸市邯山区陵西大街火车站',\n  departureDate: '2023-10-25',\n  departureTime: '15:30',\n  distance: '35',\n  estimatedDuration: '1',\n  tripNotes: '途经安徽-河南-陕西-汉中，请提前10分钟到达上车地点',\n  passengers: 1,\n  availableSeats: 3,\n  price: '30',\n  username: '李师傅',\n  avatar: '/static/images/avatar/user2.png',\n  phone: '13987654321',\n  isVerified: true,\n  isPremium: true,\n  publishTime: '2023-10-22 12:30',\n  expiryTime: '2023-10-25 15:30',\n  remark: '准时发车，不等待，有行李提前告知，谢谢配合',\n  carModel: '大众途观',\n  carColor: '白色',\n  carPlate: '冀G·12345',\n  rating: 4.8,\n  ratingCount: 125,\n  ratingTags: ['准时守信', '驾驶平稳', '热情礼貌', '车内整洁']\n});\n\n// 计算属性\nconst typeText = computed(() => {\n  const typeMap = {\n    'people-to-car': '人找车',\n    'car-to-people': '车找人',\n    'goods-to-car': '货找车',\n    'car-to-goods': '车找货'\n  };\n  return typeMap[carpoolInfo.value.type] || '人找车';\n});\n\n// 页面加载\nonMounted(() => {\n  // 获取状态栏高度\n  const sysInfo = uni.getSystemInfoSync();\n  statusBarHeight.value = sysInfo.statusBarHeight || 20;\n  \n  const pages = getCurrentPages();\n  const currentPage = pages[pages.length - 1];\n  const options = currentPage.$page?.options || {};\n  \n  if (options && options.id) {\n    carpoolId.value = options.id;\n    if (options.type) {\n      carpoolInfo.value.type = options.type;\n    }\n    getCarpoolDetail();\n  }\n});\n\n// 返回上一页\nconst goBack = () => {\n  uni.navigateBack({\n    delta: 1\n  });\n};\n\n// 分享到聊天\nconst onShareAppMessage = (options) => {\n  return {\n    title: `${typeText.value}：${carpoolInfo.value.startPoint} → ${carpoolInfo.value.endPoint}`,\n    path: `/carpool-package/pages/carpool/detail/index?id=${carpoolId.value}&type=${carpoolInfo.value.type}`,\n    imageUrl: '/static/images/share-cover.png',\n    desc: `出发时间: ${carpoolInfo.value.departureDate} ${carpoolInfo.value.departureTime}`,\n    success: (res) => {\n      uni.showToast({\n        title: '转发成功',\n        icon: 'success'\n      });\n    },\n    fail: (err) => {\n      console.error('转发失败', err);\n    }\n  };\n};\n\n// 分享到朋友圈\nconst onShareTimeline = () => {\n  return {\n    title: `${typeText.value}：${carpoolInfo.value.startPoint} → ${carpoolInfo.value.endPoint}`,\n    query: `id=${carpoolId.value}&type=${carpoolInfo.value.type}`,\n    imageUrl: '/static/images/share-cover.png',\n    success: (res) => {\n      uni.showToast({\n        title: '转发成功',\n        icon: 'success'\n      });\n    },\n    fail: (err) => {\n      console.error('转发失败', err);\n    }\n  };\n};\n\n// 获取拼车详情\nconst getCarpoolDetail = () => {\n  // 这里应该是真实的API调用\n  // 目前使用模拟数据\n  console.log('获取拼车ID:', carpoolId.value, '类型:', carpoolInfo.value.type);\n  \n  // 模拟API调用\n  uni.showLoading({\n    title: '加载中...'\n  });\n  \n  // 模拟不同类型的拼车数据\n  const mockData = {\n    // 车找人的示例数据\n    'car-to-people': {\n      id: carpoolId.value,\n      type: 'car-to-people',\n      startPoint: '磁州城区-汽车站',\n      startAddress: '磁州区新世纪广场北侧100米',\n      endPoint: '邯郸火车站',\n      endAddress: '邯郸市丛台区陵西大街61号',\n      distance: '35',\n      departureDate: '2023-10-15',\n      departureTime: '14:30',\n      availableSeats: 3,\n      price: '25',\n      username: '李师傅',\n      avatar: '/static/images/avatar/user2.png',\n      isVerified: true,\n      isPremium: true,\n      publishTime: '2023-10-14 09:30',\n      expiryTime: '2023-10-17 09:30',\n      contactPhone: '13987654321',\n      userId: 'user456',\n      carModel: '大众朗逸',\n      carColor: '白色',\n      carPlate: '冀D·12345',\n      remark: '准时发车，不等人，请提前到达上车点。可提供小费上门接送。',\n      rating: 4.8,\n      ratingCount: 125,\n      ratingTags: ['准时守信', '驾驶平稳', '热情礼貌', '车内整洁']\n    },\n    // 人找车的示例数据\n    'people-to-car': {\n      id: carpoolId.value,\n      type: 'people-to-car',\n      startPoint: '磁县人民医院',\n      startAddress: '磁县人民医院南门',\n      endPoint: '邯郸东站',\n      endAddress: '邯郸市丛台区邯郸东站',\n      distance: '30',\n      departureDate: '2023-10-16',\n      departureTime: '09:30',\n      passengers: 2,\n      price: '20',\n      username: '王先生',\n      avatar: '/static/images/avatar/user3.png',\n      isVerified: true,\n      isPremium: false,\n      publishTime: '2023-10-15 08:30',\n      expiryTime: '2023-10-18 08:30',\n      contactPhone: '13812345678',\n      userId: 'user789',\n      remark: '两人行李少，希望拼车去高铁站。',\n      rating: 4.8,\n      ratingCount: 125,\n      ratingTags: ['准时守信', '驾驶平稳', '热情礼貌', '车内整洁']\n    },\n    // 货找车的示例数据\n    'goods-to-car': {\n      id: carpoolId.value,\n      type: 'goods-to-car',\n      startPoint: '磁县商贸城',\n      startAddress: '磁县商贸城西门',\n      endPoint: '邯郸市区',\n      endAddress: '邯郸市丛台区中心广场',\n      distance: '28',\n      departureDate: '2023-10-17',\n      departureTime: '13:00',\n      price: '40',\n      username: '张先生',\n      avatar: '/static/images/avatar/user4.png',\n      isVerified: true,\n      isPremium: false,\n      publishTime: '2023-10-16 10:30',\n      expiryTime: '2023-10-19 10:30',\n      contactPhone: '13698765432',\n      userId: 'user101',\n      remark: '一箱水果，约20公斤，需要送到市区。',\n      rating: 4.8,\n      ratingCount: 125,\n      ratingTags: ['准时守信', '驾驶平稳', '热情礼貌', '车内整洁']\n    },\n    // 车找货的示例数据\n    'car-to-goods': {\n      id: carpoolId.value,\n      type: 'car-to-goods',\n      startPoint: '邯郸物流园',\n      startAddress: '邯郸市物流园区北门',\n      endPoint: '磁县',\n      endAddress: '磁县城区',\n      distance: '32',\n      departureDate: '2023-10-18',\n      departureTime: '16:00',\n      price: '35',\n      username: '赵师傅',\n      avatar: '/static/images/avatar/user5.png',\n      isVerified: true,\n      isPremium: true,\n      publishTime: '2023-10-17 14:30',\n      expiryTime: '2023-10-20 14:30',\n      contactPhone: '13765432109',\n      userId: 'user202',\n      carModel: '五菱小卡',\n      carColor: '白色',\n      carPlate: '冀D·54321',\n      remark: '回程车辆，可带小件货物，当天到达。',\n      rating: 4.8,\n      ratingCount: 125,\n      ratingTags: ['准时守信', '驾驶平稳', '热情礼貌', '车内整洁']\n    }\n  };\n  \n  // 使用传递过来的类型参数选择正确的数据\n  setTimeout(() => {\n    // 确保我们有有效的类型，否则使用默认类型\n    const type = carpoolInfo.value.type || 'car-to-people';\n    \n    // 更新拼车信息，保留原始的类型\n    const originalType = carpoolInfo.value.type;\n    carpoolInfo.value = mockData[type] || mockData['car-to-people'];\n    \n    // 确保类型正确\n    carpoolInfo.value.type = originalType;\n    \n    uni.hideLoading();\n  }, 500);\n};\n\n// 显示操作菜单\nconst showActionSheet = () => {\n  uni.showActionSheet({\n    itemList: ['评价司机', '举报', '刷新', '复制信息'],\n    success: (res) => {\n      switch(res.tapIndex) {\n        case 0:\n          rateDriver();\n          break;\n        case 1:\n          reportCarpool();\n          break;\n        case 2:\n          refreshCarpool();\n          break;\n        case 3:\n          copyInfo();\n          break;\n      }\n    }\n  });\n};\n\n// 举报\nconst reportCarpool = () => {\n  uni.navigateTo({\n    url: `/pages/carpool/report?id=${carpoolId.value}`\n  });\n};\n\n// 刷新\nconst refreshCarpool = () => {\n  uni.showLoading({\n    title: '刷新中...'\n  });\n  \n  setTimeout(() => {\n    uni.hideLoading();\n    uni.showToast({\n      title: '刷新成功',\n      icon: 'success'\n    });\n  }, 1000);\n};\n\n// 显示推广工具\nconst showPromotionTool = () => {\n  uni.navigateTo({\n    url: `/subPackages/promotion/pages/promotion-tool?type=carpool&id=${carpoolInfo.value.id}`\n  });\n};\n\n// 复制信息\nconst copyInfo = () => {\n  const info = `${typeText.value}：${carpoolInfo.value.startPoint} → ${carpoolInfo.value.endPoint}，出发时间：${carpoolInfo.value.departureDate} ${carpoolInfo.value.departureTime}，联系人：${carpoolInfo.value.username}，电话：${carpoolInfo.value.contactPhone}`;\n  \n  uni.setClipboardData({\n    data: info,\n    success: () => {\n      uni.showToast({\n        title: '已复制到剪贴板',\n        icon: 'success'\n      });\n    }\n  });\n};\n\n// 切换收藏状态\nconst toggleFavorite = () => {\n  isFavorite.value = !isFavorite.value;\n  \n  uni.showToast({\n    title: isFavorite.value ? '收藏成功' : '已取消收藏',\n    icon: 'success'\n  });\n};\n\n// 拨打电话\nconst callDriver = () => {\n  uni.makePhoneCall({\n    phoneNumber: carpoolInfo.value.phone\n  });\n};\n\n// 评价司机\nconst rateDriver = () => {\n  // 检查是否有联系过司机\n  const contactHistory = uni.getStorageSync('contactHistory') || [];\n  const hasContacted = contactHistory.some(item => item.userId === carpoolInfo.value.userId);\n  \n  if (hasContacted) {\n    // 已联系过，可以直接评价\n    uni.navigateTo({\n      url: `/carpool-package/pages/carpool/my/create-rating?driverId=${carpoolInfo.value.userId}&phoneNumber=${carpoolInfo.value.contactPhone}&carpoolId=${carpoolInfo.value.id}&startLocation=${encodeURIComponent(carpoolInfo.value.startPoint)}&endLocation=${encodeURIComponent(carpoolInfo.value.endPoint)}&departureTime=${carpoolInfo.value.departureTime}&departureDate=${carpoolInfo.value.departureDate}`\n    });\n  } else {\n    // 显示提示对话框\n    uni.showModal({\n      title: '提示',\n      content: '您需要先联系司机后才能进行评价，是否立即联系司机？',\n      success: (res) => {\n        if (res.confirm) {\n          callDriver();\n        }\n      }\n    });\n  }\n};\n\n// 发送私信\nconst sendMessage = () => {\n  // 实现发送私信的逻辑\n  console.log('发送私信');\n  \n  // 跳转到聊天页面\n  uni.navigateTo({\n    url: `/pages/message/chat?userId=${carpoolInfo.value.userId}&username=${encodeURIComponent(carpoolInfo.value.username)}&avatar=${encodeURIComponent(carpoolInfo.value.avatar)}&type=carpool&carpoolId=${carpoolInfo.value.id}`\n  });\n};\n\n// 暴露分享方法\ndefineExpose({\n  onShareAppMessage,\n  onShareTimeline\n});\n</script>\n\n<style lang=\"scss\">\n.detail-container {\n  min-height: 100vh;\n  background-color: #F5F8FC;\n  position: relative;\n  padding-top: calc(44px + var(--status-bar-height));\n}\n\n/* 自定义导航栏样式 */\n.custom-header {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  z-index: 100;\n  background-color: #1677FF;\n}\n\n.header-content {\n  height: 44px;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 0 10px;\n}\n\n.left-action, .right-action {\n  width: 40px;\n  height: 44px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.action-icon {\n  width: 24px;\n  height: 24px;\n  filter: brightness(0) invert(1);\n}\n\n.title-area {\n  flex: 1;\n  text-align: center;\n}\n\n.page-title {\n  font-size: 18px;\n  font-weight: 600;\n  color: #FFFFFF;\n}\n\n/* 内容区域 */\n.detail-content {\n  padding: 5rpx 20rpx 20rpx;\n  height: calc(100vh - 60px - var(--status-bar-height) - 44px);\n  box-sizing: border-box;\n}\n\n/* 标签容器 */\n.header-tag-container {\n  display: flex;\n  justify-content: flex-start;\n  margin-bottom: 24rpx;\n  padding: 25px 24rpx 0;\n}\n\n/* 信息头部 */\n.detail-header {\n  margin-bottom: 32rpx;\n  width: 100%;\n}\n\n.header-tag {\n  display: inline-flex;\n  align-items: center;\n  font-size: 28rpx;\n  font-weight: 600;\n  color: #ffffff;\n  padding: 10rpx 24rpx;\n  border-radius: 12rpx;\n  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);\n  z-index: 10;\n  position: relative;\n}\n\n.header-tag.people-to-car {\n  background: linear-gradient(135deg, #0A84FF, #5AC8FA);\n}\n\n.header-tag.car-to-people {\n  background: linear-gradient(135deg, #FF2D55, #FF9500);\n}\n\n.header-tag.goods-to-car {\n  background: linear-gradient(135deg, #30D158, #34C759);\n}\n\n.header-tag.car-to-goods {\n  background: linear-gradient(135deg, #FF9F0A, #FFD60A);\n}\n\n.verified-tag {\n  font-size: 22rpx;\n  background: linear-gradient(135deg, rgba(255, 255, 255, 0.3), rgba(255, 255, 255, 0.15));\n  padding: 4rpx 12rpx;\n  border-radius: 20rpx;\n  margin-left: 12rpx;\n  backdrop-filter: blur(4px);\n  -webkit-backdrop-filter: blur(4px);\n  white-space: nowrap;\n  display: inline-flex;\n  align-items: center;\n  justify-content: center;\n  position: relative;\n  z-index: 5;\n  border: 1rpx solid rgba(255, 255, 255, 0.4);\n  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.1);\n}\n\n.verified-icon {\n  width: 28rpx;\n  height: 28rpx;\n  border-radius: 50%;\n  background-color: #3DE07E;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-right: 6rpx;\n  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.15);\n}\n\n.verified-icon svg {\n  color: white;\n}\n\n/* 路线卡片 */\n.route-card, .info-card, .user-card, .car-card, .remark-card, .trip-details-card, .disclaimer-card {\n  background-color: #ffffff;\n  border-radius: 24rpx;\n  padding: 32rpx 32rpx 24rpx;\n  margin: 0 24rpx 24rpx;\n  box-shadow: 0 8rpx 24rpx rgba(10, 132, 255, 0.08);\n  width: calc(100% - 48rpx);\n  box-sizing: border-box;\n  overflow: hidden;\n}\n\n.route-points {\n  position: relative;\n}\n\n.route-point {\n  display: flex;\n  align-items: flex-start;\n  position: relative;\n  padding: 20rpx 0;\n}\n\n.point-dot {\n  width: 28rpx;\n  height: 28rpx;\n  border-radius: 50%;\n  margin-right: 24rpx;\n  margin-top: 8rpx;\n  flex-shrink: 0;\n  z-index: 2;\n}\n\n.start {\n  background-color: #0A84FF;\n  box-shadow: 0 0 0 8rpx rgba(10, 132, 255, 0.15);\n}\n\n.end {\n  background-color: #FF2D55;\n  box-shadow: 0 0 0 8rpx rgba(255, 45, 85, 0.15);\n}\n\n.point-info {\n  flex: 1;\n}\n\n.point-name {\n  font-size: 32rpx;\n  font-weight: 600;\n  color: #333333;\n  margin-bottom: 10rpx;\n  display: block;\n}\n\n.point-address {\n  font-size: 24rpx;\n  color: #999999;\n  line-height: 1.4;\n  display: block;\n}\n\n.route-divider {\n  padding: 10rpx 0 10rpx 14rpx;\n  position: relative;\n}\n\n.divider-line {\n  position: absolute;\n  left: 14rpx;\n  top: 0;\n  bottom: 0;\n  width: 2px;\n  background: linear-gradient(to bottom, #0A84FF, #FF2D55);\n  z-index: 1;\n}\n\n.divider-info {\n  margin-left: 34rpx;\n  background-color: #F2F7FD;\n  border-radius: 16rpx;\n  padding: 8rpx 16rpx;\n  display: inline-block;\n}\n\n.divider-text {\n  font-size: 24rpx;\n  color: #666666;\n  font-weight: 500;\n}\n\n/* 信息卡片 - 新设计 */\n.info-card {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 0;\n  background-color: #ffffff;\n}\n\n.info-item-new {\n  width: 50%;\n  padding: 20rpx;\n  box-sizing: border-box;\n  display: flex;\n  flex-direction: column;\n  border-bottom: 1px solid #F2F2F7;\n}\n\n.info-item-new:nth-child(odd) {\n  border-right: 1px solid #F2F2F7;\n}\n\n.info-label-new {\n  font-size: 24rpx;\n  color: #8E8E93;\n  margin-bottom: 12rpx;\n}\n\n.info-value-new {\n  font-size: 32rpx;\n  color: #333333;\n  font-weight: 500;\n}\n\n.price-value {\n  color: #FF2D55;\n}\n\n/* 用户卡片 */\n.user-card, .car-card, .remark-card {\n  background-color: #ffffff;\n  border-radius: 24rpx;\n  padding: 32rpx;\n  margin-bottom: 24rpx;\n  box-shadow: 0 8rpx 24rpx rgba(10, 132, 255, 0.08);\n}\n\n.user-header, .car-header, .remark-header {\n  margin-bottom: 24rpx;\n  border-bottom: 1px solid #F2F2F7;\n  padding-bottom: 16rpx;\n}\n\n.section-title {\n  font-size: 30rpx;\n  font-weight: 600;\n  color: #333333;\n  position: relative;\n  padding-left: 16rpx;\n}\n\n.section-title::before {\n  content: '';\n  position: absolute;\n  left: 0;\n  top: 6rpx;\n  bottom: 6rpx;\n  width: 6rpx;\n  background: linear-gradient(to bottom, #0A84FF, #5AC8FA);\n  border-radius: 3rpx;\n}\n\n.user-info {\n  display: flex;\n  align-items: center;\n}\n\n.user-avatar {\n  width: 80rpx;\n  height: 80rpx;\n  border-radius: 40rpx;\n  margin-right: 20rpx;\n  border: 2rpx solid #FFFFFF;\n  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);\n}\n\n.user-details {\n  flex: 1;\n}\n\n.user-name-row {\n  display: flex;\n  align-items: center;\n  margin-bottom: 8rpx;\n}\n\n.user-name {\n  font-size: 32rpx;\n  font-weight: 600;\n  color: #333333;\n  margin-right: 12rpx;\n}\n\n.user-badges {\n  display: flex;\n  gap: 8rpx;\n  flex-wrap: nowrap;\n}\n\n.user-badge {\n  font-size: 22rpx;\n  padding: 4rpx 12rpx;\n  border-radius: 16rpx;\n  white-space: nowrap;\n  display: inline-flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.user-badge.verified {\n  background: linear-gradient(135deg, rgba(10, 132, 255, 0.15), rgba(10, 132, 255, 0.05));\n  color: #0A84FF;\n  border: 1px solid rgba(10, 132, 255, 0.3);\n  display: inline-flex;\n  align-items: center;\n  box-shadow: 0 2rpx 6rpx rgba(10, 132, 255, 0.1);\n}\n\n.verified-icon-badge {\n  width: 24rpx;\n  height: 24rpx;\n  border-radius: 50%;\n  background-color: #0A84FF;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-right: 6rpx;\n}\n\n.verified-icon-badge svg {\n  color: white;\n}\n\n.user-badge.premium {\n  background-color: rgba(255, 45, 85, 0.1);\n  color: #FF2D55;\n  border: 1px solid rgba(255, 45, 85, 0.3);\n}\n\n.user-meta {\n  font-size: 24rpx;\n  color: #8E8E93;\n}\n\n/* 车辆信息 */\n.car-info {\n  display: flex;\n  flex-wrap: wrap;\n}\n\n.car-item {\n  width: 50%;\n  margin-bottom: 16rpx;\n}\n\n.car-label {\n  font-size: 24rpx;\n  color: #8E8E93;\n  margin-bottom: 4rpx;\n  display: block;\n}\n\n.car-value {\n  font-size: 30rpx;\n  color: #333333;\n  font-weight: 500;\n}\n\n/* 补充说明 */\n.remark-content {\n  font-size: 28rpx;\n  color: #666666;\n  line-height: 1.6;\n}\n\n/* 行程详情 */\n.trip-details-header {\n  margin-bottom: 24rpx;\n  border-bottom: 1px solid #F2F2F7;\n  padding-bottom: 16rpx;\n}\n\n.trip-details-content {\n  display: flex;\n  flex-direction: column;\n  gap: 24rpx;\n}\n\n.trip-details-item {\n  width: 100%;\n  display: flex;\n  justify-content: space-between;\n  padding: 8rpx 0;\n  border-bottom: 1px solid #F9F9F9;\n}\n\n.trip-details-label {\n  font-size: 28rpx;\n  color: #8E8E93;\n  flex: 1;\n}\n\n.trip-details-value {\n  font-size: 28rpx;\n  color: #333333;\n  font-weight: 500;\n  flex: 2;\n  text-align: right;\n}\n\n/* 免责声明 */\n.disclaimer-content {\n  display: flex;\n  align-items: flex-start;\n}\n\n.disclaimer-icon {\n  width: 40rpx;\n  height: 40rpx;\n  margin-right: 16rpx;\n  flex-shrink: 0;\n}\n\n.disclaimer-icon image {\n  width: 100%;\n  height: 100%;\n}\n\n.disclaimer-text {\n  font-size: 26rpx;\n  color: #8E8E93;\n  line-height: 1.6;\n  flex: 1;\n}\n\n/* 发布时间 */\n.publish-time-card {\n  background-color: #ffffff;\n  border-radius: 24rpx;\n  padding: 24rpx 32rpx;\n  margin: 0 24rpx 24rpx;\n  box-shadow: 0 8rpx 24rpx rgba(10, 132, 255, 0.08);\n  width: calc(100% - 48rpx);\n  box-sizing: border-box;\n  overflow: hidden;\n}\n\n.publish-time {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  gap: 8rpx;\n}\n\n.publish-time text {\n  font-size: 24rpx;\n  color: #8E8E93;\n}\n\n/* 底部操作栏 */\n.bottom-actions {\n  position: fixed;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background-color: #ffffff;\n  display: flex;\n  align-items: center;\n  padding: 12rpx 16rpx;\n  box-shadow: 0 -1rpx 6rpx rgba(0, 0, 0, 0.05);\n  z-index: 100;\n  padding-bottom: calc(12rpx + env(safe-area-inset-bottom));\n  box-sizing: border-box;\n  border-top: 1px solid #f5f5f5;\n}\n\n.action-group {\n  display: flex;\n  align-items: center;\n  flex: 1;\n}\n\n.action-btn {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: 8rpx 0;\n  flex: 1;\n  background: transparent;\n  border: none;\n  box-shadow: none;\n  height: 100rpx;\n}\n\n.action-btn::after {\n  border: none;\n}\n\n.action-btn image {\n  width: 44rpx;\n  height: 44rpx;\n  margin-bottom: 8rpx;\n}\n\n.action-btn text {\n  font-size: 22rpx;\n  color: #666666;\n  line-height: 1;\n}\n\n.call-btn-container {\n  display: flex;\n  justify-content: flex-end;\n  width: 240rpx;\n}\n\n.call-btn {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  height: 100rpx;\n  border-radius: 50rpx;\n  padding: 0 32rpx;\n  font-size: 28rpx;\n  font-weight: 500;\n  background-color: #1677FF;\n  color: #ffffff;\n  border: none;\n  box-shadow: 0 4rpx 8rpx rgba(22, 119, 255, 0.2);\n  width: 100%;\n}\n\n.call-btn image {\n  width: 32rpx;\n  height: 32rpx;\n  margin-right: 8rpx;\n}\n\n.call-btn::after {\n  border: none;\n}\n\n.safe-area-bottom {\n  height: env(safe-area-inset-bottom);\n  width: 100%;\n}\n\n/* 司机评分 */\n.driver-rating {\n  margin-top: 24rpx;\n  padding-top: 24rpx;\n  border-top: 1px solid #F2F2F7;\n}\n\n.rating-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 16rpx;\n}\n\n.rating-title {\n  font-size: 28rpx;\n  font-weight: 500;\n  color: #333333;\n}\n\n.rating-count {\n  font-size: 24rpx;\n  color: #8E8E93;\n}\n\n.rating-stars {\n  display: flex;\n  align-items: center;\n  margin-bottom: 16rpx;\n}\n\n.star-container {\n  position: relative;\n  width: 240rpx;\n  height: 36rpx;\n  margin-right: 16rpx;\n}\n\n.star-bg {\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  background-image: url('/static/images/tabbar/rating/star-bg.png');\n  background-size: 240rpx 36rpx;\n  background-repeat: no-repeat;\n}\n\n.star-fill {\n  position: absolute;\n  top: 0;\n  left: 0;\n  height: 100%;\n  background-image: url('/static/images/tabbar/rating/star-fill.png');\n  background-size: 240rpx 36rpx;\n  background-repeat: no-repeat;\n  z-index: 1;\n}\n\n.rating-value {\n  font-size: 32rpx;\n  font-weight: 600;\n  color: #FF9500;\n}\n\n.rating-tags {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 12rpx;\n}\n\n.rating-tag {\n  font-size: 24rpx;\n  color: #666666;\n  background-color: #F2F7FD;\n  padding: 6rpx 16rpx;\n  border-radius: 24rpx;\n}\n</style>\n", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/carpool-package/pages/carpool/detail/index.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "computed", "onMounted", "uni", "MiniProgramPage"], "mappings": ";;;;;;;;;;;AAuQA,MAAA,uBAAA,MAAA;;;;AAGA,UAAA,kBAAAA,cAAAA,IAAA,EAAA;AAGA,UAAA,YAAAA,cAAAA,IAAA,EAAA;AAEA,UAAA,aAAAA,cAAAA,IAAA,KAAA;AAGA,UAAA,cAAAA,cAAAA,IAAA;AAAA,MACA,IAAA;AAAA,MACA,MAAA;AAAA,MACA,YAAA;AAAA,MACA,cAAA;AAAA,MACA,UAAA;AAAA,MACA,YAAA;AAAA,MACA,eAAA;AAAA,MACA,eAAA;AAAA,MACA,UAAA;AAAA,MACA,mBAAA;AAAA,MACA,WAAA;AAAA,MACA,YAAA;AAAA,MACA,gBAAA;AAAA,MACA,OAAA;AAAA,MACA,UAAA;AAAA,MACA,QAAA;AAAA,MACA,OAAA;AAAA,MACA,YAAA;AAAA,MACA,WAAA;AAAA,MACA,aAAA;AAAA,MACA,YAAA;AAAA,MACA,QAAA;AAAA,MACA,UAAA;AAAA,MACA,UAAA;AAAA,MACA,UAAA;AAAA,MACA,QAAA;AAAA,MACA,aAAA;AAAA,MACA,YAAA,CAAA,QAAA,QAAA,QAAA,MAAA;AAAA,IACA,CAAA;AAGA,UAAA,WAAAC,cAAA,SAAA,MAAA;AACA,YAAA,UAAA;AAAA,QACA,iBAAA;AAAA,QACA,iBAAA;AAAA,QACA,gBAAA;AAAA,QACA,gBAAA;AAAA,MACA;AACA,aAAA,QAAA,YAAA,MAAA,IAAA,KAAA;AAAA,IACA,CAAA;AAGAC,kBAAAA,UAAA,MAAA;;AAEA,YAAA,UAAAC,oBAAA;AACA,sBAAA,QAAA,QAAA,mBAAA;AAEA,YAAA,QAAA;AACA,YAAA,cAAA,MAAA,MAAA,SAAA,CAAA;AACA,YAAA,YAAA,iBAAA,UAAA,mBAAA,YAAA,CAAA;AAEA,UAAA,WAAA,QAAA,IAAA;AACA,kBAAA,QAAA,QAAA;AACA,YAAA,QAAA,MAAA;AACA,sBAAA,MAAA,OAAA,QAAA;AAAA,QACA;AACA;MACA;AAAA,IACA,CAAA;AAGA,UAAA,SAAA,MAAA;AACAA,oBAAAA,MAAA,aAAA;AAAA,QACA,OAAA;AAAA,MACA,CAAA;AAAA,IACA;AAGA,UAAA,oBAAA,CAAA,YAAA;AACA,aAAA;AAAA,QACA,OAAA,GAAA,SAAA,KAAA,IAAA,YAAA,MAAA,UAAA,MAAA,YAAA,MAAA,QAAA;AAAA,QACA,MAAA,kDAAA,UAAA,KAAA,SAAA,YAAA,MAAA,IAAA;AAAA,QACA,UAAA;AAAA,QACA,MAAA,SAAA,YAAA,MAAA,aAAA,IAAA,YAAA,MAAA,aAAA;AAAA,QACA,SAAA,CAAA,QAAA;AACAA,wBAAAA,MAAA,UAAA;AAAA,YACA,OAAA;AAAA,YACA,MAAA;AAAA,UACA,CAAA;AAAA,QACA;AAAA,QACA,MAAA,CAAA,QAAA;AACAA,wBAAA,MAAA,MAAA,SAAA,yDAAA,QAAA,GAAA;AAAA,QACA;AAAA,MACA;AAAA,IACA;AAGA,UAAA,kBAAA,MAAA;AACA,aAAA;AAAA,QACA,OAAA,GAAA,SAAA,KAAA,IAAA,YAAA,MAAA,UAAA,MAAA,YAAA,MAAA,QAAA;AAAA,QACA,OAAA,MAAA,UAAA,KAAA,SAAA,YAAA,MAAA,IAAA;AAAA,QACA,UAAA;AAAA,QACA,SAAA,CAAA,QAAA;AACAA,wBAAAA,MAAA,UAAA;AAAA,YACA,OAAA;AAAA,YACA,MAAA;AAAA,UACA,CAAA;AAAA,QACA;AAAA,QACA,MAAA,CAAA,QAAA;AACAA,wBAAA,MAAA,MAAA,SAAA,yDAAA,QAAA,GAAA;AAAA,QACA;AAAA,MACA;AAAA,IACA;AAGA,UAAA,mBAAA,MAAA;AAGAA,oBAAAA,MAAA,MAAA,OAAA,yDAAA,WAAA,UAAA,OAAA,OAAA,YAAA,MAAA,IAAA;AAGAA,oBAAAA,MAAA,YAAA;AAAA,QACA,OAAA;AAAA,MACA,CAAA;AAGA,YAAA,WAAA;AAAA;AAAA,QAEA,iBAAA;AAAA,UACA,IAAA,UAAA;AAAA,UACA,MAAA;AAAA,UACA,YAAA;AAAA,UACA,cAAA;AAAA,UACA,UAAA;AAAA,UACA,YAAA;AAAA,UACA,UAAA;AAAA,UACA,eAAA;AAAA,UACA,eAAA;AAAA,UACA,gBAAA;AAAA,UACA,OAAA;AAAA,UACA,UAAA;AAAA,UACA,QAAA;AAAA,UACA,YAAA;AAAA,UACA,WAAA;AAAA,UACA,aAAA;AAAA,UACA,YAAA;AAAA,UACA,cAAA;AAAA,UACA,QAAA;AAAA,UACA,UAAA;AAAA,UACA,UAAA;AAAA,UACA,UAAA;AAAA,UACA,QAAA;AAAA,UACA,QAAA;AAAA,UACA,aAAA;AAAA,UACA,YAAA,CAAA,QAAA,QAAA,QAAA,MAAA;AAAA,QACA;AAAA;AAAA,QAEA,iBAAA;AAAA,UACA,IAAA,UAAA;AAAA,UACA,MAAA;AAAA,UACA,YAAA;AAAA,UACA,cAAA;AAAA,UACA,UAAA;AAAA,UACA,YAAA;AAAA,UACA,UAAA;AAAA,UACA,eAAA;AAAA,UACA,eAAA;AAAA,UACA,YAAA;AAAA,UACA,OAAA;AAAA,UACA,UAAA;AAAA,UACA,QAAA;AAAA,UACA,YAAA;AAAA,UACA,WAAA;AAAA,UACA,aAAA;AAAA,UACA,YAAA;AAAA,UACA,cAAA;AAAA,UACA,QAAA;AAAA,UACA,QAAA;AAAA,UACA,QAAA;AAAA,UACA,aAAA;AAAA,UACA,YAAA,CAAA,QAAA,QAAA,QAAA,MAAA;AAAA,QACA;AAAA;AAAA,QAEA,gBAAA;AAAA,UACA,IAAA,UAAA;AAAA,UACA,MAAA;AAAA,UACA,YAAA;AAAA,UACA,cAAA;AAAA,UACA,UAAA;AAAA,UACA,YAAA;AAAA,UACA,UAAA;AAAA,UACA,eAAA;AAAA,UACA,eAAA;AAAA,UACA,OAAA;AAAA,UACA,UAAA;AAAA,UACA,QAAA;AAAA,UACA,YAAA;AAAA,UACA,WAAA;AAAA,UACA,aAAA;AAAA,UACA,YAAA;AAAA,UACA,cAAA;AAAA,UACA,QAAA;AAAA,UACA,QAAA;AAAA,UACA,QAAA;AAAA,UACA,aAAA;AAAA,UACA,YAAA,CAAA,QAAA,QAAA,QAAA,MAAA;AAAA,QACA;AAAA;AAAA,QAEA,gBAAA;AAAA,UACA,IAAA,UAAA;AAAA,UACA,MAAA;AAAA,UACA,YAAA;AAAA,UACA,cAAA;AAAA,UACA,UAAA;AAAA,UACA,YAAA;AAAA,UACA,UAAA;AAAA,UACA,eAAA;AAAA,UACA,eAAA;AAAA,UACA,OAAA;AAAA,UACA,UAAA;AAAA,UACA,QAAA;AAAA,UACA,YAAA;AAAA,UACA,WAAA;AAAA,UACA,aAAA;AAAA,UACA,YAAA;AAAA,UACA,cAAA;AAAA,UACA,QAAA;AAAA,UACA,UAAA;AAAA,UACA,UAAA;AAAA,UACA,UAAA;AAAA,UACA,QAAA;AAAA,UACA,QAAA;AAAA,UACA,aAAA;AAAA,UACA,YAAA,CAAA,QAAA,QAAA,QAAA,MAAA;AAAA,QACA;AAAA,MACA;AAGA,iBAAA,MAAA;AAEA,cAAA,OAAA,YAAA,MAAA,QAAA;AAGA,cAAA,eAAA,YAAA,MAAA;AACA,oBAAA,QAAA,SAAA,IAAA,KAAA,SAAA,eAAA;AAGA,oBAAA,MAAA,OAAA;AAEAA,sBAAA,MAAA,YAAA;AAAA,MACA,GAAA,GAAA;AAAA,IACA;AAGA,UAAA,kBAAA,MAAA;AACAA,oBAAAA,MAAA,gBAAA;AAAA,QACA,UAAA,CAAA,QAAA,MAAA,MAAA,MAAA;AAAA,QACA,SAAA,CAAA,QAAA;AACA,kBAAA,IAAA,UAAA;AAAA,YACA,KAAA;AACA;AACA;AAAA,YACA,KAAA;AACA;AACA;AAAA,YACA,KAAA;AACA;AACA;AAAA,YACA,KAAA;AACA;AACA;AAAA,UACA;AAAA,QACA;AAAA,MACA,CAAA;AAAA,IACA;AAGA,UAAA,gBAAA,MAAA;AACAA,oBAAAA,MAAA,WAAA;AAAA,QACA,KAAA,4BAAA,UAAA,KAAA;AAAA,MACA,CAAA;AAAA,IACA;AAGA,UAAA,iBAAA,MAAA;AACAA,oBAAAA,MAAA,YAAA;AAAA,QACA,OAAA;AAAA,MACA,CAAA;AAEA,iBAAA,MAAA;AACAA,sBAAA,MAAA,YAAA;AACAA,sBAAAA,MAAA,UAAA;AAAA,UACA,OAAA;AAAA,UACA,MAAA;AAAA,QACA,CAAA;AAAA,MACA,GAAA,GAAA;AAAA,IACA;AAGA,UAAA,oBAAA,MAAA;AACAA,oBAAAA,MAAA,WAAA;AAAA,QACA,KAAA,+DAAA,YAAA,MAAA,EAAA;AAAA,MACA,CAAA;AAAA,IACA;AAGA,UAAA,WAAA,MAAA;AACA,YAAA,OAAA,GAAA,SAAA,KAAA,IAAA,YAAA,MAAA,UAAA,MAAA,YAAA,MAAA,QAAA,SAAA,YAAA,MAAA,aAAA,IAAA,YAAA,MAAA,aAAA,QAAA,YAAA,MAAA,QAAA,OAAA,YAAA,MAAA,YAAA;AAEAA,oBAAAA,MAAA,iBAAA;AAAA,QACA,MAAA;AAAA,QACA,SAAA,MAAA;AACAA,wBAAAA,MAAA,UAAA;AAAA,YACA,OAAA;AAAA,YACA,MAAA;AAAA,UACA,CAAA;AAAA,QACA;AAAA,MACA,CAAA;AAAA,IACA;AAGA,UAAA,iBAAA,MAAA;AACA,iBAAA,QAAA,CAAA,WAAA;AAEAA,oBAAAA,MAAA,UAAA;AAAA,QACA,OAAA,WAAA,QAAA,SAAA;AAAA,QACA,MAAA;AAAA,MACA,CAAA;AAAA,IACA;AAGA,UAAA,aAAA,MAAA;AACAA,oBAAAA,MAAA,cAAA;AAAA,QACA,aAAA,YAAA,MAAA;AAAA,MACA,CAAA;AAAA,IACA;AAGA,UAAA,aAAA,MAAA;AAEA,YAAA,iBAAAA,cAAA,MAAA,eAAA,gBAAA,KAAA,CAAA;AACA,YAAA,eAAA,eAAA,KAAA,UAAA,KAAA,WAAA,YAAA,MAAA,MAAA;AAEA,UAAA,cAAA;AAEAA,sBAAAA,MAAA,WAAA;AAAA,UACA,KAAA,4DAAA,YAAA,MAAA,MAAA,gBAAA,YAAA,MAAA,YAAA,cAAA,YAAA,MAAA,EAAA,kBAAA,mBAAA,YAAA,MAAA,UAAA,CAAA,gBAAA,mBAAA,YAAA,MAAA,QAAA,CAAA,kBAAA,YAAA,MAAA,aAAA,kBAAA,YAAA,MAAA,aAAA;AAAA,QACA,CAAA;AAAA,MACA,OAAA;AAEAA,sBAAAA,MAAA,UAAA;AAAA,UACA,OAAA;AAAA,UACA,SAAA;AAAA,UACA,SAAA,CAAA,QAAA;AACA,gBAAA,IAAA,SAAA;AACA;YACA;AAAA,UACA;AAAA,QACA,CAAA;AAAA,MACA;AAAA,IACA;AAGA,UAAA,cAAA,MAAA;AAEAA,oBAAAA,MAAA,MAAA,OAAA,yDAAA,MAAA;AAGAA,oBAAAA,MAAA,WAAA;AAAA,QACA,KAAA,8BAAA,YAAA,MAAA,MAAA,aAAA,mBAAA,YAAA,MAAA,QAAA,CAAA,WAAA,mBAAA,YAAA,MAAA,MAAA,CAAA,2BAAA,YAAA,MAAA,EAAA;AAAA,MACA,CAAA;AAAA,IACA;AAGA,aAAA;AAAA,MACA;AAAA,MACA;AAAA,IACA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACjoBA,GAAG,WAAWC,SAAe;"}