{"version": 3, "file": "reports.js", "sources": ["subPackages/merchant-admin-marketing/pages/marketing/distribution/reports.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/c3ViUGFja2FnZXNcbWVyY2hhbnQtYWRtaW4tbWFya2V0aW5nXHBhZ2VzXG1hcmtldGluZ1xkaXN0cmlidXRpb25ccmVwb3J0cy52dWU"], "sourcesContent": ["<template>\r\n  <view class=\"promotion-report-container\">\r\n    <uni-nav-bar title=\"推广报表\" :border=\"false\" backgroundColor=\"#8A2BE2\" color=\"#fff\" statusBar fixed>\r\n      <template #left>\r\n        <view class=\"nav-left\" @click=\"goBack\">\r\n          <uni-icons type=\"arrow-left\" color=\"#fff\" size=\"22\"></uni-icons>\r\n        </view>\r\n      </template>\r\n    </uni-nav-bar>\r\n    \r\n    <view class=\"content\">\r\n      <view class=\"stats-cards\">\r\n        <view class=\"stats-card\">\r\n          <view class=\"stats-title\">今日推广订单</view>\r\n          <view class=\"stats-value\">0</view>\r\n        </view>\r\n        <view class=\"stats-card\">\r\n          <view class=\"stats-title\">今日预估佣金</view>\r\n          <view class=\"stats-value\">¥0.00</view>\r\n        </view>\r\n        <view class=\"stats-card\">\r\n          <view class=\"stats-title\">本月推广订单</view>\r\n          <view class=\"stats-value\">0</view>\r\n        </view>\r\n        <view class=\"stats-card\">\r\n          <view class=\"stats-title\">本月预估佣金</view>\r\n          <view class=\"stats-value\">¥0.00</view>\r\n        </view>\r\n      </view>\r\n      \r\n      <view class=\"report-tabs\">\r\n        <view class=\"tab-header\">\r\n          <view class=\"tab\" :class=\"{ active: activeTab === 'orders' }\" @click=\"activeTab = 'orders'\">推广订单</view>\r\n          <view class=\"tab\" :class=\"{ active: activeTab === 'commission' }\" @click=\"activeTab = 'commission'\">佣金明细</view>\r\n        </view>\r\n        \r\n        <view class=\"tab-content\">\r\n          <!-- 推广订单 -->\r\n          <view v-if=\"activeTab === 'orders'\" class=\"orders-list\">\r\n            <view class=\"empty-tip\" v-if=\"ordersList.length === 0\">\r\n              <image src=\"/static/images/empty.png\" mode=\"aspectFit\" class=\"empty-image\"></image>\r\n              <view class=\"empty-text\">暂无推广订单数据</view>\r\n            </view>\r\n            <view v-else class=\"order-item\" v-for=\"(item, index) in ordersList\" :key=\"index\">\r\n              <view class=\"order-header\">\r\n                <view class=\"order-no\">订单号：{{item.orderNo}}</view>\r\n                <view class=\"order-status\">{{item.status}}</view>\r\n              </view>\r\n              <view class=\"order-info\">\r\n                <view class=\"goods-info\">\r\n                  <image class=\"goods-image\" :src=\"item.goodsImage\" mode=\"aspectFill\"></image>\r\n                  <view class=\"goods-detail\">\r\n                    <view class=\"goods-name\">{{item.goodsName}}</view>\r\n                    <view class=\"goods-price\">¥{{item.price}}</view>\r\n                  </view>\r\n                </view>\r\n                <view class=\"commission-info\">\r\n                  <view class=\"commission-label\">佣金：</view>\r\n                  <view class=\"commission-value\">¥{{item.commission}}</view>\r\n                </view>\r\n              </view>\r\n              <view class=\"order-footer\">\r\n                <view class=\"order-time\">{{item.time}}</view>\r\n              </view>\r\n            </view>\r\n          </view>\r\n          \r\n          <!-- 佣金明细 -->\r\n          <view v-if=\"activeTab === 'commission'\" class=\"commission-list\">\r\n            <view class=\"empty-tip\" v-if=\"commissionList.length === 0\">\r\n              <image src=\"/static/images/empty.png\" mode=\"aspectFit\" class=\"empty-image\"></image>\r\n              <view class=\"empty-text\">暂无佣金明细数据</view>\r\n            </view>\r\n            <view v-else class=\"commission-item\" v-for=\"(item, index) in commissionList\" :key=\"index\">\r\n              <view class=\"commission-header\">\r\n                <view class=\"commission-title\">{{item.title}}</view>\r\n                <view class=\"commission-amount\" :class=\"{'income': item.type === 'income'}\">{{item.type === 'income' ? '+' : '-'}}¥{{item.amount}}</view>\r\n              </view>\r\n              <view class=\"commission-time\">{{item.time}}</view>\r\n            </view>\r\n          </view>\r\n        </view>\r\n      </view>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  data() {\r\n    return {\r\n      activeTab: 'orders',\r\n      ordersList: [],\r\n      commissionList: []\r\n    }\r\n  },\r\n  onLoad() {\r\n    // 加载数据\r\n    this.loadData();\r\n  },\r\n  methods: {\r\n    goBack() {\r\n      uni.navigateBack();\r\n    },\r\n    loadData() {\r\n      // 模拟数据请求\r\n      // 实际应用中应通过API请求获取数据\r\n      setTimeout(() => {\r\n        // 这里仅作为示例，真实场景需要替换为API调用\r\n        this.ordersList = [];\r\n        this.commissionList = [];\r\n      }, 500);\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.promotion-report-container {\r\n  min-height: 100vh;\r\n  background-color: #f5f5f5;\r\n}\r\n\r\n.content {\r\n  margin-top: 100rpx;\r\n  padding: 20rpx;\r\n}\r\n\r\n.stats-cards {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  justify-content: space-between;\r\n}\r\n\r\n.stats-card {\r\n  width: 48%;\r\n  background-color: #ffffff;\r\n  border-radius: 12rpx;\r\n  padding: 24rpx;\r\n  margin-bottom: 20rpx;\r\n  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.05);\r\n}\r\n\r\n.stats-title {\r\n  font-size: 26rpx;\r\n  color: #666;\r\n  margin-bottom: 10rpx;\r\n}\r\n\r\n.stats-value {\r\n  font-size: 36rpx;\r\n  color: #333;\r\n  font-weight: bold;\r\n}\r\n\r\n.report-tabs {\r\n  margin-top: 20rpx;\r\n  background-color: #fff;\r\n  border-radius: 12rpx;\r\n  overflow: hidden;\r\n  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.05);\r\n}\r\n\r\n.tab-header {\r\n  display: flex;\r\n  border-bottom: 1rpx solid #eee;\r\n}\r\n\r\n.tab {\r\n  flex: 1;\r\n  text-align: center;\r\n  padding: 24rpx 0;\r\n  font-size: 28rpx;\r\n  color: #666;\r\n  position: relative;\r\n}\r\n\r\n.tab.active {\r\n  color: #8A2BE2;\r\n  font-weight: bold;\r\n}\r\n\r\n.tab.active::after {\r\n  content: '';\r\n  position: absolute;\r\n  bottom: 0;\r\n  left: 50%;\r\n  transform: translateX(-50%);\r\n  width: 60rpx;\r\n  height: 4rpx;\r\n  background-color: #8A2BE2;\r\n}\r\n\r\n.tab-content {\r\n  min-height: 500rpx;\r\n  padding: 20rpx;\r\n}\r\n\r\n.empty-tip {\r\n  text-align: center;\r\n  padding: 60rpx 0;\r\n}\r\n\r\n.empty-image {\r\n  width: 200rpx;\r\n  height: 200rpx;\r\n  margin-bottom: 20rpx;\r\n}\r\n\r\n.empty-text {\r\n  color: #999;\r\n  font-size: 28rpx;\r\n}\r\n\r\n.order-item {\r\n  background-color: #fff;\r\n  border-radius: 12rpx;\r\n  padding: 24rpx;\r\n  margin-bottom: 20rpx;\r\n  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.05);\r\n}\r\n\r\n.order-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  margin-bottom: 20rpx;\r\n}\r\n\r\n.order-no {\r\n  font-size: 26rpx;\r\n  color: #666;\r\n}\r\n\r\n.order-status {\r\n  font-size: 26rpx;\r\n  color: #8A2BE2;\r\n}\r\n\r\n.goods-info {\r\n  display: flex;\r\n  margin-bottom: 20rpx;\r\n}\r\n\r\n.goods-image {\r\n  width: 120rpx;\r\n  height: 120rpx;\r\n  border-radius: 8rpx;\r\n  margin-right: 20rpx;\r\n}\r\n\r\n.goods-detail {\r\n  flex: 1;\r\n}\r\n\r\n.goods-name {\r\n  font-size: 28rpx;\r\n  color: #333;\r\n  margin-bottom: 10rpx;\r\n}\r\n\r\n.goods-price {\r\n  font-size: 26rpx;\r\n  color: #ff6b6b;\r\n}\r\n\r\n.commission-info {\r\n  display: flex;\r\n  justify-content: flex-end;\r\n  margin-bottom: 20rpx;\r\n}\r\n\r\n.commission-label {\r\n  font-size: 26rpx;\r\n  color: #666;\r\n}\r\n\r\n.commission-value {\r\n  font-size: 26rpx;\r\n  color: #ff6b6b;\r\n}\r\n\r\n.order-footer {\r\n  text-align: right;\r\n}\r\n\r\n.order-time {\r\n  font-size: 24rpx;\r\n  color: #999;\r\n}\r\n\r\n.commission-item {\r\n  background-color: #fff;\r\n  border-radius: 12rpx;\r\n  padding: 24rpx;\r\n  margin-bottom: 20rpx;\r\n  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.05);\r\n}\r\n\r\n.commission-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  margin-bottom: 10rpx;\r\n}\r\n\r\n.commission-title {\r\n  font-size: 28rpx;\r\n  color: #333;\r\n}\r\n\r\n.commission-amount {\r\n  font-size: 28rpx;\r\n  color: #ff6b6b;\r\n}\r\n\r\n.commission-amount.income {\r\n  color: #27ae60;\r\n}\r\n\r\n.commission-time {\r\n  font-size: 24rpx;\r\n  color: #999;\r\n  text-align: right;\r\n}\r\n</style> ", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/subPackages/merchant-admin-marketing/pages/marketing/distribution/reports.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni"], "mappings": ";;;AAwFA,MAAK,YAAU;AAAA,EACb,OAAO;AACL,WAAO;AAAA,MACL,WAAW;AAAA,MACX,YAAY,CAAE;AAAA,MACd,gBAAgB,CAAC;AAAA,IACnB;AAAA,EACD;AAAA,EACD,SAAS;AAEP,SAAK,SAAQ;AAAA,EACd;AAAA,EACD,SAAS;AAAA,IACP,SAAS;AACPA,oBAAG,MAAC,aAAY;AAAA,IACjB;AAAA,IACD,WAAW;AAGT,iBAAW,MAAM;AAEf,aAAK,aAAa;AAClB,aAAK,iBAAiB;MACvB,GAAE,GAAG;AAAA,IACR;AAAA,EACF;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACjHA,GAAG,WAAW,eAAe;"}