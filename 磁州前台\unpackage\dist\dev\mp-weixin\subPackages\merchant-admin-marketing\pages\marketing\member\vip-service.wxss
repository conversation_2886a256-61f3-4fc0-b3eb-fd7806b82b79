/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 移除阿里妈妈字体引用，改用系统默认字体 */
/* 移除原有阿里妈妈字体定义
$uni-font-family: 'AlimamaShuHeiTi';
$uni-font-family-text: 'AlimamaShuHeiTi', -apple-system, BlinkMacSystemFont, 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;
*/
body.data-v-a6b86a0e, html.data-v-a6b86a0e, #app.data-v-a6b86a0e, .index-container.data-v-a6b86a0e {
  font-family: "AlimamaShuHeiTi", -apple-system, BlinkMacSystemFont, "Helvetica Neue", "PingFang SC", "Microsoft YaHei", sans-serif;
}
.vip-service-container.data-v-a6b86a0e {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}
.page-header.data-v-a6b86a0e {
  margin-bottom: 20rpx;
}
.title-section .page-title.data-v-a6b86a0e {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}
.title-section .page-subtitle.data-v-a6b86a0e {
  font-size: 24rpx;
  color: #666;
  margin-top: 6rpx;
}
.section-card.data-v-a6b86a0e {
  background-color: #fff;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
  padding: 24rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}
.section-card .section-title.data-v-a6b86a0e {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  position: relative;
  padding-left: 20rpx;
}
.section-card .section-title.data-v-a6b86a0e::before {
  content: "";
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 8rpx;
  height: 30rpx;
  background-color: #4A00E0;
  border-radius: 4rpx;
}
.switch-item.data-v-a6b86a0e {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.switch-item .switch-content .switch-title.data-v-a6b86a0e {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
}
.switch-item .switch-content .switch-desc.data-v-a6b86a0e {
  font-size: 24rpx;
  color: #666;
  margin-top: 6rpx;
}
.form-item.data-v-a6b86a0e {
  margin-bottom: 24rpx;
}
.form-item.data-v-a6b86a0e:last-child {
  margin-bottom: 0;
}
.form-item .form-label.data-v-a6b86a0e {
  display: block;
  font-size: 28rpx;
  color: #333;
  margin-bottom: 12rpx;
}
.form-item .form-input.data-v-a6b86a0e {
  width: 100%;
  height: 80rpx;
  border: 1rpx solid #ddd;
  border-radius: 8rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  box-sizing: border-box;
}
.form-item .form-input-group.data-v-a6b86a0e {
  display: flex;
  align-items: center;
}
.form-item .form-input-group .form-input.data-v-a6b86a0e {
  width: 200rpx;
}
.form-item .form-input-group .input-suffix.data-v-a6b86a0e {
  margin-left: 10rpx;
  font-size: 28rpx;
  color: #333;
}
.form-item .form-textarea.data-v-a6b86a0e {
  width: 100%;
  height: 160rpx;
  border: 1rpx solid #ddd;
  border-radius: 8rpx;
  padding: 20rpx;
  font-size: 28rpx;
  box-sizing: border-box;
}
.form-item .checkbox-group.data-v-a6b86a0e {
  display: flex;
  flex-wrap: wrap;
}
.form-item .checkbox-group .checkbox-item.data-v-a6b86a0e {
  display: flex;
  align-items: center;
  margin-right: 30rpx;
  margin-bottom: 20rpx;
}
.form-item .checkbox-group .checkbox-item .checkbox-icon.data-v-a6b86a0e {
  width: 40rpx;
  height: 40rpx;
  border: 1rpx solid #ddd;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 10rpx;
}
.form-item .checkbox-group .checkbox-item .checkbox-icon .checkbox-inner.data-v-a6b86a0e {
  width: 24rpx;
  height: 24rpx;
  background-color: #4A00E0;
}
.form-item .checkbox-group .checkbox-item.active .checkbox-icon.data-v-a6b86a0e {
  border-color: #4A00E0;
}
.form-item .checkbox-group .checkbox-item .checkbox-text.data-v-a6b86a0e {
  font-size: 28rpx;
  color: #333;
}
.form-item .radio-group.data-v-a6b86a0e {
  display: flex;
}
.form-item .radio-group .radio-item.data-v-a6b86a0e {
  padding: 12rpx 30rpx;
  border: 1rpx solid #ddd;
  border-radius: 8rpx;
  margin-right: 20rpx;
}
.form-item .radio-group .radio-item .radio-text.data-v-a6b86a0e {
  font-size: 28rpx;
  color: #333;
}
.form-item .radio-group .radio-item.active.data-v-a6b86a0e {
  background-color: #4A00E0;
  border-color: #4A00E0;
}
.form-item .radio-group .radio-item.active .radio-text.data-v-a6b86a0e {
  color: #fff;
}
.form-item.switch-item.data-v-a6b86a0e {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.form-item.switch-item .form-label.data-v-a6b86a0e {
  margin-bottom: 0;
}
.form-item .avatar-upload .preview-avatar.data-v-a6b86a0e {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
}
.form-item .avatar-upload .upload-btn.data-v-a6b86a0e {
  width: 120rpx;
  height: 120rpx;
  border: 1rpx dashed #ddd;
  border-radius: 50%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.form-item .avatar-upload .upload-btn .upload-icon.data-v-a6b86a0e {
  font-size: 40rpx;
  color: #999;
  margin-bottom: 4rpx;
}
.form-item .avatar-upload .upload-btn .upload-text.data-v-a6b86a0e {
  font-size: 20rpx;
  color: #999;
}
.staff-list.data-v-a6b86a0e {
  margin-bottom: 24rpx;
}
.staff-list .staff-item.data-v-a6b86a0e {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}
.staff-list .staff-item.data-v-a6b86a0e:last-child {
  border-bottom: none;
}
.staff-list .staff-item .staff-info.data-v-a6b86a0e {
  display: flex;
  align-items: center;
}
.staff-list .staff-item .staff-info .staff-avatar.data-v-a6b86a0e {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  margin-right: 20rpx;
}
.staff-list .staff-item .staff-info .staff-detail .staff-name.data-v-a6b86a0e {
  font-size: 28rpx;
  color: #333;
  font-weight: bold;
}
.staff-list .staff-item .staff-info .staff-detail .staff-position.data-v-a6b86a0e {
  font-size: 24rpx;
  color: #666;
  margin-top: 6rpx;
}
.staff-list .staff-item .staff-actions.data-v-a6b86a0e {
  display: flex;
  align-items: center;
}
.staff-list .staff-item .staff-actions .staff-status.data-v-a6b86a0e {
  font-size: 24rpx;
  color: #999;
  padding: 4rpx 16rpx;
  background-color: #f5f5f5;
  border-radius: 20rpx;
  margin-right: 16rpx;
}
.staff-list .staff-item .staff-actions .staff-status.active.data-v-a6b86a0e {
  color: #4A00E0;
  background-color: rgba(74, 0, 224, 0.1);
}
.staff-list .staff-item .staff-actions .action-btn.data-v-a6b86a0e {
  font-size: 24rpx;
  padding: 4rpx 16rpx;
  border-radius: 20rpx;
  margin-left: 10rpx;
}
.staff-list .staff-item .staff-actions .action-btn.edit.data-v-a6b86a0e {
  color: #4A00E0;
  background-color: rgba(74, 0, 224, 0.1);
}
.staff-list .staff-item .staff-actions .action-btn.delete.data-v-a6b86a0e {
  color: #ff4d4f;
  background-color: rgba(255, 77, 79, 0.1);
}
.add-btn.data-v-a6b86a0e {
  width: 100%;
  height: 80rpx;
  background-color: #f5f5f5;
  color: #666;
  font-size: 28rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8rpx;
}
.level-list .level-item.data-v-a6b86a0e {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}
.level-list .level-item.data-v-a6b86a0e:last-child {
  border-bottom: none;
}
.level-list .level-item .level-checkbox.data-v-a6b86a0e {
  width: 40rpx;
  height: 40rpx;
  border: 1rpx solid #ddd;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
}
.level-list .level-item .level-checkbox.checked.data-v-a6b86a0e {
  background-color: #4A00E0;
  border-color: #4A00E0;
}
.level-list .level-item .level-checkbox .checkbox-inner.data-v-a6b86a0e {
  width: 20rpx;
  height: 20rpx;
  background-color: #fff;
}
.level-list .level-item .level-content.data-v-a6b86a0e {
  flex: 1;
}
.level-list .level-item .level-content .level-name.data-v-a6b86a0e {
  font-size: 28rpx;
  color: #333;
  font-weight: bold;
}
.level-list .level-item .level-content .level-desc.data-v-a6b86a0e {
  font-size: 24rpx;
  color: #666;
  margin-top: 6rpx;
}
.bottom-bar.data-v-a6b86a0e {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  padding: 20rpx;
  background-color: #fff;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
}
.bottom-bar .save-btn.data-v-a6b86a0e {
  width: 100%;
  height: 90rpx;
  background-color: #4A00E0;
  color: #fff;
  font-size: 32rpx;
  border-radius: 45rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.staff-form-popup.data-v-a6b86a0e {
  width: 650rpx;
  background-color: #fff;
  border-radius: 12rpx;
  overflow: hidden;
}
.staff-form-popup .popup-header.data-v-a6b86a0e {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx;
  border-bottom: 1rpx solid #eee;
}
.staff-form-popup .popup-header .popup-title.data-v-a6b86a0e {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}
.staff-form-popup .popup-header .popup-close.data-v-a6b86a0e {
  font-size: 40rpx;
  color: #999;
}
.staff-form-popup .popup-body.data-v-a6b86a0e {
  padding: 24rpx;
  max-height: 60vh;
  overflow-y: auto;
}
.staff-form-popup .popup-footer.data-v-a6b86a0e {
  display: flex;
  border-top: 1rpx solid #eee;
}
.staff-form-popup .popup-footer button.data-v-a6b86a0e {
  flex: 1;
  height: 90rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  border-radius: 0;
}
.staff-form-popup .popup-footer button.cancel-btn.data-v-a6b86a0e {
  background-color: #f5f5f5;
  color: #666;
}
.staff-form-popup .popup-footer button.confirm-btn.data-v-a6b86a0e {
  background-color: #4A00E0;
  color: #fff;
}