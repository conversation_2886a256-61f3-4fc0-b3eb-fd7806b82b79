{"version": 3, "file": "index.js", "sources": ["subPackages/merchant-admin-marketing/pages/marketing/distribution/index.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/c3ViUGFja2FnZXNcbWVyY2hhbnQtYWRtaW4tbWFya2V0aW5nXHBhZ2VzXG1hcmtldGluZ1xkaXN0cmlidXRpb25caW5kZXgudnVl"], "sourcesContent": ["<template>\r\n  <view class=\"distribution-container\">\r\n    <!-- 自定义导航栏 -->\r\n    <view class=\"navbar\">\r\n      <view class=\"navbar-back\" @click=\"goBack\">\r\n        <view class=\"back-icon\"></view>\r\n      </view>\r\n      <text class=\"navbar-title\">分销系统</text>\r\n      <view class=\"navbar-right\">\r\n        <view class=\"help-icon\" @click=\"showHelp\">?</view>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 数据概览卡片 -->\r\n    <view class=\"overview-section\">\r\n      <view class=\"overview-header\">\r\n        <text class=\"section-title\">分销概览</text>\r\n        <view class=\"date-picker\" @click=\"showDatePicker\">\r\n          <text class=\"date-text\">{{dateRange}}</text>\r\n          <view class=\"date-icon\"></view>\r\n        </view>\r\n      </view>\r\n      \r\n      <view class=\"stats-cards\">\r\n        <view class=\"stats-card\">\r\n          <view class=\"card-value\">{{distributionData.totalDistributors}}</view>\r\n          <view class=\"card-label\">分销员总数</view>\r\n          <view class=\"card-trend\" :class=\"distributionData.distributorsTrend\">\r\n            <view class=\"trend-arrow\"></view>\r\n            <text class=\"trend-value\">{{distributionData.distributorsGrowth}}</text>\r\n          </view>\r\n        </view>\r\n        \r\n        <view class=\"stats-card\">\r\n          <view class=\"card-value\">¥{{formatNumber(distributionData.totalCommission)}}</view>\r\n          <view class=\"card-label\">累计佣金</view>\r\n          <view class=\"card-trend\" :class=\"distributionData.commissionTrend\">\r\n            <view class=\"trend-arrow\"></view>\r\n            <text class=\"trend-value\">{{distributionData.commissionGrowth}}</text>\r\n          </view>\r\n        </view>\r\n        \r\n        <view class=\"stats-card\">\r\n          <view class=\"card-value\">{{distributionData.totalOrders}}</view>\r\n          <view class=\"card-label\">分销订单数</view>\r\n          <view class=\"card-trend\" :class=\"distributionData.ordersTrend\">\r\n            <view class=\"trend-arrow\"></view>\r\n            <text class=\"trend-value\">{{distributionData.ordersGrowth}}</text>\r\n          </view>\r\n        </view>\r\n        \r\n        <view class=\"stats-card\">\r\n          <view class=\"card-value\">¥{{formatNumber(distributionData.averageCommission)}}</view>\r\n          <view class=\"card-label\">平均佣金</view>\r\n          <view class=\"card-trend\" :class=\"distributionData.averageTrend\">\r\n            <view class=\"trend-arrow\"></view>\r\n            <text class=\"trend-value\">{{distributionData.averageGrowth}}</text>\r\n          </view>\r\n        </view>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 分销员榜单 -->\r\n    <view class=\"leaderboard-section\">\r\n      <view class=\"section-header\">\r\n        <text class=\"section-title\">分销员排行榜</text>\r\n        <text class=\"view-all\" @click=\"viewAllDistributors\">查看全部</text>\r\n      </view>\r\n      \r\n      <view class=\"leaderboard-list\">\r\n        <view class=\"leaderboard-item\" v-for=\"(item, index) in topDistributors\" :key=\"index\" @click=\"viewDistributorDetail(item)\">\r\n          <view class=\"rank-badge\" :class=\"{'top-rank': index < 3}\">{{index + 1}}</view>\r\n          <image class=\"distributor-avatar\" :src=\"item.avatar\" mode=\"aspectFill\"></image>\r\n          <view class=\"distributor-info\">\r\n            <view class=\"distributor-name\">{{item.name}}</view>\r\n            <view class=\"distributor-level\">{{item.level}}</view>\r\n          </view>\r\n          <view class=\"distributor-stats\">\r\n            <view class=\"stat-item\">\r\n              <text class=\"stat-value\">¥{{formatNumber(item.commission)}}</text>\r\n              <text class=\"stat-label\">佣金</text>\r\n            </view>\r\n            <view class=\"stat-item\">\r\n              <text class=\"stat-value\">{{item.orders}}</text>\r\n              <text class=\"stat-label\">订单</text>\r\n            </view>\r\n          </view>\r\n        </view>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 分销设置卡片 -->\r\n    <view class=\"settings-section\">\r\n      <view class=\"section-header\">\r\n        <text class=\"section-title\">分销设置</text>\r\n        <view class=\"switch-container\">\r\n          <text class=\"switch-label\">分销功能</text>\r\n          <switch :checked=\"distributionEnabled\" @change=\"toggleDistribution\" color=\"#6B0FBE\" />\r\n        </view>\r\n      </view>\r\n      \r\n      <view class=\"settings-list\">\r\n        <view class=\"settings-item\" @click=\"navigateToSetting('conditions')\">\r\n          <view class=\"item-left\">\r\n            <view class=\"item-icon conditions\"></view>\r\n            <text class=\"item-title\">成为分销员条件</text>\r\n          </view>\r\n          <view class=\"item-right\">\r\n            <text class=\"item-value\">{{distributionSettings.conditionText}}</text>\r\n            <view class=\"item-arrow\"></view>\r\n          </view>\r\n        </view>\r\n        \r\n        <view class=\"settings-item\" @click=\"navigateToSetting('levels')\">\r\n          <view class=\"item-left\">\r\n            <view class=\"item-icon levels\"></view>\r\n            <text class=\"item-title\">分销等级设置</text>\r\n          </view>\r\n          <view class=\"item-right\">\r\n            <text class=\"item-value\">{{distributionSettings.levelCount}}个等级</text>\r\n            <view class=\"item-arrow\"></view>\r\n          </view>\r\n        </view>\r\n        \r\n        <view class=\"settings-item\" @click=\"navigateToSetting('withdrawal')\">\r\n          <view class=\"item-left\">\r\n            <view class=\"item-icon withdrawal\"></view>\r\n            <text class=\"item-title\">提现设置</text>\r\n          </view>\r\n          <view class=\"item-right\">\r\n            <text class=\"item-value\">最低¥{{distributionSettings.minWithdrawal}}元</text>\r\n            <view class=\"item-arrow\"></view>\r\n          </view>\r\n        </view>\r\n        \r\n        <view class=\"settings-item\" @click=\"navigateToSetting('agreement')\">\r\n          <view class=\"item-left\">\r\n            <view class=\"item-icon agreement\"></view>\r\n            <text class=\"item-title\">分销协议</text>\r\n          </view>\r\n          <view class=\"item-right\">\r\n            <view class=\"item-arrow\"></view>\r\n          </view>\r\n        </view>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 佣金规则卡片 -->\r\n    <view class=\"commission-section\">\r\n      <view class=\"section-header\">\r\n        <text class=\"section-title\">佣金规则</text>\r\n        <view class=\"edit-btn\" @click=\"editCommissionRules\">编辑</view>\r\n      </view>\r\n      \r\n      <view class=\"commission-rules\">\r\n        <view class=\"rule-card\">\r\n          <view class=\"rule-header\">\r\n            <text class=\"rule-title\">一级分销</text>\r\n            <text class=\"rule-value\">{{commissionRules.level1}}%</text>\r\n          </view>\r\n          <view class=\"rule-desc\">直接推广获得订单金额的佣金比例</view>\r\n        </view>\r\n        \r\n        <view class=\"rule-card\">\r\n          <view class=\"rule-header\">\r\n            <text class=\"rule-title\">二级分销</text>\r\n            <text class=\"rule-value\">{{commissionRules.level2}}%</text>\r\n          </view>\r\n          <view class=\"rule-desc\">间接推广获得订单金额的佣金比例</view>\r\n        </view>\r\n        \r\n        <view class=\"rule-card\" v-if=\"commissionRules.level3 > 0\">\r\n          <view class=\"rule-header\">\r\n            <text class=\"rule-title\">三级分销</text>\r\n            <text class=\"rule-value\">{{commissionRules.level3}}%</text>\r\n          </view>\r\n          <view class=\"rule-desc\">三级推广获得订单金额的佣金比例</view>\r\n        </view>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 推广工具卡片 -->\r\n    <view class=\"promotion-section\">\r\n      <view class=\"section-header\">\r\n        <text class=\"section-title\">推广工具</text>\r\n      </view>\r\n      \r\n      <view class=\"promotion-tools\">\r\n        <view class=\"tool-card\" v-for=\"(tool, index) in promotionTools\" :key=\"index\" @click=\"usePromotionTool(tool)\">\r\n          <image class=\"tool-icon\" :src=\"tool.icon\" mode=\"aspectFit\"></image>\r\n          <text class=\"tool-name\">{{tool.name}}</text>\r\n          <text class=\"tool-desc\">{{tool.description}}</text>\r\n        </view>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 浮动操作按钮 -->\r\n    <view class=\"floating-action-button\" @click=\"showActionMenu\">\r\n      <view class=\"fab-icon\">+</view>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  data() {\r\n    return {\r\n      dateRange: '2023-04-01 ~ 2023-04-30',\r\n      distributionEnabled: true,\r\n      \r\n      // 分销数据概览\r\n      distributionData: {\r\n        totalDistributors: 268,\r\n        distributorsTrend: 'up',\r\n        distributorsGrowth: '12%',\r\n        \r\n        totalCommission: 26584.50,\r\n        commissionTrend: 'up',\r\n        commissionGrowth: '8.5%',\r\n        \r\n        totalOrders: 1256,\r\n        ordersTrend: 'up',\r\n        ordersGrowth: '15.2%',\r\n        \r\n        averageCommission: 99.20,\r\n        averageTrend: 'down',\r\n        averageGrowth: '3.1%'\r\n      },\r\n      \r\n      // 分销员排行榜\r\n      topDistributors: [\r\n        {\r\n          id: 1,\r\n          name: '张小明',\r\n          avatar: '/static/images/avatar-1.png',\r\n          level: '钻石分销员',\r\n          commission: 5862.50,\r\n          orders: 86\r\n        },\r\n        {\r\n          id: 2,\r\n          name: '李佳怡',\r\n          avatar: '/static/images/avatar-2.png',\r\n          level: '金牌分销员',\r\n          commission: 4235.80,\r\n          orders: 64\r\n        },\r\n        {\r\n          id: 3,\r\n          name: '王大力',\r\n          avatar: '/static/images/avatar-3.png',\r\n          level: '金牌分销员',\r\n          commission: 3756.20,\r\n          orders: 58\r\n        },\r\n        {\r\n          id: 4,\r\n          name: '赵丽丽',\r\n          avatar: '/static/images/avatar-4.png',\r\n          level: '银牌分销员',\r\n          commission: 2845.60,\r\n          orders: 42\r\n        },\r\n        {\r\n          id: 5,\r\n          name: '陈小红',\r\n          avatar: '/static/images/avatar-5.png',\r\n          level: '银牌分销员',\r\n          commission: 2356.80,\r\n          orders: 35\r\n        }\r\n      ],\r\n      \r\n      // 分销设置\r\n      distributionSettings: {\r\n        conditionText: '购买商品并申请',\r\n        levelCount: 3,\r\n        minWithdrawal: 50\r\n      },\r\n      \r\n      // 佣金规则\r\n      commissionRules: {\r\n        level1: 15,\r\n        level2: 5,\r\n        level3: 2\r\n      },\r\n      \r\n      // 推广工具\r\n      promotionTools: [\r\n        {\r\n          id: 1,\r\n          name: '推广海报',\r\n          description: '生成专属推广海报',\r\n          icon: '/static/images/poster-icon.png'\r\n        },\r\n        {\r\n          id: 2,\r\n          name: '推广二维码',\r\n          description: '生成专属推广二维码',\r\n          icon: '/static/images/qrcode-icon.png'\r\n        },\r\n        {\r\n          id: 3,\r\n          name: '商品推广卡片',\r\n          description: '生成商品推广卡片',\r\n          icon: '/static/images/product-card-icon.png'\r\n        },\r\n        {\r\n          id: 4,\r\n          name: '分销员邀请',\r\n          description: '邀请好友成为分销员',\r\n          icon: '/static/images/invite-icon.png'\r\n        },\r\n        {\r\n          id: 5,\r\n          name: '推广文案',\r\n          description: '获取推广文案模板',\r\n          icon: '/static/images/text-icon.png'\r\n        },\r\n        {\r\n          id: 6,\r\n          name: '推广活动',\r\n          description: '管理推广营销活动',\r\n          icon: '/static/images/activity-icon.png'\r\n        },\r\n        {\r\n          id: 7,\r\n          name: '素材库',\r\n          description: '分销推广素材管理',\r\n          icon: '/static/images/materials-icon.png'\r\n        }\r\n      ]\r\n    }\r\n  },\r\n  methods: {\r\n    goBack() {\r\n      uni.navigateBack();\r\n    },\r\n    \r\n    showHelp() {\r\n      uni.showModal({\r\n        title: '分销系统帮助',\r\n        content: '分销系统是一种营销模式，通过发展分销员推广商品，获取佣金的方式促进销售增长。',\r\n        showCancel: false\r\n      });\r\n    },\r\n    \r\n    formatNumber(number) {\r\n      return number.toFixed(2).replace(/\\B(?=(\\d{3})+(?!\\d))/g, ',');\r\n    },\r\n    \r\n    showDatePicker() {\r\n      // 实现日期选择器\r\n      uni.showToast({\r\n        title: '日期选择功能开发中',\r\n        icon: 'none'\r\n      });\r\n    },\r\n    \r\n    viewAllDistributors() {\r\n      uni.navigateTo({\r\n        url: '/subPackages/merchant-admin-marketing/pages/marketing/distribution/distributors'\r\n      });\r\n    },\r\n    \r\n    viewDistributorDetail(distributor) {\r\n      uni.navigateTo({\r\n        url: `/subPackages/merchant-admin-marketing/pages/marketing/distribution/distributor-detail?id=${distributor.id}`\r\n      });\r\n    },\r\n    \r\n    toggleDistribution(e) {\r\n      this.distributionEnabled = e.detail.value;\r\n      uni.showToast({\r\n        title: this.distributionEnabled ? '分销功能已开启' : '分销功能已关闭',\r\n        icon: 'none'\r\n      });\r\n    },\r\n    \r\n    navigateToSetting(type) {\r\n      const routes = {\r\n        conditions: '/subPackages/merchant-admin-marketing/pages/marketing/distribution/conditions',\r\n        levels: '/subPackages/merchant-admin-marketing/pages/marketing/distribution/levels',\r\n        withdrawal: '/subPackages/merchant-admin-marketing/pages/marketing/distribution/withdrawal',\r\n        agreement: '/subPackages/merchant-admin-marketing/pages/marketing/distribution/agreement'\r\n      };\r\n      \r\n      uni.navigateTo({\r\n        url: routes[type]\r\n      });\r\n    },\r\n    \r\n    editCommissionRules() {\r\n      uni.navigateTo({\r\n        url: '/subPackages/merchant-admin-marketing/pages/marketing/distribution/commission-rules'\r\n      });\r\n    },\r\n    \r\n    usePromotionTool(tool) {\r\n      // 根据工具ID跳转到不同页面\r\n      let url = '';\r\n      \r\n      switch(tool.id) {\r\n        case 1: // 推广海报\r\n          url = '/subPackages/merchant-admin-marketing/pages/marketing/distribution/promotion-tool?id=1&name=推广海报';\r\n          break;\r\n        case 2: // 推广二维码\r\n          url = '/subPackages/merchant-admin-marketing/pages/marketing/distribution/qrcode';\r\n          break;\r\n        case 3: // 商品推广卡片\r\n          url = '/subPackages/merchant-admin-marketing/pages/marketing/distribution/product-cards';\r\n          break;\r\n        case 4: // 分销员渠道\r\n          url = '/subPackages/merchant-admin-marketing/pages/marketing/distribution/channels';\r\n          break;\r\n        case 5: // 推广文案\r\n          url = '/subPackages/merchant-admin-marketing/pages/marketing/distribution/promotion-text';\r\n          break;\r\n        case 6: // 推广活动\r\n          url = '/subPackages/merchant-admin-marketing/pages/marketing/distribution/promotion-activity';\r\n          break;\r\n        case 7: // 素材库\r\n          url = '/subPackages/merchant-admin-marketing/pages/marketing/distribution/promotion-materials';\r\n          break;\r\n        default:\r\n          url = `/subPackages/merchant-admin-marketing/pages/marketing/distribution/promotion-tool?id=${tool.id}&name=${tool.name}`;\r\n      }\r\n      \r\n      uni.navigateTo({\r\n        url: url\r\n      });\r\n    },\r\n    \r\n    showActionMenu() {\r\n      uni.showActionSheet({\r\n        itemList: ['新增分销员', '导出分销数据', '佣金发放', '分销活动创建'],\r\n        success: (res) => {\r\n          const actions = [\r\n            () => this.addDistributor(),\r\n            () => this.exportData(),\r\n            () => this.payCommission(),\r\n            () => this.createActivity()\r\n          ];\r\n          \r\n          actions[res.tapIndex]();\r\n        }\r\n      });\r\n    },\r\n    \r\n    addDistributor() {\r\n      uni.navigateTo({\r\n        url: '/subPackages/merchant-admin-marketing/pages/marketing/distribution/add-distributor'\r\n      });\r\n    },\r\n    \r\n    exportData() {\r\n      uni.showLoading({\r\n        title: '导出中...'\r\n      });\r\n      \r\n      setTimeout(() => {\r\n        uni.hideLoading();\r\n        uni.showToast({\r\n          title: '数据导出成功',\r\n          icon: 'success'\r\n        });\r\n      }, 1500);\r\n    },\r\n    \r\n    payCommission() {\r\n      uni.navigateTo({\r\n        url: '/subPackages/merchant-admin-marketing/pages/marketing/distribution/pay-commission'\r\n      });\r\n    },\r\n    \r\n    createActivity() {\r\n      uni.navigateTo({\r\n        url: '/subPackages/merchant-admin-marketing/pages/marketing/distribution/create-activity'\r\n      });\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.distribution-container {\r\n  min-height: 100vh;\r\n  background-color: #F5F7FA;\r\n  padding-bottom: env(safe-area-inset-bottom);\r\n}\r\n\r\n/* 导航栏样式 */\r\n.navbar {\r\n  position: sticky;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  background: linear-gradient(135deg, #A764CA, #6B0FBE);\r\n  color: #fff;\r\n  padding: 44px 16px 15px;\r\n  display: flex;\r\n  align-items: center;\r\n  z-index: 100;\r\n  box-shadow: 0 2px 10px rgba(107, 15, 190, 0.15);\r\n}\r\n\r\n.navbar-back {\r\n  width: 36px;\r\n  height: 36px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.back-icon {\r\n  width: 12px;\r\n  height: 12px;\r\n  border-left: 2px solid #fff;\r\n  border-bottom: 2px solid #fff;\r\n  transform: rotate(45deg);\r\n}\r\n\r\n.navbar-title {\r\n  flex: 1;\r\n  text-align: center;\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n  letter-spacing: 0.5px;\r\n}\r\n\r\n.navbar-right {\r\n  width: 36px;\r\n  height: 36px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.help-icon {\r\n  width: 24px;\r\n  height: 24px;\r\n  border-radius: 12px;\r\n  background: rgba(255, 255, 255, 0.2);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  font-size: 14px;\r\n  font-weight: bold;\r\n}\r\n\r\n/* 通用部分样式 */\r\n.section-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.section-title {\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  color: #333;\r\n}\r\n\r\n.view-all {\r\n  font-size: 14px;\r\n  color: #6B0FBE;\r\n}\r\n\r\n.edit-btn {\r\n  background: rgba(107, 15, 190, 0.1);\r\n  color: #6B0FBE;\r\n  font-size: 14px;\r\n  padding: 5px 12px;\r\n  border-radius: 15px;\r\n}\r\n\r\n/* 概览部分样式 */\r\n.overview-section {\r\n  margin: 15px;\r\n  background: #fff;\r\n  border-radius: 12px;\r\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);\r\n  padding: 15px;\r\n}\r\n\r\n.overview-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.date-picker {\r\n  display: flex;\r\n  align-items: center;\r\n  background: #F5F7FA;\r\n  border-radius: 15px;\r\n  padding: 5px 10px;\r\n}\r\n\r\n.date-text {\r\n  font-size: 12px;\r\n  color: #666;\r\n  margin-right: 5px;\r\n}\r\n\r\n.date-icon {\r\n  width: 8px;\r\n  height: 8px;\r\n  border-top: 2px solid #666;\r\n  border-right: 2px solid #666;\r\n  transform: rotate(135deg);\r\n}\r\n\r\n/* 统计卡片样式 */\r\n.stats-cards {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  margin: 0 -7.5px;\r\n}\r\n\r\n.stats-card {\r\n  width: 50%;\r\n  padding: 7.5px;\r\n  box-sizing: border-box;\r\n  position: relative;\r\n}\r\n\r\n.card-value {\r\n  font-size: 20px;\r\n  font-weight: 600;\r\n  color: #333;\r\n  margin-bottom: 5px;\r\n  background: #F8FAFC;\r\n  padding: 15px;\r\n  border-radius: 10px;\r\n  border-left: 3px solid #6B0FBE;\r\n}\r\n\r\n.card-label {\r\n  font-size: 12px;\r\n  color: #999;\r\n  position: absolute;\r\n  bottom: 20px;\r\n  left: 25px;\r\n}\r\n\r\n.card-trend {\r\n  position: absolute;\r\n  bottom: 20px;\r\n  right: 25px;\r\n  display: flex;\r\n  align-items: center;\r\n  font-size: 12px;\r\n}\r\n\r\n.card-trend.up {\r\n  color: #34C759;\r\n}\r\n\r\n.card-trend.down {\r\n  color: #FF3B30;\r\n}\r\n\r\n.trend-arrow {\r\n  width: 0;\r\n  height: 0;\r\n  margin-right: 3px;\r\n}\r\n\r\n.card-trend.up .trend-arrow {\r\n  border-left: 4px solid transparent;\r\n  border-right: 4px solid transparent;\r\n  border-bottom: 6px solid #34C759;\r\n}\r\n\r\n.card-trend.down .trend-arrow {\r\n  border-left: 4px solid transparent;\r\n  border-right: 4px solid transparent;\r\n  border-top: 6px solid #FF3B30;\r\n}\r\n\r\n/* 排行榜样式 */\r\n.leaderboard-section {\r\n  margin: 15px;\r\n  background: #fff;\r\n  border-radius: 12px;\r\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);\r\n  padding: 15px;\r\n}\r\n\r\n.leaderboard-list {\r\n  margin-top: 10px;\r\n}\r\n\r\n.leaderboard-item {\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 12px 0;\r\n  border-bottom: 1px solid #f0f0f0;\r\n}\r\n\r\n.leaderboard-item:last-child {\r\n  border-bottom: none;\r\n}\r\n\r\n.rank-badge {\r\n  width: 24px;\r\n  height: 24px;\r\n  background: #F5F7FA;\r\n  border-radius: 12px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  font-size: 12px;\r\n  font-weight: bold;\r\n  color: #999;\r\n  margin-right: 10px;\r\n}\r\n\r\n.rank-badge.top-rank {\r\n  background: linear-gradient(135deg, #A764CA, #6B0FBE);\r\n  color: #fff;\r\n}\r\n\r\n.distributor-avatar {\r\n  width: 40px;\r\n  height: 40px;\r\n  border-radius: 20px;\r\n  margin-right: 10px;\r\n  background-color: #f5f5f5;\r\n}\r\n\r\n.distributor-info {\r\n  flex: 1;\r\n}\r\n\r\n.distributor-name {\r\n  font-size: 15px;\r\n  color: #333;\r\n  margin-bottom: 3px;\r\n}\r\n\r\n.distributor-level {\r\n  font-size: 12px;\r\n  color: #6B0FBE;\r\n  background: rgba(107, 15, 190, 0.1);\r\n  display: inline-block;\r\n  padding: 2px 8px;\r\n  border-radius: 10px;\r\n}\r\n\r\n.distributor-stats {\r\n  display: flex;\r\n  margin-left: 10px;\r\n}\r\n\r\n.stat-item {\r\n  margin-left: 15px;\r\n  text-align: right;\r\n}\r\n\r\n.stat-value {\r\n  display: block;\r\n  font-size: 14px;\r\n  font-weight: 600;\r\n  color: #333;\r\n  margin-bottom: 2px;\r\n}\r\n\r\n.stat-label {\r\n  font-size: 12px;\r\n  color: #999;\r\n}\r\n\r\n/* 设置部分样式 */\r\n.settings-section {\r\n  margin: 15px;\r\n  background: #fff;\r\n  border-radius: 12px;\r\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);\r\n  padding: 15px;\r\n}\r\n\r\n.switch-container {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.switch-label {\r\n  font-size: 14px;\r\n  color: #666;\r\n  margin-right: 10px;\r\n}\r\n\r\n.settings-list {\r\n  margin-top: 10px;\r\n}\r\n\r\n.settings-item {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 15px 0;\r\n  border-bottom: 1px solid #f0f0f0;\r\n}\r\n\r\n.settings-item:last-child {\r\n  border-bottom: none;\r\n}\r\n\r\n.item-left {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.item-icon {\r\n  width: 36px;\r\n  height: 36px;\r\n  border-radius: 18px;\r\n  margin-right: 10px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  position: relative;\r\n}\r\n\r\n.item-icon.conditions {\r\n  background: rgba(255, 149, 0, 0.1);\r\n}\r\n\r\n.item-icon.conditions::before {\r\n  content: '';\r\n  width: 16px;\r\n  height: 16px;\r\n  border: 2px solid #FF9500;\r\n  border-radius: 10px;\r\n  position: absolute;\r\n}\r\n\r\n.item-icon.levels {\r\n  background: rgba(0, 122, 255, 0.1);\r\n}\r\n\r\n.item-icon.levels::before {\r\n  content: '';\r\n  width: 18px;\r\n  height: 4px;\r\n  background: #007AFF;\r\n  position: absolute;\r\n  top: 12px;\r\n}\r\n\r\n.item-icon.levels::after {\r\n  content: '';\r\n  width: 18px;\r\n  height: 4px;\r\n  background: #007AFF;\r\n  position: absolute;\r\n  top: 20px;\r\n}\r\n\r\n.item-icon.withdrawal {\r\n  background: rgba(52, 199, 89, 0.1);\r\n}\r\n\r\n.item-icon.withdrawal::before {\r\n  content: '';\r\n  width: 16px;\r\n  height: 10px;\r\n  border: 2px solid #34C759;\r\n  border-radius: 2px;\r\n  position: absolute;\r\n}\r\n\r\n.item-icon.agreement {\r\n  background: rgba(88, 86, 214, 0.1);\r\n}\r\n\r\n.item-icon.agreement::before {\r\n  content: '';\r\n  width: 14px;\r\n  height: 18px;\r\n  border: 2px solid #5856D6;\r\n  border-radius: 2px;\r\n  position: absolute;\r\n}\r\n\r\n.item-icon.agreement::after {\r\n  content: '';\r\n  width: 10px;\r\n  height: 2px;\r\n  background: #5856D6;\r\n  position: absolute;\r\n  top: 14px;\r\n}\r\n\r\n.item-title {\r\n  font-size: 15px;\r\n  color: #333;\r\n}\r\n\r\n.item-right {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.item-value {\r\n  font-size: 14px;\r\n  color: #999;\r\n  margin-right: 10px;\r\n}\r\n\r\n.item-arrow {\r\n  width: 8px;\r\n  height: 8px;\r\n  border-top: 1.5px solid #999;\r\n  border-right: 1.5px solid #999;\r\n  transform: rotate(45deg);\r\n}\r\n\r\n/* 佣金规则样式 */\r\n.commission-section {\r\n  margin: 15px;\r\n  background: #fff;\r\n  border-radius: 12px;\r\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);\r\n  padding: 15px;\r\n}\r\n\r\n.commission-rules {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  margin: 0 -7.5px;\r\n}\r\n\r\n.rule-card {\r\n  width: calc(100% / 3);\r\n  padding: 7.5px;\r\n  box-sizing: border-box;\r\n}\r\n\r\n.rule-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 5px;\r\n}\r\n\r\n.rule-title {\r\n  font-size: 14px;\r\n  font-weight: 500;\r\n  color: #333;\r\n}\r\n\r\n.rule-value {\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  color: #6B0FBE;\r\n}\r\n\r\n.rule-desc {\r\n  font-size: 12px;\r\n  color: #999;\r\n  height: 32px;\r\n}\r\n\r\n/* 推广工具样式 */\r\n.promotion-section {\r\n  margin: 15px;\r\n  background: #fff;\r\n  border-radius: 12px;\r\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);\r\n  padding: 15px;\r\n}\r\n\r\n.promotion-tools {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  margin: 0 -7.5px;\r\n}\r\n\r\n.tool-card {\r\n  width: 25%;\r\n  padding: 7.5px;\r\n  box-sizing: border-box;\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n}\r\n\r\n.tool-icon {\r\n  width: 50px;\r\n  height: 50px;\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.tool-name {\r\n  font-size: 14px;\r\n  color: #333;\r\n  margin-bottom: 3px;\r\n  text-align: center;\r\n}\r\n\r\n.tool-desc {\r\n  font-size: 10px;\r\n  color: #999;\r\n  text-align: center;\r\n  height: 28px;\r\n  overflow: hidden;\r\n  display: -webkit-box;\r\n  -webkit-line-clamp: 2;\r\n  -webkit-box-orient: vertical;\r\n}\r\n\r\n/* 浮动操作按钮 */\r\n.floating-action-button {\r\n  position: fixed;\r\n  bottom: 30px;\r\n  right: 30px;\r\n  width: 56px;\r\n  height: 56px;\r\n  border-radius: 28px;\r\n  background: linear-gradient(135deg, #A764CA, #6B0FBE);\r\n  box-shadow: 0 4px 15px rgba(107, 15, 190, 0.3);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  z-index: 100;\r\n}\r\n\r\n.fab-icon {\r\n  font-size: 28px;\r\n  color: #fff;\r\n  font-weight: 300;\r\n  line-height: 1;\r\n  margin-top: -2px;\r\n}\r\n\r\n/* 响应式调整 */\r\n@media screen and (max-width: 375px) {\r\n  .rule-card {\r\n    width: 50%;\r\n  }\r\n  \r\n  .tool-card {\r\n    width: 33.33%;\r\n  }\r\n}\r\n\r\n@media screen and (max-width: 320px) {\r\n  .stats-card {\r\n    width: 100%;\r\n  }\r\n  \r\n  .tool-card {\r\n    width: 50%;\r\n  }\r\n}\r\n</style> ", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/subPackages/merchant-admin-marketing/pages/marketing/distribution/index.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni"], "mappings": ";;AA4MA,MAAK,YAAU;AAAA,EACb,OAAO;AACL,WAAO;AAAA,MACL,WAAW;AAAA,MACX,qBAAqB;AAAA;AAAA,MAGrB,kBAAkB;AAAA,QAChB,mBAAmB;AAAA,QACnB,mBAAmB;AAAA,QACnB,oBAAoB;AAAA,QAEpB,iBAAiB;AAAA,QACjB,iBAAiB;AAAA,QACjB,kBAAkB;AAAA,QAElB,aAAa;AAAA,QACb,aAAa;AAAA,QACb,cAAc;AAAA,QAEd,mBAAmB;AAAA,QACnB,cAAc;AAAA,QACd,eAAe;AAAA,MAChB;AAAA;AAAA,MAGD,iBAAiB;AAAA,QACf;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,QAAQ;AAAA,UACR,OAAO;AAAA,UACP,YAAY;AAAA,UACZ,QAAQ;AAAA,QACT;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,QAAQ;AAAA,UACR,OAAO;AAAA,UACP,YAAY;AAAA,UACZ,QAAQ;AAAA,QACT;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,QAAQ;AAAA,UACR,OAAO;AAAA,UACP,YAAY;AAAA,UACZ,QAAQ;AAAA,QACT;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,QAAQ;AAAA,UACR,OAAO;AAAA,UACP,YAAY;AAAA,UACZ,QAAQ;AAAA,QACT;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,QAAQ;AAAA,UACR,OAAO;AAAA,UACP,YAAY;AAAA,UACZ,QAAQ;AAAA,QACV;AAAA,MACD;AAAA;AAAA,MAGD,sBAAsB;AAAA,QACpB,eAAe;AAAA,QACf,YAAY;AAAA,QACZ,eAAe;AAAA,MAChB;AAAA;AAAA,MAGD,iBAAiB;AAAA,QACf,QAAQ;AAAA,QACR,QAAQ;AAAA,QACR,QAAQ;AAAA,MACT;AAAA;AAAA,MAGD,gBAAgB;AAAA,QACd;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,aAAa;AAAA,UACb,MAAM;AAAA,QACP;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,aAAa;AAAA,UACb,MAAM;AAAA,QACP;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,aAAa;AAAA,UACb,MAAM;AAAA,QACP;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,aAAa;AAAA,UACb,MAAM;AAAA,QACP;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,aAAa;AAAA,UACb,MAAM;AAAA,QACP;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,aAAa;AAAA,UACb,MAAM;AAAA,QACP;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,aAAa;AAAA,UACb,MAAM;AAAA,QACR;AAAA,MACF;AAAA,IACF;AAAA,EACD;AAAA,EACD,SAAS;AAAA,IACP,SAAS;AACPA,oBAAG,MAAC,aAAY;AAAA,IACjB;AAAA,IAED,WAAW;AACTA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,SAAS;AAAA,QACT,YAAY;AAAA,MACd,CAAC;AAAA,IACF;AAAA,IAED,aAAa,QAAQ;AACnB,aAAO,OAAO,QAAQ,CAAC,EAAE,QAAQ,yBAAyB,GAAG;AAAA,IAC9D;AAAA,IAED,iBAAiB;AAEfA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,MACR,CAAC;AAAA,IACF;AAAA,IAED,sBAAsB;AACpBA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK;AAAA,MACP,CAAC;AAAA,IACF;AAAA,IAED,sBAAsB,aAAa;AACjCA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,4FAA4F,YAAY,EAAE;AAAA,MACjH,CAAC;AAAA,IACF;AAAA,IAED,mBAAmB,GAAG;AACpB,WAAK,sBAAsB,EAAE,OAAO;AACpCA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO,KAAK,sBAAsB,YAAY;AAAA,QAC9C,MAAM;AAAA,MACR,CAAC;AAAA,IACF;AAAA,IAED,kBAAkB,MAAM;AACtB,YAAM,SAAS;AAAA,QACb,YAAY;AAAA,QACZ,QAAQ;AAAA,QACR,YAAY;AAAA,QACZ,WAAW;AAAA;AAGbA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,OAAO,IAAI;AAAA,MAClB,CAAC;AAAA,IACF;AAAA,IAED,sBAAsB;AACpBA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK;AAAA,MACP,CAAC;AAAA,IACF;AAAA,IAED,iBAAiB,MAAM;AAErB,UAAI,MAAM;AAEV,cAAO,KAAK,IAAE;AAAA,QACZ,KAAK;AACH,gBAAM;AACN;AAAA,QACF,KAAK;AACH,gBAAM;AACN;AAAA,QACF,KAAK;AACH,gBAAM;AACN;AAAA,QACF,KAAK;AACH,gBAAM;AACN;AAAA,QACF,KAAK;AACH,gBAAM;AACN;AAAA,QACF,KAAK;AACH,gBAAM;AACN;AAAA,QACF,KAAK;AACH,gBAAM;AACN;AAAA,QACF;AACE,gBAAM,wFAAwF,KAAK,EAAE,SAAS,KAAK,IAAI;AAAA,MAC3H;AAEAA,oBAAAA,MAAI,WAAW;AAAA,QACb;AAAA,MACF,CAAC;AAAA,IACF;AAAA,IAED,iBAAiB;AACfA,oBAAAA,MAAI,gBAAgB;AAAA,QAClB,UAAU,CAAC,SAAS,UAAU,QAAQ,QAAQ;AAAA,QAC9C,SAAS,CAAC,QAAQ;AAChB,gBAAM,UAAU;AAAA,YACd,MAAM,KAAK,eAAgB;AAAA,YAC3B,MAAM,KAAK,WAAY;AAAA,YACvB,MAAM,KAAK,cAAe;AAAA,YAC1B,MAAM,KAAK,eAAe;AAAA;AAG5B,kBAAQ,IAAI,QAAQ;QACtB;AAAA,MACF,CAAC;AAAA,IACF;AAAA,IAED,iBAAiB;AACfA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK;AAAA,MACP,CAAC;AAAA,IACF;AAAA,IAED,aAAa;AACXA,oBAAAA,MAAI,YAAY;AAAA,QACd,OAAO;AAAA,MACT,CAAC;AAED,iBAAW,MAAM;AACfA,sBAAG,MAAC,YAAW;AACfA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACR,CAAC;AAAA,MACF,GAAE,IAAI;AAAA,IACR;AAAA,IAED,gBAAgB;AACdA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK;AAAA,MACP,CAAC;AAAA,IACF;AAAA,IAED,iBAAiB;AACfA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK;AAAA,MACP,CAAC;AAAA,IACH;AAAA,EACF;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACheA,GAAG,WAAW,eAAe;"}