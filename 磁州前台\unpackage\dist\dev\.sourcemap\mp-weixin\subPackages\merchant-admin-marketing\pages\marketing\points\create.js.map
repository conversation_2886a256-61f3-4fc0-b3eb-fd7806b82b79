{"version": 3, "file": "create.js", "sources": ["subPackages/merchant-admin-marketing/pages/marketing/points/create.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/c3ViUGFja2FnZXNcbWVyY2hhbnQtYWRtaW4tbWFya2V0aW5nXHBhZ2VzXG1hcmtldGluZ1xwb2ludHNcY3JlYXRlLnZ1ZQ"], "sourcesContent": ["<template>\r\n  <view class=\"points-create-container\">\r\n    <!-- 自定义导航栏 -->\r\n    <view class=\"navbar\">\r\n      <view class=\"navbar-left\">\r\n        <view class=\"back-button\" @tap=\"goBack\">\r\n          <svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n            <path d=\"M15 18L9 12L15 6\" stroke=\"white\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\r\n          </svg>\r\n        </view>\r\n      </view>\r\n      <view class=\"navbar-title\">\r\n        <text class=\"title-text\">创建积分商品</text>\r\n      </view>\r\n      <view class=\"navbar-right\">\r\n        <!-- 占位 -->\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 页面内容区域 -->\r\n    <scroll-view scroll-y class=\"content-area\">\r\n      <!-- 表单区域 -->\r\n      <view class=\"form-section\">\r\n        <!-- 基本信息 -->\r\n        <view class=\"form-group\">\r\n          <view class=\"form-title\">基本信息</view>\r\n          \r\n          <view class=\"form-item\">\r\n            <text class=\"form-label\">商品名称</text>\r\n            <input class=\"form-input\" v-model=\"formData.name\" placeholder=\"请输入积分商品名称\" />\r\n          </view>\r\n          \r\n          <view class=\"form-item\">\r\n            <text class=\"form-label\">所需积分</text>\r\n            <input class=\"form-input\" v-model=\"formData.points\" type=\"number\" placeholder=\"请输入兑换所需积分\" />\r\n          </view>\r\n          \r\n          <view class=\"form-item\">\r\n            <text class=\"form-label\">库存数量</text>\r\n            <input class=\"form-input\" v-model=\"formData.stock\" type=\"number\" placeholder=\"请输入商品库存\" />\r\n          </view>\r\n        </view>\r\n        \r\n        <!-- 商品图片 -->\r\n        <view class=\"form-group\">\r\n          <view class=\"form-title\">商品图片</view>\r\n          <view class=\"image-uploader\">\r\n            <view class=\"upload-box\" @tap=\"chooseImage\" v-if=\"!formData.image\">\r\n              <svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n                <path d=\"M12 5V19M5 12H19\" stroke=\"#999999\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\r\n              </svg>\r\n              <text class=\"upload-text\">上传图片</text>\r\n            </view>\r\n            <view class=\"image-preview\" v-else>\r\n              <image class=\"preview-image\" :src=\"formData.image\" mode=\"aspectFill\"></image>\r\n              <view class=\"delete-image\" @tap=\"deleteImage\">\r\n                <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n                  <path d=\"M18 6L6 18M6 6L18 18\" stroke=\"white\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\r\n                </svg>\r\n              </view>\r\n            </view>\r\n          </view>\r\n        </view>\r\n        \r\n        <!-- 有效期 -->\r\n        <view class=\"form-group\">\r\n          <view class=\"form-title\">有效期</view>\r\n          \r\n          <view class=\"form-item\">\r\n            <text class=\"form-label\">开始时间</text>\r\n            <picker mode=\"date\" :value=\"formData.startDate\" @change=\"onStartDateChange\" class=\"date-picker\">\r\n              <view class=\"picker-value\">{{formData.startDate || '请选择开始日期'}}</view>\r\n            </picker>\r\n          </view>\r\n          \r\n          <view class=\"form-item\">\r\n            <text class=\"form-label\">结束时间</text>\r\n            <picker mode=\"date\" :value=\"formData.endDate\" @change=\"onEndDateChange\" class=\"date-picker\">\r\n              <view class=\"picker-value\">{{formData.endDate || '请选择结束日期'}}</view>\r\n            </picker>\r\n          </view>\r\n        </view>\r\n        \r\n        <!-- 商品描述 -->\r\n        <view class=\"form-group\">\r\n          <view class=\"form-title\">商品描述</view>\r\n          <textarea class=\"form-textarea\" v-model=\"formData.description\" placeholder=\"请输入商品描述信息\" />\r\n        </view>\r\n        \r\n        <!-- 兑换规则 -->\r\n        <view class=\"form-group\">\r\n          <view class=\"form-title\">兑换规则</view>\r\n          <textarea class=\"form-textarea\" v-model=\"formData.rules\" placeholder=\"请输入兑换规则\" />\r\n        </view>\r\n      </view>\r\n      \r\n      <!-- 底部空间 -->\r\n      <view class=\"bottom-space\"></view>\r\n    </scroll-view>\r\n    \r\n    <!-- 底部操作栏 -->\r\n    <view class=\"bottom-bar\">\r\n      <view class=\"action-button save-draft\" @tap=\"saveDraft\">保存草稿</view>\r\n      <view class=\"action-button publish\" @tap=\"publishItem\">立即发布</view>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script setup>\r\nimport { ref, reactive } from 'vue';\r\n\r\n// 表单数据\r\nconst formData = reactive({\r\n  name: '',\r\n  points: '',\r\n  stock: '',\r\n  image: '',\r\n  startDate: '',\r\n  endDate: '',\r\n  description: '',\r\n  rules: ''\r\n});\r\n\r\n// 选择图片\r\nconst chooseImage = () => {\r\n  uni.chooseImage({\r\n    count: 1,\r\n    sizeType: ['compressed'],\r\n    sourceType: ['album', 'camera'],\r\n    success: (res) => {\r\n      formData.image = res.tempFilePaths[0];\r\n    }\r\n  });\r\n};\r\n\r\n// 删除图片\r\nconst deleteImage = () => {\r\n  formData.image = '';\r\n};\r\n\r\n// 日期选择\r\nconst onStartDateChange = (e) => {\r\n  formData.startDate = e.detail.value;\r\n};\r\n\r\nconst onEndDateChange = (e) => {\r\n  formData.endDate = e.detail.value;\r\n};\r\n\r\n// 保存草稿\r\nconst saveDraft = () => {\r\n  if (!formData.name) {\r\n    uni.showToast({\r\n      title: '请输入商品名称',\r\n      icon: 'none'\r\n    });\r\n    return;\r\n  }\r\n  \r\n  uni.showToast({\r\n    title: '草稿保存成功',\r\n    icon: 'success'\r\n  });\r\n  \r\n  setTimeout(() => {\r\n    goBack();\r\n  }, 1500);\r\n};\r\n\r\n// 发布商品\r\nconst publishItem = () => {\r\n  // 表单验证\r\n  if (!formData.name) {\r\n    uni.showToast({\r\n      title: '请输入商品名称',\r\n      icon: 'none'\r\n    });\r\n    return;\r\n  }\r\n  \r\n  if (!formData.points) {\r\n    uni.showToast({\r\n      title: '请输入所需积分',\r\n      icon: 'none'\r\n    });\r\n    return;\r\n  }\r\n  \r\n  if (!formData.stock) {\r\n    uni.showToast({\r\n      title: '请输入库存数量',\r\n      icon: 'none'\r\n    });\r\n    return;\r\n  }\r\n  \r\n  if (!formData.image) {\r\n    uni.showToast({\r\n      title: '请上传商品图片',\r\n      icon: 'none'\r\n    });\r\n    return;\r\n  }\r\n  \r\n  if (!formData.startDate || !formData.endDate) {\r\n    uni.showToast({\r\n      title: '请设置有效期',\r\n      icon: 'none'\r\n    });\r\n    return;\r\n  }\r\n  \r\n  // 提交表单\r\n  uni.showLoading({\r\n    title: '正在发布...'\r\n  });\r\n  \r\n  // 模拟API请求\r\n  setTimeout(() => {\r\n    uni.hideLoading();\r\n    uni.showToast({\r\n      title: '发布成功',\r\n      icon: 'success'\r\n    });\r\n    \r\n    setTimeout(() => {\r\n      goBack();\r\n    }, 1500);\r\n  }, 2000);\r\n};\r\n\r\n// 返回上一页\r\nconst goBack = () => {\r\n  uni.navigateBack();\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n/* 页面容器 */\r\n.points-create-container {\r\n  display: flex;\r\n  flex-direction: column;\r\n  min-height: 100vh;\r\n  background-color: #F5F7FA;\r\n  position: relative;\r\n}\r\n\r\n/* 导航栏样式 */\r\n.navbar {\r\n  position: sticky;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  background: linear-gradient(135deg, #FF7600, #FF3C00);\r\n  color: white;\r\n  padding: 48px 20px 16px;\r\n  display: flex;\r\n  align-items: center;\r\n  z-index: 100;\r\n  box-shadow: 0 2px 8px rgba(255, 120, 0, 0.15);\r\n}\r\n\r\n.navbar-left {\r\n  width: 40px;\r\n}\r\n\r\n.back-button {\r\n  width: 36px;\r\n  height: 36px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.navbar-title {\r\n  flex: 1;\r\n  text-align: center;\r\n}\r\n\r\n.title-text {\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n}\r\n\r\n.navbar-right {\r\n  width: 40px;\r\n}\r\n\r\n/* 内容区域 */\r\n.content-area {\r\n  flex: 1;\r\n  padding: 16px;\r\n  box-sizing: border-box;\r\n  height: calc(100vh - 140px); /* 减去导航栏和底部操作栏的高度 */\r\n}\r\n\r\n/* 表单区域 */\r\n.form-section {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.form-group {\r\n  background-color: #FFFFFF;\r\n  border-radius: 12px;\r\n  padding: 20px;\r\n  margin-bottom: 16px;\r\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n.form-title {\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  color: #333333;\r\n  margin-bottom: 16px;\r\n}\r\n\r\n.form-item {\r\n  margin-bottom: 16px;\r\n}\r\n\r\n.form-label {\r\n  font-size: 14px;\r\n  color: #666666;\r\n  display: block;\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.form-input {\r\n  width: 100%;\r\n  height: 44px;\r\n  background-color: #F5F7FA;\r\n  border-radius: 8px;\r\n  padding: 0 12px;\r\n  box-sizing: border-box;\r\n  font-size: 14px;\r\n  color: #333333;\r\n}\r\n\r\n.form-textarea {\r\n  width: 100%;\r\n  height: 120px;\r\n  background-color: #F5F7FA;\r\n  border-radius: 8px;\r\n  padding: 12px;\r\n  box-sizing: border-box;\r\n  font-size: 14px;\r\n  color: #333333;\r\n}\r\n\r\n/* 图片上传 */\r\n.image-uploader {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.upload-box {\r\n  width: 120px;\r\n  height: 120px;\r\n  background-color: #F5F7FA;\r\n  border-radius: 8px;\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.upload-text {\r\n  font-size: 14px;\r\n  color: #999999;\r\n  margin-top: 8px;\r\n}\r\n\r\n.image-preview {\r\n  position: relative;\r\n  width: 120px;\r\n  height: 120px;\r\n  border-radius: 8px;\r\n  overflow: hidden;\r\n}\r\n\r\n.preview-image {\r\n  width: 100%;\r\n  height: 100%;\r\n  object-fit: cover;\r\n}\r\n\r\n.delete-image {\r\n  position: absolute;\r\n  top: 4px;\r\n  right: 4px;\r\n  width: 24px;\r\n  height: 24px;\r\n  border-radius: 12px;\r\n  background-color: rgba(0, 0, 0, 0.5);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n/* 日期选择器 */\r\n.date-picker {\r\n  width: 100%;\r\n  height: 44px;\r\n  background-color: #F5F7FA;\r\n  border-radius: 8px;\r\n  padding: 0 12px;\r\n  box-sizing: border-box;\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.picker-value {\r\n  font-size: 14px;\r\n  color: #333333;\r\n}\r\n\r\n/* 底部操作栏 */\r\n.bottom-bar {\r\n  position: fixed;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  height: 60px;\r\n  background-color: #FFFFFF;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n  padding: 0 16px;\r\n  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);\r\n  z-index: 99;\r\n}\r\n\r\n.action-button {\r\n  flex: 1;\r\n  height: 44px;\r\n  border-radius: 22px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  font-size: 16px;\r\n  font-weight: 500;\r\n}\r\n\r\n.save-draft {\r\n  background-color: #F5F7FA;\r\n  color: #666666;\r\n  margin-right: 12px;\r\n}\r\n\r\n.publish {\r\n  background: linear-gradient(135deg, #FF7600, #FF3C00);\r\n  color: white;\r\n}\r\n\r\n/* 底部空间 */\r\n.bottom-space {\r\n  height: 60px;\r\n}\r\n</style> ", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/subPackages/merchant-admin-marketing/pages/marketing/points/create.vue'\nwx.createPage(MiniProgramPage)"], "names": ["reactive", "uni", "MiniProgramPage"], "mappings": ";;;;;;;;;;AAgHA,UAAM,WAAWA,cAAAA,SAAS;AAAA,MACxB,MAAM;AAAA,MACN,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,OAAO;AAAA,MACP,WAAW;AAAA,MACX,SAAS;AAAA,MACT,aAAa;AAAA,MACb,OAAO;AAAA,IACT,CAAC;AAGD,UAAM,cAAc,MAAM;AACxBC,oBAAAA,MAAI,YAAY;AAAA,QACd,OAAO;AAAA,QACP,UAAU,CAAC,YAAY;AAAA,QACvB,YAAY,CAAC,SAAS,QAAQ;AAAA,QAC9B,SAAS,CAAC,QAAQ;AAChB,mBAAS,QAAQ,IAAI,cAAc,CAAC;AAAA,QACrC;AAAA,MACL,CAAG;AAAA,IACH;AAGA,UAAM,cAAc,MAAM;AACxB,eAAS,QAAQ;AAAA,IACnB;AAGA,UAAM,oBAAoB,CAAC,MAAM;AAC/B,eAAS,YAAY,EAAE,OAAO;AAAA,IAChC;AAEA,UAAM,kBAAkB,CAAC,MAAM;AAC7B,eAAS,UAAU,EAAE,OAAO;AAAA,IAC9B;AAGA,UAAM,YAAY,MAAM;AACtB,UAAI,CAAC,SAAS,MAAM;AAClBA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AACD;AAAA,MACD;AAEDA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,MACV,CAAG;AAED,iBAAW,MAAM;AACf;MACD,GAAE,IAAI;AAAA,IACT;AAGA,UAAM,cAAc,MAAM;AAExB,UAAI,CAAC,SAAS,MAAM;AAClBA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AACD;AAAA,MACD;AAED,UAAI,CAAC,SAAS,QAAQ;AACpBA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AACD;AAAA,MACD;AAED,UAAI,CAAC,SAAS,OAAO;AACnBA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AACD;AAAA,MACD;AAED,UAAI,CAAC,SAAS,OAAO;AACnBA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AACD;AAAA,MACD;AAED,UAAI,CAAC,SAAS,aAAa,CAAC,SAAS,SAAS;AAC5CA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AACD;AAAA,MACD;AAGDA,oBAAAA,MAAI,YAAY;AAAA,QACd,OAAO;AAAA,MACX,CAAG;AAGD,iBAAW,MAAM;AACfA,sBAAG,MAAC,YAAW;AACfA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AAED,mBAAW,MAAM;AACf;QACD,GAAE,IAAI;AAAA,MACR,GAAE,GAAI;AAAA,IACT;AAGA,UAAM,SAAS,MAAM;AACnBA,oBAAG,MAAC,aAAY;AAAA,IAClB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACzOA,GAAG,WAAWC,SAAe;"}