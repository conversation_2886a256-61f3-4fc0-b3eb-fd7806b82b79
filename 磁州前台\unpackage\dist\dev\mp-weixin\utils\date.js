"use strict";
function formatDate(date, format = "YYYY-MM-DD") {
  if (!date)
    return "";
  const d = new Date(date);
  if (isNaN(d.getTime()))
    return "";
  const year = d.getFullYear();
  const month = d.getMonth() + 1;
  const day = d.getDate();
  const hour = d.getHours();
  const minute = d.getMinutes();
  const second = d.getSeconds();
  return format.replace("YYYY", year).replace("MM", month.toString().padStart(2, "0")).replace("DD", day.toString().padStart(2, "0")).replace("HH", hour.toString().padStart(2, "0")).replace("mm", minute.toString().padStart(2, "0")).replace("ss", second.toString().padStart(2, "0"));
}
function formatTime(date) {
  if (!date)
    return "";
  const d = new Date(date);
  if (isNaN(d.getTime()))
    return "";
  return formatDate(d, "YYYY-MM-DD HH:mm");
}
exports.formatDate = formatDate;
exports.formatTime = formatTime;
//# sourceMappingURL=../../.sourcemap/mp-weixin/utils/date.js.map
