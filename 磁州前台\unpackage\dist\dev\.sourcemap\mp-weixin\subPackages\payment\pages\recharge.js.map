{"version": 3, "file": "recharge.js", "sources": ["subPackages/payment/pages/recharge.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/c3ViUGFja2FnZXNccGF5bWVudFxwYWdlc1xyZWNoYXJnZS52dWU"], "sourcesContent": ["<template>\r\n  <view class=\"recharge-container\">\r\n    <!-- 自定义导航栏 -->\r\n    <view class=\"custom-navbar\" :style=\"{ paddingTop: statusBarHeight + 'px' }\">\r\n      <view class=\"navbar-left\" @click=\"goBack\">\r\n        <image src=\"/static/images/tabbar/返回键.png\" class=\"back-icon\"></image>\r\n      </view>\r\n      <view class=\"navbar-title\">充值</view>\r\n      <view class=\"navbar-right\"></view>\r\n    </view>\r\n    \r\n    <!-- 充值金额区域 -->\r\n    <view class=\"recharge-section\" :style=\"{ marginTop: (navbarHeight + 10) + 'px' }\">\r\n      <view class=\"section-title\">充值金额</view>\r\n      <view class=\"amount-input-area\">\r\n        <text class=\"currency-symbol\">¥</text>\r\n        <input \r\n          class=\"amount-input\" \r\n          type=\"digit\" \r\n          v-model=\"amount\" \r\n          placeholder=\"0.00\"\r\n          @input=\"handleAmountInput\"\r\n          focus\r\n        />\r\n      </view>\r\n      <view class=\"amount-quick-select\">\r\n        <view \r\n          class=\"quick-amount-item\" \r\n          v-for=\"(item, index) in quickAmounts\" \r\n          :key=\"index\"\r\n          :class=\"{'selected': amount === item.toString()}\"\r\n          @click=\"selectAmount(item)\"\r\n        >\r\n          {{item}}元\r\n        </view>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 充值方式 -->\r\n    <view class=\"payment-method\">\r\n      <view class=\"section-title\">充值方式</view>\r\n      <view class=\"payment-options\">\r\n        <view \r\n          class=\"payment-option\" \r\n          v-for=\"(item, index) in paymentMethods\" \r\n          :key=\"index\"\r\n          :class=\"{'selected': selectedPayment === item.id}\"\r\n          @click=\"selectPayment(item.id)\"\r\n        >\r\n          <view class=\"payment-left\">\r\n            <image :src=\"item.icon\" class=\"payment-icon\"></image>\r\n            <text class=\"payment-name\">{{ item.name }}</text>\r\n          </view>\r\n          <view class=\"payment-right\">\r\n            <view class=\"payment-check\" v-if=\"selectedPayment === item.id\">\r\n              <image src=\"/static/images/tabbar/选中.png\" class=\"check-icon\"></image>\r\n            </view>\r\n          </view>\r\n        </view>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 充值说明 -->\r\n    <view class=\"recharge-notice\">\r\n      <view class=\"notice-title\">充值说明</view>\r\n      <view class=\"notice-item\">1. 充值金额最低1元</view>\r\n      <view class=\"notice-item\">2. 充值成功后，余额将立即到账</view>\r\n      <view class=\"notice-item\">3. 如遇到充值问题，请联系客服</view>\r\n    </view>\r\n    \r\n    <!-- 充值按钮 -->\r\n    <view class=\"bottom-btn-area\">\r\n      <button \r\n        class=\"recharge-btn\" \r\n        :disabled=\"!canRecharge\" \r\n        :class=\"{'btn-disabled': !canRecharge}\"\r\n        @click=\"submitRecharge\"\r\n      >\r\n        确认充值\r\n      </button>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script setup>\r\nimport { ref, computed, onMounted } from 'vue';\r\nimport { smartNavigate } from '@/utils/navigation.js';\r\n\r\n// 响应式状态\r\nconst statusBarHeight = ref(20);\r\nconst navbarHeight = ref(64);\r\nconst amount = ref('');\r\nconst selectedPayment = ref('wxpay');\r\nconst quickAmounts = ref([10, 50, 100, 200, 500]);\r\nconst paymentMethods = ref([\r\n  {\r\n    id: 'wxpay',\r\n    name: '微信支付',\r\n    icon: '/static/images/payment/wxpay.png'\r\n  },\r\n  {\r\n    id: 'alipay',\r\n    name: '支付宝支付',\r\n    icon: '/static/images/payment/alipay.png'\r\n  }\r\n]);\r\n\r\n// 计算属性\r\nconst canRecharge = computed(() => {\r\n  const amountNum = parseFloat(amount.value);\r\n  return amountNum >= 1 && !isNaN(amountNum);\r\n});\r\n\r\n// 方法\r\n// 返回上一页\r\nconst goBack = () => {\r\n  uni.navigateBack();\r\n};\r\n\r\n// 页面跳转\r\nconst navigateTo = (url) => {\r\n  smartNavigate(url).catch(err => {\r\n    console.error('页面跳转失败:', err);\r\n  });\r\n};\r\n\r\n// 处理金额输入\r\nconst handleAmountInput = (e) => {\r\n  const value = e.detail.value;\r\n  // 限制只能输入两位小数\r\n  if (value.indexOf('.') !== -1) {\r\n    const decimal = value.split('.')[1];\r\n    if (decimal.length > 2) {\r\n      amount.value = parseFloat(value).toFixed(2);\r\n    }\r\n  }\r\n};\r\n\r\n// 选择快捷金额\r\nconst selectAmount = (amountValue) => {\r\n  amount.value = amountValue.toString();\r\n};\r\n\r\n// 选择支付方式\r\nconst selectPayment = (paymentId) => {\r\n  selectedPayment.value = paymentId;\r\n};\r\n\r\n// 提交充值\r\nconst submitRecharge = () => {\r\n  if (!canRecharge.value) {\r\n    return;\r\n  }\r\n  \r\n  uni.showLoading({\r\n    title: '请求中...'\r\n  });\r\n  \r\n  // 模拟API请求\r\n  setTimeout(() => {\r\n    uni.hideLoading();\r\n    \r\n    // 根据不同支付方式处理\r\n    if (selectedPayment.value === 'wxpay') {\r\n      // 调用微信支付\r\n      uni.showModal({\r\n        title: '微信支付',\r\n        content: `确认支付¥${amount.value}？`,\r\n        success: (res) => {\r\n          if (res.confirm) {\r\n            processPayment();\r\n          }\r\n        }\r\n      });\r\n    } else if (selectedPayment.value === 'alipay') {\r\n      // 调用支付宝支付\r\n      uni.showModal({\r\n        title: '支付宝支付',\r\n        content: `确认支付¥${amount.value}？`,\r\n        success: (res) => {\r\n          if (res.confirm) {\r\n            processPayment();\r\n          }\r\n        }\r\n      });\r\n    }\r\n  }, 500);\r\n};\r\n\r\n// 处理支付结果\r\nconst processPayment = () => {\r\n  uni.showLoading({\r\n    title: '支付中...'\r\n  });\r\n  \r\n  // 模拟支付过程\r\n  setTimeout(() => {\r\n    uni.hideLoading();\r\n    \r\n    // 支付成功后\r\n    uni.showToast({\r\n      title: '充值成功',\r\n      icon: 'success',\r\n      duration: 2000,\r\n      success: () => {\r\n        setTimeout(() => {\r\n          // 返回钱包页面\r\n          uni.navigateBack();\r\n        }, 2000);\r\n      }\r\n    });\r\n  }, 1500);\r\n};\r\n\r\n// 生命周期钩子\r\nonMounted(() => {\r\n  // 获取状态栏高度\r\n  const sysInfo = uni.getSystemInfoSync();\r\n  statusBarHeight.value = sysInfo.statusBarHeight;\r\n  navbarHeight.value = statusBarHeight.value + 44;\r\n});\r\n</script>\r\n\r\n<style>\r\n.recharge-container {\r\n  min-height: 100vh;\r\n  background-color: #f5f7fa;\r\n}\r\n\r\n/* 自定义导航栏 */\r\n.custom-navbar {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  height: 44px;\r\n  background-color: #0052CC;\r\n  color: #fff;\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 0 15px;\r\n  z-index: 999;\r\n}\r\n\r\n.navbar-left {\r\n  width: 80rpx;\r\n  height: 60rpx;\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.back-icon {\r\n  width: 40rpx;\r\n  height: 40rpx;\r\n}\r\n\r\n.navbar-title {\r\n  flex: 1;\r\n  text-align: center;\r\n  font-size: 18px;\r\n  font-weight: 500;\r\n}\r\n\r\n.navbar-right {\r\n  width: 80rpx;\r\n}\r\n\r\n/* 充值金额区域 */\r\n.recharge-section {\r\n  background-color: #fff;\r\n  margin: 30rpx;\r\n  padding: 30rpx;\r\n  border-radius: 20rpx;\r\n  box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n.section-title {\r\n  font-size: 28rpx;\r\n  color: #666;\r\n  margin-bottom: 30rpx;\r\n}\r\n\r\n.amount-input-area {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-bottom: 40rpx;\r\n}\r\n\r\n.currency-symbol {\r\n  font-size: 50rpx;\r\n  font-weight: bold;\r\n  margin-right: 20rpx;\r\n}\r\n\r\n.amount-input {\r\n  font-size: 60rpx;\r\n  font-weight: bold;\r\n  flex: 1;\r\n}\r\n\r\n.amount-quick-select {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  justify-content: space-between;\r\n}\r\n\r\n.quick-amount-item {\r\n  width: 120rpx;\r\n  height: 70rpx;\r\n  line-height: 70rpx;\r\n  text-align: center;\r\n  background-color: #f8f9fc;\r\n  border-radius: 35rpx;\r\n  margin-bottom: 20rpx;\r\n  font-size: 26rpx;\r\n  color: #333;\r\n  border: 1px solid transparent;\r\n}\r\n\r\n.quick-amount-item.selected {\r\n  background-color: #e6f0ff;\r\n  color: #0052CC;\r\n  border: 1px solid #0052CC;\r\n}\r\n\r\n/* 支付方式 */\r\n.payment-method {\r\n  background-color: #fff;\r\n  margin: 30rpx;\r\n  padding: 30rpx;\r\n  border-radius: 20rpx;\r\n  box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n.payment-options {\r\n  margin-top: 20rpx;\r\n}\r\n\r\n.payment-option {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 30rpx 0;\r\n  border-bottom: 1rpx solid #f5f5f5;\r\n}\r\n\r\n.payment-option:last-child {\r\n  border-bottom: none;\r\n}\r\n\r\n.payment-left {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.payment-icon {\r\n  width: 60rpx;\r\n  height: 60rpx;\r\n  margin-right: 20rpx;\r\n}\r\n\r\n.payment-name {\r\n  font-size: 28rpx;\r\n}\r\n\r\n.payment-check {\r\n  width: 40rpx;\r\n  height: 40rpx;\r\n}\r\n\r\n.check-icon {\r\n  width: 40rpx;\r\n  height: 40rpx;\r\n}\r\n\r\n/* 充值说明 */\r\n.recharge-notice {\r\n  margin: 30rpx;\r\n  padding: 30rpx;\r\n  background-color: #fff;\r\n  border-radius: 20rpx;\r\n  box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n.notice-title {\r\n  font-size: 28rpx;\r\n  font-weight: 500;\r\n  margin-bottom: 20rpx;\r\n}\r\n\r\n.notice-item {\r\n  font-size: 24rpx;\r\n  color: #999;\r\n  margin-bottom: 10rpx;\r\n  line-height: 1.5;\r\n}\r\n\r\n/* 底部按钮区域 */\r\n.bottom-btn-area {\r\n  position: fixed;\r\n  bottom: 0;\r\n  left: 0;\r\n  right: 0;\r\n  padding: 30rpx;\r\n  background-color: #fff;\r\n  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n.recharge-btn {\r\n  width: 100%;\r\n  height: 90rpx;\r\n  line-height: 90rpx;\r\n  background-color: #0052CC;\r\n  color: #fff;\r\n  font-size: 32rpx;\r\n  border-radius: 45rpx;\r\n}\r\n\r\n.btn-disabled {\r\n  background-color: #cccccc;\r\n  color: #ffffff;\r\n}\r\n</style> \r\n", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/subPackages/payment/pages/recharge.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "computed", "uni", "onMounted", "MiniProgramPage"], "mappings": ";;;;;;AAyFA,UAAM,kBAAkBA,cAAAA,IAAI,EAAE;AAC9B,UAAM,eAAeA,cAAAA,IAAI,EAAE;AAC3B,UAAM,SAASA,cAAAA,IAAI,EAAE;AACrB,UAAM,kBAAkBA,cAAAA,IAAI,OAAO;AACnC,UAAM,eAAeA,cAAAA,IAAI,CAAC,IAAI,IAAI,KAAK,KAAK,GAAG,CAAC;AAChD,UAAM,iBAAiBA,cAAAA,IAAI;AAAA,MACzB;AAAA,QACE,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,MAAM;AAAA,MACP;AAAA,MACD;AAAA,QACE,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,MAAM;AAAA,MACP;AAAA,IACH,CAAC;AAGD,UAAM,cAAcC,cAAQ,SAAC,MAAM;AACjC,YAAM,YAAY,WAAW,OAAO,KAAK;AACzC,aAAO,aAAa,KAAK,CAAC,MAAM,SAAS;AAAA,IAC3C,CAAC;AAID,UAAM,SAAS,MAAM;AACnBC,oBAAG,MAAC,aAAY;AAAA,IAClB;AAUA,UAAM,oBAAoB,CAAC,MAAM;AAC/B,YAAM,QAAQ,EAAE,OAAO;AAEvB,UAAI,MAAM,QAAQ,GAAG,MAAM,IAAI;AAC7B,cAAM,UAAU,MAAM,MAAM,GAAG,EAAE,CAAC;AAClC,YAAI,QAAQ,SAAS,GAAG;AACtB,iBAAO,QAAQ,WAAW,KAAK,EAAE,QAAQ,CAAC;AAAA,QAC3C;AAAA,MACF;AAAA,IACH;AAGA,UAAM,eAAe,CAAC,gBAAgB;AACpC,aAAO,QAAQ,YAAY;IAC7B;AAGA,UAAM,gBAAgB,CAAC,cAAc;AACnC,sBAAgB,QAAQ;AAAA,IAC1B;AAGA,UAAM,iBAAiB,MAAM;AAC3B,UAAI,CAAC,YAAY,OAAO;AACtB;AAAA,MACD;AAEDA,oBAAAA,MAAI,YAAY;AAAA,QACd,OAAO;AAAA,MACX,CAAG;AAGD,iBAAW,MAAM;AACfA,sBAAG,MAAC,YAAW;AAGf,YAAI,gBAAgB,UAAU,SAAS;AAErCA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,SAAS,QAAQ,OAAO,KAAK;AAAA,YAC7B,SAAS,CAAC,QAAQ;AAChB,kBAAI,IAAI,SAAS;AACf;cACD;AAAA,YACF;AAAA,UACT,CAAO;AAAA,QACP,WAAe,gBAAgB,UAAU,UAAU;AAE7CA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,SAAS,QAAQ,OAAO,KAAK;AAAA,YAC7B,SAAS,CAAC,QAAQ;AAChB,kBAAI,IAAI,SAAS;AACf;cACD;AAAA,YACF;AAAA,UACT,CAAO;AAAA,QACF;AAAA,MACF,GAAE,GAAG;AAAA,IACR;AAGA,UAAM,iBAAiB,MAAM;AAC3BA,oBAAAA,MAAI,YAAY;AAAA,QACd,OAAO;AAAA,MACX,CAAG;AAGD,iBAAW,MAAM;AACfA,sBAAG,MAAC,YAAW;AAGfA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,UACN,UAAU;AAAA,UACV,SAAS,MAAM;AACb,uBAAW,MAAM;AAEfA,4BAAG,MAAC,aAAY;AAAA,YACjB,GAAE,GAAI;AAAA,UACR;AAAA,QACP,CAAK;AAAA,MACF,GAAE,IAAI;AAAA,IACT;AAGAC,kBAAAA,UAAU,MAAM;AAEd,YAAM,UAAUD,oBAAI;AACpB,sBAAgB,QAAQ,QAAQ;AAChC,mBAAa,QAAQ,gBAAgB,QAAQ;AAAA,IAC/C,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC3ND,GAAG,WAAWE,SAAe;"}