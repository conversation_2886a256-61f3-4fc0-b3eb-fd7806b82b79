<template>
  <view class="red-packet-detail">
    <view class="header">
      <view class="title">{{ redPacket.title }}</view>
      <view class="amount">¥{{ redPacket.amount }}</view>
      <view class="status">{{ getStatusText(redPacket.status) }}</view>
    </view>
    
    <view class="info-section">
      <view class="info-item">
        <text class="label">红包类型</text>
        <text class="value">{{ getTypeText(redPacket.type) }}</text>
      </view>
      <view class="info-item">
        <text class="label">发送时间</text>
        <text class="value">{{ formatTime(redPacket.createTime) }}</text>
      </view>
      <view class="info-item">
        <text class="label">过期时间</text>
        <text class="value">{{ formatTime(redPacket.expireTime) }}</text>
      </view>
    </view>
    
    <view class="records-section" v-if="redPacket.records && redPacket.records.length > 0">
      <view class="section-title">领取记录</view>
      <view class="record-list">
        <view class="record-item" v-for="record in redPacket.records" :key="record.id">
          <image class="avatar" :src="record.user.avatar" mode="aspectFill" />
          <view class="user-info">
            <text class="nickname">{{ record.user.nickname }}</text>
            <text class="time">{{ formatTime(record.createTime) }}</text>
          </view>
          <text class="amount">¥{{ record.amount }}</text>
        </view>
      </view>
    </view>
    
    <view class="action-bar">
      <button class="share-btn" @click="handleShare">
        <text class="iconfont icon-share"></text>
        分享红包
      </button>
      <button class="grab-btn" v-if="!isExpired && !hasGrabbed" @click="handleGrab">抢红包</button>
    </view>
  </view>
</template>

<script>
import { formatTime } from '@/utils/format';
import { showShareMenu } from '@/utils/share';
import { RED_PACKET_TYPE } from '@/utils/redPacket';

export default {
  data() {
    return {
      redPacket: null,
      isExpired: false,
      hasGrabbed: false
    };
  },
  
  onLoad(options) {
    this.loadRedPacketDetail(options.id);
  },
  
  methods: {
    async loadRedPacketDetail(id) {
      try {
        const res = await uni.request({
          url: `/api/red-packets/${id}`,
          method: 'GET'
        });
        
        if (res.data.code === 0) {
          this.redPacket = res.data.data;
          this.isExpired = new Date() > new Date(this.redPacket.expireTime);
          this.hasGrabbed = this.redPacket.records.some(record => record.userId === this.userId);
        }
      } catch (error) {
        uni.showToast({
          title: '加载失败',
          icon: 'error'
        });
      }
    },
    
    getTypeText(type) {
      const typeMap = {
        [RED_PACKET_TYPE.NORMAL]: '普通红包',
        [RED_PACKET_TYPE.LUCKY]: '拼手气红包',
        [RED_PACKET_TYPE.FIXED]: '固定金额红包'
      };
      return typeMap[type] || '红包';
    },
    
    getStatusText(status) {
      const statusMap = {
        0: '进行中',
        1: '已领完',
        2: '已过期'
      };
      return statusMap[status] || '未知状态';
    },
    
    async handleShare() {
      try {
        await showShareMenu({
          type: 'redPacket',
          data: this.redPacket
        });
        uni.showToast({
          title: '分享成功',
          icon: 'success'
        });
      } catch (error) {
        uni.showToast({
          title: '分享失败',
          icon: 'error'
        });
      }
    },
    
    async handleGrab() {
      if (this.isExpired || this.hasGrabbed) return;
      
      try {
        const res = await uni.request({
          url: `/api/red-packets/${this.redPacket.id}/grab`,
          method: 'POST'
        });
        
        if (res.data.code === 0) {
          uni.showToast({
            title: '抢到红包啦！',
            icon: 'success'
          });
          this.hasGrabbed = true;
          this.loadRedPacketDetail(this.redPacket.id);
        } else {
          uni.showToast({
            title: res.data.message || '抢红包失败',
            icon: 'error'
          });
        }
      } catch (error) {
        uni.showToast({
          title: '抢红包失败',
          icon: 'error'
        });
      }
    }
  }
};
</script>

<style>
.red-packet-detail {
  padding: 30rpx;
  background: #fff;
  min-height: 100vh;
}

.header {
  text-align: center;
  padding: 40rpx 0;
  background: linear-gradient(45deg, #ff4d4f, #ff7875);
  border-radius: 20rpx;
  color: #fff;
  margin-bottom: 30rpx;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
}

.amount {
  font-size: 60rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
}

.status {
  font-size: 28rpx;
  opacity: 0.8;
}

.info-section {
  background: #f8f8f8;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.info-item:last-child {
  margin-bottom: 0;
}

.label {
  color: #666;
  font-size: 28rpx;
}

.value {
  color: #333;
  font-size: 28rpx;
}

.records-section {
  background: #f8f8f8;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
}

.record-item {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #eee;
}

.record-item:last-child {
  border-bottom: none;
}

.avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  margin-right: 20rpx;
}

.user-info {
  flex: 1;
}

.nickname {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 6rpx;
}

.time {
  font-size: 24rpx;
  color: #999;
}

.amount {
  font-size: 32rpx;
  color: #ff4d4f;
  font-weight: bold;
}

.action-bar {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  padding: 20rpx 30rpx;
  background: #fff;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.share-btn, .grab-btn {
  flex: 1;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  border-radius: 40rpx;
  font-size: 32rpx;
  margin: 0 10rpx;
}

.share-btn {
  background: #f5f5f5;
  color: #666;
  display: flex;
  align-items: center;
  justify-content: center;
}

.share-btn .iconfont {
  margin-right: 6rpx;
  font-size: 28rpx;
}

.grab-btn {
  background: linear-gradient(45deg, #ff4d4f, #ff7875);
  color: #fff;
}

.grab-btn[disabled] {
  background: #ccc;
  color: #fff;
}
</style> 