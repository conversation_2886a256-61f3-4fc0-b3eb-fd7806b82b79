/* 通用详情页样式优化 */

/* 详情页容器 */
.detail-container {
  min-height: 100vh;
  background-color: #f7f8fa;
  padding-bottom: 150rpx;
}
.detail-wrapper {
  padding: 24rpx;
}

/* 内容卡片通用样式 */
.content-card {
  background-color: #fff;
  border-radius: 16rpx;
  margin-bottom: 24rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
}

/* 标题区样式优化 */
.title-row {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16rpx;
}
.main-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  line-height: 1.4;
  flex: 1;
  margin-right: 20rpx;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.price-text {
  font-size: 36rpx;
  font-weight: 600;
  color: #ff4d4f;
  white-space: nowrap;
}

/* 元数据样式 */
.meta-info {
  margin-bottom: 24rpx;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}
.tag-group {
  display: flex;
  flex-wrap: wrap;
  margin-right: 20rpx;
  margin-bottom: 8rpx;
}
.info-tag {
  font-size: 24rpx;
  color: #1890ff;
  background-color: rgba(24, 144, 255, 0.08);
  padding: 4rpx 16rpx;
  border-radius: 6rpx;
  margin-right: 12rpx;
  margin-bottom: 8rpx;
}
.publish-time {
  font-size: 24rpx;
  color: #999;
}

/* 轮播图优化 */
.detail-swiper {
  height: 420rpx;
  border-radius: 12rpx;
  overflow: hidden;
  margin-bottom: 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
}
.swiper-image {
  width: 100%;
  height: 100%;
}

/* 基本信息卡片内部布局 */
.basic-info {
  display: flex;
  flex-wrap: wrap;
  background-color: #f9fafc;
  border-radius: 12rpx;
  padding: 16rpx 0;
  margin-top: 20rpx;
}
.info-item {
  width: 50%;
  padding: 12rpx 24rpx;
  box-sizing: border-box;
}
.info-label {
  font-size: 26rpx;
  color: #999;
  margin-bottom: 8rpx;
  display: block;
}
.info-value {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

/* 详情信息列表 */
.detail-list {
  display: flex;
  flex-direction: column;
}
.detail-item {
  display: flex;
  padding: 16rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}
.detail-item:last-child {
  border-bottom: none;
}
.detail-label {
  width: 160rpx;
  font-size: 28rpx;
  color: #888;
}
.detail-value {
  flex: 1;
  font-size: 28rpx;
  color: #333;
  line-height: 1.5;
}

/* 区块标题优化 */
.section-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 20rpx;
  position: relative;
  padding-left: 16rpx;
}
.section-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 6rpx;
  width: 6rpx;
  height: 28rpx;
  background-color: #1890ff;
  border-radius: 3rpx;
}

/* 描述内容样式 */
.description-content {
  padding: 20rpx;
  background-color: #f9fafc;
  border-radius: 12rpx;
  line-height: 1.6;
}
.description-text {
  font-size: 28rpx;
  color: #555;
  line-height: 1.6;
}

/* 联系人信息样式 */
.contact-content {
  padding: 20rpx;
  background-color: #f9fafc;
  border-radius: 12rpx;
}
.contact-item {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}
.contact-label {
  width: 120rpx;
  font-size: 28rpx;
  color: #888;
}
.contact-value {
  font-size: 28rpx;
  color: #333;
  flex: 1;
}
.contact-phone {
  color: #52c41a;
  font-weight: 500;
}
.contact-tips {
  display: flex;
  align-items: center;
  margin-top: 16rpx;
  padding: 10rpx 16rpx;
  background-color: rgba(255, 152, 0, 0.1);
  border-radius: 8rpx;
}
.tips-icon {
  font-size: 24rpx;
  color: #ff9800;
  margin-right: 8rpx;
}
.tips-text {
  font-size: 24rpx;
  color: #ff9800;
}

/* 发布者/卖家信息样式 */
.publisher-header {
  display: flex;
  align-items: center;
  padding-bottom: 16rpx;
}
.avatar-container {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  overflow: hidden;
  margin-right: 20rpx;
  border: 1rpx solid #f0f0f0;
}
.avatar-image {
  width: 100%;
  height: 100%;
}
.publisher-info {
  flex: 1;
}
.publisher-name {
  font-size: 30rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 8rpx;
}
.publisher-meta {
  display: flex;
  align-items: center;
}
.meta-text {
  font-size: 24rpx;
  color: #888;
  margin-right: 16rpx;
  padding: 2rpx 12rpx;
  background-color: #f5f7fa;
  border-radius: 4rpx;
}

/* 相似推荐列表 */
.similar-list {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -10rpx;
}
.similar-item {
  width: calc(50% - 20rpx);
  margin: 10rpx;
  background-color: #f9fafc;
  border-radius: 12rpx;
  overflow: hidden;
}
.similar-image {
  height: 180rpx;
  width: 100%;
}
.similar-info {
  padding: 12rpx;
}
.similar-title {
  font-size: 26rpx;
  color: #333;
  margin-bottom: 8rpx;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.similar-price {
  font-size: 26rpx;
  color: #ff4d4f;
  font-weight: 500;
}
.similar-meta {
  font-size: 22rpx;
  color: #999;
  margin-top: 4rpx;
}

/* 底部互动工具栏优化 */
.interaction-toolbar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  padding: 10rpx 10rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-top: 1rpx solid #f0f0f0;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  height: 120rpx;
  z-index: 100;
}
.toolbar-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 6rpx 0;
  margin: 0 4rpx;
}
.toolbar-icon {
  width: 44rpx;
  height: 44rpx;
  margin-bottom: 6rpx;
}
.toolbar-text {
  font-size: 22rpx;
  color: #666;
}
.share-button {
  background: transparent;
  border: none;
  margin: 0;
  padding: 0;
  line-height: normal;
  border-radius: 0;
  flex: 1;
}
.share-button::after {
  display: none;
}
.call-button {
  flex: 3;
  background: linear-gradient(135deg, #0052CC, #0066FF);
  height: 90rpx;
  margin: 0 0 0 10rpx;
  border-radius: 45rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 82, 204, 0.2);
}
.call-button-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
}
.call-text {
  color: #fff;
  font-size: 30rpx;
  font-weight: bold;
  line-height: 1.2;
}
.call-subtitle {
  color: rgba(255, 255, 255, 0.9);
  font-size: 20rpx;
  line-height: 1.2;
}

/* 自适应调整 */
@media screen and (max-width: 375px) {
.content-card {
    padding: 20rpx;
}
.main-title {
    font-size: 32rpx;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}
.detail-wrapper {
    padding: 16rpx;
}
.detail-swiper {
    height: 380rpx;
}
}

/* 招聘详情页的特殊样式 */
.job-detail-container {
  /* 可以添加特定于招聘详情页的样式 */
}

/* 岗位职责和任职要求列表样式 */
.job-responsibility,
.job-requirement {
  padding: 16rpx;
  background-color: #f9fafc;
  border-radius: 12rpx;
}
.list-item {
  display: flex;
  margin-bottom: 16rpx;
}
.list-item:last-child {
  margin-bottom: 0;
}
.list-dot {
  font-size: 28rpx;
  color: #1890ff;
  margin-right: 12rpx;
  line-height: 1.6;
}
.list-text {
  font-size: 28rpx;
  color: #555;
  line-height: 1.6;
  flex: 1;
}

/* 福利标签样式 */
.job-benefits {
  display: flex;
  flex-wrap: wrap;
  padding: 16rpx;
  background-color: #f9fafc;
  border-radius: 12rpx;
}
.benefit-tag {
  display: flex;
  align-items: center;
  background-color: #f6ffed;
  border: 1rpx solid #b7eb8f;
  border-radius: 6rpx;
  padding: 8rpx 16rpx;
  margin-right: 16rpx;
  margin-bottom: 16rpx;
}
.benefit-icon {
  font-size: 24rpx;
  color: #52c41a;
  margin-right: 8rpx;
}
.benefit-text {
  font-size: 24rpx;
  color: #52c41a;
}

/* 工作地址地图样式 */
.location-content {
  display: flex;
  align-items: center;
  padding: 20rpx;
  background-color: #f9fafc;
  border-radius: 12rpx;
  margin-bottom: 24rpx;
}
.location-text {
  flex: 1;
  font-size: 28rpx;
  color: #555;
  margin: 0 12rpx;
}
.location-arrow {
  font-size: 24rpx;
  color: #999;
}
.location-map {
  height: 240rpx;
  border-radius: 12rpx;
  overflow: hidden;
}
.map-preview {
  width: 100%;
  height: 100%;
}

/* 隐藏的分享按钮 */
.hidden-share-btn {
  position: fixed;
  width: 2rpx;
  height: 2rpx;
  opacity: 0;
  top: -999rpx;
  left: -999rpx;
  z-index: -1;
  overflow: hidden;
  padding: 0;
  margin: 0;
  border: none;
}
.hidden-share-btn::after {
  display: none;
}

/* 悬浮海报按钮 */
.float-poster-btn {
  position: fixed;
  right: 30rpx;
  bottom: 200rpx;
  width: 100rpx;
  height: 100rpx;
  background: rgba(240, 240, 240, 0.9);
  border-radius: 50%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  box-shadow: 0 6rpx 20rpx rgba(0,0,0,0.08);
  z-index: 90;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1rpx solid rgba(230, 230, 230, 0.6);
  transition: all 0.2s ease;
}
.float-poster-btn:active {
  transform: scale(0.95);
  box-shadow: 0 3rpx 10rpx rgba(0,0,0,0.08);
}
.poster-icon {
  width: 40rpx;
  height: 40rpx;
  margin-bottom: 4rpx;
}
.poster-text {
  font-size: 20rpx;
  color: #444;
  line-height: 1;
}
.job-detail-wrapper {
  padding: 24rpx;
  padding-top: 144px; /* 增加顶部内边距，确保内容不被导航栏遮挡 */
}

/* 相关招聘信息卡片样式 */
.related-jobs-card {
  margin-top: 12px;
  background-color: #fff;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

/* 可折叠标题栏样式 */
.collapsible-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  background-color: #fff;
  position: relative;
}
.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

/* 内容区样式 */
.collapsible-content {
  padding: 0 16px 16px;
  overflow: hidden;
}

/* 相关职位列表样式 */
.related-jobs-list {
  margin-bottom: 12px;
}
.related-job-item {
  padding: 12px 0;
  border-bottom: 1px solid #f5f5f5;
}
.related-job-item:last-child {
  border-bottom: none;
}
.job-item-content {
  display: flex;
  align-items: center;
}
.job-item-left {
  margin-right: 12px;
}
.company-logo {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  background-color: #f5f7fa;
}
.job-item-middle {
  flex: 1;
  overflow: hidden;
}
.job-item-title {
  font-size: 15px;
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.job-item-company {
  font-size: 13px;
  color: #666;
  margin-bottom: 6px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.job-item-tags {
  display: flex;
  flex-wrap: wrap;
}
.job-item-tag {
  font-size: 11px;
  color: #0066ff;
  background-color: rgba(0, 102, 255, 0.1);
  padding: 2px 6px;
  border-radius: 4px;
  margin-right: 6px;
}
.job-item-tag-more {
  font-size: 11px;
  color: #999;
}
.job-item-right {
  min-width: 80px;
  text-align: right;
}
.job-item-salary {
  font-size: 15px;
  font-weight: 500;
  color: #ff5252;
}

/* 查看更多按钮样式 */
.view-more-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 44px;
  background-color: #f7f9fc;
  border-radius: 8px;
  margin-top: 8px;
}
.view-more-text {
  font-size: 14px;
  color: #0066ff;
}
.view-more-icon {
  margin-left: 4px;
  font-size: 12px;
  color: #0066ff;
}

/* 空数据提示样式 */
.empty-related-jobs {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 24px 0;
}
.empty-image {
  width: 80px;
  height: 80px;
  margin-bottom: 12px;
}
.empty-text {
  font-size: 14px;
  color: #999;
}

/* 红包区域样式 */
.red-packet-card {
  background-color: #fff;
  margin: 20rpx 20rpx 30rpx;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 20rpx rgba(0, 0, 0, 0.08);
}
.red-packet-section {
  padding: 16rpx;
}
.red-packet-container {
  position: relative;
  height: 160rpx;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 16rpx rgba(255, 90, 95, 0.15);
}
.red-packet-blur-bg {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  z-index: 1;
  background: linear-gradient(to left, #FF5A5F, #FF8A8E);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}
.red-packet-content {
  position: relative;
  z-index: 2;
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 100%;
  padding: 0 30rpx;
}
.red-packet-left {
  flex: 1;
  display: flex;
  align-items: center;
}
.red-packet-icon {
  width: 80rpx;
  height: 80rpx;
  margin-right: 20rpx;
  filter: drop-shadow(0 2rpx 4rpx rgba(0, 0, 0, 0.1));
}
.red-packet-info {
  flex: 1;
}
.red-packet-title {
  font-size: 32rpx;
  color: #FFFFFF;
  font-weight: bold;
  margin-bottom: 8rpx;
  text-shadow: none;
}
.red-packet-desc {
  font-size: 20rpx;
  color: #FFFFFF;
  font-weight: bold;
  text-shadow: none;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.red-packet-right {
  display: flex;
  flex-direction: column;
  align-items: center;
}
.red-packet-amount {
  font-size: 36rpx;
  color: #FFFFFF;
  font-weight: bold;
  margin-bottom: 10rpx;
  text-shadow: none;
}
.red-packet-amount .prefix {
  font-size: 28rpx;
  margin-right: 4rpx;
}
.grab-btn {
  background-color: #FFFFFF;
  color: #F05A5F;
  font-size: 24rpx;
  padding: 12rpx 32rpx;
  border-radius: 30rpx;
  font-weight: bold;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.05);
}
.red-packet-validity, 
.red-packet-description {
  margin-top: 12rpx;
  padding: 0 16rpx;
}
.validity-text, 
.description-text {
  font-size: 24rpx;
  color: #666;
  line-height: 1.5;
}
.red-packet-description {
  margin-bottom: 10rpx;
}

/* 红包弹窗 */
.red-packet-popup {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
}
.red-packet-popup-wrapper {
  position: relative;
  width: 600rpx;
  background-color: #FFFFFF;
  border-radius: 24rpx;
  overflow: hidden;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.15);
}
.red-packet-task-modal {
  position: relative;
  width: 100%;
  padding: 40rpx 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  box-sizing: border-box;
}
.red-packet-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: calc(100% - 60rpx);
  padding: 0 0;
  margin-bottom: 30rpx;
  box-sizing: border-box;
}
.red-packet-modal-title {
  font-size: 36rpx;
  color: #333;
  font-weight: 600;
  flex-grow: 1;
  text-align: center;
  padding-right: 60rpx; /* 为关闭按钮留出空间 */
}
.red-packet-modal-close {
  font-size: 40rpx;
  color: #999;
  cursor: pointer;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}
.red-packet-modal-amount {
  font-size: 72rpx;
  color: #FF5A5F;
  font-weight: bold;
  text-align: center;
  margin-bottom: 40rpx;
  width: 100%; /* 确保金额文本占据全部宽度以便自身居中 */
}
.red-packet-task-list {
  background-color: #F8F8F8;
  border-radius: 16rpx;
  padding: 20rpx;
  margin-bottom: 30rpx;
  width: calc(100% - 60rpx); /* 减去弹窗左右内边距 */
  box-sizing: border-box;
}
.red-packet-task-item {
  display: flex;
  align-items: center;
  padding: 20rpx;
  background-color: #FFFFFF;
  border-radius: 12rpx;
  margin-bottom: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  box-sizing: border-box;
}
.red-packet-task-item:last-child {
  margin-bottom: 0;
}
.task-completed {
  background-color: #F6FFED;
  border: 1rpx solid #B7EB8F;
}
.task-icon-container {
  width: 48rpx;
  height: 48rpx;
  background-color: #FFECEC;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
  flex-shrink: 0;
}
.completed-icon-container {
  background-color: #F6FFED;
}
.task-icon {
  width: 28rpx;
  height: 28rpx;
}
.task-info {
  flex: 1;
}
.task-title {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 4rpx;
}
.task-desc {
  font-size: 24rpx;
  color: #999;
}
.task-action-btn {
  background-color: #FF5A5F;
  color: #FFFFFF;
  font-size: 24rpx;
  padding: 12rpx 30rpx;
  border-radius: 30rpx;
  font-weight: 600;
  margin-left: 20rpx;
  border: none;
  outline: none;
  flex-shrink: 0;
  width: 140rpx; /* 设置固定宽度 */
  text-align: center;
}
.completed-btn {
  background-color: #52C41A;
}
.red-packet-get-btn {
  background-color: rgba(255, 90, 95, 0.3);
  color: #FFFFFF;
  font-size: 32rpx;
  font-weight: 600;
  padding: 24rpx 0;
  border-radius: 16rpx;
  text-align: center;
  margin-bottom: 20rpx;
  transition: all 0.3s;
  width: calc(100% - 60rpx); /* 减去弹窗左右内边距 */
}
.red-packet-get-btn-active {
  background-color: #FF5A5F;
  box-shadow: 0 4rpx 12rpx rgba(255, 90, 95, 0.3);
}
.red-packet-tip {
  text-align: center;
  width: 100%;
}
.red-packet-tip-text {
  font-size: 24rpx;
  color: #999;
}
.red-packet-success-modal {
  position: relative;
  overflow: hidden;
  padding: 40rpx;
  text-align: center;
}
.success-bg {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  z-index: -1;
}
.success-content {
  position: relative;
  z-index: 1;
  padding: 30rpx 0;
}
.success-title {
  font-size: 40rpx;
  color: #FFD700;
  font-weight: bold;
  margin-bottom: 30rpx;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
}
.success-amount {
  font-size: 80rpx;
  color: #FFD700;
  font-weight: bold;
  margin-bottom: 30rpx;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
}
.success-desc {
  font-size: 28rpx;
  color: #FFFFFF;
  margin-bottom: 60rpx;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
}
.success-btn-group {
  display: flex;
  justify-content: center;
  gap: 30rpx;
}
.success-btn {
  background-color: rgba(255, 255, 255, 0.2);
  color: #FFFFFF;
  font-size: 28rpx;
  padding: 16rpx 40rpx;
  border-radius: 40rpx;
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
}
.success-btn-wallet {
  background-color: #FFD700;
  color: #333;
}

/* 评论输入区域样式 */
.comment-popup {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}
.comment-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
}
.comment-container {
  position: relative;
  width: 80%;
  max-width: 600px;
  background-color: #fff;
  border-radius: 12px;
  padding: 20px;
}
.comment-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}
.comment-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}
.comment-close {
  font-size: 20px;
  color: #999;
  cursor: pointer;
}
.comment-body {
  margin-bottom: 20px;
}
.comment-textarea {
  width: 100%;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  margin-bottom: 10px;
}
.comment-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.comment-count {
  font-size: 14px;
  color: #999;
}
.comment-submit {
  background-color: #0066ff;
  color: #fff;
  padding: 10px 20px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

/* 活跃工具栏项样式 */
.active-toolbar-item {
  color: #0066ff;
}
.active-toolbar-item .toolbar-text {
  color: #0066ff;
}

/* 自定义导航栏样式 */
.custom-navbar {
  background: linear-gradient(135deg, #0066FF, #0052CC);
  height: 88rpx;
  padding-top: 44px; /* 状态栏高度 */
  display: flex;
  align-items: center;
  position: fixed; /* 改为固定定位 */
  top: 0;
  left: 0;
  right: 0;
  padding-bottom: 10rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 82, 204, 0.2);
  z-index: 100; /* 提高z-index确保在最上层 */
}
.navbar-left {
  width: 60px;
  display: flex;
  align-items: center;
}
.back-icon {
  width: 20px;
  height: 20px;
}
.navbar-title {
  flex: 1;
  text-align: center;
  font-size: 17px;
  font-weight: 500;
  color: #fff;
}
.navbar-right {
  width: 60px;
  display: flex;
  justify-content: flex-end;
  align-items: center;
}
