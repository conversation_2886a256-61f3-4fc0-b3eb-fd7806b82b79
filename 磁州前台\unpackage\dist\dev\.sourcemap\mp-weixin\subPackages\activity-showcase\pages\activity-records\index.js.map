{"version": 3, "file": "index.js", "sources": ["subPackages/activity-showcase/pages/activity-records/index.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/c3ViUGFja2FnZXNcYWN0aXZpdHktc2hvd2Nhc2VccGFnZXNcYWN0aXZpdHktcmVjb3Jkc1xpbmRleC52dWU"], "sourcesContent": ["<template>\n  <view class=\"activity-records-container\">\n    <!-- 自定义导航栏 -->\n    <view class=\"custom-navbar\">\n      <view class=\"navbar-bg\"></view>\n      <view class=\"navbar-content\">\n        <view class=\"back-btn\" @click=\"goBack\">\n          <image src=\"/static/images/tabbar/最新返回键.png\" mode=\"aspectFit\" class=\"back-icon\"></image>\n        </view>\n        <view class=\"navbar-title\">活动记录</view>\n        <view class=\"navbar-right\">\n          <view class=\"filter-btn\" @click=\"showFilter\">\n            <svg class=\"icon\" viewBox=\"0 0 24 24\" width=\"24\" height=\"24\">\n              <path d=\"M22 3H2l8 9.46V19l4 2v-8.54L22 3z\" stroke=\"#FFFFFF\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\n            </svg>\n          </view>\n        </view>\n      </view>\n    </view>\n\n    <!-- 活动类型标签栏 -->\n    <view class=\"activity-tabs\">\n      <view \n        v-for=\"(tab, index) in activityTabs\" \n        :key=\"index\"\n        class=\"tab-item\"\n        :class=\"{ active: currentTab === index }\"\n        @click=\"switchTab(index)\"\n      >\n        <text class=\"tab-text\">{{ tab.name }}</text>\n        <view class=\"tab-indicator\" v-if=\"currentTab === index\" :style=\"{\n          background: 'linear-gradient(90deg, #FF3B69 0%, #FF7A9E 100%)'\n        }\"></view>\n      </view>\n    </view>\n\n    <!-- 活动记录列表区域 -->\n    <swiper class=\"records-swiper\" :current=\"currentTab\" @change=\"onSwiperChange\">\n      <swiper-item v-for=\"(tab, tabIndex) in activityTabs\" :key=\"tabIndex\">\n        <scroll-view \n          class=\"tab-content\" \n          scroll-y \n          refresher-enabled\n          :refresher-triggered=\"isRefreshing\"\n          @refresherrefresh=\"onRefresh\"\n          @scrolltolower=\"loadMore\"\n        >\n          <view class=\"records-list\">\n            <view \n              v-for=\"record in getRecordsByType(tab.type)\" \n              :key=\"record.id\"\n              class=\"record-card\"\n              @click=\"viewActivityDetail(record)\"\n            >\n              <!-- 活动图片 -->\n              <image :src=\"record.image\" class=\"record-image\" mode=\"aspectFill\"></image>\n              \n              <!-- 活动状态标签 -->\n              <view class=\"status-tag\" :style=\"{\n                background: getStatusBackground(record.status),\n                color: '#FFFFFF'\n              }\">\n                {{ getStatusText(record.status) }}\n              </view>\n              \n              <!-- 活动信息 -->\n              <view class=\"record-info\">\n                <text class=\"record-title\">{{ record.title }}</text>\n                \n                <view class=\"record-meta\">\n                  <view class=\"meta-item\">\n                    <svg class=\"meta-icon\" viewBox=\"0 0 24 24\" width=\"16\" height=\"16\">\n                      <rect x=\"3\" y=\"4\" width=\"18\" height=\"18\" rx=\"2\" ry=\"2\" stroke=\"#999999\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></rect>\n                      <line x1=\"16\" y1=\"2\" x2=\"16\" y2=\"6\" stroke=\"#999999\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></line>\n                      <line x1=\"8\" y1=\"2\" x2=\"8\" y2=\"6\" stroke=\"#999999\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></line>\n                      <line x1=\"3\" y1=\"10\" x2=\"21\" y2=\"10\" stroke=\"#999999\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></line>\n                    </svg>\n                    <text class=\"meta-text\">{{ record.date }}</text>\n                  </view>\n                  \n                  <view class=\"meta-item\">\n                    <svg class=\"meta-icon\" viewBox=\"0 0 24 24\" width=\"16\" height=\"16\">\n                      <circle cx=\"12\" cy=\"12\" r=\"10\" stroke=\"#999999\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></circle>\n                      <polyline points=\"12 6 12 12 16 14\" stroke=\"#999999\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></polyline>\n                    </svg>\n                    <text class=\"meta-text\">{{ record.time }}</text>\n                  </view>\n                  \n                  <view class=\"meta-item\">\n                    <svg class=\"meta-icon\" viewBox=\"0 0 24 24\" width=\"16\" height=\"16\">\n                      <path d=\"M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0118 0z\" stroke=\"#999999\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\n                      <circle cx=\"12\" cy=\"10\" r=\"3\" stroke=\"#999999\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></circle>\n                    </svg>\n                    <text class=\"meta-text\">{{ record.location }}</text>\n                  </view>\n                </view>\n                \n                <view class=\"record-bottom\">\n                  <view class=\"participants\">\n                    <view class=\"avatar-group\">\n                      <image \n                        v-for=\"(avatar, avatarIndex) in record.participants.slice(0, 3)\" \n                        :key=\"avatarIndex\"\n                        :src=\"avatar\"\n                        class=\"participant-avatar\"\n                      ></image>\n                      <view class=\"avatar-more\" v-if=\"record.participants.length > 3\">\n                        +{{ record.participants.length - 3 }}\n                      </view>\n                    </view>\n                    <text class=\"participant-count\">{{ record.participants.length }}人参与</text>\n                  </view>\n                  \n                  <view class=\"record-actions\">\n                    <view \n                      class=\"action-btn share\"\n                      @click.stop=\"shareActivity(record)\"\n                    >\n                      <svg class=\"action-icon\" viewBox=\"0 0 24 24\" width=\"16\" height=\"16\">\n                        <circle cx=\"18\" cy=\"5\" r=\"3\" stroke=\"#666666\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></circle>\n                        <circle cx=\"6\" cy=\"12\" r=\"3\" stroke=\"#666666\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></circle>\n                        <circle cx=\"18\" cy=\"19\" r=\"3\" stroke=\"#666666\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></circle>\n                        <line x1=\"8.59\" y1=\"13.51\" x2=\"15.42\" y2=\"17.49\" stroke=\"#666666\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></line>\n                        <line x1=\"15.41\" y1=\"6.51\" x2=\"8.59\" y2=\"10.49\" stroke=\"#666666\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></line>\n                      </svg>\n                      <text>分享</text>\n                    </view>\n                    \n                    <view \n                      class=\"action-btn primary\"\n                      :style=\"{\n                        background: getPrimaryActionBgColor(record.status),\n                        color: '#FFFFFF'\n                      }\"\n                      @click.stop=\"handlePrimaryAction(record)\"\n                    >\n                      {{ getPrimaryActionText(record.status) }}\n                    </view>\n                  </view>\n                </view>\n              </view>\n            </view>\n          </view>\n\n          <!-- 空状态 -->\n          <view class=\"empty-state\" v-if=\"getRecordsByType(tab.type).length === 0\">\n            <image class=\"empty-image\" :src=\"tab.emptyImage || '/static/images/empty-records.png'\"></image>\n            <text class=\"empty-text\">{{ tab.emptyText || '暂无相关活动记录' }}</text>\n            <view class=\"action-btn\" @click=\"navigateTo('/subPackages/activity-showcase/pages/list/index')\" :style=\"{\n              background: 'linear-gradient(135deg, #FF3B69 0%, #FF7A9E 100%)',\n              borderRadius: '35px',\n              boxShadow: '0 5px 15px rgba(255,59,105,0.3)'\n            }\">\n              <text>去参与活动</text>\n            </view>\n          </view>\n        </scroll-view>\n      </swiper-item>\n    </swiper>\n\n    <!-- 筛选弹窗 -->\n    <uni-popup ref=\"filterPopup\" type=\"bottom\">\n      <view class=\"filter-popup\">\n        <view class=\"filter-header\">\n          <text class=\"filter-title\">筛选</text>\n          <view class=\"filter-close\" @click=\"closeFilter\">\n            <svg class=\"icon\" viewBox=\"0 0 24 24\" width=\"24\" height=\"24\">\n              <line x1=\"18\" y1=\"6\" x2=\"6\" y2=\"18\" stroke=\"#333333\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></line>\n              <line x1=\"6\" y1=\"6\" x2=\"18\" y2=\"18\" stroke=\"#333333\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></line>\n            </svg>\n          </view>\n        </view>\n        \n        <view class=\"filter-content\">\n          <!-- 时间筛选 -->\n          <view class=\"filter-section\">\n            <text class=\"section-title\">时间范围</text>\n            <view class=\"filter-options\">\n              <view \n                v-for=\"(option, index) in timeOptions\" \n                :key=\"index\"\n                class=\"filter-option\"\n                :class=\"{ active: selectedTimeOption === index }\"\n                @click=\"selectTimeOption(index)\"\n              >\n                {{ option.label }}\n              </view>\n            </view>\n          </view>\n          \n          <!-- 活动状态筛选 -->\n          <view class=\"filter-section\">\n            <text class=\"section-title\">活动状态</text>\n            <view class=\"filter-options\">\n              <view \n                v-for=\"(option, index) in statusOptions\" \n                :key=\"index\"\n                class=\"filter-option\"\n                :class=\"{ active: selectedStatusOptions.includes(index) }\"\n                @click=\"toggleStatusOption(index)\"\n              >\n                {{ option.label }}\n              </view>\n            </view>\n          </view>\n          \n          <!-- 活动类型筛选 -->\n          <view class=\"filter-section\">\n            <text class=\"section-title\">活动类型</text>\n            <view class=\"filter-options\">\n              <view \n                v-for=\"(option, index) in typeOptions\" \n                :key=\"index\"\n                class=\"filter-option\"\n                :class=\"{ active: selectedTypeOptions.includes(index) }\"\n                @click=\"toggleTypeOption(index)\"\n              >\n                {{ option.label }}\n              </view>\n            </view>\n          </view>\n        </view>\n        \n        <view class=\"filter-footer\">\n          <view class=\"filter-reset\" @click=\"resetFilter\">\n            <text>重置</text>\n          </view>\n          <view class=\"filter-apply\" @click=\"applyFilter\">\n            <text>确定</text>\n          </view>\n        </view>\n      </view>\n    </uni-popup>\n  </view>\n</template>\n\n<script setup>\nimport { ref, computed, onMounted } from 'vue';\n\n// 页面状态\nconst currentTab = ref(0);\nconst isRefreshing = ref(false);\nconst recordsList = ref([]);\nconst filterPopup = ref(null);\n\n// 筛选选项\nconst selectedTimeOption = ref(0);\nconst selectedStatusOptions = ref([0]);\nconst selectedTypeOptions = ref([0]);\n\n// 筛选选项数据\nconst timeOptions = [\n  { label: '全部时间', value: 'all' },\n  { label: '最近一周', value: 'week' },\n  { label: '最近一月', value: 'month' },\n  { label: '最近三月', value: 'three_months' },\n  { label: '自定义', value: 'custom' }\n];\n\nconst statusOptions = [\n  { label: '全部状态', value: 'all' },\n  { label: '未开始', value: 'upcoming' },\n  { label: '进行中', value: 'ongoing' },\n  { label: '已结束', value: 'ended' },\n  { label: '已取消', value: 'cancelled' }\n];\n\nconst typeOptions = [\n  { label: '全部类型', value: 'all' },\n  { label: '文化活动', value: 'culture' },\n  { label: '体育赛事', value: 'sports' },\n  { label: '亲子活动', value: 'family' },\n  { label: '公益活动', value: 'charity' },\n  { label: '户外拓展', value: 'outdoor' }\n];\n\n// 活动标签页\nconst activityTabs = [\n  { name: '全部', type: 'all', emptyText: '暂无活动记录', emptyImage: '/static/images/empty-activities.png' },\n  { name: '我参与的', type: 'participated', emptyText: '暂无参与活动', emptyImage: '/static/images/empty-participated.png' },\n  { name: '我发起的', type: 'created', emptyText: '暂无发起活动', emptyImage: '/static/images/empty-created.png' },\n  { name: '已收藏', type: 'favorite', emptyText: '暂无收藏活动', emptyImage: '/static/images/empty-favorites.png' }\n];\n\n// 模拟数据\nconst mockRecords = [\n  {\n    id: '1001',\n    title: '磁州文化节',\n    type: 'culture',\n    status: 'upcoming',\n    date: '2024-06-15',\n    time: '09:00-18:00',\n    location: '磁州文化广场',\n    image: '/static/demo/activity1.jpg',\n    participants: [\n      '/static/demo/avatar1.png',\n      '/static/demo/avatar2.png',\n      '/static/demo/avatar3.png',\n      '/static/demo/avatar4.png',\n      '/static/demo/avatar5.png'\n    ],\n    recordType: 'participated'\n  },\n  {\n    id: '1002',\n    title: '亲子户外拓展活动',\n    type: 'family',\n    status: 'ongoing',\n    date: '2024-05-28',\n    time: '14:00-17:00',\n    location: '磁州森林公园',\n    image: '/static/demo/activity2.jpg',\n    participants: [\n      '/static/demo/avatar1.png',\n      '/static/demo/avatar3.png',\n      '/static/demo/avatar5.png'\n    ],\n    recordType: 'participated'\n  },\n  {\n    id: '1003',\n    title: '社区篮球赛',\n    type: 'sports',\n    status: 'ended',\n    date: '2024-05-20',\n    time: '10:00-12:00',\n    location: '磁州体育中心',\n    image: '/static/demo/activity3.jpg',\n    participants: [\n      '/static/demo/avatar2.png',\n      '/static/demo/avatar4.png',\n      '/static/demo/avatar5.png',\n      '/static/demo/avatar1.png'\n    ],\n    recordType: 'created'\n  },\n  {\n    id: '1004',\n    title: '传统文化体验课',\n    type: 'culture',\n    status: 'cancelled',\n    date: '2024-05-15',\n    time: '15:00-17:00',\n    location: '磁州文化馆',\n    image: '/static/demo/activity4.jpg',\n    participants: [\n      '/static/demo/avatar3.png',\n      '/static/demo/avatar1.png'\n    ],\n    recordType: 'favorite'\n  },\n  {\n    id: '1005',\n    title: '环保公益行动',\n    type: 'charity',\n    status: 'upcoming',\n    date: '2024-06-05',\n    time: '09:00-12:00',\n    location: '磁州河畔',\n    image: '/static/demo/activity5.jpg',\n    participants: [\n      '/static/demo/avatar1.png',\n      '/static/demo/avatar2.png',\n      '/static/demo/avatar3.png',\n      '/static/demo/avatar4.png',\n      '/static/demo/avatar5.png',\n      '/static/demo/avatar1.png',\n      '/static/demo/avatar2.png'\n    ],\n    recordType: 'created'\n  }\n];\n\n// 生命周期\nonMounted(() => {\n  loadRecords();\n});\n\n// 方法\nconst loadRecords = () => {\n  // 模拟加载数据\n  recordsList.value = mockRecords;\n};\n\nconst getRecordsByType = (type) => {\n  if (type === 'all') {\n    return recordsList.value;\n  }\n  return recordsList.value.filter(record => record.recordType === type);\n};\n\nconst switchTab = (index) => {\n  currentTab.value = index;\n};\n\nconst onSwiperChange = (e) => {\n  currentTab.value = e.detail.current;\n};\n\nconst onRefresh = () => {\n  isRefreshing.value = true;\n  setTimeout(() => {\n    loadRecords();\n    isRefreshing.value = false;\n  }, 1000);\n};\n\nconst loadMore = () => {\n  // 模拟加载更多\n  console.log('加载更多记录');\n};\n\nconst getStatusText = (status) => {\n  const statusMap = {\n    'upcoming': '未开始',\n    'ongoing': '进行中',\n    'ended': '已结束',\n    'cancelled': '已取消'\n  };\n  return statusMap[status] || '未知状态';\n};\n\nconst getStatusBackground = (status) => {\n  const bgMap = {\n    'upcoming': 'rgba(255, 149, 0, 0.8)',\n    'ongoing': 'rgba(52, 199, 89, 0.8)',\n    'ended': 'rgba(142, 142, 147, 0.8)',\n    'cancelled': 'rgba(255, 59, 48, 0.8)'\n  };\n  return bgMap[status] || 'rgba(142, 142, 147, 0.8)';\n};\n\nconst getPrimaryActionText = (status) => {\n  const actionMap = {\n    'upcoming': '立即报名',\n    'ongoing': '查看详情',\n    'ended': '活动回顾',\n    'cancelled': '查看详情'\n  };\n  return actionMap[status] || '查看详情';\n};\n\nconst getPrimaryActionBgColor = (status) => {\n  const bgColorMap = {\n    'upcoming': 'linear-gradient(135deg, #FF3B69 0%, #FF7A9E 100%)',\n    'ongoing': 'linear-gradient(135deg, #34C759 0%, #7ED321 100%)',\n    'ended': 'linear-gradient(135deg, #8E8E93 0%, #AEAEB2 100%)',\n    'cancelled': 'linear-gradient(135deg, #8E8E93 0%, #AEAEB2 100%)'\n  };\n  return bgColorMap[status] || 'linear-gradient(135deg, #FF3B69 0%, #FF7A9E 100%)';\n};\n\nconst handlePrimaryAction = (record) => {\n  switch (record.status) {\n    case 'upcoming':\n      navigateTo(`/subPackages/activity-showcase/pages/activity-detail/index?id=${record.id}&action=register`);\n      break;\n    case 'ongoing':\n      navigateTo(`/subPackages/activity-showcase/pages/activity-detail/index?id=${record.id}`);\n      break;\n    case 'ended':\n      navigateTo(`/subPackages/activity-showcase/pages/activity-detail/index?id=${record.id}&tab=review`);\n      break;\n    default:\n      navigateTo(`/subPackages/activity-showcase/pages/activity-detail/index?id=${record.id}`);\n  }\n};\n\nconst viewActivityDetail = (record) => {\n  navigateTo(`/subPackages/activity-showcase/pages/activity-detail/index?id=${record.id}`);\n};\n\nconst shareActivity = (record) => {\n  uni.showShareMenu({\n    withShareTicket: true,\n    menus: ['shareAppMessage', 'shareTimeline']\n  });\n};\n\nconst showFilter = () => {\n  filterPopup.value.open();\n};\n\nconst closeFilter = () => {\n  filterPopup.value.close();\n};\n\nconst selectTimeOption = (index) => {\n  selectedTimeOption.value = index;\n};\n\nconst toggleStatusOption = (index) => {\n  const position = selectedStatusOptions.value.indexOf(index);\n  if (index === 0) {\n    // 如果选择\"全部\"，清除其他选项\n    selectedStatusOptions.value = [0];\n  } else {\n    // 如果选择其他选项，移除\"全部\"选项\n    if (selectedStatusOptions.value.includes(0)) {\n      selectedStatusOptions.value = selectedStatusOptions.value.filter(item => item !== 0);\n    }\n    \n    // 切换选中状态\n    if (position !== -1) {\n      selectedStatusOptions.value.splice(position, 1);\n      // 如果没有选项，默认选中\"全部\"\n      if (selectedStatusOptions.value.length === 0) {\n        selectedStatusOptions.value = [0];\n      }\n    } else {\n      selectedStatusOptions.value.push(index);\n    }\n  }\n};\n\nconst toggleTypeOption = (index) => {\n  const position = selectedTypeOptions.value.indexOf(index);\n  if (index === 0) {\n    // 如果选择\"全部\"，清除其他选项\n    selectedTypeOptions.value = [0];\n  } else {\n    // 如果选择其他选项，移除\"全部\"选项\n    if (selectedTypeOptions.value.includes(0)) {\n      selectedTypeOptions.value = selectedTypeOptions.value.filter(item => item !== 0);\n    }\n    \n    // 切换选中状态\n    if (position !== -1) {\n      selectedTypeOptions.value.splice(position, 1);\n      // 如果没有选项，默认选中\"全部\"\n      if (selectedTypeOptions.value.length === 0) {\n        selectedTypeOptions.value = [0];\n      }\n    } else {\n      selectedTypeOptions.value.push(index);\n    }\n  }\n};\n\nconst resetFilter = () => {\n  selectedTimeOption.value = 0;\n  selectedStatusOptions.value = [0];\n  selectedTypeOptions.value = [0];\n};\n\nconst applyFilter = () => {\n  // 应用筛选\n  console.log('应用筛选', {\n    time: timeOptions[selectedTimeOption.value].value,\n    status: selectedStatusOptions.value.map(index => statusOptions[index].value),\n    type: selectedTypeOptions.value.map(index => typeOptions[index].value)\n  });\n  \n  // 模拟筛选结果\n  uni.showToast({\n    title: '筛选已应用',\n    icon: 'success'\n  });\n  \n  closeFilter();\n};\n\nconst goBack = () => {\n  uni.navigateBack();\n};\n\nconst navigateTo = (url) => {\n  uni.navigateTo({ url });\n};\n</script>\n\n<style scoped>\n.activity-records-container {\n  min-height: 100vh;\n  background-color: #F5F5F5;\n  padding-bottom: 30rpx;\n}\n\n/* 导航栏样式 */\n.custom-navbar {\n  position: fixed;\n  top: 0;\n  left: 0;\n  width: 100%;\n  z-index: 100;\n}\n\n.navbar-bg {\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  background: linear-gradient(135deg, #FF3B69 0%, #FF7A9E 100%);\n}\n\n.navbar-content {\n  position: relative;\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  height: 107rpx; /* 原来是102rpx，增加5rpx */\n  padding: var(--status-bar-height) 30rpx 0;\n}\n\n.back-btn, .filter-btn {\n  width: 60rpx;\n  height: 60rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.back-icon {\n  width: 36rpx;\n  height: 36rpx;\n}\n\n.navbar-title {\n  font-size: 36rpx;\n  font-weight: bold;\n  color: #FFFFFF;\n}\n\n.navbar-right {\n  display: flex;\n  align-items: center;\n}\n\n/* 标签栏样式 */\n.activity-tabs {\n  display: flex;\n  background: #FFFFFF;\n  padding: 0 20rpx;\n  margin-top: calc(var(--status-bar-height) + 107rpx); /* 原来是102rpx，增加5rpx */\n  border-bottom: 1rpx solid #EEEEEE;\n  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.05);\n}\n\n.tab-item {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  padding: 20rpx 0;\n  position: relative;\n}\n\n.tab-text {\n  font-size: 28rpx;\n  color: #333333;\n  padding: 0 10rpx;\n}\n\n.tab-item.active .tab-text {\n  color: #FF3B69;\n  font-weight: 500;\n}\n\n.tab-indicator {\n  position: absolute;\n  bottom: 0;\n  width: 40rpx;\n  height: 6rpx;\n  border-radius: 3rpx;\n}\n\n/* 活动记录列表样式 */\n.records-swiper {\n  height: calc(100vh - var(--status-bar-height) - 107rpx - 70rpx); /* 原来是102rpx，增加5rpx */\n}\n\n.tab-content {\n  height: 100%;\n  padding: 20rpx;\n}\n\n.records-list {\n  padding-bottom: 30rpx;\n}\n\n.record-card {\n  background: #FFFFFF;\n  border-radius: 20rpx;\n  margin-bottom: 20rpx;\n  overflow: hidden;\n  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.05);\n  position: relative;\n}\n\n.record-image {\n  width: 100%;\n  height: 300rpx;\n  object-fit: cover;\n}\n\n.status-tag {\n  position: absolute;\n  top: 20rpx;\n  right: 20rpx;\n  padding: 6rpx 20rpx;\n  border-radius: 30rpx;\n  font-size: 24rpx;\n  font-weight: 500;\n}\n\n.record-info {\n  padding: 20rpx 30rpx;\n}\n\n.record-title {\n  font-size: 32rpx;\n  font-weight: bold;\n  color: #333333;\n  margin-bottom: 20rpx;\n  display: -webkit-box;\n  -webkit-line-clamp: 2;\n  -webkit-box-orient: vertical;\n  overflow: hidden;\n  text-overflow: ellipsis;\n}\n\n.record-meta {\n  display: flex;\n  flex-wrap: wrap;\n  margin-bottom: 20rpx;\n}\n\n.meta-item {\n  display: flex;\n  align-items: center;\n  margin-right: 30rpx;\n  margin-bottom: 10rpx;\n}\n\n.meta-icon {\n  margin-right: 6rpx;\n}\n\n.meta-text {\n  font-size: 24rpx;\n  color: #999999;\n}\n\n.record-bottom {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-top: 10rpx;\n}\n\n.participants {\n  display: flex;\n  align-items: center;\n}\n\n.avatar-group {\n  display: flex;\n  margin-right: 10rpx;\n}\n\n.participant-avatar {\n  width: 50rpx;\n  height: 50rpx;\n  border-radius: 50%;\n  border: 2rpx solid #FFFFFF;\n  margin-left: -10rpx;\n}\n\n.participant-avatar:first-child {\n  margin-left: 0;\n}\n\n.avatar-more {\n  width: 50rpx;\n  height: 50rpx;\n  border-radius: 50%;\n  background: #F0F0F0;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 20rpx;\n  color: #666666;\n  margin-left: -10rpx;\n  border: 2rpx solid #FFFFFF;\n}\n\n.participant-count {\n  font-size: 24rpx;\n  color: #666666;\n}\n\n.record-actions {\n  display: flex;\n  align-items: center;\n}\n\n.action-btn {\n\t  display: flex;\n\t  align-items: center;\n\t  justify-content: center;\n\t  padding: 10rpx 20rpx;\n\t  border-radius: 30rpx;\n\t  font-size: 24rpx;\n\t  margin-left: 15rpx;\n\t}\n\t\n\t.action-btn.share {\n\t  border: 1rpx solid #DDDDDD;\n\t  color: #666666;\n\t}\n\t\n\t.action-icon {\n\t  margin-right: 6rpx;\n\t}\n\t\n\t.action-btn.primary {\n\t  box-shadow: 0 4rpx 8rpx rgba(0,0,0,0.1);\n\t}\n\t\n\t/* 空状态样式 */\n\t.empty-state {\n\t  display: flex;\n\t  flex-direction: column;\n\t  align-items: center;\n\t  justify-content: center;\n\t  padding: 100rpx 0;\n\t}\n\t\n\t.empty-image {\n\t  width: 200rpx;\n\t  height: 200rpx;\n\t  margin-bottom: 30rpx;\n\t}\n\t\n\t.empty-text {\n\t  font-size: 28rpx;\n\t  color: #999999;\n\t  margin-bottom: 30rpx;\n\t}\n\t\n\t.empty-state .action-btn {\n\t  padding: 15rpx 60rpx;\n\t  font-size: 28rpx;\n\t  color: #FFFFFF;\n\t}\n\t\n\t/* 筛选弹窗样式 */\n\t.filter-popup {\n\t  background: #FFFFFF;\n\t  border-top-left-radius: 30rpx;\n\t  border-top-right-radius: 30rpx;\n\t  padding: 30rpx;\n\t  max-height: 70vh;\n\t}\n\t\n\t.filter-header {\n\t  display: flex;\n\t  justify-content: space-between;\n\t  align-items: center;\n\t  margin-bottom: 30rpx;\n\t}\n\t\n\t.filter-title {\n\t  font-size: 32rpx;\n\t  font-weight: bold;\n\t  color: #333333;\n\t}\n\t\n\t.filter-close {\n\t  width: 60rpx;\n\t  height: 60rpx;\n\t  display: flex;\n\t  align-items: center;\n\t  justify-content: center;\n\t}\n\t\n\t.filter-content {\n\t  max-height: calc(70vh - 180rpx);\n\t  overflow-y: auto;\n\t}\n\t\n\t.filter-section {\n\t  margin-bottom: 30rpx;\n\t}\n\t\n\t.section-title {\n\t  font-size: 28rpx;\n\t  color: #333333;\n\t  font-weight: 500;\n\t  margin-bottom: 20rpx;\n\t}\n\t\n\t.filter-options {\n\t  display: flex;\n\t  flex-wrap: wrap;\n\t}\n\t\n\t.filter-option {\n\t  padding: 10rpx 30rpx;\n\t  border-radius: 30rpx;\n\t  font-size: 26rpx;\n\t  color: #666666;\n\t  background: #F5F5F5;\n\t  margin-right: 20rpx;\n\t  margin-bottom: 20rpx;\n\t}\n\t\n\t.filter-option.active {\n\t  background: rgba(255, 59, 105, 0.1);\n\t  color: #FF3B69;\n\t  border: 1rpx solid rgba(255, 59, 105, 0.3);\n\t}\n\t\n\t.filter-footer {\n\t  display: flex;\n\t  justify-content: space-between;\n\t  margin-top: 30rpx;\n\t  padding-top: 20rpx;\n\t  border-top: 1rpx solid #F0F0F0;\n\t}\n\t\n\t.filter-reset, .filter-apply {\n\t  flex: 1;\n\t  height: 80rpx;\n\t  display: flex;\n\t  align-items: center;\n\t  justify-content: center;\n\t  border-radius: 40rpx;\n\t  font-size: 28rpx;\n\t}\n\t\n\t.filter-reset {\n\t  background: #F5F5F5;\n\t  color: #666666;\n\t  margin-right: 20rpx;\n\t}\n\t\n\t.filter-apply {\n\t  background: linear-gradient(135deg, #FF3B69 0%, #FF7A9E 100%);\n\t  color: #FFFFFF;\n\t  box-shadow: 0 4rpx 8rpx rgba(255, 59, 105, 0.2);\n\t}\n\t</style>", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/subPackages/activity-showcase/pages/activity-records/index.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "onMounted", "uni"], "mappings": ";;;;;;;;;;;;;;;;AAgPA,UAAM,aAAaA,cAAAA,IAAI,CAAC;AACxB,UAAM,eAAeA,cAAAA,IAAI,KAAK;AAC9B,UAAM,cAAcA,cAAAA,IAAI,CAAA,CAAE;AAC1B,UAAM,cAAcA,cAAAA,IAAI,IAAI;AAG5B,UAAM,qBAAqBA,cAAAA,IAAI,CAAC;AAChC,UAAM,wBAAwBA,cAAG,IAAC,CAAC,CAAC,CAAC;AACrC,UAAM,sBAAsBA,cAAG,IAAC,CAAC,CAAC,CAAC;AAGnC,UAAM,cAAc;AAAA,MAClB,EAAE,OAAO,QAAQ,OAAO,MAAO;AAAA,MAC/B,EAAE,OAAO,QAAQ,OAAO,OAAQ;AAAA,MAChC,EAAE,OAAO,QAAQ,OAAO,QAAS;AAAA,MACjC,EAAE,OAAO,QAAQ,OAAO,eAAgB;AAAA,MACxC,EAAE,OAAO,OAAO,OAAO,SAAU;AAAA,IACnC;AAEA,UAAM,gBAAgB;AAAA,MACpB,EAAE,OAAO,QAAQ,OAAO,MAAO;AAAA,MAC/B,EAAE,OAAO,OAAO,OAAO,WAAY;AAAA,MACnC,EAAE,OAAO,OAAO,OAAO,UAAW;AAAA,MAClC,EAAE,OAAO,OAAO,OAAO,QAAS;AAAA,MAChC,EAAE,OAAO,OAAO,OAAO,YAAa;AAAA,IACtC;AAEA,UAAM,cAAc;AAAA,MAClB,EAAE,OAAO,QAAQ,OAAO,MAAO;AAAA,MAC/B,EAAE,OAAO,QAAQ,OAAO,UAAW;AAAA,MACnC,EAAE,OAAO,QAAQ,OAAO,SAAU;AAAA,MAClC,EAAE,OAAO,QAAQ,OAAO,SAAU;AAAA,MAClC,EAAE,OAAO,QAAQ,OAAO,UAAW;AAAA,MACnC,EAAE,OAAO,QAAQ,OAAO,UAAW;AAAA,IACrC;AAGA,UAAM,eAAe;AAAA,MACnB,EAAE,MAAM,MAAM,MAAM,OAAO,WAAW,UAAU,YAAY,sCAAuC;AAAA,MACnG,EAAE,MAAM,QAAQ,MAAM,gBAAgB,WAAW,UAAU,YAAY,wCAAyC;AAAA,MAChH,EAAE,MAAM,QAAQ,MAAM,WAAW,WAAW,UAAU,YAAY,mCAAoC;AAAA,MACtG,EAAE,MAAM,OAAO,MAAM,YAAY,WAAW,UAAU,YAAY,qCAAsC;AAAA,IAC1G;AAGA,UAAM,cAAc;AAAA,MAClB;AAAA,QACE,IAAI;AAAA,QACJ,OAAO;AAAA,QACP,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,MAAM;AAAA,QACN,MAAM;AAAA,QACN,UAAU;AAAA,QACV,OAAO;AAAA,QACP,cAAc;AAAA,UACZ;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACD;AAAA,QACD,YAAY;AAAA,MACb;AAAA,MACD;AAAA,QACE,IAAI;AAAA,QACJ,OAAO;AAAA,QACP,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,MAAM;AAAA,QACN,MAAM;AAAA,QACN,UAAU;AAAA,QACV,OAAO;AAAA,QACP,cAAc;AAAA,UACZ;AAAA,UACA;AAAA,UACA;AAAA,QACD;AAAA,QACD,YAAY;AAAA,MACb;AAAA,MACD;AAAA,QACE,IAAI;AAAA,QACJ,OAAO;AAAA,QACP,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,MAAM;AAAA,QACN,MAAM;AAAA,QACN,UAAU;AAAA,QACV,OAAO;AAAA,QACP,cAAc;AAAA,UACZ;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACD;AAAA,QACD,YAAY;AAAA,MACb;AAAA,MACD;AAAA,QACE,IAAI;AAAA,QACJ,OAAO;AAAA,QACP,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,MAAM;AAAA,QACN,MAAM;AAAA,QACN,UAAU;AAAA,QACV,OAAO;AAAA,QACP,cAAc;AAAA,UACZ;AAAA,UACA;AAAA,QACD;AAAA,QACD,YAAY;AAAA,MACb;AAAA,MACD;AAAA,QACE,IAAI;AAAA,QACJ,OAAO;AAAA,QACP,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,MAAM;AAAA,QACN,MAAM;AAAA,QACN,UAAU;AAAA,QACV,OAAO;AAAA,QACP,cAAc;AAAA,UACZ;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACD;AAAA,QACD,YAAY;AAAA,MACb;AAAA,IACH;AAGAC,kBAAAA,UAAU,MAAM;AACd;IACF,CAAC;AAGD,UAAM,cAAc,MAAM;AAExB,kBAAY,QAAQ;AAAA,IACtB;AAEA,UAAM,mBAAmB,CAAC,SAAS;AACjC,UAAI,SAAS,OAAO;AAClB,eAAO,YAAY;AAAA,MACpB;AACD,aAAO,YAAY,MAAM,OAAO,YAAU,OAAO,eAAe,IAAI;AAAA,IACtE;AAEA,UAAM,YAAY,CAAC,UAAU;AAC3B,iBAAW,QAAQ;AAAA,IACrB;AAEA,UAAM,iBAAiB,CAAC,MAAM;AAC5B,iBAAW,QAAQ,EAAE,OAAO;AAAA,IAC9B;AAEA,UAAM,YAAY,MAAM;AACtB,mBAAa,QAAQ;AACrB,iBAAW,MAAM;AACf;AACA,qBAAa,QAAQ;AAAA,MACtB,GAAE,GAAI;AAAA,IACT;AAEA,UAAM,WAAW,MAAM;AAErBC,oBAAAA,MAAY,MAAA,OAAA,yEAAA,QAAQ;AAAA,IACtB;AAEA,UAAM,gBAAgB,CAAC,WAAW;AAChC,YAAM,YAAY;AAAA,QAChB,YAAY;AAAA,QACZ,WAAW;AAAA,QACX,SAAS;AAAA,QACT,aAAa;AAAA,MACjB;AACE,aAAO,UAAU,MAAM,KAAK;AAAA,IAC9B;AAEA,UAAM,sBAAsB,CAAC,WAAW;AACtC,YAAM,QAAQ;AAAA,QACZ,YAAY;AAAA,QACZ,WAAW;AAAA,QACX,SAAS;AAAA,QACT,aAAa;AAAA,MACjB;AACE,aAAO,MAAM,MAAM,KAAK;AAAA,IAC1B;AAEA,UAAM,uBAAuB,CAAC,WAAW;AACvC,YAAM,YAAY;AAAA,QAChB,YAAY;AAAA,QACZ,WAAW;AAAA,QACX,SAAS;AAAA,QACT,aAAa;AAAA,MACjB;AACE,aAAO,UAAU,MAAM,KAAK;AAAA,IAC9B;AAEA,UAAM,0BAA0B,CAAC,WAAW;AAC1C,YAAM,aAAa;AAAA,QACjB,YAAY;AAAA,QACZ,WAAW;AAAA,QACX,SAAS;AAAA,QACT,aAAa;AAAA,MACjB;AACE,aAAO,WAAW,MAAM,KAAK;AAAA,IAC/B;AAEA,UAAM,sBAAsB,CAAC,WAAW;AACtC,cAAQ,OAAO,QAAM;AAAA,QACnB,KAAK;AACH,qBAAW,iEAAiE,OAAO,EAAE,kBAAkB;AACvG;AAAA,QACF,KAAK;AACH,qBAAW,iEAAiE,OAAO,EAAE,EAAE;AACvF;AAAA,QACF,KAAK;AACH,qBAAW,iEAAiE,OAAO,EAAE,aAAa;AAClG;AAAA,QACF;AACE,qBAAW,iEAAiE,OAAO,EAAE,EAAE;AAAA,MAC1F;AAAA,IACH;AAEA,UAAM,qBAAqB,CAAC,WAAW;AACrC,iBAAW,iEAAiE,OAAO,EAAE,EAAE;AAAA,IACzF;AAEA,UAAM,gBAAgB,CAAC,WAAW;AAChCA,oBAAAA,MAAI,cAAc;AAAA,QAChB,iBAAiB;AAAA,QACjB,OAAO,CAAC,mBAAmB,eAAe;AAAA,MAC9C,CAAG;AAAA,IACH;AAEA,UAAM,aAAa,MAAM;AACvB,kBAAY,MAAM;IACpB;AAEA,UAAM,cAAc,MAAM;AACxB,kBAAY,MAAM;IACpB;AAEA,UAAM,mBAAmB,CAAC,UAAU;AAClC,yBAAmB,QAAQ;AAAA,IAC7B;AAEA,UAAM,qBAAqB,CAAC,UAAU;AACpC,YAAM,WAAW,sBAAsB,MAAM,QAAQ,KAAK;AAC1D,UAAI,UAAU,GAAG;AAEf,8BAAsB,QAAQ,CAAC,CAAC;AAAA,MACpC,OAAS;AAEL,YAAI,sBAAsB,MAAM,SAAS,CAAC,GAAG;AAC3C,gCAAsB,QAAQ,sBAAsB,MAAM,OAAO,UAAQ,SAAS,CAAC;AAAA,QACpF;AAGD,YAAI,aAAa,IAAI;AACnB,gCAAsB,MAAM,OAAO,UAAU,CAAC;AAE9C,cAAI,sBAAsB,MAAM,WAAW,GAAG;AAC5C,kCAAsB,QAAQ,CAAC,CAAC;AAAA,UACjC;AAAA,QACP,OAAW;AACL,gCAAsB,MAAM,KAAK,KAAK;AAAA,QACvC;AAAA,MACF;AAAA,IACH;AAEA,UAAM,mBAAmB,CAAC,UAAU;AAClC,YAAM,WAAW,oBAAoB,MAAM,QAAQ,KAAK;AACxD,UAAI,UAAU,GAAG;AAEf,4BAAoB,QAAQ,CAAC,CAAC;AAAA,MAClC,OAAS;AAEL,YAAI,oBAAoB,MAAM,SAAS,CAAC,GAAG;AACzC,8BAAoB,QAAQ,oBAAoB,MAAM,OAAO,UAAQ,SAAS,CAAC;AAAA,QAChF;AAGD,YAAI,aAAa,IAAI;AACnB,8BAAoB,MAAM,OAAO,UAAU,CAAC;AAE5C,cAAI,oBAAoB,MAAM,WAAW,GAAG;AAC1C,gCAAoB,QAAQ,CAAC,CAAC;AAAA,UAC/B;AAAA,QACP,OAAW;AACL,8BAAoB,MAAM,KAAK,KAAK;AAAA,QACrC;AAAA,MACF;AAAA,IACH;AAEA,UAAM,cAAc,MAAM;AACxB,yBAAmB,QAAQ;AAC3B,4BAAsB,QAAQ,CAAC,CAAC;AAChC,0BAAoB,QAAQ,CAAC,CAAC;AAAA,IAChC;AAEA,UAAM,cAAc,MAAM;AAExBA,oBAAAA,MAAY,MAAA,OAAA,yEAAA,QAAQ;AAAA,QAClB,MAAM,YAAY,mBAAmB,KAAK,EAAE;AAAA,QAC5C,QAAQ,sBAAsB,MAAM,IAAI,WAAS,cAAc,KAAK,EAAE,KAAK;AAAA,QAC3E,MAAM,oBAAoB,MAAM,IAAI,WAAS,YAAY,KAAK,EAAE,KAAK;AAAA,MACzE,CAAG;AAGDA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,MACV,CAAG;AAED;IACF;AAEA,UAAM,SAAS,MAAM;AACnBA,oBAAG,MAAC,aAAY;AAAA,IAClB;AAEA,UAAM,aAAa,CAAC,QAAQ;AAC1BA,oBAAAA,MAAI,WAAW,EAAE,IAAG,CAAE;AAAA,IACxB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACxjBA,GAAG,WAAW,eAAe;"}