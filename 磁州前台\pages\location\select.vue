<template>
  <view class="location-select-container">
    <!-- 搜索框 -->
    <view class="search-box">
      <view class="search-input-box">
        <text class="iconfont icon-search"></text>
        <input type="text" v-model="keyword" class="search-input" placeholder="搜索地址" confirm-type="search" @confirm="searchLocation" />
        <text class="clear-btn" v-if="keyword" @click="clearKeyword">×</text>
      </view>
      <button class="cancel-btn" @click="goBack">取消</button>
    </view>
    
    <!-- 当前位置 -->
    <view class="current-location-section">
      <view class="section-title">当前位置</view>
      <view class="current-location-box" @click="useCurrentLocation">
        <view class="location-icon-box">
          <text class="iconfont icon-location"></text>
        </view>
        <view class="location-info">
          <text class="location-name">{{ currentLocation ? currentLocation.address : '获取当前位置' }}</text>
          <text class="location-detail" v-if="currentLocation">{{ currentLocation.location }}</text>
        </view>
        <view class="location-action">
          <text class="use-btn">使用</text>
        </view>
      </view>
    </view>
    
    <!-- 搜索结果 -->
    <view class="search-result-section" v-if="searchResults.length > 0">
      <view class="section-title">搜索结果</view>
      <view class="location-list">
        <view class="location-item" v-for="(item, index) in searchResults" :key="index" @click="selectLocation(item)">
          <view class="location-icon-box">
            <text class="iconfont icon-pin"></text>
          </view>
          <view class="location-info">
            <text class="location-name">{{ item.name }}</text>
            <text class="location-detail">{{ item.address }}</text>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 历史记录 -->
    <view class="history-section" v-if="historyLocations.length > 0 && !keyword">
      <view class="section-title-row">
        <text class="section-title">历史记录</text>
        <text class="clear-history" @click="clearHistory">清空</text>
      </view>
      <view class="location-list">
        <view class="location-item" v-for="(item, index) in historyLocations" :key="index" @click="selectLocation(item)">
          <view class="location-icon-box">
            <text class="iconfont icon-time"></text>
          </view>
          <view class="location-info">
            <text class="location-name">{{ item.name }}</text>
            <text class="location-detail">{{ item.address }}</text>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 搜索为空提示 -->
    <view class="empty-result" v-if="keyword && searchResults.length === 0">
      <image src="/static/images/empty-search.png" mode="aspectFit" class="empty-image"></image>
      <text class="empty-text">没有找到相关地址</text>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue';
import { getCurrentLocation, refreshLocation } from '@/utils/locationService.js';

// 搜索关键词
const keyword = ref('');
// 当前位置
const currentLocation = ref(null);
// 搜索结果
const searchResults = ref([]);
// 历史记录
const historyLocations = ref([]);

// 清除关键词
const clearKeyword = () => {
  keyword.value = '';
  searchResults.value = [];
};

// 返回上一页
const goBack = () => {
  uni.navigateBack();
};

// 使用当前位置
const useCurrentLocation = () => {
  if (!currentLocation.value) {
    uni.showLoading({
      title: '获取位置中...'
    });
    
    refreshLocation().then(location => {
      uni.hideLoading();
      currentLocation.value = location;
      selectLocation({
        name: location.address || '当前位置',
        address: location.location || '',
        latitude: location.latitude,
        longitude: location.longitude
      });
    }).catch(() => {
      uni.hideLoading();
      // 获取失败时提供默认位置选项
      uni.showModal({
        title: '获取位置失败',
        content: '是否使用默认位置？',
        confirmText: '使用默认位置',
        cancelText: '取消',
        success: (res) => {
          if (res.confirm) {
            useDefaultLocation();
          }
        }
      });
    });
  } else {
    selectLocation({
      name: currentLocation.value.address || '当前位置',
      address: currentLocation.value.location || '',
      latitude: currentLocation.value.latitude,
      longitude: currentLocation.value.longitude
    });
  }
};

// 添加使用默认位置函数
const useDefaultLocation = () => {
  const defaultLoc = {
    name: '磁县',
    address: '河北省邯郸市磁县',
    latitude: 36.313076,
    longitude: 114.347312
  };
  selectLocation(defaultLoc);
};

// 搜索位置
const searchLocation = () => {
  if (!keyword.value.trim()) {
    return;
  }
  
  uni.showLoading({
    title: '搜索中...'
  });
  
  // 这里应该调用地图服务API进行搜索
  // 示例中使用模拟数据
  setTimeout(() => {
    searchResults.value = [
      {
        name: '搜索结果1',
        address: keyword.value + '附近的位置1',
        latitude: 39.908692,
        longitude: 116.397477
      },
      {
        name: '搜索结果2',
        address: keyword.value + '附近的位置2',
        latitude: 39.908692,
        longitude: 116.397477
      }
    ];
    uni.hideLoading();
  }, 1000);
};

// 选择位置
const selectLocation = (location) => {
  // 保存到历史记录
  saveToHistory(location);
  
  // 返回并传递选择的位置
  const pages = getCurrentPages();
  const prevPage = pages[pages.length - 2];
  
  // 如果有上一页，设置上一页数据
  if (prevPage) {
    prevPage.$vm.setSelectedLocation && prevPage.$vm.setSelectedLocation(location);
  }
  
  // 返回上一页
  uni.navigateBack();
};

// 保存到历史记录
const saveToHistory = (location) => {
  // 检查是否已存在
  const existIndex = historyLocations.value.findIndex(item => 
    item.latitude === location.latitude && item.longitude === location.longitude
  );
  
  // 如果已存在，先删除
  if (existIndex > -1) {
    historyLocations.value.splice(existIndex, 1);
  }
  
  // 添加到开头
  historyLocations.value.unshift(location);
  
  // 最多保存10条记录
  if (historyLocations.value.length > 10) {
    historyLocations.value = historyLocations.value.slice(0, 10);
  }
  
  // 保存到本地
  uni.setStorageSync('location_history', historyLocations.value);
};

// 清空历史记录
const clearHistory = () => {
  uni.showModal({
    title: '提示',
    content: '确定要清空历史记录吗？',
    success: (res) => {
      if (res.confirm) {
        historyLocations.value = [];
        uni.setStorageSync('location_history', []);
      }
    }
  });
};

onMounted(() => {
  // 获取当前位置
  currentLocation.value = getCurrentLocation();
  
  // 如果无位置信息，尝试刷新
  if (!currentLocation.value) {
    refreshLocation().then(location => {
      currentLocation.value = location;
    }).catch(console.error);
  }
  
  // 加载历史记录
  const history = uni.getStorageSync('location_history');
  if (history) {
    historyLocations.value = history;
  }
});
</script>

<style lang="scss">
.location-select-container {
  padding: 20rpx;
  background-color: #f7f7f7;
  min-height: 100vh;
}

.search-box {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
}

.search-input-box {
  flex: 1;
  display: flex;
  align-items: center;
  background-color: #fff;
  border-radius: 8rpx;
  padding: 0 20rpx;
  height: 80rpx;
}

.search-input {
  flex: 1;
  height: 80rpx;
  font-size: 28rpx;
  margin: 0 20rpx;
}

.clear-btn {
  font-size: 40rpx;
  color: #999;
  padding: 0 10rpx;
}

.cancel-btn {
  width: 120rpx;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  font-size: 28rpx;
  margin-left: 20rpx;
  background: none;
  padding: 0;
  color: #0052CC;
}

.section-title {
  font-size: 28rpx;
  color: #666;
  margin: 20rpx 0;
}

.section-title-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 20rpx 0;
}

.clear-history {
  font-size: 24rpx;
  color: #999;
}

.current-location-box, .location-item {
  display: flex;
  align-items: center;
  background-color: #fff;
  padding: 20rpx;
  border-radius: 8rpx;
  margin-bottom: 20rpx;
}

.location-icon-box {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #0052CC;
  font-size: 32rpx;
  margin-right: 20rpx;
}

.location-info {
  flex: 1;
  overflow: hidden;
}

.location-name {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 6rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.location-detail {
  font-size: 24rpx;
  color: #999;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.location-action {
  margin-left: 20rpx;
}

.use-btn {
  display: inline-block;
  padding: 6rpx 20rpx;
  background-color: rgba(0, 82, 204, 0.1);
  color: #0052CC;
  border-radius: 30rpx;
  font-size: 24rpx;
}

.empty-result {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}

.empty-image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 30rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}
</style> 