"use strict";
const common_vendor = require("../../common/vendor.js");
const common_assets = require("../../common/assets.js");
if (!Array) {
  const _component_RedPacketEntry = common_vendor.resolveComponent("RedPacketEntry");
  _component_RedPacketEntry();
}
const _sfc_main = {
  __name: "publish",
  setup(__props) {
    const imageLoadStatus = common_vendor.ref({});
    const preloadComplete = common_vendor.ref(false);
    const testPublishCount = common_vendor.ref(0);
    const merchantTestCount = common_vendor.ref(0);
    const form = common_vendor.ref({
      redPacket: null
    });
    const setPagePriority = () => {
      if (common_vendor.index.canIUse && common_vendor.index.canIUse("setPageInfo")) {
        try {
          common_vendor.wx$1.setPageInfo({
            pageType: "homePage",
            performanceLevel: "high"
          });
        } catch (e) {
          common_vendor.index.__f__("error", "at pages/publish/publish.vue:211", "设置页面优先级失败", e);
        }
      }
    };
    const preloadImages = () => {
      const imageList = [
        "/static/images/tabbar/到家服务.png",
        "/static/images/tabbar/寻找服务.png",
        "/static/images/tabbar/招聘信息.png",
        "/static/images/tabbar/求职信息.png",
        "/static/images/tabbar/出租.png",
        "/static/images/tabbar/房屋出售.png",
        "/static/images/tabbar/二手车辆.png",
        "/static/images/tabbar/宠物信息.png",
        "/static/images/tabbar/商家活动.png",
        "/static/images/tabbar/车辆服务.png",
        "/static/images/tabbar/二手闲置.png",
        "/static/images/tabbar/磁州拼车.png",
        "/static/images/tabbar/生意转让.png",
        "/static/images/tabbar/教育培训.png",
        "/static/images/tabbar/其他服务.png"
      ];
      let loadedCount = 0;
      const totalImages = imageList.length;
      imageList.forEach((img) => {
        common_vendor.index.getImageInfo({
          src: img,
          success: () => {
            imageLoadStatus.value[img] = true;
            loadedCount++;
            if (loadedCount === totalImages) {
              preloadComplete.value = true;
            }
          },
          fail: () => {
            common_vendor.index.__f__("error", "at pages/publish/publish.vue:250", "图片加载失败:", img);
            loadedCount++;
            if (loadedCount === totalImages) {
              preloadComplete.value = true;
            }
          }
        });
      });
      setTimeout(() => {
        preloadComplete.value = true;
      }, 1e3);
    };
    const loadTestPublishCount = () => {
      try {
        const testData = common_vendor.index.getStorageSync("testPublishData") || [];
        testPublishCount.value = testData.length;
      } catch (e) {
        common_vendor.index.__f__("error", "at pages/publish/publish.vue:270", "加载测试数据计数失败", e);
        testPublishCount.value = 0;
      }
    };
    const loadMerchantTestCount = () => {
      try {
        const merchantTestData = common_vendor.index.getStorageSync("merchantTestData") || [];
        merchantTestCount.value = merchantTestData.length;
      } catch (e) {
        common_vendor.index.__f__("error", "at pages/publish/publish.vue:281", "加载商家入驻测试数据计数失败", e);
        merchantTestCount.value = 0;
      }
    };
    const handleMerchantApply = () => {
      common_vendor.index.navigateTo({
        url: "/pages/business/join"
      });
    };
    const testMerchantJoin = () => {
      common_vendor.index.showModal({
        title: "商家入驻测试",
        content: "请输入任意内容，完成快速商家入驻测试",
        editable: true,
        placeholderText: "输入商家名称",
        success: (res) => {
          if (res.confirm) {
            const shopName = res.content || "测试商家";
            common_vendor.index.showLoading({
              title: "处理中",
              mask: true
            });
            setTimeout(() => {
              common_vendor.index.hideLoading();
              try {
                const merchantTestData = common_vendor.index.getStorageSync("merchantTestData") || [];
                const newMerchantData = {
                  id: Date.now().toString(),
                  shopName,
                  address: "测试地址",
                  category: "测试类目",
                  scale: "1-10人",
                  businessTime: "09:00-21:00",
                  contactPhone: "***********",
                  description: "这是一个测试商家",
                  createTime: (/* @__PURE__ */ new Date()).toLocaleString(),
                  joinMethod: "free",
                  shopImage: "/static/images/tabbar/商家入驻.png",
                  logo: "/static/images/tabbar/商家入驻.png",
                  qrcode: "",
                  album: []
                };
                merchantTestData.push(newMerchantData);
                common_vendor.index.setStorageSync("merchantTestData", merchantTestData);
                merchantTestCount.value = merchantTestData.length;
                common_vendor.index.setStorageSync("lastMerchantId", newMerchantData.id);
                common_vendor.index.setStorageSync("lastMerchantData", newMerchantData);
                common_vendor.index.navigateTo({
                  url: `/pages/business/success?id=${newMerchantData.id}&shopName=${encodeURIComponent(shopName)}&memberType=${encodeURIComponent("测试版")}&isTest=true`,
                  fail: (err) => {
                    common_vendor.index.__f__("error", "at pages/publish/publish.vue:345", "跳转到商家入驻成功页面失败:", err);
                    common_vendor.index.navigateTo({
                      url: "/pages/publish/success?id=" + newMerchantData.id + "&type=merchant",
                      fail: (err2) => {
                        common_vendor.index.__f__("error", "at pages/publish/publish.vue:350", "跳转到通用成功页面也失败:", err2);
                        common_vendor.index.showToast({
                          title: "入驻成功",
                          icon: "success",
                          duration: 2e3
                        });
                      }
                    });
                  }
                });
              } catch (e) {
                common_vendor.index.__f__("error", "at pages/publish/publish.vue:361", "存储商家测试数据失败", e);
                common_vendor.index.showToast({
                  title: "入驻成功",
                  icon: "success",
                  duration: 2e3
                });
              }
            }, 800);
          }
        }
      });
    };
    const navigateToPublishDetail = (item) => {
      if (item.type === "test") {
        setTimeout(() => {
          common_vendor.index.showModal({
            title: "测试发布",
            content: "请输入任意内容进行测试发布",
            editable: true,
            placeholderText: "随意输入一个字符",
            success: (res) => {
              if (res.confirm) {
                const content = res.content || "测试内容";
                common_vendor.index.showLoading({
                  title: "发布中",
                  mask: true
                });
                setTimeout(() => {
                  common_vendor.index.hideLoading();
                  try {
                    const testData = common_vendor.index.getStorageSync("testPublishData") || [];
                    const newData = {
                      id: Date.now().toString(),
                      type: "test",
                      category: "测试发布",
                      categoryName: "测试发布",
                      title: "测试发布",
                      content,
                      createTime: (/* @__PURE__ */ new Date()).toLocaleString()
                    };
                    testData.push(newData);
                    common_vendor.index.setStorageSync("testPublishData", testData);
                    testPublishCount.value = testData.length;
                    common_vendor.index.setStorageSync("lastPublishId", newData.id);
                    common_vendor.index.setStorageSync("lastPublishData", newData);
                    common_vendor.index.navigateTo({
                      url: "/pages/publish/success?id=" + newData.id,
                      fail: (err) => {
                        common_vendor.index.__f__("error", "at pages/publish/publish.vue:411", "跳转到发布成功页面失败:", err);
                        common_vendor.index.showToast({
                          title: "发布成功",
                          icon: "success",
                          duration: 2e3
                        });
                      }
                    });
                  } catch (e) {
                    common_vendor.index.__f__("error", "at pages/publish/publish.vue:420", "存储测试数据失败", e);
                    common_vendor.index.showToast({
                      title: "发布成功",
                      icon: "success",
                      duration: 2e3
                    });
                  }
                }, 800);
              }
            }
          });
        }, 300);
        return;
      }
      common_vendor.index.showLoading({
        title: "加载中",
        mask: true
      });
      const params = {
        type: item.type,
        name: item.name,
        category: item.name,
        categoryType: item.type
      };
      const queryString = Object.keys(params).map((key) => `${key}=${encodeURIComponent(params[key])}`).join("&");
      try {
        common_vendor.index.navigateTo({
          url: `/pages/publish/detail?${queryString}&isPublish=true`,
          success: function() {
            common_vendor.index.__f__("log", "at pages/publish/publish.vue:452", "导航成功");
            common_vendor.index.hideLoading();
          },
          fail: function(err) {
            common_vendor.index.__f__("error", "at pages/publish/publish.vue:456", "导航失败:", err);
            common_vendor.index.navigateTo({
              url: `../publish/detail?${queryString}&isPublish=true`,
              success: function() {
                common_vendor.index.hideLoading();
              },
              fail: function(err2) {
                common_vendor.index.__f__("error", "at pages/publish/publish.vue:463", "第二次导航失败:", err2);
                common_vendor.index.hideLoading();
                common_vendor.index.showToast({
                  title: "页面跳转失败，请重启应用",
                  icon: "none"
                });
              }
            });
          }
        });
      } catch (e) {
        common_vendor.index.__f__("error", "at pages/publish/publish.vue:474", "导航异常:", e);
        common_vendor.index.hideLoading();
        common_vendor.index.showToast({
          title: "系统繁忙，请稍后再试",
          icon: "none"
        });
      }
    };
    const showTestDataManager = () => {
      try {
        const testData = common_vendor.index.getStorageSync("testPublishData") || [];
        if (testData.length === 0) {
          common_vendor.index.showToast({
            title: "暂无测试数据",
            icon: "none"
          });
          return;
        }
        common_vendor.index.showActionSheet({
          itemList: ["查看测试数据", "清除测试数据"],
          success: (res) => {
            if (res.tapIndex === 0) {
              viewTestData(testData);
            } else if (res.tapIndex === 1) {
              clearTestData();
            }
          }
        });
      } catch (e) {
        common_vendor.index.__f__("error", "at pages/publish/publish.vue:504", "打开测试数据管理器失败", e);
        common_vendor.index.showToast({
          title: "操作失败，请重试",
          icon: "none"
        });
      }
    };
    const viewTestData = (testData) => {
      const dataList = testData.map((item, index) => {
        return `${index + 1}. ${item.content} (${item.date})`;
      }).join("\n\n");
      common_vendor.index.showModal({
        title: "测试数据列表",
        content: dataList,
        confirmText: "关闭",
        showCancel: false
      });
    };
    const clearTestData = () => {
      common_vendor.index.showModal({
        title: "确认清除",
        content: "确定要清除所有测试数据吗？",
        success: (res) => {
          if (res.confirm) {
            try {
              common_vendor.index.removeStorageSync("testPublishData");
              testPublishCount.value = 0;
              common_vendor.index.showToast({
                title: "测试数据已清除",
                icon: "success"
              });
            } catch (e) {
              common_vendor.index.__f__("error", "at pages/publish/publish.vue:538", "清除测试数据失败", e);
              common_vendor.index.showToast({
                title: "清除失败，请重试",
                icon: "none"
              });
            }
          }
        }
      });
    };
    const navigateToServiceCategory = () => {
      common_vendor.index.__f__("log", "at pages/publish/publish.vue:550", "跳转到服务类型选择页面");
      common_vendor.index.navigateTo({
        url: "/pages/publish/service-category"
      });
    };
    common_vendor.onLoad(() => {
      setPagePriority();
      preloadImages();
      loadTestPublishCount();
      loadMerchantTestCount();
    });
    common_vendor.onShow(() => {
      loadTestPublishCount();
      loadMerchantTestCount();
    });
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_assets._imports_0$7,
        b: common_vendor.o((...args) => _ctx.goBack && _ctx.goBack(...args)),
        c: common_assets._imports_2$4,
        d: common_vendor.o(handleMerchantApply),
        e: common_vendor.o(handleMerchantApply),
        f: common_assets._imports_2$6,
        g: common_vendor.o(navigateToServiceCategory),
        h: common_assets._imports_3$6,
        i: common_vendor.o(($event) => navigateToPublishDetail({
          name: "寻找服务",
          type: "find_service"
        })),
        j: common_assets._imports_4$2,
        k: common_vendor.o(($event) => navigateToPublishDetail({
          name: "招聘信息",
          type: "hire"
        })),
        l: common_assets._imports_5$2,
        m: common_vendor.o(($event) => navigateToPublishDetail({
          name: "求职信息",
          type: "job_wanted"
        })),
        n: common_assets._imports_6$2,
        o: common_vendor.o(($event) => navigateToPublishDetail({
          name: "房屋出租",
          type: "house_rent"
        })),
        p: common_assets._imports_7$1,
        q: common_vendor.o(($event) => navigateToPublishDetail({
          name: "房屋出售",
          type: "house_sell"
        })),
        r: common_assets._imports_8$1,
        s: common_vendor.o(($event) => navigateToPublishDetail({
          name: "二手车辆",
          type: "used_car"
        })),
        t: common_assets._imports_9$2,
        v: common_vendor.o(($event) => navigateToPublishDetail({
          name: "宠物信息",
          type: "pet"
        })),
        w: common_assets._imports_3$7,
        x: common_vendor.o(($event) => navigateToPublishDetail({
          name: "商家活动",
          type: "merchant_activity"
        })),
        y: common_assets._imports_11$1,
        z: common_vendor.o(($event) => navigateToPublishDetail({
          name: "车辆服务",
          type: "car_service"
        })),
        A: common_assets._imports_12$2,
        B: common_vendor.o(($event) => navigateToPublishDetail({
          name: "二手闲置",
          type: "second_hand"
        })),
        C: common_assets._imports_13$1,
        D: common_vendor.o(($event) => navigateToPublishDetail({
          name: "磁州拼车",
          type: "carpool"
        })),
        E: common_assets._imports_14$2,
        F: common_vendor.o(($event) => navigateToPublishDetail({
          name: "生意转让",
          type: "business_transfer"
        })),
        G: common_assets._imports_15$1,
        H: common_vendor.o(($event) => navigateToPublishDetail({
          name: "教育培训",
          type: "education"
        })),
        I: common_assets._imports_16$1,
        J: common_vendor.o(($event) => navigateToPublishDetail({
          name: "其他服务",
          type: "other_service"
        })),
        K: common_assets._imports_17,
        L: common_vendor.o(($event) => navigateToPublishDetail({
          name: "婚恋交友",
          type: "dating"
        })),
        M: testPublishCount.value > 0
      }, testPublishCount.value > 0 ? {
        N: common_vendor.t(testPublishCount.value)
      } : {}, {
        O: common_vendor.o(($event) => navigateToPublishDetail({
          name: "测试发布",
          type: "test"
        })),
        P: common_vendor.o(showTestDataManager),
        Q: merchantTestCount.value > 0
      }, merchantTestCount.value > 0 ? {
        R: common_vendor.t(merchantTestCount.value)
      } : {}, {
        S: common_vendor.o(testMerchantJoin),
        T: common_vendor.o(($event) => form.value.redPacket = $event),
        U: common_vendor.p({
          modelValue: form.value.redPacket
        })
      });
    };
  }
};
wx.createPage(_sfc_main);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/publish/publish.js.map
