/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 移除阿里妈妈字体引用，改用系统默认字体 */
/* 移除原有阿里妈妈字体定义
$uni-font-family: 'AlimamaShuHeiTi';
$uni-font-family-text: 'AlimamaShuHeiTi', -apple-system, BlinkMacSystemFont, 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;
*/
body.data-v-8ed27b9c, html.data-v-8ed27b9c, #app.data-v-8ed27b9c, .index-container.data-v-8ed27b9c {
  font-family: "AlimamaShuHeiTi", -apple-system, BlinkMacSystemFont, "Helvetica Neue", "PingFang SC", "Microsoft YaHei", sans-serif;
}
.data-v-8ed27b9c:root {
  /* 颜色系统 - 苹果风格 */
  --brand-primary: #0A84FF;
  /* 苹果蓝 */
  --brand-secondary: #5E5CE6;
  /* 苹果紫 */
  --accent-purple: #5E5CE6;
  --accent-pink: #FF2D55;
  --accent-orange: #FF9500;
  --accent-yellow: #FFCC00;
  --accent-green: #30D158;
  --accent-teal: #64D2FF;
  /* 中性色 */
  --bg-primary: #F2F3F7;
  --bg-secondary: #FFFFFF;
  --bg-tertiary: #FAFAFA;
  --bg-card: #FFFFFF;
  /* 文本颜色 */
  --text-primary: #1C1C1E;
  --text-secondary: #3A3A3C;
  --text-tertiary: #8E8E93;
  --text-quaternary: #C7C7CC;
  /* 边框颜色 */
  --border-light: rgba(60, 60, 67, 0.06);
  --border-medium: rgba(60, 60, 67, 0.12);
  /* 功能色 */
  --success: #30D158;
  --warning: #FF9F0A;
  --danger: #FF453A;
  --info: #64D2FF;
  /* 间距 */
  --space-xs: 4px;
  --space-sm: 8px;
  --space-md: 16px;
  --space-lg: 24px;
  --space-xl: 32px;
  /* 圆角 - 更新为35度圆角 */
  --radius-sm: 12px;
  --radius-md: 20px;
  --radius-lg: 35px;
  /* 阴影 - 更精致的阴影系统 */
  --shadow-xs: 0 2px 8px rgba(0, 0, 0, 0.04), 0 1px 3px rgba(0, 0, 0, 0.02);
  --shadow-sm: 0 4px 12px rgba(0, 0, 0, 0.06), 0 2px 6px rgba(0, 0, 0, 0.03);
  --shadow-md: 0 8px 24px rgba(0, 0, 0, 0.08), 0 4px 12px rgba(0, 0, 0, 0.04);
  --shadow-lg: 0 16px 32px rgba(0, 0, 0, 0.1), 0 8px 16px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 24px 48px rgba(0, 0, 0, 0.12), 0 12px 24px rgba(0, 0, 0, 0.06);
}

/* 页面容器 */
.merchant-dashboard.data-v-8ed27b9c {
  background-color: var(--bg-primary);
  min-height: 100vh;
  padding-bottom: 100px;
  position: relative;
  overflow-x: hidden;
}

/* 状态栏背景 */
.status-bar-bg.data-v-8ed27b9c {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: var(--status-bar-height, 44px);
  background: #0052CC;
  z-index: 100;
}

/* 顶部导航栏 - 恢复原始样式 */
.navbar.data-v-8ed27b9c {
  position: fixed;
  top: var(--status-bar-height, 44px);
  left: 0;
  right: 0;
  height: 62px;
  /* 再增加高度3px，从59px到62px */
  background: #0052CC;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 100;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}
.navbar-left.data-v-8ed27b9c {
  position: absolute;
  left: 16px;
  top: 0;
  bottom: 0;
  display: flex;
  align-items: center;
}
.back-icon.data-v-8ed27b9c {
  width: 24px;
  height: 24px;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='white'%3E%3Cpath d='M15.41 7.41L14 6l-6 6 6 6 1.41-1.41L10.83 12z'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
}
.navbar-title.data-v-8ed27b9c {
  color: white;
  font-size: 20px;
  /* 增加字体大小 */
  font-weight: 700;
  /* 增加字体粗细 */
  letter-spacing: 0.5px;
}
.navbar-right.data-v-8ed27b9c {
  position: absolute;
  right: 16px;
  top: 0;
  bottom: 0;
  display: flex;
  align-items: center;
}

/* 主内容区域 */
.content-container.data-v-8ed27b9c {
  padding: calc(var(--status-bar-height, 44px) + 62px + 16px) 16px 0;
  /* 调整padding，将59px改为62px */
  display: flex;
  flex-direction: column;
  gap: 16px;
  /* 减小卡片之间的间距 */
}

/* 业绩概览卡片 - 更精致的设计 */
.performance-card.data-v-8ed27b9c {
  background: linear-gradient(145deg, #ffffff, #f8f9fc);
  border-radius: var(--radius-lg);
  padding: 18px;
  /* 减小内边距 */
  position: relative;
  box-shadow: var(--shadow-md);
  overflow: hidden;
  border: 1px solid rgba(255, 255, 255, 0.8);
  width: 90%;
  /* 调整宽度 */
  margin-left: auto;
  margin-right: auto;
  transform: translateZ(0);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
}
.performance-card.data-v-8ed27b9c::before {
  content: "";
  position: absolute;
  top: 0;
  right: 0;
  width: 180px;
  height: 180px;
  background: radial-gradient(circle, rgba(46, 91, 255, 0.06) 0%, transparent 70%);
  border-radius: 50%;
  transform: translate(30%, -30%);
}
.performance-card.data-v-8ed27b9c::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  width: 150px;
  height: 150px;
  background: radial-gradient(circle, rgba(46, 91, 255, 0.04) 0%, transparent 70%);
  border-radius: 50%;
  transform: translate(-30%, 30%);
}
.performance-header.data-v-8ed27b9c {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  /* 减小下边距 */
  position: relative;
  z-index: 2;
}
.performance-title.data-v-8ed27b9c {
  flex: 1;
}
.greeting-container.data-v-8ed27b9c {
  display: flex;
  align-items: center;
  margin-top: 0;
}
.greeting.data-v-8ed27b9c {
  font-size: 18px;
  /* 减小字体大小 */
  font-weight: 700;
  margin-bottom: 2px;
  display: block;
  letter-spacing: 0.3px;
  color: var(--text-primary);
  background: linear-gradient(135deg, #1C1C1E, #3A3A3C);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-fill-color: transparent;
}
.greeting-icon.data-v-8ed27b9c {
  width: 18px;
  height: 18px;
  opacity: 0.8;
  margin-left: 8px;
}
.date.data-v-8ed27b9c {
  font-size: 13px;
  /* 减小字体大小 */
  color: var(--text-tertiary);
  display: block;
  margin-top: 2px;
  /* 减小上边距 */
  font-weight: 500;
}
.merchant-badge.data-v-8ed27b9c {
  padding: 6px 12px;
  /* 减小内边距 */
  background: linear-gradient(135deg, #0A84FF, #5E5CE6);
  border-radius: 16px;
  /* 减小圆角 */
  font-size: 12px;
  color: white;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 6px;
  box-shadow: 0 4px 12px rgba(10, 132, 255, 0.25);
  border: 1px solid rgba(255, 255, 255, 0.3);
}
.merchant-badge-icon.data-v-8ed27b9c {
  width: 14px;
  /* 减小图标尺寸 */
  height: 14px;
  /* 减小图标尺寸 */
}
.performance-metrics.data-v-8ed27b9c {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: linear-gradient(145deg, rgba(255, 255, 255, 0.9), rgba(248, 249, 252, 0.9));
  border-radius: 24px;
  padding: 16px;
  /* 减小内边距 */
  position: relative;
  z-index: 2;
  box-shadow: var(--shadow-sm);
  border: 1px solid rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  margin-top: 12px;
  /* 减小上边距 */
}
.metric-item.data-v-8ed27b9c {
  text-align: center;
  flex: 1;
}
.metric-divider.data-v-8ed27b9c {
  width: 1px;
  height: 40px;
  background: linear-gradient(to bottom, transparent, rgba(0, 0, 0, 0.06), transparent);
}
.metric-value.data-v-8ed27b9c {
  font-size: 20px;
  /* 减小字体大小 */
  font-weight: 700;
  margin-bottom: 4px;
  display: block;
  background: linear-gradient(135deg, #0A84FF, #5E5CE6);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-fill-color: transparent;
}
.metric-label.data-v-8ed27b9c {
  font-size: 12px;
  color: var(--text-tertiary);
  font-weight: 500;
}

/* 快捷功能卡片 - 更现代的设计 */
.quick-actions-card.data-v-8ed27b9c {
  background: linear-gradient(145deg, #ffffff, #f8f9fc);
  border-radius: var(--radius-lg);
  padding: 18px 16px;
  /* 增加左右内边距，从12px改为16px */
  box-shadow: var(--shadow-md);
  position: relative;
  overflow: hidden;
  border: 1px solid rgba(255, 255, 255, 0.8);
  width: 90%;
  /* 调整宽度 */
  margin-left: auto;
  margin-right: auto;
  transform: translateZ(0);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
}
.quick-actions-card.data-v-8ed27b9c::before {
  content: "";
  position: absolute;
  top: -10%;
  right: -10%;
  width: 200px;
  height: 200px;
  background: radial-gradient(circle, rgba(10, 132, 255, 0.03) 0%, transparent 70%);
  border-radius: 50%;
}
.section-header.data-v-8ed27b9c {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  /* 减小下边距 */
  padding-bottom: 12px;
  /* 减小内边距 */
  border-bottom: 1px solid rgba(60, 60, 67, 0.08);
}
.overview-title.data-v-8ed27b9c {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
}
.overview-icon.data-v-8ed27b9c {
  width: 20px;
  height: 20px;
}
.section-action.data-v-8ed27b9c {
  display: flex;
  align-items: center;
  gap: 4px;
  color: var(--brand-primary);
  font-size: 14px;
  font-weight: 500;
}
.action-text.data-v-8ed27b9c {
  color: var(--brand-primary);
}
.quick-actions.data-v-8ed27b9c {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 12px;
  /* 增加间距，从8px改为12px */
}
.action-item.data-v-8ed27b9c {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 12px 4px;
  /* 增加水平内边距，添加4px的左右内边距 */
  transition: all 0.3s ease;
  border-radius: 20px;
  background: linear-gradient(145deg, #ffffff, #f8f9fc);
  box-shadow: var(--shadow-xs);
  border: 1px solid rgba(255, 255, 255, 0.8);
  position: relative;
  /* 添加相对定位 */
  z-index: 1;
  /* 确保按钮在正确的层级 */
  min-height: 80px;
  /* 确保最小高度 */
  overflow: visible;
  /* 确保内容不会被裁剪 */
}
.action-item.data-v-8ed27b9c:active {
  transform: scale(0.96);
  box-shadow: var(--shadow-sm);
  background-color: rgba(0, 0, 0, 0.02);
  /* 添加点击效果 */
}
.action-icon-container.data-v-8ed27b9c {
  width: 44px;
  /* 减小图标容器尺寸 */
  height: 44px;
  /* 减小图标容器尺寸 */
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 8px;
  /* 减小下边距 */
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);
  position: relative;
  /* 添加相对定位 */
  z-index: 2;
  /* 确保图标在按钮上层 */
}
.action-svg.data-v-8ed27b9c {
  width: 20px;
  /* 减小图标尺寸 */
  height: 20px;
  /* 减小图标尺寸 */
  position: relative;
  /* 添加相对定位 */
  z-index: 3;
  /* 确保SVG在最上层 */
}
.action-text.data-v-8ed27b9c {
  font-size: 12px;
  /* 减小字体大小 */
  position: relative;
  /* 添加相对定位 */
  z-index: 2;
  /* 确保文本在按钮上层 */
  width: 100%;
  /* 确保文本宽度占满容器 */
  text-align: center;
  /* 文本居中 */
  padding: 0 2px;
  /* 添加水平内边距 */
}

/* 业务概览 - 更精致的设计 */
.business-overview.data-v-8ed27b9c {
  background-color: var(--bg-card);
  border-radius: var(--radius-lg);
  padding: var(--space-md) var(--space-md);
  /* 减小左右内边距，确保卡片能完整显示 */
  margin-bottom: var(--space-lg);
  box-shadow: 0 6px 18px rgba(0, 0, 0, 0.09), 0 2px 6px rgba(0, 0, 0, 0.05);
  position: relative;
  overflow: hidden;
  /* 修改回hidden，但确保内部元素不会超出容器 */
  border: 1px solid rgba(200, 210, 230, 0.3);
  width: 90%;
  /* 调整为90%，与其他卡片保持一致 */
  margin-left: auto;
  margin-right: auto;
  transform: translateZ(0);
  /* 启用GPU加速，增强立体感 */
  max-width: 600px;
  /* 调整最大宽度限制 */
}
.overview-header.data-v-8ed27b9c {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 12px;
  /* 减小内边距 */
  border-bottom: 1px solid #f0f2f5;
  border-radius: var(--radius-lg) var(--radius-lg) 0 0;
  /* 添加顶部圆角 */
  overflow: hidden;
  /* 确保内容不会溢出 */
}

/* 统一所有模块标题样式 */
.overview-title.data-v-8ed27b9c {
  display: flex;
  align-items: center;
  font-size: 15px;
  /* 稍微减小字体大小 */
  font-weight: 600;
  color: var(--text-primary);
}
.overview-icon.data-v-8ed27b9c {
  width: 16px;
  /* 减小图标尺寸 */
  height: 16px;
  /* 减小图标尺寸 */
  margin-right: 6px;
  /* 减小右边距 */
  flex-shrink: 0;
}
.tab-group.data-v-8ed27b9c {
  display: flex;
  background-color: #f5f7fa;
  border-radius: 6px;
  padding: 2px;
  transform: scale(0.95);
  /* 稍微缩小标签组 */
}
.tab.data-v-8ed27b9c {
  padding: 4px 10px;
  font-size: 12px;
  border-radius: 4px;
  color: #666;
  transition: all 0.2s ease;
}
.tab.active.data-v-8ed27b9c {
  background-color: white;
  color: var(--brand-primary);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}
.overview-cards.data-v-8ed27b9c {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1px;
  background-color: #f0f2f5;
  width: 98%;
  /* 稍微减小宽度，确保不会超出父容器 */
  border-radius: 0 0 var(--radius-lg) var(--radius-lg);
  /* 添加底部圆角 */
  overflow: hidden;
  /* 确保内容不会溢出 */
  margin: 0 auto;
  /* 确保居中 */
}
.overview-card.data-v-8ed27b9c {
  background-color: white;
  padding: 12px 14px;
  /* 减小内边距 */
  display: flex;
  flex-direction: column;
  position: relative;
}
.card-label.data-v-8ed27b9c {
  font-size: 11px;
  /* 减小字体大小 */
  color: #666;
  margin-bottom: 3px;
  /* 减小下边距 */
}
.card-value.data-v-8ed27b9c {
  font-size: 16px;
  /* 减小字体大小 */
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
  /* 减小下边距 */
}
.card-trend.data-v-8ed27b9c {
  display: flex;
  align-items: center;
  gap: 3px;
  /* 减小间距 */
  font-size: 11px;
  /* 减小字体大小 */
  font-weight: 500;
}
.card-trend.up.data-v-8ed27b9c {
  color: var(--success);
}
.card-trend.down.data-v-8ed27b9c {
  color: var(--danger);
}
.icon-customer.data-v-8ed27b9c {
  background: linear-gradient(135deg, #FF9F0A, #FF3B30);
}
.icon-campaign.data-v-8ed27b9c {
  background: linear-gradient(135deg, #FF2D55, #FF375F);
}
.icon-setting.data-v-8ed27b9c {
  background: linear-gradient(135deg, #8E8E93, #636366);
}
.icon-product.data-v-8ed27b9c {
  background: linear-gradient(135deg, #32D74B, #30B856);
}
.icon-verification.data-v-8ed27b9c {
  background: linear-gradient(135deg, #64D2FF, #5AC8F5);
}
.icon-analysis.data-v-8ed27b9c {
  background: linear-gradient(135deg, #BF5AF2, #A347D1);
}
.ai-assistant.data-v-8ed27b9c {
  background: linear-gradient(145deg, #ffffff, #f8f9fc);
  border-radius: var(--radius-lg);
  padding: 18px;
  /* 减小内边距 */
  box-shadow: var(--shadow-md);
  position: relative;
  overflow: hidden;
  border: 1px solid rgba(255, 255, 255, 0.8);
  width: 90%;
  /* 调整宽度 */
  margin-left: auto;
  margin-right: auto;
  transform: translateZ(0);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
}
.assistant-insights.data-v-8ed27b9c {
  display: flex;
  flex-direction: column;
  gap: 16px;
}
.insight-card.data-v-8ed27b9c {
  background: linear-gradient(145deg, #ffffff, #f8f9fc);
  border-radius: 24px;
  padding: 16px;
  /* 减小内边距 */
  display: flex;
  align-items: center;
  transition: all 0.3s ease;
  box-shadow: var(--shadow-sm);
  border: 1px solid rgba(255, 255, 255, 0.8);
  border-left: 4px solid var(--brand-primary);
}
.insight-card.warning.data-v-8ed27b9c {
  border-left-color: var(--warning);
}
.insight-card.opportunity.data-v-8ed27b9c {
  border-left-color: var(--success);
}
.insight-card.data-v-8ed27b9c:active {
  transform: scale(0.98);
  box-shadow: var(--shadow-md);
}
.insight-icon-container.data-v-8ed27b9c {
  width: 44px;
  /* 减小图标容器尺寸 */
  height: 44px;
  /* 减小图标容器尺寸 */
  border-radius: 16px;
  margin-right: 14px;
  /* 减小右边距 */
  background: rgba(10, 132, 255, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}
.insight-card .insight-icon-container.warning.data-v-8ed27b9c {
  background: rgba(255, 159, 10, 0.1);
}
.insight-card .insight-icon-container.opportunity.data-v-8ed27b9c {
  background: rgba(48, 209, 88, 0.1);
}
.insight-icon.data-v-8ed27b9c {
  width: 24px;
  /* 减小图标尺寸 */
  height: 24px;
  /* 减小图标尺寸 */
}
.insight-content.data-v-8ed27b9c {
  flex: 1;
}
.insight-title.data-v-8ed27b9c {
  font-size: 14px;
  /* 减小字体大小 */
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 2px;
  /* 减小下边距 */
  display: block;
}
.insight-desc.data-v-8ed27b9c {
  font-size: 12px;
  /* 减小字体大小 */
  color: var(--text-tertiary);
  line-height: 1.4;
}
.insight-action.data-v-8ed27b9c {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 12px;
  flex-shrink: 0;
}

/* 底部导航栏 - 恢复原始样式 */
.tab-bar.data-v-8ed27b9c {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 56px;
  background-color: #FFFFFF;
  display: flex;
  justify-content: space-around;
  align-items: center;
  border-top: 1px solid #F0F0F0;
  padding-bottom: env(safe-area-inset-bottom);
  z-index: 100;
}
.tab-item.data-v-8ed27b9c {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  padding: 6px 0;
  box-sizing: border-box;
  position: relative;
}
.tab-icon.data-v-8ed27b9c {
  width: 24px;
  height: 24px;
  margin-bottom: 4px;
  color: #999999;
  display: flex;
  justify-content: center;
  align-items: center;
  background-size: contain;
  background-position: center;
  background-repeat: no-repeat;
}
.tab-icon.dashboard.data-v-8ed27b9c {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%230A84FF'%3E%3Cpath d='M3 13h8V3H3v10zm0 8h8v-6H3v6zm10 0h8V11h-8v10zm0-18v6h8V3h-8z'/%3E%3C/svg%3E");
}
.tab-icon.store.data-v-8ed27b9c {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%230A84FF'%3E%3Cpath d='M20 4H4v2h16V4zm1 10v-2l-1-5H4l-1 5v2h1v6h10v-6h4v6h2v-6h1zm-9 4H6v-4h6v4z'/%3E%3C/svg%3E");
}
.tab-icon.marketing.data-v-8ed27b9c {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%230A84FF'%3E%3Cpath d='M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z'/%3E%3C/svg%3E");
}
.tab-icon.orders.data-v-8ed27b9c {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%230A84FF'%3E%3Cpath d='M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-5 14H7v-2h7v2zm3-4H7v-2h10v2zm0-4H7V7h10v2z'/%3E%3C/svg%3E");
}
.tab-icon.more.data-v-8ed27b9c {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%230A84FF'%3E%3Cpath d='M12 8c1.1 0 2-.9 2-2s-.9-2-2-2-2 .9-2 2 .9 2 2 2zm0 2c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2zm0 6c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2z'/%3E%3C/svg%3E");
}
.tab-text.data-v-8ed27b9c {
  font-size: 10px;
  color: #999999;
}
.tab-item.active .tab-icon.data-v-8ed27b9c {
  color: #0052CC;
}
.tab-item.active .tab-text.data-v-8ed27b9c {
  color: #0052CC;
}
.active-indicator.data-v-8ed27b9c {
  position: absolute;
  top: 0;
  width: 20px;
  height: 3px;
  background-color: #0052CC;
  border-radius: 1.5px;
}

/* 营销中心与其他图标保持一致的颜色 */
.tab-item.active[data-tab=marketing] .tab-icon.data-v-8ed27b9c {
  color: #0052CC;
}
.tab-item.active[data-tab=marketing] .tab-text.data-v-8ed27b9c {
  color: #0052CC;
}
.tab-item.active[data-tab=marketing] .active-indicator.data-v-8ed27b9c {
  background-color: #0052CC;
}
.simple-chart.wide.data-v-8ed27b9c {
  position: relative;
  height: 280px;
  margin-bottom: 30px;
  padding: 0;
  background: #fff;
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-sm);
  overflow: hidden;
  width: 100%;
}
.trend-svg.data-v-8ed27b9c {
  width: 100%;
  height: 100%;
  display: block;
}
.y-axis.wide.data-v-8ed27b9c {
  position: absolute;
  left: 0;
  top: 0;
  bottom: 25px;
  width: 40px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  z-index: 2;
  pointer-events: none;
}
.y-axis-label.data-v-8ed27b9c {
  font-size: 13px;
  color: #999;
  text-align: right;
  padding-right: 10px;
  transform: translateY(-50%);
}
.x-axis.wide.data-v-8ed27b9c {
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  height: 25px;
  z-index: 2;
}
.x-axis-label.data-v-8ed27b9c {
  position: absolute;
  font-size: 13px;
  color: #999;
  transform: translateX(-50%);
  text-align: center;
  bottom: 0;
}
.chart-legend.data-v-8ed27b9c {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: var(--space-lg);
}
.legend-item.data-v-8ed27b9c {
  display: flex;
  align-items: center;
  font-size: 13px;
  color: var(--text-secondary);
}
.legend-dot.data-v-8ed27b9c {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-right: var(--space-sm);
}
.legend-dot.sales.data-v-8ed27b9c {
  background: #0A84FF;
}
.legend-dot.orders.data-v-8ed27b9c {
  background: #FF9500;
}

/* 更精致、高级的销售趋势图表样式 */
.premium-chart-container.data-v-8ed27b9c {
  background: linear-gradient(145deg, #ffffff, #f8f9fc);
  border-radius: var(--radius-lg);
  padding: var(--space-lg);
  margin: 0 0 var(--space-lg);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.04), 0 1px 3px rgba(0, 0, 0, 0.05);
  width: 100%;
  border: 1px solid rgba(200, 210, 230, 0.3);
  position: relative;
  overflow: hidden;
}
.premium-chart-container.data-v-8ed27b9c::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #0A84FF, #5E5CE6);
  border-radius: var(--radius-lg) var(--radius-lg) 0 0;
}
.premium-chart-header.data-v-8ed27b9c {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-lg);
  padding-bottom: var(--space-md);
  border-bottom: 1px solid rgba(200, 210, 230, 0.3);
}
.title-section.data-v-8ed27b9c {
  display: flex;
  flex-direction: column;
}
.premium-title.data-v-8ed27b9c {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 4px;
}
.premium-subtitle.data-v-8ed27b9c {
  font-size: 13px;
  color: var(--text-tertiary);
}
.premium-tabs.data-v-8ed27b9c {
  display: flex;
  background: rgba(200, 210, 230, 0.15);
  border-radius: 20px;
  padding: 2px;
}
.premium-tab.data-v-8ed27b9c {
  padding: 8px 16px;
  font-size: 13px;
  color: var(--text-tertiary);
  border-radius: 18px;
  transition: all 0.3s ease;
  font-weight: 500;
}
.premium-tab.active.data-v-8ed27b9c {
  background: linear-gradient(90deg, #0A84FF, #5E5CE6);
  color: white;
  box-shadow: 0 4px 8px rgba(10, 132, 255, 0.2);
}
.premium-chart.data-v-8ed27b9c {
  position: relative;
  height: 280px;
  margin-bottom: var(--space-md);
  padding: 20px 20px 40px;
  background-color: #FCFCFF;
  border-radius: var(--radius-md);
  border: 1px solid rgba(200, 210, 230, 0.3);
  overflow: hidden;
}

/* 图表背景 */
.chart-background.data-v-8ed27b9c {
  position: absolute;
  left: 20px;
  right: 20px;
  top: 20px;
  bottom: 40px;
  z-index: 1;
}
.grid-line.data-v-8ed27b9c {
  position: absolute;
  left: 0;
  right: 0;
  height: 1px;
  background-color: rgba(200, 210, 230, 0.2);
}
.grid-line.data-v-8ed27b9c:nth-child(1) {
  top: 0%;
}
.grid-line.data-v-8ed27b9c:nth-child(2) {
  top: 33.33%;
}
.grid-line.data-v-8ed27b9c:nth-child(3) {
  top: 66.66%;
}
.grid-line.data-v-8ed27b9c:nth-child(4) {
  top: 100%;
}
.grid-line-vertical.data-v-8ed27b9c {
  position: absolute;
  top: 0;
  bottom: 0;
  width: 1px;
  background-color: rgba(200, 210, 230, 0.1);
}
.grid-line-vertical.data-v-8ed27b9c:nth-child(1) {
  left: 0%;
}
.grid-line-vertical.data-v-8ed27b9c:nth-child(2) {
  left: 16.67%;
}
.grid-line-vertical.data-v-8ed27b9c:nth-child(3) {
  left: 33.33%;
}
.grid-line-vertical.data-v-8ed27b9c:nth-child(4) {
  left: 50%;
}
.grid-line-vertical.data-v-8ed27b9c:nth-child(5) {
  left: 66.67%;
}
.grid-line-vertical.data-v-8ed27b9c:nth-child(6) {
  left: 83.33%;
}
.grid-line-vertical.data-v-8ed27b9c:nth-child(7) {
  left: 100%;
}

/* 区域图表 */
.area-chart.data-v-8ed27b9c {
  position: absolute;
  left: 20px;
  right: 20px;
  top: 20px;
  bottom: 40px;
  z-index: 2;
}

/* 数据点 */
.data-points.data-v-8ed27b9c {
  position: absolute;
  left: 20px;
  right: 20px;
  top: 20px;
  bottom: 40px;
  z-index: 3;
}
.data-point.data-v-8ed27b9c {
  position: absolute;
  width: 4px;
  height: 4px;
  border-radius: 50%;
  transform: translate(-50%, -50%);
  cursor: pointer;
  transition: all 0.2s ease;
}
.data-point.sales.data-v-8ed27b9c {
  background-color: #0A84FF;
  box-shadow: 0 0 0 2px rgba(10, 132, 255, 0.1);
}
.data-point.orders.data-v-8ed27b9c {
  background-color: #FF9500;
  box-shadow: 0 0 0 2px rgba(255, 149, 0, 0.1);
}
.active-point.data-v-8ed27b9c {
  position: absolute;
  width: 14px;
  height: 14px;
  border-radius: 50%;
  transform: translate(-50%, -50%);
  z-index: 4;
  box-shadow: 0 0 0 4px rgba(255, 255, 255, 0.8);
}
.active-point.sales.data-v-8ed27b9c {
  background-color: #0A84FF;
  border: 2px solid white;
}
.active-point.orders.data-v-8ed27b9c {
  background-color: #FF9500;
  border: 2px solid white;
}

/* 提示框 */
.premium-tooltip.data-v-8ed27b9c {
  position: absolute;
  transform: translateX(-50%);
  z-index: 10;
  background: white;
  border-radius: 8px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
  padding: 12px;
  min-width: 160px;
  pointer-events: none;
  border: 1px solid rgba(200, 210, 230, 0.3);
}
.premium-tooltip.data-v-8ed27b9c::after {
  content: "";
  position: absolute;
  bottom: -8px;
  left: 50%;
  transform: translateX(-50%);
  width: 14px;
  height: 14px;
  background: white;
  border-right: 1px solid rgba(200, 210, 230, 0.3);
  border-bottom: 1px solid rgba(200, 210, 230, 0.3);
  transform: translateX(-50%) rotate(45deg);
}
.tooltip-date.data-v-8ed27b9c {
  font-size: 12px;
  color: var(--text-tertiary);
  text-align: center;
  margin-bottom: 8px;
  border-bottom: 1px solid rgba(200, 210, 230, 0.3);
  padding-bottom: 8px;
}
.tooltip-content.data-v-8ed27b9c {
  display: flex;
  flex-direction: column;
  gap: 8px;
}
.tooltip-item.data-v-8ed27b9c {
  display: flex;
  align-items: center;
}
.tooltip-dot.data-v-8ed27b9c {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-right: 8px;
}
.tooltip-item.sales .tooltip-dot.data-v-8ed27b9c {
  background-color: #0A84FF;
}
.tooltip-item.orders .tooltip-dot.data-v-8ed27b9c {
  background-color: #FF9500;
}
.tooltip-label.data-v-8ed27b9c {
  font-size: 12px;
  color: var(--text-tertiary);
  margin-right: 8px;
}
.tooltip-value.data-v-8ed27b9c {
  font-size: 14px;
  font-weight: 600;
  color: var(--text-primary);
  margin-left: auto;
}

/* 坐标轴 */
.y-axis-premium.data-v-8ed27b9c {
  position: absolute;
  left: 0;
  top: 20px;
  bottom: 40px;
  width: 40px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  z-index: 2;
  pointer-events: none;
}
.x-axis-premium.data-v-8ed27b9c {
  position: absolute;
  left: 20px;
  right: 20px;
  bottom: 0;
  height: 40px;
  z-index: 2;
}
.y-axis-label.data-v-8ed27b9c {
  font-size: 12px;
  color: var(--text-tertiary);
  text-align: center;
  transform: translateY(-50%);
}
.x-axis-label.data-v-8ed27b9c {
  position: absolute;
  font-size: 12px;
  color: var(--text-tertiary);
  transform: translateX(-50%);
  text-align: center;
  bottom: 12px;
}

/* 图表底部 */
.chart-footer.data-v-8ed27b9c {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: var(--space-md);
}
.chart-legend.data-v-8ed27b9c {
  display: flex;
  gap: var(--space-lg);
}
.legend-item.data-v-8ed27b9c {
  display: flex;
  align-items: center;
  font-size: 13px;
  color: var(--text-secondary);
}
.legend-dot.data-v-8ed27b9c {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-right: var(--space-sm);
}
.legend-dot.sales.data-v-8ed27b9c {
  background: #0A84FF;
}
.legend-dot.orders.data-v-8ed27b9c {
  background: #FF9500;
}
.chart-actions.data-v-8ed27b9c {
  display: flex;
  gap: var(--space-md);
}
.action-btn.data-v-8ed27b9c {
  display: flex;
  align-items: center;
  padding: 6px 12px;
  border-radius: 4px;
  background: rgba(200, 210, 230, 0.15);
  font-size: 12px;
  color: var(--text-secondary);
  cursor: pointer;
  transition: all 0.2s ease;
}
.action-btn.data-v-8ed27b9c:hover {
  background: rgba(200, 210, 230, 0.3);
}
.action-icon.data-v-8ed27b9c {
  width: 14px;
  height: 14px;
  margin-right: 4px;
  opacity: 0.7;
}
.action-icon.export.data-v-8ed27b9c {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23666'%3E%3Cpath d='M19 9h-4V3H9v6H5l7 7 7-7zM5 18v2h14v-2H5z'/%3E%3C/svg%3E");
  background-size: contain;
}
.action-icon.report.data-v-8ed27b9c {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23666'%3E%3Cpath d='M14 2H6c-1.1 0-1.99.9-1.99 2L4 20c0 1.1.89 2 1.99 2H18c1.1 0 2-.9 2-2V8l-6-6zm2 16H8v-2h8v2zm0-4H8v-2h8v2zm-3-5V3.5L18.5 9H13z'/%3E%3C/svg%3E");
  background-size: contain;
}

/* 分析洞察模块样式 */
.analytics-grid.data-v-8ed27b9c {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  grid-gap: 12px;
  margin-top: 12px;
}
.analytics-card.data-v-8ed27b9c {
  background-color: var(--bg-secondary);
  border-radius: 12px;
  padding: 16px;
  display: flex;
  align-items: center;
  box-shadow: var(--shadow-xs);
  transition: all 0.3s ease;
}
.analytics-card.data-v-8ed27b9c:active {
  transform: scale(0.98);
  background-color: var(--bg-tertiary);
}
.analytics-icon.data-v-8ed27b9c {
  width: 40px;
  height: 40px;
  border-radius: 10px;
  margin-right: 12px;
  background-size: 24px;
  background-position: center;
  background-repeat: no-repeat;
  background-color: rgba(10, 132, 255, 0.1);
}
.analytics-icon.customer-insights.data-v-8ed27b9c {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%230A84FF'%3E%3Cpath d='M9 11.75c-.69 0-1.25.56-1.25 1.25s.56 1.25 1.25 1.25 1.25-.56 1.25-1.25-.56-1.25-1.25-1.25zm6 0c-.69 0-1.25.56-1.25 1.25s.56 1.25 1.25 1.25 1.25-.56 1.25-1.25-.56-1.25-1.25-1.25zM12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8 0-.29.02-.58.05-.86 2.36-1.05 4.23-2.98 5.21-5.37C11.07 8.33 14.05 10 17.42 10c.78 0 1.53-.09 2.25-.26.21.71.33 1.47.33 2.26 0 4.41-3.59 8-8 8z'/%3E%3C/svg%3E");
}
.analytics-icon.product-analytics.data-v-8ed27b9c {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%230A84FF'%3E%3Cpath d='M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-5 14H7v-7h2v7zm4 0h-2v-7h2v7zm4 0h-2v-7h2v7z'/%3E%3C/svg%3E");
}
.analytics-icon.market-trends.data-v-8ed27b9c {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%230A84FF'%3E%3Cpath d='M3.5 18.49l6-6.01 4 4L22 6.92l-1.41-1.41-7.09 7.97-4-4L2 16.99z'/%3E%3C/svg%3E");
}
.analytics-icon.performance-metrics.data-v-8ed27b9c {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%230A84FF'%3E%3Cpath d='M15.9 5c-.17 0-.32.09-.41.23l-.07.15-5.18 11.65c-.16.29-.26.61-.26.96 0 1.11.9 2.01 2.01 2.01.96 0 1.77-.68 1.96-1.59l.01-.03L16.4 9.6l3.3 5.89c.12.22.35.35.59.35.05 0 .1 0 .15-.01.29-.06.51-.31.54-.6l.37-4.88 2.04.87c.22.09.47.01.63-.18.21-.23.21-.59-.01-.82l-6.53-5.89c-.18-.17-.43-.22-.64-.15z'/%3E%3C/svg%3E");
}
.analytics-content.data-v-8ed27b9c {
  flex: 1;
}
.analytics-title.data-v-8ed27b9c {
  font-size: 15px;
  font-weight: 500;
  color: var(--text-primary);
  margin-bottom: 4px;
  display: block;
}
.analytics-desc.data-v-8ed27b9c {
  font-size: 12px;
  color: var(--text-tertiary);
  display: block;
  line-height: 1.4;
}

/* 系统设置模块样式 */
.settings-container.data-v-8ed27b9c {
  margin-bottom: 80px;
  /* 为底部导航腾出空间 */
}
.settings-grid.data-v-8ed27b9c {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  grid-gap: 12px;
  margin-top: 12px;
}
.settings-card.data-v-8ed27b9c {
  background-color: var(--bg-secondary);
  border-radius: 12px;
  padding: 16px 8px;
  display: flex;
  flex-direction: column;
  align-items: center;
  box-shadow: var(--shadow-xs);
  transition: all 0.3s ease;
}
.settings-card.data-v-8ed27b9c:active {
  transform: scale(0.95);
  background-color: var(--bg-tertiary);
}
.settings-icon.data-v-8ed27b9c {
  width: 44px;
  height: 44px;
  border-radius: 22px;
  margin-bottom: 8px;
  background-size: 24px;
  background-position: center;
  background-repeat: no-repeat;
  background-color: rgba(10, 132, 255, 0.1);
}
.settings-icon.account.data-v-8ed27b9c {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%230A84FF'%3E%3Cpath d='M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z'/%3E%3C/svg%3E");
}
.settings-icon.security.data-v-8ed27b9c {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%230A84FF'%3E%3Cpath d='M12 1L3 5v6c0 5.55 3.84 10.74 9 12 5.16-1.26 9-6.45 9-12V5l-9-4zm0 10.99h7c-.53 4.12-3.28 7.79-7 8.94V12H5V6.3l7-3.11v8.8z'/%3E%3C/svg%3E");
}
.settings-icon.notifications.data-v-8ed27b9c {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%230A84FF'%3E%3Cpath d='M12 22c1.1 0 2-.9 2-2h-4c0 1.1.89 2 2 2zm6-6v-5c0-3.07-1.64-5.64-4.5-6.32V4c0-.83-.67-1.5-1.5-1.5s-1.5.67-1.5 1.5v.68C7.63 5.36 6 7.92 6 11v5l-2 2v1h16v-1l-2-2z'/%3E%3C/svg%3E");
}
.settings-icon.payment.data-v-8ed27b9c {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%230A84FF'%3E%3Cpath d='M20 4H4c-1.11 0-1.99.89-1.99 2L2 18c0 1.11.89 2 1.99 2h16c1.11 0 2-.89 2-2V6c0-1.11-.89-2-2-2zm0 14H4v-6h16v6zm0-10H4V6h16v2z'/%3E%3C/svg%3E");
}
.settings-title.data-v-8ed27b9c {
  font-size: 13px;
  color: var(--text-secondary);
  text-align: center;
}

/* 响应式调整 */
@media screen and (max-width: 360px) {
.settings-grid.data-v-8ed27b9c {
    grid-template-columns: repeat(2, 1fr);
}
.analytics-card.data-v-8ed27b9c {
    padding: 12px;
}
.analytics-icon.data-v-8ed27b9c {
    width: 36px;
    height: 36px;
}
.analytics-title.data-v-8ed27b9c {
    font-size: 14px;
}
}
/* 分析洞察模块 */
.analytics-section.data-v-8ed27b9c {
  margin-bottom: var(--space-lg);
  background-color: var(--bg-card);
  border-radius: var(--radius-lg);
  padding: var(--space-lg);
  box-shadow: var(--shadow-sm);
  width: 96%;
  margin-left: auto;
  margin-right: auto;
}
.analytics-grid.data-v-8ed27b9c {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  grid-gap: 12px;
  margin-top: 12px;
}
.analytics-card.data-v-8ed27b9c {
  background-color: var(--bg-secondary);
  border-radius: 12px;
  padding: 16px;
  display: flex;
  align-items: center;
  box-shadow: var(--shadow-xs);
  transition: all 0.3s ease;
}
.analytics-card.data-v-8ed27b9c:active {
  transform: scale(0.98);
  background-color: var(--bg-tertiary);
}
.analytics-icon.data-v-8ed27b9c {
  width: 40px;
  height: 40px;
  border-radius: 10px;
  margin-right: 12px;
  background-size: 24px;
  background-position: center;
  background-repeat: no-repeat;
  background-color: rgba(10, 132, 255, 0.1);
}
.analytics-icon.customer-insights.data-v-8ed27b9c {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%230A84FF'%3E%3Cpath d='M9 11.75c-.69 0-1.25.56-1.25 1.25s.56 1.25 1.25 1.25 1.25-.56 1.25-1.25-.56-1.25-1.25-1.25zm6 0c-.69 0-1.25.56-1.25 1.25s.56 1.25 1.25 1.25 1.25-.56 1.25-1.25-.56-1.25-1.25-1.25zM12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8 0-.29.02-.58.05-.86 2.36-1.05 4.23-2.98 5.21-5.37C11.07 8.33 14.05 10 17.42 10c.78 0 1.53-.09 2.25-.26.21.71.33 1.47.33 2.26 0 4.41-3.59 8-8 8z'/%3E%3C/svg%3E");
}
.analytics-icon.product-analytics.data-v-8ed27b9c {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%230A84FF'%3E%3Cpath d='M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-5 14H7v-7h2v7zm4 0h-2v-7h2v7zm4 0h-2v-7h2v7z'/%3E%3C/svg%3E");
}
.analytics-icon.market-trends.data-v-8ed27b9c {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%230A84FF'%3E%3Cpath d='M3.5 18.49l6-6.01 4 4L22 6.92l-1.41-1.41-7.09 7.97-4-4L2 16.99z'/%3E%3C/svg%3E");
}
.analytics-icon.performance-metrics.data-v-8ed27b9c {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%230A84FF'%3E%3Cpath d='M15.9 5c-.17 0-.32.09-.41.23l-.07.15-5.18 11.65c-.16.29-.26.61-.26.96 0 1.11.9 2.01 2.01 2.01.96 0 1.77-.68 1.96-1.59l.01-.03L16.4 9.6l3.3 5.89c.12.22.35.35.59.35.05 0 .1 0 .15-.01.29-.06.51-.31.54-.6l.37-4.88 2.04.87c.22.09.47.01.63-.18.21-.23.21-.59-.01-.82l-6.53-5.89c-.18-.17-.43-.22-.64-.15z'/%3E%3C/svg%3E");
}
.analytics-content.data-v-8ed27b9c {
  flex: 1;
}
.analytics-title.data-v-8ed27b9c {
  font-size: 15px;
  font-weight: 500;
  color: var(--text-primary);
  margin-bottom: 4px;
  display: block;
}
.analytics-desc.data-v-8ed27b9c {
  font-size: 12px;
  color: var(--text-tertiary);
  display: block;
  line-height: 1.4;
}

/* 系统设置模块样式 */
.settings-section.data-v-8ed27b9c {
  margin-bottom: var(--space-lg);
  background-color: var(--bg-card);
  border-radius: var(--radius-lg);
  padding: var(--space-lg);
  box-shadow: var(--shadow-sm);
  width: 96%;
  margin-left: auto;
  margin-right: auto;
}
.settings-grid.data-v-8ed27b9c {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  grid-gap: 12px;
  margin-top: 12px;
}
.settings-card.data-v-8ed27b9c {
  background-color: var(--bg-secondary);
  border-radius: 12px;
  padding: 16px 8px;
  display: flex;
  flex-direction: column;
  align-items: center;
  box-shadow: var(--shadow-xs);
  transition: all 0.3s ease;
}
.settings-card.data-v-8ed27b9c:active {
  transform: scale(0.95);
  background-color: var(--bg-tertiary);
}
.settings-icon.data-v-8ed27b9c {
  width: 44px;
  height: 44px;
  border-radius: 22px;
  margin-bottom: 8px;
  background-size: 24px;
  background-position: center;
  background-repeat: no-repeat;
  background-color: rgba(10, 132, 255, 0.1);
}
.settings-icon.account.data-v-8ed27b9c {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%230A84FF'%3E%3Cpath d='M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z'/%3E%3C/svg%3E");
}
.settings-icon.security.data-v-8ed27b9c {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%230A84FF'%3E%3Cpath d='M12 1L3 5v6c0 5.55 3.84 10.74 9 12 5.16-1.26 9-6.45 9-12V5l-9-4zm0 10.99h7c-.53 4.12-3.28 7.79-7 8.94V12H5V6.3l7-3.11v8.8z'/%3E%3C/svg%3E");
}
.settings-icon.notifications.data-v-8ed27b9c {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%230A84FF'%3E%3Cpath d='M12 22c1.1 0 2-.9 2-2h-4c0 1.1.89 2 2 2zm6-6v-5c0-3.07-1.64-5.64-4.5-6.32V4c0-.83-.67-1.5-1.5-1.5s-1.5.67-1.5 1.5v.68C7.63 5.36 6 7.92 6 11v5l-2 2v1h16v-1l-2-2z'/%3E%3C/svg%3E");
}
.settings-icon.payment.data-v-8ed27b9c {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%230A84FF'%3E%3Cpath d='M20 4H4c-1.11 0-1.99.89-1.99 2L2 18c0 1.11.89 2 1.99 2h16c1.11 0 2-.89 2-2V6c0-1.11-.89-2-2-2zm0 14H4v-6h16v6zm0-10H4V6h16v2z'/%3E%3C/svg%3E");
}
.settings-title.data-v-8ed27b9c {
  font-size: 13px;
  color: var(--text-secondary);
  text-align: center;
}

/* 响应式调整 */
@media screen and (max-width: 360px) {
.settings-grid.data-v-8ed27b9c {
    grid-template-columns: repeat(2, 1fr);
}
.analytics-card.data-v-8ed27b9c {
    padding: 12px;
}
.analytics-icon.data-v-8ed27b9c {
    width: 36px;
    height: 36px;
}
.analytics-title.data-v-8ed27b9c {
    font-size: 14px;
}
}
/* 入口按钮样式 */
.entry-section.data-v-8ed27b9c {
  margin-bottom: calc(var(--space-lg) + 70px);
  /* 增加底部间距，避免被导航栏遮挡 */
  background-color: var(--bg-card);
  border-radius: var(--radius-lg);
  padding: 0;
  box-shadow: var(--shadow-sm);
  border: 1px solid rgba(200, 210, 230, 0.3);
  width: 96%;
  margin-left: auto;
  margin-right: auto;
}
.entry-button.data-v-8ed27b9c {
  display: flex;
  align-items: center;
  padding: var(--space-md) var(--space-lg);
  transition: background-color 0.2s ease;
  position: relative;
}
.entry-button.data-v-8ed27b9c:active {
  background-color: var(--bg-secondary);
}
.entry-icon.data-v-8ed27b9c {
  width: 36px;
  height: 36px;
  border-radius: 18px;
  background-size: 20px;
  background-position: center;
  background-repeat: no-repeat;
  background-color: rgba(10, 132, 255, 0.1);
  margin-right: var(--space-md);
  flex-shrink: 0;
}
.entry-icon.settings.data-v-8ed27b9c {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%230A84FF'%3E%3Cpath d='M19.14 12.94c.04-.3.06-.61.06-.94 0-.32-.02-.64-.07-.94l2.03-1.58c.18-.14.23-.41.12-.61l-1.92-3.32c-.12-.22-.37-.29-.59-.22l-2.39.96c-.5-.38-1.03-.7-1.62-.94l-.36-2.54c-.04-.24-.24-.41-.48-.41h-3.84c-.24 0-.43.17-.47.41l-.36 2.54c-.59.24-1.13.57-1.62.94l-2.39-.96c-.22-.08-.47 0-.59.22L2.74 8.87c-.12.21-.08.47.12.61l2.03 1.58c-.05.3-.09.63-.09.94s.02.64.07.94l-2.03 1.58c-.18.14-.23.41-.12.61l1.92 3.32c.12.22.37.29.59.22l2.39-.96c.5.38 1.03.7 1.62.94l.36 2.54c.05.24.24.41.48.41h3.84c.24 0 .44-.17.47-.41l.36-2.54c.59-.24 1.13-.56 1.62-.94l2.39.96c.22.08.47 0 .59-.22l1.92-3.32c.12-.22.07-.47-.12-.61l-2.01-1.58zM12 15.6c-1.98 0-3.6-1.62-3.6-3.6s1.62-3.6 3.6-3.6 3.6 1.62 3.6 3.6-1.62 3.6-3.6 3.6z'/%3E%3C/svg%3E");
}
.entry-icon.analytics.data-v-8ed27b9c {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%230A84FF'%3E%3Cpath d='M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-5h2v5zm4 0h-2v-3h2v3zm0-5h-2v-2h2v2zm4 5h-2V7h2v10z'/%3E%3C/svg%3E");
}
.entry-icon.campaign.data-v-8ed27b9c {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%230A84FF'%3E%3Cpath d='M11 17.25C11 17.25 16 14 16 9.75C16 5.5 11 5.5 11 9.75M11 17.25C11 17.25 6 14 6 9.75C6 5.5 11 5.5 11 9.75M11 17.25V21M11 9.75V3' stroke='%230A84FF' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E");
}
.entry-text.data-v-8ed27b9c {
  font-size: 16px;
  font-weight: 500;
  color: var(--text-primary);
  flex: 1;
}
.entry-arrow.data-v-8ed27b9c {
  width: 20px;
  height: 20px;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%238E8E93'%3E%3Cpath d='M9 18L15 12L9 6' stroke='%238E8E93' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E");
  background-size: contain;
  background-position: center;
  background-repeat: no-repeat;
  flex-shrink: 0;
}

/* 添加媒体查询以在小屏幕上优化销售趋势图表 */
@media screen and (max-width: 375px) {
.chart-header.data-v-8ed27b9c {
    flex-direction: column;
    align-items: flex-start;
}
.time-tabs.data-v-8ed27b9c {
    margin-left: 0;
    margin-top: var(--space-xs);
    width: 100%;
}
.simple-chart.wide.data-v-8ed27b9c {
    height: 220px;
    /* 在小屏幕上减小图表高度 */
}
.x-axis-label.data-v-8ed27b9c {
    font-size: 10px;
    /* 减小标签字体大小 */
}
}