/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 移除阿里妈妈字体引用，改用系统默认字体 */
/* 移除原有阿里妈妈字体定义
$uni-font-family: 'AlimamaShuHeiTi';
$uni-font-family-text: 'AlimamaShuHeiTi', -apple-system, BlinkMacSystemFont, 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;
*/
body, html, #app, .index-container {
  font-family: "AlimamaShuHeiTi", -apple-system, BlinkMacSystemFont, "Helvetica Neue", "PingFang SC", "Microsoft YaHei", sans-serif;
}
.publish-container {
  min-height: 100vh;
  background-color: #f5f7fa;
  padding-bottom: 40rpx;
}

/* 自定义导航栏 */
.custom-navbar {
  display: flex;
  align-items: center;
  height: 120rpx;
  padding: 0 30rpx;
  background-color: #0A84FF;
  position: relative;
}
.navbar-left {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
}
.navbar-title {
  position: absolute;
  left: 0;
  right: 0;
  text-align: center;
  font-size: 36rpx;
  font-weight: 600;
  color: #ffffff;
}

/* 表单容器 */
.form-container {
  padding: 30rpx;
}
.form-group {
  background-color: #ffffff;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}
.form-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 20rpx;
  padding-left: 20rpx;
  border-left: 8rpx solid #0A84FF;
}
.form-item {
  margin-bottom: 30rpx;
}
.label {
  display: block;
  font-size: 28rpx;
  color: #666666;
  margin-bottom: 10rpx;
}
input, .picker-value {
  width: 100%;
  height: 80rpx;
  background-color: #f8f8f8;
  border-radius: 10rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  color: #333333;
  box-sizing: border-box;
}
.picker-value {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.picker-value image {
  width: 40rpx;
  height: 40rpx;
}
.input-wrapper {
  position: relative;
}
.location-btn {
  position: absolute;
  right: 20rpx;
  top: 50%;
  transform: translateY(-50%);
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.location-btn image {
  width: 40rpx;
  height: 40rpx;
}
.weight-input, .price-input {
  position: relative;
}
.unit {
  position: absolute;
  right: 20rpx;
  top: 50%;
  transform: translateY(-50%);
  font-size: 28rpx;
  color: #666666;
}
textarea {
  width: 100%;
  height: 200rpx;
  background-color: #f8f8f8;
  border-radius: 10rpx;
  padding: 20rpx;
  font-size: 28rpx;
  color: #333333;
  box-sizing: border-box;
}
.word-count {
  text-align: right;
  font-size: 24rpx;
  color: #999999;
  margin-top: 10rpx;
}

/* 提交区域 */
.submit-section {
  padding: 0 30rpx;
  margin-top: 40rpx;
}
.agreement {
  margin-bottom: 30rpx;
  font-size: 26rpx;
  color: #666666;
}
.link {
  color: #0A84FF;
}
.submit-btn {
  height: 90rpx;
  border-radius: 45rpx;
  background-image: linear-gradient(135deg, #0A84FF, #0055B8);
  color: #ffffff;
  font-size: 32rpx;
  font-weight: 600;
  box-shadow: 0 8rpx 16rpx rgba(10, 132, 255, 0.3);
}
.submit-btn[disabled] {
  background-image: linear-gradient(135deg, #cccccc, #999999);
  box-shadow: none;
  color: #ffffff;
}

/* 发布方式选择 */
.publish-options {
  margin-bottom: 30rpx;
}
.options-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 20rpx;
}
.option-card {
  background-color: #ffffff;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
  cursor: pointer;
  display: flex;
  align-items: center;
  position: relative;
  transition: all 0.3s ease;
}
.option-card:active {
  transform: translateY(-2rpx);
  box-shadow: 0 6rpx 15rpx rgba(0, 0, 0, 0.08);
}
.option-selected {
  border: 2rpx solid #0A84FF;
  background-color: rgba(10, 132, 255, 0.05);
}
.option-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
  flex-shrink: 0;
}
.option-icon image {
  width: 40rpx;
  height: 40rpx;
  filter: brightness(0) invert(1);
}
.ad-icon {
  background: linear-gradient(135deg, #2c96ff, #5f65ff);
}
.premium-icon {
  background: linear-gradient(135deg, #ff9500, #ff2d55);
}
.option-content {
  flex: 1;
}
.option-name {
  font-size: 30rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 6rpx;
  display: flex;
  align-items: center;
}
.option-tag {
  font-size: 20rpx;
  padding: 2rpx 10rpx;
  border-radius: 10rpx;
  margin-left: 10rpx;
  color: #ffffff;
}
.recommend {
  background-color: #1677FF;
}
.premium {
  background-color: #ff9500;
}
.option-desc {
  font-size: 24rpx;
  color: #666666;
}
.option-checkbox {
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  border: 2rpx solid #dddddd;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 20rpx;
  flex-shrink: 0;
}
.option-selected .option-checkbox {
  border-color: #0A84FF;
}
.checkbox-inner {
  width: 20rpx;
  height: 20rpx;
  background-color: #0A84FF;
  border-radius: 50%;
}
.via-points-container {
  margin-bottom: 30rpx;
}
.via-point-item {
  display: flex;
  align-items: center;
  margin-bottom: 10rpx;
}
.via-point-actions {
  display: flex;
  align-items: center;
  margin-left: 10rpx;
}
.via-point-delete {
  width: 20rpx;
  height: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}
.delete-icon {
  color: #ff0000;
}
.add-via-point {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 80rpx;
  background-color: #f8f8f8;
  border-radius: 10rpx;
  cursor: pointer;
  transition: all 0.3s ease;
}
.add-via-point:active {
  transform: translateY(-2rpx);
  box-shadow: 0 6rpx 15rpx rgba(0, 0, 0, 0.08);
}
.add-icon {
  font-size: 32rpx;
  color: #0A84FF;
  margin-right: 10rpx;
}
.add-text {
  font-size: 28rpx;
  color: #0A84FF;
}