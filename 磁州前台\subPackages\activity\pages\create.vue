<template>
	<view class="create-container">
		<view class="nav-header">
			<view class="nav-back" @click="goBack">
				<image src="/static/images/tabbar/返回键.png" class="back-icon"></image>
			</view>
			<text class="nav-title">创建活动</text>
		</view>
		
		<view class="form-content">
			<view class="form-section">
				<view class="form-item">
					<text class="form-label">活动封面</text>
					<view class="upload-box" @click="chooseImage">
						<image v-if="formData.image" :src="formData.image" class="preview-image" mode="aspectFill"></image>
						<view v-else class="upload-placeholder">
							<image src="/static/images/tabbar/上传.png" class="upload-icon"></image>
							<text class="upload-text">上传封面图片</text>
						</view>
					</view>
				</view>
				
				<view class="form-item">
					<text class="form-label">活动标题</text>
					<input class="form-input" v-model="formData.title" placeholder="请输入活动标题" maxlength="30" />
					<text class="input-count">{{formData.title.length}}/30</text>
				</view>
				
				<view class="form-item">
					<text class="form-label">活动时间</text>
					<view class="time-picker">
						<picker mode="date" :value="formData.startDate" @change="onStartDateChange">
							<view class="picker-item">
								<text class="picker-text">{{formData.startDate || '开始日期'}}</text>
								<image src="/static/images/tabbar/日历.png" class="picker-icon"></image>
							</view>
						</picker>
						<text class="time-separator">至</text>
						<picker mode="date" :value="formData.endDate" @change="onEndDateChange">
							<view class="picker-item">
								<text class="picker-text">{{formData.endDate || '结束日期'}}</text>
								<image src="/static/images/tabbar/日历.png" class="picker-icon"></image>
							</view>
						</picker>
					</view>
				</view>
				
				<view class="form-item">
					<text class="form-label">活动地点</text>
					<view class="location-picker" @click="chooseLocation">
						<text class="location-text">{{formData.location || '选择活动地点'}}</text>
						<image src="/static/images/tabbar/定位.png" class="location-icon"></image>
					</view>
				</view>
				
				<view class="form-item">
					<text class="form-label">活动详情</text>
					<textarea class="form-textarea" v-model="formData.description" placeholder="请详细描述活动内容、流程等信息" maxlength="500" />
					<text class="input-count">{{formData.description.length}}/500</text>
				</view>
				
				<view class="form-item">
					<text class="form-label">活动规则</text>
					<view class="rule-list">
						<view class="rule-item" v-for="(rule, index) in formData.rules" :key="index">
							<input class="rule-input" v-model="formData.rules[index]" placeholder="请输入活动规则" />
							<view class="rule-delete" @click="deleteRule(index)">
								<image src="/static/images/tabbar/删除.png" class="delete-icon"></image>
							</view>
						</view>
						<view class="add-rule" @click="addRule">
							<text class="add-rule-text">+ 添加规则</text>
						</view>
					</view>
				</view>
				
				<view class="form-item">
					<text class="form-label">主办方</text>
					<input class="form-input" v-model="formData.organizer" placeholder="请输入主办方名称" />
				</view>
				
				<view class="form-item">
					<text class="form-label">联系电话</text>
					<input class="form-input" v-model="formData.phone" placeholder="请输入联系电话" type="number" />
				</view>
			</view>
			
			<view class="form-tips">
				<text class="tips-text">提交后，您的活动将在审核通过后展示</text>
			</view>
		</view>
		
		<view class="action-bar">
			<button class="cancel-btn" @click="goBack">取消</button>
			<button class="submit-btn" @click="submitActivity">提交活动</button>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				formData: {
					title: '',
					image: '',
					startDate: '',
					endDate: '',
					location: '',
					description: '',
					rules: [''],
					organizer: '',
					phone: ''
				}
			}
		},
		methods: {
			goBack() {
				uni.navigateBack();
			},
			chooseImage() {
				uni.chooseImage({
					count: 1,
					sizeType: ['compressed'],
					sourceType: ['album', 'camera'],
					success: (res) => {
						this.formData.image = res.tempFilePaths[0];
					}
				});
			},
			onStartDateChange(e) {
				this.formData.startDate = e.detail.value;
			},
			onEndDateChange(e) {
				this.formData.endDate = e.detail.value;
			},
			chooseLocation() {
				uni.chooseLocation({
					success: (res) => {
						this.formData.location = res.name;
						this.formData.address = res.address;
						this.formData.latitude = res.latitude;
						this.formData.longitude = res.longitude;
					}
				});
			},
			addRule() {
				this.formData.rules.push('');
			},
			deleteRule(index) {
				if (this.formData.rules.length > 1) {
					this.formData.rules.splice(index, 1);
				} else {
					uni.showToast({
						title: '至少保留一条规则',
						icon: 'none'
					});
				}
			},
			submitActivity() {
				// 表单验证
				if (!this.formData.title) {
					return this.showError('请输入活动标题');
				}
				if (!this.formData.image) {
					return this.showError('请上传活动封面');
				}
				if (!this.formData.startDate || !this.formData.endDate) {
					return this.showError('请选择活动时间');
				}
				if (!this.formData.location) {
					return this.showError('请选择活动地点');
				}
				if (!this.formData.description) {
					return this.showError('请输入活动详情');
				}
				if (!this.formData.rules[0]) {
					return this.showError('请至少输入一条活动规则');
				}
				if (!this.formData.organizer) {
					return this.showError('请输入主办方名称');
				}
				if (!this.formData.phone) {
					return this.showError('请输入联系电话');
				}
				
				// 提交表单
				uni.showLoading({
					title: '提交中...'
				});
				
				// 模拟提交
				setTimeout(() => {
					uni.hideLoading();
					uni.showToast({
						title: '提交成功',
						icon: 'success'
					});
					
					// 延迟返回
					setTimeout(() => {
						uni.navigateBack();
					}, 1500);
				}, 1000);
			},
			showError(message) {
				uni.showToast({
					title: message,
					icon: 'none'
				});
				return false;
			}
		}
	}
</script>

<style>
	.create-container {
		min-height: 100vh;
		background-color: #f5f5f5;
		padding-bottom: 120rpx;
	}
	
	.nav-header {
		display: flex;
		align-items: center;
		height: 90rpx;
		background-color: #1677FF;
		color: #fff;
		padding: 0 30rpx;
		padding-top: var(--status-bar-height);
	}
	
	.nav-back {
		width: 60rpx;
		height: 60rpx;
		display: flex;
		align-items: center;
		justify-content: center;
	}
	
	.back-icon {
		width: 40rpx;
		height: 40rpx;
	}
	
	.nav-title {
		flex: 1;
		text-align: center;
		font-size: 34rpx;
		font-weight: bold;
		margin-right: 60rpx;
	}
	
	.form-content {
		padding: 30rpx;
	}
	
	.form-section {
		background-color: #fff;
		border-radius: 20rpx;
		padding: 30rpx;
		margin-bottom: 30rpx;
	}
	
	.form-item {
		margin-bottom: 30rpx;
		position: relative;
	}
	
	.form-label {
		font-size: 30rpx;
		color: #333;
		font-weight: bold;
		margin-bottom: 20rpx;
		display: block;
	}
	
	.form-input {
		width: 100%;
		height: 80rpx;
		background-color: #f8f8f8;
		border-radius: 10rpx;
		padding: 0 20rpx;
		font-size: 28rpx;
		color: #333;
	}
	
	.form-textarea {
		width: 100%;
		height: 200rpx;
		background-color: #f8f8f8;
		border-radius: 10rpx;
		padding: 20rpx;
		font-size: 28rpx;
		color: #333;
	}
	
	.input-count {
		position: absolute;
		right: 10rpx;
		bottom: 10rpx;
		font-size: 24rpx;
		color: #999;
	}
	
	.upload-box {
		width: 100%;
		height: 300rpx;
		background-color: #f8f8f8;
		border-radius: 10rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		overflow: hidden;
	}
	
	.upload-placeholder {
		display: flex;
		flex-direction: column;
		align-items: center;
	}
	
	.upload-icon {
		width: 80rpx;
		height: 80rpx;
		margin-bottom: 20rpx;
	}
	
	.upload-text {
		font-size: 28rpx;
		color: #999;
	}
	
	.preview-image {
		width: 100%;
		height: 100%;
	}
	
	.time-picker {
		display: flex;
		align-items: center;
	}
	
	.picker-item {
		flex: 1;
		height: 80rpx;
		background-color: #f8f8f8;
		border-radius: 10rpx;
		padding: 0 20rpx;
		display: flex;
		align-items: center;
		justify-content: space-between;
	}
	
	.picker-text {
		font-size: 28rpx;
		color: #333;
	}
	
	.picker-icon {
		width: 40rpx;
		height: 40rpx;
	}
	
	.time-separator {
		margin: 0 20rpx;
		font-size: 28rpx;
		color: #999;
	}
	
	.location-picker {
		height: 80rpx;
		background-color: #f8f8f8;
		border-radius: 10rpx;
		padding: 0 20rpx;
		display: flex;
		align-items: center;
		justify-content: space-between;
	}
	
	.location-text {
		font-size: 28rpx;
		color: #333;
		flex: 1;
	}
	
	.location-icon {
		width: 40rpx;
		height: 40rpx;
	}
	
	.rule-list {
		display: flex;
		flex-direction: column;
		gap: 20rpx;
	}
	
	.rule-item {
		display: flex;
		align-items: center;
	}
	
	.rule-input {
		flex: 1;
		height: 80rpx;
		background-color: #f8f8f8;
		border-radius: 10rpx;
		padding: 0 20rpx;
		font-size: 28rpx;
		color: #333;
	}
	
	.rule-delete {
		width: 80rpx;
		height: 80rpx;
		display: flex;
		align-items: center;
		justify-content: center;
	}
	
	.delete-icon {
		width: 40rpx;
		height: 40rpx;
	}
	
	.add-rule {
		height: 80rpx;
		background-color: #f0f7ff;
		border-radius: 10rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		margin-top: 10rpx;
	}
	
	.add-rule-text {
		font-size: 28rpx;
		color: #1677FF;
	}
	
	.form-tips {
		padding: 0 30rpx;
	}
	
	.tips-text {
		font-size: 24rpx;
		color: #999;
		text-align: center;
	}
	
	.action-bar {
		position: fixed;
		bottom: 0;
		left: 0;
		right: 0;
		height: 100rpx;
		background: #fff;
		display: flex;
		align-items: center;
		padding: 0 30rpx;
		box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
	}
	
	.cancel-btn {
		flex: 1;
		height: 80rpx;
		background-color: #f5f5f5;
		color: #666;
		font-size: 30rpx;
		border-radius: 40rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		margin-right: 20rpx;
	}
	
	.submit-btn {
		flex: 2;
		height: 80rpx;
		background-color: #1677FF;
		color: #fff;
		font-size: 30rpx;
		border-radius: 40rpx;
		display: flex;
		align-items: center;
		justify-content: center;
	}
</style> 
