{"version": 3, "file": "tasks.js", "sources": ["subPackages/merchant-admin-marketing/pages/marketing/member/tasks.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/c3ViUGFja2FnZXNcbWVyY2hhbnQtYWRtaW4tbWFya2V0aW5nXHBhZ2VzXG1hcmtldGluZ1xtZW1iZXJcdGFza3MudnVl"], "sourcesContent": ["<template>\r\n  <view class=\"tasks-container\">\r\n    <!-- 自定义导航栏 -->\r\n    <view class=\"navbar\">\r\n      <view class=\"navbar-back\" @click=\"goBack\">\r\n        <view class=\"back-icon\"></view>\r\n      </view>\r\n      <text class=\"navbar-title\">会员任务</text>\r\n      <view class=\"navbar-right\">\r\n        <view class=\"help-icon\" @click=\"showHelp\">?</view>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 任务概览 -->\r\n    <view class=\"overview-section\">\r\n      <view class=\"overview-header\">\r\n        <text class=\"section-title\">任务概览</text>\r\n        <view class=\"date-picker\" @click=\"showDatePicker\">\r\n          <text class=\"date-text\">{{dateRange}}</text>\r\n          <view class=\"date-icon\"></view>\r\n        </view>\r\n      </view>\r\n      \r\n      <view class=\"stats-cards\">\r\n        <view class=\"stats-card\">\r\n          <view class=\"card-value\">{{taskData.totalTasks}}</view>\r\n          <view class=\"card-label\">总任务数</view>\r\n        </view>\r\n        \r\n        <view class=\"stats-card\">\r\n          <view class=\"card-value\">{{taskData.activeTasks}}</view>\r\n          <view class=\"card-label\">进行中任务</view>\r\n        </view>\r\n        \r\n        <view class=\"stats-card\">\r\n          <view class=\"card-value\">{{taskData.completionRate}}%</view>\r\n          <view class=\"card-label\">任务完成率</view>\r\n          <view class=\"card-trend\" :class=\"taskData.completionTrend\">\r\n            <view class=\"trend-arrow\"></view>\r\n            <text class=\"trend-value\">{{taskData.completionGrowth}}</text>\r\n          </view>\r\n        </view>\r\n        \r\n        <view class=\"stats-card\">\r\n          <view class=\"card-value\">{{taskData.participationRate}}%</view>\r\n          <view class=\"card-label\">会员参与率</view>\r\n          <view class=\"card-trend\" :class=\"taskData.participationTrend\">\r\n            <view class=\"trend-arrow\"></view>\r\n            <text class=\"trend-value\">{{taskData.participationGrowth}}</text>\r\n          </view>\r\n        </view>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 任务分类标签 -->\r\n    <view class=\"tabs-section\">\r\n      <scroll-view scroll-x class=\"tabs-scroll\" show-scrollbar=\"false\">\r\n        <view class=\"tabs\">\r\n          <view \r\n            class=\"tab-item\" \r\n            v-for=\"(tab, index) in tabs\" \r\n            :key=\"index\"\r\n            :class=\"{ active: currentTab === index }\"\r\n            @click=\"switchTab(index)\"\r\n          >\r\n            {{tab}}\r\n          </view>\r\n        </view>\r\n      </scroll-view>\r\n    </view>\r\n    \r\n    <!-- 任务列表 -->\r\n    <view class=\"tasks-section\">\r\n      <view class=\"section-header\">\r\n        <text class=\"section-title\">{{tabs[currentTab]}}</text>\r\n        <view class=\"add-btn\" @click=\"createTask\">\r\n          <text class=\"btn-text\">添加任务</text>\r\n          <view class=\"plus-icon\"></view>\r\n        </view>\r\n      </view>\r\n      \r\n      <view class=\"tasks-list\">\r\n        <view class=\"task-item\" v-for=\"(task, index) in filteredTasks\" :key=\"index\" @click=\"editTask(task)\">\r\n          <view class=\"task-icon\" :style=\"{ background: task.iconBg }\">\r\n            <svg class=\"svg-icon\" viewBox=\"0 0 24 24\" :style=\"{ fill: task.iconColor }\">\r\n              <path :d=\"task.iconPath\"></path>\r\n            </svg>\r\n          </view>\r\n          <view class=\"task-content\">\r\n            <view class=\"task-header\">\r\n              <text class=\"task-name\">{{task.name}}</text>\r\n              <text class=\"task-status\" :class=\"task.status\">{{task.statusText}}</text>\r\n            </view>\r\n            <view class=\"task-desc\">{{task.description}}</view>\r\n            <view class=\"task-meta\">\r\n              <view class=\"task-reward\">\r\n                <text class=\"reward-label\">奖励: </text>\r\n                <text class=\"reward-value\">{{task.reward}}</text>\r\n              </view>\r\n              <view class=\"task-period\">\r\n                <text class=\"period-label\">周期: </text>\r\n                <text class=\"period-value\">{{task.period}}</text>\r\n              </view>\r\n            </view>\r\n          </view>\r\n          <view class=\"task-action\">\r\n            <switch :checked=\"task.enabled\" @change=\"(e) => toggleTask(task, e)\" color=\"#F6D365\" />\r\n          </view>\r\n        </view>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 浮动操作按钮 -->\r\n    <view class=\"floating-action-button\" @click=\"createTask\">\r\n      <view class=\"fab-icon\">+</view>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  data() {\r\n    return {\r\n      dateRange: '2023-04-01 ~ 2023-04-30',\r\n      currentTab: 0,\r\n      \r\n      // 任务数据概览\r\n      taskData: {\r\n        totalTasks: 12,\r\n        activeTasks: 8,\r\n        completionRate: 68.5,\r\n        completionTrend: 'up',\r\n        completionGrowth: '5.2%',\r\n        participationRate: 42.3,\r\n        participationTrend: 'up',\r\n        participationGrowth: '3.8%'\r\n      },\r\n      \r\n      // 任务分类标签\r\n      tabs: ['全部任务', '日常任务', '成长任务', '消费任务', '社交任务'],\r\n      \r\n      // 任务列表\r\n      tasks: [\r\n        {\r\n          id: 1,\r\n          name: '每日签到',\r\n          description: '会员每日签到获得积分奖励',\r\n          reward: '5积分/次',\r\n          period: '每日一次',\r\n          category: '日常任务',\r\n          status: 'active',\r\n          statusText: '进行中',\r\n          enabled: true,\r\n          iconPath: 'M19 3h-1V1h-2v2H8V1H6v2H5c-1.11 0-1.99.9-1.99 2L3 19c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm0 16H5V8h14v11zM7 10h5v5H7v-5z',\r\n          iconBg: 'rgba(246, 211, 101, 0.1)',\r\n          iconColor: '#F6D365'\r\n        },\r\n        {\r\n          id: 2,\r\n          name: '浏览商品',\r\n          description: '每日浏览5件商品获得积分奖励',\r\n          reward: '10积分/次',\r\n          period: '每日一次',\r\n          category: '日常任务',\r\n          status: 'active',\r\n          statusText: '进行中',\r\n          enabled: true,\r\n          iconPath: 'M12 4.5C7 4.5 2.73 7.61 1 12c1.73 4.39 6 7.5 11 7.5s9.27-3.11 11-7.5c-1.73-4.39-6-7.5-11-7.5zM12 17c-2.76 0-5-2.24-5-5s2.24-5 5-5 5 2.24 5 5-2.24 5-5 5zm0-8c-1.66 0-3 1.34-3 3s1.34 3 3 3 3-1.34 3-3-1.34-3-3-3z',\r\n          iconBg: 'rgba(246, 211, 101, 0.1)',\r\n          iconColor: '#F6D365'\r\n        },\r\n        {\r\n          id: 3,\r\n          name: '分享商品',\r\n          description: '分享商品给好友获得积分奖励',\r\n          reward: '15积分/次',\r\n          period: '每日三次',\r\n          category: '社交任务',\r\n          status: 'active',\r\n          statusText: '进行中',\r\n          enabled: true,\r\n          iconPath: 'M18 16.08c-.76 0-1.44.3-1.96.77L8.91 12.7c.05-.23.09-.46.09-.7s-.04-.47-.09-.7l7.05-4.11c.54.5 1.25.81 2.04.81 1.66 0 3-1.34 3-3s-1.34-3-3-3-3 1.34-3 3c0 .***********.7L8.04 9.81C7.5 9.31 6.79 9 6 9c-1.66 0-3 1.34-3 3s1.34 3 3 3c.79 0 1.5-.31 2.04-.81l7.12 4.16c-.05.21-.08.43-.08.65 0 1.61 1.31 2.92 2.92 2.92 1.61 0 2.92-1.31 2.92-2.92s-1.31-2.92-2.92-2.92z',\r\n          iconBg: 'rgba(246, 211, 101, 0.1)',\r\n          iconColor: '#F6D365'\r\n        },\r\n        {\r\n          id: 4,\r\n          name: '邀请好友',\r\n          description: '邀请好友注册成为会员',\r\n          reward: '50积分/人',\r\n          period: '长期有效',\r\n          category: '社交任务',\r\n          status: 'active',\r\n          statusText: '进行中',\r\n          enabled: true,\r\n          iconPath: 'M16 11c1.66 0 2.99-1.34 2.99-3S17.66 5 16 5c-1.66 0-3 1.34-3 3s1.34 3 3 3zm-8 0c1.66 0 2.99-1.34 2.99-3S9.66 5 8 5C6.34 5 5 6.34 5 8s1.34 3 3 3zm0 2c-2.33 0-7 1.17-7 3.5V19h14v-2.5c0-2.33-4.67-3.5-7-3.5zm8 0c-.29 0-.62.02-.97.05 1.16.84 1.97 1.97 1.97 3.45V19h6v-2.5c0-2.33-4.67-3.5-7-3.5z',\r\n          iconBg: 'rgba(246, 211, 101, 0.1)',\r\n          iconColor: '#F6D365'\r\n        },\r\n        {\r\n          id: 5,\r\n          name: '完善资料',\r\n          description: '完善个人资料获得一次性奖励',\r\n          reward: '30积分',\r\n          period: '一次性',\r\n          category: '成长任务',\r\n          status: 'active',\r\n          statusText: '进行中',\r\n          enabled: true,\r\n          iconPath: 'M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z',\r\n          iconBg: 'rgba(246, 211, 101, 0.1)',\r\n          iconColor: '#F6D365'\r\n        },\r\n        {\r\n          id: 6,\r\n          name: '首次购物',\r\n          description: '首次在商城购物获得奖励',\r\n          reward: '100积分',\r\n          period: '一次性',\r\n          category: '消费任务',\r\n          status: 'active',\r\n          statusText: '进行中',\r\n          enabled: true,\r\n          iconPath: 'M7 18c-1.1 0-1.99.9-1.99 2S5.9 22 7 22s2-.9 2-2-.9-2-2-2zM1 2v2h2l3.6 7.59-1.35 2.45c-.16.28-.25.61-.25.96 0 1.1.9 2 2 2h12v-2H7.42c-.14 0-.25-.11-.25-.25l.03-.12.9-1.63h7.45c.75 0 1.41-.41 1.75-1.03l3.58-6.49c.08-.14.12-.31.12-.48 0-.55-.45-1-1-1H5.21l-.94-2H1zm16 16c-1.1 0-1.99.9-1.99 2s.89 2 1.99 2 2-.9 2-2-.9-2-2-2z',\r\n          iconBg: 'rgba(246, 211, 101, 0.1)',\r\n          iconColor: '#F6D365'\r\n        },\r\n        {\r\n          id: 7,\r\n          name: '商品评价',\r\n          description: '购买商品后评价获得积分奖励',\r\n          reward: '20积分/次',\r\n          period: '每单一次',\r\n          category: '消费任务',\r\n          status: 'active',\r\n          statusText: '进行中',\r\n          enabled: true,\r\n          iconPath: 'M20 2H4c-1.1 0-1.99.9-1.99 2L2 22l4-4h14c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm-7 12h-2v-2h2v2zm0-4h-2V6h2v4z',\r\n          iconBg: 'rgba(246, 211, 101, 0.1)',\r\n          iconColor: '#F6D365'\r\n        },\r\n        {\r\n          id: 8,\r\n          name: '连续签到7天',\r\n          description: '连续签到7天获得额外奖励',\r\n          reward: '50积分',\r\n          period: '每周一次',\r\n          category: '成长任务',\r\n          status: 'active',\r\n          statusText: '进行中',\r\n          enabled: true,\r\n          iconPath: 'M19 3h-1V1h-2v2H8V1H6v2H5c-1.11 0-1.99.9-1.99 2L3 19c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm0 16H5V8h14v11zM7 10h5v5H7v-5z',\r\n          iconBg: 'rgba(246, 211, 101, 0.1)',\r\n          iconColor: '#F6D365'\r\n        }\r\n      ]\r\n    }\r\n  },\r\n  computed: {\r\n    filteredTasks() {\r\n      if (this.currentTab === 0) {\r\n        return this.tasks;\r\n      } else {\r\n        const category = this.tabs[this.currentTab];\r\n        return this.tasks.filter(task => task.category === category);\r\n      }\r\n    }\r\n  },\r\n  methods: {\r\n    goBack() {\r\n      uni.navigateBack();\r\n    },\r\n    \r\n    showHelp() {\r\n      uni.showModal({\r\n        title: '会员任务帮助',\r\n        content: '会员任务是指会员可以通过完成特定任务获得积分或其他奖励的活动，可以提高会员活跃度和忠诚度。',\r\n        showCancel: false\r\n      });\r\n    },\r\n    \r\n    showDatePicker() {\r\n      // 实现日期选择器\r\n      uni.showToast({\r\n        title: '日期选择功能开发中',\r\n        icon: 'none'\r\n      });\r\n    },\r\n    \r\n    switchTab(index) {\r\n      this.currentTab = index;\r\n    },\r\n    \r\n    createTask() {\r\n      uni.navigateTo({\r\n        url: '/subPackages/merchant-admin-marketing/pages/marketing/member/create-task'\r\n      });\r\n    },\r\n    \r\n    editTask(task) {\r\n      uni.navigateTo({\r\n        url: `/subPackages/merchant-admin-marketing/pages/marketing/member/edit-task?id=${task.id}`\r\n      });\r\n    },\r\n    \r\n    toggleTask(task, e) {\r\n      // 更新任务状态\r\n      const index = this.tasks.findIndex(item => item.id === task.id);\r\n      if (index !== -1) {\r\n        this.tasks[index].enabled = e.detail.value;\r\n        this.tasks[index].status = e.detail.value ? 'active' : 'inactive';\r\n        this.tasks[index].statusText = e.detail.value ? '进行中' : '已暂停';\r\n      }\r\n      \r\n      uni.showToast({\r\n        title: e.detail.value ? `${task.name}已启用` : `${task.name}已禁用`,\r\n        icon: 'none'\r\n      });\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.tasks-container {\r\n  min-height: 100vh;\r\n  background-color: #F5F7FA;\r\n  padding-bottom: env(safe-area-inset-bottom);\r\n}\r\n\r\n/* 导航栏样式 */\r\n.navbar {\r\n  position: sticky;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  background: linear-gradient(135deg, #F6D365, #FDA085);\r\n  color: #fff;\r\n  padding: 44px 16px 15px;\r\n  display: flex;\r\n  align-items: center;\r\n  z-index: 100;\r\n  box-shadow: 0 2px 10px rgba(246, 211, 101, 0.15);\r\n}\r\n\r\n.navbar-back {\r\n  width: 36px;\r\n  height: 36px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.back-icon {\r\n  width: 12px;\r\n  height: 12px;\r\n  border-left: 2px solid #fff;\r\n  border-bottom: 2px solid #fff;\r\n  transform: rotate(45deg);\r\n}\r\n\r\n.navbar-title {\r\n  flex: 1;\r\n  text-align: center;\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n  letter-spacing: 0.5px;\r\n}\r\n\r\n.navbar-right {\r\n  width: 36px;\r\n  height: 36px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.help-icon {\r\n  width: 24px;\r\n  height: 24px;\r\n  border-radius: 12px;\r\n  background: rgba(255, 255, 255, 0.2);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  font-size: 14px;\r\n  font-weight: bold;\r\n}\r\n\r\n/* 通用部分样式 */\r\n.section-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.section-title {\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  color: #333;\r\n}\r\n\r\n.add-btn {\r\n  display: flex;\r\n  align-items: center;\r\n  background: #F6D365;\r\n  border-radius: 15px;\r\n  padding: 5px 12px;\r\n  color: white;\r\n}\r\n\r\n.btn-text {\r\n  font-size: 13px;\r\n  margin-right: 5px;\r\n}\r\n\r\n.plus-icon {\r\n  width: 12px;\r\n  height: 12px;\r\n  position: relative;\r\n}\r\n\r\n.plus-icon:before,\r\n.plus-icon:after {\r\n  content: '';\r\n  position: absolute;\r\n  background: white;\r\n}\r\n\r\n.plus-icon:before {\r\n  width: 12px;\r\n  height: 2px;\r\n  top: 5px;\r\n  left: 0;\r\n}\r\n\r\n.plus-icon:after {\r\n  height: 12px;\r\n  width: 2px;\r\n  left: 5px;\r\n  top: 0;\r\n}\r\n\r\n/* 概览部分样式 */\r\n.overview-section {\r\n  margin: 15px;\r\n  background: #fff;\r\n  border-radius: 12px;\r\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);\r\n  padding: 15px;\r\n}\r\n\r\n.overview-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.date-picker {\r\n  display: flex;\r\n  align-items: center;\r\n  background: #F5F7FA;\r\n  border-radius: 15px;\r\n  padding: 5px 10px;\r\n}\r\n\r\n.date-text {\r\n  font-size: 12px;\r\n  color: #666;\r\n  margin-right: 5px;\r\n}\r\n\r\n.date-icon {\r\n  width: 8px;\r\n  height: 8px;\r\n  border-top: 2px solid #666;\r\n  border-right: 2px solid #666;\r\n  transform: rotate(135deg);\r\n}\r\n\r\n/* 统计卡片样式 */\r\n.stats-cards {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  margin: 0 -7.5px;\r\n}\r\n\r\n.stats-card {\r\n  width: 50%;\r\n  padding: 7.5px;\r\n  box-sizing: border-box;\r\n  position: relative;\r\n}\r\n\r\n.card-value {\r\n  font-size: 20px;\r\n  font-weight: 600;\r\n  color: #333;\r\n  margin-bottom: 5px;\r\n  background: #F8FAFC;\r\n  padding: 15px;\r\n  border-radius: 10px;\r\n  border-left: 3px solid #F6D365;\r\n}\r\n\r\n.card-label {\r\n  font-size: 12px;\r\n  color: #999;\r\n  position: absolute;\r\n  bottom: 20px;\r\n  left: 25px;\r\n}\r\n\r\n.card-trend {\r\n  position: absolute;\r\n  bottom: 20px;\r\n  right: 25px;\r\n  display: flex;\r\n  align-items: center;\r\n  font-size: 12px;\r\n}\r\n\r\n.card-trend.up {\r\n  color: #34C759;\r\n}\r\n\r\n.card-trend.down {\r\n  color: #FF3B30;\r\n}\r\n\r\n.trend-arrow {\r\n  width: 0;\r\n  height: 0;\r\n  margin-right: 3px;\r\n}\r\n\r\n.card-trend.up .trend-arrow {\r\n  border-left: 4px solid transparent;\r\n  border-right: 4px solid transparent;\r\n  border-bottom: 6px solid #34C759;\r\n}\r\n\r\n.card-trend.down .trend-arrow {\r\n  border-left: 4px solid transparent;\r\n  border-right: 4px solid transparent;\r\n  border-top: 6px solid #FF3B30;\r\n}\r\n\r\n/* 标签页样式 */\r\n.tabs-section {\r\n  margin: 15px 15px 0;\r\n}\r\n\r\n.tabs-scroll {\r\n  white-space: nowrap;\r\n}\r\n\r\n.tabs {\r\n  display: inline-flex;\r\n  padding: 5px 0;\r\n}\r\n\r\n.tab-item {\r\n  padding: 8px 16px;\r\n  font-size: 14px;\r\n  color: #666;\r\n  margin-right: 10px;\r\n  background: #fff;\r\n  border-radius: 20px;\r\n  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);\r\n  transition: all 0.3s;\r\n}\r\n\r\n.tab-item.active {\r\n  background: #F6D365;\r\n  color: white;\r\n  box-shadow: 0 2px 8px rgba(246, 211, 101, 0.3);\r\n}\r\n\r\n/* 任务列表样式 */\r\n.tasks-section {\r\n  margin: 15px;\r\n  background: #fff;\r\n  border-radius: 12px;\r\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);\r\n  padding: 15px;\r\n}\r\n\r\n.tasks-list {\r\n  margin-top: 10px;\r\n}\r\n\r\n.task-item {\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 15px;\r\n  margin-bottom: 10px;\r\n  background: #F8FAFC;\r\n  border-radius: 10px;\r\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n.task-icon {\r\n  width: 48px;\r\n  height: 48px;\r\n  border-radius: 24px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  margin-right: 15px;\r\n}\r\n\r\n.svg-icon {\r\n  width: 28px;\r\n  height: 28px;\r\n}\r\n\r\n.task-content {\r\n  flex: 1;\r\n}\r\n\r\n.task-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 5px;\r\n}\r\n\r\n.task-name {\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  color: #333;\r\n}\r\n\r\n.task-status {\r\n  font-size: 12px;\r\n  padding: 2px 8px;\r\n  border-radius: 10px;\r\n}\r\n\r\n.task-status.active {\r\n  background: rgba(52, 199, 89, 0.1);\r\n  color: #34C759;\r\n}\r\n\r\n.task-status.inactive {\r\n  background: rgba(142, 142, 147, 0.1);\r\n  color: #8E8E93;\r\n}\r\n\r\n.task-desc {\r\n  font-size: 13px;\r\n  color: #666;\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.task-meta {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.task-reward {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-right: 15px;\r\n}\r\n\r\n.reward-label {\r\n  font-size: 12px;\r\n  color: #999;\r\n}\r\n\r\n.reward-value {\r\n  font-size: 12px;\r\n  color: #F6D365;\r\n  font-weight: 500;\r\n}\r\n\r\n.task-period {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.period-label {\r\n  font-size: 12px;\r\n  color: #999;\r\n}\r\n\r\n.period-value {\r\n  font-size: 12px;\r\n  color: #666;\r\n}\r\n\r\n.task-action {\r\n  margin-left: 10px;\r\n}\r\n\r\n/* 浮动操作按钮 */\r\n.floating-action-button {\r\n  position: fixed;\r\n  bottom: 30px;\r\n  right: 30px;\r\n  width: 56px;\r\n  height: 56px;\r\n  border-radius: 28px;\r\n  background: linear-gradient(135deg, #F6D365, #FDA085);\r\n  box-shadow: 0 4px 15px rgba(246, 211, 101, 0.3);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  z-index: 100;\r\n}\r\n\r\n.fab-icon {\r\n  font-size: 28px;\r\n  color: #fff;\r\n  font-weight: 300;\r\n  line-height: 1;\r\n  margin-top: -2px;\r\n}\r\n\r\n/* 响应式调整 */\r\n@media screen and (max-width: 375px) {\r\n  .stats-card {\r\n    width: 100%;\r\n  }\r\n}\r\n</style> ", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/subPackages/merchant-admin-marketing/pages/marketing/member/tasks.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni"], "mappings": ";;AAwHA,MAAK,YAAU;AAAA,EACb,OAAO;AACL,WAAO;AAAA,MACL,WAAW;AAAA,MACX,YAAY;AAAA;AAAA,MAGZ,UAAU;AAAA,QACR,YAAY;AAAA,QACZ,aAAa;AAAA,QACb,gBAAgB;AAAA,QAChB,iBAAiB;AAAA,QACjB,kBAAkB;AAAA,QAClB,mBAAmB;AAAA,QACnB,oBAAoB;AAAA,QACpB,qBAAqB;AAAA,MACtB;AAAA;AAAA,MAGD,MAAM,CAAC,QAAQ,QAAQ,QAAQ,QAAQ,MAAM;AAAA;AAAA,MAG7C,OAAO;AAAA,QACL;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,aAAa;AAAA,UACb,QAAQ;AAAA,UACR,QAAQ;AAAA,UACR,UAAU;AAAA,UACV,QAAQ;AAAA,UACR,YAAY;AAAA,UACZ,SAAS;AAAA,UACT,UAAU;AAAA,UACV,QAAQ;AAAA,UACR,WAAW;AAAA,QACZ;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,aAAa;AAAA,UACb,QAAQ;AAAA,UACR,QAAQ;AAAA,UACR,UAAU;AAAA,UACV,QAAQ;AAAA,UACR,YAAY;AAAA,UACZ,SAAS;AAAA,UACT,UAAU;AAAA,UACV,QAAQ;AAAA,UACR,WAAW;AAAA,QACZ;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,aAAa;AAAA,UACb,QAAQ;AAAA,UACR,QAAQ;AAAA,UACR,UAAU;AAAA,UACV,QAAQ;AAAA,UACR,YAAY;AAAA,UACZ,SAAS;AAAA,UACT,UAAU;AAAA,UACV,QAAQ;AAAA,UACR,WAAW;AAAA,QACZ;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,aAAa;AAAA,UACb,QAAQ;AAAA,UACR,QAAQ;AAAA,UACR,UAAU;AAAA,UACV,QAAQ;AAAA,UACR,YAAY;AAAA,UACZ,SAAS;AAAA,UACT,UAAU;AAAA,UACV,QAAQ;AAAA,UACR,WAAW;AAAA,QACZ;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,aAAa;AAAA,UACb,QAAQ;AAAA,UACR,QAAQ;AAAA,UACR,UAAU;AAAA,UACV,QAAQ;AAAA,UACR,YAAY;AAAA,UACZ,SAAS;AAAA,UACT,UAAU;AAAA,UACV,QAAQ;AAAA,UACR,WAAW;AAAA,QACZ;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,aAAa;AAAA,UACb,QAAQ;AAAA,UACR,QAAQ;AAAA,UACR,UAAU;AAAA,UACV,QAAQ;AAAA,UACR,YAAY;AAAA,UACZ,SAAS;AAAA,UACT,UAAU;AAAA,UACV,QAAQ;AAAA,UACR,WAAW;AAAA,QACZ;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,aAAa;AAAA,UACb,QAAQ;AAAA,UACR,QAAQ;AAAA,UACR,UAAU;AAAA,UACV,QAAQ;AAAA,UACR,YAAY;AAAA,UACZ,SAAS;AAAA,UACT,UAAU;AAAA,UACV,QAAQ;AAAA,UACR,WAAW;AAAA,QACZ;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,aAAa;AAAA,UACb,QAAQ;AAAA,UACR,QAAQ;AAAA,UACR,UAAU;AAAA,UACV,QAAQ;AAAA,UACR,YAAY;AAAA,UACZ,SAAS;AAAA,UACT,UAAU;AAAA,UACV,QAAQ;AAAA,UACR,WAAW;AAAA,QACb;AAAA,MACF;AAAA,IACF;AAAA,EACD;AAAA,EACD,UAAU;AAAA,IACR,gBAAgB;AACd,UAAI,KAAK,eAAe,GAAG;AACzB,eAAO,KAAK;AAAA,aACP;AACL,cAAM,WAAW,KAAK,KAAK,KAAK,UAAU;AAC1C,eAAO,KAAK,MAAM,OAAO,UAAQ,KAAK,aAAa,QAAQ;AAAA,MAC7D;AAAA,IACF;AAAA,EACD;AAAA,EACD,SAAS;AAAA,IACP,SAAS;AACPA,oBAAG,MAAC,aAAY;AAAA,IACjB;AAAA,IAED,WAAW;AACTA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,SAAS;AAAA,QACT,YAAY;AAAA,MACd,CAAC;AAAA,IACF;AAAA,IAED,iBAAiB;AAEfA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,MACR,CAAC;AAAA,IACF;AAAA,IAED,UAAU,OAAO;AACf,WAAK,aAAa;AAAA,IACnB;AAAA,IAED,aAAa;AACXA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK;AAAA,MACP,CAAC;AAAA,IACF;AAAA,IAED,SAAS,MAAM;AACbA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,6EAA6E,KAAK,EAAE;AAAA,MAC3F,CAAC;AAAA,IACF;AAAA,IAED,WAAW,MAAM,GAAG;AAElB,YAAM,QAAQ,KAAK,MAAM,UAAU,UAAQ,KAAK,OAAO,KAAK,EAAE;AAC9D,UAAI,UAAU,IAAI;AAChB,aAAK,MAAM,KAAK,EAAE,UAAU,EAAE,OAAO;AACrC,aAAK,MAAM,KAAK,EAAE,SAAS,EAAE,OAAO,QAAQ,WAAW;AACvD,aAAK,MAAM,KAAK,EAAE,aAAa,EAAE,OAAO,QAAQ,QAAQ;AAAA,MAC1D;AAEAA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO,EAAE,OAAO,QAAQ,GAAG,KAAK,IAAI,QAAQ,GAAG,KAAK,IAAI;AAAA,QACxD,MAAM;AAAA,MACR,CAAC;AAAA,IACH;AAAA,EACF;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC/TA,GAAG,WAAW,eAAe;"}