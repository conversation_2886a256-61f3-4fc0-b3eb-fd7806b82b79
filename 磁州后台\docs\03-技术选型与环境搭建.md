# 磁州生活网后台管理系统 - 技术选型与环境搭建

## 文档信息
- **文档版本**: v1.0.0
- **创建日期**: 2025-01-04
- **文档类型**: 技术选型与环境搭建
- **目标读者**: 技术架构师、开发工程师、运维工程师

## 1. 技术选型

### 1.1 技术选型原则
- **成熟稳定**: 选择经过生产环境验证的成熟技术
- **社区活跃**: 技术社区活跃，文档完善，问题解决及时
- **性能优异**: 满足高并发、低延迟的性能要求
- **易于维护**: 代码可读性强，维护成本低
- **团队熟悉**: 团队对技术栈有一定的掌握基础

### 1.2 前端技术栈

#### 1.2.1 核心框架
```json
{
  "框架": "Vue 3.4+",
  "语言": "TypeScript 5.0+",
  "构建工具": "Vite 5.0+",
  "包管理器": "pnpm 8.0+",
  "选择理由": [
    "Vue 3 Composition API提供更好的逻辑复用",
    "TypeScript提供类型安全和更好的开发体验",
    "Vite提供极快的开发服务器和构建速度",
    "pnpm节省磁盘空间，安装速度快"
  ]
}
```

#### 1.2.2 UI组件库
```json
{
  "主要组件库": "Element Plus 2.4+",
  "图标库": "@element-plus/icons-vue",
  "图表库": "ECharts 5.4+",
  "富文本编辑器": "@wangeditor/editor 5.0+",
  "选择理由": [
    "Element Plus是Vue 3生态最成熟的企业级UI库",
    "ECharts功能强大，图表类型丰富",
    "wangEditor轻量级，易于集成和定制"
  ]
}
```

#### 1.2.3 状态管理与路由
```json
{
  "状态管理": "Pinia 2.1+",
  "路由管理": "Vue Router 4.2+",
  "HTTP客户端": "Axios 1.6+",
  "工具库": "Lodash-es 4.17+",
  "选择理由": [
    "Pinia是Vue 3官方推荐的状态管理库",
    "Vue Router 4完全支持Vue 3和TypeScript",
    "Axios是最流行的HTTP客户端库"
  ]
}
```

### 1.3 后端技术栈

#### 1.3.1 核心框架
```json
{
  "应用框架": "Spring Boot 3.2+",
  "微服务框架": "Spring Cloud 2023.0+",
  "安全框架": "Spring Security 6.2+",
  "JDK版本": "OpenJDK 17+",
  "选择理由": [
    "Spring Boot 3支持原生镜像，启动速度更快",
    "Spring Cloud生态完善，微服务组件齐全",
    "Spring Security提供企业级安全解决方案",
    "JDK 17是LTS版本，性能和安全性更好"
  ]
}
```

#### 1.3.2 数据访问层
```json
{
  "ORM框架": "MyBatis Plus 3.5+",
  "JPA实现": "Spring Data JPA 3.2+",
  "数据库连接池": "HikariCP 5.0+",
  "数据库迁移": "Flyway 9.0+",
  "选择理由": [
    "MyBatis Plus提供强大的CRUD操作和代码生成",
    "Spring Data JPA简化复杂查询操作",
    "HikariCP是性能最优的连接池",
    "Flyway提供数据库版本控制"
  ]
}
```

#### 1.3.3 微服务组件
```json
{
  "服务注册发现": "Nacos 2.3+",
  "配置中心": "Nacos Config",
  "API网关": "Spring Cloud Gateway 4.0+",
  "负载均衡": "Spring Cloud LoadBalancer",
  "熔断器": "Resilience4j 2.0+",
  "选择理由": [
    "Nacos提供服务注册发现和配置管理一体化解决方案",
    "Spring Cloud Gateway性能优异，支持响应式编程",
    "Resilience4j轻量级，功能完善"
  ]
}
```

### 1.4 数据存储技术栈

#### 1.4.1 数据库
```json
{
  "关系数据库": "MySQL 8.0+",
  "缓存数据库": "Redis 7.0+",
  "搜索引擎": "Elasticsearch 8.0+",
  "时序数据库": "InfluxDB 2.0+ (可选)",
  "选择理由": [
    "MySQL 8.0性能大幅提升，支持JSON数据类型",
    "Redis 7.0支持多线程，性能更强",
    "Elasticsearch 8.0搜索性能和安全性更好"
  ]
}
```

#### 1.4.2 消息队列
```json
{
  "消息队列": "RabbitMQ 3.12+",
  "备选方案": "Apache Kafka 3.0+",
  "选择理由": [
    "RabbitMQ易于使用，支持多种消息模式",
    "Kafka适合大数据量场景，可作为备选"
  ]
}
```

### 1.5 基础设施技术栈

#### 1.5.1 容器化与编排
```json
{
  "容器运行时": "Docker 24.0+",
  "容器编排": "Kubernetes 1.28+",
  "包管理": "Helm 3.12+",
  "镜像仓库": "Harbor 2.8+",
  "选择理由": [
    "Docker是容器化标准",
    "Kubernetes是容器编排事实标准",
    "Helm简化Kubernetes应用部署",
    "Harbor提供企业级镜像仓库"
  ]
}
```

#### 1.5.2 监控与日志
```json
{
  "监控系统": "Prometheus 2.45+ + Grafana 10.0+",
  "日志收集": "ELK Stack (Elasticsearch + Logstash + Kibana)",
  "链路追踪": "Jaeger 1.47+",
  "告警系统": "AlertManager 0.25+",
  "选择理由": [
    "Prometheus + Grafana是云原生监控标准",
    "ELK Stack是日志分析的经典组合",
    "Jaeger提供分布式链路追踪"
  ]
}
```

## 2. 开发环境搭建

### 2.1 开发工具要求

#### 2.1.1 必需工具
```bash
# JDK 17+
java -version
# openjdk version "17.0.8" 2023-07-18

# Node.js 18+
node --version
# v18.17.0

# Docker 24+
docker --version
# Docker version 24.0.5

# Git 2.40+
git --version
# git version 2.40.1
```

#### 2.1.2 推荐IDE
- **后端开发**: IntelliJ IDEA Ultimate 2023.2+
- **前端开发**: Visual Studio Code + Vue Language Features (Volar)
- **数据库管理**: DataGrip 或 DBeaver
- **API测试**: Postman 或 Insomnia

### 2.2 本地开发环境

#### 2.2.1 基础服务Docker Compose
```yaml
# docker-compose.dev.yml
version: '3.8'

services:
  mysql:
    image: mysql:8.0
    container_name: cizhou-mysql
    environment:
      MYSQL_ROOT_PASSWORD: root123456
      MYSQL_DATABASE: cizhou_admin
      MYSQL_USER: cizhou
      MYSQL_PASSWORD: cizhou123
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./docker/mysql/conf:/etc/mysql/conf.d
      - ./docker/mysql/init:/docker-entrypoint-initdb.d
    command: --default-authentication-plugin=mysql_native_password

  redis:
    image: redis:7-alpine
    container_name: cizhou-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
      - ./docker/redis/redis.conf:/etc/redis/redis.conf
    command: redis-server /etc/redis/redis.conf

  nacos:
    image: nacos/nacos-server:v2.3.0
    container_name: cizhou-nacos
    environment:
      MODE: standalone
      SPRING_DATASOURCE_PLATFORM: mysql
      MYSQL_SERVICE_HOST: mysql
      MYSQL_SERVICE_DB_NAME: nacos
      MYSQL_SERVICE_USER: root
      MYSQL_SERVICE_PASSWORD: root123456
    ports:
      - "8848:8848"
      - "9848:9848"
    depends_on:
      - mysql
    volumes:
      - nacos_data:/home/<USER>/data

  elasticsearch:
    image: elasticsearch:8.8.0
    container_name: cizhou-elasticsearch
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
    ports:
      - "9200:9200"
      - "9300:9300"
    volumes:
      - es_data:/usr/share/elasticsearch/data

  kibana:
    image: kibana:8.8.0
    container_name: cizhou-kibana
    environment:
      ELASTICSEARCH_HOSTS: http://elasticsearch:9200
    ports:
      - "5601:5601"
    depends_on:
      - elasticsearch

  rabbitmq:
    image: rabbitmq:3.12-management-alpine
    container_name: cizhou-rabbitmq
    environment:
      RABBITMQ_DEFAULT_USER: admin
      RABBITMQ_DEFAULT_PASS: admin123
    ports:
      - "5672:5672"
      - "15672:15672"
    volumes:
      - rabbitmq_data:/var/lib/rabbitmq

volumes:
  mysql_data:
  redis_data:
  nacos_data:
  es_data:
  rabbitmq_data:
```

#### 2.2.2 启动开发环境
```bash
# 1. 克隆项目
git clone <repository-url>
cd cizhou-admin

# 2. 启动基础服务
docker-compose -f docker-compose.dev.yml up -d

# 3. 等待服务启动完成
docker-compose -f docker-compose.dev.yml ps

# 4. 初始化数据库
# 访问 http://localhost:8848/nacos (admin/nacos)
# 访问 http://localhost:15672 (admin/admin123)
```

### 2.3 项目结构

#### 2.3.1 后端项目结构
```
cizhou-admin-backend/
├── cizhou-admin-parent/          # 父项目
├── cizhou-admin-common/          # 公共模块
│   ├── common-core/              # 核心工具类
│   ├── common-security/          # 安全组件
│   ├── common-redis/             # Redis组件
│   └── common-swagger/           # API文档组件
├── cizhou-admin-gateway/         # API网关
├── cizhou-admin-auth/            # 认证服务
├── cizhou-admin-modules/         # 业务模块
│   ├── user-service/             # 用户服务
│   ├── content-service/          # 内容服务
│   ├── merchant-service/         # 商家服务
│   ├── carpool-service/          # 拼车服务
│   ├── cashback-service/         # 返利服务
│   ├── payment-service/          # 支付服务
│   ├── message-service/          # 消息服务
│   └── file-service/             # 文件服务
├── cizhou-admin-api/             # API接口定义
└── cizhou-admin-sql/             # 数据库脚本
```

#### 2.3.2 前端项目结构
```
cizhou-admin-frontend/
├── public/                       # 静态资源
├── src/
│   ├── api/                      # API接口
│   ├── assets/                   # 资源文件
│   ├── components/               # 公共组件
│   ├── composables/              # 组合式函数
│   ├── layouts/                  # 布局组件
│   ├── router/                   # 路由配置
│   ├── stores/                   # 状态管理
│   ├── styles/                   # 样式文件
│   ├── types/                    # TypeScript类型
│   ├── utils/                    # 工具函数
│   ├── views/                    # 页面组件
│   ├── App.vue                   # 根组件
│   └── main.ts                   # 入口文件
├── tests/                        # 测试文件
├── .env.development              # 开发环境配置
├── .env.production               # 生产环境配置
├── package.json                  # 依赖配置
├── tsconfig.json                 # TypeScript配置
├── vite.config.ts                # Vite配置
└── README.md                     # 项目说明
```

### 2.4 开发规范配置

#### 2.4.1 代码格式化配置
```json
// .prettierrc
{
  "semi": false,
  "singleQuote": true,
  "tabWidth": 2,
  "trailingComma": "es5",
  "printWidth": 100,
  "endOfLine": "lf"
}
```

#### 2.4.2 ESLint配置
```javascript
// .eslintrc.js
module.exports = {
  extends: [
    '@vue/eslint-config-typescript',
    '@vue/eslint-config-prettier'
  ],
  rules: {
    '@typescript-eslint/no-unused-vars': 'error',
    '@typescript-eslint/no-explicit-any': 'warn',
    'vue/multi-word-component-names': 'off'
  }
}
```

#### 2.4.3 Git提交规范
```bash
# 安装commitizen
npm install -g commitizen cz-conventional-changelog

# 配置
echo '{ "path": "cz-conventional-changelog" }' > ~/.czrc

# 使用
git cz
```

## 3. 环境配置

### 3.1 开发环境配置

#### 3.1.1 后端配置文件
```yaml
# application-dev.yml
server:
  port: 8080

spring:
  datasource:
    url: ************************************************************************************************************
    username: cizhou
    password: cizhou123
    driver-class-name: com.mysql.cj.jdbc.Driver
    
  redis:
    host: localhost
    port: 6379
    database: 0
    timeout: 3000ms
    
  cloud:
    nacos:
      discovery:
        server-addr: localhost:8848
      config:
        server-addr: localhost:8848
        file-extension: yml

mybatis-plus:
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0

logging:
  level:
    com.cizhou: debug
```

#### 3.1.2 前端环境配置
```typescript
// .env.development
VITE_APP_TITLE=磁州生活网后台管理系统
VITE_APP_BASE_API=http://localhost:8080/api
VITE_APP_UPLOAD_URL=http://localhost:8080/api/file/upload
VITE_APP_WS_URL=ws://localhost:8080/ws
```

### 3.2 测试环境配置

#### 3.2.1 Docker部署配置
```yaml
# docker-compose.test.yml
version: '3.8'

services:
  cizhou-gateway:
    image: cizhou/admin-gateway:latest
    ports:
      - "8080:8080"
    environment:
      SPRING_PROFILES_ACTIVE: test
      NACOS_SERVER_ADDR: nacos:8848
    depends_on:
      - nacos
      - mysql
      - redis

  cizhou-user-service:
    image: cizhou/admin-user-service:latest
    environment:
      SPRING_PROFILES_ACTIVE: test
      NACOS_SERVER_ADDR: nacos:8848
    depends_on:
      - nacos
      - mysql
      - redis
```

### 3.3 生产环境配置

#### 3.3.1 Kubernetes部署配置
```yaml
# k8s/namespace.yaml
apiVersion: v1
kind: Namespace
metadata:
  name: cizhou-admin

---
# k8s/configmap.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: cizhou-admin-config
  namespace: cizhou-admin
data:
  application.yml: |
    spring:
      profiles:
        active: prod
      cloud:
        nacos:
          discovery:
            server-addr: nacos-service:8848
          config:
            server-addr: nacos-service:8848

---
# k8s/deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: cizhou-admin-gateway
  namespace: cizhou-admin
spec:
  replicas: 2
  selector:
    matchLabels:
      app: cizhou-admin-gateway
  template:
    metadata:
      labels:
        app: cizhou-admin-gateway
    spec:
      containers:
      - name: gateway
        image: cizhou/admin-gateway:latest
        ports:
        - containerPort: 8080
        env:
        - name: SPRING_PROFILES_ACTIVE
          value: "prod"
        volumeMounts:
        - name: config
          mountPath: /app/config
      volumes:
      - name: config
        configMap:
          name: cizhou-admin-config
```

## 4. 开发流程

### 4.1 分支管理策略
```
master          # 主分支，生产环境代码
├── develop     # 开发分支，集成测试
├── feature/*   # 功能分支
├── hotfix/*    # 热修复分支
└── release/*   # 发布分支
```

### 4.2 开发工作流
1. **功能开发**: 从develop创建feature分支
2. **代码提交**: 遵循commit规范
3. **代码审查**: 提交Pull Request
4. **集成测试**: 合并到develop分支
5. **发布准备**: 创建release分支
6. **生产发布**: 合并到master分支

---

**文档状态**: 技术选型完成，环境配置就绪
**下一步**: 开始详细的API接口设计
