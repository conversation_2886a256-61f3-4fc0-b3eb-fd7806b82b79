/*!
  * vue-i18n v9.1.7
  * (c) 2021 ka<PERSON><PERSON> ka<PERSON>
  * Released under the MIT License.
  */
import{ref as e,getCurrentInstance as t,computed as a,watch as n,createVNode as r,Text as l,h as o,Fragment as s,inject as i,onMounted as c,onUnmounted as u,isRef as m}from"vue";const f="function"==typeof Symbol&&"symbol"==typeof Symbol.toStringTag,g=e=>f?Symbol(e):e,p=e=>JSON.stringify(e).replace(/\u2028/g,"\\u2028").replace(/\u2029/g,"\\u2029").replace(/\u0027/g,"\\u0027"),b=e=>"number"==typeof e&&isFinite(e),_=e=>"[object RegExp]"===I(e),d=e=>S(e)&&0===Object.keys(e).length;function h(e,t){"undefined"!=typeof console&&(console.warn("[intlify] "+e),t&&console.warn(t.stack))}const v=Object.assign;function k(e){return e.replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&apos;")}const F=Object.prototype.hasOwnProperty;function y(e,t){return F.call(e,t)}const w=Array.isArray,L=e=>"function"==typeof e,M=e=>"string"==typeof e,W=e=>"boolean"==typeof e,$=e=>null!==e&&"object"==typeof e,T=Object.prototype.toString,I=e=>T.call(e),S=e=>"[object Object]"===I(e),E=[];E[0]={w:[0],i:[3,0],"[":[4],o:[7]},E[1]={w:[1],".":[2],"[":[4],o:[7]},E[2]={w:[2],i:[3,0],0:[3,0]},E[3]={i:[3,0],0:[3,0],w:[1,1],".":[2,1],"[":[4,1],o:[7,1]},E[4]={"'":[5,0],'"':[6,0],"[":[4,2],"]":[1,3],o:8,l:[4,0]},E[5]={"'":[4,0],o:8,l:[5,0]},E[6]={'"':[4,0],o:8,l:[6,0]};const O=/^\s?(?:true|false|-?[\d.]+|'[^']*'|"[^"]*")\s?$/;function P(e){if(null==e)return"o";switch(e.charCodeAt(0)){case 91:case 93:case 46:case 34:case 39:return e;case 95:case 36:case 45:return"i";case 9:case 10:case 13:case 160:case 65279:case 8232:case 8233:return"w"}return"i"}function C(e){const t=e.trim();return("0"!==e.charAt(0)||!isNaN(parseInt(e)))&&(O.test(t)?function(e){const t=e.charCodeAt(0);return t!==e.charCodeAt(e.length-1)||34!==t&&39!==t?e:e.slice(1,-1)}(t):"*"+t)}const H=new Map;function N(e,t){if(!$(e))return null;let a=H.get(t);if(a||(a=function(e){const t=[];let a,n,r,l,o,s,i,c=-1,u=0,m=0;const f=[];function g(){const t=e[c+1];if(5===u&&"'"===t||6===u&&'"'===t)return c++,r="\\"+t,f[0](),!0}for(f[0]=()=>{void 0===n?n=r:n+=r},f[1]=()=>{void 0!==n&&(t.push(n),n=void 0)},f[2]=()=>{f[0](),m++},f[3]=()=>{if(m>0)m--,u=4,f[0]();else{if(m=0,void 0===n)return!1;if(n=C(n),!1===n)return!1;f[1]()}};null!==u;)if(c++,a=e[c],"\\"!==a||!g()){if(l=P(a),i=E[u],o=i[l]||i.l||8,8===o)return;if(u=o[0],void 0!==o[1]&&(s=f[o[1]],s&&(r=a,!1===s())))return;if(7===u)return t}}(t),a&&H.set(t,a)),!a)return null;const n=a.length;let r=e,l=0;for(;l<n;){const e=r[a[l]];if(void 0===e)return null;r=e,l++}return r}function j(e){if(!$(e))return e;for(const t in e)if(y(e,t))if(t.includes(".")){const a=t.split("."),n=a.length-1;let r=e;for(let e=0;e<n;e++)a[e]in r||(r[a[e]]={}),r=r[a[e]];r[a[n]]=e[t],delete e[t],$(r[a[n]])&&j(r[a[n]])}else $(e[t])&&j(e[t]);return e}const R=e=>e,D=e=>"",x=e=>0===e.length?"":e.join(""),U=e=>null==e?"":w(e)||S(e)&&e.toString===T?JSON.stringify(e,null,2):String(e);function z(e,t){return e=Math.abs(e),2===t?e?e>1?1:0:1:e?Math.min(e,2):0}function A(e={}){const t=e.locale,a=function(e){const t=b(e.pluralIndex)?e.pluralIndex:-1;return e.named&&(b(e.named.count)||b(e.named.n))?b(e.named.count)?e.named.count:b(e.named.n)?e.named.n:t:t}(e),n=$(e.pluralRules)&&M(t)&&L(e.pluralRules[t])?e.pluralRules[t]:z,r=$(e.pluralRules)&&M(t)&&L(e.pluralRules[t])?z:void 0,l=e.list||[],o=e.named||{};b(e.pluralIndex)&&function(e,t){t.count||(t.count=e),t.n||(t.n=e)}(a,o);function s(t){const a=L(e.messages)?e.messages(t):!!$(e.messages)&&e.messages[t];return a||(e.parent?e.parent.message(t):D)}const i=S(e.processor)&&L(e.processor.normalize)?e.processor.normalize:x,c=S(e.processor)&&L(e.processor.interpolate)?e.processor.interpolate:U,u={list:e=>l[e],named:e=>o[e],plural:e=>e[n(a,e.length,r)],linked:(t,a)=>{const n=s(t)(u);return M(a)?(r=a,e.modifiers?e.modifiers[r]:R)(n):n;var r},message:s,type:S(e.processor)&&M(e.processor.type)?e.processor.type:"text",interpolate:c,normalize:i};return u}let J=0;function V(e={}){const t=M(e.version)?e.version:"9.1.7",a=M(e.locale)?e.locale:"en-US",n=w(e.fallbackLocale)||S(e.fallbackLocale)||M(e.fallbackLocale)||!1===e.fallbackLocale?e.fallbackLocale:a,r=S(e.messages)?e.messages:{[a]:{}},l=S(e.datetimeFormats)?e.datetimeFormats:{[a]:{}},o=S(e.numberFormats)?e.numberFormats:{[a]:{}},s=v({},e.modifiers||{},{upper:e=>M(e)?e.toUpperCase():e,lower:e=>M(e)?e.toLowerCase():e,capitalize:e=>M(e)?`${e.charAt(0).toLocaleUpperCase()}${e.substr(1)}`:e}),i=e.pluralRules||{},c=L(e.missing)?e.missing:null,u=!W(e.missingWarn)&&!_(e.missingWarn)||e.missingWarn,m=!W(e.fallbackWarn)&&!_(e.fallbackWarn)||e.fallbackWarn,f=!!e.fallbackFormat,g=!!e.unresolving,p=L(e.postTranslation)?e.postTranslation:null,b=S(e.processor)?e.processor:null,d=!W(e.warnHtmlMessage)||e.warnHtmlMessage,k=!!e.escapeParameter,F=L(e.messageCompiler)?e.messageCompiler:undefined,y=L(e.onWarn)?e.onWarn:h,T=e,I=$(T.__datetimeFormatters)?T.__datetimeFormatters:new Map,E=$(T.__numberFormatters)?T.__numberFormatters:new Map,O=$(T.__meta)?T.__meta:{};J++;return{version:t,cid:J,locale:a,fallbackLocale:n,messages:r,datetimeFormats:l,numberFormats:o,modifiers:s,pluralRules:i,missing:c,missingWarn:u,fallbackWarn:m,fallbackFormat:f,unresolving:g,postTranslation:p,processor:b,warnHtmlMessage:d,escapeParameter:k,messageCompiler:F,onWarn:y,__datetimeFormatters:I,__numberFormatters:E,__meta:O}}function q(e,t,a,n,r){const{missing:l}=e;if(null!==l){const n=l(e,a,t,r);return M(n)?n:t}return t}function B(e,t,a){const n=e;n.__localeChainCache||(n.__localeChainCache=new Map);let r=n.__localeChainCache.get(a);if(!r){r=[];let e=[a];for(;w(e);)e=G(r,e,t);const l=w(t)?t:S(t)?t.default?t.default:null:t;e=M(l)?[l]:l,w(e)&&G(r,e,!1),n.__localeChainCache.set(a,r)}return r}function G(e,t,a){let n=!0;for(let r=0;r<t.length&&W(n);r++){M(t[r])&&(n=Y(e,t[r],a))}return n}function Y(e,t,a){let n;const r=t.split("-");do{n=Z(e,r.join("-"),a),r.splice(-1,1)}while(r.length&&!0===n);return n}function Z(e,t,a){let n=!1;if(!e.includes(t)&&(n=!0,t)){n="!"!==t[t.length-1];const r=t.replace(/!/g,"");e.push(r),(w(a)||S(a))&&a[r]&&(n=a[r])}return n}function K(e,t,a){e.__localeChainCache=new Map,B(e,a,t)}const Q=()=>"",X=e=>L(e);function ee(e,...t){const{fallbackFormat:a,postTranslation:n,unresolving:r,fallbackLocale:l,messages:o}=e,[s,i]=ae(...t),c=(W(i.missingWarn),W(i.fallbackWarn),W(i.escapeParameter)?i.escapeParameter:e.escapeParameter),u=!!i.resolvedMessage,m=M(i.default)||W(i.default)?W(i.default)?s:i.default:a?s:"",f=a||""!==m,g=M(i.locale)?i.locale:e.locale;c&&function(e){w(e.list)?e.list=e.list.map((e=>M(e)?k(e):e)):$(e.named)&&Object.keys(e.named).forEach((t=>{M(e.named[t])&&(e.named[t]=k(e.named[t]))}))}(i);let[p,_,d]=u?[s,g,o[g]||{}]:function(e,t,a,n,r,l){const{messages:o}=e,s=B(e,n,a);let i,c={},u=null;const m="translate";for(let a=0;a<s.length&&(i=s[a],c=o[i]||{},null===(u=N(c,t))&&(u=c[t]),!M(u)&&!L(u));a++){const a=q(e,t,i,0,m);a!==t&&(u=a)}return[u,i,c]}(e,s,g,l),h=s;if(u||M(p)||X(p)||f&&(p=m,h=p),!(u||(M(p)||X(p))&&M(_)))return r?-1:s;let v=!1;const F=X(p)?p:te(e,s,_,p,h,(()=>{v=!0}));if(v)return p;const y=function(e,t,a){return t(a)}(0,F,A(function(e,t,a,n){const{modifiers:r,pluralRules:l}=e,o={locale:t,modifiers:r,pluralRules:l,messages:n=>{const r=N(a,n);if(M(r)){let a=!1;const l=te(e,n,t,r,n,(()=>{a=!0}));return a?Q:l}return X(r)?r:Q}};e.processor&&(o.processor=e.processor);n.list&&(o.list=n.list);n.named&&(o.named=n.named);b(n.plural)&&(o.pluralIndex=n.plural);return o}(e,_,d,i)));return n?n(y):y}function te(e,t,a,n,r,l){const{messageCompiler:o,warnHtmlMessage:s}=e;if(X(n)){const e=n;return e.locale=e.locale||a,e.key=e.key||t,e}const i=o(n,function(e,t,a,n,r,l){return{warnHtmlMessage:r,onError:e=>{throw l&&l(e),e},onCacheKey:e=>((e,t,a)=>p({l:e,k:t,s:a}))(t,a,e)}}(0,a,r,0,s,l));return i.locale=a,i.key=t,i.source=n,i}function ae(...e){const[t,a,n]=e,r={};if(!M(t)&&!b(t)&&!X(t))throw Error(14);const l=b(t)?String(t):(X(t),t);return b(a)?r.plural=a:M(a)?r.default=a:S(a)&&!d(a)?r.named=a:w(a)&&(r.list=a),b(n)?r.plural=n:M(n)?r.default=n:S(n)&&v(r,n),[l,r]}function ne(e,...t){const{datetimeFormats:a,unresolving:n,fallbackLocale:r}=e,{__datetimeFormatters:l}=e,[o,s,i,c]=re(...t);W(i.missingWarn);W(i.fallbackWarn);const u=!!i.part,m=M(i.locale)?i.locale:e.locale,f=B(e,r,m);if(!M(o)||""===o)return new Intl.DateTimeFormat(m).format(s);let g,p={},b=null;for(let t=0;t<f.length&&(g=f[t],p=a[g]||{},b=p[o],!S(b));t++)q(e,o,g,0,"datetime format");if(!S(b)||!M(g))return n?-1:o;let _=`${g}__${o}`;d(c)||(_=`${_}__${JSON.stringify(c)}`);let h=l.get(_);return h||(h=new Intl.DateTimeFormat(g,v({},b,c)),l.set(_,h)),u?h.formatToParts(s):h.format(s)}function re(...e){const[t,a,n,r]=e;let l,o={},s={};if(M(t)){if(!/\d{4}-\d{2}-\d{2}(T.*)?/.test(t))throw Error(16);l=new Date(t);try{l.toISOString()}catch(e){throw Error(16)}}else if("[object Date]"===I(t)){if(isNaN(t.getTime()))throw Error(15);l=t}else{if(!b(t))throw Error(14);l=t}return M(a)?o.key=a:S(a)&&(o=a),M(n)?o.locale=n:S(n)&&(s=n),S(r)&&(s=r),[o.key||"",l,o,s]}function le(e,t,a){const n=e;for(const e in a){const a=`${t}__${e}`;n.__datetimeFormatters.has(a)&&n.__datetimeFormatters.delete(a)}}function oe(e,...t){const{numberFormats:a,unresolving:n,fallbackLocale:r}=e,{__numberFormatters:l}=e,[o,s,i,c]=se(...t);W(i.missingWarn);W(i.fallbackWarn);const u=!!i.part,m=M(i.locale)?i.locale:e.locale,f=B(e,r,m);if(!M(o)||""===o)return new Intl.NumberFormat(m).format(s);let g,p={},b=null;for(let t=0;t<f.length&&(g=f[t],p=a[g]||{},b=p[o],!S(b));t++)q(e,o,g,0,"number format");if(!S(b)||!M(g))return n?-1:o;let _=`${g}__${o}`;d(c)||(_=`${_}__${JSON.stringify(c)}`);let h=l.get(_);return h||(h=new Intl.NumberFormat(g,v({},b,c)),l.set(_,h)),u?h.formatToParts(s):h.format(s)}function se(...e){const[t,a,n,r]=e;let l={},o={};if(!b(t))throw Error(14);const s=t;return M(a)?l.key=a:S(a)&&(l=a),M(n)?l.locale=n:S(n)&&(o=n),S(r)&&(o=r),[l.key||"",s,l,o]}function ie(e,t,a){const n=e;for(const e in a){const a=`${t}__${e}`;n.__numberFormatters.has(a)&&n.__numberFormatters.delete(a)}}const ce="9.1.7",ue=g("__transrateVNode"),me=g("__datetimeParts"),fe=g("__numberParts"),ge=g("__setPluralRules");let pe=0;function be(e){return(a,n,r,l)=>e(n,r,t()||void 0,l)}function _e(e,t){const{messages:a,__i18n:n}=t,r=S(a)?a:w(n)?{}:{[e]:{}};if(w(n)&&n.forEach((({locale:e,resource:t})=>{e?(r[e]=r[e]||{},he(t,r[e])):he(t,r)})),t.flatJson)for(const e in r)y(r,e)&&j(r[e]);return r}const de=e=>!$(e)||w(e);function he(e,t){if(de(e)||de(t))throw Error(20);for(const a in e)y(e,a)&&(de(e[a])||de(t[a])?t[a]=e[a]:he(e[a],t[a]))}function ve(t={}){const{__root:o}=t,s=void 0===o;let i=!W(t.inheritLocale)||t.inheritLocale;const c=e(o&&i?o.locale.value:M(t.locale)?t.locale:"en-US"),u=e(o&&i?o.fallbackLocale.value:M(t.fallbackLocale)||w(t.fallbackLocale)||S(t.fallbackLocale)||!1===t.fallbackLocale?t.fallbackLocale:c.value),m=e(_e(c.value,t)),f=e(S(t.datetimeFormats)?t.datetimeFormats:{[c.value]:{}}),g=e(S(t.numberFormats)?t.numberFormats:{[c.value]:{}});let p=o?o.missingWarn:!W(t.missingWarn)&&!_(t.missingWarn)||t.missingWarn,d=o?o.fallbackWarn:!W(t.fallbackWarn)&&!_(t.fallbackWarn)||t.fallbackWarn,h=o?o.fallbackRoot:!W(t.fallbackRoot)||t.fallbackRoot,k=!!t.fallbackFormat,F=L(t.missing)?t.missing:null,y=L(t.missing)?be(t.missing):null,T=L(t.postTranslation)?t.postTranslation:null,I=!W(t.warnHtmlMessage)||t.warnHtmlMessage,E=!!t.escapeParameter;const O=o?o.modifiers:S(t.modifiers)?t.modifiers:{};let P,C=t.pluralRules||o&&o.pluralRules;P=V({version:"9.1.7",locale:c.value,fallbackLocale:u.value,messages:m.value,datetimeFormats:f.value,numberFormats:g.value,modifiers:O,pluralRules:C,missing:null===y?void 0:y,missingWarn:p,fallbackWarn:d,fallbackFormat:k,unresolving:!0,postTranslation:null===T?void 0:T,warnHtmlMessage:I,escapeParameter:E,__datetimeFormatters:S(P)?P.__datetimeFormatters:void 0,__numberFormatters:S(P)?P.__numberFormatters:void 0,__v_emitter:S(P)?P.__v_emitter:void 0,__meta:{framework:"vue"}}),K(P,c.value,u.value);const H=a({get:()=>c.value,set:e=>{c.value=e,P.locale=c.value}}),j=a({get:()=>u.value,set:e=>{u.value=e,P.fallbackLocale=u.value,K(P,c.value,e)}}),R=a((()=>m.value)),D=a((()=>f.value)),x=a((()=>g.value));function U(e,t,a,n,r,l){let s;if(s=e(P),b(s)&&-1===s){const[e,a]=t();return o&&h?n(o):r(e)}if(l(s))return s;throw Error(14)}function z(...e){return U((t=>ee(t,...e)),(()=>ae(...e)),0,(t=>t.t(...e)),(e=>e),(e=>M(e)))}const A={normalize:function(e){return e.map((e=>M(e)?r(l,null,e,0):e))},interpolate:e=>e,type:"vnode"};function J(e){return m.value[e]||{}}pe++,o&&(n(o.locale,(e=>{i&&(c.value=e,P.locale=e,K(P,c.value,u.value))})),n(o.fallbackLocale,(e=>{i&&(u.value=e,P.fallbackLocale=e,K(P,c.value,u.value))})));return{id:pe,locale:H,fallbackLocale:j,get inheritLocale(){return i},set inheritLocale(e){i=e,e&&o&&(c.value=o.locale.value,u.value=o.fallbackLocale.value,K(P,c.value,u.value))},get availableLocales(){return Object.keys(m.value).sort()},messages:R,datetimeFormats:D,numberFormats:x,get modifiers(){return O},get pluralRules(){return C||{}},get isGlobal(){return s},get missingWarn(){return p},set missingWarn(e){p=e,P.missingWarn=p},get fallbackWarn(){return d},set fallbackWarn(e){d=e,P.fallbackWarn=d},get fallbackRoot(){return h},set fallbackRoot(e){h=e},get fallbackFormat(){return k},set fallbackFormat(e){k=e,P.fallbackFormat=k},get warnHtmlMessage(){return I},set warnHtmlMessage(e){I=e,P.warnHtmlMessage=e},get escapeParameter(){return E},set escapeParameter(e){E=e,P.escapeParameter=e},t:z,rt:function(...e){const[t,a,n]=e;if(n&&!$(n))throw Error(15);return z(t,a,v({resolvedMessage:!0},n||{}))},d:function(...e){return U((t=>ne(t,...e)),(()=>re(...e)),0,(t=>t.d(...e)),(()=>""),(e=>M(e)))},n:function(...e){return U((t=>oe(t,...e)),(()=>se(...e)),0,(t=>t.n(...e)),(()=>""),(e=>M(e)))},te:function(e,t){return null!==N(J(M(t)?t:c.value),e)},tm:function(e){const t=function(e){let t=null;const a=B(P,u.value,c.value);for(let n=0;n<a.length;n++){const r=N(m.value[a[n]]||{},e);if(null!=r){t=r;break}}return t}(e);return null!=t?t:o&&o.tm(e)||{}},getLocaleMessage:J,setLocaleMessage:function(e,t){m.value[e]=t,P.messages=m.value},mergeLocaleMessage:function(e,t){m.value[e]=m.value[e]||{},he(t,m.value[e]),P.messages=m.value},getDateTimeFormat:function(e){return f.value[e]||{}},setDateTimeFormat:function(e,t){f.value[e]=t,P.datetimeFormats=f.value,le(P,e,t)},mergeDateTimeFormat:function(e,t){f.value[e]=v(f.value[e]||{},t),P.datetimeFormats=f.value,le(P,e,t)},getNumberFormat:function(e){return g.value[e]||{}},setNumberFormat:function(e,t){g.value[e]=t,P.numberFormats=g.value,ie(P,e,t)},mergeNumberFormat:function(e,t){g.value[e]=v(g.value[e]||{},t),P.numberFormats=g.value,ie(P,e,t)},getPostTranslationHandler:function(){return L(T)?T:null},setPostTranslationHandler:function(e){T=e,P.postTranslation=e},getMissingHandler:function(){return F},setMissingHandler:function(e){null!==e&&(y=be(e)),F=e,P.missing=y},[ue]:function(...e){return U((t=>{let a;const n=t;try{n.processor=A,a=ee(n,...e)}finally{n.processor=null}return a}),(()=>ae(...e)),0,(t=>t[ue](...e)),(e=>[r(l,null,e,0)]),(e=>w(e)))},[fe]:function(...e){return U((t=>oe(t,...e)),(()=>se(...e)),0,(t=>t[fe](...e)),(()=>[]),(e=>M(e)||w(e)))},[me]:function(...e){return U((t=>ne(t,...e)),(()=>re(...e)),0,(t=>t[me](...e)),(()=>[]),(e=>M(e)||w(e)))},[ge]:function(e){C=e,P.pluralRules=C}}}function ke(e={}){const t=ve(function(e){const t=M(e.locale)?e.locale:"en-US",a=M(e.fallbackLocale)||w(e.fallbackLocale)||S(e.fallbackLocale)||!1===e.fallbackLocale?e.fallbackLocale:t,n=L(e.missing)?e.missing:void 0,r=!W(e.silentTranslationWarn)&&!_(e.silentTranslationWarn)||!e.silentTranslationWarn,l=!W(e.silentFallbackWarn)&&!_(e.silentFallbackWarn)||!e.silentFallbackWarn,o=!W(e.fallbackRoot)||e.fallbackRoot,s=!!e.formatFallbackMessages,i=S(e.modifiers)?e.modifiers:{},c=e.pluralizationRules,u=L(e.postTranslation)?e.postTranslation:void 0,m=!M(e.warnHtmlInMessage)||"off"!==e.warnHtmlInMessage,f=!!e.escapeParameterHtml,g=!W(e.sync)||e.sync;let p=e.messages;if(S(e.sharedMessages)){const t=e.sharedMessages;p=Object.keys(t).reduce(((e,a)=>{const n=e[a]||(e[a]={});return v(n,t[a]),e}),p||{})}const{__i18n:b,__root:d}=e;return{locale:t,fallbackLocale:a,messages:p,flatJson:e.flatJson,datetimeFormats:e.datetimeFormats,numberFormats:e.numberFormats,missing:n,missingWarn:r,fallbackWarn:l,fallbackRoot:o,fallbackFormat:s,modifiers:i,pluralRules:c,postTranslation:u,warnHtmlMessage:m,escapeParameter:f,inheritLocale:g,__i18n:b,__root:d}}(e)),a={id:t.id,get locale(){return t.locale.value},set locale(e){t.locale.value=e},get fallbackLocale(){return t.fallbackLocale.value},set fallbackLocale(e){t.fallbackLocale.value=e},get messages(){return t.messages.value},get datetimeFormats(){return t.datetimeFormats.value},get numberFormats(){return t.numberFormats.value},get availableLocales(){return t.availableLocales},get formatter(){return{interpolate:()=>[]}},set formatter(e){},get missing(){return t.getMissingHandler()},set missing(e){t.setMissingHandler(e)},get silentTranslationWarn(){return W(t.missingWarn)?!t.missingWarn:t.missingWarn},set silentTranslationWarn(e){t.missingWarn=W(e)?!e:e},get silentFallbackWarn(){return W(t.fallbackWarn)?!t.fallbackWarn:t.fallbackWarn},set silentFallbackWarn(e){t.fallbackWarn=W(e)?!e:e},get modifiers(){return t.modifiers},get formatFallbackMessages(){return t.fallbackFormat},set formatFallbackMessages(e){t.fallbackFormat=e},get postTranslation(){return t.getPostTranslationHandler()},set postTranslation(e){t.setPostTranslationHandler(e)},get sync(){return t.inheritLocale},set sync(e){t.inheritLocale=e},get warnHtmlInMessage(){return t.warnHtmlMessage?"warn":"off"},set warnHtmlInMessage(e){t.warnHtmlMessage="off"!==e},get escapeParameterHtml(){return t.escapeParameter},set escapeParameterHtml(e){t.escapeParameter=e},get preserveDirectiveContent(){return!0},set preserveDirectiveContent(e){},get pluralizationRules(){return t.pluralRules||{}},__composer:t,t(...e){const[a,n,r]=e,l={};let o=null,s=null;if(!M(a))throw Error(15);const i=a;return M(n)?l.locale=n:w(n)?o=n:S(n)&&(s=n),w(r)?o=r:S(r)&&(s=r),t.t(i,o||s||{},l)},rt:(...e)=>t.rt(...e),tc(...e){const[a,n,r]=e,l={plural:1};let o=null,s=null;if(!M(a))throw Error(15);const i=a;return M(n)?l.locale=n:b(n)?l.plural=n:w(n)?o=n:S(n)&&(s=n),M(r)?l.locale=r:w(r)?o=r:S(r)&&(s=r),t.t(i,o||s||{},l)},te:(e,a)=>t.te(e,a),tm:e=>t.tm(e),getLocaleMessage:e=>t.getLocaleMessage(e),setLocaleMessage(e,a){t.setLocaleMessage(e,a)},mergeLocaleMessage(e,a){t.mergeLocaleMessage(e,a)},d:(...e)=>t.d(...e),getDateTimeFormat:e=>t.getDateTimeFormat(e),setDateTimeFormat(e,a){t.setDateTimeFormat(e,a)},mergeDateTimeFormat(e,a){t.mergeDateTimeFormat(e,a)},n:(...e)=>t.n(...e),getNumberFormat:e=>t.getNumberFormat(e),setNumberFormat(e,a){t.setNumberFormat(e,a)},mergeNumberFormat(e,a){t.mergeNumberFormat(e,a)},getChoiceIndex:(e,t)=>-1,__onComponentInstanceCreated(t){const{componentInstanceCreatedListener:n}=e;n&&n(t,a)}};return a}const Fe={tag:{type:[String,Object]},locale:{type:String},scope:{type:String,validator:e=>"parent"===e||"global"===e,default:"parent"},i18n:{type:Object}},ye={name:"i18n-t",props:v({keypath:{type:String,required:!0},plural:{type:[Number,String],validator:e=>b(e)||!isNaN(e)}},Fe),setup(e,t){const{slots:a,attrs:n}=t,r=e.i18n||Ee({useScope:e.scope}),l=Object.keys(a).filter((e=>"_"!==e));return()=>{const a={};e.locale&&(a.locale=e.locale),void 0!==e.plural&&(a.plural=M(e.plural)?+e.plural:e.plural);const i=function({slots:e},t){return 1===t.length&&"default"===t[0]?e.default?e.default():[]:t.reduce(((t,a)=>{const n=e[a];return n&&(t[a]=n()),t}),{})}(t,l),c=r[ue](e.keypath,i,a),u=v({},n);return M(e.tag)||$(e.tag)?o(e.tag,u,c):o(s,u,c)}}};function we(e,t,a,n){const{slots:r,attrs:l}=t;return()=>{const t={part:!0};let i={};e.locale&&(t.locale=e.locale),M(e.format)?t.key=e.format:$(e.format)&&(M(e.format.key)&&(t.key=e.format.key),i=Object.keys(e.format).reduce(((t,n)=>a.includes(n)?v({},t,{[n]:e.format[n]}):t),{}));const c=n(e.value,t,i);let u=[t.key];w(c)?u=c.map(((e,t)=>{const a=r[e.type];return a?a({[e.type]:e.value,index:t,parts:c}):[e.value]})):M(c)&&(u=[c]);const m=v({},l);return M(e.tag)||$(e.tag)?o(e.tag,m,u):o(s,m,u)}}const Le=["localeMatcher","style","unit","unitDisplay","currency","currencyDisplay","useGrouping","numberingSystem","minimumIntegerDigits","minimumFractionDigits","maximumFractionDigits","minimumSignificantDigits","maximumSignificantDigits","notation","formatMatcher"],Me={name:"i18n-n",props:v({value:{type:Number,required:!0},format:{type:[String,Object]}},Fe),setup(e,t){const a=e.i18n||Ee({useScope:"parent"});return we(e,t,Le,((...e)=>a[fe](...e)))}},We=["dateStyle","timeStyle","fractionalSecondDigits","calendar","dayPeriod","numberingSystem","localeMatcher","timeZone","hour12","hourCycle","formatMatcher","weekday","era","year","month","day","hour","minute","second","timeZoneName"],$e={name:"i18n-d",props:v({value:{type:[Number,Date],required:!0},format:{type:[String,Object]}},Fe),setup(e,t){const a=e.i18n||Ee({useScope:"parent"});return we(e,t,We,((...e)=>a[me](...e)))}};function Te(e){const t=(t,{instance:a,value:n})=>{if(!a||!a.$)throw Error(22);const r=function(e,t){const a=e;if("composition"===e.mode)return a.__getInstance(t)||e.global;{const n=a.__getInstance(t);return null!=n?n.__composer:e.global.__composer}}(e,a.$),l=function(e){if(M(e))return{path:e};if(S(e)){if(!("path"in e))throw Error(19,"path");return e}throw Error(20)}(n);t.textContent=r.t(...function(e){const{path:t,locale:a,args:n,choice:r,plural:l}=e,o={},s=n||{};M(a)&&(o.locale=a);b(r)&&(o.plural=r);b(l)&&(o.plural=l);return[t,s,o]}(l))};return{beforeMount:t,beforeUpdate:t}}function Ie(e,t){e.locale=t.locale||e.locale,e.fallbackLocale=t.fallbackLocale||e.fallbackLocale,e.missing=t.missing||e.missing,e.silentTranslationWarn=t.silentTranslationWarn||e.silentFallbackWarn,e.silentFallbackWarn=t.silentFallbackWarn||e.silentFallbackWarn,e.formatFallbackMessages=t.formatFallbackMessages||e.formatFallbackMessages,e.postTranslation=t.postTranslation||e.postTranslation,e.warnHtmlInMessage=t.warnHtmlInMessage||e.warnHtmlInMessage,e.escapeParameterHtml=t.escapeParameterHtml||e.escapeParameterHtml,e.sync=t.sync||e.sync,e.__composer[ge](t.pluralizationRules||e.pluralizationRules);const a=_e(e.locale,{messages:t.messages,__i18n:t.__i18n});return Object.keys(a).forEach((t=>e.mergeLocaleMessage(t,a[t]))),t.datetimeFormats&&Object.keys(t.datetimeFormats).forEach((a=>e.mergeDateTimeFormat(a,t.datetimeFormats[a]))),t.numberFormats&&Object.keys(t.numberFormats).forEach((a=>e.mergeNumberFormat(a,t.numberFormats[a]))),e}function Se(e={}){const a=!W(e.legacy)||e.legacy,n=!!e.globalInjection,r=new Map,l=a?ke(e):ve(e),o=g(""),s={get mode(){return a?"legacy":"composition"},async install(e,...r){e.__VUE_I18N_SYMBOL__=o,e.provide(e.__VUE_I18N_SYMBOL__,s),!a&&n&&function(e,t){const a=Object.create(null);Oe.forEach((e=>{const n=Object.getOwnPropertyDescriptor(t,e);if(!n)throw Error(22);const r=m(n.value)?{get:()=>n.value.value,set(e){n.value.value=e}}:{get:()=>n.get&&n.get()};Object.defineProperty(a,e,r)})),e.config.globalProperties.$i18n=a,Pe.forEach((a=>{const n=Object.getOwnPropertyDescriptor(t,a);if(!n||!n.value)throw Error(22);Object.defineProperty(e.config.globalProperties,`$${a}`,n)}))}(e,s.global),function(e,t,...a){const n=S(a[0])?a[0]:{},r=!!n.useI18nComponentName;(!W(n.globalInstall)||n.globalInstall)&&(e.component(r?"i18n":ye.name,ye),e.component(Me.name,Me),e.component($e.name,$e)),e.directive("t",Te(t))}(e,s,...r),a&&e.mixin(function(e,a,n){return{beforeCreate(){const r=t();if(!r)throw Error(22);const l=this.$options;if(l.i18n){const t=l.i18n;l.__i18n&&(t.__i18n=l.__i18n),t.__root=a,this.$i18n=this===this.$root?Ie(e,t):ke(t)}else this.$i18n=l.__i18n?this===this.$root?Ie(e,l):ke({__i18n:l.__i18n,__root:a}):e;e.__onComponentInstanceCreated(this.$i18n),n.__setInstance(r,this.$i18n),this.$t=(...e)=>this.$i18n.t(...e),this.$rt=(...e)=>this.$i18n.rt(...e),this.$tc=(...e)=>this.$i18n.tc(...e),this.$te=(e,t)=>this.$i18n.te(e,t),this.$d=(...e)=>this.$i18n.d(...e),this.$n=(...e)=>this.$i18n.n(...e),this.$tm=e=>this.$i18n.tm(e)},mounted(){},beforeUnmount(){const e=t();if(!e)throw Error(22);delete this.$t,delete this.$rt,delete this.$tc,delete this.$te,delete this.$d,delete this.$n,delete this.$tm,n.__deleteInstance(e),delete this.$i18n}}}(l,l.__composer,s))},get global(){return l},__instances:r,__getInstance:e=>r.get(e)||null,__setInstance(e,t){r.set(e,t)},__deleteInstance(e){r.delete(e)}};return s}function Ee(e={}){const a=t();if(null==a)throw Error(16);if(!a.appContext.app.__VUE_I18N_SYMBOL__)throw Error(17);const n=i(a.appContext.app.__VUE_I18N_SYMBOL__);if(!n)throw Error(22);const r="composition"===n.mode?n.global:n.global.__composer,l=d(e)?"__i18n"in a.type?"local":"global":e.useScope?e.useScope:"local";if("global"===l){let t=$(e.messages)?e.messages:{};"__i18nGlobal"in a.type&&(t=_e(r.locale.value,{messages:t,__i18n:a.type.__i18nGlobal}));const n=Object.keys(t);if(n.length&&n.forEach((e=>{r.mergeLocaleMessage(e,t[e])})),$(e.datetimeFormats)){const t=Object.keys(e.datetimeFormats);t.length&&t.forEach((t=>{r.mergeDateTimeFormat(t,e.datetimeFormats[t])}))}if($(e.numberFormats)){const t=Object.keys(e.numberFormats);t.length&&t.forEach((t=>{r.mergeNumberFormat(t,e.numberFormats[t])}))}return r}if("parent"===l){let e=function(e,t){let a=null;const n=t.root;let r=t.parent;for(;null!=r;){const t=e;if("composition"===e.mode)a=t.__getInstance(r);else{const e=t.__getInstance(r);null!=e&&(a=e.__composer)}if(null!=a)break;if(n===r)break;r=r.parent}return a}(n,a);return null==e&&(e=r),e}if("legacy"===n.mode)throw Error(18);const o=n;let s=o.__getInstance(a);if(null==s){const t=a.type,n=v({},e);t.__i18n&&(n.__i18n=t.__i18n),r&&(n.__root=r),s=ve(n),function(e,t,a){c((()=>{}),t),u((()=>{e.__deleteInstance(t)}),t)}(o,a),o.__setInstance(a,s)}return s}const Oe=["locale","fallbackLocale","availableLocales"],Pe=["t","rt","d","n","tm"];export{$e as DatetimeFormat,Me as NumberFormat,ye as Translation,ce as VERSION,Se as createI18n,Ee as useI18n,Te as vTDirective};
