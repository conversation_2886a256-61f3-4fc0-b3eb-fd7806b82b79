{"version": 3, "file": "FloatPromotionButton.js", "sources": ["components/FloatPromotionButton.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniComponent:/QzovVXNlcnMvQWRtaW5pc3RyYXRvci9EZXNrdG9wL-aWsOW7uuaWh-S7tuWkuS_no4Hlt57liY3lj7AvY29tcG9uZW50cy9GbG9hdFByb21vdGlvbkJ1dHRvbi52dWU"], "sourcesContent": ["<template>\r\n  <view>\r\n    <!-- 悬浮推广按钮 -->\r\n    <view\r\n      class=\"float-promotion-button\"\r\n      :style=\"{\r\n        right: position.right,\r\n        bottom: position.bottom,\r\n        width: size,\r\n        height: size\r\n      }\"\r\n      @click=\"showPromotionModal = true\"\r\n    >\r\n      <image class=\"button-icon\" :src=\"icon\" mode=\"aspectFit\"></image>\r\n    </view>\r\n\r\n    <!-- 推广操作弹窗 -->\r\n    <view v-if=\"showPromotionModal\" class=\"promotion-modal-mask\" @click=\"showPromotionModal = false\">\r\n      <view class=\"promotion-modal-content\" @click.stop>\r\n        <view class=\"modal-header\">\r\n          <text class=\"modal-title\">推广工具</text>\r\n          <view class=\"modal-close\" @click=\"showPromotionModal = false\">\r\n            <text>×</text>\r\n          </view>\r\n        </view>\r\n        <ConfigurablePremiumActions\r\n          :pageType=\"pageType\"\r\n          :showMode=\"showMode\"\r\n          :itemData=\"itemData\"\r\n          @action-completed=\"handleActionCompleted\"\r\n          @action-cancelled=\"handleActionCancelled\"\r\n        />\r\n      </view>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script setup>\r\nimport { ref } from 'vue';\r\nimport ConfigurablePremiumActions from '@/components/premium/ConfigurablePremiumActions.vue';\r\n\r\n// 定义组件属性\r\nconst props = defineProps({\r\n  // 按钮位置\r\n  position: {\r\n    type: Object,\r\n    default: () => ({\r\n      right: '30rpx',\r\n      bottom: '180rpx'\r\n    })\r\n  },\r\n  // 按钮大小\r\n  size: {\r\n    type: String,\r\n    default: '80rpx'\r\n  },\r\n  // 按钮图标\r\n  icon: {\r\n    type: String,\r\n    default: '/static/images/promotion-icon.svg'\r\n  },\r\n  // 页面类型\r\n  pageType: {\r\n    type: String,\r\n    default: 'publish'\r\n  },\r\n  // 显示模式\r\n  showMode: {\r\n    type: String,\r\n    default: 'direct'\r\n  },\r\n  // 项目数据\r\n  itemData: {\r\n    type: Object,\r\n    default: () => ({\r\n      id: '',\r\n      title: '推广',\r\n      description: '提升曝光度'\r\n    })\r\n  }\r\n});\r\n\r\n// 定义事件\r\nconst emit = defineEmits(['action-completed', 'action-cancelled']);\r\n\r\n// 控制弹窗显示\r\nconst showPromotionModal = ref(false);\r\n\r\n// 处理操作完成\r\nconst handleActionCompleted = (result) => {\r\n  console.log('推广操作完成:', result);\r\n  showPromotionModal.value = false;\r\n  emit('action-completed', result);\r\n};\r\n\r\n// 处理操作取消\r\nconst handleActionCancelled = (result) => {\r\n  console.log('推广操作取消:', result);\r\n  showPromotionModal.value = false;\r\n  emit('action-cancelled', result);\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.float-promotion-button {\r\n  position: fixed;\r\n  z-index: 999;\r\n  border-radius: 50%;\r\n  background: linear-gradient(135deg, #3846cd, #2c3aa0);\r\n  box-shadow: 0 4rpx 10rpx rgba(56, 70, 205, 0.3);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  transition: all 0.2s ease;\r\n  \r\n  &:active {\r\n    transform: scale(0.95);\r\n  }\r\n}\r\n\r\n.button-icon {\r\n  width: 50%;\r\n  height: 50%;\r\n}\r\n\r\n/* 推广弹窗样式 */\r\n.promotion-modal-mask {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background: rgba(0, 0, 0, 0.5);\r\n  z-index: 1000;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  padding: 40rpx;\r\n}\r\n\r\n.promotion-modal-content {\r\n  background: #fff;\r\n  border-radius: 20rpx;\r\n  width: 100%;\r\n  max-width: 600rpx;\r\n  max-height: 80vh;\r\n  overflow: hidden;\r\n}\r\n\r\n.modal-header {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n  padding: 30rpx;\r\n  border-bottom: 1px solid #f0f0f0;\r\n}\r\n\r\n.modal-title {\r\n  font-size: 32rpx;\r\n  font-weight: 600;\r\n  color: #333;\r\n}\r\n\r\n.modal-close {\r\n  width: 60rpx;\r\n  height: 60rpx;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  font-size: 40rpx;\r\n  color: #999;\r\n  border-radius: 50%;\r\n  background: #f5f5f5;\r\n}\r\n</style> ", "import Component from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/components/FloatPromotionButton.vue'\nwx.createComponent(Component)"], "names": ["ref", "uni"], "mappings": ";;;;;AAuCA,MAAM,6BAA6B,MAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4C9C,UAAM,OAAO;AAGb,UAAM,qBAAqBA,cAAAA,IAAI,KAAK;AAGpC,UAAM,wBAAwB,CAAC,WAAW;AACxCC,oFAAY,WAAW,MAAM;AAC7B,yBAAmB,QAAQ;AAC3B,WAAK,oBAAoB,MAAM;AAAA,IACjC;AAGA,UAAM,wBAAwB,CAAC,WAAW;AACxCA,oFAAY,WAAW,MAAM;AAC7B,yBAAmB,QAAQ;AAC3B,WAAK,oBAAoB,MAAM;AAAA,IACjC;;;;;;;;;;;;;;;;;;;;;;;;;;;ACnGA,GAAG,gBAAgB,SAAS;"}