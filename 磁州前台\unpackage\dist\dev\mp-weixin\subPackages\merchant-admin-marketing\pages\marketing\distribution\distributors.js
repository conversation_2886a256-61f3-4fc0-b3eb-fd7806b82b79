"use strict";
const common_vendor = require("../../../../../common/vendor.js");
const common_assets = require("../../../../../common/assets.js");
const utils_distributionService = require("../../../../../utils/distributionService.js");
const _sfc_main = {
  __name: "distributors",
  setup(__props) {
    const searchParams = common_vendor.reactive({
      keyword: "",
      status: "all",
      level: "all",
      sortBy: "created_at",
      sortOrder: "desc",
      page: 1,
      pageSize: 10
    });
    const statusOptions = [
      { value: "all", name: "全部状态" },
      { value: "active", name: "已启用" },
      { value: "disabled", name: "已禁用" },
      { value: "pending", name: "待审核" },
      { value: "rejected", name: "已拒绝" }
    ];
    const levelOptions = [
      { value: "all", name: "全部等级" },
      { value: "1", name: "普通分销员" },
      { value: "2", name: "高级分销员" },
      { value: "3", name: "金牌分销员" }
    ];
    const sortOptions = [
      { value: { field: "created_at", order: "desc" }, name: "注册时间降序" },
      { value: { field: "created_at", order: "asc" }, name: "注册时间升序" },
      { value: { field: "commission_total", order: "desc" }, name: "佣金总额降序" },
      { value: { field: "commission_total", order: "asc" }, name: "佣金总额升序" },
      { value: { field: "order_count", order: "desc" }, name: "订单数量降序" },
      { value: { field: "order_count", order: "asc" }, name: "订单数量升序" }
    ];
    const statusIndex = common_vendor.ref(0);
    const levelIndex = common_vendor.ref(0);
    const sortIndex = common_vendor.ref(0);
    const distributors = common_vendor.ref([]);
    const pagination = common_vendor.reactive({
      current: 1,
      pageSize: 10,
      total: 0,
      totalPages: 0
    });
    const loading = common_vendor.ref(false);
    const showLevelModal = common_vendor.ref(false);
    const selectedDistributor = common_vendor.ref({});
    const selectedLevel = common_vendor.ref("");
    common_vendor.onMounted(async () => {
      await getDistributionLevels();
      await getDistributorsList();
    });
    const getDistributionLevels = async () => {
      try {
        const result = await utils_distributionService.distributionService.getDistributionLevels();
        if (result && result.levels && result.levels.length > 0) {
          levelOptions.length = 1;
          result.levels.forEach((level) => {
            levelOptions.push({
              value: level.id.toString(),
              name: level.name
            });
          });
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at subPackages/merchant-admin-marketing/pages/marketing/distribution/distributors.vue:313", "获取分销等级列表失败", error);
      }
    };
    const getDistributorsList = async (loadMore = false) => {
      try {
        loading.value = true;
        const params = {
          ...searchParams,
          page: searchParams.page,
          pageSize: searchParams.pageSize
        };
        const result = await utils_distributionService.distributionService.getDistributorsList(params);
        if (result) {
          distributors.value = result.list || [];
          pagination.current = result.pagination.current;
          pagination.total = result.pagination.total;
          pagination.totalPages = result.pagination.totalPages;
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at subPackages/merchant-admin-marketing/pages/marketing/distribution/distributors.vue:339", "获取分销员列表失败", error);
        common_vendor.index.showToast({
          title: "获取分销员列表失败",
          icon: "none"
        });
      } finally {
        loading.value = false;
      }
    };
    const searchDistributors = () => {
      searchParams.page = 1;
      getDistributorsList();
    };
    const clearSearch = () => {
      searchParams.keyword = "";
      searchDistributors();
    };
    const onStatusChange = (e) => {
      const index = e.detail.value;
      statusIndex.value = index;
      searchParams.status = statusOptions[index].value;
      searchDistributors();
    };
    const onLevelChange = (e) => {
      const index = e.detail.value;
      levelIndex.value = index;
      searchParams.level = levelOptions[index].value;
      searchDistributors();
    };
    const onSortChange = (e) => {
      const index = e.detail.value;
      sortIndex.value = index;
      const sortOption = sortOptions[index].value;
      searchParams.sortBy = sortOption.field;
      searchParams.sortOrder = sortOption.order;
      searchDistributors();
    };
    const goBack = () => {
      common_vendor.index.navigateBack();
    };
    const showHelp = () => {
      common_vendor.index.showModal({
        title: "分销员管理帮助",
        content: "在此页面您可以查看和管理所有分销员，包括审核申请、设置等级、启用或禁用分销员等操作。",
        showCancel: false
      });
    };
    const formatAmount = (amount) => {
      return (amount || 0).toFixed(2);
    };
    const formatDate = (dateString) => {
      if (!dateString)
        return "";
      const date = new Date(dateString);
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, "0");
      const day = String(date.getDate()).padStart(2, "0");
      return `${year}-${month}-${day}`;
    };
    const getStatusClass = (status) => {
      switch (status) {
        case "active":
          return "status-active";
        case "disabled":
          return "status-disabled";
        case "pending":
          return "status-pending";
        case "rejected":
          return "status-rejected";
        default:
          return "";
      }
    };
    const getStatusText = (status) => {
      switch (status) {
        case "active":
          return "已启用";
        case "disabled":
          return "已禁用";
        case "pending":
          return "待审核";
        case "rejected":
          return "已拒绝";
        default:
          return "未知";
      }
    };
    const viewDetail = (item) => {
      common_vendor.index.navigateTo({
        url: `/subPackages/merchant-admin-marketing/pages/marketing/distribution/distributor-detail?id=${item.id}`
      });
    };
    const approveDistributor = (item) => {
      common_vendor.index.showModal({
        title: "审核通过",
        content: `确定通过 ${item.name} 的分销员申请吗？`,
        success: async (res) => {
          if (res.confirm) {
            try {
              common_vendor.index.showLoading({
                title: "处理中...",
                mask: true
              });
              const result = await utils_distributionService.distributionService.reviewDistributorApplication({
                id: item.id,
                status: "approved"
              });
              common_vendor.index.hideLoading();
              if (result.success) {
                common_vendor.index.showToast({
                  title: "审核通过成功",
                  icon: "success"
                });
                getDistributorsList();
              } else {
                common_vendor.index.showModal({
                  title: "审核失败",
                  content: result.message || "请稍后再试",
                  showCancel: false
                });
              }
            } catch (error) {
              common_vendor.index.hideLoading();
              common_vendor.index.__f__("error", "at subPackages/merchant-admin-marketing/pages/marketing/distribution/distributors.vue:494", "审核失败", error);
              common_vendor.index.showToast({
                title: "审核失败",
                icon: "none"
              });
            }
          }
        }
      });
    };
    const rejectDistributor = (item) => {
      common_vendor.index.showModal({
        title: "拒绝申请",
        content: `确定拒绝 ${item.name} 的分销员申请吗？`,
        success: async (res) => {
          if (res.confirm) {
            try {
              common_vendor.index.showLoading({
                title: "处理中...",
                mask: true
              });
              const result = await utils_distributionService.distributionService.reviewDistributorApplication({
                id: item.id,
                status: "rejected"
              });
              common_vendor.index.hideLoading();
              if (result.success) {
                common_vendor.index.showToast({
                  title: "已拒绝申请",
                  icon: "success"
                });
                getDistributorsList();
              } else {
                common_vendor.index.showModal({
                  title: "操作失败",
                  content: result.message || "请稍后再试",
                  showCancel: false
                });
              }
            } catch (error) {
              common_vendor.index.hideLoading();
              common_vendor.index.__f__("error", "at subPackages/merchant-admin-marketing/pages/marketing/distribution/distributors.vue:542", "操作失败", error);
              common_vendor.index.showToast({
                title: "操作失败",
                icon: "none"
              });
            }
          }
        }
      });
    };
    const disableDistributor = (item) => {
      common_vendor.index.showModal({
        title: "禁用分销员",
        content: `确定禁用 ${item.name} 的分销员资格吗？禁用后该用户将无法进行分销活动。`,
        success: async (res) => {
          if (res.confirm) {
            try {
              common_vendor.index.showLoading({
                title: "处理中...",
                mask: true
              });
              const result = await utils_distributionService.distributionService.toggleDistributorStatus({
                id: item.id,
                status: "disabled"
              });
              common_vendor.index.hideLoading();
              if (result.success) {
                common_vendor.index.showToast({
                  title: "禁用成功",
                  icon: "success"
                });
                getDistributorsList();
              } else {
                common_vendor.index.showModal({
                  title: "操作失败",
                  content: result.message || "请稍后再试",
                  showCancel: false
                });
              }
            } catch (error) {
              common_vendor.index.hideLoading();
              common_vendor.index.__f__("error", "at subPackages/merchant-admin-marketing/pages/marketing/distribution/distributors.vue:590", "操作失败", error);
              common_vendor.index.showToast({
                title: "操作失败",
                icon: "none"
              });
            }
          }
        }
      });
    };
    const enableDistributor = (item) => {
      common_vendor.index.showModal({
        title: "启用分销员",
        content: `确定启用 ${item.name} 的分销员资格吗？`,
        success: async (res) => {
          if (res.confirm) {
            try {
              common_vendor.index.showLoading({
                title: "处理中...",
                mask: true
              });
              const result = await utils_distributionService.distributionService.toggleDistributorStatus({
                id: item.id,
                status: "active"
              });
              common_vendor.index.hideLoading();
              if (result.success) {
                common_vendor.index.showToast({
                  title: "启用成功",
                  icon: "success"
                });
                getDistributorsList();
              } else {
                common_vendor.index.showModal({
                  title: "操作失败",
                  content: result.message || "请稍后再试",
                  showCancel: false
                });
              }
            } catch (error) {
              common_vendor.index.hideLoading();
              common_vendor.index.__f__("error", "at subPackages/merchant-admin-marketing/pages/marketing/distribution/distributors.vue:638", "操作失败", error);
              common_vendor.index.showToast({
                title: "操作失败",
                icon: "none"
              });
            }
          }
        }
      });
    };
    const setLevel = (item) => {
      selectedDistributor.value = item;
      selectedLevel.value = item.levelId ? item.levelId.toString() : "1";
      showLevelModal.value = true;
    };
    const closeLevelModal = () => {
      showLevelModal.value = false;
    };
    const confirmSetLevel = async () => {
      try {
        common_vendor.index.showLoading({
          title: "设置中...",
          mask: true
        });
        const result = await utils_distributionService.distributionService.setDistributorLevel({
          id: selectedDistributor.value.id,
          levelId: selectedLevel.value
        });
        common_vendor.index.hideLoading();
        if (result.success) {
          common_vendor.index.showToast({
            title: "设置成功",
            icon: "success"
          });
          closeLevelModal();
          getDistributorsList();
        } else {
          common_vendor.index.showModal({
            title: "设置失败",
            content: result.message || "请稍后再试",
            showCancel: false
          });
        }
      } catch (error) {
        common_vendor.index.hideLoading();
        common_vendor.index.__f__("error", "at subPackages/merchant-admin-marketing/pages/marketing/distribution/distributors.vue:695", "设置等级失败", error);
        common_vendor.index.showToast({
          title: "设置失败",
          icon: "none"
        });
      }
    };
    const prevPage = () => {
      if (pagination.current > 1) {
        searchParams.page = pagination.current - 1;
        getDistributorsList();
      }
    };
    const nextPage = () => {
      if (pagination.current < pagination.totalPages) {
        searchParams.page = pagination.current + 1;
        getDistributorsList();
      }
    };
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_vendor.o(goBack),
        b: common_vendor.o(showHelp),
        c: common_vendor.o(searchDistributors),
        d: searchParams.keyword,
        e: common_vendor.o(($event) => searchParams.keyword = $event.detail.value),
        f: searchParams.keyword
      }, searchParams.keyword ? {
        g: common_vendor.o(clearSearch)
      } : {}, {
        h: common_vendor.o(searchDistributors),
        i: common_vendor.t(statusOptions[statusIndex.value].name),
        j: statusOptions,
        k: statusIndex.value,
        l: common_vendor.o(onStatusChange),
        m: common_vendor.t(levelOptions[levelIndex.value].name),
        n: levelOptions,
        o: levelIndex.value,
        p: common_vendor.o(onLevelChange),
        q: common_vendor.t(sortOptions[sortIndex.value].name),
        r: sortOptions,
        s: sortIndex.value,
        t: common_vendor.o(onSortChange),
        v: distributors.value.length > 0
      }, distributors.value.length > 0 ? {
        w: common_vendor.f(distributors.value, (item, index, i0) => {
          return common_vendor.e({
            a: item.avatar || "/static/images/default-avatar.png",
            b: common_vendor.t(item.name),
            c: common_vendor.t(item.levelName),
            d: item.levelColor || "#67C23A",
            e: common_vendor.t(item.phone),
            f: common_vendor.t(getStatusText(item.status)),
            g: common_vendor.n(getStatusClass(item.status)),
            h: common_vendor.t(item.orderCount || 0),
            i: common_vendor.t(formatAmount(item.commissionTotal)),
            j: common_vendor.t(item.teamCount || 0),
            k: common_vendor.t(formatDate(item.createdAt)),
            l: common_vendor.o(($event) => viewDetail(item), index),
            m: item.status === "pending"
          }, item.status === "pending" ? {
            n: common_vendor.o(($event) => approveDistributor(item), index),
            o: common_vendor.o(($event) => rejectDistributor(item), index)
          } : item.status === "active" ? {
            q: common_vendor.o(($event) => disableDistributor(item), index),
            r: common_vendor.o(($event) => setLevel(item), index)
          } : item.status === "disabled" ? {
            t: common_vendor.o(($event) => enableDistributor(item), index)
          } : {}, {
            p: item.status === "active",
            s: item.status === "disabled",
            v: index
          });
        })
      } : !loading.value ? {
        y: common_assets._imports_0$18
      } : {}, {
        x: !loading.value,
        z: loading.value
      }, loading.value ? {} : {}, {
        A: distributors.value.length > 0 && !loading.value
      }, distributors.value.length > 0 && !loading.value ? {
        B: common_vendor.t(pagination.total),
        C: common_vendor.t(pagination.current),
        D: common_vendor.t(pagination.totalPages),
        E: pagination.current <= 1 ? 1 : "",
        F: common_vendor.o(prevPage),
        G: pagination.current >= pagination.totalPages ? 1 : "",
        H: common_vendor.o(nextPage)
      } : {}, {
        I: showLevelModal.value
      }, showLevelModal.value ? {
        J: common_vendor.o(closeLevelModal),
        K: common_vendor.o(closeLevelModal),
        L: selectedDistributor.value.avatar || "/static/images/default-avatar.png",
        M: common_vendor.t(selectedDistributor.value.name),
        N: common_vendor.f(levelOptions.slice(1), (level, idx, i0) => {
          return common_vendor.e({
            a: selectedLevel.value === level.value
          }, selectedLevel.value === level.value ? {} : {}, {
            b: common_vendor.t(level.name),
            c: idx,
            d: selectedLevel.value === level.value ? 1 : "",
            e: common_vendor.o(($event) => selectedLevel.value = level.value, idx)
          });
        }),
        O: common_vendor.o(closeLevelModal),
        P: common_vendor.o(confirmSetLevel)
      } : {});
    };
  }
};
wx.createPage(_sfc_main);
//# sourceMappingURL=../../../../../../.sourcemap/mp-weixin/subPackages/merchant-admin-marketing/pages/marketing/distribution/distributors.js.map
