/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 移除阿里妈妈字体引用，改用系统默认字体 */
/* 移除原有阿里妈妈字体定义
$uni-font-family: 'AlimamaShuHeiTi';
$uni-font-family-text: 'AlimamaShuHeiTi', -apple-system, BlinkMacSystemFont, 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;
*/
body.data-v-2734631e, html.data-v-2734631e, #app.data-v-2734631e, .index-container.data-v-2734631e {
  font-family: "AlimamaShuHeiTi", -apple-system, BlinkMacSystemFont, "Helvetica Neue", "PingFang SC", "Microsoft YaHei", sans-serif;
}
.discount-detail-container.data-v-2734631e {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #F5F7FA;
  padding-bottom: 120rpx;
  /* 为底部按钮留出空间 */
}

/* 自定义导航栏 */
.custom-navbar.data-v-2734631e {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: calc(var(--status-bar-height, 25px) + 62px);
  width: 100%;
  z-index: 100;
}
.custom-navbar .navbar-bg.data-v-2734631e {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #5E5CE6 0%, #5856D6 100%);
}
.custom-navbar .navbar-content.data-v-2734631e {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 100%;
  padding-top: var(--status-bar-height, 25px);
  padding-left: 30rpx;
  padding-right: 30rpx;
  box-sizing: border-box;
}
.custom-navbar .back-btn.data-v-2734631e {
  width: 40rpx;
  height: 40rpx;
  margin-right: 10rpx;
}
.custom-navbar .back-icon.data-v-2734631e {
  width: 100%;
  height: 100%;
}
.custom-navbar .navbar-title.data-v-2734631e {
  font-size: 18px;
  font-weight: 600;
  color: #FFFFFF;
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
}

/* 加载状态 */
.loading-container.data-v-2734631e {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
}
.loading-container .loading-spinner.data-v-2734631e {
  width: 80rpx;
  height: 80rpx;
  border: 6rpx solid #f3f3f3;
  border-top: 6rpx solid #5856D6;
  border-radius: 50%;
  animation: spin-2734631e 1s linear infinite;
  margin-bottom: 20rpx;
}
.loading-container .loading-text.data-v-2734631e {
  font-size: 28rpx;
  color: #8E8E93;
}
@keyframes spin-2734631e {
0% {
    transform: rotate(0deg);
}
100% {
    transform: rotate(360deg);
}
}
/* 商家信息卡片 */
.merchant-card.data-v-2734631e {
  background-color: #FFFFFF;
  border-radius: 35rpx;
  padding: 30rpx;
  margin: 30rpx;
  box-shadow: 0 15rpx 35rpx rgba(0, 0, 0, 0.08);
}
.merchant-card .merchant-header.data-v-2734631e {
  display: flex;
  align-items: center;
}
.merchant-card .merchant-header .merchant-logo.data-v-2734631e {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  margin-right: 20rpx;
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.1);
}
.merchant-card .merchant-header .merchant-info.data-v-2734631e {
  flex: 1;
}
.merchant-card .merchant-header .merchant-name.data-v-2734631e {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 10rpx;
}
.merchant-card .merchant-header .merchant-rating.data-v-2734631e {
  display: flex;
  align-items: center;
}
.merchant-card .merchant-header .rating-stars.data-v-2734631e {
  display: flex;
  margin-right: 10rpx;
}
.merchant-card .merchant-header .star-icon.data-v-2734631e {
  width: 24rpx;
  height: 24rpx;
  margin-right: 4rpx;
}
.merchant-card .merchant-header .rating-score.data-v-2734631e {
  font-size: 24rpx;
  color: #FF9500;
  font-weight: 600;
}
.merchant-card .merchant-header .merchant-action.data-v-2734631e {
  display: flex;
  align-items: center;
  font-size: 28rpx;
  color: #5856D6;
  font-weight: 500;
}
.merchant-card .merchant-header .merchant-action .arrow-icon.data-v-2734631e {
  font-size: 32rpx;
  margin-left: 5rpx;
}

/* 满减规则卡片 */
.rules-card.data-v-2734631e {
  background-color: #FFFFFF;
  border-radius: 35rpx;
  padding: 30rpx;
  margin: 0 30rpx 30rpx 30rpx;
  box-shadow: 0 15rpx 35rpx rgba(0, 0, 0, 0.08);
}
.rules-card .card-header.data-v-2734631e {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
  border-bottom: 1rpx solid #EFEFEF;
  padding-bottom: 20rpx;
}
.rules-card .card-title.data-v-2734631e {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
}
.rules-card .activity-time.data-v-2734631e {
  font-size: 24rpx;
  color: #8E8E93;
}
.rules-card .rules-content.data-v-2734631e {
  display: flex;
  flex-direction: column;
  margin-bottom: 20rpx;
}
.rules-card .rule-item.data-v-2734631e {
  position: relative;
  display: flex;
  align-items: center;
  padding: 20rpx;
  margin-bottom: 15rpx;
  background-color: #F5F7FA;
  border-radius: 16rpx;
}
.rules-card .rule-item.highlight.data-v-2734631e {
  background: linear-gradient(135deg, #F0EFFF 0%, #E6E6FF 100%);
  border: 1rpx solid #D1D1FF;
}
.rules-card .rule-item.highlight .rule-text.data-v-2734631e {
  color: #5856D6;
  font-weight: 600;
}
.rules-card .rule-item .rule-tag.data-v-2734631e {
  position: absolute;
  top: -10rpx;
  right: 20rpx;
  background-color: #5856D6;
  color: #FFFFFF;
  font-size: 20rpx;
  padding: 4rpx 12rpx;
  border-radius: 10rpx;
}
.rules-card .rule-item .rule-text.data-v-2734631e {
  font-size: 28rpx;
  color: #666666;
}
.rules-card .rules-notice.data-v-2734631e {
  font-size: 24rpx;
  color: #8E8E93;
  padding-top: 10rpx;
}

/* 分销入口 */
.distribution-section.data-v-2734631e {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 0 30rpx 30rpx 30rpx;
  padding: 20rpx;
  background: linear-gradient(135deg, #F0EFFF 0%, #E6E6FF 100%);
  border-radius: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(88, 86, 214, 0.1);
}
.distribution-section .distribution-left.data-v-2734631e {
  display: flex;
  align-items: center;
}
.distribution-section .distribution-icon.data-v-2734631e {
  width: 60rpx;
  height: 60rpx;
  margin-right: 15rpx;
}
.distribution-section .distribution-icon .icon-image.data-v-2734631e {
  width: 100%;
  height: 100%;
}
.distribution-section .distribution-info.data-v-2734631e {
  display: flex;
  flex-direction: column;
}
.distribution-section .distribution-title.data-v-2734631e {
  font-size: 28rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 5rpx;
}
.distribution-section .distribution-desc.data-v-2734631e {
  font-size: 24rpx;
  color: #5856D6;
  font-weight: 600;
}
.distribution-section .distribution-right.data-v-2734631e {
  display: flex;
  align-items: center;
}
.distribution-section .distribution-btn.data-v-2734631e {
  font-size: 26rpx;
  color: #5856D6;
  font-weight: 600;
}
.distribution-section .arrow-icon.data-v-2734631e {
  font-size: 32rpx;
  color: #5856D6;
  margin-left: 5rpx;
}

/* 活动说明卡片 */
.description-card.data-v-2734631e {
  background-color: #FFFFFF;
  border-radius: 35rpx;
  padding: 30rpx;
  margin: 0 30rpx 30rpx 30rpx;
  box-shadow: 0 15rpx 35rpx rgba(0, 0, 0, 0.08);
}
.description-card .card-header.data-v-2734631e {
  margin-bottom: 20rpx;
  border-bottom: 1rpx solid #EFEFEF;
  padding-bottom: 20rpx;
}
.description-card .card-title.data-v-2734631e {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
}
.description-card .description-content.data-v-2734631e {
  display: flex;
  flex-direction: column;
}
.description-card .description-item.data-v-2734631e {
  display: flex;
  align-items: flex-start;
  margin-bottom: 15rpx;
}
.description-card .description-item .item-dot.data-v-2734631e {
  width: 12rpx;
  height: 12rpx;
  border-radius: 50%;
  background-color: #5856D6;
  margin-top: 12rpx;
  margin-right: 15rpx;
  flex-shrink: 0;
}
.description-card .description-item .item-text.data-v-2734631e {
  font-size: 28rpx;
  color: #666666;
  line-height: 1.6;
}

/* 推荐商品卡片 */
.products-card.data-v-2734631e {
  background-color: #FFFFFF;
  border-radius: 35rpx;
  padding: 30rpx;
  margin: 0 30rpx 30rpx 30rpx;
  box-shadow: 0 15rpx 35rpx rgba(0, 0, 0, 0.08);
}
.products-card .card-header.data-v-2734631e {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
  border-bottom: 1rpx solid #EFEFEF;
  padding-bottom: 20rpx;
}
.products-card .card-header .card-more.data-v-2734631e {
  display: flex;
  align-items: center;
  font-size: 26rpx;
  color: #8E8E93;
}
.products-card .card-header .card-more .arrow-icon.data-v-2734631e {
  font-size: 30rpx;
  margin-left: 5rpx;
}
.products-card .products-scroll.data-v-2734631e {
  width: 100%;
}
.products-card .products-list.data-v-2734631e {
  display: flex;
  padding: 10rpx 0;
}
.products-card .product-item.data-v-2734631e {
  width: 200rpx;
  margin-right: 20rpx;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 8rpx 16rpx rgba(0, 0, 0, 0.05);
  background-color: #FFFFFF;
  transition: transform 0.3s ease;
}
.products-card .product-item.data-v-2734631e:active {
  transform: scale(0.98);
}
.products-card .product-item .product-image.data-v-2734631e {
  width: 200rpx;
  height: 200rpx;
  object-fit: cover;
}
.products-card .product-item .product-info.data-v-2734631e {
  padding: 15rpx;
}
.products-card .product-item .product-name.data-v-2734631e {
  font-size: 24rpx;
  color: #333333;
  line-height: 1.4;
  height: 68rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}
.products-card .product-item .product-price.data-v-2734631e {
  display: flex;
  align-items: baseline;
  margin-top: 10rpx;
}
.products-card .product-item .product-price .price-symbol.data-v-2734631e {
  font-size: 22rpx;
  color: #5856D6;
}
.products-card .product-item .product-price .price-value.data-v-2734631e {
  font-size: 28rpx;
  font-weight: 600;
  color: #5856D6;
}

/* 底部按钮 */
.bottom-bar.data-v-2734631e {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  height: 100rpx;
  background-color: #FFFFFF;
  display: flex;
  align-items: center;
  padding: 0 30rpx;
  box-shadow: 0 -4rpx 12rpx rgba(0, 0, 0, 0.05);
  z-index: 99;
}
.bottom-bar .action-btn.data-v-2734631e {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-right: 40rpx;
}
.bottom-bar .action-btn .action-icon.data-v-2734631e {
  width: 40rpx;
  height: 40rpx;
  margin-bottom: 5rpx;
}
.bottom-bar .action-btn text.data-v-2734631e {
  font-size: 22rpx;
  color: #8E8E93;
}
.bottom-bar .use-btn.data-v-2734631e {
  flex: 1;
  height: 80rpx;
  background: linear-gradient(135deg, #5E5CE6 0%, #5856D6 100%);
  border-radius: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 30rpx;
  font-weight: 600;
  color: #FFFFFF;
  box-shadow: 0 8rpx 16rpx rgba(88, 86, 214, 0.2);
  transition: transform 0.3s ease;
}
.bottom-bar .use-btn.data-v-2734631e:active {
  transform: translateY(5rpx);
  box-shadow: 0 4rpx 8rpx rgba(88, 86, 214, 0.15);
}