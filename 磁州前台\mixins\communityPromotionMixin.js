/**
 * 社区信息推广混入
 * 为社区信息详情页提供推广能力
 */
import basePromotionMixin from './basePromotionMixin';

export default {
  mixins: [basePromotionMixin],

  data() {
    return {
      // 设置页面类型为社区信息
      pageType: 'community'
    };
  },

  methods: {
    /**
     * 重写：判断当前用户是否是内容所有者
     */
    isContentOwner() {
      // 获取当前用户ID
      const currentUserId = this.$store?.state?.user?.userId || '';
      // 获取信息发布者ID
      const publisherId = this.infoDetail?.userId || this.info?.userId || '';

      // 判断当前用户是否是信息发布者
      return currentUserId && publisherId && currentUserId === publisherId;
    },

    /**
     * 重写：判断当前内容是否支持佣金
     */
    isCommissionContent() {
      // 社区信息通常不支持佣金，除非特殊设置
      return false;
    },

    /**
     * 重写：生成推广数据
     */
    generatePromotionData() {
      // 获取信息数据
      const info = this.infoDetail || this.info || {};
      
      // 构建推广数据
      this.promotionData = {
        id: info.id || '',
        title: info.title || '',
        image: info.image || info.images?.[0] || '',
        category: info.categoryName || info.category || '',
        publishTime: info.publishTime || info.createTime || '',
        location: info.location || info.address || '',
        contact: info.contact || info.phone || '',
        // 如果有更多信息特定字段，可以在这里添加
      };
    },

    /**
     * 显示信息推广浮层
     */
    showCommunityPromotion() {
      // 如果没有推广权限，显示提示
      if (!this.hasPromotionPermission) {
        uni.showToast({
          title: '暂无推广权限',
          icon: 'none'
        });
        return;
      }

      // 打开推广工具
      this.openPromotionTools();
    }
  }
}; 