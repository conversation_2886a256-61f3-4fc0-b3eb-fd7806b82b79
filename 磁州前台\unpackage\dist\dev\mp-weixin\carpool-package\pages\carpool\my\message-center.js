"use strict";
const common_vendor = require("../../../../common/vendor.js");
const common_assets = require("../../../../common/assets.js");
const _sfc_main = {
  __name: "message-center",
  setup(__props, { expose: __expose }) {
    const statusBarHeight = common_vendor.ref(20);
    const tabs = common_vendor.ref(["系统消息", "拼车消息"]);
    const currentTab = common_vendor.ref(0);
    const messageList = common_vendor.ref([]);
    const page = common_vendor.ref(1);
    common_vendor.ref(10);
    const hasMore = common_vendor.ref(true);
    const isLoading = common_vendor.ref(false);
    const isRefreshing = common_vendor.ref(false);
    const showMessageDetail = common_vendor.ref(false);
    const currentMessage = common_vendor.ref({});
    const systemMessages = common_vendor.computed(() => {
      return messageList.value.filter((msg) => msg.type === "system");
    });
    const carpoolMessages = common_vendor.computed(() => {
      return messageList.value.filter((msg) => msg.type === "carpool");
    });
    const loadData = () => {
      if (isLoading.value)
        return;
      isLoading.value = true;
      setTimeout(() => {
        const mockData = [
          {
            id: "5001",
            type: "system",
            title: "系统通知",
            content: "您的账号已完成实名认证，现在可以发布拼车信息了。",
            fullContent: "尊敬的用户：\n\n恭喜您！您的账号已通过实名认证审核，现在您可以使用平台的全部功能，包括发布拼车信息、查看联系方式等。\n\n为了保障平台用户的安全，我们对所有用户进行严格的身份审核。感谢您的配合与支持！\n\n如有任何疑问，请联系客服。",
            time: "2023-10-15 16:30",
            isRead: true
          },
          {
            id: "5002",
            type: "system",
            title: "活动通知",
            content: "新用户专享优惠：首次发布拼车信息可获得置顶券一张。",
            time: "2023-10-14 10:15",
            isRead: false
          },
          {
            id: "5003",
            type: "carpool",
            title: "拼车申请",
            content: '用户"王先生"申请加入您发布的拼车行程。',
            time: "2023-10-13 14:20",
            isRead: false,
            carpoolInfo: {
              id: "1001",
              startPoint: "磁县公交站",
              endPoint: "邯郸东站",
              departureTime: "2023-10-16 08:30",
              seatCount: 3,
              price: 15
            }
          },
          {
            id: "5004",
            type: "carpool",
            title: "行程提醒",
            content: "您有一个拼车行程即将开始，请提前做好准备。",
            time: "2023-10-12 18:30",
            isRead: true,
            carpoolInfo: {
              id: "1002",
              startPoint: "磁县老城区",
              endPoint: "邯郸高新区",
              departureTime: "2023-10-13 07:30",
              seatCount: 4,
              price: 10
            }
          }
        ];
        if (page.value === 1) {
          messageList.value = mockData;
        } else {
          messageList.value = [...messageList.value, ...mockData];
        }
        if (page.value >= 2) {
          hasMore.value = false;
        }
        isLoading.value = false;
        isRefreshing.value = false;
      }, 1e3);
    };
    const loadMore = () => {
      if (!hasMore.value || isLoading.value)
        return;
      page.value++;
      loadData();
    };
    const onRefresh = () => {
      isRefreshing.value = true;
      page.value = 1;
      hasMore.value = true;
      loadData();
    };
    const goBack = () => {
      common_vendor.index.navigateBack();
    };
    const switchTab = (index) => {
      currentTab.value = index;
    };
    const readMessage = (message) => {
      if (!message.isRead) {
        messageList.value = messageList.value.map((item) => {
          if (item.id === message.id) {
            return { ...item, isRead: true };
          }
          return item;
        });
      }
      currentMessage.value = message;
      showMessageDetail.value = true;
    };
    const closeMessageDetail = () => {
      showMessageDetail.value = false;
    };
    const deleteCurrentMessage = () => {
      common_vendor.index.showModal({
        title: "提示",
        content: "确定要删除此条消息吗？",
        success: (res) => {
          if (res.confirm) {
            messageList.value = messageList.value.filter((item) => item.id !== currentMessage.value.id);
            closeMessageDetail();
            common_vendor.index.showToast({
              title: "删除成功",
              icon: "success"
            });
          }
        }
      });
    };
    const viewCarpoolDetail = () => {
      if (!currentMessage.value.carpoolInfo)
        return;
      common_vendor.index.navigateTo({
        url: `/carpool-package/pages/carpool/detail/index?id=${currentMessage.value.carpoolInfo.id}`
      });
      closeMessageDetail();
    };
    const showClearConfirm = () => {
      common_vendor.index.showModal({
        title: "提示",
        content: `确定要清空${currentTab.value === 0 ? "系统" : "拼车"}消息吗？此操作不可恢复。`,
        success: (res) => {
          if (res.confirm) {
            clearMessages();
          }
        }
      });
    };
    const clearMessages = () => {
      const messageType = currentTab.value === 0 ? "system" : "carpool";
      messageList.value = messageList.value.filter((item) => item.type !== messageType);
      common_vendor.index.showToast({
        title: "已清空",
        icon: "success"
      });
    };
    common_vendor.onMounted(() => {
      var _a;
      const sysInfo = common_vendor.index.getSystemInfoSync();
      statusBarHeight.value = sysInfo.statusBarHeight || 20;
      const pages = getCurrentPages();
      const currentPage = pages[pages.length - 1];
      const options = ((_a = currentPage.$page) == null ? void 0 : _a.options) || {};
      if (options.tab) {
        currentTab.value = parseInt(options.tab) || 0;
      }
      loadData();
    });
    __expose({
      loadData,
      switchTab
    });
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_assets._imports_0$7,
        b: common_vendor.o(goBack),
        c: statusBarHeight.value + "px",
        d: common_vendor.f(tabs.value, (tab, index, i0) => {
          return common_vendor.e({
            a: common_vendor.t(tab),
            b: currentTab.value === index
          }, currentTab.value === index ? {} : {}, {
            c: index,
            d: currentTab.value === index ? 1 : "",
            e: common_vendor.o(($event) => switchTab(index), index)
          });
        }),
        e: currentTab.value === 0
      }, currentTab.value === 0 ? common_vendor.e({
        f: common_vendor.f(systemMessages.value, (item, index, i0) => {
          return common_vendor.e({
            a: !item.isRead
          }, !item.isRead ? {} : {}, {
            b: common_vendor.t(item.title),
            c: common_vendor.t(item.time),
            d: common_vendor.t(item.content),
            e: item.id,
            f: common_vendor.o(($event) => readMessage(item), item.id)
          });
        }),
        g: common_assets._imports_1$34,
        h: systemMessages.value.length === 0 && !isLoading.value
      }, systemMessages.value.length === 0 && !isLoading.value ? {
        i: common_assets._imports_2$30
      } : {}) : {}, {
        j: currentTab.value === 1
      }, currentTab.value === 1 ? common_vendor.e({
        k: common_vendor.f(carpoolMessages.value, (item, index, i0) => {
          return common_vendor.e({
            a: !item.isRead
          }, !item.isRead ? {} : {}, {
            b: common_vendor.t(item.title),
            c: common_vendor.t(item.time),
            d: common_vendor.t(item.content),
            e: item.carpoolInfo
          }, item.carpoolInfo ? {
            f: common_vendor.t(item.carpoolInfo.startPoint),
            g: common_vendor.t(item.carpoolInfo.endPoint),
            h: common_vendor.t(item.carpoolInfo.departureTime)
          } : {}, {
            i: item.id,
            j: common_vendor.o(($event) => readMessage(item), item.id)
          });
        }),
        l: common_assets._imports_3$28,
        m: carpoolMessages.value.length === 0 && !isLoading.value
      }, carpoolMessages.value.length === 0 && !isLoading.value ? {
        n: common_assets._imports_2$30
      } : {}) : {}, {
        o: isLoading.value && !isRefreshing.value
      }, isLoading.value && !isRefreshing.value ? {} : {}, {
        p: messageList.value.length > 0 && !hasMore.value
      }, messageList.value.length > 0 && !hasMore.value ? {} : {}, {
        q: common_vendor.o(loadMore),
        r: common_vendor.o(onRefresh),
        s: isRefreshing.value,
        t: messageList.value.length > 0
      }, messageList.value.length > 0 ? {
        v: common_vendor.o(showClearConfirm)
      } : {}, {
        w: showMessageDetail.value
      }, showMessageDetail.value ? {
        x: common_vendor.o(closeMessageDetail)
      } : {}, {
        y: showMessageDetail.value
      }, showMessageDetail.value ? common_vendor.e({
        z: common_vendor.t(currentMessage.value.title),
        A: common_vendor.o(closeMessageDetail),
        B: common_vendor.t(currentMessage.value.time),
        C: common_vendor.t(currentMessage.value.fullContent || currentMessage.value.content),
        D: currentMessage.value.carpoolInfo
      }, currentMessage.value.carpoolInfo ? common_vendor.e({
        E: common_vendor.t(currentMessage.value.carpoolInfo.startPoint),
        F: common_vendor.t(currentMessage.value.carpoolInfo.endPoint),
        G: common_assets._imports_1$33,
        H: common_vendor.t(currentMessage.value.carpoolInfo.departureTime),
        I: common_assets._imports_2$29,
        J: common_vendor.t(currentMessage.value.carpoolInfo.seatCount),
        K: currentMessage.value.carpoolInfo.price
      }, currentMessage.value.carpoolInfo.price ? {
        L: common_assets._imports_3$27,
        M: common_vendor.t(currentMessage.value.carpoolInfo.price)
      } : {}) : {}, {
        N: common_vendor.o(deleteCurrentMessage),
        O: currentMessage.value.carpoolInfo
      }, currentMessage.value.carpoolInfo ? {
        P: common_vendor.o(viewCarpoolDetail)
      } : {}) : {});
    };
  }
};
wx.createPage(_sfc_main);
//# sourceMappingURL=../../../../../.sourcemap/mp-weixin/carpool-package/pages/carpool/my/message-center.js.map
