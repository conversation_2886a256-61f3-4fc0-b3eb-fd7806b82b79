/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 移除阿里妈妈字体引用，改用系统默认字体 */
/* 移除原有阿里妈妈字体定义
$uni-font-family: 'AlimamaShuHeiTi';
$uni-font-family-text: 'AlimamaShuHeiTi', -apple-system, BlinkMacSystemFont, 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;
*/
body.data-v-a3d9a803, html.data-v-a3d9a803, #app.data-v-a3d9a803, .index-container.data-v-a3d9a803 {
  font-family: "AlimamaShuHeiTi", -apple-system, BlinkMacSystemFont, "Helvetica Neue", "PingFang SC", "Microsoft YaHei", sans-serif;
}

/* 四宫格特色功能区样式 */
.feature-grid.data-v-a3d9a803 {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  padding: 30rpx 24rpx;
  background-color: #FFFFFF;
  border-radius: 32rpx;
  margin: 0 25rpx 34rpx;
  box-shadow: 0 10rpx 25rpx rgba(0, 0, 0, 0.08);
  position: relative;
  overflow: hidden;
  z-index: 1;
}
.feature-item.data-v-a3d9a803 {
  width: 48%;
  height: 164rpx;
  border-radius: 26rpx;
  display: flex;
  align-items: center;
  padding: 20rpx;
  box-sizing: border-box;
  margin-bottom: 24rpx;
  box-shadow: 0 6rpx 12rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  border: none;
}
.feature-item.data-v-a3d9a803:active {
  transform: translateY(2rpx) scale(0.98);
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.15);
}
.feature-icon.data-v-a3d9a803 {
  width: 80rpx;
  height: 80rpx;
  z-index: 2;
  position: relative;
  filter: drop-shadow(0 2rpx 3rpx rgba(0, 0, 0, 0.2));
}
.feature-content.data-v-a3d9a803 {
  display: flex;
  flex-direction: column;
  justify-content: center;
  margin-left: 20rpx;
  z-index: 1;
}
.feature-title.data-v-a3d9a803 {
  font-size: 32rpx;
  font-weight: 800;
  color: #333333;
  margin-bottom: 6rpx;
  text-shadow: none;
}
.feature-desc.data-v-a3d9a803 {
  font-size: 24rpx;
  font-weight: 600;
  color: #666666;
  text-shadow: none;
}

/* 推广赚佣金动态效果 */
.promo-text.data-v-a3d9a803 {
  position: relative;
  animation: promo-pulse-a3d9a803 1.5s infinite ease-in-out;
  text-shadow: 0 0 5rpx rgba(170, 123, 160, 0.3);
}
@keyframes promo-pulse-a3d9a803 {
0% {
    transform: scale(1);
}
50% {
    transform: scale(1.1);
}
100% {
    transform: scale(1);
}
}
.card-section.data-v-a3d9a803 {
  margin-bottom: 20rpx;
}
.fade-in.data-v-a3d9a803 {
  animation: fadeIn-a3d9a803 0.5s ease-in-out;
}
@keyframes fadeIn-a3d9a803 {
from {
    opacity: 0;
    transform: translateY(20rpx);
}
to {
    opacity: 1;
    transform: translateY(0);
}
}