/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 移除阿里妈妈字体引用，改用系统默认字体 */
/* 移除原有阿里妈妈字体定义
$uni-font-family: 'AlimamaShuHeiTi';
$uni-font-family-text: 'AlimamaShuHeiTi', -apple-system, BlinkMacSystemFont, 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;
*/
body.data-v-fda7de77, html.data-v-fda7de77, #app.data-v-fda7de77, .index-container.data-v-fda7de77 {
  font-family: "AlimamaShuHeiTi", -apple-system, BlinkMacSystemFont, "Helvetica Neue", "PingFang SC", "Microsoft YaHei", sans-serif;
}
.page-container.data-v-fda7de77 {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f5f5f5;
}

/* 导航栏样式 */
.navbar.data-v-fda7de77 {
  display: flex;
  align-items: center;
  height: 44px;
  background-color: #fff;
  padding: 0 15px;
  position: relative;
}
.navbar-back.data-v-fda7de77 {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.back-icon.data-v-fda7de77 {
  width: 12px;
  height: 12px;
  border-top: 2px solid #333;
  border-left: 2px solid #333;
  transform: rotate(-45deg);
}
.navbar-title.data-v-fda7de77 {
  flex: 1;
  text-align: center;
  font-size: 17px;
  font-weight: 600;
  color: #333;
}
.navbar-right.data-v-fda7de77 {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.settings-icon.data-v-fda7de77 {
  color: #333;
}

/* 公共样式 */
.section-header.data-v-fda7de77 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}
.section-title.data-v-fda7de77 {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

/* 数据概览样式 */
.overview-section.data-v-fda7de77 {
  padding: 15px;
  background-color: #fff;
  margin-bottom: 10px;
}
.date-picker.data-v-fda7de77 {
  display: flex;
  align-items: center;
  padding: 5px 10px;
  background-color: #f5f5f5;
  border-radius: 15px;
}
.date-text.data-v-fda7de77 {
  font-size: 12px;
  color: #666;
  margin-right: 5px;
}
.date-icon.data-v-fda7de77 {
  color: #666;
}
.stats-cards.data-v-fda7de77 {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -5px;
}
.stats-card.data-v-fda7de77 {
  width: calc(50% - 10px);
  margin: 5px;
  background-color: #f9f9f9;
  border-radius: 8px;
  padding: 15px;
  text-align: center;
}
.stats-value.data-v-fda7de77 {
  font-size: 20px;
  font-weight: 600;
  color: #333;
  display: block;
  margin-bottom: 5px;
}
.stats-label.data-v-fda7de77 {
  font-size: 12px;
  color: #999;
}
.chart-container.data-v-fda7de77 {
  margin-top: 15px;
  padding-top: 15px;
  border-top: 1px solid #f0f0f0;
}
.chart-header.data-v-fda7de77 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}
.chart-title.data-v-fda7de77 {
  font-size: 14px;
  font-weight: 600;
  color: #333;
}
.chart-legend.data-v-fda7de77 {
  display: flex;
}
.legend-item.data-v-fda7de77 {
  display: flex;
  align-items: center;
  margin-left: 10px;
}
.legend-color.data-v-fda7de77 {
  width: 8px;
  height: 8px;
  border-radius: 4px;
  margin-right: 5px;
}
.legend-text.data-v-fda7de77 {
  font-size: 12px;
  color: #999;
}
.chart-placeholder.data-v-fda7de77 {
  height: 160px;
  position: relative;
}
.chart-mock.data-v-fda7de77 {
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  top: 0;
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
}
.chart-column.data-v-fda7de77 {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  height: 100%;
  position: relative;
}
.column-sent.data-v-fda7de77 {
  width: 6px;
  background-color: rgba(255, 77, 79, 0.7);
  border-radius: 3px 3px 0 0;
  margin-bottom: 2px;
}
.column-received.data-v-fda7de77 {
  width: 6px;
  background-color: rgba(82, 196, 26, 0.7);
  border-radius: 3px 3px 0 0;
}
.column-label.data-v-fda7de77 {
  position: absolute;
  bottom: -20px;
  font-size: 10px;
  color: #999;
}

/* 营销工具样式 */
.tools-section.data-v-fda7de77 {
  padding: 15px;
  background-color: #fff;
  margin-bottom: 10px;
}
.tools-grid.data-v-fda7de77 {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -5px;
}
.tool-item.data-v-fda7de77 {
  width: calc(33.33% - 10px);
  margin: 5px;
  padding: 15px 10px;
  display: flex;
  flex-direction: column;
  align-items: center;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}
.tool-icon.data-v-fda7de77 {
  width: 48px;
  height: 48px;
  border-radius: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 10px;
}
.tool-name.data-v-fda7de77 {
  font-size: 14px;
  font-weight: 600;
  color: #333;
  margin-bottom: 5px;
}
.tool-desc.data-v-fda7de77 {
  font-size: 12px;
  color: #999;
  text-align: center;
}

/* 最近活动样式 */
.recent-section.data-v-fda7de77 {
  padding: 15px;
  background-color: #fff;
  margin-bottom: 10px;
}
.view-all.data-v-fda7de77 {
  display: flex;
  align-items: center;
}
.view-all-text.data-v-fda7de77 {
  font-size: 12px;
  color: #999;
  margin-right: 5px;
}
.arrow-icon.data-v-fda7de77 {
  color: #999;
}
.activity-list.data-v-fda7de77 {
  margin: 0 -15px;
}
.activity-item.data-v-fda7de77 {
  display: flex;
  align-items: center;
  padding: 15px;
  border-bottom: 1px solid #f0f0f0;
}
.activity-item.data-v-fda7de77:last-child {
  border-bottom: none;
}
.activity-icon.data-v-fda7de77 {
  width: 40px;
  height: 40px;
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
}
.icon-inner.data-v-fda7de77 {
  font-size: 14px;
  font-weight: 600;
}
.activity-content.data-v-fda7de77 {
  flex: 1;
}
.activity-header.data-v-fda7de77 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 5px;
}
.activity-name.data-v-fda7de77 {
  font-size: 15px;
  font-weight: 600;
  color: #333;
}
.activity-status.data-v-fda7de77 {
  font-size: 12px;
  padding: 2px 6px;
  border-radius: 10px;
}
.status-active.data-v-fda7de77 {
  background-color: rgba(82, 196, 26, 0.1);
  color: #52C41A;
}
.status-ended.data-v-fda7de77 {
  background-color: rgba(153, 153, 153, 0.1);
  color: #999;
}
.activity-info.data-v-fda7de77 {
  display: flex;
  justify-content: space-between;
}
.activity-time.data-v-fda7de77, .activity-count.data-v-fda7de77 {
  font-size: 12px;
  color: #999;
}

/* 营销攻略样式 */
.strategy-section.data-v-fda7de77 {
  padding: 15px;
  background-color: #fff;
  margin-bottom: 20px;
}
.strategy-list.data-v-fda7de77 {
  margin: 0 -15px;
}
.strategy-item.data-v-fda7de77 {
  display: flex;
  align-items: center;
  padding: 15px;
  border-bottom: 1px solid #f0f0f0;
}
.strategy-item.data-v-fda7de77:last-child {
  border-bottom: none;
}
.strategy-icon.data-v-fda7de77 {
  width: 40px;
  height: 40px;
  border-radius: 20px;
  margin-right: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.strategy-content.data-v-fda7de77 {
  flex: 1;
}
.strategy-title.data-v-fda7de77 {
  font-size: 14px;
  font-weight: 600;
  color: #333;
  margin-bottom: 5px;
}
.strategy-desc.data-v-fda7de77 {
  font-size: 12px;
  color: #666;
}
.strategy-arrow.data-v-fda7de77 {
  width: 12px;
  height: 12px;
  border-top: 1px solid #ccc;
  border-right: 1px solid #ccc;
  transform: rotate(45deg);
}