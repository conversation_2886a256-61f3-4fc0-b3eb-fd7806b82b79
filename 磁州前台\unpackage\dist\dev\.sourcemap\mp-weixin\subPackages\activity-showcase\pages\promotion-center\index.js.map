{"version": 3, "file": "index.js", "sources": ["subPackages/activity-showcase/pages/promotion-center/index.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/c3ViUGFja2FnZXNcYWN0aXZpdHktc2hvd2Nhc2VccGFnZXNccHJvbW90aW9uLWNlbnRlclxpbmRleC52dWU"], "sourcesContent": ["<template>\n  <view class=\"promotion-center-container\">\n    <!-- 自定义导航栏 -->\n    <view class=\"custom-navbar\">\n      <view class=\"navbar-bg\"></view>\n      <view class=\"navbar-content\">\n        <view class=\"back-btn\" @click=\"goBack\">\n          <svg class=\"icon\" viewBox=\"0 0 24 24\" width=\"24\" height=\"24\">\n            <path d=\"M19 12H5M12 19l-7-7 7-7\" stroke=\"#FFFFFF\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\n          </svg>\n        </view>\n        <view class=\"navbar-title\">促销中心</view>\n        <view class=\"navbar-right\">\n          <view class=\"message-btn\" @click=\"navigateTo('/subPackages/activity-showcase/pages/message/index')\">\n            <svg class=\"icon\" viewBox=\"0 0 24 24\" width=\"24\" height=\"24\">\n              <path d=\"M21 15a2 2 0 01-2 2H7l-4 4V5a2 2 0 012-2h14a2 2 0 012 2z\" stroke=\"#FFFFFF\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\n            </svg>\n            <view class=\"badge\" v-if=\"unreadMessages > 0\">{{ unreadMessages > 99 ? '99+' : unreadMessages }}</view>\n          </view>\n        </view>\n      </view>\n    </view>\n\n    <!-- 顶部轮播图 -->\n    <swiper class=\"banner-swiper\" \n      :indicator-dots=\"true\" \n      indicator-color=\"rgba(255,255,255,0.4)\"\n      indicator-active-color=\"#FFFFFF\"\n      :autoplay=\"true\" \n      :interval=\"4000\" \n      :duration=\"400\"\n      :style=\"{ marginTop: 'calc(var(--status-bar-height) + 90rpx)' }\"\n    >\n      <swiper-item v-for=\"(banner, index) in banners\" :key=\"index\">\n        <image :src=\"banner.image\" mode=\"aspectFill\" class=\"banner-image\" @click=\"handleBannerClick(banner)\"></image>\n      </swiper-item>\n    </swiper>\n\n    <!-- 促销类型导航 -->\n    <view class=\"promotion-nav\">\n      <view \n        v-for=\"(item, index) in promotionTypes\" \n        :key=\"index\"\n        class=\"nav-item\"\n        @click=\"navigateTo(item.url)\"\n      >\n        <view class=\"nav-icon\" :style=\"{ background: item.bgColor }\">\n          <svg class=\"icon\" viewBox=\"0 0 24 24\" width=\"24\" height=\"24\">\n            <path :d=\"item.icon\" stroke=\"#FFFFFF\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\n          </svg>\n        </view>\n        <text class=\"nav-text\">{{ item.name }}</text>\n      </view>\n    </view>\n\n    <!-- 限时特惠区域 -->\n    <view class=\"section flash-sale-section\">\n      <view class=\"section-header\">\n        <view class=\"header-left\">\n          <text class=\"section-title\">限时特惠</text>\n          <view class=\"countdown\">\n            <text class=\"countdown-label\">距结束</text>\n            <text class=\"time-block\">{{ countdown.hours }}</text>\n            <text class=\"time-colon\">:</text>\n            <text class=\"time-block\">{{ countdown.minutes }}</text>\n            <text class=\"time-colon\">:</text>\n            <text class=\"time-block\">{{ countdown.seconds }}</text>\n          </view>\n        </view>\n        <view class=\"header-right\" @click=\"navigateTo('/subPackages/activity-showcase/pages/flash-sale/index')\">\n          <text class=\"view-all\">查看全部</text>\n          <svg class=\"icon\" viewBox=\"0 0 24 24\" width=\"16\" height=\"16\">\n            <path d=\"M9 18l6-6-6-6\" stroke=\"#FF3B30\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\n          </svg>\n        </view>\n      </view>\n      \n      <scroll-view class=\"flash-sale-scroll\" scroll-x>\n        <view class=\"flash-sale-list\">\n          <view \n            v-for=\"(item, index) in flashSaleItems\" \n            :key=\"index\"\n            class=\"flash-sale-item\"\n            @click=\"viewFlashSaleDetail(item)\"\n          >\n            <image :src=\"item.image\" class=\"item-image\" mode=\"aspectFill\"></image>\n            <view class=\"discount-tag\">{{ item.discount }}</view>\n            <view class=\"item-info\">\n              <text class=\"item-title\">{{ item.title }}</text>\n              <view class=\"item-price\">\n                <text class=\"price-current\">¥{{ item.currentPrice.toFixed(2) }}</text>\n                <text class=\"price-original\">¥{{ item.originalPrice.toFixed(2) }}</text>\n              </view>\n              <view class=\"progress-bar\">\n                <view class=\"progress-fill\" :style=\"{ width: `${item.soldPercentage}%` }\"></view>\n              </view>\n              <view class=\"item-sales\">\n                <text>已售{{ item.soldCount }}件</text>\n                <text class=\"sales-status\" v-if=\"item.soldPercentage > 80\">即将售罄</text>\n                <text class=\"sales-status\" v-else>热卖中</text>\n              </view>\n            </view>\n          </view>\n        </view>\n      </scroll-view>\n    </view>\n\n    <!-- 拼团优惠区域 -->\n    <view class=\"section group-buy-section\">\n      <view class=\"section-header\">\n        <view class=\"header-left\">\n          <text class=\"section-title\">拼团优惠</text>\n          <view class=\"section-tag\">低至5折</view>\n        </view>\n        <view class=\"header-right\" @click=\"navigateTo('/subPackages/activity-showcase/pages/group-buy/index')\">\n          <text class=\"view-all\">查看全部</text>\n          <svg class=\"icon\" viewBox=\"0 0 24 24\" width=\"16\" height=\"16\">\n            <path d=\"M9 18l6-6-6-6\" stroke=\"#FF9500\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\n          </svg>\n        </view>\n      </view>\n      \n      <view class=\"group-buy-grid\">\n        <view \n          v-for=\"(item, index) in groupBuyItems.slice(0, 2)\" \n          :key=\"index\"\n          class=\"group-buy-item\"\n          @click=\"viewGroupBuyDetail(item)\"\n        >\n          <image :src=\"item.image\" class=\"item-image\" mode=\"aspectFill\"></image>\n          <view class=\"item-info\">\n            <text class=\"item-title\">{{ item.title }}</text>\n            <view class=\"group-info\">\n              <view class=\"group-price\">\n                <text class=\"price-label\">{{ item.groupSize }}人团:</text>\n                <text class=\"price-current\">¥{{ item.groupPrice.toFixed(2) }}</text>\n                <text class=\"price-original\">¥{{ item.originalPrice.toFixed(2) }}</text>\n              </view>\n              <view class=\"group-btn\">去拼团</view>\n            </view>\n          </view>\n        </view>\n      </view>\n      \n      <view class=\"group-buy-list\">\n        <view \n          v-for=\"(item, index) in groupBuyItems.slice(2, 5)\" \n          :key=\"index\"\n          class=\"list-item\"\n          @click=\"viewGroupBuyDetail(item)\"\n        >\n          <image :src=\"item.image\" class=\"list-item-image\" mode=\"aspectFill\"></image>\n          <view class=\"list-item-info\">\n            <text class=\"list-item-title\">{{ item.title }}</text>\n            <view class=\"list-item-price\">\n              <view class=\"group-price\">\n                <text class=\"price-label\">{{ item.groupSize }}人团:</text>\n                <text class=\"price-current\">¥{{ item.groupPrice.toFixed(2) }}</text>\n                <text class=\"price-original\">¥{{ item.originalPrice.toFixed(2) }}</text>\n              </view>\n              <view class=\"group-btn\">去拼团</view>\n            </view>\n          </view>\n        </view>\n      </view>\n    </view>\n\n    <!-- 满减优惠区域 -->\n    <view class=\"section discount-section\">\n      <view class=\"section-header\">\n        <view class=\"header-left\">\n          <text class=\"section-title\">满减优惠</text>\n          <view class=\"section-tag\">满200减30</view>\n        </view>\n        <view class=\"header-right\" @click=\"navigateTo('/subPackages/activity-showcase/pages/discount/index')\">\n          <text class=\"view-all\">查看全部</text>\n          <svg class=\"icon\" viewBox=\"0 0 24 24\" width=\"16\" height=\"16\">\n            <path d=\"M9 18l6-6-6-6\" stroke=\"#5AC8FA\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\n          </svg>\n        </view>\n      </view>\n      \n      <view class=\"discount-grid\">\n        <view \n          v-for=\"(shop, index) in discountShops\" \n          :key=\"index\"\n          class=\"discount-shop\"\n          @click=\"viewShopDetail(shop)\"\n        >\n          <image :src=\"shop.logo\" class=\"shop-logo\" mode=\"aspectFill\"></image>\n          <view class=\"shop-info\">\n            <text class=\"shop-name\">{{ shop.name }}</text>\n            <view class=\"discount-tags\">\n              <view \n                v-for=\"(tag, tagIndex) in shop.discounts\" \n                :key=\"tagIndex\"\n                class=\"discount-tag\"\n              >\n                {{ tag }}\n              </view>\n            </view>\n          </view>\n        </view>\n      </view>\n    </view>\n\n    <!-- 优惠券专区 -->\n    <view class=\"section coupon-section\">\n      <view class=\"section-header\">\n        <view class=\"header-left\">\n          <text class=\"section-title\">优惠券专区</text>\n          <view class=\"section-tag\">天天领券</view>\n        </view>\n        <view class=\"header-right\" @click=\"navigateTo('/subPackages/activity-showcase/pages/coupon/index')\">\n          <text class=\"view-all\">查看全部</text>\n          <svg class=\"icon\" viewBox=\"0 0 24 24\" width=\"16\" height=\"16\">\n            <path d=\"M9 18l6-6-6-6\" stroke=\"#FF9500\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\n          </svg>\n        </view>\n      </view>\n      \n      <scroll-view class=\"coupon-scroll\" scroll-x>\n        <view class=\"coupon-list\">\n          <view \n            v-for=\"(coupon, index) in coupons\" \n            :key=\"index\"\n            class=\"coupon-item\"\n            :style=\"{ background: getCouponBackground(coupon.type) }\"\n            @click=\"receiveCoupon(coupon)\"\n          >\n            <view class=\"coupon-left\">\n              <view class=\"coupon-value\">\n                <text class=\"currency\" v-if=\"coupon.type !== 'discount'\">¥</text>\n                <text class=\"amount\">{{ coupon.value }}</text>\n                <text class=\"unit\" v-if=\"coupon.type === 'discount'\">折</text>\n              </view>\n              <text class=\"coupon-condition\">{{ coupon.condition }}</text>\n            </view>\n            <view class=\"coupon-divider\"></view>\n            <view class=\"coupon-right\">\n              <text class=\"coupon-name\">{{ coupon.name }}</text>\n              <text class=\"coupon-shop\">{{ coupon.shopName }}</text>\n              <text class=\"coupon-validity\">{{ coupon.validityPeriod }}</text>\n              <view class=\"coupon-btn\">立即领取</view>\n            </view>\n          </view>\n        </view>\n      </scroll-view>\n    </view>\n\n    <!-- 推荐活动区域 -->\n    <view class=\"section activity-section\">\n      <view class=\"section-header\">\n        <view class=\"header-left\">\n          <text class=\"section-title\">推荐活动</text>\n        </view>\n        <view class=\"header-right\" @click=\"navigateTo('/subPackages/activity-showcase/pages/list/index')\">\n          <text class=\"view-all\">查看全部</text>\n          <svg class=\"icon\" viewBox=\"0 0 24 24\" width=\"16\" height=\"16\">\n            <path d=\"M9 18l6-6-6-6\" stroke=\"#FF3B69\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\n          </svg>\n        </view>\n      </view>\n      \n      <view class=\"activity-list\">\n        <view \n          v-for=\"(activity, index) in recommendedActivities\" \n          :key=\"index\"\n          class=\"activity-item\"\n          @click=\"viewActivityDetail(activity)\"\n        >\n          <image :src=\"activity.image\" class=\"activity-image\" mode=\"aspectFill\"></image>\n          <view class=\"activity-info\">\n            <text class=\"activity-title\">{{ activity.title }}</text>\n            <view class=\"activity-meta\">\n              <view class=\"meta-item\">\n                <svg class=\"meta-icon\" viewBox=\"0 0 24 24\" width=\"16\" height=\"16\">\n                  <rect x=\"3\" y=\"4\" width=\"18\" height=\"18\" rx=\"2\" ry=\"2\" stroke=\"#999999\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></rect>\n                  <line x1=\"16\" y1=\"2\" x2=\"16\" y2=\"6\" stroke=\"#999999\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></line>\n                  <line x1=\"8\" y1=\"2\" x2=\"8\" y2=\"6\" stroke=\"#999999\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></line>\n                  <line x1=\"3\" y1=\"10\" x2=\"21\" y2=\"10\" stroke=\"#999999\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></line>\n                </svg>\n                <text class=\"meta-text\">{{ activity.date }}</text>\n              </view>\n              <view class=\"meta-item\">\n                <svg class=\"meta-icon\" viewBox=\"0 0 24 24\" width=\"16\" height=\"16\">\n                  <path d=\"M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0118 0z\" stroke=\"#999999\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\n                  <circle cx=\"12\" cy=\"10\" r=\"3\" stroke=\"#999999\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></circle>\n                </svg>\n                <text class=\"meta-text\">{{ activity.location }}</text>\n              </view>\n            </view>\n            <view class=\"activity-bottom\">\n              <view class=\"price-tag\" v-if=\"activity.price > 0\">\n                <text class=\"price-symbol\">¥</text>\n                <text class=\"price-value\">{{ activity.price.toFixed(2) }}</text>\n              </view>\n              <text class=\"price-free\" v-else>免费</text>\n              <view class=\"participants\">\n                <view class=\"avatar-group\">\n                  <image \n                    v-for=\"(avatar, avatarIndex) in activity.participants.slice(0, 3)\" \n                    :key=\"avatarIndex\"\n                    :src=\"avatar\"\n                    class=\"participant-avatar\"\n                  ></image>\n                </view>\n                <text class=\"participant-count\">{{ activity.participants.length }}人参与</text>\n              </view>\n            </view>\n          </view>\n        </view>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script setup>\nimport { ref, onMounted, onUnmounted } from 'vue';\n\n// 页面状态\nconst unreadMessages = ref(3);\nconst countdown = ref({ hours: '00', minutes: '00', seconds: '00' });\nlet countdownTimer = null;\n\n// 轮播图数据\nconst banners = [\n  { \n    id: '1',\n    image: '/static/demo/banner1.jpg',\n    type: 'flash_sale',\n    targetId: '1001'\n  },\n  { \n    id: '2',\n    image: '/static/demo/banner2.jpg',\n    type: 'group_buy',\n    targetId: '1002'\n  },\n  { \n    id: '3',\n    image: '/static/demo/banner3.jpg',\n    type: 'activity',\n    targetId: '1003'\n  }\n];\n\n// 促销类型导航\nconst promotionTypes = [\n  {\n    name: '限时特惠',\n    icon: 'M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z',\n    url: '/subPackages/activity-showcase/pages/flash-sale/index',\n    bgColor: 'linear-gradient(135deg, #FF3B30 0%, #FF9580 100%)'\n  },\n  {\n    name: '拼团优惠',\n    icon: 'M17 21v-2a4 4 0 00-4-4H5a4 4 0 00-4 4v2M9 11a4 4 0 100-8 4 4 0 000 8zM23 21v-2a4 4 0 00-3-3.87m-4-12a4 4 0 010 7.75',\n    url: '/subPackages/activity-showcase/pages/group-buy/index',\n    bgColor: 'linear-gradient(135deg, #FF9500 0%, #FFCC00 100%)'\n  },\n  {\n    name: '满减优惠',\n    icon: 'M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z',\n    url: '/subPackages/activity-showcase/pages/discount/index',\n    bgColor: 'linear-gradient(135deg, #5AC8FA 0%, #90E0FF 100%)'\n  },\n  {\n    name: '优惠券',\n    icon: 'M20 7h-4m4 0v16H4V7h4m12 0l-3-3H7l-3 3m4 0h10',\n    url: '/subPackages/activity-showcase/pages/coupon/index',\n    bgColor: 'linear-gradient(135deg, #FF9500 0%, #FFCC00 100%)'\n  },\n  {\n    name: '全部活动',\n    icon: 'M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10',\n    url: '/subPackages/activity-showcase/pages/list/index',\n    bgColor: 'linear-gradient(135deg, #FF3B69 0%, #FF7A9E 100%)'\n  }\n];\n\n// 限时特惠数据\nconst flashSaleItems = [\n  {\n    id: '1001',\n    title: '磁州窑陶艺体验课程',\n    image: '/static/demo/product1.jpg',\n    currentPrice: 99.00,\n    originalPrice: 198.00,\n    discount: '5折',\n    soldCount: 356,\n    soldPercentage: 85\n  },\n  {\n    id: '1002',\n    title: '磁州特产礼盒',\n    image: '/static/demo/product2.jpg',\n    currentPrice: 128.00,\n    originalPrice: 198.00,\n    discount: '6.5折',\n    soldCount: 512,\n    soldPercentage: 60\n  },\n  {\n    id: '1003',\n    title: '户外露营套装',\n    image: '/static/demo/product3.jpg',\n    currentPrice: 199.00,\n    originalPrice: 399.00,\n    discount: '5折',\n    soldCount: 128,\n    soldPercentage: 90\n  },\n  {\n    id: '1004',\n    title: '亲子烘焙课程',\n    image: '/static/demo/product4.jpg',\n    currentPrice: 158.00,\n    originalPrice: 258.00,\n    discount: '6折',\n    soldCount: 245,\n    soldPercentage: 75\n  }\n];\n\n// 拼团优惠数据\nconst groupBuyItems = [\n  {\n    id: '2001',\n    title: '磁州文化体验一日游',\n    image: '/static/demo/activity1.jpg',\n    groupPrice: 99.00,\n    originalPrice: 198.00,\n    groupSize: 3\n  },\n  {\n    id: '2002',\n    title: '亲子户外拓展活动',\n    image: '/static/demo/activity2.jpg',\n    groupPrice: 128.00,\n    originalPrice: 198.00,\n    groupSize: 2\n  },\n  {\n    id: '2003',\n    title: '传统文化体验课',\n    image: '/static/demo/activity4.jpg',\n    groupPrice: 88.00,\n    originalPrice: 158.00,\n    groupSize: 3\n  },\n  {\n    id: '2004',\n    title: '磁州美食体验',\n    image: '/static/demo/product6.jpg',\n    groupPrice: 68.00,\n    originalPrice: 128.00,\n    groupSize: 4\n  },\n  {\n    id: '2005',\n    title: '陶艺DIY体验',\n    image: '/static/demo/product7.jpg',\n    groupPrice: 79.00,\n    originalPrice: 158.00,\n    groupSize: 2\n  }\n];\n\n// 满减优惠商铺数据\nconst discountShops = [\n  {\n    id: '3001',\n    name: '磁州文化体验馆',\n    logo: '/static/demo/shop1.jpg',\n    discounts: ['满200减30', '满500减100']\n  },\n  {\n    id: '3002',\n    name: '磁州美食城',\n    logo: '/static/demo/shop2.jpg',\n    discounts: ['满100减15', '满300减50']\n  },\n  {\n    id: '3003',\n    name: '磁州户外拓展中心',\n    logo: '/static/demo/shop3.jpg',\n    discounts: ['满300减50', '满600减120']\n  },\n  {\n    id: '3004',\n    name: '磁州亲子乐园',\n    logo: '/static/demo/shop4.jpg',\n    discounts: ['满150减30', '满300减70']\n  }\n];\n\n// 优惠券数据\nconst coupons = [\n  {\n    id: '4001',\n    name: '新人专享券',\n    shopName: '磁州文化体验馆',\n    type: 'cash',\n    value: '30',\n    condition: '满200可用',\n    validityPeriod: '有效期至2024-06-30'\n  },\n  {\n    id: '4002',\n    name: '美食专享券',\n    shopName: '磁州美食城',\n    type: 'cash',\n    value: '15',\n    condition: '满100可用',\n    validityPeriod: '有效期至2024-06-15'\n  },\n  {\n    id: '4003',\n    name: '户外活动券',\n    shopName: '磁州户外拓展中心',\n    type: 'discount',\n    value: '8.5',\n    condition: '无门槛',\n    validityPeriod: '有效期至2024-06-20'\n  },\n  {\n    id: '4004',\n    name: '亲子活动券',\n    shopName: '磁州亲子乐园',\n    type: 'cash',\n    value: '50',\n    condition: '满300可用',\n    validityPeriod: '有效期至2024-06-25'\n  }\n];\n\n// 推荐活动数据\nconst recommendedActivities = [\n  {\n    id: '5001',\n    title: '磁州文化节',\n    image: '/static/demo/activity1.jpg',\n    date: '2024-06-15',\n    location: '磁州文化广场',\n    price: 0,\n    participants: [\n      '/static/demo/avatar1.png',\n      '/static/demo/avatar2.png',\n      '/static/demo/avatar3.png',\n      '/static/demo/avatar4.png',\n      '/static/demo/avatar5.png'\n    ]\n  },\n  {\n    id: '5002',\n    title: '亲子户外拓展活动',\n    image: '/static/demo/activity2.jpg',\n    date: '2024-05-28',\n    location: '磁州森林公园',\n    price: 128,\n    participants: [\n      '/static/demo/avatar1.png',\n      '/static/demo/avatar3.png',\n      '/static/demo/avatar5.png'\n    ]\n  },\n  {\n    id: '5003',\n    title: '社区篮球赛',\n    image: '/static/demo/activity3.jpg',\n    date: '2024-06-05',\n    location: '磁州体育中心',\n    price: 50,\n    participants: [\n      '/static/demo/avatar2.png',\n      '/static/demo/avatar4.png',\n      '/static/demo/avatar5.png',\n      '/static/demo/avatar1.png'\n    ]\n  }\n];\n\n// 生命周期\nonMounted(() => {\n  startCountdown();\n});\n\nonUnmounted(() => {\n  if (countdownTimer) {\n    clearInterval(countdownTimer);\n  }\n});\n\n// 方法\nconst startCountdown = () => {\n  // 设置倒计时结束时间（当前时间 + 12小时）\n  const endTime = new Date();\n  endTime.setHours(endTime.getHours() + 12);\n  \n  countdownTimer = setInterval(() => {\n    const now = new Date();\n    const diff = endTime - now;\n    \n    if (diff <= 0) {\n      clearInterval(countdownTimer);\n      countdown.value = { hours: '00', minutes: '00', seconds: '00' };\n      return;\n    }\n    \n    const hours = Math.floor(diff / (1000 * 60 * 60));\n    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));\n    const seconds = Math.floor((diff % (1000 * 60)) / 1000);\n    \n    countdown.value = {\n      hours: hours.toString().padStart(2, '0'),\n      minutes: minutes.toString().padStart(2, '0'),\n      seconds: seconds.toString().padStart(2, '0')\n    };\n  }, 1000);\n};\n\nconst handleBannerClick = (banner) => {\n  switch (banner.type) {\n    case 'flash_sale':\n      navigateTo(`/subPackages/activity-showcase/pages/flash-sale/detail?id=${banner.targetId}`);\n      break;\n    case 'group_buy':\n      navigateTo(`/subPackages/activity-showcase/pages/group-buy/detail?id=${banner.targetId}`);\n      break;\n    case 'activity':\n      navigateTo(`/subPackages/activity-showcase/pages/activity-detail/index?id=${banner.targetId}`);\n      break;\n    default:\n      navigateTo('/subPackages/activity-showcase/pages/list/index');\n  }\n};\n\nconst viewFlashSaleDetail = (item) => {\n  navigateTo(`/subPackages/activity-showcase/pages/flash-sale/detail?id=${item.id}`);\n};\n\nconst viewGroupBuyDetail = (item) => {\n  navigateTo(`/subPackages/activity-showcase/pages/group-buy/detail?id=${item.id}`);\n};\n\nconst viewShopDetail = (shop) => {\n  navigateTo(`/subPackages/activity-showcase/pages/shops/detail?id=${shop.id}`);\n};\n\nconst viewActivityDetail = (activity) => {\n  navigateTo(`/subPackages/activity-showcase/pages/activity-detail/index?id=${activity.id}`);\n};\n\nconst receiveCoupon = (coupon) => {\n  uni.showToast({\n    title: '领取成功',\n    icon: 'success'\n  });\n};\n\nconst getCouponBackground = (type) => {\n  const bgMap = {\n    'cash': 'linear-gradient(to right, #FF9500, #FFCC00)',\n    'discount': 'linear-gradient(to right, #5AC8FA, #90E0FF)',\n    'free': 'linear-gradient(to right, #34C759, #7ED321)'\n  };\n  return bgMap[type] || bgMap['cash'];\n};\n\nconst goBack = () => {\n  uni.navigateBack();\n};\n\nconst navigateTo = (url) => {\n  uni.navigateTo({ url });\n};\n</script>\n\n<style scoped>\n.promotion-center-container {\n  min-height: 100vh;\n  background-color: #F5F5F5;\n  padding-bottom: 30rpx;\n}\n\n/* 导航栏样式 */\n.custom-navbar {\n  position: fixed;\n  top: 0;\n  left: 0;\n  width: 100%;\n  z-index: 100;\n}\n\n.navbar-bg {\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  background: linear-gradient(135deg, #FF3B69 0%, #FF7A9E 100%);\n}\n\n.navbar-content {\n  position: relative;\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  height: 90rpx;\n  padding: var(--status-bar-height) 30rpx 0;\n}\n\n.back-btn, .message-btn {\n  width: 60rpx;\n  height: 60rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.message-btn {\n  position: relative;\n}\n\n.badge {\n  position: absolute;\n  top: 0;\n  right: 0;\n  min-width: 32rpx;\n  height: 32rpx;\n  border-radius: 16rpx;\n  background: #FF3B30;\n  color: #FFFFFF;\n  font-size: 20rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  padding: 0 6rpx;\n}\n\n.navbar-title {\n  font-size: 36rpx;\n  font-weight: bold;\n  color: #FFFFFF;\n}\n\n.navbar-right {\n  display: flex;\n  align-items: center;\n}\n\n/* 轮播图样式 */\n.banner-swiper {\n  height: 300rpx;\n  width: 100%;\n}\n\n.banner-image {\n  width: 100%;\n  height: 100%;\n}\n\n/* 促销类型导航样式 */\n.promotion-nav {\n  display: flex;\n  flex-wrap: wrap;\n  background: #FFFFFF;\n  padding: 30rpx 20rpx;\n  margin: 20rpx;\n  border-radius: 20rpx;\n  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.05);\n}\n\n.nav-item {\n  width: 20%;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  margin-bottom: 20rpx;\n}\n\n.nav-icon {\n  width: 80rpx;\n  height: 80rpx;\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-bottom: 10rpx;\n}\n\n.nav-text {\n  font-size: 24rpx;\n  color: #333333;\n}\n\n/* 通用区域样式 */\n.section {\n  background: #FFFFFF;\n  margin: 20rpx;\n  border-radius: 20rpx;\n  padding: 20rpx;\n  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.05);\n}\n\n.section-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 20rpx;\n}\n\n.header-left {\n  display: flex;\n  align-items: center;\n}\n\n.section-title {\n  font-size: 32rpx;\n  font-weight: bold;\n  color: #333333;\n  margin-right: 15rpx;\n}\n\n.section-tag {\n  font-size: 22rpx;\n  color: #FFFFFF;\n  background: #FF3B69;\n  padding: 4rpx 12rpx;\n  border-radius: 20rpx;\n}\n\n.header-right {\n  display: flex;\n  align-items: center;\n}\n\n.view-all {\n  font-size: 26rpx;\n  color: #999999;\n  margin-right: 6rpx;\n}\n\n/* 限时特惠区域样式 */\n.flash-sale-section .section-title {\n  color: #FF3B30;\n}\n\n.countdown {\n  display: flex;\n  align-items: center;\n  background: rgba(255, 59, 48, 0.1);\n  border-radius: 20rpx;\n  padding: 4rpx 12rpx;\n}\n\n.countdown-label {\n  font-size: 22rpx;\n  color: #FF3B30;\n  margin-right: 6rpx;\n}\n\n.time-block {\n  width: 40rpx;\n  height: 40rpx;\n  background: #FF3B30;\n  color: #FFFFFF;\n  font-size: 22rpx;\n  font-weight: bold;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  border-radius: 6rpx;\n}\n\n.time-colon {\n  font-size: 22rpx;\n  color: #FF3B30;\n  margin: 0 4rpx;\n}\n\n.flash-sale-scroll {\n  white-space: nowrap;\n}\n\n.flash-sale-list {\n  display: flex;\n  padding: 10rpx 0;\n}\n\n.flash-sale-item {\n  display: inline-block;\n  width: 280rpx;\n  margin-right: 20rpx;\n  border-radius: 15rpx;\n  overflow: hidden;\n  background: #FFFFFF;\n  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.05);\n  position: relative;\n}\n\n.item-image {\n  width: 100%;\n  height: 280rpx;\n  object-fit: cover;\n}\n\n.discount-tag {\n  position: absolute;\n  top: 10rpx;\n  right: 10rpx;\n  background: rgba(255, 59, 48, 0.8);\n  color: #FFFFFF;\n  font-size: 22rpx;\n  padding: 4rpx 12rpx;\n  border-radius: 20rpx;\n}\n\n.item-info {\n  padding: 15rpx;\n}\n\n.item-title {\n  font-size: 26rpx;\n  color: #333333;\n  margin-bottom: 10rpx;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  display: -webkit-box;\n  -webkit-line-clamp: 2;\n  -webkit-box-orient: vertical;\n  white-space: normal;\n  height: 72rpx;\n}\n\n.item-price {\n  display: flex;\n  align-items: center;\n  margin-bottom: 10rpx;\n}\n\n.price-current {\n  font-size: 28rpx;\n  font-weight: bold;\n  color: #FF3B30;\n  margin-right: 10rpx;\n}\n\n.price-original {\n  font-size: 24rpx;\n  color: #999999;\n  text-decoration: line-through;\n}\n\n.progress-bar {\n  height: 8rpx;\n  background: #F0F0F0;\n  border-radius: 4rpx;\n  margin-bottom: 10rpx;\n  overflow: hidden;\n}\n\n.progress-fill {\n  height: 100%;\n  background: #FF3B30;\n  border-radius: 4rpx;\n}\n\n.item-sales {\n  display: flex;\n  justify-content: space-between;\n  font-size: 22rpx;\n  color: #999999;\n}\n\n.sales-status {\n  color: #FF3B30;\n}\n\n/* 拼团优惠区域样式 */\n.group-buy-section .section-title {\n  color: #FF9500;\n}\n\n.group-buy-section .section-tag {\n  background: #FF9500;\n}\n\n.group-buy-grid {\n  display: flex;\n  justify-content: space-between;\n  margin-bottom: 20rpx;\n}\n\n.group-buy-item {\n  width: 48%;\n  border-radius: 15rpx;\n  overflow: hidden;\n  background: #FFFFFF;\n  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.05);\n}\n\n.group-buy-item .item-image {\n  width: 100%;\n  height: 200rpx;\n}\n\n.group-buy-item .item-info {\n  padding: 15rpx;\n}\n\n.group-info {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.group-price {\n  display: flex;\n  align-items: baseline;\n  flex-wrap: wrap;\n}\n\n.price-label {\n  font-size: 22rpx;\n  color: #666666;\n  margin-right: 6rpx;\n}\n\n.group-btn {\n  font-size: 24rpx;\n  color: #FFFFFF;\n  background: #FF9500;\n  padding: 6rpx 15rpx;\n  border-radius: 20rpx;\n}\n\n.group-buy-list {\n  margin-top: 20rpx;\n}\n\n.list-item {\n  display: flex;\n  margin-bottom: 20rpx;\n  padding-bottom: 20rpx;\n  border-bottom: 1rpx solid #F0F0F0;\n}\n\n.list-item:last-child {\n  margin-bottom: 0;\n  padding-bottom: 0;\n  border-bottom: none;\n}\n\n.list-item-image {\n  width: 160rpx;\n  height: 160rpx;\n  border-radius: 10rpx;\n  margin-right: 20rpx;\n}\n\n.list-item-info {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  justify-content: space-between;\n}\n\n.list-item-title {\n  font-size: 28rpx;\n  color: #333333;\n  margin-bottom: 10rpx;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  display: -webkit-box;\n  -webkit-line-clamp: 2;\n  -webkit-box-orient: vertical;\n}\n\n.list-item-price {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n/* 满减优惠区域样式 */\n.discount-section .section-title {\n  color: #5AC8FA;\n}\n\n.discount-section .section-tag {\n  background: #5AC8FA;\n}\n\n.discount-grid {\n  display: grid;\n  grid-template-columns: repeat(2, 1fr);\n  gap: 20rpx;\n}\n\n.discount-shop {\n  display: flex;\n  align-items: center;\n  padding: 15rpx;\n  background: #F9F9F9;\n  border-radius: 15rpx;\n}\n\n.shop-logo {\n  width: 80rpx;\n  height: 80rpx;\n  border-radius: 50%;\n  margin-right: 15rpx;\n}\n\n.shop-info {\n  flex: 1;\n}\n\n.shop-name {\n  font-size: 26rpx;\n  color: #333333;\n  margin-bottom: 10rpx;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n}\n\n.discount-tags {\n  display: flex;\n  flex-wrap: wrap;\n}\n\n.discount-tags .discount-tag {\n  font-size: 20rpx;\n  color: #5AC8FA;\n  background: rgba(90, 200, 250, 0.1);\n  padding: 4rpx 10rpx;\n  border-radius: 10rpx;\n  margin-right: 10rpx;\n  margin-bottom: 6rpx;\n}\n\n/* 优惠券专区样式 */\n.coupon-section .section-title {\n  color: #FF9500;\n}\n\n.coupon-section .section-tag {\n  background: #FF9500;\n}\n\n.coupon-scroll {\n  white-space: nowrap;\n}\n\n.coupon-list {\n  display: flex;\n  padding: 10rpx 0;\n}\n\n.coupon-item {\n  display: inline-flex;\n  width: 500rpx;\n  height: 180rpx;\n  margin-right: 20rpx;\n  border-radius: 15rpx;\n  overflow: hidden;\n  position: relative;\n}\n\n.coupon-item::before {\n  content: '';\n  position: absolute;\n  left: 160rpx;\n  top: -10rpx;\n  width: 20rpx;\n  height: 20rpx;\n  border-radius: 50%;\n  background: #F5F5F5;\n}\n\n.coupon-item::after {\n  content: '';\n  position: absolute;\n  left: 160rpx;\n  bottom: -10rpx;\n  width: 20rpx;\n  height: 20rpx;\n  border-radius: 50%;\n  background: #F5F5F5;\n}\n\n.coupon-left {\n  width: 160rpx;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: 20rpx;\n}\n\n.coupon-value {\n  display: flex;\n  align-items: baseline;\n}\n\n.currency {\n  font-size: 24rpx;\n  color: #FFFFFF;\n}\n\n.amount {\n  font-size: 48rpx;\n  font-weight: bold;\n  color: #FFFFFF;\n}\n\n.unit {\n  font-size: 24rpx;\n  color: #FFFFFF;\n}\n\n.coupon-condition {\n  font-size: 22rpx;\n  color: rgba(255, 255, 255, 0.8);\n  margin-top: 10rpx;\n}\n\n.coupon-divider {\n  width: 1rpx;\n  height: 140rpx;\n  background: rgba(255, 255, 255, 0.3);\n  margin: 20rpx 0;\n  position: relative;\n  z-index: 1;\n}\n\n.coupon-right {\n  flex: 1;\n  padding: 20rpx;\n  display: flex;\n  flex-direction: column;\n  justify-content: space-between;\n}\n\n.coupon-name {\n  font-size: 28rpx;\n  font-weight: bold;\n  color: #FFFFFF;\n  margin-bottom: 6rpx;\n}\n\n.coupon-shop {\n  font-size: 24rpx;\n  color: rgba(255, 255, 255, 0.8);\n  margin-bottom: 6rpx;\n}\n\n.coupon-validity {\n  font-size: 22rpx;\n  color: rgba(255, 255, 255, 0.6);\n}\n\n.coupon-btn {\n  align-self: flex-end;\n  font-size: 24rpx;\n  color: #FFFFFF;\n  background: rgba(255, 255, 255, 0.2);\n  padding: 6rpx 15rpx;\n  border-radius: 20rpx;\n  border: 1rpx solid rgba(255, 255, 255, 0.4);\n}\n\n/* 推荐活动区域样式 */\n.activity-section .section-title {\n  color: #FF3B69;\n}\n\n.activity-list {\n  display: flex;\n  flex-direction: column;\n}\n\n.activity-item {\n  display: flex;\n  margin-bottom: 20rpx;\n  padding-bottom: 20rpx;\n  border-bottom: 1rpx solid #F0F0F0;\n}\n\n.activity-item:last-child {\n  margin-bottom: 0;\n  padding-bottom: 0;\n  border-bottom: none;\n}\n\n.activity-image {\n  width: 200rpx;\n  height: 150rpx;\n  border-radius: 10rpx;\n  margin-right: 20rpx;\n}\n\n.activity-info {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  justify-content: space-between;\n}\n\n.activity-title {\n  font-size: 28rpx;\n  color: #333333;\n  margin-bottom: 10rpx;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  display: -webkit-box;\n  -webkit-line-clamp: 2;\n  -webkit-box-orient: vertical;\n}\n\n.activity-meta {\n  display: flex;\n  margin-bottom: 10rpx;\n}\n\n.meta-item {\n  display: flex;\n  align-items: center;\n  margin-right: 20rpx;\n}\n\n.meta-icon {\n  margin-right: 6rpx;\n}\n\n.meta-text {\n  font-size: 24rpx;\n  color: #999999;\n}\n\n.activity-bottom {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.price-tag {\n  display: flex;\n  align-items: baseline;\n}\n\n.price-symbol {\n  font-size: 22rpx;\n  color: #FF3B69;\n  margin-right: 2rpx;\n}\n\n.price-value {\n  font-size: 28rpx;\n  font-weight: bold;\n  color: #FF3B69;\n}\n\n.price-free {\n  font-size: 24rpx;\n  color: #34C759;\n  font-weight: 500;\n}\n\n.participants {\n  display: flex;\n  align-items: center;\n}\n\n.avatar-group {\n  display: flex;\n  margin-right: 10rpx;\n}\n\n.participant-avatar {\n  width: 40rpx;\n  height: 40rpx;\n  border-radius: 50%;\n  border: 2rpx solid #FFFFFF;\n  margin-left: -10rpx;\n}\n\n.participant-avatar:first-child {\n  margin-left: 0;\n}\n\n.participant-count {\n  font-size: 22rpx;\n  color: #999999;\n}\n</style>", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/subPackages/activity-showcase/pages/promotion-center/index.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "onMounted", "onUnmounted", "uni"], "mappings": ";;;;;;;;;;;;;AAiUA,UAAM,iBAAiBA,cAAAA,IAAI,CAAC;AAC5B,UAAM,YAAYA,cAAAA,IAAI,EAAE,OAAO,MAAM,SAAS,MAAM,SAAS,KAAI,CAAE;AACnE,QAAI,iBAAiB;AAGrB,UAAM,UAAU;AAAA,MACd;AAAA,QACE,IAAI;AAAA,QACJ,OAAO;AAAA,QACP,MAAM;AAAA,QACN,UAAU;AAAA,MACX;AAAA,MACD;AAAA,QACE,IAAI;AAAA,QACJ,OAAO;AAAA,QACP,MAAM;AAAA,QACN,UAAU;AAAA,MACX;AAAA,MACD;AAAA,QACE,IAAI;AAAA,QACJ,OAAO;AAAA,QACP,MAAM;AAAA,QACN,UAAU;AAAA,MACX;AAAA,IACH;AAGA,UAAM,iBAAiB;AAAA,MACrB;AAAA,QACE,MAAM;AAAA,QACN,MAAM;AAAA,QACN,KAAK;AAAA,QACL,SAAS;AAAA,MACV;AAAA,MACD;AAAA,QACE,MAAM;AAAA,QACN,MAAM;AAAA,QACN,KAAK;AAAA,QACL,SAAS;AAAA,MACV;AAAA,MACD;AAAA,QACE,MAAM;AAAA,QACN,MAAM;AAAA,QACN,KAAK;AAAA,QACL,SAAS;AAAA,MACV;AAAA,MACD;AAAA,QACE,MAAM;AAAA,QACN,MAAM;AAAA,QACN,KAAK;AAAA,QACL,SAAS;AAAA,MACV;AAAA,MACD;AAAA,QACE,MAAM;AAAA,QACN,MAAM;AAAA,QACN,KAAK;AAAA,QACL,SAAS;AAAA,MACV;AAAA,IACH;AAGA,UAAM,iBAAiB;AAAA,MACrB;AAAA,QACE,IAAI;AAAA,QACJ,OAAO;AAAA,QACP,OAAO;AAAA,QACP,cAAc;AAAA,QACd,eAAe;AAAA,QACf,UAAU;AAAA,QACV,WAAW;AAAA,QACX,gBAAgB;AAAA,MACjB;AAAA,MACD;AAAA,QACE,IAAI;AAAA,QACJ,OAAO;AAAA,QACP,OAAO;AAAA,QACP,cAAc;AAAA,QACd,eAAe;AAAA,QACf,UAAU;AAAA,QACV,WAAW;AAAA,QACX,gBAAgB;AAAA,MACjB;AAAA,MACD;AAAA,QACE,IAAI;AAAA,QACJ,OAAO;AAAA,QACP,OAAO;AAAA,QACP,cAAc;AAAA,QACd,eAAe;AAAA,QACf,UAAU;AAAA,QACV,WAAW;AAAA,QACX,gBAAgB;AAAA,MACjB;AAAA,MACD;AAAA,QACE,IAAI;AAAA,QACJ,OAAO;AAAA,QACP,OAAO;AAAA,QACP,cAAc;AAAA,QACd,eAAe;AAAA,QACf,UAAU;AAAA,QACV,WAAW;AAAA,QACX,gBAAgB;AAAA,MACjB;AAAA,IACH;AAGA,UAAM,gBAAgB;AAAA,MACpB;AAAA,QACE,IAAI;AAAA,QACJ,OAAO;AAAA,QACP,OAAO;AAAA,QACP,YAAY;AAAA,QACZ,eAAe;AAAA,QACf,WAAW;AAAA,MACZ;AAAA,MACD;AAAA,QACE,IAAI;AAAA,QACJ,OAAO;AAAA,QACP,OAAO;AAAA,QACP,YAAY;AAAA,QACZ,eAAe;AAAA,QACf,WAAW;AAAA,MACZ;AAAA,MACD;AAAA,QACE,IAAI;AAAA,QACJ,OAAO;AAAA,QACP,OAAO;AAAA,QACP,YAAY;AAAA,QACZ,eAAe;AAAA,QACf,WAAW;AAAA,MACZ;AAAA,MACD;AAAA,QACE,IAAI;AAAA,QACJ,OAAO;AAAA,QACP,OAAO;AAAA,QACP,YAAY;AAAA,QACZ,eAAe;AAAA,QACf,WAAW;AAAA,MACZ;AAAA,MACD;AAAA,QACE,IAAI;AAAA,QACJ,OAAO;AAAA,QACP,OAAO;AAAA,QACP,YAAY;AAAA,QACZ,eAAe;AAAA,QACf,WAAW;AAAA,MACZ;AAAA,IACH;AAGA,UAAM,gBAAgB;AAAA,MACpB;AAAA,QACE,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,MAAM;AAAA,QACN,WAAW,CAAC,WAAW,UAAU;AAAA,MAClC;AAAA,MACD;AAAA,QACE,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,MAAM;AAAA,QACN,WAAW,CAAC,WAAW,SAAS;AAAA,MACjC;AAAA,MACD;AAAA,QACE,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,MAAM;AAAA,QACN,WAAW,CAAC,WAAW,UAAU;AAAA,MAClC;AAAA,MACD;AAAA,QACE,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,MAAM;AAAA,QACN,WAAW,CAAC,WAAW,SAAS;AAAA,MACjC;AAAA,IACH;AAGA,UAAM,UAAU;AAAA,MACd;AAAA,QACE,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,UAAU;AAAA,QACV,MAAM;AAAA,QACN,OAAO;AAAA,QACP,WAAW;AAAA,QACX,gBAAgB;AAAA,MACjB;AAAA,MACD;AAAA,QACE,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,UAAU;AAAA,QACV,MAAM;AAAA,QACN,OAAO;AAAA,QACP,WAAW;AAAA,QACX,gBAAgB;AAAA,MACjB;AAAA,MACD;AAAA,QACE,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,UAAU;AAAA,QACV,MAAM;AAAA,QACN,OAAO;AAAA,QACP,WAAW;AAAA,QACX,gBAAgB;AAAA,MACjB;AAAA,MACD;AAAA,QACE,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,UAAU;AAAA,QACV,MAAM;AAAA,QACN,OAAO;AAAA,QACP,WAAW;AAAA,QACX,gBAAgB;AAAA,MACjB;AAAA,IACH;AAGA,UAAM,wBAAwB;AAAA,MAC5B;AAAA,QACE,IAAI;AAAA,QACJ,OAAO;AAAA,QACP,OAAO;AAAA,QACP,MAAM;AAAA,QACN,UAAU;AAAA,QACV,OAAO;AAAA,QACP,cAAc;AAAA,UACZ;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACD;AAAA,MACF;AAAA,MACD;AAAA,QACE,IAAI;AAAA,QACJ,OAAO;AAAA,QACP,OAAO;AAAA,QACP,MAAM;AAAA,QACN,UAAU;AAAA,QACV,OAAO;AAAA,QACP,cAAc;AAAA,UACZ;AAAA,UACA;AAAA,UACA;AAAA,QACD;AAAA,MACF;AAAA,MACD;AAAA,QACE,IAAI;AAAA,QACJ,OAAO;AAAA,QACP,OAAO;AAAA,QACP,MAAM;AAAA,QACN,UAAU;AAAA,QACV,OAAO;AAAA,QACP,cAAc;AAAA,UACZ;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACD;AAAA,MACF;AAAA,IACH;AAGAC,kBAAAA,UAAU,MAAM;AACd;IACF,CAAC;AAEDC,kBAAAA,YAAY,MAAM;AAChB,UAAI,gBAAgB;AAClB,sBAAc,cAAc;AAAA,MAC7B;AAAA,IACH,CAAC;AAGD,UAAM,iBAAiB,MAAM;AAE3B,YAAM,UAAU,oBAAI;AACpB,cAAQ,SAAS,QAAQ,SAAU,IAAG,EAAE;AAExC,uBAAiB,YAAY,MAAM;AACjC,cAAM,MAAM,oBAAI;AAChB,cAAM,OAAO,UAAU;AAEvB,YAAI,QAAQ,GAAG;AACb,wBAAc,cAAc;AAC5B,oBAAU,QAAQ,EAAE,OAAO,MAAM,SAAS,MAAM,SAAS;AACzD;AAAA,QACD;AAED,cAAM,QAAQ,KAAK,MAAM,QAAQ,MAAO,KAAK,GAAG;AAChD,cAAM,UAAU,KAAK,MAAO,QAAQ,MAAO,KAAK,OAAQ,MAAO,GAAG;AAClE,cAAM,UAAU,KAAK,MAAO,QAAQ,MAAO,MAAO,GAAI;AAEtD,kBAAU,QAAQ;AAAA,UAChB,OAAO,MAAM,SAAQ,EAAG,SAAS,GAAG,GAAG;AAAA,UACvC,SAAS,QAAQ,SAAQ,EAAG,SAAS,GAAG,GAAG;AAAA,UAC3C,SAAS,QAAQ,SAAQ,EAAG,SAAS,GAAG,GAAG;AAAA,QACjD;AAAA,MACG,GAAE,GAAI;AAAA,IACT;AAEA,UAAM,oBAAoB,CAAC,WAAW;AACpC,cAAQ,OAAO,MAAI;AAAA,QACjB,KAAK;AACH,qBAAW,6DAA6D,OAAO,QAAQ,EAAE;AACzF;AAAA,QACF,KAAK;AACH,qBAAW,4DAA4D,OAAO,QAAQ,EAAE;AACxF;AAAA,QACF,KAAK;AACH,qBAAW,iEAAiE,OAAO,QAAQ,EAAE;AAC7F;AAAA,QACF;AACE,qBAAW,iDAAiD;AAAA,MAC/D;AAAA,IACH;AAEA,UAAM,sBAAsB,CAAC,SAAS;AACpC,iBAAW,6DAA6D,KAAK,EAAE,EAAE;AAAA,IACnF;AAEA,UAAM,qBAAqB,CAAC,SAAS;AACnC,iBAAW,4DAA4D,KAAK,EAAE,EAAE;AAAA,IAClF;AAEA,UAAM,iBAAiB,CAAC,SAAS;AAC/B,iBAAW,wDAAwD,KAAK,EAAE,EAAE;AAAA,IAC9E;AAEA,UAAM,qBAAqB,CAAC,aAAa;AACvC,iBAAW,iEAAiE,SAAS,EAAE,EAAE;AAAA,IAC3F;AAEA,UAAM,gBAAgB,CAAC,WAAW;AAChCC,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,MACV,CAAG;AAAA,IACH;AAEA,UAAM,sBAAsB,CAAC,SAAS;AACpC,YAAM,QAAQ;AAAA,QACZ,QAAQ;AAAA,QACR,YAAY;AAAA,QACZ,QAAQ;AAAA,MACZ;AACE,aAAO,MAAM,IAAI,KAAK,MAAM,MAAM;AAAA,IACpC;AAEA,UAAM,SAAS,MAAM;AACnBA,oBAAG,MAAC,aAAY;AAAA,IAClB;AAEA,UAAM,aAAa,CAAC,QAAQ;AAC1BA,oBAAAA,MAAI,WAAW,EAAE,IAAG,CAAE;AAAA,IACxB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACnqBA,GAAG,WAAW,eAAe;"}