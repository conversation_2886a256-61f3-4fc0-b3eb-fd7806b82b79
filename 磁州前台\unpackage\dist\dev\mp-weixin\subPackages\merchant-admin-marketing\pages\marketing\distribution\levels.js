"use strict";
const common_vendor = require("../../../../../common/vendor.js");
const utils_distributionService = require("../../../../../utils/distributionService.js");
const _sfc_main = {
  __name: "levels",
  setup(__props) {
    const settings = common_vendor.reactive({
      enableLevels: true,
      autoUpgrade: true,
      downgradeEnable: false,
      upgradeCycle: "monthly"
    });
    const levels = common_vendor.ref([
      {
        id: 1,
        name: "普通分销员",
        tag: "初级",
        color: "#67C23A",
        upgradeType: "sales",
        upgradeValue: 0,
        commissionRates: {
          level1: 10,
          level2: 5
        },
        benefits: "基础分销权益"
      },
      {
        id: 2,
        name: "高级分销员",
        tag: "高级",
        color: "#409EFF",
        upgradeType: "sales",
        upgradeValue: 1e3,
        commissionRates: {
          level1: 15,
          level2: 8
        },
        benefits: "专属活动优先参与权"
      },
      {
        id: 3,
        name: "金牌分销员",
        tag: "金牌",
        color: "#E6A23C",
        upgradeType: "sales",
        upgradeValue: 5e3,
        commissionRates: {
          level1: 20,
          level2: 10
        },
        benefits: "专属客服、活动优先参与权"
      }
    ]);
    const colorOptions = [
      "#67C23A",
      "#409EFF",
      "#E6A23C",
      "#F56C6C",
      "#909399",
      "#6B0FBE",
      "#FF9500",
      "#00C58E",
      "#1989FA",
      "#FF5722"
    ];
    const showLevelModal = common_vendor.ref(false);
    const isEditMode = common_vendor.ref(false);
    const currentLevelIndex = common_vendor.ref(-1);
    const formData = common_vendor.reactive({
      name: "",
      tag: "",
      color: "#67C23A",
      upgradeType: "sales",
      upgradeValue: 0,
      commissionRates: {
        level1: 0,
        level2: 0
      },
      benefits: ""
    });
    const canSubmit = common_vendor.computed(() => {
      return formData.name.trim() !== "" && formData.tag.trim() !== "" && formData.upgradeValue !== "" && formData.commissionRates.level1 !== "" && formData.commissionRates.level2 !== "";
    });
    common_vendor.onMounted(async () => {
      await getDistributionLevels();
    });
    const getDistributionLevels = async () => {
      try {
        const result = await utils_distributionService.distributionService.getDistributionLevels();
        if (result) {
          Object.assign(settings, result.settings);
          if (result.levels && result.levels.length > 0) {
            levels.value = result.levels;
          }
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at subPackages/merchant-admin-marketing/pages/marketing/distribution/levels.vue:276", "获取分销等级设置失败", error);
        common_vendor.index.showToast({
          title: "获取分销等级设置失败",
          icon: "none"
        });
      }
    };
    const toggleLevels = (e) => {
      settings.enableLevels = e.detail.value;
    };
    const getUpgradeText = (level) => {
      if (level.upgradeType === "sales") {
        return `累计销售额 ≥ ${level.upgradeValue}元`;
      } else {
        return `累计订单数 ≥ ${level.upgradeValue}单`;
      }
    };
    const addLevel = () => {
      isEditMode.value = false;
      currentLevelIndex.value = -1;
      formData.name = "";
      formData.tag = "";
      formData.color = "#67C23A";
      formData.upgradeType = "sales";
      formData.upgradeValue = "";
      formData.commissionRates.level1 = "";
      formData.commissionRates.level2 = "";
      formData.benefits = "";
      showLevelModal.value = true;
    };
    const editLevel = (index) => {
      isEditMode.value = true;
      currentLevelIndex.value = index;
      const level = levels.value[index];
      formData.name = level.name;
      formData.tag = level.tag;
      formData.color = level.color;
      formData.upgradeType = level.upgradeType;
      formData.upgradeValue = level.upgradeValue;
      formData.commissionRates.level1 = level.commissionRates.level1;
      formData.commissionRates.level2 = level.commissionRates.level2;
      formData.benefits = level.benefits;
      showLevelModal.value = true;
    };
    const deleteLevel = (index) => {
      common_vendor.index.showModal({
        title: "删除等级",
        content: "确定要删除该等级吗？删除后无法恢复。",
        success: (res) => {
          if (res.confirm) {
            levels.value.splice(index, 1);
          }
        }
      });
    };
    const closeLevelModal = () => {
      showLevelModal.value = false;
    };
    const submitLevel = () => {
      if (!canSubmit.value)
        return;
      const newLevel = {
        name: formData.name,
        tag: formData.tag,
        color: formData.color,
        upgradeType: formData.upgradeType,
        upgradeValue: parseFloat(formData.upgradeValue),
        commissionRates: {
          level1: parseFloat(formData.commissionRates.level1),
          level2: parseFloat(formData.commissionRates.level2)
        },
        benefits: formData.benefits
      };
      if (isEditMode.value) {
        levels.value[currentLevelIndex.value] = {
          ...levels.value[currentLevelIndex.value],
          ...newLevel
        };
      } else {
        newLevel.id = levels.value.length > 0 ? Math.max(...levels.value.map((l) => l.id)) + 1 : 1;
        levels.value.push(newLevel);
      }
      closeLevelModal();
    };
    const saveSettings = async () => {
      try {
        common_vendor.index.showLoading({
          title: "保存中...",
          mask: true
        });
        const result = await utils_distributionService.distributionService.saveDistributionLevels({
          settings,
          levels: levels.value
        });
        common_vendor.index.hideLoading();
        if (result.success) {
          common_vendor.index.showToast({
            title: "保存成功",
            icon: "success"
          });
        } else {
          common_vendor.index.showModal({
            title: "保存失败",
            content: result.message || "请稍后再试",
            showCancel: false
          });
        }
      } catch (error) {
        common_vendor.index.hideLoading();
        common_vendor.index.__f__("error", "at subPackages/merchant-admin-marketing/pages/marketing/distribution/levels.vue:415", "保存分销等级设置失败", error);
        common_vendor.index.showToast({
          title: "保存失败",
          icon: "none"
        });
      }
    };
    const goBack = () => {
      common_vendor.index.navigateBack();
    };
    const showHelp = () => {
      common_vendor.index.showModal({
        title: "分销等级帮助",
        content: "分销等级系统可以根据分销员的业绩设置不同的佣金比例和特权，激励分销员提升业绩。系统会根据设置的条件自动升级分销员等级。",
        showCancel: false
      });
    };
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_vendor.o(goBack),
        b: common_vendor.o(showHelp),
        c: settings.enableLevels,
        d: common_vendor.o(toggleLevels),
        e: settings.enableLevels
      }, settings.enableLevels ? {
        f: common_vendor.f(levels.value, (level, index, i0) => {
          return common_vendor.e({
            a: common_vendor.t(level.name),
            b: common_vendor.t(level.tag),
            c: level.color,
            d: common_vendor.o(($event) => editLevel(index), index),
            e: index > 0
          }, index > 0 ? {
            f: common_vendor.o(($event) => deleteLevel(index), index)
          } : {}, {
            g: common_vendor.t(getUpgradeText(level)),
            h: common_vendor.t(level.commissionRates.level1),
            i: common_vendor.t(level.commissionRates.level2),
            j: common_vendor.t(level.benefits || "无特殊权益"),
            k: index
          });
        }),
        g: common_vendor.o(addLevel)
      } : {}, {
        h: showLevelModal.value
      }, showLevelModal.value ? {
        i: common_vendor.o(closeLevelModal),
        j: common_vendor.t(isEditMode.value ? "编辑等级" : "添加等级"),
        k: common_vendor.o(closeLevelModal),
        l: formData.name,
        m: common_vendor.o(($event) => formData.name = $event.detail.value),
        n: formData.tag,
        o: common_vendor.o(($event) => formData.tag = $event.detail.value),
        p: common_vendor.f(colorOptions, (color, index, i0) => {
          return {
            a: index,
            b: color,
            c: formData.color === color ? 1 : "",
            d: common_vendor.o(($event) => formData.color = color, index)
          };
        }),
        q: formData.upgradeType === "sales" ? 1 : "",
        r: common_vendor.o(($event) => formData.upgradeType = "sales"),
        s: formData.upgradeType === "orders" ? 1 : "",
        t: common_vendor.o(($event) => formData.upgradeType = "orders"),
        v: `请输入最低${formData.upgradeType === "sales" ? "销售额" : "订单数"}`,
        w: formData.upgradeValue,
        x: common_vendor.o(($event) => formData.upgradeValue = $event.detail.value),
        y: formData.commissionRates.level1,
        z: common_vendor.o(($event) => formData.commissionRates.level1 = $event.detail.value),
        A: formData.commissionRates.level2,
        B: common_vendor.o(($event) => formData.commissionRates.level2 = $event.detail.value),
        C: formData.benefits,
        D: common_vendor.o(($event) => formData.benefits = $event.detail.value),
        E: common_vendor.o(closeLevelModal),
        F: !canSubmit.value,
        G: !canSubmit.value ? 1 : "",
        H: common_vendor.o(submitLevel)
      } : {}, {
        I: settings.enableLevels
      }, settings.enableLevels ? {
        J: common_vendor.o(saveSettings)
      } : {});
    };
  }
};
wx.createPage(_sfc_main);
//# sourceMappingURL=../../../../../../.sourcemap/mp-weixin/subPackages/merchant-admin-marketing/pages/marketing/distribution/levels.js.map
