"use strict";
const common_vendor = require("../../../../../common/vendor.js");
const common_assets = require("../../../../../common/assets.js");
const _sfc_main = {
  data() {
    return {};
  },
  methods: {
    goBack() {
      common_vendor.index.navigateBack();
    },
    shareGuide() {
      common_vendor.index.showActionSheet({
        itemList: ["分享给好友", "分享到朋友圈", "复制链接"],
        success: function(res) {
          common_vendor.index.showToast({
            title: "分享成功",
            icon: "success"
          });
        }
      });
    },
    contactService() {
      common_vendor.index.makePhoneCall({
        phoneNumber: "************"
      });
    }
  }
};
if (!Array) {
  const _component_circle = common_vendor.resolveComponent("circle");
  const _component_line = common_vendor.resolveComponent("line");
  const _component_svg = common_vendor.resolveComponent("svg");
  const _component_path = common_vendor.resolveComponent("path");
  const _component_rect = common_vendor.resolveComponent("rect");
  const _component_polyline = common_vendor.resolveComponent("polyline");
  (_component_circle + _component_line + _component_svg + _component_path + _component_rect + _component_polyline)();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {
    a: common_vendor.o((...args) => $options.goBack && $options.goBack(...args)),
    b: common_vendor.p({
      cx: "18",
      cy: "5",
      r: "3"
    }),
    c: common_vendor.p({
      cx: "6",
      cy: "12",
      r: "3"
    }),
    d: common_vendor.p({
      cx: "18",
      cy: "19",
      r: "3"
    }),
    e: common_vendor.p({
      x1: "8.59",
      y1: "13.51",
      x2: "15.42",
      y2: "17.49"
    }),
    f: common_vendor.p({
      x1: "15.41",
      y1: "6.51",
      x2: "8.59",
      y2: "10.49"
    }),
    g: common_vendor.p({
      xmlns: "http://www.w3.org/2000/svg",
      width: "20",
      height: "20",
      viewBox: "0 0 24 24",
      fill: "none",
      stroke: "currentColor",
      ["stroke-width"]: "2",
      ["stroke-linecap"]: "round",
      ["stroke-linejoin"]: "round"
    }),
    h: common_vendor.o((...args) => $options.shareGuide && $options.shareGuide(...args)),
    i: common_vendor.p({
      d: "M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"
    }),
    j: common_vendor.p({
      cx: "9",
      cy: "7",
      r: "4"
    }),
    k: common_vendor.p({
      d: "M23 21v-2a4 4 0 0 0-3-3.87"
    }),
    l: common_vendor.p({
      d: "M16 3.13a4 4 0 0 1 0 7.75"
    }),
    m: common_vendor.p({
      xmlns: "http://www.w3.org/2000/svg",
      width: "20",
      height: "20",
      viewBox: "0 0 24 24",
      fill: "none",
      stroke: "#9708CC",
      ["stroke-width"]: "2",
      ["stroke-linecap"]: "round",
      ["stroke-linejoin"]: "round"
    }),
    n: common_assets._imports_0$41,
    o: common_vendor.p({
      d: "M22 12h-4l-3 9L9 3l-3 9H2"
    }),
    p: common_vendor.p({
      xmlns: "http://www.w3.org/2000/svg",
      width: "20",
      height: "20",
      viewBox: "0 0 24 24",
      fill: "none",
      stroke: "#9708CC",
      ["stroke-width"]: "2",
      ["stroke-linecap"]: "round",
      ["stroke-linejoin"]: "round"
    }),
    q: common_vendor.p({
      d: "M12 2L2 7l10 5 10-5-10-5z"
    }),
    r: common_vendor.p({
      d: "M2 17l10 5 10-5"
    }),
    s: common_vendor.p({
      d: "M2 12l10 5 10-5"
    }),
    t: common_vendor.p({
      xmlns: "http://www.w3.org/2000/svg",
      width: "20",
      height: "20",
      viewBox: "0 0 24 24",
      fill: "none",
      stroke: "#9708CC",
      ["stroke-width"]: "2",
      ["stroke-linecap"]: "round",
      ["stroke-linejoin"]: "round"
    }),
    v: common_vendor.p({
      d: "M6 9H4.5a2.5 2.5 0 0 1 0-5H6"
    }),
    w: common_vendor.p({
      d: "M18 9h1.5a2.5 2.5 0 0 0 0-5H18"
    }),
    x: common_vendor.p({
      d: "M4 22h16"
    }),
    y: common_vendor.p({
      d: "M10 14.66V17c0 .55-.47.98-.97 1.21C7.85 18.75 7 20.24 7 22"
    }),
    z: common_vendor.p({
      d: "M14 14.66V17c0 .55.47.98.97 1.21C16.15 18.75 17 20.24 17 22"
    }),
    A: common_vendor.p({
      d: "M18 2H6v7a6 6 0 0 0 12 0V2Z"
    }),
    B: common_vendor.p({
      xmlns: "http://www.w3.org/2000/svg",
      width: "20",
      height: "20",
      viewBox: "0 0 24 24",
      fill: "none",
      stroke: "#9708CC",
      ["stroke-width"]: "2",
      ["stroke-linecap"]: "round",
      ["stroke-linejoin"]: "round"
    }),
    C: common_vendor.p({
      cx: "12",
      cy: "12",
      r: "10"
    }),
    D: common_vendor.p({
      x1: "12",
      y1: "8",
      x2: "12",
      y2: "16"
    }),
    E: common_vendor.p({
      x1: "8",
      y1: "12",
      x2: "16",
      y2: "12"
    }),
    F: common_vendor.p({
      xmlns: "http://www.w3.org/2000/svg",
      width: "20",
      height: "20",
      viewBox: "0 0 24 24",
      fill: "none",
      stroke: "#9708CC",
      ["stroke-width"]: "2",
      ["stroke-linecap"]: "round",
      ["stroke-linejoin"]: "round"
    }),
    G: common_vendor.p({
      d: "M8 3H5a2 2 0 0 0-2 2v3"
    }),
    H: common_vendor.p({
      d: "M21 8V5a2 2 0 0 0-2-2h-3"
    }),
    I: common_vendor.p({
      d: "M3 16v3a2 2 0 0 0 2 2h3"
    }),
    J: common_vendor.p({
      d: "M16 21h3a2 2 0 0 0 2-2v-3"
    }),
    K: common_vendor.p({
      xmlns: "http://www.w3.org/2000/svg",
      width: "20",
      height: "20",
      viewBox: "0 0 24 24",
      fill: "none",
      stroke: "#9708CC",
      ["stroke-width"]: "2",
      ["stroke-linecap"]: "round",
      ["stroke-linejoin"]: "round"
    }),
    L: common_vendor.p({
      d: "M12 20h9"
    }),
    M: common_vendor.p({
      d: "M16.5 3.5a2.121 2.121 0 0 1 3 3L7 19l-4 1 1-4L16.5 3.5z"
    }),
    N: common_vendor.p({
      xmlns: "http://www.w3.org/2000/svg",
      width: "20",
      height: "20",
      viewBox: "0 0 24 24",
      fill: "none",
      stroke: "#9708CC",
      ["stroke-width"]: "2",
      ["stroke-linecap"]: "round",
      ["stroke-linejoin"]: "round"
    }),
    O: common_vendor.p({
      cx: "12",
      cy: "12",
      r: "10"
    }),
    P: common_vendor.p({
      d: "M12 8v4"
    }),
    Q: common_vendor.p({
      d: "M12 16h.01"
    }),
    R: common_vendor.p({
      xmlns: "http://www.w3.org/2000/svg",
      width: "16",
      height: "16",
      viewBox: "0 0 24 24",
      fill: "none",
      stroke: "#9708CC",
      ["stroke-width"]: "2",
      ["stroke-linecap"]: "round",
      ["stroke-linejoin"]: "round"
    }),
    S: common_vendor.p({
      d: "M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"
    }),
    T: common_vendor.p({
      cx: "9",
      cy: "7",
      r: "4"
    }),
    U: common_vendor.p({
      d: "M23 21v-2a4 4 0 0 0-3-3.87"
    }),
    V: common_vendor.p({
      d: "M16 3.13a4 4 0 0 1 0 7.75"
    }),
    W: common_vendor.p({
      xmlns: "http://www.w3.org/2000/svg",
      width: "16",
      height: "16",
      viewBox: "0 0 24 24",
      fill: "none",
      stroke: "#9708CC",
      ["stroke-width"]: "2",
      ["stroke-linecap"]: "round",
      ["stroke-linejoin"]: "round"
    }),
    X: common_vendor.p({
      x: "3",
      y: "3",
      width: "18",
      height: "18",
      rx: "2",
      ry: "2"
    }),
    Y: common_vendor.p({
      cx: "8.5",
      cy: "8.5",
      r: "1.5"
    }),
    Z: common_vendor.p({
      points: "21 15 16 10 5 21"
    }),
    aa: common_vendor.p({
      xmlns: "http://www.w3.org/2000/svg",
      width: "16",
      height: "16",
      viewBox: "0 0 24 24",
      fill: "none",
      stroke: "#9708CC",
      ["stroke-width"]: "2",
      ["stroke-linecap"]: "round",
      ["stroke-linejoin"]: "round"
    }),
    ab: common_vendor.p({
      d: "M22 11.08V12a10 10 0 1 1-5.93-9.14"
    }),
    ac: common_vendor.p({
      points: "22 4 12 14.01 9 11.01"
    }),
    ad: common_vendor.p({
      xmlns: "http://www.w3.org/2000/svg",
      width: "16",
      height: "16",
      viewBox: "0 0 24 24",
      fill: "none",
      stroke: "#9708CC",
      ["stroke-width"]: "2",
      ["stroke-linecap"]: "round",
      ["stroke-linejoin"]: "round"
    }),
    ae: common_vendor.p({
      d: "M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"
    }),
    af: common_vendor.p({
      points: "14 2 14 8 20 8"
    }),
    ag: common_vendor.p({
      x1: "16",
      y1: "13",
      x2: "8",
      y2: "13"
    }),
    ah: common_vendor.p({
      x1: "16",
      y1: "17",
      x2: "8",
      y2: "17"
    }),
    ai: common_vendor.p({
      points: "10 9 9 9 8 9"
    }),
    aj: common_vendor.p({
      xmlns: "http://www.w3.org/2000/svg",
      width: "20",
      height: "20",
      viewBox: "0 0 24 24",
      fill: "none",
      stroke: "#9708CC",
      ["stroke-width"]: "2",
      ["stroke-linecap"]: "round",
      ["stroke-linejoin"]: "round"
    }),
    ak: common_vendor.p({
      d: "M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"
    }),
    al: common_vendor.p({
      points: "14 2 14 8 20 8"
    }),
    am: common_vendor.p({
      x1: "16",
      y1: "13",
      x2: "8",
      y2: "13"
    }),
    an: common_vendor.p({
      x1: "16",
      y1: "17",
      x2: "8",
      y2: "17"
    }),
    ao: common_vendor.p({
      points: "10 9 9 9 8 9"
    }),
    ap: common_vendor.p({
      xmlns: "http://www.w3.org/2000/svg",
      width: "20",
      height: "20",
      viewBox: "0 0 24 24",
      fill: "none",
      stroke: "#9708CC",
      ["stroke-width"]: "2",
      ["stroke-linecap"]: "round",
      ["stroke-linejoin"]: "round"
    }),
    aq: common_vendor.o((...args) => $options.contactService && $options.contactService(...args))
  };
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-3fe4a351"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../../../.sourcemap/mp-weixin/subPackages/merchant-admin-marketing/pages/marketing/redpacket/fission-redpacket-guide.js.map
