<template>
  <view class="info-list">
    <!-- 分类标签栏 -->
    <category-tabs 
      :categories="categories" 
      :active-category="activeCategory" 
      @category-change="handleCategoryChange"
      @open-filter="handleOpenFilter"
    />
    
    <!-- 内容区域，添加顶部间距 -->
    <view class="content-area">
      <view class="list-header">
        <text class="list-title">同城信息</text>
        <view class="list-actions">
          <view class="action-btn" @click="handleOpenFilter">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <polygon points="22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3"></polygon>
            </svg>
            <text>筛选</text>
          </view>
          <view class="action-btn" @click="handleOpenSort">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <line x1="4" y1="21" x2="4" y2="14"></line>
              <line x1="4" y1="10" x2="4" y2="3"></line>
              <line x1="12" y1="21" x2="12" y2="12"></line>
              <line x1="12" y1="8" x2="12" y2="3"></line>
              <line x1="20" y1="21" x2="20" y2="16"></line>
              <line x1="20" y1="12" x2="20" y2="3"></line>
              <line x1="1" y1="14" x2="7" y2="14"></line>
              <line x1="9" y1="8" x2="15" y2="8"></line>
              <line x1="17" y1="16" x2="23" y2="16"></line>
            </svg>
            <text>排序</text>
          </view>
        </view>
      </view>
      
      <!-- 信息列表 -->
      <view class="info-items">
        <block v-for="(item, index) in filteredInfoList" :key="item.id">
          <info-card-factory :item="item" @click="handleItemClick(item)" />
        </block>
      </view>
      
      <!-- 加载更多 -->
      <view class="load-more" v-if="hasMore">
        <text class="loading-text">{{loading ? '加载中...' : '上拉加载更多'}}</text>
      </view>
      
      <!-- 没有更多数据 -->
      <view class="no-more" v-else>
        <text class="no-more-text">没有更多数据了</text>
      </view>
    </view>
    
    <!-- 悬浮发布按钮 -->
    <view class="publish-btn" @click="handlePublish">
      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
        <line x1="12" y1="5" x2="12" y2="19"></line>
        <line x1="5" y1="12" x2="19" y2="12"></line>
      </svg>
      <text>发布</text>
    </view>
  </view>
</template>

<script>
import InfoCardFactory from './cards/InfoCardFactory.vue';
import CategoryTabs from './CategoryTabs.vue';
import { infoListData } from '../data/infoListData.js';

export default {
  name: 'InfoList',
  components: {
    InfoCardFactory,
    CategoryTabs
  },
  data() {
    return {
      infoList: [],
      page: 1,
      pageSize: 10,
      loading: false,
      hasMore: true,
      activeCategory: 'all',
      categories: [
        { label: '全部', value: 'all', icon: 'all' },
        { label: '招聘信息', value: '招聘信息', icon: 'job' },
        { label: '求职信息', value: '求职信息', icon: 'job' },
        { label: '房屋出租', value: '房屋出租', icon: 'house' },
        { label: '房屋出售', value: '房屋出售', icon: 'house' },
        { label: '二手闲置', value: '二手闲置', icon: 'secondhand' },
        { label: '二手车辆', value: '二手车辆', icon: 'secondhand' },
        { label: '到家服务', value: '到家服务', icon: 'service' },
        { label: '寻找服务', value: '寻找服务', icon: 'service' },
        { label: '生意转让', value: '生意转让', icon: 'business' },
        { label: '宠物信息', value: '宠物信息', icon: 'pet' },
        { label: '商家活动', value: '商家活动', icon: 'merchant' },
        { label: '婚恋交友', value: '婚恋交友', icon: 'dating' },
        { label: '车辆服务', value: '车辆服务', icon: 'car' },
        { label: '磁州拼车', value: '磁州拼车', icon: 'carpool' },
        { label: '教育培训', value: '教育培训', icon: 'education' },
        { label: '其他服务', value: '其他服务', icon: 'other' }
      ]
    };
  },
  computed: {
    filteredInfoList() {
      if (this.activeCategory === 'all') {
        return this.infoList;
      } else {
        return this.infoList.filter(item => item.category === this.activeCategory);
      }
    }
  },
  created() {
    this.loadData();
  },
  methods: {
    loadData() {
      this.loading = true;
      
      // 模拟异步加载数据
      setTimeout(() => {
        // 使用示例数据
        const newData = infoListData;
        
        if (this.page === 1) {
          this.infoList = newData;
        } else {
          // 如果是加载更多，则追加数据
          this.infoList = [...this.infoList, ...newData];
        }
        
        // 模拟没有更多数据的情况
        if (this.page >= 2) {
          this.hasMore = false;
        }
        
        this.loading = false;
        this.page++;
      }, 500);
    },
    handleLoadMore() {
      if (this.loading || !this.hasMore) return;
      this.loadData();
    },
    handleItemClick(item) {
      // 处理点击信息项
      console.log('点击了信息项:', item);
      // 可以跳转到详情页或执行其他操作
    },
    handlePublish() {
      // 处理发布按钮点击
      console.log('点击了发布按钮');
      // 可以跳转到发布页面
    },
    handleCategoryChange(category) {
      this.activeCategory = category;
      // 切换分类后可以重新加载数据
      this.page = 1;
      this.hasMore = true;
      this.loadData();
    },
    handleOpenFilter() {
      console.log('打开筛选');
      // 实现筛选功能
    },
    handleOpenSort() {
      console.log('打开排序');
      // 实现排序功能
    }
  },
  // 监听页面滚动到底部，加载更多数据
  onReachBottom() {
    this.handleLoadMore();
  }
}
</script>

<style scoped>
.info-list {
  position: relative;
}

.content-area {
  padding: 20rpx;
  margin-top: 112rpx; /* 为吸顶的分类标签栏预留空间 */
}

.list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.list-title {
  font-size: 36rpx;
  font-weight: 700;
  color: #1c1c1e;
  font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;
}

.list-actions {
  display: flex;
  gap: 16rpx;
}

.action-btn {
  display: flex;
  align-items: center;
  background: rgba(0, 0, 0, 0.05);
  padding: 8rpx 16rpx;
  border-radius: 16rpx;
  color: #636366;
}

.action-btn svg {
  margin-right: 6rpx;
}

.action-btn text {
  font-size: 24rpx;
}

.info-items {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.load-more, .no-more {
  text-align: center;
  padding: 30rpx 0;
}

.loading-text, .no-more-text {
  font-size: 26rpx;
  color: #8e8e93;
}

.publish-btn {
  position: fixed;
  right: 30rpx;
  bottom: 100rpx;
  width: 120rpx;
  height: 120rpx;
  background: linear-gradient(135deg, #007AFF, #5856D6);
  border-radius: 50%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #FFFFFF;
  box-shadow: 0 6rpx 16rpx rgba(0, 122, 255, 0.3);
  z-index: 100;
}

.publish-btn svg {
  margin-bottom: 4rpx;
}

.publish-btn text {
  font-size: 24rpx;
  font-weight: 500;
}
</style> 