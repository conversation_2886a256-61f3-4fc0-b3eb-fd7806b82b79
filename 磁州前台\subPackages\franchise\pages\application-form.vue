<template>
  <view class="application-container">
    <!-- 自定义导航栏 -->
    <cu-custom bgColor="bg-gradient-blue" isBack>
      <template #backText>返回</template>
      <template #content>
        <text class="text-center text-white text-bold" style="font-weight: 700; font-size: 36rpx;">区域加盟申请</text>
      </template>
    </cu-custom>
    
    <!-- 表单内容 -->
    <view class="form-wrapper">
      <!-- 申请区域 -->
      <view class="form-section">
        <view class="section-header">
          <text class="section-title">申请区域</text>
        </view>
        <view class="section-content">
          <view class="form-item">
            <view class="item-label required">区域</view>
            <view class="item-content">
              <text class="selected-region">{{ selectedRegion }}</text>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 基本信息 -->
      <view class="form-section">
        <view class="section-header">
          <text class="section-title">基本信息</text>
        </view>
        <view class="section-content">
          <view class="form-item">
            <view class="item-label required">姓名</view>
            <view class="item-content">
              <input 
                class="form-input" 
                type="text" 
                placeholder="请输入您的真实姓名" 
                v-model="formData.name" 
                @input="validateField('name')"
              />
            </view>
            <view class="error-message" v-if="errors.name">{{ errors.name }}</view>
          </view>
          
          <view class="form-item">
            <view class="item-label required">手机号码</view>
            <view class="item-content">
              <input 
                class="form-input" 
                type="number" 
                placeholder="请输入您的手机号码" 
                v-model="formData.phone" 
                maxlength="11"
                @input="validateField('phone')"
              />
            </view>
            <view class="error-message" v-if="errors.phone">{{ errors.phone }}</view>
          </view>
          
          <view class="form-item">
            <view class="item-label required">微信号</view>
            <view class="item-content">
              <input 
                class="form-input" 
                type="text" 
                placeholder="请输入您的微信号码" 
                v-model="formData.wechat"
                @input="validateField('wechat')"
              />
            </view>
            <view class="error-message" v-if="errors.wechat">{{ errors.wechat }}</view>
          </view>
        </view>
      </view>
      
      <!-- 公司信息 -->
      <view class="form-section">
        <view class="section-header">
          <text class="section-title">公司信息</text>
          <text class="section-subtitle">(个人申请可填写个体工商户信息)</text>
        </view>
        <view class="section-content">
          <view class="form-item">
            <view class="item-label required">公司名称</view>
            <view class="item-content">
              <input 
                class="form-input" 
                type="text" 
                placeholder="请输入公司/个体工商户名称" 
                v-model="formData.companyName"
                @input="validateField('companyName')"
              />
            </view>
            <view class="error-message" v-if="errors.companyName">{{ errors.companyName }}</view>
          </view>
          
          <view class="form-item">
            <view class="item-label required">营业执照</view>
            <view class="item-content">
              <view class="upload-box" @tap="chooseBusinessLicense">
                <image v-if="formData.businessLicense" :src="formData.businessLicense" mode="aspectFill" class="license-image"></image>
                <view v-else class="upload-placeholder">
                  <text class="cuIcon-camerafill"></text>
                  <text class="upload-text">上传营业执照照片</text>
                </view>
              </view>
            </view>
            <view class="error-message" v-if="errors.businessLicense">{{ errors.businessLicense }}</view>
          </view>
          
          <view class="form-item">
            <view class="item-label">公司地址</view>
            <view class="item-content">
              <input 
                class="form-input" 
                type="text" 
                placeholder="请输入公司详细地址" 
                v-model="formData.companyAddress"
              />
            </view>
          </view>
        </view>
      </view>
      
      <!-- 运营能力 -->
      <view class="form-section">
        <view class="section-header">
          <text class="section-title">运营能力</text>
        </view>
        <view class="section-content">
          <view class="form-item">
            <view class="item-label required">运营人数</view>
            <view class="item-content">
              <picker 
                mode="selector" 
                :range="staffOptions" 
                @change="staffChange" 
                class="selector-picker"
              >
                <view class="picker-value">
                  <text>{{ formData.staffCount || '请选择运营团队人数' }}</text>
                  <text class="cuIcon-unfold"></text>
                </view>
              </picker>
            </view>
            <view class="error-message" v-if="errors.staffCount">{{ errors.staffCount }}</view>
          </view>
          
          <view class="form-item">
            <view class="item-label required">本地资源</view>
            <view class="item-content">
              <view class="checkbox-group">
                <view 
                  class="checkbox-item" 
                  v-for="(item, index) in resourceOptions" 
                  :key="index"
                  @tap="toggleResource(item.value)"
                  :class="{'checked': formData.resources.includes(item.value)}"
                >
                  <text class="checkbox-icon" :class="formData.resources.includes(item.value) ? 'cuIcon-squarecheckfill text-blue' : 'cuIcon-square'"></text>
                  <text class="checkbox-label">{{ item.label }}</text>
                </view>
              </view>
            </view>
            <view class="error-message" v-if="errors.resources">{{ errors.resources }}</view>
          </view>
          
          <view class="form-item">
            <view class="item-label">已有商家资源</view>
            <view class="item-content">
              <textarea 
                class="form-textarea" 
                placeholder="请描述您现有的商家资源情况（选填）" 
                v-model="formData.merchantResources"
                maxlength="200"
              ></textarea>
              <view class="textarea-counter">{{ formData.merchantResources.length }}/200</view>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 补充信息 -->
      <view class="form-section">
        <view class="section-header">
          <text class="section-title">补充信息</text>
        </view>
        <view class="section-content">
          <view class="form-item">
            <view class="item-label">加盟优势</view>
            <view class="item-content">
              <textarea 
                class="form-textarea" 
                placeholder="请描述您的加盟优势（选填）" 
                v-model="formData.advantages"
                maxlength="500"
              ></textarea>
              <view class="textarea-counter">{{ formData.advantages.length }}/500</view>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 协议同意 -->
      <view class="agreement-box">
        <view class="agreement-checkbox" @tap="toggleAgreement">
          <text class="checkbox-icon" :class="formData.agreement ? 'cuIcon-squarecheckfill text-blue' : 'cuIcon-square'"></text>
        </view>
        <view class="agreement-text">
          <text>我已阅读并同意</text>
          <text class="agreement-link" @tap="showAgreement">《区域加盟合作协议》</text>
        </view>
      </view>
      <view class="error-message centered" v-if="errors.agreement">{{ errors.agreement }}</view>
      
      <!-- 提交按钮 -->
      <view class="submit-btn-wrapper">
        <button class="submit-btn" @tap="submitApplication" :disabled="submitting">
          <text v-if="!submitting">提交申请</text>
          <view v-else class="loading-icon"></view>
        </button>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';

// 响应式状态
const selectedRegion = ref('');
const formData = reactive({
  name: '',
  phone: '',
  wechat: '',
  companyName: '',
  businessLicense: '',
  companyAddress: '',
  staffCount: '',
  resources: [],
  merchantResources: '',
  advantages: '',
  agreement: false
});
const errors = reactive({});
const submitting = ref(false);

// 选项数据
const staffOptions = ['1-3人', '4-10人', '11-20人', '20人以上'];
const resourceOptions = [
  { label: '本地商家资源', value: 'merchant' },
  { label: '媒体资源', value: 'media' },
  { label: '政府资源', value: 'government' },
  { label: '协会/商会资源', value: 'association' },
  { label: '高校/培训机构资源', value: 'education' }
];

// 生命周期钩子
onMounted(() => {
  // 获取页面参数
  const pages = getCurrentPages();
  const currentPage = pages[pages.length - 1];
  const options = currentPage.options || {};
  
  // 从URL参数获取区域信息
  if (options.region) {
    selectedRegion.value = decodeURIComponent(options.region);
  }
});

// 表单字段验证
const validateField = (field) => {
  switch (field) {
    case 'name':
      errors.name = formData.name ? '' : '请输入姓名';
      break;
    case 'phone':
      if (!formData.phone) {
        errors.phone = '请输入手机号码';
      } else if (!/^1[3-9]\d{9}$/.test(formData.phone)) {
        errors.phone = '请输入正确的手机号码';
      } else {
        errors.phone = '';
      }
      break;
    case 'wechat':
      errors.wechat = formData.wechat ? '' : '请输入微信号';
      break;
    case 'companyName':
      errors.companyName = formData.companyName ? '' : '请输入公司名称';
      break;
    case 'businessLicense':
      errors.businessLicense = formData.businessLicense ? '' : '请上传营业执照';
      break;
    case 'staffCount':
      errors.staffCount = formData.staffCount ? '' : '请选择运营人数';
      break;
    case 'resources':
      errors.resources = formData.resources.length > 0 ? '' : '请至少选择一项本地资源';
      break;
    case 'agreement':
      errors.agreement = formData.agreement ? '' : '请同意协议后提交';
      break;
  }
};

// 验证所有字段
const validateForm = () => {
  validateField('name');
  validateField('phone');
  validateField('wechat');
  validateField('companyName');
  validateField('businessLicense');
  validateField('staffCount');
  validateField('resources');
  validateField('agreement');
  
  // 检查是否有错误
  for (const key in errors) {
    if (errors[key]) {
      return false;
    }
  }
  return true;
};

// 选择运营人数
const staffChange = (e) => {
  const index = e.detail.value;
  formData.staffCount = staffOptions[index];
  validateField('staffCount');
};

// 切换资源选择
const toggleResource = (value) => {
  const index = formData.resources.indexOf(value);
  if (index === -1) {
    formData.resources.push(value);
  } else {
    formData.resources.splice(index, 1);
  }
  validateField('resources');
};

// 选择营业执照图片
const chooseBusinessLicense = () => {
  uni.chooseImage({
    count: 1,
    sizeType: ['compressed'],
    sourceType: ['album', 'camera'],
    success: (res) => {
      formData.businessLicense = res.tempFilePaths[0];
      validateField('businessLicense');
    }
  });
};

// 切换协议同意状态
const toggleAgreement = () => {
  formData.agreement = !formData.agreement;
  validateField('agreement');
};

// 显示协议内容
const showAgreement = () => {
  uni.navigateTo({
    url: '/subPackages/franchise/pages/agreement'
  });
};

// 提交申请
const submitApplication = () => {
  if (submitting.value) return;
  
  if (!validateForm()) {
    uni.showToast({
      title: '请完善表单信息',
      icon: 'none'
    });
    return;
  }
  
  submitting.value = true;
  
  // 模拟API请求，实际应调用服务器接口
  setTimeout(() => {
    submitting.value = false;
    
    uni.showModal({
      title: '申请提交成功',
      content: '您的区域加盟申请已提交，我们将在3个工作日内审核并联系您。',
      showCancel: false,
      success: () => {
        uni.navigateBack();
      }
    });
  }, 1500);
};
</script>

<style lang="scss" scoped>
.application-container {
  min-height: 100vh;
  background-color: #F5F7FA;
  padding-bottom: 40rpx;
}

.form-wrapper {
  padding: 30rpx;
}

.form-section {
  background-color: #FFFFFF;
  border-radius: 20rpx;
  margin-bottom: 30rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
}

.section-header {
  padding: 30rpx;
  border-bottom: 1px solid #F0F0F0;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  position: relative;
  padding-left: 20rpx;
  
  &::before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 6rpx;
    height: 30rpx;
    background-color: #1677FF;
    border-radius: 3rpx;
  }
}

.section-subtitle {
  font-size: 24rpx;
  color: #999999;
  margin-left: 20rpx;
}

.section-content {
  padding: 20rpx 30rpx;
}

.form-item {
  margin-bottom: 30rpx;
  
  &:last-child {
    margin-bottom: 0;
  }
}

.item-label {
  font-size: 28rpx;
  color: #333333;
  margin-bottom: 15rpx;
  
  &.required::before {
    content: '*';
    color: #FF3B30;
    margin-right: 6rpx;
  }
}

.form-input {
  height: 90rpx;
  line-height: 90rpx;
  padding: 0 20rpx;
  background-color: #F9FCFF;
  border-radius: 8rpx;
  border: 1px solid #E5E5E5;
  font-size: 28rpx;
  width: 100%;
  box-sizing: border-box;
}

.form-textarea {
  width: 100%;
  height: 200rpx;
  padding: 20rpx;
  background-color: #F9FCFF;
  border-radius: 8rpx;
  border: 1px solid #E5E5E5;
  font-size: 28rpx;
  box-sizing: border-box;
}

.textarea-counter {
  text-align: right;
  font-size: 24rpx;
  color: #999999;
  margin-top: 10rpx;
}

.selected-region {
  font-size: 28rpx;
  color: #1677FF;
  font-weight: 500;
  background-color: rgba(22, 119, 255, 0.05);
  padding: 15rpx 20rpx;
  border-radius: 8rpx;
  display: inline-block;
}

.upload-box {
  width: 280rpx;
  height: 400rpx;
  border: 1px dashed #CCCCCC;
  border-radius: 8rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.upload-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  
  .cuIcon-camerafill {
    font-size: 60rpx;
    color: #CCCCCC;
    margin-bottom: 15rpx;
  }
  
  .upload-text {
    font-size: 24rpx;
    color: #999999;
  }
}

.license-image {
  width: 100%;
  height: 100%;
}

.selector-picker {
  width: 100%;
}

.picker-value {
  height: 90rpx;
  line-height: 90rpx;
  padding: 0 20rpx;
  background-color: #F9FCFF;
  border-radius: 8rpx;
  border: 1px solid #E5E5E5;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 28rpx;
  color: #333333;
}

.checkbox-group {
  display: flex;
  flex-direction: column;
}

.checkbox-item {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
  
  &:last-child {
    margin-bottom: 0;
  }
  
  &.checked {
    .checkbox-label {
      color: #1677FF;
    }
  }
}

.checkbox-icon {
  font-size: 40rpx;
  margin-right: 15rpx;
}

.checkbox-label {
  font-size: 28rpx;
  color: #333333;
}

.agreement-box {
  display: flex;
  align-items: center;
  margin: 40rpx 0 20rpx;
  padding: 0 10rpx;
}

.agreement-checkbox {
  margin-right: 15rpx;
  
  .checkbox-icon {
    font-size: 40rpx;
  }
}

.agreement-text {
  font-size: 26rpx;
  color: #666666;
}

.agreement-link {
  color: #1677FF;
}

.error-message {
  font-size: 24rpx;
  color: #FF3B30;
  margin-top: 10rpx;
  
  &.centered {
    text-align: center;
  }
}

.submit-btn-wrapper {
  margin: 60rpx 0 40rpx;
  padding: 0 30rpx;
}

.submit-btn {
  width: 100%;
  height: 90rpx;
  line-height: 90rpx;
  background: linear-gradient(90deg, #1677FF, #4F9DFF);
  color: #FFFFFF;
  font-size: 32rpx;
  font-weight: 500;
  border-radius: 45rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  
  &[disabled] {
    background: linear-gradient(90deg, #CCCCCC, #E5E5E5);
    color: #FFFFFF;
    opacity: 1;
  }
}

.loading-icon {
  width: 40rpx;
  height: 40rpx;
  border: 4rpx solid #FFFFFF;
  border-radius: 50%;
  border-top-color: transparent;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
</style>