<view class="points-management-container"><view class="navbar"><view class="navbar-left"><view class="back-button" bindtap="{{c}}"><svg wx:if="{{b}}" u-s="{{['d']}}" u-i="6796c6eb-0" bind:__l="__l" u-p="{{b}}"><path wx:if="{{a}}" u-i="6796c6eb-1,6796c6eb-0" bind:__l="__l" u-p="{{a}}"/></svg></view></view><view class="navbar-title"><text class="title-text">积分商城管理</text></view><view class="navbar-right"></view></view><view class="top-actions"><create-button wx:if="{{e}}" bindclick="{{d}}" u-i="6796c6eb-2" bind:__l="__l" u-p="{{e}}"/></view><scroll-view scroll-y class="content-area"><view class="overview-section"><view class="overview-header"><text class="section-title">积分概览</text><view class="date-picker"><text class="date-text">近30天</text><view class="arrow-icon"></view></view></view><view class="overview-cards"><view class="overview-card"><view class="card-value">{{f}}</view><view class="card-label">累计发放积分</view></view><view class="overview-card"><view class="card-value">{{g}}</view><view class="card-label">已使用积分</view></view><view class="overview-card"><view class="card-value">{{h}}</view><view class="card-label">活跃用户数</view></view><view class="overview-card"><view class="card-value">{{i}}%</view><view class="card-label">兑换转化率</view></view></view></view><view class="points-items-section"><view class="section-header"><text class="section-title">积分商品</text><view class="filter-buttons"><view class="{{['filter-button', j && 'active']}}" bindtap="{{k}}">全部</view><view class="{{['filter-button', l && 'active']}}" bindtap="{{m}}">进行中</view><view class="{{['filter-button', n && 'active']}}" bindtap="{{o}}">草稿</view><view class="{{['filter-button', p && 'active']}}" bindtap="{{q}}">已结束</view></view></view><view class="points-items-list"><view wx:for="{{r}}" wx:for-item="item" wx:key="r" class="points-item" bindtap="{{item.s}}"><view class="item-image-container"><image class="item-image" src="{{item.a}}" mode="aspectFill"></image><view class="{{['item-status', item.c]}}">{{item.b}}</view></view><view class="item-content"><view class="item-name">{{item.d}}</view><view class="item-points"><text class="points-value">{{item.e}}</text><text class="points-label">积分</text></view><view class="item-stats"><view class="stat-item"><text class="stat-value">{{item.f}}</text><text class="stat-label">库存</text></view><view class="stat-item"><text class="stat-value">{{item.g}}</text><text class="stat-label">已兑换</text></view><view class="stat-item"><text class="stat-value">{{item.h}}</text><text class="stat-label">浏览</text></view></view><view class="item-time">{{item.i}}</view></view><view class="item-actions"><view class="action-button edit" catchtap="{{item.m}}"><svg wx:if="{{v}}" u-s="{{['d']}}" u-i="{{item.l}}" bind:__l="__l" u-p="{{v}}"><path wx:if="{{s}}" u-i="{{item.j}}" bind:__l="__l" u-p="{{s}}"/><path wx:if="{{t}}" u-i="{{item.k}}" bind:__l="__l" u-p="{{t}}"/></svg></view><view class="action-button delete" catchtap="{{item.q}}"><svg wx:if="{{y}}" u-s="{{['d']}}" u-i="{{item.p}}" bind:__l="__l" u-p="{{y}}"><path wx:if="{{w}}" u-i="{{item.n}}" bind:__l="__l" u-p="{{w}}"/><path wx:if="{{x}}" u-i="{{item.o}}" bind:__l="__l" u-p="{{x}}"/></svg></view></view></view></view><view wx:if="{{z}}" class="empty-state"><image class="empty-image" src="{{A}}" mode="aspectFit"></image><text class="empty-text">暂无{{B}}积分商品</text></view></view><view class="points-rules-section"><view class="section-header"><text class="section-title">积分规则设置</text><view class="edit-button" bindtap="{{F}}"><svg wx:if="{{E}}" u-s="{{['d']}}" u-i="6796c6eb-9" bind:__l="__l" u-p="{{E}}"><path wx:if="{{C}}" u-i="6796c6eb-10,6796c6eb-9" bind:__l="__l" u-p="{{C}}"/><path wx:if="{{D}}" u-i="6796c6eb-11,6796c6eb-9" bind:__l="__l" u-p="{{D}}"/></svg><text>编辑</text></view></view><view class="rules-list"><view wx:for="{{G}}" wx:for-item="rule" wx:key="I" class="rule-item"><view class="{{['rule-icon', rule.E]}}"><svg wx:if="{{rule.a}}" u-s="{{['d']}}" u-i="{{rule.h}}" bind:__l="__l" u-p="{{rule.i}}"><path wx:if="{{rule.c}}" u-i="{{rule.b}}" bind:__l="__l" u-p="{{rule.c}}"/><path wx:if="{{rule.e}}" u-i="{{rule.d}}" bind:__l="__l" u-p="{{rule.e}}"/><path wx:if="{{rule.g}}" u-i="{{rule.f}}" bind:__l="__l" u-p="{{rule.g}}"/></svg><svg wx:if="{{rule.j}}" u-s="{{['d']}}" u-i="{{rule.o}}" bind:__l="__l" u-p="{{rule.p}}"><path wx:if="{{rule.l}}" u-i="{{rule.k}}" bind:__l="__l" u-p="{{rule.l}}"/><path wx:if="{{rule.n}}" u-i="{{rule.m}}" bind:__l="__l" u-p="{{rule.n}}"/></svg><svg wx:if="{{rule.q}}" u-s="{{['d']}}" u-i="{{rule.C}}" bind:__l="__l" u-p="{{rule.D}}"><path wx:if="{{rule.s}}" u-i="{{rule.r}}" bind:__l="__l" u-p="{{rule.s}}"/><path wx:if="{{rule.v}}" u-i="{{rule.t}}" bind:__l="__l" u-p="{{rule.v}}"/><path wx:if="{{rule.x}}" u-i="{{rule.w}}" bind:__l="__l" u-p="{{rule.x}}"/><path wx:if="{{rule.z}}" u-i="{{rule.y}}" bind:__l="__l" u-p="{{rule.z}}"/><path wx:if="{{rule.B}}" u-i="{{rule.A}}" bind:__l="__l" u-p="{{rule.B}}"/></svg></view><view class="rule-content"><view class="rule-title">{{rule.F}}</view><view class="rule-desc">{{rule.G}}</view></view><view class="rule-points">+{{rule.H}}</view></view></view></view><view class="bottom-space"></view></scroll-view></view>