{"version": 3, "file": "pay-commission.js", "sources": ["subPackages/merchant-admin-marketing/pages/marketing/distribution/pay-commission.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/c3ViUGFja2FnZXNcbWVyY2hhbnQtYWRtaW4tbWFya2V0aW5nXHBhZ2VzXG1hcmtldGluZ1xkaXN0cmlidXRpb25ccGF5LWNvbW1pc3Npb24udnVl"], "sourcesContent": ["<template>\n  <view class=\"page-container\">\n    <!-- 自定义导航栏 -->\n    <view class=\"navbar\">\n      <view class=\"navbar-back\" @click=\"goBack\">\n        <view class=\"back-icon\"></view>\n      </view>\n      <text class=\"navbar-title\">佣金发放</text>\n      <view class=\"navbar-right\">\n        <view class=\"help-icon\" @click=\"showHelp\">?</view>\n      </view>\n    </view>\n    \n    <!-- 页面内容 -->\n    <view class=\"content-container\">\n      <text class=\"page-tip\">此页面正在开发中...</text>\n    </view>\n  </view>\n</template>\n\n<script>\nexport default {\n  data() {\n    return {\n      // 数据将在这里定义\n    }\n  },\n  methods: {\n    goBack() {\n      uni.navigateBack();\n    },\n    showHelp() {\n      uni.showModal({\n        title: '佣金发放帮助',\n        content: '在此页面您可以管理分销佣金的发放，查看佣金记录等。',\n        showCancel: false\n      });\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\">\n.page-container {\n  min-height: 100vh;\n  background-color: #F5F7FA;\n  padding-bottom: env(safe-area-inset-bottom);\n}\n\n/* 导航栏样式 */\n.navbar {\n  position: sticky;\n  top: 0;\n  left: 0;\n  right: 0;\n  background: linear-gradient(135deg, #9370DB, #8A2BE2);\n  color: #fff;\n  padding: 44px 16px 15px;\n  display: flex;\n  align-items: center;\n  z-index: 100;\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.15);\n}\n\n.navbar-back {\n  width: 36px;\n  height: 36px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.back-icon {\n  width: 12px;\n  height: 12px;\n  border-left: 2px solid #fff;\n  border-bottom: 2px solid #fff;\n  transform: rotate(45deg);\n}\n\n.navbar-title {\n  flex: 1;\n  text-align: center;\n  font-size: 18px;\n  font-weight: 600;\n  letter-spacing: 0.5px;\n}\n\n.navbar-right {\n  width: 36px;\n  height: 36px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.help-icon {\n  width: 24px;\n  height: 24px;\n  border-radius: 12px;\n  background: rgba(255, 255, 255, 0.2);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 14px;\n  font-weight: bold;\n}\n\n/* 内容样式 */\n.content-container {\n  padding: 20px;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  min-height: 300px;\n}\n\n.page-tip {\n  font-size: 16px;\n  color: #999;\n  margin-top: 20px;\n}\n</style>", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/subPackages/merchant-admin-marketing/pages/marketing/distribution/pay-commission.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni"], "mappings": ";;AAqBA,MAAK,YAAU;AAAA,EACb,OAAO;AACL,WAAO;AAAA;AAAA,IAEP;AAAA,EACD;AAAA,EACD,SAAS;AAAA,IACP,SAAS;AACPA,oBAAG,MAAC,aAAY;AAAA,IACjB;AAAA,IACD,WAAW;AACTA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,SAAS;AAAA,QACT,YAAY;AAAA,MACd,CAAC;AAAA,IACH;AAAA,EACF;AACF;;;;;;;;ACtCA,GAAG,WAAW,eAAe;"}