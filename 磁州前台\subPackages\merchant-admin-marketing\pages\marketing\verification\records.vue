<template>
  <view class="records-container">
    <!-- 自定义导航栏 -->
    <view class="navbar">
      <view class="navbar-back" @click="goBack">
        <view class="back-icon"></view>
      </view>
      <text class="navbar-title">核销记录</text>
      <view class="navbar-right">
        <view class="help-icon" @click="showHelp">?</view>
      </view>
    </view>
    
    <!-- 筛选区域 -->
    <view class="filter-area">
      <view class="filter-tabs">
        <view 
          class="filter-tab" 
          v-for="(tab, index) in filterTabs" 
          :key="index"
          :class="{ 'active': currentTab === index }"
          @click="switchTab(index)"
        >
          <text>{{tab}}</text>
        </view>
      </view>
      
      <view class="filter-actions">
        <view class="date-filter" @click="showDatePicker">
          <text class="date-text">{{dateRange}}</text>
          <view class="date-arrow"></view>
        </view>
        
        <view class="search-btn" @click="showSearch">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="18" height="18" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <circle cx="11" cy="11" r="8"></circle>
            <line x1="21" y1="21" x2="16.65" y2="16.65"></line>
          </svg>
        </view>
      </view>
    </view>
    
    <!-- 记录列表 -->
    <view class="records-list">
      <view v-if="filteredRecords.length === 0" class="empty-records">
        <text class="empty-text">暂无核销记录</text>
      </view>
      <view v-else class="record-item" v-for="(record, index) in filteredRecords" :key="index" @click="showRecordDetail(record)">
        <view class="record-type" :class="record.typeClass">{{record.typeText}}</view>
        <view class="record-content">
          <view class="record-main">
            <text class="record-title">{{record.title}}</text>
            <text class="record-code">{{record.code}}</text>
          </view>
          <view class="record-info">
            <text class="record-user">用户：{{record.user}}</text>
            <text class="record-time">{{record.time}}</text>
          </view>
        </view>
        <view class="record-status" :class="record.statusClass">{{record.status}}</view>
      </view>
    </view>
    
    <!-- 加载更多 -->
    <view class="load-more" v-if="hasMore">
      <text class="load-text" @click="loadMore">加载更多</text>
    </view>
    
    <!-- 搜索弹窗 -->
    <view class="search-popup" v-if="showSearchPopup">
      <view class="search-header">
        <view class="search-input-box">
          <input 
            class="search-input" 
            type="text" 
            placeholder="搜索核销码/商品名称/用户" 
            v-model="searchKeyword"
            focus
            @confirm="doSearch"
          />
          <view class="clear-btn" v-if="searchKeyword" @click="clearSearch">×</view>
        </view>
        <view class="cancel-btn" @click="hideSearch">取消</view>
      </view>
    </view>
    
    <!-- 记录详情弹窗 -->
    <view class="detail-popup" v-if="showDetailPopup">
      <view class="popup-content">
        <view class="popup-header">
          <text class="popup-title">核销详情</text>
          <view class="popup-close" @click="closeDetailPopup">×</view>
        </view>
        
        <view class="detail-info">
          <view class="detail-status" :class="currentRecord.statusClass">{{currentRecord.status}}</view>
          
          <view class="detail-item">
            <text class="detail-label">核销类型</text>
            <text class="detail-value">{{currentRecord.typeText}}</text>
          </view>
          <view class="detail-item">
            <text class="detail-label">商品名称</text>
            <text class="detail-value">{{currentRecord.title}}</text>
          </view>
          <view class="detail-item">
            <text class="detail-label">核销码</text>
            <text class="detail-value">{{currentRecord.code}}</text>
          </view>
          <view class="detail-item">
            <text class="detail-label">用户信息</text>
            <text class="detail-value">{{currentRecord.user}}</text>
          </view>
          <view class="detail-item">
            <text class="detail-label">核销时间</text>
            <text class="detail-value">{{currentRecord.time}}</text>
          </view>
          <view class="detail-item">
            <text class="detail-label">核销人员</text>
            <text class="detail-value">{{currentRecord.operator || '系统'}}</text>
          </view>
        </view>
        
        <button class="close-btn" @click="closeDetailPopup">关闭</button>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      filterTabs: ['全部', '拼团', '优惠券', '秒杀'],
      currentTab: 0,
      dateRange: '最近7天',
      searchKeyword: '',
      showSearchPopup: false,
      showDetailPopup: false,
      currentRecord: {},
      hasMore: true,
      records: [
        {
          id: 1,
          typeText: '拼团',
          typeClass: 'type-group',
          title: '双人下午茶套餐拼团',
          code: 'GP20230618001',
          user: '张三 (138****8888)',
          time: '2023-06-18 14:30',
          status: '已核销',
          statusClass: 'status-success',
          operator: '王店长'
        },
        {
          id: 2,
          typeText: '优惠券',
          typeClass: 'type-coupon',
          title: '新店开业满100减20券',
          code: 'CP20230618002',
          user: '李四 (139****9999)',
          time: '2023-06-18 11:15',
          status: '已核销',
          statusClass: 'status-success',
          operator: '王店长'
        },
        {
          id: 3,
          typeText: '秒杀',
          typeClass: 'type-flash',
          title: '限时特价烤鸭套餐',
          code: 'FS20230617005',
          user: '王五 (137****7777)',
          time: '2023-06-17 18:45',
          status: '已核销',
          statusClass: 'status-success',
          operator: '张经理'
        },
        {
          id: 4,
          typeText: '拼团',
          typeClass: 'type-group',
          title: '亲子套餐拼团',
          code: 'GP20230617003',
          user: '赵六 (136****6666)',
          time: '2023-06-17 12:20',
          status: '已核销',
          statusClass: 'status-success',
          operator: '张经理'
        },
        {
          id: 5,
          typeText: '优惠券',
          typeClass: 'type-coupon',
          title: '生日特惠券',
          code: 'CP20230616008',
          user: '钱七 (135****5555)',
          time: '2023-06-16 19:30',
          status: '已核销',
          statusClass: 'status-success',
          operator: '李助理'
        },
        {
          id: 6,
          typeText: '秒杀',
          typeClass: 'type-flash',
          title: '周末特价牛排套餐',
          code: 'FS20230616002',
          user: '孙八 (134****4444)',
          time: '2023-06-16 18:10',
          status: '已核销',
          statusClass: 'status-success',
          operator: '李助理'
        }
      ]
    }
  },
  computed: {
    filteredRecords() {
      let result = [...this.records];
      
      // 根据标签筛选
      if (this.currentTab > 0) {
        const tabType = this.filterTabs[this.currentTab];
        result = result.filter(record => record.typeText === tabType);
      }
      
      // 根据搜索关键词筛选
      if (this.searchKeyword) {
        const keyword = this.searchKeyword.toLowerCase();
        result = result.filter(record => 
          record.title.toLowerCase().includes(keyword) || 
          record.code.toLowerCase().includes(keyword) || 
          record.user.toLowerCase().includes(keyword)
        );
      }
      
      return result;
    }
  },
  methods: {
    goBack() {
      uni.navigateBack();
    },
    showHelp() {
      uni.showToast({
        title: '核销记录帮助',
        icon: 'none'
      });
    },
    switchTab(index) {
      this.currentTab = index;
    },
    showDatePicker() {
      uni.showActionSheet({
        itemList: ['今天', '昨天', '最近7天', '最近30天', '全部'],
        success: (res) => {
          const dateOptions = ['今天', '昨天', '最近7天', '最近30天', '全部'];
          this.dateRange = dateOptions[res.tapIndex];
          
          uni.showToast({
            title: `已选择${this.dateRange}`,
            icon: 'none'
          });
        }
      });
    },
    showSearch() {
      this.showSearchPopup = true;
    },
    hideSearch() {
      this.showSearchPopup = false;
      this.searchKeyword = '';
    },
    clearSearch() {
      this.searchKeyword = '';
    },
    doSearch() {
      this.showSearchPopup = false;
      
      // 如果没有搜索结果，显示提示
      if (this.filteredRecords.length === 0) {
        uni.showToast({
          title: '未找到相关记录',
          icon: 'none'
        });
      }
    },
    showRecordDetail(record) {
      this.currentRecord = record;
      this.showDetailPopup = true;
    },
    closeDetailPopup() {
      this.showDetailPopup = false;
    },
    loadMore() {
      uni.showLoading({
        title: '加载中...'
      });
      
      // 模拟加载更多数据
      setTimeout(() => {
        uni.hideLoading();
        
        // 添加更多模拟数据
        const moreRecords = [
          {
            id: 7,
            typeText: '拼团',
            typeClass: 'type-group',
            title: '四人火锅套餐拼团',
            code: 'GP20230615004',
            user: '周九 (133****3333)',
            time: '2023-06-15 20:15',
            status: '已核销',
            statusClass: 'status-success',
            operator: '王店长'
          },
          {
            id: 8,
            typeText: '优惠券',
            typeClass: 'type-coupon',
            title: '下午茶优惠券',
            code: 'CP20230615007',
            user: '吴十 (132****2222)',
            time: '2023-06-15 15:40',
            status: '已核销',
            statusClass: 'status-success',
            operator: '张经理'
          }
        ];
        
        this.records = [...this.records, ...moreRecords];
        
        // 假设没有更多数据了
        this.hasMore = false;
        
        uni.showToast({
          title: '已加载全部数据',
          icon: 'none'
        });
      }, 1000);
    }
  }
}
</script>

<style lang="scss">
.records-container {
  min-height: 100vh;
  background-color: #F5F7FA;
  padding-bottom: 20px;
}

/* 导航栏样式 */
.navbar {
  position: sticky;
  top: 0;
  left: 0;
  right: 0;
  background: linear-gradient(135deg, #e67e22, #d35400);
  color: #fff;
  padding: 44px 16px 15px;
  display: flex;
  align-items: center;
  z-index: 100;
  box-shadow: 0 4px 20px rgba(230, 126, 34, 0.2);
}

.navbar-back {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-icon {
  width: 12px;
  height: 12px;
  border-left: 2px solid #fff;
  border-bottom: 2px solid #fff;
  transform: rotate(45deg);
}

.navbar-title {
  flex: 1;
  text-align: center;
  font-size: 18px;
  font-weight: 600;
  letter-spacing: 0.5px;
}

.navbar-right {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.help-icon {
  width: 24px;
  height: 24px;
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: bold;
}

/* 筛选区域样式 */
.filter-area {
  background: #FFFFFF;
  padding: 20px;
  margin-bottom: 15px;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.08);
}

.filter-tabs {
  display: flex;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  padding-bottom: 12px;
  margin-bottom: 18px;
}

.filter-tab {
  margin-right: 25px;
  padding: 6px 0;
  font-size: 15px;
  color: #666;
  position: relative;
  transition: color 0.2s;
}

.filter-tab.active {
  color: #e67e22;
  font-weight: 600;
}

.filter-tab.active::after {
  content: '';
  position: absolute;
  bottom: -13px;
  left: 0;
  right: 0;
  height: 3px;
  background: #e67e22;
  border-radius: 1.5px;
}

.filter-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.date-filter {
  display: flex;
  align-items: center;
  background: #F5F7FA;
  border-radius: 18px;
  padding: 8px 14px;
  transition: background-color 0.2s;
}

.date-filter:active {
  background: #EBEEF2;
}

.date-text {
  font-size: 14px;
  color: #666;
  margin-right: 8px;
  font-weight: 500;
}

.date-arrow {
  width: 8px;
  height: 8px;
  border-top: 2px solid #666;
  border-right: 2px solid #666;
  transform: rotate(135deg);
}

.search-btn {
  width: 36px;
  height: 36px;
  border-radius: 18px;
  background: #F5F7FA;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #666;
  transition: background-color 0.2s;
}

.search-btn:active {
  background: #EBEEF2;
}

/* 记录列表样式 */
.records-list {
  background: #FFFFFF;
  margin: 0 15px;
  border-radius: 20px;
  padding: 20px;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.08);
}

.empty-records {
  padding: 50px 0;
  text-align: center;
}

.empty-text {
  font-size: 14px;
  color: #999;
}

.record-item {
  display: flex;
  align-items: center;
  padding: 16px 0;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.record-item:last-child {
  border-bottom: none;
}

.record-type {
  width: 60px;
  height: 28px;
  border-radius: 14px;
  font-size: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 14px;
  font-weight: 500;
}

.type-group {
  background-color: rgba(39, 174, 96, 0.15);
  color: #27ae60;
}

.type-coupon {
  background-color: rgba(41, 128, 185, 0.15);
  color: #2980b9;
}

.type-flash {
  background-color: rgba(231, 76, 60, 0.15);
  color: #e74c3c;
}

.record-content {
  flex: 1;
}

.record-main {
  margin-bottom: 4px;
}

.record-title {
  font-size: 15px;
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
}

.record-code {
  font-size: 13px;
  color: #777;
}

.record-info {
  display: flex;
  font-size: 13px;
  color: #777;
  margin-top: 6px;
}

.record-user {
  margin-right: 12px;
}

.record-status {
  font-size: 13px;
  font-weight: 500;
}

.status-success {
  color: #27ae60;
}

.status-pending {
  color: #e67e22;
}

.status-failed {
  color: #e74c3c;
}

/* 加载更多样式 */
.load-more {
  text-align: center;
  padding: 20px 0;
}

.load-text {
  font-size: 15px;
  color: #2980b9;
  font-weight: 500;
  padding: 10px 20px;
  border-radius: 18px;
  background: rgba(41, 128, 185, 0.1);
  display: inline-block;
  transition: background-color 0.2s;
}

.load-text:active {
  background: rgba(41, 128, 185, 0.15);
}

/* 搜索弹窗样式 */
.search-popup {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: #FFFFFF;
  z-index: 999;
}

.search-header {
  padding: 44px 15px 15px;
  display: flex;
  align-items: center;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
}

.search-input-box {
  flex: 1;
  position: relative;
  margin-right: 15px;
}

.search-input {
  width: 100%;
  height: 40px;
  background: #F5F7FA;
  border-radius: 20px;
  padding: 0 18px;
  font-size: 15px;
  color: #333;
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.clear-btn {
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  width: 18px;
  height: 18px;
  border-radius: 9px;
  background: #C7C7CC;
  color: #FFFFFF;
  font-size: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.cancel-btn {
  font-size: 15px;
  color: #2980b9;
  font-weight: 500;
  padding: 8px 12px;
  border-radius: 16px;
}

.cancel-btn:active {
  background: rgba(41, 128, 185, 0.1);
}

/* 记录详情弹窗样式 */
.detail-popup {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
  z-index: 999;
  display: flex;
  align-items: center;
  justify-content: center;
}

.popup-content {
  width: 85%;
  background: #fff;
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.3);
}

.popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 18px 20px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  background: #f8f8f8;
}

.popup-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.popup-close {
  font-size: 22px;
  color: #777;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 18px;
}

.popup-close:active {
  background: rgba(0, 0, 0, 0.05);
}

.detail-info {
  padding: 20px;
  position: relative;
}

.detail-status {
  position: absolute;
  top: 20px;
  right: 20px;
  font-size: 15px;
  font-weight: 600;
}

.detail-item {
  display: flex;
  margin-bottom: 16px;
}

.detail-item:last-child {
  margin-bottom: 0;
}

.detail-label {
  width: 90px;
  font-size: 15px;
  color: #777;
}

.detail-value {
  flex: 1;
  font-size: 15px;
  color: #333;
  font-weight: 500;
}

.close-btn {
  width: 100%;
  height: 56px;
  line-height: 56px;
  text-align: center;
  font-size: 17px;
  font-weight: 500;
  color: #2980b9;
  border: none;
  border-top: 1px solid rgba(0, 0, 0, 0.05);
  border-radius: 0;
  background: #fff;
}
</style>
