"use strict";
const common_vendor = require("../common/vendor.js");
const shareConfig = {
  // 分享标题
  getTitle(type, data) {
    const titles = {
      redPacket: `${data.sender.nickname}的红包`,
      post: data.title || "分享一个有趣的动态",
      promotion: data.title || "分享一个优惠活动"
    };
    return titles[type] || "分享";
  },
  // 分享描述
  getDesc(type, data) {
    const descs = {
      redPacket: "快来抢红包啦！",
      post: data.content || "快来看看吧",
      promotion: data.description || "优惠活动等你来"
    };
    return descs[type] || "";
  },
  // 分享图片
  getImage(type, data) {
    var _a;
    const images = {
      redPacket: "/static/images/share-red-packet.png",
      post: ((_a = data.images) == null ? void 0 : _a[0]) || "/static/images/share-post.png",
      promotion: data.image || "/static/images/share-promotion.png"
    };
    return images[type] || "/static/images/share-default.png";
  },
  // 分享链接
  getPath(type, data) {
    const paths = {
      redPacket: `/pages/red-packet/grab?id=${data.id}`,
      post: `/pages/post/detail?id=${data.id}`,
      promotion: `/pages/promotion/detail?id=${data.id}`
    };
    return paths[type] || "/pages/index/index";
  }
};
function shareToWechat(options) {
  return new Promise((resolve, reject) => {
    common_vendor.index.share({
      provider: "weixin",
      scene: "WXSceneSession",
      type: 0,
      title: shareConfig.getTitle(options.type, options.data),
      summary: shareConfig.getDesc(options.type, options.data),
      imageUrl: shareConfig.getImage(options.type, options.data),
      href: shareConfig.getPath(options.type, options.data),
      success: function(res) {
        resolve(res);
      },
      fail: function(err) {
        reject(err);
      }
    });
  });
}
function shareToTimeline(options) {
  return new Promise((resolve, reject) => {
    common_vendor.index.share({
      provider: "weixin",
      scene: "WXSceneTimeline",
      type: 0,
      title: shareConfig.getTitle(options.type, options.data),
      summary: shareConfig.getDesc(options.type, options.data),
      imageUrl: shareConfig.getImage(options.type, options.data),
      href: shareConfig.getPath(options.type, options.data),
      success: function(res) {
        resolve(res);
      },
      fail: function(err) {
        reject(err);
      }
    });
  });
}
function showShareMenu(options) {
  return new Promise((resolve, reject) => {
    common_vendor.index.showActionSheet({
      itemList: ["分享到微信好友", "分享到朋友圈"],
      success: function(res) {
        if (res.tapIndex === 0) {
          shareToWechat(options).then(resolve).catch(reject);
        } else if (res.tapIndex === 1) {
          shareToTimeline(options).then(resolve).catch(reject);
        }
      },
      fail: function(err) {
        reject(err);
      }
    });
  });
}
exports.showShareMenu = showShareMenu;
//# sourceMappingURL=../../.sourcemap/mp-weixin/utils/share.js.map
