<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="description" content="磁州生活网后台管理系统 - 企业级微服务管理平台" />
    <meta name="keywords" content="磁州生活网,后台管理,企业级,微服务,Vue3,TypeScript" />
    <meta name="author" content="Cizhou Team" />
    <title>磁州生活网后台管理系统</title>
    
    <!-- 预加载关键资源 -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    
    <!-- 防止页面闪烁 -->
    <style>
      #app {
        height: 100vh;
        margin: 0;
        padding: 0;
      }
      
      .loading-container {
        display: flex;
        justify-content: center;
        align-items: center;
        height: 100vh;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      }
      
      .loading-spinner {
        width: 50px;
        height: 50px;
        border: 4px solid rgba(255, 255, 255, 0.3);
        border-top: 4px solid #ffffff;
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }
      
      .loading-text {
        color: #ffffff;
        font-size: 16px;
        margin-top: 20px;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      }
      
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
    </style>
  </head>
  <body>
    <div id="app">
      <!-- 加载动画 -->
      <div class="loading-container">
        <div>
          <div class="loading-spinner"></div>
          <div class="loading-text">磁州生活网后台管理系统加载中...</div>
        </div>
      </div>
    </div>
    <script type="module" src="/src/main.ts"></script>
  </body>
</html>
