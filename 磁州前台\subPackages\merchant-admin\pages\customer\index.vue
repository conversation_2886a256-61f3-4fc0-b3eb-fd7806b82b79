<template>
  <view class="customer-management-container">
    <!-- 自定义导航栏 -->
    <view class="navbar">
      <view class="navbar-back" @click="goBack">
        <view class="back-icon"></view>
      </view>
      <text class="navbar-title">客户运营</text>
      <view class="navbar-right">
        <view class="help-icon">?</view>
      </view>
    </view>
    
    <!-- 客户数据概览 -->
    <view class="data-overview">
      <view class="overview-header">
        <text class="overview-title">客户数据概览</text>
        <view class="date-selector">
          <text class="date-text">{{selectedDateRange}}</text>
          <view class="selector-icon"></view>
        </view>
      </view>
      
      <view class="metric-cards">
        <view class="metric-card">
          <view class="metric-icon customers"></view>
          <view class="metric-data">
            <text class="metric-value">{{customerMetrics.totalCustomers}}</text>
            <text class="metric-label">总客户数</text>
          </view>
          <view class="metric-trend up">
            <view class="trend-icon"></view>
            <text class="trend-text">{{customerMetrics.customerGrowth}}</text>
          </view>
        </view>
        
        <view class="metric-card">
          <view class="metric-icon new"></view>
          <view class="metric-data">
            <text class="metric-value">{{customerMetrics.newCustomers}}</text>
            <text class="metric-label">新增客户</text>
          </view>
          <view class="metric-trend up">
            <view class="trend-icon"></view>
            <text class="trend-text">{{customerMetrics.newCustomerGrowth}}</text>
          </view>
        </view>
        
        <view class="metric-card">
          <view class="metric-icon active"></view>
          <view class="metric-data">
            <text class="metric-value">{{customerMetrics.activeRate}}%</text>
            <text class="metric-label">活跃率</text>
          </view>
          <view class="metric-trend down">
            <view class="trend-icon"></view>
            <text class="trend-text">{{customerMetrics.activeRateChange}}</text>
          </view>
        </view>
        
        <view class="metric-card">
          <view class="metric-icon repurchase"></view>
          <view class="metric-data">
            <text class="metric-value">{{customerMetrics.repurchaseRate}}%</text>
            <text class="metric-label">复购率</text>
          </view>
          <view class="metric-trend up">
            <view class="trend-icon"></view>
            <text class="trend-text">{{customerMetrics.repurchaseRateChange}}</text>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 客户分群分析 -->
    <view class="customer-segments">
      <view class="section-header">
        <text class="section-title">客户分群分析</text>
        <text class="section-action" @tap="navigateTo('/pages/customer/segments')">管理分群</text>
      </view>
      
      <view class="segment-tabs">
        <view 
          class="segment-tab" 
          v-for="(tab, index) in segmentTabs" 
          :key="index" 
          :class="{ active: currentSegment === tab.id }"
          @tap="switchSegment(tab.id)">
          {{tab.name}}
        </view>
      </view>
      
      <view class="segment-info-card">
        <view class="segment-header">
          <view class="segment-title-container">
            <text class="segment-title">{{currentSegmentData.name}}</text>
            <view class="segment-tag" :class="currentSegmentData.tagType">{{currentSegmentData.tag}}</view>
          </view>
          <view class="segment-action" @tap="targetSegment(currentSegmentData)">
            <text>定向营销</text>
            <view class="action-icon"></view>
          </view>
        </view>
        
        <view class="segment-metrics">
          <view class="segment-metric">
            <text class="metric-value">{{currentSegmentData.customerCount}}</text>
            <text class="metric-label">客户数量</text>
          </view>
          <view class="segment-metric">
            <text class="metric-value">¥{{currentSegmentData.avgValue}}</text>
            <text class="metric-label">客户价值</text>
          </view>
          <view class="segment-metric">
            <text class="metric-value">{{currentSegmentData.purchaseFrequency}}</text>
            <text class="metric-label">购买频次</text>
          </view>
        </view>
        
        <view class="segment-chart">
          <canvas canvas-id="segmentChart" class="chart-canvas"></canvas>
          <text class="chart-title">消费趋势 (近6个月)</text>
        </view>
        
        <view class="segment-traits">
          <text class="traits-title">客群特征:</text>
          <view class="traits-tags">
            <view class="trait-tag" v-for="(trait, index) in currentSegmentData.traits" :key="index">
              {{trait}}
            </view>
          </view>
        </view>
        
        <view class="segment-actions">
          <view class="action-btn detail" @tap="viewSegmentDetail(currentSegmentData)">
            <text>查看详情</text>
          </view>
          <view class="action-btn export" @tap="exportSegmentData(currentSegmentData)">
            <text>导出数据</text>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 客户生命周期 -->
    <view class="lifecycle-section">
      <view class="section-header">
        <text class="section-title">客户生命周期</text>
        <text class="section-action" @tap="navigateTo('/pages/customer/lifecycle')">查看详情</text>
      </view>
      
      <view class="lifecycle-card">
        <view class="lifecycle-chart">
          <image class="lifecycle-image" src="/static/images/lifecycle-chart.png" mode="widthFix"></image>
        </view>
        
        <view class="lifecycle-metrics">
          <view class="lifecycle-stage">
            <view class="stage-header">
              <view class="stage-icon new"></view>
              <text class="stage-name">新客户</text>
            </view>
            <view class="stage-data">
              <text class="stage-value">{{lifecycle.newCustomers}}</text>
              <view class="stage-trend" :class="lifecycle.newTrend">
                <view class="trend-icon"></view>
                <text class="trend-value">{{lifecycle.newGrowth}}</text>
              </view>
            </view>
          </view>
          
          <view class="lifecycle-stage">
            <view class="stage-header">
              <view class="stage-icon growing"></view>
              <text class="stage-name">成长期</text>
            </view>
            <view class="stage-data">
              <text class="stage-value">{{lifecycle.growingCustomers}}</text>
              <view class="stage-trend" :class="lifecycle.growingTrend">
                <view class="trend-icon"></view>
                <text class="trend-value">{{lifecycle.growingGrowth}}</text>
              </view>
            </view>
          </view>
          
          <view class="lifecycle-stage">
            <view class="stage-header">
              <view class="stage-icon mature"></view>
              <text class="stage-name">成熟期</text>
            </view>
            <view class="stage-data">
              <text class="stage-value">{{lifecycle.matureCustomers}}</text>
              <view class="stage-trend" :class="lifecycle.matureTrend">
                <view class="trend-icon"></view>
                <text class="trend-value">{{lifecycle.matureGrowth}}</text>
              </view>
            </view>
          </view>
          
          <view class="lifecycle-stage">
            <view class="stage-header">
              <view class="stage-icon risk"></view>
              <text class="stage-name">流失风险</text>
            </view>
            <view class="stage-data">
              <text class="stage-value">{{lifecycle.riskCustomers}}</text>
              <view class="stage-trend" :class="lifecycle.riskTrend">
                <view class="trend-icon"></view>
                <text class="trend-value">{{lifecycle.riskGrowth}}</text>
              </view>
            </view>
          </view>
        </view>
        
        <view class="lifecycle-insights">
          <view class="insight-icon"></view>
          <text class="insight-text">{{lifecycle.insight}}</text>
        </view>
      </view>
    </view>
    
    <!-- RFM 价值矩阵 -->
    <view class="rfm-section">
      <view class="section-header">
        <text class="section-title">客户价值矩阵</text>
        <text class="section-action" @tap="navigateTo('/pages/customer/rfm')">查看详情</text>
      </view>
      
      <view class="rfm-card">
        <view class="rfm-matrix">
          <view class="rfm-row" v-for="(row, rowIndex) in rfmMatrix" :key="'row-'+rowIndex">
            <view 
              class="rfm-cell" 
              v-for="(cell, cellIndex) in row" 
              :key="'cell-'+cellIndex"
              :class="[cell.type]"
              @tap="viewRfmSegment(cell)">
              <text class="cell-name">{{cell.name}}</text>
              <text class="cell-value">{{cell.value}}%</text>
            </view>
          </view>
        </view>
        
        <view class="rfm-legend">
          <text class="legend-title">价值: </text>
          <view class="legend-item">
            <view class="legend-color high"></view>
            <text class="legend-text">高价值</text>
          </view>
          <view class="legend-item">
            <view class="legend-color medium"></view>
            <text class="legend-text">中价值</text>
          </view>
          <view class="legend-item">
            <view class="legend-color low"></view>
            <text class="legend-text">低价值</text>
          </view>
          <view class="legend-item">
            <view class="legend-color potential"></view>
            <text class="legend-text">潜力客户</text>
          </view>
        </view>
        
        <view class="rfm-desc">
          <text class="desc-title">RFM分析: </text>
          <text class="desc-text">基于客户最近一次消费(Recency)、消费频率(Frequency)及消费金额(Monetary)进行客户分群</text>
        </view>
      </view>
    </view>
    
    <!-- 客户标签管理 -->
    <view class="tag-section">
      <view class="section-header">
        <text class="section-title">客户标签管理</text>
        <text class="section-action" @tap="navigateTo('/pages/customer/tags')">管理标签</text>
      </view>
      
      <view class="tag-card">
        <view class="tag-categories">
          <view 
            class="tag-category" 
            v-for="(category, index) in tagCategories" 
            :key="index"
            :class="{ active: currentTagCategory === category.id }"
            @tap="switchTagCategory(category.id)">
            {{category.name}}
          </view>
        </view>
        
        <view class="tag-cloud">
          <view 
            class="tag-item" 
            v-for="(tag, index) in currentCategoryTags" 
            :key="index"
            :class="tag.size"
            @tap="viewTagCustomers(tag)">
            <text>{{tag.name}}</text>
            <text class="tag-count">({{tag.count}})</text>
          </view>
        </view>
        
        <view class="tag-footer">
          <view class="create-tag-btn" @tap="createNewTag">
            <view class="btn-icon"></view>
            <text class="btn-text">创建新标签</text>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 客户互动管理 -->
    <view class="interaction-section">
      <view class="section-header">
        <text class="section-title">客户互动</text>
        <text class="section-action" @tap="navigateTo('/pages/customer/interaction')">全部消息</text>
      </view>
      
      <view class="message-stats">
        <view class="message-stat-item">
          <text class="stat-value">{{messageStats.unread}}</text>
          <text class="stat-label">未读消息</text>
        </view>
        <view class="message-stat-item">
          <text class="stat-value">{{messageStats.today}}</text>
          <text class="stat-label">今日消息</text>
        </view>
        <view class="message-stat-item">
          <text class="stat-value">{{messageStats.responseRate}}%</text>
          <text class="stat-label">响应率</text>
        </view>
        <view class="message-stat-item">
          <text class="stat-value">{{messageStats.responseTime}}</text>
          <text class="stat-label">平均响应时间</text>
        </view>
      </view>
      
      <view class="recent-messages">
        <view class="message-item" v-for="(message, index) in recentMessages" :key="index" @tap="viewConversation(message)">
          <image class="customer-avatar" :src="message.customerAvatar" mode="aspectFill"></image>
          <view class="message-content">
            <view class="message-header">
              <text class="customer-name">{{message.customerName}}</text>
              <text class="message-time">{{message.time}}</text>
            </view>
            <text class="message-preview">{{message.content}}</text>
          </view>
          <view class="message-badge" v-if="message.unreadCount > 0">{{message.unreadCount}}</view>
        </view>
        
        <view class="interaction-footer">
          <view class="action-btn" @tap="navigateTo('/pages/customer/message')">
            <view class="btn-icon message"></view>
            <text class="btn-text">消息中心</text>
          </view>
          <view class="action-btn" @tap="navigateTo('/pages/customer/notification')">
            <view class="btn-icon notify"></view>
            <text class="btn-text">发送通知</text>
          </view>
          <view class="action-btn" @tap="navigateTo('/pages/customer/survey')">
            <view class="btn-icon survey"></view>
            <text class="btn-text">问卷调研</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      // 选中的日期范围
      selectedDateRange: '过去30天',
      
      // 客户数据概览
      customerMetrics: {
        totalCustomers: '3,216',
        customerGrowth: '+8.3%',
        newCustomers: '238',
        newCustomerGrowth: '+12.7%',
        activeRate: '42.8',
        activeRateChange: '-2.5%',
        repurchaseRate: '31.5',
        repurchaseRateChange: '+5.4%'
      },
      
      // 客户分群标签页
      segmentTabs: [
        { id: 1, name: '高价值客户' },
        { id: 2, name: '活跃消费者' },
        { id: 3, name: '新客户' },
        { id: 4, name: '流失风险' },
        { id: 5, name: '低频消费' },
      ],
      currentSegment: 1,
      
      // 分群数据
      segmentData: [
        {
          id: 1,
          name: '高价值客户',
          tagType: 'high-value',
          tag: '高价值',
          customerCount: '325',
          avgValue: '4,625',
          purchaseFrequency: '3.7次/月',
          traits: ['高频消费者', '忠诚会员', '品质敏感', '夜间购物', '追求体验'],
          chartData: [8500, 9200, 9800, 10200, 11500, 12300]
        },
        {
          id: 2,
          name: '活跃消费者',
          tagType: 'active',
          tag: '活跃',
          customerCount: '462',
          avgValue: '1,850',
          purchaseFrequency: '2.8次/月',
          traits: ['周末消费者', '热衷促销', '追求性价比', '多频次', '购物篮多样'],
          chartData: [3600, 3900, 4200, 4500, 4800, 5100]
        },
        {
          id: 3,
          name: '新客户',
          tagType: 'new',
          tag: '新客',
          customerCount: '538',
          avgValue: '960',
          purchaseFrequency: '1.2次/月',
          traits: ['价格敏感', '尝试购买', '小额消费', '移动端', '社交媒体引导'],
          chartData: [0, 0, 0, 0, 350, 950]
        },
        {
          id: 4,
          name: '流失风险',
          tagType: 'risk',
          tag: '风险',
          customerCount: '186',
          avgValue: '2,340',
          purchaseFrequency: '0.5次/月',
          traits: ['消费频次下降', '曾为高价值', '购买周期拉长', '有投诉历史', '客服互动'],
          chartData: [5200, 4800, 4200, 3600, 3200, 2800]
        },
        {
          id: 5,
          name: '低频消费',
          tagType: 'low',
          tag: '低频',
          customerCount: '1,705',
          avgValue: '780',
          purchaseFrequency: '0.7次/月',
          traits: ['季节性消费', '特定品类', '低参与度', '价格敏感', '偶发性购买'],
          chartData: [1200, 1050, 1350, 1100, 950, 1300]
        }
      ],
      
      // 客户生命周期数据
      lifecycle: {
        newCustomers: '538',
        newTrend: 'up',
        newGrowth: '+16.7%',
        growingCustomers: '782',
        growingTrend: 'up',
        growingGrowth: '+8.3%',
        matureCustomers: '1,246',
        matureTrend: 'down',
        matureGrowth: '-2.1%',
        riskCustomers: '650',
        riskTrend: 'down',
        riskGrowth: '-5.3%',
        insight: '新客户增长强劲，但成熟客户群体略有下降，建议关注成熟客户的留存策略。'
      },
      
      // RFM价值矩阵
      rfmMatrix: [
        [
          { name: '重要价值', value: 12, type: 'high' },
          { name: '重要保持', value: 8, type: 'high' },
          { name: '重要发展', value: 6, type: 'medium' },
        ],
        [
          { name: '一般价值', value: 10, type: 'medium' },
          { name: '一般保持', value: 14, type: 'medium' },
          { name: '一般发展', value: 11, type: 'low' },
        ],
        [
          { name: '潜力客户', value: 7, type: 'potential' },
          { name: '新客发展', value: 18, type: 'potential' },
          { name: '低价值', value: 14, type: 'low' },
        ]
      ],
      
      // 客户标签分类
      tagCategories: [
        { id: 1, name: '消费习惯' },
        { id: 2, name: '生活方式' },
        { id: 3, name: '人口特征' },
        { id: 4, name: '行为特征' },
        { id: 5, name: '偏好喜好' }
      ],
      currentTagCategory: 1,
      
      // 标签数据
      tagData: {
        1: [ // 消费习惯标签
          { name: '夜间购物', count: 625, size: 'large' },
          { name: '周末消费', count: 842, size: 'large' },
          { name: '促销敏感', count: 1236, size: 'xlarge' },
          { name: '高频次', count: 465, size: 'medium' },
          { name: '大额消费', count: 284, size: 'medium' },
          { name: '理性决策', count: 356, size: 'medium' },
          { name: '冲动购买', count: 478, size: 'medium' },
          { name: '季节性', count: 592, size: 'large' },
          { name: '特殊节日', count: 734, size: 'large' },
          { name: '长期积累', count: 185, size: 'small' },
          { name: '预算型', count: 605, size: 'large' },
          { name: '奢侈型', count: 146, size: 'small' }
        ],
        2: [ // 生活方式标签
          { name: '重视家庭', count: 865, size: 'large' },
          { name: '现代简约', count: 742, size: 'large' },
          { name: '工作繁忙', count: 638, size: 'large' },
          { name: '喜爱旅行', count: 425, size: 'medium' },
          { name: '健康生活', count: 528, size: 'medium' },
          { name: '社交活跃', count: 356, size: 'medium' },
          { name: '宅家爱好者', count: 492, size: 'medium' },
          { name: '典雅风格', count: 168, size: 'small' },
          { name: '环保意识', count: 274, size: 'medium' },
          { name: '科技发烧友', count: 183, size: 'small' },
          { name: '追求品质', count: 427, size: 'medium' }
        ],
        3: [ // 人口特征标签
          { name: '25-34岁', count: 782, size: 'large' },
          { name: '35-44岁', count: 864, size: 'large' },
          { name: '城市居民', count: 1568, size: 'xlarge' },
          { name: '高收入', count: 485, size: 'medium' },
          { name: '中等收入', count: 1246, size: 'xlarge' },
          { name: '已婚', count: 956, size: 'large' },
          { name: '有子女', count: 842, size: 'large' },
          { name: '高学历', count: 685, size: 'large' },
          { name: '女性', count: 1842, size: 'xlarge' },
          { name: '男性', count: 1374, size: 'xlarge' }
        ],
        4: [ // 行为特征标签
          { name: '多渠道', count: 562, size: 'large' },
          { name: '移动端', count: 1675, size: 'xlarge' },
          { name: '评论积极', count: 246, size: 'medium' },
          { name: '经常退货', count: 128, size: 'small' },
          { name: '分享推荐', count: 184, size: 'small' },
          { name: '货比三家', count: 567, size: 'large' },
          { name: '忠诚会员', count: 425, size: 'medium' },
          { name: '投诉历史', count: 78, size: 'xsmall' },
          { name: '客服咨询', count: 265, size: 'medium' },
          { name: '自助服务', count: 684, size: 'large' },
          { name: '快速决策', count: 347, size: 'medium' },
          { name: '深度研究', count: 428, size: 'medium' }
        ],
        5: [ // 偏好喜好标签
          { name: '北欧风格', count: 456, size: 'medium' },
          { name: '简约设计', count: 687, size: 'large' },
          { name: '环保材质', count: 345, size: 'medium' },
          { name: '明亮色调', count: 426, size: 'medium' },
          { name: '温暖色系', count: 372, size: 'medium' },
          { name: '多功能', count: 582, size: 'large' },
          { name: '极简主义', count: 428, size: 'medium' },
          { name: '复古风格', count: 216, size: 'small' },
          { name: '高科技', count: 284, size: 'medium' },
          { name: '实用主义', count: 745, size: 'large' },
          { name: '精致装饰', count: 367, size: 'medium' },
          { name: '舒适体验', count: 648, size: 'large' }
        ]
      },
      
      // 客户互动数据
      messageStats: {
        unread: 16,
        today: 28,
        responseRate: 93.5,
        responseTime: '8分钟'
      },
      
      // 最近消息
      recentMessages: [
        {
          id: 1,
          customerName: '张先生',
          customerAvatar: '/static/images/avatar-1.png',
          time: '10分钟前',
          content: '请问我订购的沙发什么时候能到货？',
          unreadCount: 1
        },
        {
          id: 2,
          customerName: '王女士',
          customerAvatar: '/static/images/avatar-2.png',
          time: '28分钟前',
          content: '收到的床垫有点问题，可以联系我处理一下吗？',
          unreadCount: 2
        },
        {
          id: 3,
          customerName: '李先生',
          customerAvatar: '/static/images/avatar-3.png',
          time: '1小时前',
          content: '请问你们有没有提供上门测量服务？',
          unreadCount: 0
        },
        {
          id: 4,
          customerName: '赵女士',
          customerAvatar: '/static/images/avatar-4.png',
          time: '2小时前',
          content: '窗帘的安装视频我看了，但还是有些不明白...',
          unreadCount: 0
        }
      ]
    }
  },
  
  computed: {
    // 获取当前选中的分群数据
    currentSegmentData() {
      return this.segmentData.find(segment => segment.id === this.currentSegment) || this.segmentData[0];
    },
    
    // 获取当前标签分类的标签
    currentCategoryTags() {
      return this.tagData[this.currentTagCategory] || [];
    }
  },
  
  mounted() {
    // 在实际项目中，这里应该调用API获取数据
    this.$nextTick(() => {
      this.drawSegmentChart();
    });
  },
  
  methods: {
    // 基础导航
    goBack() {
      uni.navigateBack();
    },
    
    navigateTo(url) {
      uni.navigateTo({
        url: url
      });
    },
    
    // 切换客户分群
    switchSegment(segmentId) {
      this.currentSegment = segmentId;
      this.$nextTick(() => {
        this.drawSegmentChart();
      });
    },
    
    // 定向营销客群
    targetSegment(segment) {
      uni.navigateTo({
        url: `/pages/customer/target?id=${segment.id}&name=${segment.name}`
      });
    },
    
    // 查看分群详情
    viewSegmentDetail(segment) {
      uni.navigateTo({
        url: `/pages/customer/segment-detail?id=${segment.id}`
      });
    },
    
    // 导出分群数据
    exportSegmentData(segment) {
      uni.showToast({
        title: `正在导出${segment.name}数据`,
        icon: 'none'
      });
    },
    
    // 绘制分群趋势图表
    drawSegmentChart() {
      // 在实际项目中，应该使用chart组件绘制图表
      // 这里只是模拟
      console.log('绘制图表，数据：', this.currentSegmentData.chartData);
      // 如果需要实现真实图表，可以使用uCharts、ECharts等图表库
    },
    
    // 查看RFM客群详情
    viewRfmSegment(cell) {
      uni.navigateTo({
        url: `/pages/customer/rfm-detail?name=${cell.name}&type=${cell.type}`
      });
    },
    
    // 切换标签分类
    switchTagCategory(categoryId) {
      this.currentTagCategory = categoryId;
    },
    
    // 查看标签客户列表
    viewTagCustomers(tag) {
      uni.navigateTo({
        url: `/pages/customer/tag-customers?tag=${tag.name}&count=${tag.count}`
      });
    },
    
    // 创建新标签
    createNewTag() {
      uni.navigateTo({
        url: '/pages/customer/create-tag'
      });
    },
    
    // 查看聊天记录
    viewConversation(message) {
      uni.navigateTo({
        url: `/pages/customer/conversation?id=${message.id}&name=${message.customerName}`
      });
    }
  }
}
</script>

<style lang="scss">
/* 页面容器 */
.customer-management-container {
  min-height: 100vh;
  background-color: #F5F7FA;
  display: flex;
  flex-direction: column;
}

/* 导航栏 */
.navbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 44px 16px 10px;
  background: linear-gradient(135deg, #1677FF, #065DD2);
  position: relative;
  z-index: 100;
}

.navbar-back {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
}

.back-icon {
  width: 12px;
  height: 12px;
  border-left: 2px solid #fff;
  border-bottom: 2px solid #fff;
  transform: rotate(45deg);
}

.navbar-title {
  color: #fff;
  font-size: 18px;
  font-weight: 600;
  flex: 1;
  text-align: center;
}

.navbar-right {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.help-icon {
  width: 24px;
  height: 24px;
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  font-size: 14px;
  font-weight: 600;
}

/* 客户数据概览 */
.data-overview {
  background: #fff;
  border-radius: 10px;
  margin: 15px;
  padding: 15px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.03);
}

.overview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.overview-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.date-selector {
  display: flex;
  align-items: center;
  font-size: 14px;
  color: #666;
}

.selector-icon {
  width: 12px;
  height: 12px;
  margin-left: 5px;
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%23666666"><path d="M7 10l5 5 5-5z"/></svg>');
  background-repeat: no-repeat;
  background-size: cover;
}

.metric-cards {
  display: flex;
  flex-wrap: wrap;
  margin: -5px;
}

.metric-card {
  width: calc(50% - 10px);
  background: #F8FAFC;
  border-radius: 10px;
  padding: 15px;
  margin: 5px;
  display: flex;
  align-items: center;
}

.metric-icon {
  width: 40px;
  height: 40px;
  border-radius: 20px;
  margin-right: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.metric-icon.customers {
  background: linear-gradient(135deg, #3498DB, #2980B9);
}

.metric-icon.new {
  background: linear-gradient(135deg, #2ECC71, #27AE60);
}

.metric-icon.active {
  background: linear-gradient(135deg, #F39C12, #E67E22);
}

.metric-icon.repurchase {
  background: linear-gradient(135deg, #9B59B6, #8E44AD);
}

.metric-data {
  flex: 1;
}

.metric-value {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin-bottom: 5px;
  display: block;
}

.metric-label {
  font-size: 12px;
  color: #999;
}

.metric-trend {
  display: flex;
  align-items: center;
  font-size: 12px;
  margin-left: 5px;
}

.metric-trend.up {
  color: #52C41A;
}

.metric-trend.down {
  color: #FF4D4F;
}

.trend-icon {
  width: 12px;
  height: 12px;
  margin-right: 2px;
}

.metric-trend.up .trend-icon {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%2352C41A"><path d="M7 14l5-5 5 5z"/></svg>');
  background-repeat: no-repeat;
  background-size: cover;
}

.metric-trend.down .trend-icon {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%23FF4D4F"><path d="M7 10l5 5 5-5z"/></svg>');
  background-repeat: no-repeat;
  background-size: cover;
}

/* 通用区块样式 */
.customer-segments, .lifecycle-section, .rfm-section, .tag-section, .interaction-section {
  background: #fff;
  border-radius: 10px;
  margin: 15px;
  padding: 15px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.03);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.section-action {
  font-size: 14px;
  color: #1677FF;
}

/* 客户分群标签页 */
.segment-tabs {
  display: flex;
  overflow-x: auto;
  white-space: nowrap;
  margin-bottom: 15px;
  scrollbar-width: none; /* Firefox */
}

.segment-tabs::-webkit-scrollbar {
  display: none; /* Chrome/Safari/Opera */
}

.segment-tab {
  padding: 8px 16px;
  margin-right: 10px;
  font-size: 14px;
  color: #666;
  background: #F5F7FA;
  border-radius: 16px;
  flex-shrink: 0;
}

.segment-tab.active {
  background: #F0F7FF;
  color: #1677FF;
  font-weight: 500;
}

.segment-info-card {
  background: #F8FAFC;
  border-radius: 10px;
  padding: 15px;
}

.segment-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.segment-title-container {
  display: flex;
  align-items: center;
}

.segment-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-right: 8px;
}

.segment-tag {
  padding: 2px 8px;
  border-radius: 10px;
  font-size: 12px;
}

.segment-tag.high-value {
  background: #E6F7FF;
  color: #1677FF;
}

.segment-tag.active {
  background: #F6FFED;
  color: #52C41A;
}

.segment-tag.new {
  background: #FCF5EB;
  color: #FA8C16;
}

.segment-tag.risk {
  background: #FFF1F0;
  color: #FF4D4F;
}

.segment-tag.low {
  background: #F5F5F5;
  color: #999;
}

.segment-action {
  display: flex;
  align-items: center;
  font-size: 14px;
  color: #1677FF;
}

.action-icon {
  width: 12px;
  height: 12px;
  margin-left: 5px;
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%231677FF"><path d="M8.59 16.59L13.17 12 8.59 7.41 10 6l6 6-6 6-1.41-1.41z"/></svg>');
  background-repeat: no-repeat;
  background-size: cover;
}

.segment-metrics {
  display: flex;
  margin-bottom: 15px;
}

.segment-metric {
  flex: 1;
  text-align: center;
  padding: 0 10px;
}

.segment-metric:not(:last-child) {
  border-right: 1px solid #EAEAEA;
}

.segment-chart {
  height: 200px;
  margin-bottom: 15px;
  position: relative;
}

.chart-canvas {
  width: 100%;
  height: 100%;
}

.chart-title {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  text-align: center;
  font-size: 12px;
  color: #999;
}

.segment-traits {
  margin-bottom: 15px;
}

.traits-title {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin-bottom: 10px;
  display: block;
}

.traits-tags {
  display: flex;
  flex-wrap: wrap;
}

.trait-tag {
  padding: 6px 12px;
  background: #F5F7FA;
  border-radius: 16px;
  font-size: 12px;
  color: #666;
  margin: 0 8px 8px 0;
}

.segment-actions {
  display: flex;
}

.action-btn {
  flex: 1;
  height: 40px;
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  margin: 0 5px;
}

.action-btn.detail {
  background: #F0F7FF;
  color: #1677FF;
}

.action-btn.export {
  background: #F5F5F5;
  color: #666;
}

/* 客户生命周期 */
.lifecycle-card {
  background: #F8FAFC;
  border-radius: 10px;
  padding: 15px;
}

.lifecycle-chart {
  margin-bottom: 15px;
}

.lifecycle-image {
  width: 100%;
  height: auto;
  max-height: 180px;
  border-radius: 8px;
}

.lifecycle-metrics {
  display: flex;
  flex-wrap: wrap;
  margin: -5px;
}

.lifecycle-stage {
  width: calc(50% - 10px);
  margin: 5px;
  background: #fff;
  border-radius: 8px;
  padding: 10px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.02);
}

.stage-header {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.stage-icon {
  width: 16px;
  height: 16px;
  border-radius: 8px;
  margin-right: 8px;
}

.stage-icon.new {
  background: #52C41A;
}

.stage-icon.growing {
  background: #1677FF;
}

.stage-icon.mature {
  background: #722ED1;
}

.stage-icon.risk {
  background: #FF4D4F;
}

.stage-name {
  font-size: 14px;
  color: #666;
}

.stage-data {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.stage-value {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.stage-trend {
  display: flex;
  align-items: center;
  font-size: 12px;
}

.stage-trend.up {
  color: #52C41A;
}

.stage-trend.down {
  color: #FF4D4F;
}

.trend-value {
  font-size: 12px;
}

.lifecycle-insights {
  margin-top: 15px;
  padding: 10px;
  background: #F0F7FF;
  border-radius: 8px;
  display: flex;
  align-items: flex-start;
}

.insight-icon {
  width: 20px;
  height: 20px;
  margin-right: 10px;
  flex-shrink: 0;
  margin-top: 2px;
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%231677FF"><path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 15c-.55 0-1-.45-1-1v-4c0-.55.45-1 1-1s1 .45 1 1v4c0 .55-.45 1-1 1zm1-8h-2V7h2v2z"/></svg>');
  background-repeat: no-repeat;
  background-size: cover;
}

.insight-text {
  font-size: 14px;
  color: #1677FF;
  line-height: 1.5;
}

/* RFM 价值矩阵 */
.rfm-card {
  background: #F8FAFC;
  border-radius: 10px;
  padding: 15px;
}

.rfm-matrix {
  display: flex;
  flex-direction: column;
  margin-bottom: 15px;
}

.rfm-row {
  display: flex;
  height: 80px;
  margin-bottom: 10px;
}

.rfm-row:last-child {
  margin-bottom: 0;
}

.rfm-cell {
  flex: 1;
  margin: 0 5px;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.rfm-cell.high {
  background: linear-gradient(135deg, #E6F7FF, #D4EFFF);
}

.rfm-cell.medium {
  background: linear-gradient(135deg, #F0F7FF, #E8F4FF);
}

.rfm-cell.low {
  background: linear-gradient(135deg, #F9F9F9, #F5F5F5);
}

.rfm-cell.potential {
  background: linear-gradient(135deg, #FCF5EB, #FAEBD7);
}

.cell-name {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin-bottom: 5px;
}

.cell-value {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.rfm-legend {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
  flex-wrap: wrap;
}

.legend-title {
  font-size: 14px;
  color: #666;
  margin-right: 10px;
}

.legend-item {
  display: flex;
  align-items: center;
  margin-right: 15px;
  margin-bottom: 5px;
}

.legend-color {
  width: 12px;
  height: 12px;
  border-radius: 3px;
  margin-right: 5px;
}

.legend-color.high {
  background: #1677FF;
}

.legend-color.medium {
  background: #52C41A;
}

.legend-color.low {
  background: #BFBFBF;
}

.legend-color.potential {
  background: #FA8C16;
}

.legend-text {
  font-size: 12px;
  color: #666;
}

.rfm-desc {
  background: #fff;
  border-radius: 8px;
  padding: 10px;
}

.desc-title {
  font-size: 14px;
  font-weight: 500;
  color: #333;
}

.desc-text {
  font-size: 12px;
  color: #666;
  line-height: 1.5;
}

/* 客户标签管理 */
.tag-card {
  background: #F8FAFC;
  border-radius: 10px;
  padding: 15px;
}

.tag-categories {
  display: flex;
  overflow-x: auto;
  white-space: nowrap;
  margin-bottom: 15px;
  scrollbar-width: none;
}

.tag-categories::-webkit-scrollbar {
  display: none;
}

.tag-category {
  padding: 8px 16px;
  margin-right: 10px;
  font-size: 14px;
  color: #666;
  background: #fff;
  border-radius: 16px;
  flex-shrink: 0;
}

.tag-category.active {
  background: #1677FF;
  color: #fff;
}

.tag-cloud {
  display: flex;
  flex-wrap: wrap;
  padding: 10px;
  background: #fff;
  border-radius: 8px;
  margin-bottom: 15px;
}

.tag-item {
  padding: 6px 12px;
  background: #F5F7FA;
  border-radius: 16px;
  margin: 5px;
  font-size: 12px;
  color: #666;
  display: flex;
  align-items: center;
}

.tag-item.xsmall {
  font-size: 10px;
}

.tag-item.small {
  font-size: 12px;
}

.tag-item.medium {
  font-size: 14px;
}

.tag-item.large {
  font-size: 16px;
  font-weight: 500;
}

.tag-item.xlarge {
  font-size: 18px;
  font-weight: 600;
}

.tag-count {
  font-size: 10px;
  color: #999;
  margin-left: 5px;
}

.tag-footer {
  display: flex;
  justify-content: center;
}

.create-tag-btn {
  display: flex;
  align-items: center;
  padding: 8px 16px;
  background: #1677FF;
  border-radius: 16px;
  color: #fff;
}

.btn-icon {
  width: 16px;
  height: 16px;
  margin-right: 5px;
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="white"><path d="M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z"/></svg>');
  background-repeat: no-repeat;
  background-size: cover;
}

.btn-text {
  font-size: 14px;
  font-weight: 500;
}

/* 客户互动 */
.message-stats {
  display: flex;
  margin-bottom: 15px;
}

.message-stat-item {
  flex: 1;
  text-align: center;
}

.stat-value {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  display: block;
  margin-bottom: 5px;
}

.stat-label {
  font-size: 12px;
  color: #999;
}

.recent-messages {
  background: #F8FAFC;
  border-radius: 10px;
  padding: 15px;
}

.message-item {
  display: flex;
  align-items: center;
  padding: 10px;
  background: #fff;
  border-radius: 8px;
  margin-bottom: 10px;
  position: relative;
}

.customer-avatar {
  width: 40px;
  height: 40px;
  border-radius: 20px;
  margin-right: 10px;
}

.message-content {
  flex: 1;
  min-width: 0;
}

.message-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 5px;
}

.customer-name {
  font-size: 14px;
  font-weight: 500;
  color: #333;
}

.message-time {
  font-size: 12px;
  color: #999;
}

.message-preview {
  font-size: 12px;
  color: #666;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 200px;
}

.message-badge {
  position: absolute;
  top: 10px;
  right: 10px;
  min-width: 18px;
  height: 18px;
  border-radius: 9px;
  background: #FF4D4F;
  color: #fff;
  font-size: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 6px;
}

.interaction-footer {
  display: flex;
  justify-content: space-between;
  margin-top: 15px;
}

.action-btn {
  flex: 1;
  height: 40px;
  margin: 0 5px;
  background: #fff;
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.btn-icon {
  width: 16px;
  height: 16px;
  margin-right: 5px;
}

.btn-icon.message {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%231677FF"><path d="M20 2H4c-1.1 0-1.99.9-1.99 2L2 22l4-4h14c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm-2 12H6v-2h12v2zm0-3H6V9h12v2zm0-3H6V6h12v2z"/></svg>');
  background-repeat: no-repeat;
  background-size: cover;
}

.btn-icon.notify {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%231677FF"><path d="M12 22c1.1 0 2-.9 2-2h-4c0 1.1.89 2 2 2zm6-6v-5c0-3.07-1.64-5.64-4.5-6.32V4c0-.83-.67-1.5-1.5-1.5s-1.5.67-1.5 1.5v.68C7.63 5.36 6 7.92 6 11v5l-2 2v1h16v-1l-2-2z"/></svg>');
  background-repeat: no-repeat;
  background-size: cover;
}

.btn-icon.survey {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%231677FF"><path d="M11 7h2v2h-2zm0 4h2v6h-2zm1-9C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8z"/></svg>');
  background-repeat: no-repeat;
  background-size: cover;
}

.btn-text {
  font-size: 14px;
  color: #1677FF;
}
</style> 