{"version": 3, "file": "toppedInfo.js", "sources": ["mock/info/toppedInfo.js"], "sourcesContent": ["// 置顶信息列表模拟数据\r\nexport const toppedInfoList = [\r\n  {\r\n    id: 'topped-job-1', \r\n    category: '招聘信息', \r\n    content: '急招会计1名，五险一金，双休，2年以上工作经验，薪资4500-6000', \r\n    time: '2024-05-15 14:30', \r\n    views: 208, \r\n    pageType: 'job-detail', \r\n    isTopped: true,\r\n    topType: 'paid',\r\n    topExpiry: '2024-05-20'\r\n  },\r\n  {\r\n    id: 'topped-house-1', \r\n    category: '房屋出租',\r\n    content: '市中心精装两室一厅出租，家电齐全，拎包入住，交通便利，周边配套设施完善', \r\n    time: '2024-05-14 10:20', \r\n    views: 197, \r\n    pageType: 'house-rent-detail', \r\n    isTopped: true,\r\n    topType: 'ad',\r\n    topExpiry: '2024-05-22',\r\n    images: ['/static/images/tabbar/wxacode.jpg', '/static/images/tabbar/wxacode.jpg']\r\n  },\r\n  {\r\n    id: 'topped-service-1', \r\n    category: '到家服务', \r\n    content: '专业家政服务，保洁、月嫂、育儿嫂、老人陪护，持证上岗，服务优质', \r\n    time: '2024-05-15 16:20', \r\n    views: 178, \r\n    pageType: 'home-service-detail', \r\n    isTopped: true,\r\n    topType: 'paid',\r\n    topExpiry: '2024-05-25'\r\n  },\r\n  {\r\n    id: 'topped-business-1', \r\n    category: '生意转让', \r\n    content: '繁华商圈饭店转让，160平米，接手即可营业，因家中有事急转', \r\n    time: '2024-05-16 09:15', \r\n    views: 255, \r\n    pageType: 'business-transfer-detail', \r\n    isTopped: true,\r\n    topType: 'paid',\r\n    topExpiry: '2024-05-23'\r\n  },\r\n  {\r\n    id: 'topped-car-1', \r\n    category: '二手车辆', \r\n    content: '2022年丰田凯美瑞2.5L混动，行驶1.2万公里，无事故无泡水，可分期', \r\n    time: '2024-05-14 11:30', \r\n    views: 221, \r\n    pageType: 'car-detail', \r\n    isTopped: true,\r\n    topType: 'ad',\r\n    topExpiry: '2024-05-21',\r\n    images: ['/static/images/tabbar/wxacode.jpg']\r\n  }\r\n];\r\n\r\n// 模拟获取置顶信息的API函数\r\nexport const fetchToppedInfo = () => {\r\n  return new Promise((resolve) => {\r\n    setTimeout(() => {\r\n      resolve(toppedInfoList);\r\n    }, 300);\r\n  });\r\n}; "], "names": [], "mappings": ";AACO,MAAM,iBAAiB;AAAA,EAC5B;AAAA,IACE,IAAI;AAAA,IACJ,UAAU;AAAA,IACV,SAAS;AAAA,IACT,MAAM;AAAA,IACN,OAAO;AAAA,IACP,UAAU;AAAA,IACV,UAAU;AAAA,IACV,SAAS;AAAA,IACT,WAAW;AAAA,EACZ;AAAA,EACD;AAAA,IACE,IAAI;AAAA,IACJ,UAAU;AAAA,IACV,SAAS;AAAA,IACT,MAAM;AAAA,IACN,OAAO;AAAA,IACP,UAAU;AAAA,IACV,UAAU;AAAA,IACV,SAAS;AAAA,IACT,WAAW;AAAA,IACX,QAAQ,CAAC,qCAAqC,mCAAmC;AAAA,EAClF;AAAA,EACD;AAAA,IACE,IAAI;AAAA,IACJ,UAAU;AAAA,IACV,SAAS;AAAA,IACT,MAAM;AAAA,IACN,OAAO;AAAA,IACP,UAAU;AAAA,IACV,UAAU;AAAA,IACV,SAAS;AAAA,IACT,WAAW;AAAA,EACZ;AAAA,EACD;AAAA,IACE,IAAI;AAAA,IACJ,UAAU;AAAA,IACV,SAAS;AAAA,IACT,MAAM;AAAA,IACN,OAAO;AAAA,IACP,UAAU;AAAA,IACV,UAAU;AAAA,IACV,SAAS;AAAA,IACT,WAAW;AAAA,EACZ;AAAA,EACD;AAAA,IACE,IAAI;AAAA,IACJ,UAAU;AAAA,IACV,SAAS;AAAA,IACT,MAAM;AAAA,IACN,OAAO;AAAA,IACP,UAAU;AAAA,IACV,UAAU;AAAA,IACV,SAAS;AAAA,IACT,WAAW;AAAA,IACX,QAAQ,CAAC,mCAAmC;AAAA,EAC7C;AACH;AAGY,MAAC,kBAAkB,MAAM;AACnC,SAAO,IAAI,QAAQ,CAAC,YAAY;AAC9B,eAAW,MAAM;AACf,cAAQ,cAAc;AAAA,IACvB,GAAE,GAAG;AAAA,EACV,CAAG;AACH;;"}