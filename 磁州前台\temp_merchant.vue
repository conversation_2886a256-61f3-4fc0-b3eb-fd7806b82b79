<template>
  <view class="merchant-container">
    <!-- 自定义导航栏 -->
    <view class="custom-navbar" :style="{ paddingTop: statusBarHeight + 'px' }">
      <view class="navbar-left" @click="goBack">
        <image src="/static/images/tabbar/返回键.png" class="back-icon"></image>
      </view>
      <view class="navbar-title">商家后台</view>
      <view class="navbar-right" @click="navigateToSettings">
        <image src="/static/images/tabbar/设置.png" class="setting-icon"></image>
      </view>
    </view>
    
    <!-- 商家信息卡片 -->
    <view class="merchant-card" :style="{ marginTop: (navbarHeight + 10) + 'px' }">
      <view class="merchant-header">
        <image class="merchant-logo" :src="merchantInfo.logo || '/static/images/default-shop.png'" mode="aspectFill"></image>
        <view class="merchant-info">
          <view class="merchant-name">{{ merchantInfo.name || '未认证商家' }}</view>
          <view class="merchant-status" :class="{'verified': merchantInfo.verified}">
            <image class="status-icon" :src="merchantInfo.verified ? '/static/images/tabbar/已认证.png' : '/static/images/tabbar/未认证.png'"></image>
            <text class="status-text">{{ merchantInfo.verified ? '已认证' : '未认证' }}</text>
          </view>
        </view>
        <view class="merchant-action">
          <button v-if="!merchantInfo.verified" class="verify-btn" @click="navigateToVerify">立即认证</button>
          <button v-else class="edit-btn" @click="navigateToMerchantEdit">编辑资料</button>
        </view>
      </view>
      
      <view class="merchant-stats">
        <view class="stat-item">
          <view class="stat-number">{{ statsData.views }}</view>
          <view class="stat-label">今日浏览</view>
        </view>
        <view class="stat-divider"></view>
        <view class="stat-item">
          <view class="stat-number">{{ statsData.leads }}</view>
          <view class="stat-label">咨询量</view>
        </view>
        <view class="stat-divider"></view>
        <view class="stat-item">
          <view class="stat-number">{{ statsData.income }}</view>
          <view class="stat-label">本月收入</view>
        </view>
      </view>
    </view>
    
    <!-- 待处理事项 -->
    <view class="pending-section">
      <view class="section-header">
        <text class="section-title">待处理事项</text>
        <text class="section-more" @click="navigateTo('/subPackages/merchant-plugin/pages/todo')">查看全部</text>
      </view>
      
      <view class="pending-list">
        <view v-if="pendingItems.length > 0">
          <view class="pending-item" v-for="(item, index) in pendingItems" :key="index" @click="handlePendingItem(item)">
            <view class="pending-left">
              <text class="pending-badge" :class="'badge-' + item.type">{{ item.count }}</text>
        </view>
            <view class="pending-center">
              <view class="pending-title">{{ item.title }}</view>
              <view class="pending-desc">{{ item.desc }}</view>
        </view>
            <view class="pending-right">
              <text class="action-btn">处理</text>
        </view>
        </view>
        </view>
        <view v-else class="empty-view">
          <image class="empty-icon" src="/static/images/empty-inbox.png" mode="aspectFit"></image>
          <view class="empty-text">暂无待处理事项</view>
        </view>
        </view>
        </view>
    
    <!-- 核心业务模块 -->
    <view class="core-business-section">
      <view class="section-header">
        <view class="title-wrapper">
          <view class="title-indicator title-indicator-core"></view>
          <text class="section-title">信息管理</text>
      </view>
        <text class="section-subtitle">管理您发布的信息内容</text>
    </view>
    
      <view class="core-grid">
        <view class="core-item" @click="navigateTo('/subPackages/merchant-plugin/pages/info-publish')">
          <view class="core-icon-wrapper bg-publish">
            <image class="core-icon" src="/static/images/tabbar/发布.png"></image>
          </view>
          <text class="core-name">发布信息</text>
        </view>
        
        <view class="core-item" @click="navigateTo('/subPackages/merchant-plugin/pages/info-manage')">
          <view class="core-icon-wrapper bg-manage">
            <image class="core-icon" src="/static/images/tabbar/管理.png"></image>
          </view>
          <text class="core-name">信息管理</text>
        </view>
        
        <view class="core-item" @click="navigateTo('/subPackages/merchant-plugin/pages/top-boost')">
          <view class="core-icon-wrapper bg-boost">
            <image class="core-icon" src="/static/images/tabbar/置顶.png"></image>
          </view>
          <text class="core-name">信息置顶</text>
        </view>
        
        <view class="core-item" @click="navigateTo('/subPackages/merchant-plugin/pages/reviews')">
          <view class="core-icon-wrapper bg-reviews">
            <image class="core-icon" src="/static/images/tabbar/评价.png"></image>
          </view>
          <text class="core-name">评价留言</text>
        </view>
      </view>
    </view>
    
    <!-- 营销推广 -->
    <view class="marketing-section">
      <view class="section-header">
        <view class="title-wrapper">
          <view class="title-indicator title-indicator-marketing"></view>
          <text class="section-title">营销推广</text>
        </view>
        <text class="section-subtitle">提升店铺曝光与转化</text>
      </view>
      
      <view class="marketing-grid">
        <view class="marketing-item" @click="navigateTo('/subPackages/merchant-plugin/pages/marketing')">
          <view class="marketing-icon-wrapper bg-marketing">
            <image class="marketing-icon" src="/static/images/tabbar/营销.png"></image>
          </view>
          <text class="marketing-name">营销中心</text>
          </view>
        
        <view class="marketing-item" @click="navigateTo('/subPackages/merchant-plugin/pages/coupon')">
          <view class="marketing-icon-wrapper bg-coupon">
            <image class="marketing-icon" src="/static/images/tabbar/优惠券.png"></image>
          </view>
          <text class="marketing-name">优惠券</text>
        </view>
        
        <view class="marketing-item" @click="navigateTo('/subPackages/merchant-plugin/pages/red-packet-manage')">
          <view class="marketing-icon-wrapper bg-redpacket">
            <image class="marketing-icon" src="/static/images/tabbar/红包.png"></image>
          </view>
          <text class="marketing-name">红包</text>
        </view>
        
        <view class="marketing-item" @click="navigateTo('/subPackages/merchant-plugin/pages/poster')">
          <view class="marketing-icon-wrapper bg-poster">
            <image class="marketing-icon" src="/static/images/tabbar/海报.png"></image>
          </view>
          <text class="marketing-name">宣传海报</text>
        </view>
      </view>
        </view>
        
    <!-- 经营分析 -->
    <view class="analysis-section">
      <view class="section-header">
        <view class="title-wrapper">
          <view class="title-indicator title-indicator-analysis"></view>
          <text class="section-title">经营分析</text>
          </view>
        <text class="section-subtitle">了解店铺经营数据</text>
        </view>
        
      <view class="analysis-grid">
        <view class="analysis-item" @click="navigateTo('/subPackages/merchant-plugin/pages/analysis')">
          <view class="analysis-icon-wrapper bg-analysis">
            <image class="analysis-icon" src="/static/images/tabbar/数据.png"></image>
          </view>
          <text class="analysis-name">数据分析</text>
        </view>
        
        <view class="analysis-item" @click="navigateTo('/subPackages/merchant-plugin/pages/finance')">
          <view class="analysis-icon-wrapper bg-finance">
            <image class="analysis-icon" src="/static/images/tabbar/财务.png"></image>
          </view>
          <text class="analysis-name">财务管理</text>
        </view>
        
        <view class="analysis-item" @click="navigateTo('/subPackages/merchant-plugin/pages/visitor')">
          <view class="analysis-icon-wrapper bg-visitor">
            <image class="analysis-icon" src="/static/images/tabbar/访客.png"></image>
      </view>
          <text class="analysis-name">访客统计</text>
    </view>
    
        <view class="analysis-item" @click="navigateTo('/subPackages/merchant-plugin/pages/leads')">
          <view class="analysis-icon-wrapper bg-leads">
            <image class="analysis-icon" src="/static/images/tabbar/咨询.png"></image>
          </view>
          <text class="analysis-name">咨询管理</text>
        </view>
      </view>
    </view>
    
    <!-- 快捷工具 -->
    <view class="tools-section">
      <view class="section-header">
        <view class="title-wrapper">
          <view class="title-indicator title-indicator-tools"></view>
          <text class="section-title">店铺工具</text>
        </view>
        <text class="section-subtitle">提高管理效率</text>
      </view>
      
      <view class="tools-grid">
        <view class="tool-card" @click="navigateTo('/subPackages/merchant-plugin/pages/qrcode')">
          <view class="tool-card-icon bg-qrcode">
            <image src="/static/images/tabbar/二维码.png"></image>
            </view>
          <view class="tool-card-name">店铺码</view>
            </view>
        
        <view class="tool-card" @click="navigateTo('/subPackages/merchant-plugin/pages/staff')">
          <view class="tool-card-icon bg-staff">
            <image src="/static/images/tabbar/员工.png"></image>
            </view>
          <view class="tool-card-name">员工管理</view>
          </view>
        
        <view class="tool-card" @click="navigateTo('/subPackages/merchant-plugin/pages/category-manage')">
          <view class="tool-card-icon bg-category">
            <image src="/static/images/tabbar/分类.png"></image>
        </view>
          <view class="tool-card-name">分类管理</view>
        </view>
        
        <view class="tool-card" @click="navigateTo('/subPackages/merchant-plugin/pages/settings')">
          <view class="tool-card-icon bg-settings">
            <image src="/static/images/tabbar/设置.png"></image>
          </view>
          <view class="tool-card-name">店铺设置</view>
        </view>
      </view>
    </view>
    
    <!-- 行业专属功能 - 根据商家类目动态显示 -->
    <view class="industry-section" v-if="merchantCategory">
      <view class="section-header">
        <view class="section-title-large">{{ getCategoryTitle(merchantCategory) }}</view>
        <text class="section-more" @click="navigateTo('/subPackages/merchant-plugin/pages/industry-features')">更多</text>
      </view>
      
      <view class="industry-features">
        <template v-if="merchantCategory === 'food'">
          <!-- 美食小吃行业功能 -->
          <view class="industry-card" @click="navigateTo('/subPackages/merchant-plugin/pages/food/menu')">
            <image class="industry-card-bg" src="/static/images/food-menu.jpg" mode="aspectFill"></image>
            <view class="industry-card-content">
              <view class="industry-card-title">菜单管理</view>
              <view class="industry-card-desc">管理店铺菜品和价格</view>
            </view>
          </view>
        </template>
        
        <template v-else-if="merchantCategory === 'house'">
          <!-- 房产中介行业功能 -->
          <view class="industry-card" @click="navigateTo('/subPackages/merchant-plugin/pages/house/listing')">
            <image class="industry-card-bg" src="/static/images/house-listing.jpg" mode="aspectFill"></image>
            <view class="industry-card-content">
              <view class="industry-card-title">房源管理</view>
              <view class="industry-card-desc">管理出售/出租房源</view>
            </view>
          </view>
        </template>
        
        <template v-else-if="merchantCategory === 'decoration'">
          <!-- 装修家居行业功能 -->
          <view class="industry-card" @click="navigateTo('/subPackages/merchant-plugin/pages/decoration/case')">
            <image class="industry-card-bg" src="/static/images/decoration-case.jpg" mode="aspectFill"></image>
            <view class="industry-card-content">
              <view class="industry-card-title">案例展示</view>
              <view class="industry-card-desc">管理装修案例和效果图</view>
            </view>
          </view>
        </template>
        
        <template v-else-if="merchantCategory === 'service'">
          <!-- 到家服务行业功能 -->
          <view class="industry-card" @click="navigateTo('/subPackages/merchant-plugin/pages/services')">
            <image class="industry-card-bg" src="/static/images/service-manage.jpg" mode="aspectFill"></image>
            <view class="industry-card-content">
              <view class="industry-card-title">服务管理</view>
              <view class="industry-card-desc">管理上门服务项目</view>
            </view>
          </view>
        </template>
      </view>
    </view>
  </view>
</template>

<script>
import { smartNavigate } from '@/utils/navigation.js';

export default {
  data() {
    return {
      statusBarHeight: 20,
      navbarHeight: 64,
      merchantInfo: {
        logo: '',
        name: '',
        verified: false,
        merchantId: ''
      },
      statsData: {
        views: 0,
        leads: 0,
        income: '0.00'
      },
      pendingItems: [],
      merchantCategory: ''
    }
  },
  onLoad() {
    // 获取状态栏高度
    const sysInfo = uni.getSystemInfoSync();
    this.statusBarHeight = sysInfo.statusBarHeight;
    this.navbarHeight = this.statusBarHeight + 44;
    
    // 加载商家信息和数据
    this.loadMerchantInfo();
    this.loadStatsData();
    this.loadPendingItems();
  },
  
  onShareAppMessage() {
    // 自定义转发内容
    return {
      title: '商家后台 - 专业管理工具，助力商家成长',
      path: '/pages/my/merchant'
    }
  },
  
  methods: {
    // 返回上一页
    goBack() {
      uni.navigateBack();
    },
    
    // 跳转到设置页面
    navigateToSettings() {
      smartNavigate('/subPackages/merchant-plugin/pages/settings').catch(err => {
        console.error('跳转到设置页面失败:', err);
      });
    },
    
    // 页面跳转
    navigateTo(url) {
      // 直接跳转，不再拦截未认证商家
      smartNavigate(url).catch(err => {
        console.error('页面跳转失败:', err);
      });
    },
    
    // 跳转到商家认证页面
    navigateToVerify() {
      smartNavigate('/subPackages/merchant-plugin/pages/verify');
    },
    
    // 跳转到商家资料编辑页面
    navigateToMerchantEdit() {
      smartNavigate('/subPackages/merchant-plugin/pages/profile');
    },
    
    // 处理待办事项
    handlePendingItem(item) {
      const urlMap = {
        'order': '/subPackages/merchant-plugin/pages/orders',
        'comment': '/subPackages/merchant-plugin/pages/reviews',
        'finance': '/subPackages/merchant-plugin/pages/finance',
        'approval': '/subPackages/merchant-plugin/pages/approval'
      };
      
      if (urlMap[item.type]) {
        this.navigateTo(urlMap[item.type]);
      }
    },
    
    // 加载商家信息
    loadMerchantInfo() {
      // 模拟API请求
      setTimeout(() => {
        // 随机商家信息，实际应该从API获取
        const verified = Math.random() > 0.3; // 70%概率已认证
        
        this.merchantInfo = {
          logo: verified ? '/static/images/shop-logo.png' : '',
          name: verified ? '磁州特色小吃店' : '',
          verified: verified,
          merchantId: 'M' + Math.floor(Math.random() * 1000000).toString().padStart(6, '0')
        };
      }, 500);
    },
    
    // 加载数据统计
    loadStatsData() {
      // 模拟API请求
      setTimeout(() => {
        this.statsData = {
          views: Math.floor(Math.random() * 500),
          leads: Math.floor(Math.random() * 100),
          income: (Math.random() * 1000).toFixed(2)
        };
      }, 600);
    },
    
    // 加载待处理事项
    loadPendingItems() {
      // 模拟API请求
      setTimeout(() => {
        // 随机显示待处理事项，实际应该从API获取
        const havePendingItems = Math.random() > 0.3; // 70%概率有待处理事项
        
        if (havePendingItems) {
          this.pendingItems = [
            {
              type: 'service',
              count: Math.floor(Math.random() * 10) + 1,
              title: '待处理服务',
              desc: '有新的服务预约需要确认',
              url: '/subPackages/merchant-plugin/pages/service-orders'
            },
            {
              type: 'comment',
              count: Math.floor(Math.random() * 5) + 1,
              title: '新评价待回复',
              desc: '有客户的评价需要回复',
              url: '/subPackages/merchant-plugin/pages/reviews'
            },
            {
              type: 'finance',
              count: Math.floor(Math.random() * 3) + 1,
              title: '待结算账单',
              desc: '有账单需要确认结算',
              url: '/subPackages/merchant-plugin/pages/finance'
            },
            {
              type: 'info',
              count: Math.floor(Math.random() * 2) + 1,
              title: '信息即将到期',
              desc: '有信息需要更新或续期',
              url: '/subPackages/merchant-plugin/pages/info-manage'
            }
          ];
          
          // 随机显示0-3个待处理事项
          const count = Math.floor(Math.random() * 3) + 1;
          this.pendingItems = this.pendingItems.slice(0, count);
        } else {
          this.pendingItems = [];
        }
      }, 700);
    },
    
    // 获取商家类目标题
    getCategoryTitle(category) {
      const categoryTitles = {
        'food': '美食小吃',
        'house': '房产中介',
        'decoration': '装修家居',
        'service': '到家服务'
      };
      return categoryTitles[category] || '未分类商家';
    }
  }
}
</script>

<style>
.merchant-container {
  min-height: 100vh;
  background-color: #f8f9fa;
  padding-bottom: 40rpx;
}

/* 自定义导航栏 */
.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 44px;
  background: linear-gradient(135deg, #007AFF, #5856D6);
  color: #fff;
  display: flex;
  align-items: center;
  padding: 0 20px;
  z-index: 999;
  box-shadow: 0 2px 10px rgba(0, 122, 255, 0.2);
}

.navbar-left {
  width: 80rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
}

.back-icon {
  width: 40rpx;
  height: 40rpx;
}

.navbar-title {
  flex: 1;
  text-align: center;
  font-size: 18px;
  font-weight: 500;
  letter-spacing: 0.5px;
}

.navbar-right {
  width: 80rpx;
  display: flex;
  justify-content: flex-end;
}

.setting-icon {
  width: 40rpx;
  height: 40rpx;
}

/* 商家信息卡片 */
.merchant-card {
  background-color: #fff;
  margin: 0 30rpx 30rpx;
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.08);
  transform: translateZ(0);
}

.merchant-header {
  padding: 30rpx;
  display: flex;
  align-items: center;
  position: relative;
  background: linear-gradient(to right, rgba(255, 255, 255, 0.95), rgba(255, 255, 255, 0.85));
}

.merchant-logo {
  width: 120rpx;
  height: 120rpx;
  border-radius: 20rpx;
  background-color: #f5f7fa;
  margin-right: 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.merchant-info {
  flex: 1;
}

.merchant-name {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 10rpx;
}

.merchant-status {
  display: flex;
  align-items: center;
}

.status-icon {
  width: 28rpx;
  height: 28rpx;
  margin-right: 8rpx;
}

.status-text {
  font-size: 24rpx;
  color: #ff9800;
}

.verified .status-text {
  color: #34C759;
}

.merchant-action {
  margin-left: 30rpx;
}

.verify-btn {
  background: linear-gradient(135deg, #FF9500, #FF5E3A);
  color: #fff;
  font-size: 26rpx;
  padding: 10rpx 30rpx;
  border-radius: 30rpx;
  height: 60rpx;
  line-height: 40rpx;
  margin: 0;
  box-shadow: 0 4rpx 12rpx rgba(255, 94, 58, 0.2);
}

.edit-btn {
  background: linear-gradient(135deg, #007AFF, #5856D6);
  color: #fff;
  font-size: 26rpx;
  padding: 10rpx 30rpx;
  border-radius: 30rpx;
  height: 60rpx;
  line-height: 40rpx;
  margin: 0;
  box-shadow: 0 4rpx 12rpx rgba(0, 122, 255, 0.2);
}

/* 商家数据统计 */
.merchant-stats {
  display: flex;
  padding: 20rpx 0;
  border-top: 1rpx solid #f0f0f0;
  background: rgba(248, 249, 250, 0.5);
}

.stat-item {
  flex: 1;
  text-align: center;
  padding: 10rpx 0;
}

.stat-number {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 4rpx;
}

.stat-label {
  font-size: 24rpx;
  color: #999;
}

.stat-divider {
  width: 2rpx;
  background-color: #f0f0f0;
}

/* 待处理事项 */
.pending-section {
  background-color: #fff;
  margin: 0 30rpx 30rpx;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.05);
  transform: translateZ(0);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
}

.section-more {
  font-size: 26rpx;
  color: #007AFF;
}

.pending-list {
  background-color: #fff;
}

.pending-item {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f5f5f5;
  transition: all 0.3s;
}

.pending-item:active {
  background-color: #f8f9fa;
  transform: scale(0.98);
}

.pending-item:last-child {
  border-bottom: none;
}

.pending-left {
  margin-right: 20rpx;
}

.pending-badge {
  display: inline-block;
  min-width: 32rpx;
  height: 32rpx;
  border-radius: 16rpx;
  padding: 4rpx 12rpx;
  font-size: 24rpx;
  text-align: center;
  color: #fff;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.badge-order {
  background: linear-gradient(135deg, #FF3B30, #FF9500);
}

.badge-comment {
  background: linear-gradient(135deg, #FF9500, #FFCC00);
}

.badge-finance {
  background: linear-gradient(135deg, #007AFF, #5AC8FA);
}

.badge-approval {
  background: linear-gradient(135deg, #5856D6, #AF52DE);
}

.badge-service {
  background: linear-gradient(135deg, #34C759, #30B3FF);
}

.badge-info {
  background: linear-gradient(135deg, #FF9500, #FFCC00);
}

.pending-center {
  flex: 1;
}

.pending-title {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 6rpx;
}

.pending-desc {
  font-size: 24rpx;
  color: #999;
}

.pending-right {
  margin-left: 20rpx;
}

.action-btn {
  display: inline-block;
  padding: 6rpx 20rpx;
  border-radius: 30rpx;
  font-size: 24rpx;
  color: #007AFF;
  border: 1rpx solid #007AFF;
  background-color: rgba(0, 122, 255, 0.05);
  transition: all 0.3s;
}

.action-btn:active {
  transform: scale(0.95);
  background-color: rgba(0, 122, 255, 0.1);
}

/* 空状态 */
.empty-view {
  padding: 40rpx 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.empty-icon {
  width: 160rpx;
  height: 160rpx;
  margin-bottom: 20rpx;
  opacity: 0.7;
}

.empty-text {
  margin-top: 10px;
  font-size: 14px;
  color: #999;
}

/* 核心业务模块 */
.core-business-section {
  background-color: #fff;
  margin: 0 30rpx 30rpx;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.05);
  transform: translateZ(0);
}

.title-wrapper {
  display: flex;
  align-items: center;
}

.title-indicator {
  width: 6rpx;
  height: 30rpx;
  border-radius: 3rpx;
  margin-right: 15rpx;
}

.title-indicator-core {
  background: linear-gradient(to bottom, #FF9500, #FF5E3A);
}

.section-subtitle {
  font-size: 26rpx;
  color: #999;
  margin-top: 10rpx;
}

.core-grid {
  display: flex;
  flex-wrap: wrap;
  margin: 20rpx -10rpx 0;
}

.core-item {
  width: 25%;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 30rpx;
  padding: 0 10rpx;
  box-sizing: border-box;
  transition: all 0.3s;
}

.core-item:active {
  transform: scale(0.95);
}

.core-icon-wrapper {
  width: 100rpx;
  height: 100rpx;
  border-radius: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 15rpx;
  box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s;
}

.bg-publish {
  background: linear-gradient(135deg, #34C759, #30B3FF);
}

.bg-manage {
  background: linear-gradient(135deg, #007AFF, #5AC8FA);
}

.bg-boost {
  background: linear-gradient(135deg, #FF3B30, #FF9500);
}

.bg-reviews {
  background: linear-gradient(135deg, #5AC8FA, #34C759);
}

.core-icon {
  width: 50rpx;
  height: 50rpx;
}

.core-name {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

/* 快捷工具 */
.tools-section {
  background-color: #fff;
  margin: 0 30rpx 30rpx;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.05);
  transform: translateZ(0);
}

.title-indicator-tools {
  background: linear-gradient(to bottom, #34C759, #30B3FF);
}

.tools-grid {
  display: flex;
  flex-wrap: wrap;
  margin: 20rpx -10rpx 0;
}

.tool-card {
  position: relative;
  width: 25%;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 30rpx;
  padding: 0 10rpx;
  box-sizing: border-box;
  transition: all 0.3s;
}

.tool-card:active {
  transform: scale(0.95);
}

.tool-card-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 15rpx;
  box-shadow: 0 6rpx 16rpx rgba(0, 0, 0, 0.08);
}

.tool-card-icon image {
  width: 40rpx;
  height: 40rpx;
}

.bg-qrcode {
  background: linear-gradient(135deg, #007AFF, #5AC8FA);
}

.bg-poster {
  background: linear-gradient(135deg, #FFCC00, #FF9500);
}

.bg-analysis {
  background: linear-gradient(135deg, #5856D6, #AF52DE);
}

.bg-staff {
  background: linear-gradient(135deg, #34C759, #30B3FF);
}

.bg-settings {
  background: linear-gradient(135deg, #8E8E93, #636366);
}

.tool-card-name {
  font-size: 26rpx;
  color: #333;
  text-align: center;
}

.tool-card-tag {
  position: absolute;
  top: -8rpx;
  right: -8rpx;
  font-size: 20rpx;
  color: #fff;
  padding: 2rpx 10rpx;
  border-radius: 16rpx;
  background: linear-gradient(135deg, #FF3B30, #FF9500);
  box-shadow: 0 2rpx 8rpx rgba(255, 59, 48, 0.3);
  z-index: 1;
}

/* 运营专区 */
.operation-section {
  background-color: #fff;
  margin: 0 30rpx 30rpx;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.05);
  transform: translateZ(0);
}

.section-title-large {
  font-size: 34rpx;
  font-weight: 600;
  color: #333;
  position: relative;
  padding-left: 24rpx;
}

.section-title-large::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 8rpx;
  height: 34rpx;
  background: linear-gradient(to bottom, #007AFF, #5856D6);
  border-radius: 4rpx;
}

.operation-cards {
  margin-top: 30rpx;
}

.operation-card {
  position: relative;
  width: 100%;
  height: 240rpx;
  border-radius: 20rpx;
  overflow: hidden;
  margin-bottom: 30rpx;
  box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.12);
  transform: translateZ(0);
  transition: all 0.3s;
}

.operation-card:active {
  transform: scale(0.98);
}

.operation-card-bg {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.operation-card-content {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 30rpx;
  background: linear-gradient(to top, rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0));
}

.operation-card-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #fff;
  margin-bottom: 10rpx;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.operation-card-desc {
  font-size: 26rpx;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 15rpx;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.operation-card-btn {
  display: inline-block;
  padding: 8rpx 24rpx;
  border-radius: 30rpx;
  font-size: 24rpx;
  color: #fff;
  background: rgba(255, 255, 255, 0.25);
  backdrop-filter: blur(10px);
  border: 1rpx solid rgba(255, 255, 255, 0.5);
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
  transition: all 0.3s;
}

/* 行业专属功能 */
.industry-section {
  background-color: #fff;
  margin: 0 30rpx 30rpx;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.05);
  transform: translateZ(0);
}

.industry-features {
  margin-top: 30rpx;
}

.industry-card {
  position: relative;
  width: 100%;
  height: 240rpx;
  border-radius: 20rpx;
  overflow: hidden;
  margin-bottom: 30rpx;
  box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.12);
  transform: translateZ(0);
  transition: all 0.3s;
}

.industry-card:active {
  transform: scale(0.98);
}

.industry-card-bg {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.industry-card-content {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 30rpx;
  background: linear-gradient(to top, rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0));
}

.industry-card-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #fff;
  margin-bottom: 10rpx;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.industry-card-desc {
  font-size: 26rpx;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 15rpx;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

/* 新增背景色定义 */
.bg-publish {
  background: linear-gradient(135deg, #34C759, #30B3FF);
}

.bg-manage {
  background: linear-gradient(135deg, #007AFF, #5AC8FA);
}

.bg-visitor {
  background: linear-gradient(135deg, #FF9500, #FFCC00);
}

.bg-leads {
  background: linear-gradient(135deg, #5AC8FA, #34C759);
}

.bg-category {
  background: linear-gradient(135deg, #AF52DE, #5856D6);
}

.title-indicator-marketing {
  background: linear-gradient(to bottom, #FF9500, #FF5E3A);
}

.title-indicator-analysis {
  background: linear-gradient(to bottom, #5856D6, #AF52DE);
}

/* 营销推广样式 */
.marketing-section {
  background-color: #fff;
  margin: 0 30rpx 30rpx;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.05);
  transform: translateZ(0);
}

.marketing-grid {
  display: flex;
  flex-wrap: wrap;
  margin: 20rpx -10rpx 0;
}

.marketing-item {
  width: 25%;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 30rpx;
  padding: 0 10rpx;
  box-sizing: border-box;
  transition: all 0.3s;
}

.marketing-item:active {
  transform: scale(0.95);
}

.marketing-icon-wrapper {
  width: 100rpx;
  height: 100rpx;
  border-radius: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 15rpx;
  box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s;
}

.marketing-icon {
  width: 50rpx;
  height: 50rpx;
}

.marketing-name {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

/* 经营分析样式 */
.analysis-section {
  background-color: #fff;
  margin: 0 30rpx 30rpx;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.05);
  transform: translateZ(0);
}

.analysis-grid {
  display: flex;
  flex-wrap: wrap;
  margin: 20rpx -10rpx 0;
}

.analysis-item {
  width: 25%;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 30rpx;
  padding: 0 10rpx;
  box-sizing: border-box;
  transition: all 0.3s;
}

.analysis-item:active {
  transform: scale(0.95);
}

.analysis-icon-wrapper {
  width: 100rpx;
  height: 100rpx;
  border-radius: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 15rpx;
  box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s;
}

.analysis-icon {
  width: 50rpx;
  height: 50rpx;
}

.analysis-name {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.bg-product {
  background: linear-gradient(135deg, #FF9500, #FF5E3A);
}

.bg-marketing {
  background: linear-gradient(135deg, #34C759, #30B3FF);
}

.bg-finance {
  background: linear-gradient(135deg, #5856D6, #AF52DE);
}

.bg-coupon {
  background: linear-gradient(135deg, #FF9500, #FFCC00);
}

.bg-redpacket {
  background: linear-gradient(135deg, #FF3B30, #FF5E3A);
}

.bg-reviews {
  background: linear-gradient(135deg, #5AC8FA, #34C759);
}
</style>

