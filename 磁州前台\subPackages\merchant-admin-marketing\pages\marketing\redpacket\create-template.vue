<template>
  <view class="page-container">
    <!-- 自定义导航栏 -->
    <view class="navbar">
      <view class="navbar-back" @click="goBack">
        <view class="back-icon"></view>
      </view>
      <text class="navbar-title">红包模板</text>
      <view class="navbar-right">
        <view class="help-icon" @click="showHelp">?</view>
      </view>
    </view>
    
    <!-- 模板列表 -->
    <view class="template-section">
      <view class="section-header">
        <text class="section-title">我的模板</text>
        <view class="add-btn" @click="createTemplate">
          <text class="btn-text">新建模板</text>
          <view class="plus-icon-small"></view>
        </view>
      </view>
      
      <view class="template-list">
        <view class="template-item" v-for="(item, index) in myTemplates" :key="index" @click="viewTemplateDetail(item)">
          <view class="template-preview" :style="{ background: item.color }">
            <view class="template-icon">
              <image class="icon-image" :src="item.icon" mode="aspectFit"></image>
            </view>
            <text class="template-name">{{item.name}}</text>
          </view>
          <view class="template-info">
            <text class="template-desc">{{item.description}}</text>
            <view class="template-type">{{item.typeText}}</view>
          </view>
          <view class="template-actions">
            <view class="action-btn" @click.stop="useTemplate(item)">使用</view>
            <view class="action-btn edit" @click.stop="editTemplate(item)">编辑</view>
            <view class="action-btn delete" @click.stop="deleteTemplate(item)">删除</view>
          </view>
        </view>
        
        <view class="empty-state" v-if="myTemplates.length === 0">
          <view class="empty-icon"></view>
          <text class="empty-text">暂无自定义模板</text>
          <view class="empty-action" @click="createTemplate">新建模板</view>
        </view>
      </view>
    </view>
    
    <!-- 官方模板 -->
    <view class="official-section">
      <view class="section-header">
        <text class="section-title">官方模板</text>
      </view>
      
      <view class="template-grid">
        <view class="template-card" v-for="(item, index) in officialTemplates" :key="index" @click="useTemplate(item)">
          <view class="template-preview" :style="{ background: item.color }">
            <view class="template-icon">
              <image class="icon-image" :src="item.icon" mode="aspectFit"></image>
            </view>
            <text class="template-name">{{item.name}}</text>
          </view>
          <view class="template-footer">
            <text class="template-desc">{{item.description}}</text>
            <view class="template-use-btn">使用</view>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 场景模板 -->
    <view class="scenario-section">
      <view class="section-header">
        <text class="section-title">场景模板</text>
      </view>
      
      <scroll-view scroll-x class="scenario-scroll" show-scrollbar="false">
        <view class="scenario-container">
          <view class="scenario-card" v-for="(item, index) in scenarioTemplates" :key="index" @click="useTemplate(item)">
            <view class="scenario-preview" :style="{ background: item.color }">
              <view class="scenario-icon">
                <image class="icon-image" :src="item.icon" mode="aspectFit"></image>
              </view>
              <text class="scenario-name">{{item.name}}</text>
              <view class="scenario-tag">{{item.tagText}}</view>
            </view>
            <view class="scenario-footer">
              <text class="scenario-desc">{{item.description}}</text>
              <view class="scenario-use-btn">使用</view>
            </view>
          </view>
        </view>
      </scroll-view>
    </view>
    
    <!-- 模板使用指南 -->
    <view class="guide-section">
      <view class="section-header">
        <text class="section-title">模板使用指南</text>
      </view>
      
      <view class="guide-steps">
        <view class="guide-step" v-for="(step, index) in guideSteps" :key="index">
          <view class="step-number">{{index + 1}}</view>
          <view class="step-content">
            <text class="step-title">{{step.title}}</text>
            <text class="step-desc">{{step.description}}</text>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 浮动操作按钮 -->
    <view class="floating-action-button" @click="createTemplate">
      <view class="fab-icon">+</view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      // 我的模板
      myTemplates: [
        {
          id: 1,
          name: '会员专享红包',
          description: '针对会员用户的专属优惠',
          icon: '/static/images/redpacket/template-icon-1.png',
          color: '#FF5858',
          type: 'normal',
          typeText: '普通红包'
        },
        {
          id: 2,
          name: '节日特惠红包',
          description: '节假日促销活动专用',
          icon: '/static/images/redpacket/template-icon-2.png',
          color: '#4ECDC4',
          type: 'fission',
          typeText: '裂变红包'
        }
      ],
      
      // 官方模板
      officialTemplates: [
        {
          id: 101,
          name: '新用户欢迎',
          description: '吸引新用户注册和首次消费',
          icon: '/static/images/redpacket/official-icon-1.png',
          color: '#FF5858'
        },
        {
          id: 102,
          name: '老用户回馈',
          description: '提高老用户复购率和忠诚度',
          icon: '/static/images/redpacket/official-icon-2.png',
          color: '#4ECDC4'
        },
        {
          id: 103,
          name: '生日特权',
          description: '会员生日专属优惠和祝福',
          icon: '/static/images/redpacket/official-icon-3.png',
          color: '#FFD166'
        },
        {
          id: 104,
          name: '满额立减',
          description: '刺激用户提高客单价',
          icon: '/static/images/redpacket/official-icon-4.png',
          color: '#6A0572'
        }
      ],
      
      // 场景模板
      scenarioTemplates: [
        {
          id: 201,
          name: '春节红包',
          description: '新春佳节送福利',
          icon: '/static/images/redpacket/scenario-icon-1.png',
          color: '#FF5858',
          tag: 'hot',
          tagText: '热门'
        },
        {
          id: 202,
          name: '店庆活动',
          description: '周年庆典专属优惠',
          icon: '/static/images/redpacket/scenario-icon-2.png',
          color: '#4ECDC4',
          tag: 'new',
          tagText: '新品'
        },
        {
          id: 203,
          name: '会员日特惠',
          description: '每月会员专属福利',
          icon: '/static/images/redpacket/scenario-icon-3.png',
          color: '#FFD166',
          tag: '',
          tagText: ''
        },
        {
          id: 204,
          name: '限时秒杀',
          description: '限时限量抢购活动',
          icon: '/static/images/redpacket/scenario-icon-4.png',
          color: '#6A0572',
          tag: '',
          tagText: ''
        }
      ],
      
      // 使用指南
      guideSteps: [
        {
          title: '选择模板',
          description: '从我的模板、官方模板或场景模板中选择适合的模板'
        },
        {
          title: '自定义设置',
          description: '根据实际需求修改红包金额、使用规则等参数'
        },
        {
          title: '保存并发布',
          description: '确认设置无误后保存并发布红包活动'
        },
        {
          title: '分享推广',
          description: '通过多种渠道分享红包活动，提高曝光度'
        }
      ]
    }
  },
  methods: {
    goBack() {
      uni.navigateBack();
    },
    showHelp() {
      uni.showModal({
        title: '红包模板帮助',
        content: '红包模板可以帮助您快速创建红包活动，节省设置时间。您可以使用官方模板，也可以创建和保存自己的模板。',
        showCancel: false
      });
    },
    createTemplate() {
      uni.showToast({
        title: '创建模板功能开发中',
        icon: 'none'
      });
    },
    viewTemplateDetail(item) {
      uni.showToast({
        title: '查看详情功能开发中',
        icon: 'none'
      });
    },
    useTemplate(item) {
      uni.showModal({
        title: '使用模板',
        content: `确定要使用"${item.name}"模板创建红包活动吗？`,
        success: (res) => {
          if (res.confirm) {
            uni.navigateTo({
              url: '/subPackages/merchant-admin-marketing/pages/marketing/redpacket/create'
            });
          }
        }
      });
    },
    editTemplate(item) {
      uni.showToast({
        title: '编辑模板功能开发中',
        icon: 'none'
      });
    },
    deleteTemplate(item) {
      uni.showModal({
        title: '删除确认',
        content: `确定要删除"${item.name}"模板吗？`,
        success: (res) => {
          if (res.confirm) {
            // 模拟删除
            const index = this.myTemplates.findIndex(t => t.id === item.id);
            if (index !== -1) {
              this.myTemplates.splice(index, 1);
              uni.showToast({
                title: '删除成功',
                icon: 'success'
              });
            }
          }
        }
      });
    }
  }
}
</script>

<style lang="scss">
.page-container {
  min-height: 100vh;
  background-color: #F5F7FA;
  padding-bottom: env(safe-area-inset-bottom);
}

/* 导航栏样式 */
.navbar {
  position: sticky;
  top: 0;
  left: 0;
  right: 0;
  background: linear-gradient(135deg, #FF5858, #FF0000);
  color: #fff;
  padding: 44px 16px 15px;
  display: flex;
  align-items: center;
  z-index: 100;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.15);
}

.navbar-back {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-icon {
  width: 12px;
  height: 12px;
  border-left: 2px solid #fff;
  border-bottom: 2px solid #fff;
  transform: rotate(45deg);
}

.navbar-title {
  flex: 1;
  text-align: center;
  font-size: 18px;
  font-weight: 600;
  letter-spacing: 0.5px;
}

.navbar-right {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.help-icon {
  width: 24px;
  height: 24px;
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: bold;
}

/* 通用样式 */
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.add-btn {
  display: flex;
  align-items: center;
  background: linear-gradient(135deg, #FF5858, #FF0000);
  padding: 6px 12px;
  border-radius: 15px;
  color: #fff;
}

.btn-text {
  font-size: 12px;
  margin-right: 5px;
}

.plus-icon-small {
  width: 12px;
  height: 12px;
  position: relative;
}

.plus-icon-small::before,
.plus-icon-small::after {
  content: '';
  position: absolute;
  background-color: #fff;
}

.plus-icon-small::before {
  width: 12px;
  height: 2px;
  top: 5px;
  left: 0;
}

.plus-icon-small::after {
  width: 2px;
  height: 12px;
  top: 0;
  left: 5px;
}

/* 我的模板区域 */
.template-section {
  background-color: #fff;
  margin: 15px;
  border-radius: 12px;
  padding: 15px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.template-list {
  margin-top: 10px;
}

.template-item {
  background-color: #fff;
  border-radius: 10px;
  margin-bottom: 15px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.03);
  border: 1px solid #eee;
  overflow: hidden;
}

.template-preview {
  height: 100px;
  padding: 15px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.template-icon {
  width: 40px;
  height: 40px;
  border-radius: 20px;
  background: rgba(255, 255, 255, 0.2);
  margin-bottom: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.icon-image {
  width: 24px;
  height: 24px;
}

.template-name {
  color: #fff;
  font-size: 16px;
  font-weight: 600;
}

.template-info {
  padding: 12px 15px;
  border-bottom: 1px solid #f5f5f5;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.template-desc {
  font-size: 14px;
  color: #666;
  flex: 1;
}

.template-type {
  font-size: 12px;
  color: #999;
  background-color: #F5F7FA;
  padding: 3px 8px;
  border-radius: 10px;
}

.template-actions {
  padding: 10px 15px;
  display: flex;
  justify-content: flex-end;
}

.action-btn {
  margin-left: 10px;
  padding: 5px 12px;
  border-radius: 15px;
  font-size: 12px;
  background-color: #F5F7FA;
  color: #666;
}

.action-btn.edit {
  background-color: #E3F2FD;
  color: #1976D2;
}

.action-btn.delete {
  background-color: #FEE8E8;
  color: #FF5858;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 30px 0;
}

.empty-icon {
  width: 60px;
  height: 60px;
  background-color: #F5F7FA;
  border-radius: 30px;
  margin-bottom: 10px;
}

.empty-text {
  font-size: 14px;
  color: #999;
  margin-bottom: 15px;
}

.empty-action {
  padding: 8px 20px;
  background: linear-gradient(135deg, #FF5858, #FF0000);
  color: #fff;
  border-radius: 20px;
  font-size: 14px;
}

/* 官方模板区域 */
.official-section {
  background-color: #fff;
  margin: 15px;
  border-radius: 12px;
  padding: 15px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.template-grid {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -5px;
}

.template-card {
  width: calc(50% - 10px);
  margin: 5px;
  border-radius: 10px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.template-footer {
  background-color: #fff;
  padding: 10px;
}

.template-use-btn {
  background-color: #F5F7FA;
  color: #FF5858;
  font-size: 12px;
  padding: 5px 0;
  border-radius: 15px;
  text-align: center;
  margin-top: 8px;
}

/* 场景模板区域 */
.scenario-section {
  background-color: #fff;
  margin: 15px;
  border-radius: 12px;
  padding: 15px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.scenario-scroll {
  margin-top: 15px;
  white-space: nowrap;
}

.scenario-container {
  display: inline-flex;
  padding: 5px 0;
}

.scenario-card {
  width: 160px;
  margin-right: 15px;
  border-radius: 10px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.scenario-preview {
  height: 120px;
  padding: 15px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: relative;
}

.scenario-icon {
  width: 40px;
  height: 40px;
  border-radius: 20px;
  background: rgba(255, 255, 255, 0.2);
  margin-bottom: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.scenario-name {
  color: #fff;
  font-size: 16px;
  font-weight: 600;
}

.scenario-tag {
  position: absolute;
  top: 10px;
  right: 10px;
  padding: 2px 6px;
  border-radius: 10px;
  font-size: 10px;
  background-color: #FF3B30;
  color: #fff;
}

.scenario-footer {
  background-color: #fff;
  padding: 10px;
}

.scenario-desc {
  font-size: 12px;
  color: #666;
  margin-bottom: 8px;
  display: block;
  white-space: normal;
  height: 32px;
  overflow: hidden;
}

.scenario-use-btn {
  background-color: #F5F7FA;
  color: #FF5858;
  font-size: 12px;
  padding: 5px 0;
  border-radius: 15px;
  text-align: center;
}

/* 指南样式 */
.guide-section {
  background-color: #fff;
  margin: 15px;
  border-radius: 12px;
  padding: 15px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  margin-bottom: 80px;
}

.guide-steps {
  margin-top: 15px;
}

.guide-step {
  display: flex;
  margin-bottom: 15px;
}

.guide-step:last-child {
  margin-bottom: 0;
}

.step-number {
  width: 24px;
  height: 24px;
  border-radius: 12px;
  background-color: #FF5858;
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  margin-right: 10px;
  flex-shrink: 0;
}

.step-content {
  flex: 1;
}

.step-title {
  font-size: 14px;
  font-weight: 600;
  color: #333;
  margin-bottom: 5px;
  display: block;
}

.step-desc {
  font-size: 12px;
  color: #666;
}

/* 浮动操作按钮 */
.floating-action-button {
  position: fixed;
  bottom: 30px;
  right: 20px;
  width: 56px;
  height: 56px;
  border-radius: 28px;
  background: linear-gradient(135deg, #FF5858, #FF0000);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 3px 10px rgba(255, 88, 88, 0.3);
  z-index: 100;
}

.fab-icon {
  font-size: 28px;
  color: #fff;
  font-weight: 300;
}
</style>