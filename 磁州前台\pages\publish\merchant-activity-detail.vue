<template>
  <view class="detail-container activity-detail-container">
    <!-- 自定义导航栏 -->
    <view class="custom-navbar" :style="{ paddingTop: statusBarHeight + 'px' }">
      <view class="navbar-left" @click="goBack">
        <image src="/static/images/tabbar/返回键.png" class="back-icon"></image>
      </view>
      <view class="navbar-title">商家活动详情</view>
      <view class="navbar-right">
        <!-- 占位 -->
      </view>
    </view>
    
    <!-- 隐藏的分享按钮，用于自动触发 -->
    <button id="shareButton" class="hidden-share-btn" open-type="share"></button>
    
    <!-- 隐藏的Canvas用于绘制海报 -->
    <canvas canvas-id="posterCanvas" class="poster-canvas" style="width: 600px; height: 900px; position: fixed; top: -9999px; left: -9999px;"></canvas>
    
    <!-- 悬浮海报按钮 -->
    <view class="float-poster-btn" @click="generateShareImage">
      <image src="/static/images/tabbar/海报.png" class="poster-icon"></image>
      <text class="poster-text">海报</text>
    </view>
    
    <view class="detail-wrapper activity-detail-wrapper">
      <!-- 活动封面 -->
      <view class="content-card cover-card">
        <swiper class="cover-swiper" indicator-dots autoplay circular>
          <swiper-item v-for="(image, index) in activityData.images" :key="index">
            <image :src="image" mode="aspectFill" class="cover-image" @click="previewImage(index)"></image>
          </swiper-item>
        </swiper>
      </view>
      
      <!-- 活动信息卡片 -->
      <view class="content-card activity-card">
        <view class="activity-header">
          <view class="title-row">
            <text class="main-title">{{activityData.title}}</text>
            <view class="activity-status" :class="getActivityStatusClass()">{{getActivityStatusText()}}</view>
          </view>
          <view class="meta-info">
            <view class="tag-group">
              <view class="info-tag" v-for="(tag, index) in activityData.tags" :key="index">{{tag}}</view>
            </view>
            <text class="publish-time">发布于 {{formatTime(activityData.publishTime)}}</text>
          </view>
        </view>
        
        <view class="basic-info">
          <view class="info-item">
            <text class="info-label">活动类型</text>
            <text class="info-value">{{activityData.activityType}}</text>
          </view>
          <view class="info-item">
            <text class="info-label">活动时间</text>
            <text class="info-value">{{activityData.startDate}} 至 {{activityData.endDate}}</text>
          </view>
          <view class="info-item">
            <text class="info-label">商家名称</text>
            <text class="info-value">{{activityData.merchantName}}</text>
          </view>
        </view>
      </view>
      
      <!-- 活动描述 -->
      <view class="content-card description-card">
        <view class="section-title">活动描述</view>
        <view class="description-content">
          <rich-text :nodes="activityData.description" class="description-text"></rich-text>
        </view>
      </view>
      
      <!-- 活动规则 -->
      <view class="content-card rules-card">
        <view class="section-title">活动规则</view>
        <view class="rules-content">
          <rich-text :nodes="activityData.rules" class="rules-text"></rich-text>
        </view>
      </view>
      
      <!-- 商家地址卡片 -->
      <view class="content-card location-card">
        <view class="section-title">活动地点</view>
        <view class="location-content" @click="openLocation">
          <text class="iconfont icon-location"></text>
          <text class="location-text">{{activityData.address}}</text>
          <text class="iconfont icon-right location-arrow"></text>
        </view>
        <view class="location-map">
          <image src="/static/images/map-preview.png" mode="aspectFill" class="map-preview"></image>
        </view>
      </view>
      
      <!-- 联系方式卡片 -->
      <view class="content-card contact-card">
        <view class="section-title">商家联系方式</view>
        <view class="contact-content">
          <view class="contact-item">
            <text class="contact-label">联系人</text>
            <text class="contact-value">{{activityData.contact}}</text>
          </view>
          <view class="contact-item">
            <text class="contact-label">电话</text>
            <text class="contact-value contact-phone" @click="callPhone">{{activityData.phone}}</text>
          </view>
          <view class="contact-tips">
            <text class="tips-icon iconfont icon-info"></text>
            <text class="tips-text">请说明在"磁州生活网"看到的信息</text>
          </view>
        </view>
      </view>
      
      <!-- 红包区域 -->
      <view class="content-card red-packet-card" v-if="activityData.hasRedPacket">
        <view class="section-title">活动红包</view>
        <view class="red-packet-section">
          <view class="red-packet-container" @click="openRedPacket">
            <view class="red-packet-blur-bg"></view>
            <view class="red-packet-content">
              <view class="red-packet-left">
                <image class="red-packet-icon" src="/static/images/tabbar/抢红包.gif"></image>
              <view class="red-packet-info">
                <view class="red-packet-title">
                  {{activityData.redPacket.type === 'random' ? '随机金额红包' : '查看活动领红包'}}
                </view>
                <view class="red-packet-desc">
                  还剩{{activityData.redPacket.remain}}个，{{getRedPacketConditionText()}}
                  </view>
                </view>
              </view>
              <view class="red-packet-right">
                <view class="red-packet-amount"><text class="prefix">共</text> ¥{{activityData.redPacket.amount}}</view>
                <view class="grab-btn">立即领取</view>
              </view>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 举报卡片 -->
      <report-card></report-card>
      
      <!-- 相关活动推荐卡片 -->
      <view class="content-card related-activities-card">
        <!-- 标题栏 -->
        <view class="collapsible-header">
          <view class="section-title">相关活动推荐</view>
        </view>
        
        <!-- 内容区 -->
        <view class="collapsible-content">
          <!-- 简洁的活动列表 -->
          <view class="related-activities-list">
            <view class="related-activity-item" 
                 v-for="(activity, index) in relatedActivities.slice(0, 3)" 
                 :key="index" 
                 @click="navigateToActivityDetail(activity.id)">
              <view class="related-activity-left">
                <image :src="activity.image" mode="aspectFill" class="related-activity-image"></image>
              </view>
              <view class="related-activity-info">
                <view class="related-activity-title">{{activity.title}}</view>
                <view class="related-activity-meta">
                  <text class="related-meta-text">{{activity.merchantName}}</text>
                  <text class="related-meta-text">{{activity.activityType}}</text>
                </view>
                <view class="related-activity-date">{{activity.startDate}} 至 {{activity.endDate}}</view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 底部操作栏 -->
    <view class="footer-action-bar">
      <view class="action-item collect-action" @click="toggleCollect">
        <text class="action-icon" :class="{'icon-collected': isCollected, 'icon-collect': !isCollected}"></text>
        <text class="action-text">{{isCollected ? '已收藏' : '收藏'}}</text>
      </view>
      <view class="action-item share-action" @click="shareToFriend">
        <text class="action-icon icon-share"></text>
        <text class="action-text">分享</text>
      </view>
      <view class="action-item contact-action" @click="contactMerchant">
        <text class="action-text">联系商家</text>
      </view>
    </view>
    
    <!-- 海报弹窗 -->
    <view class="poster-modal" v-if="showPosterModal" @click="closePosterModal">
      <view class="poster-container" @click.stop>
        <view class="poster-header">
          <text class="poster-modal-title">分享海报</text>
          <text class="close-icon" @click="closePosterModal">×</text>
        </view>
        <view class="poster-image-container">
          <image :src="posterUrl" mode="widthFix" class="poster-preview"></image>
        </view>
        <view class="poster-footer">
          <button class="poster-btn save-btn" @click="savePoster">保存到相册</button>
          <button class="poster-btn share-btn" open-type="share">分享给朋友</button>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import { onLoad } from '@dcloudio/uni-app';
import { formatTime } from '../../utils/date.js';
import ReportCard from '../../components/ReportCard.vue';

// --- 响应式状态 ---
const statusBarHeight = ref(20);
const activityId = ref('');
const isCollected = ref(false);
const showPosterModal = ref(false);
const posterUrl = ref('');

const activityData = reactive({
        id: '1',
        title: '周年庆典大优惠',
        activityType: '优惠折扣',
        merchantName: '品味餐厅',
        startDate: '2023-06-01',
        endDate: '2023-06-30',
        address: '河北省邯郸市磁县北顺城街32号',
        description: '品味餐厅两周年庆典，全场菜品8折，满200减30，满300减50，满500减100！还有精美礼品等你来拿！',
        rules: '1. 活动时间：2023年6月1日至6月30日\n2. 折扣与满减不可同时使用\n3. 特价菜品除外\n4. 每桌最多减免100元\n5. 节假日通用',
        tags: ['周年庆', '满减优惠', '有赠品'],
        images: [
          '/static/images/default-image.png',
          '/static/images/default-image.png',
          '/static/images/default-image.png'
        ],
        contact: '李经理',
        phone: '13800138000',
        wechat: 'restaurant123',
        publishTime: new Date().getTime() - 86400000, // 一天前
        hasRedPacket: true,
        redPacket: {
          type: 'random',
          amount: 20,
          remain: 10,
          total: 20
        }
});

const relatedActivities = reactive([
        {
          id: '2',
          title: '新店开业大酬宾',
          merchantName: '鲜果超市',
          activityType: '新店开业',
          startDate: '2023-06-05',
          endDate: '2023-06-15',
          image: '/static/images/default-image.png'
        },
        {
          id: '3',
          title: '夏季特惠活动',
          merchantName: '时尚服饰',
          activityType: '限时特价',
          startDate: '2023-06-10',
          endDate: '2023-06-20',
          image: '/static/images/default-image.png'
        },
        {
          id: '4',
          title: '会员专享日',
          merchantName: '健身中心',
          activityType: '会员专享',
          startDate: '2023-06-15',
          endDate: '2023-06-16',
          image: '/static/images/default-image.png'
        }
]);


// --- 方法 ---

const goBack = () => uni.navigateBack();

const previewImage = (index) => {
  uni.previewImage({
    current: index,
    urls: activityData.images,
  });
};

const getActivityStatusText = () => {
      const now = new Date();
  const start = new Date(activityData.startDate);
  const end = new Date(activityData.endDate);
  if (now < start) return '未开始';
  if (now > end) return '已结束';
  return '进行中';
};

const getActivityStatusClass = () => {
  const status = getActivityStatusText();
  if (status === '进行中') return 'status-ongoing';
  if (status === '未开始') return 'status-not-started';
        return 'status-ended';
};

const openLocation = () => {
      uni.openLocation({
    latitude: 36.36, // 示例纬度
    longitude: 114.23, // 示例经度
    address: activityData.address,
    name: activityData.merchantName,
      });
};

const callPhone = () => {
  uni.makePhoneCall({ phoneNumber: activityData.phone });
};

const openRedPacket = () => {
  uni.showToast({ title: '红包功能正在开发中', icon: 'none' });
};

const getRedPacketConditionText = () => "点击领取";

const navigateToActivityDetail = (id) => {
  uni.navigateTo({ url: `/pages/publish/merchant-activity-detail?id=${id}` });
};

const toggleCollect = () => {
  isCollected.value = !isCollected.value;
  uni.showToast({ title: isCollected.value ? '收藏成功' : '取消收藏' });
};

const shareToFriend = () => {
  uni.share({
    provider: 'weixin',
    scene: 'WXSceneSession',
    type: 0,
    title: activityData.title,
    summary: activityData.description,
    imageUrl: activityData.images[0],
  });
};

const contactMerchant = () => {
  uni.showActionSheet({
    itemList: ['拨打电话', '添加微信'],
    success: (res) => {
      if (res.tapIndex === 0) callPhone();
      if (res.tapIndex === 1) {
        uni.setClipboardData({
          data: activityData.wechat,
          success: () => uni.showToast({ title: '微信号已复制' }),
        });
      }
    },
  });
};

const generateShareImage = () => {
  uni.showLoading({ title: '海报生成中...' });
  const ctx = uni.createCanvasContext('posterCanvas');
  // ... (省略Canvas绘制逻辑)
  // 此处应有完整的绘制海报的逻辑
      setTimeout(() => {
     // 模拟生成成功
    posterUrl.value = '/static/images/default-image.png'; // 假设这是生成的海报
    showPosterModal.value = true;
        uni.hideLoading();
      }, 1000);
};

const closePosterModal = () => {
  showPosterModal.value = false;
};

const savePoster = () => {
  uni.saveImageToPhotosAlbum({
    filePath: posterUrl.value,
    success: () => uni.showToast({ title: '保存成功' }),
    fail: () => uni.showToast({ title: '保存失败', icon: 'error' }),
  });
};


// --- 生命周期 ---
onLoad((options) => {
  activityId.value = options.id || '';
  // fetchActivityData(activityId.value);
});

onMounted(() => {
  const systemInfo = uni.getSystemInfoSync();
  statusBarHeight.value = systemInfo.statusBarHeight || 20;
});

</script>

<style lang="scss">
.detail-container {
  min-height: 100vh;
  background-color: #f5f7fa;
  padding-bottom: 120rpx; /* 为底部操作栏留出空间 */
}

/* 自定义导航栏 */
.custom-navbar {
  background: linear-gradient(135deg, #0066FF, #0052CC);
  height: 88rpx;
  padding-top: 44px; /* 状态栏高度 */
  display: flex;
  align-items: center;
  position: fixed; /* 改为固定定位 */
  top: 0;
  left: 0;
  right: 0;
  padding-bottom: 10rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 82, 204, 0.2);
  z-index: 100; /* 提高z-index确保在最上层 */
}

.navbar-left {
  position: absolute;
  left: 30rpx;
  display: flex;
  align-items: center;
}

.back-icon {
  width: 40rpx;
  height: 40rpx;
}

.navbar-title {
  color: #FFFFFF;
  font-size: 36rpx;
  font-weight: 600;
  flex: 1;
  text-align: center;
}

.navbar-right {
  position: absolute;
  right: 30rpx;
}

/* 详情包装器 */
.detail-wrapper {
  padding: 24rpx;
  padding-top: 144px; /* 增加顶部内边距，确保内容不被导航栏遮挡 */
}

/* 内容卡片通用样式 */
.content-card {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
}

/* 章节标题 */
.section-title {
  font-size: 32rpx;
  color: #333;
  font-weight: 600;
  margin-bottom: 20rpx;
  position: relative;
  padding-left: 20rpx;
}

.section-title::before {
  content: "";
  position: absolute;
  left: 0;
  top: 6rpx;
  height: 32rpx;
  width: 8rpx;
  background: linear-gradient(to bottom, #3846cd, #5868e0);
  border-radius: 4rpx;
}

/* 活动封面 */
.cover-card {
  padding: 0;
  overflow: hidden;
}

.cover-swiper {
  height: 400rpx;
  width: 100%;
}

.cover-image {
  width: 100%;
  height: 100%;
}

/* 活动信息卡片 */
.activity-header {
  margin-bottom: 20rpx;
}

.title-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.main-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  flex: 1;
}

.activity-status {
  font-size: 24rpx;
  padding: 4rpx 16rpx;
  border-radius: 20rpx;
  margin-left: 10rpx;
}

.status-upcoming {
  background-color: #e6f7ff;
  color: #1890ff;
}

.status-ongoing {
  background-color: #f6ffed;
  color: #52c41a;
}

.status-ended {
  background-color: #f5f5f5;
  color: #999;
}

.meta-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.tag-group {
  display: flex;
  flex-wrap: wrap;
}

.info-tag {
  font-size: 22rpx;
  color: #666;
  background-color: #f5f7fa;
  padding: 4rpx 16rpx;
  border-radius: 20rpx;
  margin-right: 10rpx;
  margin-bottom: 10rpx;
}

.publish-time {
  font-size: 22rpx;
  color: #999;
}

/* 基本信息列表 */
.basic-info {
  border-top: 1rpx solid #f0f0f0;
  padding-top: 20rpx;
}

.info-item {
  display: flex;
  justify-content: space-between;
  padding: 16rpx 0;
  border-bottom: 1rpx solid #f9f9f9;
}

.info-label {
  color: #666;
  font-size: 28rpx;
}

.info-value {
  color: #333;
  font-size: 28rpx;
  font-weight: 500;
}

/* 描述和规则 */
.description-content, .rules-content {
  color: #333;
  font-size: 28rpx;
  line-height: 1.6;
}

/* 位置卡片 */
.location-content {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f9f9f9;
}

.location-text {
  flex: 1;
  font-size: 28rpx;
  color: #333;
  margin: 0 10rpx;
}

.location-arrow {
  color: #999;
  font-size: 24rpx;
}

.location-map {
  margin-top: 20rpx;
  height: 200rpx;
  border-radius: 8rpx;
  overflow: hidden;
}

.map-preview {
  width: 100%;
  height: 100%;
}

/* 联系方式卡片 */
.contact-content {
  padding: 10rpx 0;
}

.contact-item {
  display: flex;
  justify-content: space-between;
  padding: 16rpx 0;
  border-bottom: 1rpx solid #f9f9f9;
}

.contact-label {
  color: #666;
  font-size: 28rpx;
}

.contact-value {
  color: #333;
  font-size: 28rpx;
}

.contact-phone {
  color: #3846cd;
}

.contact-tips {
  display: flex;
  align-items: center;
  margin-top: 20rpx;
}

.tips-icon {
  color: #3846cd;
  font-size: 28rpx;
  margin-right: 10rpx;
}

.tips-text {
  color: #999;
  font-size: 24rpx;
}

/* 红包卡片 */
.red-packet-section {
  padding: 10rpx 0;
}

.red-packet-container {
  background: linear-gradient(135deg, #FF9B9B, #FF6B6B);
  border-radius: 12rpx;
  padding: 4rpx;
  position: relative;
  overflow: hidden;
}

.red-packet-blur-bg {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url("../../../static/images/tabbar/红包背景.png") no-repeat center/cover;
  opacity: 0.1;
}

.red-packet-content {
  background: #FFF;
  border-radius: 8rpx;
  padding: 20rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
  z-index: 1;
}

.red-packet-left {
  display: flex;
  align-items: center;
}

.red-packet-icon {
  width: 60rpx;
  height: 60rpx;
  margin-right: 16rpx;
}

.red-packet-info {
  display: flex;
  flex-direction: column;
}

.red-packet-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #FF4D4F;
  margin-bottom: 6rpx;
}

.red-packet-desc {
  font-size: 22rpx;
  color: #999;
}

.red-packet-right {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.red-packet-amount {
  font-size: 32rpx;
  font-weight: 600;
  color: #FF4D4F;
  margin-bottom: 10rpx;
}

.prefix {
  font-size: 22rpx;
  font-weight: normal;
}

.grab-btn {
  background: linear-gradient(135deg, #FF9B9B, #FF6B6B);
  color: #FFF;
  font-size: 24rpx;
  padding: 6rpx 20rpx;
  border-radius: 20rpx;
}

/* 相关活动推荐 */
.related-activities-list {
  padding: 10rpx 0;
}

.related-activity-item {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f9f9f9;
}

.related-activity-item:last-child {
  border-bottom: none;
}

.related-activity-left {
  width: 120rpx;
  height: 120rpx;
  border-radius: 8rpx;
  overflow: hidden;
  margin-right: 20rpx;
}

.related-activity-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.related-activity-info {
  flex: 1;
}

.related-activity-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 8rpx;
  line-height: 1.4;
}

.related-activity-meta {
  display: flex;
  margin-bottom: 6rpx;
}

.related-meta-text {
  font-size: 24rpx;
  color: #666;
  margin-right: 16rpx;
}

.related-activity-date {
  font-size: 22rpx;
  color: #999;
}

/* 底部操作栏 */
.footer-action-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 100rpx;
  background: #FFF;
  display: flex;
  align-items: center;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  z-index: 90;
  padding-bottom: env(safe-area-inset-bottom);
}

.action-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
}

.action-icon {
  font-size: 40rpx;
  color: #999;
  margin-bottom: 6rpx;
}

.icon-collected {
  color: #3846cd;
}

.action-text {
  font-size: 24rpx;
  color: #666;
}

.contact-action {
  flex: 2;
  background: linear-gradient(135deg, #3846cd, #5868e0);
  height: 70rpx;
  border-radius: 35rpx;
  margin: 0 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.contact-action .action-text {
  color: #FFF;
  font-size: 28rpx;
  font-weight: 500;
}

/* 海报弹窗 */
.poster-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 999;
}

.poster-container {
  width: 80%;
  background: #FFF;
  border-radius: 16rpx;
  overflow: hidden;
}

.poster-header {
  padding: 20rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1rpx solid #f0f0f0;
}

.poster-modal-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
}

.close-icon {
  font-size: 40rpx;
  color: #999;
  padding: 10rpx;
}

.poster-image-container {
  padding: 30rpx;
  display: flex;
  justify-content: center;
}

.poster-preview {
  width: 100%;
  border-radius: 8rpx;
}

.poster-footer {
  display: flex;
  padding: 20rpx;
  border-top: 1rpx solid #f0f0f0;
}

.poster-btn {
  flex: 1;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  border-radius: 40rpx;
  margin: 0 10rpx;
  font-size: 28rpx;
}

.save-btn {
  background: #f5f7fa;
  color: #666;
}

.share-btn {
  background: linear-gradient(135deg, #3846cd, #5868e0);
  color: #FFF;
}

/* 悬浮海报按钮 */
.float-poster-btn {
  position: fixed;
  right: 30rpx;
  bottom: 140rpx;
  width: 100rpx;
  height: 100rpx;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 50%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
  z-index: 80;
}

.poster-icon {
  width: 40rpx;
  height: 40rpx;
  margin-bottom: 4rpx;
}

.poster-text {
  font-size: 20rpx;
  color: #666;
}

/* 隐藏的分享按钮 */
.hidden-share-btn {
  position: absolute;
  opacity: 0;
  width: 0;
  height: 0;
}

/* 海报Canvas */
.poster-canvas {
  position: fixed;
  top: -9999px;
  left: -9999px;
}
</style>