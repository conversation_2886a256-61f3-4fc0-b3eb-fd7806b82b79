"use strict";
const common_vendor = require("../../../common/vendor.js");
const common_assets = require("../../../common/assets.js");
const _sfc_main = {
  __name: "detail",
  setup(__props) {
    const statusBarHeight = common_vendor.ref(20);
    const navbarHeight = common_vendor.ref(64);
    const balanceInfo = common_vendor.ref({
      amount: 158.5,
      totalIncome: 200,
      totalExpense: 41.5,
      frozenAmount: 0
    });
    const activeTab = common_vendor.ref(0);
    const tabs = common_vendor.ref([
      { name: "全部", type: "all" },
      { name: "收入", type: "income" },
      { name: "支出", type: "expense" }
    ]);
    const transactions = common_vendor.ref([
      {
        id: "tx001",
        title: "充值",
        time: "2023-11-05 14:30",
        amount: 100,
        type: "income",
        status: "已完成",
        month: "2023年11月"
      },
      {
        id: "tx002",
        title: "服务支付",
        time: "2023-11-03 09:15",
        amount: 35,
        type: "expense",
        status: "已完成",
        month: "2023年11月"
      },
      {
        id: "tx003",
        title: "提现",
        time: "2023-10-28 16:22",
        amount: 50,
        type: "expense",
        status: "已完成",
        month: "2023年10月"
      },
      {
        id: "tx004",
        title: "充值",
        time: "2023-10-15 11:05",
        amount: 100,
        type: "income",
        status: "已完成",
        month: "2023年10月"
      },
      {
        id: "tx005",
        title: "任务收入",
        time: "2023-09-30 18:45",
        amount: 88,
        type: "income",
        status: "已完成",
        month: "2023年9月"
      }
    ]);
    const page = common_vendor.ref(1);
    common_vendor.ref(10);
    const hasMoreData = common_vendor.ref(true);
    const filteredTransactions = common_vendor.computed(() => {
      if (activeTab.value === 0) {
        return transactions.value;
      } else {
        const type = tabs.value[activeTab.value].type;
        return transactions.value.filter((item) => item.type === type);
      }
    });
    const groupedTransactions = common_vendor.computed(() => {
      const groups = {};
      filteredTransactions.value.forEach((item) => {
        if (!groups[item.month]) {
          groups[item.month] = [];
        }
        groups[item.month].push(item);
      });
      return groups;
    });
    const goBack = () => {
      common_vendor.index.navigateBack();
    };
    const switchTab = (index) => {
      activeTab.value = index;
    };
    const getWalletBalance = () => {
      setTimeout(() => {
        balanceInfo.value = {
          amount: 158.5,
          totalIncome: 200,
          totalExpense: 41.5,
          frozenAmount: 0
        };
      }, 500);
    };
    const getTransactions = () => {
      setTimeout(() => {
        if (page.value === 1)
          ;
        else {
          if (page.value >= 3) {
            hasMoreData.value = false;
          } else {
            const moreData = [
              {
                id: "tx006",
                title: "服务支付",
                time: "2023-09-22 10:30",
                amount: 25,
                type: "expense",
                status: "已完成",
                month: "2023年9月"
              },
              {
                id: "tx007",
                title: "充值",
                time: "2023-09-15 16:40",
                amount: 50,
                type: "income",
                status: "已完成",
                month: "2023年9月"
              }
            ];
            transactions.value = [...transactions.value, ...moreData];
          }
        }
      }, 500);
    };
    const loadMoreData = () => {
      if (!hasMoreData.value)
        return;
      page.value++;
      getTransactions();
    };
    const getTransactionTypeIcon = (type) => {
      const icons = {
        "income": "/static/images/tabbar/收入.png",
        "expense": "/static/images/tabbar/支出.png"
      };
      return icons[type] || icons["income"];
    };
    const getTransactionTypeClass = (type) => {
      return {
        "income-icon": type === "income",
        "expense-icon": type === "expense"
      };
    };
    common_vendor.onMounted(() => {
      const sysInfo = common_vendor.index.getSystemInfoSync();
      statusBarHeight.value = sysInfo.statusBarHeight;
      navbarHeight.value = statusBarHeight.value + 44;
      getWalletBalance();
      getTransactions();
    });
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_assets._imports_0$5,
        b: common_vendor.o(goBack),
        c: statusBarHeight.value + "px",
        d: common_vendor.t(balanceInfo.value.amount.toFixed(2)),
        e: common_vendor.t(balanceInfo.value.totalIncome.toFixed(2)),
        f: common_vendor.t(balanceInfo.value.totalExpense.toFixed(2)),
        g: navbarHeight.value + 10 + "px",
        h: common_vendor.f(tabs.value, (tab, index, i0) => {
          return {
            a: common_vendor.t(tab.name),
            b: index,
            c: activeTab.value === index ? 1 : "",
            d: common_vendor.o(($event) => switchTab(index), index)
          };
        }),
        i: filteredTransactions.value.length > 0
      }, filteredTransactions.value.length > 0 ? common_vendor.e({
        j: common_vendor.f(groupedTransactions.value, (group, month, i0) => {
          return {
            a: common_vendor.t(month),
            b: common_vendor.f(group, (item, index, i1) => {
              return {
                a: getTransactionTypeIcon(item.type),
                b: common_vendor.n(getTransactionTypeClass(item.type)),
                c: common_vendor.t(item.title),
                d: common_vendor.t(item.time),
                e: common_vendor.t(item.type === "income" ? "+" : "-"),
                f: common_vendor.t(item.amount.toFixed(2)),
                g: item.type === "income" ? 1 : "",
                h: item.type === "expense" ? 1 : "",
                i: common_vendor.t(item.status),
                j: index
              };
            }),
            c: month
          };
        }),
        k: hasMoreData.value
      }, hasMoreData.value ? {
        l: common_vendor.o(loadMoreData)
      } : {}) : {
        m: common_assets._imports_1$3
      });
    };
  }
};
wx.createPage(_sfc_main);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/subPackages/payment/pages/detail.js.map
