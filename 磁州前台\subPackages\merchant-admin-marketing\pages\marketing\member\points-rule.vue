<template>
  <view class="points-rule-container">
    <!-- 自定义导航栏 -->
    <view class="navbar">
      <view class="navbar-back" @click="goBack">
        <view class="back-icon"></view>
      </view>
      <text class="navbar-title">积分规则</text>
      <view class="navbar-right"></view>
    </view>
    
    <!-- 积分规则内容 -->
    <view class="rule-content">
      <!-- 积分获取规则 -->
      <view class="section-card">
        <view class="section-title">积分获取规则</view>
        
        <view class="rule-form">
          <view class="form-item">
            <text class="form-label">消费积分比例</text>
            <view class="form-input-group">
              <input type="number" class="form-input" v-model="pointsRules.consumptionRatio" />
              <text class="input-suffix">积分/元</text>
            </view>
          </view>
          
          <view class="form-item">
            <text class="form-label">签到积分奖励</text>
            <view class="form-input-group">
              <input type="number" class="form-input" v-model="pointsRules.checkInPoints" />
              <text class="input-suffix">积分/次</text>
            </view>
          </view>
          
          <view class="form-item">
            <text class="form-label">评价积分奖励</text>
            <view class="form-input-group">
              <input type="number" class="form-input" v-model="pointsRules.reviewPoints" />
              <text class="input-suffix">积分/次</text>
            </view>
          </view>
          
          <view class="form-item">
            <text class="form-label">分享积分奖励</text>
            <view class="form-input-group">
              <input type="number" class="form-input" v-model="pointsRules.sharePoints" />
              <text class="input-suffix">积分/次</text>
            </view>
          </view>
          
          <view class="form-item">
            <text class="form-label">新用户注册奖励</text>
            <view class="form-input-group">
              <input type="number" class="form-input" v-model="pointsRules.registerPoints" />
              <text class="input-suffix">积分</text>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 积分使用规则 -->
      <view class="section-card">
        <view class="section-title">积分使用规则</view>
        
        <view class="rule-form">
          <view class="form-item">
            <text class="form-label">积分抵扣比例</text>
            <view class="form-input-group">
              <input type="number" class="form-input" v-model="pointsRules.redemptionRatio" />
              <text class="input-suffix">积分/元</text>
            </view>
          </view>
          
          <view class="form-item">
            <text class="form-label">最低抵扣积分</text>
            <view class="form-input-group">
              <input type="number" class="form-input" v-model="pointsRules.minRedemption" />
              <text class="input-suffix">积分</text>
            </view>
          </view>
          
          <view class="form-item">
            <text class="form-label">最高抵扣比例</text>
            <view class="form-input-group">
              <input type="number" class="form-input" v-model="pointsRules.maxRedemptionRatio" />
              <text class="input-suffix">%</text>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 积分有效期规则 -->
      <view class="section-card">
        <view class="section-title">积分有效期规则</view>
        
        <view class="rule-form">
          <view class="form-item">
            <text class="form-label">积分有效期</text>
            <view class="form-input-group">
              <input type="number" class="form-input" v-model="pointsRules.validity" />
              <text class="input-suffix">个月</text>
            </view>
          </view>
          
          <view class="form-item switch-item">
            <text class="form-label">积分到期提醒</text>
            <switch :checked="pointsRules.expirationReminder" @change="toggleExpirationReminder" color="#4A00E0" />
          </view>
          
          <view class="form-item" v-if="pointsRules.expirationReminder">
            <text class="form-label">提前提醒天数</text>
            <view class="form-input-group">
              <input type="number" class="form-input" v-model="pointsRules.reminderDays" />
              <text class="input-suffix">天</text>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 积分等级规则 -->
      <view class="section-card">
        <view class="section-title">积分等级规则</view>
        
        <view class="level-list">
          <view class="level-item" v-for="(level, index) in pointsLevels" :key="index">
            <view class="level-header">
              <text class="level-name">{{level.name}}</text>
              <text class="level-points">{{level.minPoints}}-{{level.maxPoints}}积分</text>
            </view>
            <view class="level-benefits">
              <text class="benefit-item" v-for="(benefit, bIndex) in level.benefits" :key="bIndex">{{benefit}}</text>
            </view>
            <view class="level-actions">
              <text class="action-btn edit" @click="editLevel(level)">编辑</text>
              <text class="action-btn delete" v-if="index > 0" @click="deleteLevel(level)">删除</text>
            </view>
          </view>
        </view>
        
        <button class="add-btn" @click="addLevel">添加积分等级</button>
      </view>
    </view>
    
    <!-- 保存按钮 -->
    <view class="bottom-bar">
      <button class="save-btn" @click="saveRules">保存规则</button>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      // 积分规则
      pointsRules: {
        consumptionRatio: 10,
        checkInPoints: 5,
        reviewPoints: 10,
        sharePoints: 5,
        registerPoints: 100,
        
        redemptionRatio: 100,
        minRedemption: 100,
        maxRedemptionRatio: 30,
        
        validity: 12,
        expirationReminder: true,
        reminderDays: 30
      },
      
      // 积分等级
      pointsLevels: [
        {
          id: 1,
          name: '普通会员',
          minPoints: 0,
          maxPoints: 999,
          benefits: ['基础积分兑换']
        },
        {
          id: 2,
          name: '银卡会员',
          minPoints: 1000,
          maxPoints: 4999,
          benefits: ['积分兑换9.5折', '生日双倍积分']
        },
        {
          id: 3,
          name: '金卡会员',
          minPoints: 5000,
          maxPoints: 19999,
          benefits: ['积分兑换9折', '生日双倍积分', '专属积分活动']
        },
        {
          id: 4,
          name: '钻石会员',
          minPoints: 20000,
          maxPoints: 99999,
          benefits: ['积分兑换8.5折', '生日三倍积分', '专属积分活动', '积分不过期']
        }
      ]
    };
  },
  methods: {
    goBack() {
      uni.navigateBack();
    },
    
    toggleExpirationReminder(e) {
      this.pointsRules.expirationReminder = e.detail.value;
    },
    
    editLevel(level) {
      uni.showToast({
        title: '编辑等级功能开发中',
        icon: 'none'
      });
    },
    
    deleteLevel(level) {
      uni.showModal({
        title: '删除确认',
        content: `确定要删除"${level.name}"等级吗？`,
        success: (res) => {
          if (res.confirm) {
            const index = this.pointsLevels.findIndex(item => item.id === level.id);
            if (index !== -1) {
              this.pointsLevels.splice(index, 1);
              uni.showToast({
                title: '删除成功',
                icon: 'success'
              });
            }
          }
        }
      });
    },
    
    addLevel() {
      uni.showToast({
        title: '添加等级功能开发中',
        icon: 'none'
      });
    },
    
    saveRules() {
      uni.showLoading({
        title: '保存中...'
      });
      
      setTimeout(() => {
        uni.hideLoading();
        uni.showToast({
          title: '积分规则保存成功',
          icon: 'success'
        });
      }, 1000);
    }
  }
}
</script>

<style>
/* 积分规则页面样式开始 */
.points-rule-container {
  min-height: 100vh;
  background-color: #F5F7FA;
  padding-bottom: 100rpx;
}

/* 导航栏样式 */
.navbar {
  position: sticky;
  top: 0;
  left: 0;
  right: 0;
  background: linear-gradient(135deg, #8E2DE2, #4A00E0);
  color: #fff;
  padding: 44px 16px 15px;
  display: flex;
  align-items: center;
  z-index: 100;
  box-shadow: 0 2px 10px rgba(74, 0, 224, 0.15);
}

.navbar-back {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-icon {
  width: 12px;
  height: 12px;
  border-left: 2px solid #fff;
  border-bottom: 2px solid #fff;
  transform: rotate(45deg);
}

.navbar-title {
  flex: 1;
  text-align: center;
  font-size: 18px;
  font-weight: 600;
  letter-spacing: 0.5px;
}

.navbar-right {
  width: 36px;
  height: 36px;
}

/* 规则内容样式 */
.rule-content {
  padding: 20rpx;
}

.section-card {
  background: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.section-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 20rpx;
  padding-bottom: 15rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

/* 表单样式 */
.form-item {
  margin-bottom: 20rpx;
}

.form-label {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 10rpx;
  display: block;
}

.form-input-group {
  display: flex;
  align-items: center;
  border: 1rpx solid #ddd;
  border-radius: 8rpx;
  overflow: hidden;
}

.form-input {
  flex: 1;
  height: 80rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
}

.input-suffix {
  padding: 0 20rpx;
  font-size: 24rpx;
  color: #999;
  background: #f5f5f5;
  height: 80rpx;
  line-height: 80rpx;
}

.switch-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* 积分等级样式 */
.level-list {
  margin-bottom: 20rpx;
}

.level-item {
  background: #f9f9f9;
  border-radius: 8rpx;
  padding: 20rpx;
  margin-bottom: 15rpx;
}

.level-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10rpx;
}

.level-name {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
}

.level-points {
  font-size: 24rpx;
  color: #4A00E0;
  background: rgba(74, 0, 224, 0.1);
  padding: 4rpx 12rpx;
  border-radius: 20rpx;
}

.level-benefits {
  display: flex;
  flex-wrap: wrap;
  gap: 10rpx;
  margin-bottom: 15rpx;
}

.benefit-item {
  font-size: 24rpx;
  color: #666;
  background: #f0f0f0;
  padding: 4rpx 12rpx;
  border-radius: 20rpx;
}

.level-actions {
  display: flex;
  gap: 15rpx;
  justify-content: flex-end;
}

.action-btn {
  font-size: 24rpx;
  padding: 6rpx 16rpx;
  border-radius: 30rpx;
}

.action-btn.edit {
  background: rgba(74, 0, 224, 0.1);
  color: #4A00E0;
}

.action-btn.delete {
  background: rgba(255, 59, 48, 0.1);
  color: #FF3B30;
}

/* 添加按钮 */
.add-btn {
  background: #4A00E0;
  color: #fff;
  border: none;
  border-radius: 40rpx;
  height: 80rpx;
  line-height: 80rpx;
  font-size: 28rpx;
  margin-top: 20rpx;
}

/* 底部保存栏 */
.bottom-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #fff;
  padding: 20rpx;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.save-btn {
  background: #4A00E0;
  color: #fff;
  border: none;
  border-radius: 40rpx;
  height: 80rpx;
  line-height: 80rpx;
  font-size: 28rpx;
  width: 100%;
}
/* 积分规则页面样式结束 */
</style> 