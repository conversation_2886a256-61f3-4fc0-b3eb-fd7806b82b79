/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 移除阿里妈妈字体引用，改用系统默认字体 */
/* 移除原有阿里妈妈字体定义
$uni-font-family: 'AlimamaShuHeiTi';
$uni-font-family-text: 'AlimamaShuHeiTi', -apple-system, BlinkMacSystemFont, 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;
*/
body, html, #app, .index-container {
  font-family: "AlimamaShuHeiTi", -apple-system, BlinkMacSystemFont, "Helvetica Neue", "PingFang SC", "Microsoft YaHei", sans-serif;
}
.activity-management {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f5f7fa;
  position: relative;
}

/* 状态栏样式 */
.status-bar {
  background: linear-gradient(135deg, #007AFF, #5856D6);
  width: 100%;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1001;
}

/* 导航栏样式 */
.navbar {
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 16px;
  background: linear-gradient(135deg, #007AFF, #5856D6);
  color: white;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 1000;
  margin-top: var(--34f4e901);
  box-shadow: 0 2px 10px rgba(0, 122, 255, 0.2);
}
.navbar-left, .navbar-right {
  display: flex;
  align-items: center;
  min-width: 32px;
}

/* 添加返回按钮样式 */
.back-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.15);
  transition: all 0.2s ease;
}
.back-btn:active {
  background: rgba(255, 255, 255, 0.25);
  transform: scale(0.92);
}
.back-btn svg {
  width: 20px;
  height: 20px;
}
.navbar-title {
  flex: 1;
  text-align: center;
  font-size: 18px;
  font-weight: 500;
  letter-spacing: 0.5px;
}
.navbar-action {
  padding: 4px;
}

/* 内容区域 */
.content-area {
  flex: 1;
  width: 100%;
  box-sizing: border-box;
  padding: 16px;
  margin-top: var(--547e3084);
  /* 状态栏+导航栏高度 */
  height: var(--7c848ece);
}

/* 统计卡片 */
.statistics-card {
  display: flex;
  justify-content: space-between;
  background-color: white;
  border-radius: 16px;
  padding: 20px 10px;
  margin: 0 auto 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  width: 92%;
  transform: translateZ(0);
  position: relative;
  overflow: hidden;
}
.statistics-card::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: 16px;
  box-shadow: inset 0 1px 1px rgba(255, 255, 255, 0.8);
  z-index: 1;
  pointer-events: none;
}
.statistics-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.statistics-value {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
}
.statistics-label {
  font-size: 12px;
  color: #666;
}
.statistics-divider {
  width: 1px;
  height: 30px;
  background-color: #eee;
}

/* 筛选区域 */
.filter-section {
  margin: 0 auto 16px;
  width: 92%;
  /* 减小宽度，确保边缘有足够的间距 */
  background-color: #fff;
  border-radius: 12px;
  padding: 14px 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}
.filter-category {
  margin-bottom: 12px;
}
.filter-category:last-child {
  margin-bottom: 0;
}
.category-title {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin-bottom: 10px;
  padding-left: 4px;
}
.filter-tabs {
  white-space: nowrap;
  padding-right: 16px;
  margin-bottom: 4px;
  overflow-x: auto;
}
.filter-tabs::-webkit-scrollbar {
  display: none;
}
.filter-tab {
  display: inline-block;
  padding: 6px 16px;
  margin-right: 8px;
  background-color: #F2F2F7;
  border-radius: 20px;
  font-size: 13px;
  color: #666;
  transition: all 0.2s ease;
}
.filter-tab:last-child {
  margin-right: 0;
}
.filter-tab.active {
  background-color: #007AFF;
  color: white;
  font-weight: 500;
}
.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 12px;
}

/* 活动列表 */
.activity-list {
  margin: 0 auto 16px;
  width: 96%;
}
.activity-card {
  background-color: white;
  border-radius: 16px;
  margin-bottom: 20px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
  overflow: hidden;
  position: relative;
  transform: translateZ(0);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  width: 100%;
}
.activity-card:active {
  transform: scale(0.98) translateZ(0);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.08);
}
.activity-card::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(to right, transparent, rgba(255, 255, 255, 0.8), transparent);
  z-index: 1;
}
.card-header {
  display: flex;
  justify-content: center;
  padding: 12px 16px 0;
}
.status-tags {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  justify-content: center;
  gap: 6px;
}
.status-tag, .type-tag, .top-tag {
  padding: 3px 8px;
  border-radius: 10px;
  font-size: 11px;
  font-weight: 500;
}
.type-tag {
  background-color: #F2F2F7;
  color: #8E8E93;
}
.type-groupon {
  background-color: rgba(125, 122, 255, 0.1);
  color: #7D7AFF;
}
.type-coupon {
  background-color: rgba(255, 69, 58, 0.1);
  color: #FF453A;
}
.type-seckill {
  background-color: rgba(255, 55, 95, 0.1);
  color: #FF375F;
}
.type-discount {
  background-color: rgba(255, 149, 0, 0.1);
  color: #FF9500;
}
.type-points {
  background-color: rgba(52, 199, 89, 0.1);
  color: #34C759;
}
.card-content {
  display: flex;
  padding: 16px 20px;
}
.card-image {
  width: 100px;
  height: 100px;
  border-radius: 12px;
  overflow: hidden;
  margin-right: 16px;
  flex-shrink: 0;
}
.card-image image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.card-info {
  flex: 1;
  display: flex;
  flex-direction: column;
}
.activity-title {
  font-size: 17px;
  font-weight: 600;
  color: #333;
  margin-bottom: 10px;
  line-height: 1.4;
}
.activity-time {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  font-size: 13px;
  color: #666;
}
.activity-time svg {
  margin-right: 4px;
}
.activity-stats {
  display: flex;
}
.stat-item {
  display: flex;
  align-items: center;
  margin-right: 16px;
  font-size: 13px;
  color: #666;
}
.stat-item svg {
  margin-right: 4px;
}
.card-actions {
  display: flex;
  border-top: 1px solid #f5f5f5;
  padding: 12px 16px;
  justify-content: space-around;
  gap: 10px;
}
.action-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  font-size: 13px;
  color: white;
  padding: 6px 14px;
  border-radius: 18px;
  transition: all 0.2s ease;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08);
  font-weight: 500;
}
.action-button:active {
  transform: scale(0.95);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}
.action-button.edit {
  background: linear-gradient(135deg, #0A84FF, #0060DF);
}
.action-button.promote {
  background: linear-gradient(135deg, #FF9500, #E66000);
}
.action-button.share {
  background: linear-gradient(135deg, #34C759, #28A745);
}
.action-button.more {
  background: linear-gradient(135deg, #8E8E93, #636366);
}

/* 空状态 */
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 0;
  width: 92%;
  margin: 0 auto;
}
.empty-image {
  width: 120px;
  height: 120px;
  margin-bottom: 16px;
}
.empty-text {
  font-size: 16px;
  color: #999;
  margin-bottom: 20px;
}
.empty-action {
  padding: 8px 20px;
  background-color: #0A84FF;
  color: white;
  border-radius: 20px;
  font-size: 14px;
}

/* 加载中 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 0;
  width: 92%;
  margin: 0 auto;
}
.loading-spinner {
  width: 30px;
  height: 30px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #0A84FF;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 12px;
}
.loading-text {
  font-size: 14px;
  color: #999;
}
@keyframes spin {
0% {
    transform: rotate(0deg);
}
100% {
    transform: rotate(360deg);
}
}
/* 加载更多和没有更多 */
.load-more, .no-more {
  text-align: center;
  padding: 16px 0;
  font-size: 14px;
  color: #999;
  width: 92%;
  margin: 0 auto;
}
.load-more {
  color: #0A84FF;
}

/* 浮动按钮 */
.fab-button {
  position: fixed;
  right: 24px;
  bottom: 24px;
  width: 52px;
  height: 36px;
  border-radius: 18px;
  background: linear-gradient(135deg, #0A84FF, #0060DF);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 16px rgba(10, 132, 255, 0.5);
  z-index: 1000;
  transition: all 0.2s ease;
  border: 1px solid rgba(255, 255, 255, 0.3);
  /* 移除之前的after伪元素 */
  /* 添加激活状态 */
  /* 发布文字样式 */
}
.fab-button::after {
  content: none;
}
.fab-button:active {
  transform: scale(0.92);
  box-shadow: 0 2px 8px rgba(10, 132, 255, 0.3);
  background: linear-gradient(135deg, #0060DF, #0A84FF);
}
.fab-button .fab-text {
  font-size: 14px;
  color: white;
  font-weight: 500;
  line-height: 1;
  text-align: center;
}

/* 为置顶卡片添加特殊样式 */
.activity-card:has(.top-tag) {
  border-left: 3px solid #FF9500;
}
.status-running {
  background-color: rgba(52, 199, 89, 0.1);
  color: #34C759;
}
.status-upcoming {
  background-color: rgba(0, 122, 255, 0.1);
  color: #007AFF;
}
.status-ended {
  background-color: rgba(142, 142, 147, 0.1);
  color: #8E8E93;
}
.top-tag {
  background-color: rgba(255, 149, 0, 0.1);
  color: #FF9500;
}