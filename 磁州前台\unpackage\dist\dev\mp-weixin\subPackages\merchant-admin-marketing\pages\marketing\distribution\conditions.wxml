<view class="conditions-container"><view class="navbar"><view class="navbar-back" bindtap="{{a}}"><view class="back-icon"></view></view><text class="navbar-title">成为分销员条件</text><view class="navbar-right"><view class="help-icon" bindtap="{{b}}">?</view></view></view><view class="conditions-card"><view class="condition-item" bindtap="{{e}}"><view class="{{['radio-button', d && 'active']}}"><view wx:if="{{c}}" class="radio-inner"></view></view><view class="condition-content"><text class="condition-title">无条件</text><text class="condition-desc">任何用户都可以成为分销员</text></view></view><view class="condition-item" bindtap="{{h}}"><view class="{{['radio-button', g && 'active']}}"><view wx:if="{{f}}" class="radio-inner"></view></view><view class="condition-content"><text class="condition-title">申请成为分销员</text><text class="condition-desc">用户需要提交申请，审核通过后成为分销员</text></view></view><view class="condition-item" bindtap="{{k}}"><view class="{{['radio-button', j && 'active']}}"><view wx:if="{{i}}" class="radio-inner"></view></view><view class="condition-content"><text class="condition-title">购买商品并申请</text><text class="condition-desc">用户需要购买指定商品并提交申请</text></view></view><view class="condition-item" bindtap="{{n}}"><view class="{{['radio-button', m && 'active']}}"><view wx:if="{{l}}" class="radio-inner"></view></view><view class="condition-content"><text class="condition-title">邀请成为分销员</text><text class="condition-desc">现有分销员邀请新用户才能成为分销员</text></view></view></view><view wx:if="{{o}}" class="additional-card"><view class="card-header"><text class="card-title">购买商品设置</text></view><view class="form-item"><text class="form-label">购买金额要求</text><view class="form-input-group"><text class="input-prefix">¥</text><input type="digit" class="form-input" placeholder="请输入金额" value="{{p}}" bindinput="{{q}}"/></view></view><view class="form-item"><text class="form-label">指定商品</text><view class="form-switch"><switch checked="{{r}}" bindchange="{{s}}" color="#6B0FBE"/></view></view><view wx:if="{{t}}" class="product-list"><view wx:for="{{v}}" wx:for-item="product" wx:key="e" class="product-item"><image class="product-image" src="{{product.a}}" mode="aspectFill"></image><view class="product-info"><text class="product-name">{{product.b}}</text><text class="product-price">¥{{product.c}}</text></view><view class="product-remove" bindtap="{{product.d}}">×</view></view><view class="add-product" bindtap="{{w}}"><view class="add-icon">+</view><text class="add-text">添加商品</text></view></view></view><view wx:if="{{x}}" class="approval-card"><view class="card-header"><text class="card-title">审核设置</text></view><view class="form-item"><text class="form-label">需要审核</text><view class="form-switch"><switch checked="{{y}}" bindchange="{{z}}" color="#6B0FBE"/></view></view><view wx:if="{{A}}" class="form-item"><text class="form-label">自动通过审核</text><view class="form-switch"><switch checked="{{B}}" bindchange="{{C}}" color="#6B0FBE"/></view></view></view><view class="button-container"><button class="save-button" bindtap="{{D}}">保存设置</button></view></view>