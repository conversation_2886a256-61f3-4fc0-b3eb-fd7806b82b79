/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 移除阿里妈妈字体引用，改用系统默认字体 */
/* 移除原有阿里妈妈字体定义
$uni-font-family: 'AlimamaShuHeiTi';
$uni-font-family-text: 'AlimamaShuHeiTi', -apple-system, BlinkMacSystemFont, 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;
*/
body.data-v-8d06c5ee, html.data-v-8d06c5ee, #app.data-v-8d06c5ee, .index-container.data-v-8d06c5ee {
  font-family: "AlimamaShuHeiTi", -apple-system, BlinkMacSystemFont, "Helvetica Neue", "PingFang SC", "Microsoft YaHei", sans-serif;
}
.category-container.data-v-8d06c5ee {
  min-height: 100vh;
  background-color: #f5f5f5;
}
.content-container.data-v-8d06c5ee {
  display: flex;
  height: 100vh;
  padding-top: calc(var(--status-bar-height) + 44px);
}
.category-sidebar.data-v-8d06c5ee {
  width: 80px;
  height: 100%;
  background-color: #f5f5f5;
}
.category-item.data-v-8d06c5ee {
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}
.category-item text.data-v-8d06c5ee {
  font-size: 14px;
  color: #666666;
}
.category-item--active.data-v-8d06c5ee {
  background-color: #FFFFFF;
}
.category-item--active text.data-v-8d06c5ee {
  color: #9C27B0;
  font-weight: 500;
}
.category-item--active.data-v-8d06c5ee::before {
  content: "";
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 4px;
  height: 16px;
  background-color: #9C27B0;
  border-radius: 0 2px 2px 0;
}
.category-content.data-v-8d06c5ee {
  flex: 1;
  height: 100%;
  background-color: #FFFFFF;
}
.subcategory-grid.data-v-8d06c5ee {
  padding: 16px;
}
.subcategory-section.data-v-8d06c5ee {
  margin-bottom: 24px;
}
.subcategory-section.data-v-8d06c5ee:last-child {
  margin-bottom: 0;
}
.subcategory-title.data-v-8d06c5ee {
  margin-bottom: 16px;
}
.subcategory-title text.data-v-8d06c5ee {
  font-size: 16px;
  font-weight: 500;
  color: #333333;
}
.subcategory-items.data-v-8d06c5ee {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16px;
}
.subcategory-item.data-v-8d06c5ee {
  display: flex;
  flex-direction: column;
  align-items: center;
}
.subcategory-item .subcategory-icon.data-v-8d06c5ee {
  width: 60px;
  height: 60px;
  border-radius: 12px;
  margin-bottom: 8px;
}
.subcategory-item .subcategory-name.data-v-8d06c5ee {
  font-size: 12px;
  color: #666666;
}