/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 移除阿里妈妈字体引用，改用系统默认字体 */
/* 移除原有阿里妈妈字体定义
$uni-font-family: 'AlimamaShuHeiTi';
$uni-font-family-text: 'AlimamaShuHeiTi', -apple-system, BlinkMacSystemFont, 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;
*/
body.data-v-0b99154b, html.data-v-0b99154b, #app.data-v-0b99154b, .index-container.data-v-0b99154b {
  font-family: "AlimamaShuHeiTi", -apple-system, BlinkMacSystemFont, "Helvetica Neue", "PingFang SC", "Microsoft YaHei", sans-serif;
}
.life-service-container.data-v-0b99154b {
  min-height: 100vh;
  background-color: #f5f5f5;
}
.content-container.data-v-0b99154b {
  padding-top: calc(var(--status-bar-height) + 44px);
  padding-bottom: 20px;
}
.service-header.data-v-0b99154b {
  margin: 16px;
  padding: 20px;
  background-color: #FFFFFF;
  border-radius: 16px;
  display: flex;
  align-items: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}
.service-header.special-header.data-v-0b99154b {
  background: linear-gradient(135deg, #F5F0FF 0%, #EDE7F6 100%);
  padding: 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.service-header.special-header .special-info .special-title.data-v-0b99154b {
  font-size: 16px;
  color: #333333;
  margin-bottom: 6px;
  display: block;
}
.service-header.special-header .special-info .special-desc.data-v-0b99154b {
  font-size: 20px;
  color: #9C27B0;
  font-weight: 600;
  margin-bottom: 4px;
  display: block;
}
.service-header.special-header .special-info .special-subdesc.data-v-0b99154b {
  font-size: 14px;
  color: #666666;
  display: block;
}
.service-header.special-header .special-price.data-v-0b99154b {
  background-color: #FF5252;
  border-radius: 24px;
  padding: 8px 16px;
  display: flex;
  align-items: center;
}
.service-header.special-header .special-price .price-value.data-v-0b99154b {
  font-size: 24px;
  font-weight: 600;
  color: #FFFFFF;
}
.service-header.special-header .special-price .price-tag.data-v-0b99154b {
  font-size: 14px;
  color: #FFFFFF;
  background-color: rgba(255, 255, 255, 0.3);
  border-radius: 12px;
  padding: 2px 6px;
  margin-left: 4px;
}
.service-header .service-icon-container.data-v-0b99154b {
  width: 56px;
  height: 56px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
}
.service-header .service-info .service-title.data-v-0b99154b {
  font-size: 18px;
  font-weight: 600;
  color: #333333;
  margin-bottom: 6px;
  display: block;
}
.service-header .service-info .service-desc.data-v-0b99154b {
  font-size: 14px;
  color: #9C27B0;
  display: block;
}
.platform-list.data-v-0b99154b {
  margin: 16px;
  background-color: #FFFFFF;
  border-radius: 16px;
  padding: 8px 0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}
.platform-list .platform-item.data-v-0b99154b {
  display: flex;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #F0F0F0;
}
.platform-list .platform-item.data-v-0b99154b:last-child {
  border-bottom: none;
}
.platform-list .platform-item .platform-logo.data-v-0b99154b {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  margin-right: 12px;
}
.platform-list .platform-item .platform-info.data-v-0b99154b {
  flex: 1;
}
.platform-list .platform-item .platform-info .platform-name.data-v-0b99154b {
  font-size: 16px;
  color: #333333;
  margin-bottom: 4px;
  display: block;
}
.platform-list .platform-item .platform-info .platform-desc.data-v-0b99154b {
  font-size: 14px;
  color: #9C27B0;
  display: block;
}
.platform-list .platform-item .arrow-icon.data-v-0b99154b {
  margin-left: 8px;
}
.coupon-detail.data-v-0b99154b {
  margin: 16px;
  background-color: #FFFFFF;
  border-radius: 16px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}
.coupon-detail .coupon-image.data-v-0b99154b {
  width: 100%;
  border-radius: 12px;
  margin-bottom: 16px;
}
.coupon-detail .coupon-info.data-v-0b99154b {
  padding: 16px 0;
  border-bottom: 1px dashed #EEEEEE;
  margin-bottom: 16px;
}
.coupon-detail .coupon-info .coupon-title.data-v-0b99154b {
  font-size: 18px;
  font-weight: 600;
  color: #333333;
  margin-bottom: 12px;
  display: block;
}
.coupon-detail .coupon-info .coupon-value.data-v-0b99154b {
  font-size: 24px;
  font-weight: 700;
  color: #FF5252;
  margin-bottom: 6px;
  display: block;
}
.coupon-detail .coupon-info .coupon-condition.data-v-0b99154b {
  font-size: 14px;
  color: #666666;
  margin-bottom: 6px;
  display: block;
}
.coupon-detail .coupon-info .coupon-valid.data-v-0b99154b {
  font-size: 12px;
  color: #999999;
  margin-bottom: 16px;
  display: block;
}
.coupon-detail .coupon-info .get-coupon-btn.data-v-0b99154b {
  width: 100%;
  height: 44px;
  background-color: #9C27B0;
  color: #FFFFFF;
  font-size: 16px;
  font-weight: 500;
  border-radius: 22px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
}
.coupon-detail .coupon-desc .desc-title.data-v-0b99154b {
  font-size: 16px;
  font-weight: 600;
  color: #333333;
  margin-bottom: 12px;
  display: block;
}
.coupon-detail .coupon-desc .desc-item.data-v-0b99154b {
  font-size: 14px;
  color: #666666;
  margin-bottom: 8px;
  display: block;
  line-height: 1.5;
}
.vip-list.data-v-0b99154b {
  margin: 16px;
  background-color: #FFFFFF;
  border-radius: 16px;
  padding: 8px 0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}
.vip-list .vip-item.data-v-0b99154b {
  display: flex;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #F0F0F0;
}
.vip-list .vip-item.data-v-0b99154b:last-child {
  border-bottom: none;
}
.vip-list .vip-item .vip-logo.data-v-0b99154b {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  margin-right: 12px;
}
.vip-list .vip-item .vip-info.data-v-0b99154b {
  flex: 1;
}
.vip-list .vip-item .vip-info .vip-name.data-v-0b99154b {
  font-size: 16px;
  color: #333333;
  margin-bottom: 4px;
  display: block;
}
.vip-list .vip-item .vip-info .vip-desc.data-v-0b99154b {
  font-size: 14px;
  color: #666666;
  display: block;
}
.vip-list .vip-item .vip-price .price-value.data-v-0b99154b {
  font-size: 16px;
  font-weight: 600;
  color: #FF5252;
}