/*
 * uCharts®
 * 简化版本，仅用于显示合伙人收益统计图表
 * 开发者：磁州生活网
 */

class UCharts {
  constructor(opts) {
    this.opts = opts;
    this.config = {};
    this.context = opts.context;
    this.canvasId = opts.canvasId;
    this.chartData = {};
    this.categories = opts.categories || [];
    this.series = opts.series || [];
    this.width = opts.width || 375;
    this.height = opts.height || 250;
    this.padding = opts.padding || [10, 10, 10, 10];
    this.colors = ['#1890FF', '#91CB74', '#FAC858', '#EE6666', '#73C0DE', '#3CA272', '#FC8452', '#9A60B4', '#ea7ccc'];
    this.xAxis = opts.xAxis || {};
    this.yAxis = opts.yAxis || {};
    this.extra = opts.extra || {};
    this.legend = opts.legend || {};
    this.init();
  }

  init() {
    this.initConfig();
  }

  initConfig() {
    this.config = {
      width: this.width,
      height: this.height,
      padding: this.padding,
      yAxisWidth: 50,
      xAxisHeight: 30
    };
    this.chartData = {
      categories: this.categories,
      series: this.series
    };
  }

  drawCharts() {
    this.drawBackground();
    this.drawYAxis();
    this.drawXAxis();
    this.drawColumn();
    this.drawLegend();
    this.context.draw();
  }

  drawBackground() {
    const { width, height } = this.config;
    this.context.setFillStyle('#FFFFFF');
    this.context.fillRect(0, 0, width, height);
  }

  drawYAxis() {
    const { width, height, padding, yAxisWidth } = this.config;
    const yAxis = this.yAxis;
    const series = this.series;
    
    // 找出最大值
    let maxValue = 0;
    series.forEach(item => {
      const max = Math.max(...item.data);
      if (max > maxValue) {
        maxValue = max;
      }
    });
    
    // 向上取整到合适的刻度
    maxValue = Math.ceil(maxValue / 10) * 10;
    
    // 绘制Y轴刻度
    const yStep = maxValue / 5;
    const yHeight = height - padding[2] - padding[0] - this.config.xAxisHeight;
    
    this.context.setFontSize(12);
    this.context.setFillStyle('#666666');
    this.context.setTextAlign('right');
    
    for (let i = 0; i <= 5; i++) {
      const y = height - padding[2] - this.config.xAxisHeight - i * (yHeight / 5);
      const value = i * yStep;
      
      // 绘制刻度值
      this.context.fillText(value.toFixed(0), padding[3] + yAxisWidth - 5, y + 4);
      
      // 绘制网格线
      if (i > 0 && yAxis.gridType === 'dash') {
        this.context.beginPath();
        this.context.setLineDash([yAxis.dashLength || 4, yAxis.dashLength || 4]);
        this.context.setStrokeStyle('#DDDDDD');
        this.context.moveTo(padding[3] + yAxisWidth, y);
        this.context.lineTo(width - padding[1], y);
        this.context.stroke();
        this.context.setLineDash([]);
      }
    }
    
    // 保存Y轴信息用于绘制柱状图
    this.chartData.yAxisHeight = yHeight;
    this.chartData.maxValue = maxValue;
  }

  drawXAxis() {
    const { width, height, padding, yAxisWidth } = this.config;
    const categories = this.categories;
    const xAxis = this.xAxis;
    
    const xWidth = width - padding[1] - padding[3] - yAxisWidth;
    const xItemWidth = xWidth / categories.length;
    const y = height - padding[2];
    
    this.context.setFontSize(12);
    this.context.setFillStyle(xAxis.fontColor || '#666666');
    this.context.setTextAlign('center');
    
    categories.forEach((item, index) => {
      const x = padding[3] + yAxisWidth + index * xItemWidth + xItemWidth / 2;
      this.context.fillText(item, x, y);
    });
    
    // 保存X轴信息用于绘制柱状图
    this.chartData.xItemWidth = xItemWidth;
    this.chartData.xStart = padding[3] + yAxisWidth;
  }

  drawColumn() {
    const { height, padding } = this.config;
    const series = this.series;
    const { xItemWidth, xStart, yAxisHeight, maxValue } = this.chartData;
    
    series.forEach((item, seriesIndex) => {
      const data = item.data;
      const color = item.color || this.colors[seriesIndex % this.colors.length];
      const columnWidth = this.extra.column?.width || xItemWidth * 0.6;
      const radius = this.extra.column?.radius || 0;
      
      data.forEach((value, index) => {
        const x = xStart + index * xItemWidth + (xItemWidth - columnWidth) / 2;
        const columnHeight = value / maxValue * yAxisHeight;
        const y = height - padding[2] - this.config.xAxisHeight - columnHeight;
        
        // 绘制柱状图
        this.context.beginPath();
        if (radius > 0) {
          // 绘制圆角矩形
          this.drawRoundRect(x, y, columnWidth, columnHeight, radius);
        } else {
          this.context.rect(x, y, columnWidth, columnHeight);
        }
        
        // 设置填充色
        if (this.extra.column?.linearType === 'custom') {
          const grd = this.context.createLinearGradient(x, y, x, y + columnHeight);
          const customColors = this.extra.column.customColor || ['#1677FF', '#5AC8FA'];
          grd.addColorStop(0, customColors[0]);
          grd.addColorStop(1, customColors[1]);
          this.context.setFillStyle(grd);
        } else {
          this.context.setFillStyle(color);
        }
        
        this.context.fill();
      });
    });
  }

  drawRoundRect(x, y, width, height, radius) {
    this.context.moveTo(x + radius, y);
    this.context.lineTo(x + width - radius, y);
    this.context.arcTo(x + width, y, x + width, y + radius, radius);
    this.context.lineTo(x + width, y + height);
    this.context.lineTo(x, y + height);
    this.context.lineTo(x, y + radius);
    this.context.arcTo(x, y, x + radius, y, radius);
    this.context.closePath();
  }

  drawLegend() {
    if (this.legend === false) return;
    
    const { width, padding } = this.config;
    const series = this.series;
    
    if (series.length === 0) return;
    
    const legendWidth = 100;
    const legendHeight = 20;
    const x = (width - legendWidth) / 2;
    const y = padding[0];
    
    this.context.setFontSize(12);
    this.context.setFillStyle('#666666');
    this.context.setTextAlign('left');
    
    series.forEach((item, index) => {
      const color = item.color || this.colors[index % this.colors.length];
      
      // 绘制图例标记
      this.context.setFillStyle(color);
      this.context.fillRect(x + index * legendWidth, y, 15, 15);
      
      // 绘制图例文字
      this.context.setFillStyle('#666666');
      this.context.fillText(item.name, x + index * legendWidth + 20, y + 12);
    });
  }
}

export default UCharts; 