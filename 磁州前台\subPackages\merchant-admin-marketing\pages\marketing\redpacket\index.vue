<template>
  <view class="redpacket-container">
    <!-- 自定义导航栏 -->
    <view class="navbar">
      <view class="navbar-back" @click="goBack">
        <view class="back-icon"></view>
      </view>
      <text class="navbar-title">红包营销</text>
      <view class="navbar-right">
        <view class="help-icon" @click="showHelp">?</view>
      </view>
    </view>
    
    <!-- 红包数据概览 -->
    <view class="overview-section">
      <view class="overview-header">
        <text class="section-title">红包数据概览</text>
        <view class="date-picker" @click="showDatePicker">
          <text class="date-text">{{dateRange}}</text>
          <view class="date-icon"></view>
        </view>
      </view>
      
      <view class="stats-cards">
        <view class="stats-card">
          <view class="card-value">{{redpacketData.totalCount}}</view>
          <view class="card-label">发放总数</view>
          <view class="card-trend" :class="redpacketData.countTrend">
            <view class="trend-arrow"></view>
            <text class="trend-value">{{redpacketData.countGrowth}}</text>
          </view>
        </view>
        
        <view class="stats-card">
          <view class="card-value">¥{{formatNumber(redpacketData.totalAmount)}}</view>
          <view class="card-label">红包总额</view>
          <view class="card-trend" :class="redpacketData.amountTrend">
            <view class="trend-arrow"></view>
            <text class="trend-value">{{redpacketData.amountGrowth}}</text>
          </view>
        </view>
        
        <view class="stats-card">
          <view class="card-value">{{redpacketData.receiveRate}}%</view>
          <view class="card-label">领取率</view>
          <view class="card-trend" :class="redpacketData.receiveRateTrend">
            <view class="trend-arrow"></view>
            <text class="trend-value">{{redpacketData.receiveRateGrowth}}</text>
          </view>
        </view>
        
        <view class="stats-card">
          <view class="card-value">{{redpacketData.conversionRate}}%</view>
          <view class="card-label">转化率</view>
          <view class="card-trend" :class="redpacketData.conversionTrend">
            <view class="trend-arrow"></view>
            <text class="trend-value">{{redpacketData.conversionGrowth}}</text>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 红包活动列表 -->
    <view class="redpacket-list-section">
      <view class="section-header">
        <text class="section-title">红包活动</text>
        <view class="add-btn" @click="createRedpacket">
          <text class="btn-text">创建红包</text>
          <view class="plus-icon-small"></view>
        </view>
      </view>
      
      <view class="tab-header">
        <view 
          class="tab-item" 
          v-for="(tab, index) in tabList" 
          :key="index"
          :class="{ active: currentTab === index }"
          @tap="switchTab(index)">
          <text class="tab-text">{{tab}}</text>
        </view>
      </view>
      
      <view class="redpacket-list">
        <view class="redpacket-item" v-for="(item, index) in filteredRedpackets" :key="index" @click="viewRedpacketDetail(item)">
          <view class="redpacket-header">
            <view class="redpacket-type" :class="'type-'+item.type">{{item.typeText}}</view>
            <view class="redpacket-status" :class="'status-'+item.status">{{item.statusText}}</view>
          </view>
          
          <view class="redpacket-content">
            <view class="redpacket-icon" :class="item.type"></view>
            <view class="redpacket-info">
              <text class="redpacket-name">{{item.name}}</text>
              <text class="redpacket-time">{{item.timeRange}}</text>
              <view class="redpacket-amount">
                <text class="amount-label">红包金额: </text>
                <text class="amount-value" v-if="item.type === 'fixed'">¥{{item.amount}}/个</text>
                <text class="amount-value" v-else-if="item.type === 'random'">¥{{item.minAmount}}-{{item.maxAmount}}/个</text>
              </view>
            </view>
          </view>
          
          <view class="redpacket-stats">
            <view class="stat-row">
              <text class="stat-label">发放数量:</text>
              <text class="stat-value">{{item.sentCount}}/{{item.totalCount}}</text>
            </view>
            <view class="stat-row">
              <text class="stat-label">领取数量:</text>
              <text class="stat-value">{{item.receivedCount}}</text>
            </view>
            <view class="stat-row">
              <text class="stat-label">使用数量:</text>
              <text class="stat-value">{{item.usedCount}}</text>
            </view>
          </view>
          
          <view class="redpacket-actions">
            <view class="action-btn" @click.stop="editRedpacket(item)">编辑</view>
            <view class="action-btn" @click.stop="shareRedpacket(item)" v-if="item.status === 'active'">分享</view>
            <view class="action-btn delete" @click.stop="deleteRedpacket(item)" v-if="item.status === 'draft' || item.status === 'ended'">删除</view>
          </view>
        </view>
        
        <view class="empty-state" v-if="filteredRedpackets.length === 0">
          <view class="empty-icon"></view>
          <text class="empty-text">暂无{{tabList[currentTab]}}红包活动</text>
        </view>
      </view>
    </view>
    
    <!-- 红包营销工具 -->
    <view class="tools-section">
      <view class="section-header">
        <text class="section-title">红包营销工具</text>
      </view>
      
      <view class="tools-grid">
        <view class="tool-card" v-for="(tool, index) in marketingTools" :key="index" @click="useTool(tool)">
          <view class="tool-icon" :style="{ background: tool.color }">
            <image class="icon-image" :src="tool.icon" mode="aspectFit"></image>
          </view>
          <text class="tool-name">{{tool.name}}</text>
          <text class="tool-desc">{{tool.description}}</text>
        </view>
      </view>
    </view>
    
    <!-- 红包模板 -->
    <view class="templates-section">
      <view class="section-header">
        <text class="section-title">红包模板</text>
        <view class="add-btn" @click="createTemplate">
          <text class="btn-text">创建模板</text>
          <view class="plus-icon-small"></view>
        </view>
      </view>
      
      <scroll-view scroll-x class="templates-scroll" show-scrollbar="false">
        <view class="templates-container">
          <view class="template-card" v-for="(template, index) in redpacketTemplates" :key="index" @click="useTemplate(template)">
            <view class="template-preview" :style="{ background: template.color }">
              <view class="template-icon">
                <image class="icon-image" :src="template.icon" mode="aspectFit"></image>
              </view>
              <text class="template-name">{{template.name}}</text>
            </view>
            <view class="template-footer">
              <text class="template-desc">{{template.description}}</text>
              <view class="template-use-btn">使用</view>
            </view>
          </view>
        </view>
      </scroll-view>
    </view>
    
    <!-- 红包营销指南 -->
    <view class="guide-section">
      <view class="section-header">
        <text class="section-title">红包营销指南</text>
      </view>
      
      <view class="guide-list">
        <view class="guide-item" v-for="(guide, index) in marketingGuides" :key="index" @click="viewGuide(guide)">
          <view class="guide-icon" :style="{ background: guide.color }">
            <image class="icon-image" :src="guide.icon" mode="aspectFit"></image>
          </view>
          <view class="guide-content">
            <text class="guide-title">{{guide.title}}</text>
            <text class="guide-desc">{{guide.description}}</text>
          </view>
          <view class="guide-arrow"></view>
        </view>
      </view>
    </view>
    
    <!-- 浮动操作按钮 -->
    <view class="floating-action-button" @click="showActionMenu">
      <view class="fab-icon">+</view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      dateRange: '2023-04-01 ~ 2023-04-30',
      currentTab: 0,
      tabList: ['全部', '进行中', '未开始', '已结束', '草稿'],
      
      // 红包数据概览
      redpacketData: {
        totalCount: 5862,
        countTrend: 'up',
        countGrowth: '12.5%',
        
        totalAmount: 58620.50,
        amountTrend: 'up',
        amountGrowth: '15.2%',
        
        receiveRate: 76.8,
        receiveRateTrend: 'up',
        receiveRateGrowth: '3.5%',
        
        conversionRate: 42.3,
        conversionTrend: 'up',
        conversionGrowth: '5.2%'
      },
      
      // 红包活动列表
      redpacketList: [
        {
          id: 1,
          name: '新用户专享红包',
          type: 'fixed',
          typeText: '固定金额',
          status: 'active',
          statusText: '进行中',
          timeRange: '2023-04-01 ~ 2023-04-30',
          amount: 10.00,
          totalCount: 1000,
          sentCount: 568,
          receivedCount: 452,
          usedCount: 326
        },
        {
          id: 2,
          name: '五一节日红包',
          type: 'random',
          typeText: '随机金额',
          status: 'upcoming',
          statusText: '未开始',
          timeRange: '2023-05-01 ~ 2023-05-07',
          minAmount: 5.00,
          maxAmount: 50.00,
          totalCount: 2000,
          sentCount: 0,
          receivedCount: 0,
          usedCount: 0
        },
        {
          id: 3,
          name: '会员专享红包',
          type: 'fixed',
          typeText: '固定金额',
          status: 'active',
          statusText: '进行中',
          timeRange: '2023-04-15 ~ 2023-04-30',
          amount: 20.00,
          totalCount: 500,
          sentCount: 286,
          receivedCount: 215,
          usedCount: 158
        },
        {
          id: 4,
          name: '春季促销红包',
          type: 'random',
          typeText: '随机金额',
          status: 'ended',
          statusText: '已结束',
          timeRange: '2023-03-01 ~ 2023-03-31',
          minAmount: 10.00,
          maxAmount: 100.00,
          totalCount: 1000,
          sentCount: 1000,
          receivedCount: 876,
          usedCount: 654
        },
        {
          id: 5,
          name: '周末专享红包',
          type: 'fixed',
          typeText: '固定金额',
          status: 'draft',
          statusText: '草稿',
          timeRange: '未设置',
          amount: 15.00,
          totalCount: 800,
          sentCount: 0,
          receivedCount: 0,
          usedCount: 0
        }
      ],
      
      // 红包营销工具
      marketingTools: [
        {
          id: 1,
          name: '红包群发',
          description: '批量发送红包给用户',
          icon: '/static/images/send-icon.png',
          color: 'linear-gradient(135deg, #FF9A8B, #FF6B6B)'
        },
        {
          id: 2,
          name: '红包雨',
          description: '创建红包雨活动',
          icon: '/static/images/rain-icon.png',
          color: 'linear-gradient(135deg, #FFDB01, #FF9E01)'
        },
        {
          id: 3,
          name: '裂变红包',
          description: '创建裂变式红包',
          icon: '/static/images/share-icon.png',
          color: 'linear-gradient(135deg, #FF5E62, #FF9966)'
        },
        {
          id: 4,
          name: '红包数据',
          description: '红包数据分析',
          icon: '/static/images/data-icon.png',
          color: 'linear-gradient(135deg, #00F2FE, #4FACFE)'
        }
      ],
      
      // 红包模板
      redpacketTemplates: [
        {
          id: 1,
          name: '新年红包',
          description: '新年主题红包模板',
          icon: '/static/images/newyear-icon.png',
          color: 'linear-gradient(135deg, #FF416C, #FF4B2B)'
        },
        {
          id: 2,
          name: '生日红包',
          description: '生日主题红包模板',
          icon: '/static/images/birthday-icon.png',
          color: 'linear-gradient(135deg, #F6D365, #FDA085)'
        },
        {
          id: 3,
          name: '感恩红包',
          description: '感恩主题红包模板',
          icon: '/static/images/thanks-icon.png',
          color: 'linear-gradient(135deg, #A18CD1, #FBC2EB)'
        },
        {
          id: 4,
          name: '节日红包',
          description: '节日主题红包模板',
          icon: '/static/images/festival-icon.png',
          color: 'linear-gradient(135deg, #FF9A9E, #FECFEF)'
        }
      ],
      
      // 营销指南
      marketingGuides: [
        {
          id: 1,
          title: '红包营销最佳实践',
          description: '了解如何有效使用红包提高转化率',
          icon: '/static/images/guide-icon.png',
          color: 'linear-gradient(135deg, #84FAB0, #8FD3F4)'
        },
        {
          id: 2,
          title: '节日红包攻略',
          description: '节日期间红包营销策略指南',
          icon: '/static/images/strategy-icon.png',
          color: 'linear-gradient(135deg, #FCCF31, #F55555)'
        },
        {
          id: 3,
          title: '裂变红包详解',
          description: '如何利用裂变红包实现用户增长',
          icon: '/static/images/growth-icon.png',
          color: 'linear-gradient(135deg, #43E97B, #38F9D7)'
        }
      ]
    }
  },
  computed: {
    filteredRedpackets() {
      if (this.currentTab === 0) {
        return this.redpacketList;
      } else {
        const statusMap = ['', 'active', 'upcoming', 'ended', 'draft'];
        return this.redpacketList.filter(item => item.status === statusMap[this.currentTab]);
      }
    }
  },
  methods: {
    goBack() {
      uni.navigateBack();
    },
    
    showHelp() {
      uni.showModal({
        title: '红包营销帮助',
        content: '红包营销是通过发放现金红包的方式吸引用户，提高用户活跃度和转化率的营销方式。',
        showCancel: false
      });
    },
    
    formatNumber(number) {
      return number.toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    },
    
    showDatePicker() {
      // 实现日期选择器
      uni.showToast({
        title: '日期选择功能开发中',
        icon: 'none'
      });
    },
    
    switchTab(index) {
      this.currentTab = index;
    },
    
    createRedpacket() {
      uni.navigateTo({
        url: '/subPackages/merchant-admin-marketing/pages/marketing/redpacket/create'
      });
    },
    
    viewRedpacketDetail(redpacket) {
      uni.navigateTo({
        url: `/subPackages/merchant-admin-marketing/pages/marketing/redpacket/detail?id=${redpacket.id}`
      });
    },
    
    editRedpacket(redpacket) {
      uni.navigateTo({
        url: `/subPackages/merchant-admin-marketing/pages/marketing/redpacket/edit?id=${redpacket.id}`
      });
    },
    
    shareRedpacket(redpacket) {
      uni.showLoading({
        title: '生成分享链接...'
      });
      
      setTimeout(() => {
        uni.hideLoading();
        uni.showModal({
          title: '分享红包',
          content: '红包分享链接已生成，是否复制链接？',
          confirmText: '复制',
          success: (res) => {
            if (res.confirm) {
              uni.setClipboardData({
                data: `https://example.com/redpacket/${redpacket.id}`,
                success: () => {
                  uni.showToast({
                    title: '链接已复制',
                    icon: 'success'
                  });
                }
              });
            }
          }
        });
      }, 1000);
    },
    
    deleteRedpacket(redpacket) {
      uni.showModal({
        title: '删除红包',
        content: `确定要删除"${redpacket.name}"吗？`,
        success: (res) => {
          if (res.confirm) {
            // 模拟删除操作
            const index = this.redpacketList.findIndex(item => item.id === redpacket.id);
            if (index !== -1) {
              this.redpacketList.splice(index, 1);
              uni.showToast({
                title: '删除成功',
                icon: 'success'
              });
            }
          }
        }
      });
    },
    
    useTool(tool) {
      uni.navigateTo({
        url: `/subPackages/merchant-admin-marketing/pages/marketing/redpacket/tool?id=${tool.id}`
      });
    },
    
    createTemplate() {
      uni.navigateTo({
        url: '/subPackages/merchant-admin-marketing/pages/marketing/redpacket/create-template'
      });
    },
    
    useTemplate(template) {
      uni.navigateTo({
        url: `/subPackages/merchant-admin-marketing/pages/marketing/redpacket/create?template=${template.id}`
      });
    },
    
    viewGuide(guide) {
      uni.navigateTo({
        url: `/subPackages/merchant-admin-marketing/pages/marketing/redpacket/guide?id=${guide.id}`
      });
    },
    
    showActionMenu() {
      uni.showActionSheet({
        itemList: ['创建红包', '红包数据分析', '导出红包数据', '红包营销设置'],
        success: (res) => {
          const actions = [
            () => this.createRedpacket(),
            () => this.analyzeData(),
            () => this.exportData(),
            () => this.manageSettings()
          ];
          
          actions[res.tapIndex]();
        }
      });
    },
    
    analyzeData() {
      uni.navigateTo({
        url: '/subPackages/merchant-admin-marketing/pages/marketing/redpacket/data-analysis'
      });
    },
    
    exportData() {
      uni.showLoading({
        title: '导出中...'
      });
      
      setTimeout(() => {
        uni.hideLoading();
        uni.showToast({
          title: '数据导出成功',
          icon: 'success'
        });
      }, 1500);
    },
    
    manageSettings() {
      uni.navigateTo({
        url: '/subPackages/merchant-admin-marketing/pages/marketing/redpacket/settings'
      });
    }
  }
}
</script>

<style lang="scss">
.redpacket-container {
  min-height: 100vh;
  background-color: #F5F7FA;
  padding-bottom: env(safe-area-inset-bottom);
}

/* 导航栏样式 */
.navbar {
  position: sticky;
  top: 0;
  left: 0;
  right: 0;
  background: linear-gradient(135deg, #FF5858, #FF0000);
  color: #fff;
  padding: 44px 16px 15px;
  display: flex;
  align-items: center;
  z-index: 100;
  box-shadow: 0 2px 10px rgba(255, 0, 0, 0.15);
}

.navbar-back {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-icon {
  width: 12px;
  height: 12px;
  border-left: 2px solid #fff;
  border-bottom: 2px solid #fff;
  transform: rotate(45deg);
}

.navbar-title {
  flex: 1;
  text-align: center;
  font-size: 18px;
  font-weight: 600;
  letter-spacing: 0.5px;
}

.navbar-right {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.help-icon {
  width: 24px;
  height: 24px;
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: bold;
}

/* 通用部分样式 */
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.add-btn {
  display: flex;
  align-items: center;
  background: #FF3B30;
  border-radius: 15px;
  padding: 5px 12px;
  color: white;
}

.btn-text {
  font-size: 13px;
  margin-right: 5px;
}

.plus-icon-small {
  width: 12px;
  height: 12px;
  position: relative;
}

.plus-icon-small:before,
.plus-icon-small:after {
  content: '';
  position: absolute;
  background: white;
}

.plus-icon-small:before {
  width: 12px;
  height: 2px;
  top: 5px;
  left: 0;
}

.plus-icon-small:after {
  height: 12px;
  width: 2px;
  left: 5px;
  top: 0;
}

/* 概览部分样式 */
.overview-section {
  margin: 15px;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  padding: 15px;
}

.overview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.date-picker {
  display: flex;
  align-items: center;
  background: #F5F7FA;
  border-radius: 15px;
  padding: 5px 10px;
}

.date-text {
  font-size: 12px;
  color: #666;
  margin-right: 5px;
}

.date-icon {
  width: 8px;
  height: 8px;
  border-top: 2px solid #666;
  border-right: 2px solid #666;
  transform: rotate(135deg);
}

/* 统计卡片样式 */
.stats-cards {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -7.5px;
}

.stats-card {
  width: 50%;
  padding: 7.5px;
  box-sizing: border-box;
  position: relative;
}

.card-value {
  font-size: 20px;
  font-weight: 600;
  color: #333;
  margin-bottom: 5px;
  background: #F8FAFC;
  padding: 15px;
  border-radius: 10px;
  border-left: 3px solid #FF3B30;
}

.card-label {
  font-size: 12px;
  color: #999;
  position: absolute;
  bottom: 20px;
  left: 25px;
}

.card-trend {
  position: absolute;
  bottom: 20px;
  right: 25px;
  display: flex;
  align-items: center;
  font-size: 12px;
}

.card-trend.up {
  color: #34C759;
}

.card-trend.down {
  color: #FF3B30;
}

.trend-arrow {
  width: 0;
  height: 0;
  margin-right: 3px;
}

.card-trend.up .trend-arrow {
  border-left: 4px solid transparent;
  border-right: 4px solid transparent;
  border-bottom: 6px solid #34C759;
}

.card-trend.down .trend-arrow {
  border-left: 4px solid transparent;
  border-right: 4px solid transparent;
  border-top: 6px solid #FF3B30;
}

/* 红包列表样式 */
.redpacket-list-section {
  margin: 15px;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  padding: 15px;
}

.tab-header {
  display: flex;
  border-bottom: 1px solid #f0f0f0;
  margin-bottom: 15px;
}

.tab-item {
  padding: 0 15px 10px;
  font-size: 14px;
  color: #666;
  position: relative;
}

.tab-item.active {
  color: #FF3B30;
  font-weight: 500;
}

.tab-item.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 15px;
  right: 15px;
  height: 2px;
  background: #FF3B30;
  border-radius: 1px;
}

.redpacket-list {
  margin-top: 15px;
}

.redpacket-item {
  background: #FFFFFF;
  border-radius: 10px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  margin-bottom: 15px;
  overflow: hidden;
}

.redpacket-header {
  display: flex;
  justify-content: space-between;
  padding: 10px 15px;
  background: #FFF8F8;
  border-bottom: 1px solid #FFE8E8;
}

.redpacket-type {
  font-size: 12px;
  padding: 2px 8px;
  border-radius: 10px;
}

.type-fixed {
  background: rgba(255, 59, 48, 0.1);
  color: #FF3B30;
}

.type-random {
  background: rgba(255, 149, 0, 0.1);
  color: #FF9500;
}

.redpacket-status {
  font-size: 12px;
  padding: 2px 8px;
  border-radius: 10px;
}

.status-active {
  background: rgba(52, 199, 89, 0.1);
  color: #34C759;
}

.status-upcoming {
  background: rgba(0, 122, 255, 0.1);
  color: #007AFF;
}

.status-ended {
  background: rgba(142, 142, 147, 0.1);
  color: #8E8E93;
}

.status-draft {
  background: rgba(255, 204, 0, 0.1);
  color: #FFCC00;
}

.redpacket-content {
  display: flex;
  padding: 15px;
}

.redpacket-icon {
  width: 50px;
  height: 50px;
  border-radius: 25px;
  margin-right: 15px;
  position: relative;
}

.redpacket-icon.fixed {
  background: linear-gradient(135deg, #FF5858, #FF0000);
}

.redpacket-icon.fixed::before,
.redpacket-icon.random::before {
  content: '¥';
  position: absolute;
  color: white;
  font-size: 24px;
  font-weight: bold;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.redpacket-icon.random {
  background: linear-gradient(135deg, #FF9500, #FF5E3A);
}

.redpacket-info {
  flex: 1;
}

.redpacket-name {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 5px;
  display: block;
}

.redpacket-time {
  font-size: 12px;
  color: #999;
  margin-bottom: 5px;
  display: block;
}

.redpacket-amount {
  font-size: 12px;
}

.amount-label {
  color: #999;
}

.amount-value {
  color: #FF3B30;
  font-weight: 500;
}

.redpacket-stats {
  padding: 0 15px 15px;
  border-bottom: 1px solid #f0f0f0;
}

.stat-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 5px;
}

.stat-row:last-child {
  margin-bottom: 0;
}

.stat-label {
  font-size: 12px;
  color: #999;
}

.stat-value {
  font-size: 12px;
  color: #333;
  font-weight: 500;
}

.redpacket-actions {
  display: flex;
  padding: 10px 15px;
}

.action-btn {
  font-size: 12px;
  color: #FF3B30;
  background: rgba(255, 59, 48, 0.1);
  padding: 5px 10px;
  border-radius: 15px;
  margin-right: 10px;
}

.action-btn.delete {
  color: #FF3B30;
  background: rgba(255, 59, 48, 0.1);
}

.empty-state {
  padding: 30px 0;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.empty-icon {
  width: 60px;
  height: 60px;
  background: rgba(255, 59, 48, 0.1);
  border-radius: 30px;
  margin-bottom: 15px;
  position: relative;
}

.empty-icon::before {
  content: '';
  width: 30px;
  height: 30px;
  border: 2px solid #FF3B30;
  border-radius: 15px;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.empty-icon::after {
  content: '';
  width: 2px;
  height: 15px;
  background: #FF3B30;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) rotate(45deg);
}

.empty-text {
  font-size: 14px;
  color: #999;
}

/* 营销工具样式 */
.tools-section {
  margin: 15px;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  padding: 15px;
}

.tools-grid {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -7.5px;
}

.tool-card {
  width: 25%;
  padding: 7.5px;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.tool-icon {
  width: 50px;
  height: 50px;
  border-radius: 25px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 8px;
}

.icon-image {
  width: 24px;
  height: 24px;
}

.tool-name {
  font-size: 14px;
  color: #333;
  margin-bottom: 3px;
  text-align: center;
}

.tool-desc {
  font-size: 10px;
  color: #999;
  text-align: center;
  height: 28px;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

/* 红包模板样式 */
.templates-section {
  margin: 15px;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  padding: 15px;
}

.templates-scroll {
  white-space: nowrap;
  margin: 0 -15px;
  padding: 0 15px;
}

.templates-container {
  display: inline-flex;
  padding: 5px 0;
}

.template-card {
  width: 150px;
  border-radius: 12px;
  overflow: hidden;
  margin-right: 15px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.template-preview {
  height: 100px;
  padding: 15px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.template-icon {
  width: 40px;
  height: 40px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 10px;
}

.template-name {
  color: white;
  font-size: 14px;
  font-weight: 600;
}

.template-footer {
  padding: 10px;
  background: white;
  position: relative;
}

.template-desc {
  font-size: 12px;
  color: #999;
  margin-bottom: 25px;
  display: block;
}

.template-use-btn {
  position: absolute;
  bottom: 10px;
  right: 10px;
  font-size: 12px;
  color: #FF3B30;
  background: rgba(255, 59, 48, 0.1);
  padding: 3px 8px;
  border-radius: 10px;
}

/* 营销指南样式 */
.guide-section {
  margin: 15px;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  padding: 15px;
}

.guide-list {
  margin-top: 10px;
}

.guide-item {
  display: flex;
  align-items: center;
  padding: 15px 0;
  border-bottom: 1px solid #f0f0f0;
}

.guide-item:last-child {
  border-bottom: none;
}

.guide-icon {
  width: 40px;
  height: 40px;
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
}

.guide-content {
  flex: 1;
}

.guide-title {
  font-size: 15px;
  font-weight: 500;
  color: #333;
  margin-bottom: 3px;
}

.guide-desc {
  font-size: 12px;
  color: #999;
}

.guide-arrow {
  width: 8px;
  height: 8px;
  border-top: 1.5px solid #999;
  border-right: 1.5px solid #999;
  transform: rotate(45deg);
  margin-left: 15px;
}

/* 浮动操作按钮 */
.floating-action-button {
  position: fixed;
  bottom: 30px;
  right: 30px;
  width: 56px;
  height: 56px;
  border-radius: 28px;
  background: linear-gradient(135deg, #FF5858, #FF0000);
  box-shadow: 0 4px 15px rgba(255, 59, 48, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 100;
}

.fab-icon {
  font-size: 28px;
  color: #fff;
  font-weight: 300;
  line-height: 1;
  margin-top: -2px;
}

/* 响应式调整 */
@media screen and (max-width: 375px) {
  .tool-card {
    width: 33.33%;
  }
}

@media screen and (max-width: 320px) {
  .stats-card {
    width: 100%;
  }
  
  .tool-card {
    width: 50%;
  }
}
</style> 