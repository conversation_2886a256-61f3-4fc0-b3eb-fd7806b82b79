package com.cizhou.user;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;

/**
 * 用户服务启动类
 *
 * <AUTHOR> Team
 * @since 1.0.0
 */
@SpringBootApplication(scanBasePackages = {"com.cizhou.user", "com.cizhou.common"})
@EnableDiscoveryClient
@MapperScan("com.cizhou.user.mapper")
public class UserServiceApplication {

    public static void main(String[] args) {
        SpringApplication.run(UserServiceApplication.class, args);
        System.out.println("👥 磁州生活网用户服务启动成功!");
    }
}
