<template>
  <view class="member-detail-container">
    <!-- 自定义导航栏 -->
    <view class="custom-navbar">
      <view class="navbar-bg" :style="{
        background: 'linear-gradient(135deg, #AC39FF 0%, #B87AFF 100%)'
      }"></view>
      <view class="navbar-content">
        <view class="back-btn" @click="navigateBack">
          <svg class="icon" viewBox="0 0 24 24" width="24" height="24">
            <path d="M19 12H5M12 19l-7-7 7-7" stroke="#FFFFFF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
          </svg>
        </view>
        <view class="navbar-title">成员详情</view>
      </view>
    </view>

    <!-- 内容区域 -->
    <scroll-view 
      class="content-scroll" 
      scroll-y="true"
      :scroll-anchoring="true"
      :enhanced="true"
      :bounces="true"
      :show-scrollbar="false"
    >
      <!-- 成员信息卡片 -->
      <view class="member-card" :style="{
        borderRadius: '35px',
        boxShadow: '0 8px 20px rgba(172,57,255,0.15)',
        background: 'linear-gradient(135deg, #FFFFFF 0%, #F9F5FF 100%)',
        padding: '30rpx',
        marginBottom: '30rpx'
      }">
        <view class="member-header">
          <view class="member-avatar-container">
            <image class="member-avatar" :src="memberDetail.avatar" mode="aspectFill"></image>
            <view class="member-badge" v-if="memberDetail.isVerified" :style="{
              background: 'linear-gradient(135deg, #AC39FF 0%, #B87AFF 100%)'
            }">
              <svg class="icon" viewBox="0 0 24 24" width="12" height="12">
                <path d="M9 12l2 2 4-4" stroke="#FFFFFF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
              </svg>
            </view>
          </view>
          <view class="member-info">
            <view class="member-name-container">
              <text class="member-name">{{ memberDetail.name }}</text>
              <view class="member-level" :style="{
                background: getLevelColor(memberDetail.level)
              }">
                <text>{{ memberDetail.level }}</text>
              </view>
            </view>
            <text class="member-id">ID: {{ memberDetail.id }}</text>
            <view class="member-stats">
              <text class="stat-item">已加入 {{ memberDetail.joinDays }} 天</text>
              <text class="stat-divider">|</text>
              <text class="stat-item">团队 {{ memberDetail.teamCount }} 人</text>
            </view>
          </view>
          <view class="contact-btn" @click="contactMember" :style="{
            background: 'linear-gradient(135deg, #AC39FF 0%, #B87AFF 100%)'
          }">
            <svg class="icon" viewBox="0 0 24 24" width="20" height="20">
              <path d="M22 16.92v3a2 2 0 01-2.18 2 19.79 19.79 0 01-8.63-3.07 19.5 19.5 0 01-6-6 19.79 19.79 0 01-3.07-8.67A2 2 0 014.11 2h3a2 2 0 012 1.72 12.84 12.84 0 00.7 2.81 2 2 0 01-.45 2.11L8.09 9.91a16 16 0 006 6l1.27-1.27a2 2 0 012.11-.45 12.84 12.84 0 002.81.7A2 2 0 0122 16.92z" stroke="#FFFFFF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
            </svg>
            <text>联系Ta</text>
          </view>
        </view>
      </view>
      
      <!-- 贡献概览卡片 -->
      <view class="contribution-card" :style="{
        borderRadius: '35px',
        boxShadow: '0 8px 20px rgba(172,57,255,0.15)',
        background: '#FFFFFF',
        padding: '30rpx',
        marginBottom: '30rpx'
      }">
        <view class="card-header">
          <text class="card-title">贡献概览</text>
        </view>
        
        <view class="contribution-grid">
          <view class="contribution-grid-item">
            <text class="grid-value">{{ memberDetail.totalContribution }}</text>
            <text class="grid-label">累计贡献(元)</text>
          </view>
          <view class="contribution-grid-item">
            <text class="grid-value">{{ memberDetail.monthContribution }}</text>
            <text class="grid-label">本月贡献(元)</text>
          </view>
          <view class="contribution-grid-item">
            <text class="grid-value">{{ memberDetail.orderCount }}</text>
            <text class="grid-label">累计订单</text>
          </view>
          <view class="contribution-grid-item">
            <text class="grid-value">{{ memberDetail.inviteCount }}</text>
            <text class="grid-label">邀请人数</text>
          </view>
        </view>
        
        <view class="contribution-chart">
          <view class="chart-title">
            <text>近6个月贡献趋势</text>
          </view>
          <view class="chart-container">
            <view 
              v-for="(item, index) in memberDetail.contributionTrend" 
              :key="index"
              class="chart-bar-container"
            >
              <view class="chart-bar" :style="{
                height: (item.value / maxContribution) * 200 + 'rpx',
                background: 'linear-gradient(180deg, #AC39FF 0%, #B87AFF 100%)'
              }"></view>
              <text class="chart-label">{{ item.month }}</text>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 下级成员卡片 -->
      <view class="downline-card" :style="{
        borderRadius: '35px',
        boxShadow: '0 8px 20px rgba(172,57,255,0.15)',
        background: '#FFFFFF',
        padding: '30rpx',
        marginBottom: '30rpx'
      }">
        <view class="card-header">
          <text class="card-title">下级成员</text>
          <view class="header-right" v-if="memberDetail.downlineMembers.length > 0">
            <text class="view-all">共{{ memberDetail.downlineMembers.length }}人</text>
          </view>
        </view>
        
        <view class="downline-list">
          <view 
            v-for="(member, index) in memberDetail.downlineMembers" 
            :key="member.id"
            class="downline-item"
            @click="navigateToMemberDetail(member.id)"
          >
            <image class="downline-avatar" :src="member.avatar" mode="aspectFill"></image>
            <view class="downline-info">
              <view class="downline-name-level">
                <text class="downline-name">{{ member.name }}</text>
                <view class="downline-level" :style="{
                  background: getLevelColor(member.level)
                }">
                  <text>{{ member.level }}</text>
                </view>
              </view>
              <view class="downline-stats">
                <text class="downline-stat">{{ member.joinTime }}</text>
                <text class="downline-stat-divider">|</text>
                <text class="downline-stat">贡献收益: ¥{{ member.contribution }}</text>
              </view>
            </view>
            <svg class="arrow-icon" viewBox="0 0 24 24" width="16" height="16">
              <path d="M9 18l6-6-6-6" stroke="#C7C7CC" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
            </svg>
          </view>
          
          <!-- 空状态 -->
          <view class="empty-state" v-if="memberDetail.downlineMembers.length === 0">
            <text class="empty-text">暂无下级成员</text>
          </view>
        </view>
      </view>
      
      <!-- 贡献订单卡片 -->
      <view class="orders-card" :style="{
        borderRadius: '35px',
        boxShadow: '0 8px 20px rgba(172,57,255,0.15)',
        background: '#FFFFFF',
        padding: '30rpx',
        marginBottom: '30rpx'
      }">
        <view class="card-header">
          <text class="card-title">贡献订单</text>
          <view class="header-right" @click="navigateToMemberOrders">
            <text class="view-all">查看全部</text>
            <svg class="icon" viewBox="0 0 24 24" width="16" height="16">
              <path d="M9 18l6-6-6-6" stroke="#AC39FF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
            </svg>
          </view>
        </view>
        
        <view class="order-list">
          <view 
            v-for="(order, index) in memberDetail.orders" 
            :key="order.id"
            class="order-item"
            @click="navigateToOrderDetail(order.id)"
          >
            <view class="order-header">
              <text class="order-id">订单号：{{ order.orderNumber }}</text>
              <text class="order-status" :style="{
                color: getOrderStatusColor(order.status)
              }">{{ getOrderStatusText(order.status) }}</text>
            </view>
            
            <view class="order-product">
              <image class="product-image" :src="order.productImage" mode="aspectFill"></image>
              <view class="product-info">
                <text class="product-name">{{ order.productName }}</text>
                <view class="product-price-qty">
                  <text class="product-price">¥{{ order.productPrice }}</text>
                  <text class="product-qty">x{{ order.quantity }}</text>
                </view>
              </view>
            </view>
            
            <view class="order-footer">
              <view class="order-time">
                <text>{{ order.orderTime }}</text>
              </view>
              <view class="order-commission">
                <text class="commission-label">贡献收益：</text>
                <text class="commission-value">¥{{ order.commission }}</text>
              </view>
            </view>
          </view>
          
          <!-- 空状态 -->
          <view class="empty-state" v-if="memberDetail.orders.length === 0">
            <text class="empty-text">暂无贡献订单</text>
          </view>
        </view>
      </view>
      
      <!-- 底部安全区域 -->
      <view class="safe-area-bottom"></view>
    </scroll-view>
  </view>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';

// 成员详情数据
const memberDetail = ref({
  id: 'M001',
  name: '李四',
  avatar: 'https://via.placeholder.com/100',
  isVerified: true,
  level: '中级分销商',
  joinDays: 120,
  teamCount: 5,
  totalContribution: 1234.56,
  monthContribution: 234.56,
  orderCount: 15,
  inviteCount: 5,
  contributionTrend: [
    { month: '1月', value: 100 },
    { month: '2月', value: 150 },
    { month: '3月', value: 200 },
    { month: '4月', value: 180 },
    { month: '5月', value: 250 },
    { month: '6月', value: 300 }
  ],
  downlineMembers: [
    { id: 'M002', name: '王五', avatar: 'https://via.placeholder.com/50', level: '初级分销商', joinTime: '2023-02-15', contribution: 123.45 },
    { id: 'M003', name: '赵六', avatar: 'https://via.placeholder.com/50', level: '初级分销商', joinTime: '2023-03-01', contribution: 234.56 },
    { id: 'M004', name: '张三', avatar: 'https://via.placeholder.com/50', level: '初级分销商', joinTime: '2023-04-10', contribution: 345.67 }
  ],
  orders: [
    {
      id: 'O001',
      orderNumber: '20230101001',
      status: 'completed',
      productName: 'iPhone 14 Pro 深空黑 256G',
      productImage: 'https://via.placeholder.com/100',
      productPrice: 7999,
      quantity: 1,
      orderTime: '2023-01-01 12:00:00',
      commission: 399.95
    },
    {
      id: 'O002',
      orderNumber: '20230102001',
      status: 'pending_payment',
      productName: '华为Mate 50 Pro 曜金黑 512G',
      productImage: 'https://via.placeholder.com/100',
      productPrice: 6999,
      quantity: 1,
      orderTime: '2023-01-02 12:00:00',
      commission: 349.95
    },
    {
      id: 'O003',
      orderNumber: '20230103001',
      status: 'pending_delivery',
      productName: '小米12S Ultra 陶瓷白 256G',
      productImage: 'https://via.placeholder.com/100',
      productPrice: 5999,
      quantity: 1,
      orderTime: '2023-01-03 12:00:00',
      commission: 299.95
    }
  ]
});

// 计算贡献趋势最大值
const maxContribution = computed(() => {
  return Math.max(...memberDetail.value.contributionTrend.map(item => item.value));
});

// 返回上一页
const navigateBack = () => {
  uni.navigateBack();
};

// 联系成员
const contactMember = () => {
  uni.showModal({
    title: '联系成员',
    content: '确定要联系该成员吗？',
    success: (res) => {
      if (res.confirm) {
        // 这里可以实现联系功能，例如拨打电话、发送消息等
        uni.showToast({
          title: '联系功能暂未开放',
          icon: 'none'
        });
      }
    }
  });
};

// 获取等级颜色
const getLevelColor = (level) => {
  switch(level) {
    case '初级分销商':
      return 'linear-gradient(135deg, #5AC8FA 0%, #1C84FF 100%)';
    case '中级分销商':
      return 'linear-gradient(135deg, #FF9500 0%, #FFCC00 100%)';
    case '高级分销商':
      return 'linear-gradient(135deg, #FF3B69 0%, #FF7A9E 100%)';
    default:
      return 'linear-gradient(135deg, #8E8E93 0%, #C7C7CC 100%)';
  }
};

// 获取订单状态颜色
const getOrderStatusColor = (status) => {
  switch(status) {
    case 'pending_payment':
      return '#FF9500';
    case 'pending_delivery':
      return '#5AC8FA';
    case 'delivered':
      return '#34C759';
    case 'completed':
      return '#AC39FF';
    case 'invalid':
      return '#8E8E93';
    default:
      return '#333333';
  }
};

// 获取订单状态文本
const getOrderStatusText = (status) => {
  switch(status) {
    case 'pending_payment':
      return '待付款';
    case 'pending_delivery':
      return '待发货';
    case 'delivered':
      return '已发货';
    case 'completed':
      return '已完成';
    case 'invalid':
      return '已失效';
    default:
      return '未知状态';
  }
};

// 导航到成员详情页
const navigateToMemberDetail = (id) => {
  uni.navigateTo({
    url: `/subPackages/activity-showcase/pages/distribution/member-detail/index?id=${id}`
  });
};

// 导航到订单详情页
const navigateToOrderDetail = (id) => {
  uni.navigateTo({
    url: `/subPackages/activity-showcase/pages/distribution/order-detail/index?id=${id}`
  });
};

// 导航到成员订单页面
const navigateToMemberOrders = () => {
  uni.navigateTo({
    url: `/subPackages/activity-showcase/pages/distribution/member-orders/index?id=${memberDetail.value.id}`
  });
};

// 页面加载
onMounted(() => {
  // 获取成员ID
  const eventChannel = getOpenerEventChannel();
  if (eventChannel) {
    eventChannel.on('getMemberId', (data) => {
      // 根据成员ID获取成员详情
      // fetchMemberDetail(data.id);
      console.log('成员ID:', data.id);
    });
  }

  // 获取页面参数
  const query = uni.getLaunchOptionsSync().query;
  if (query && query.id) {
    // 根据成员ID获取成员详情
    // fetchMemberDetail(query.id);
    console.log('成员ID:', query.id);
  }
});

// 获取成员详情
const fetchMemberDetail = (id) => {
  // 这里应该是实际的API调用
  // 示例中使用模拟数据
};
</script>

<style lang="scss" scoped>
.member-detail-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #F8F8FA;
  position: relative;
}

.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  
  .navbar-bg {
    height: 180rpx;
    width: 100%;
  }
  
  .navbar-content {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 180rpx;
    display: flex;
    align-items: center;
    padding: 0 30rpx;
    padding-top: var(--status-bar-height);
    
    .back-btn {
      width: 60rpx;
      height: 60rpx;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    
    .navbar-title {
      flex: 1;
      text-align: center;
      font-size: 36rpx;
      font-weight: 600;
      color: #FFFFFF;
    }
  }
}

.content-scroll {
  flex: 1;
  padding: 30rpx;
  padding-top: calc(180rpx + 30rpx);
}

.member-card {
  .member-header {
    display: flex;
    align-items: center;
    
    .member-avatar-container {
      position: relative;
      margin-right: 20rpx;
      
      .member-avatar {
        width: 120rpx;
        height: 120rpx;
        border-radius: 50%;
        border: 4rpx solid #FFFFFF;
        box-shadow: 0 4rpx 8rpx rgba(0,0,0,0.1);
      }
      
      .member-badge {
        position: absolute;
        bottom: 0;
        right: 0;
        width: 36rpx;
        height: 36rpx;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        border: 2rpx solid #FFFFFF;
      }
    }
    
    .member-info {
      flex: 1;
      
      .member-name-container {
        display: flex;
        align-items: center;
        margin-bottom: 10rpx;
        
        .member-name {
          font-size: 32rpx;
          font-weight: 600;
          color: #333333;
          margin-right: 16rpx;
        }
        
        .member-level {
          padding: 4rpx 16rpx;
          border-radius: 20rpx;
          
          text {
            color: #FFFFFF;
            font-size: 22rpx;
          }
        }
      }
      
      .member-id {
        font-size: 24rpx;
        color: #666666;
        margin-bottom: 10rpx;
      }
      
      .member-stats {
        display: flex;
        align-items: center;
        
        .stat-item {
          font-size: 24rpx;
          color: #666666;
        }
        
        .stat-divider {
          margin: 0 10rpx;
          color: #CCCCCC;
        }
      }
    }
    
    .contact-btn {
      padding: 10rpx 20rpx;
      border-radius: 30rpx;
      display: flex;
      align-items: center;
      
      text {
        color: #FFFFFF;
        font-size: 24rpx;
        margin-left: 6rpx;
      }
    }
  }
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
  
  .card-title {
    font-size: 32rpx;
    font-weight: 600;
    color: #333333;
  }
  
  .header-right {
    display: flex;
    align-items: center;
    
    .view-all {
      font-size: 26rpx;
      color: #AC39FF;
      margin-right: 6rpx;
    }
  }
}

.contribution-grid {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 30rpx;
  
  .contribution-grid-item {
    width: 50%;
    padding: 20rpx 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    
    .grid-value {
      font-size: 36rpx;
      font-weight: 600;
      color: #333333;
      margin-bottom: 10rpx;
    }
    
    .grid-label {
      font-size: 24rpx;
      color: #666666;
    }
  }
}

.contribution-chart {
  .chart-title {
    margin-bottom: 20rpx;
    
    text {
      font-size: 28rpx;
      color: #333333;
    }
  }
  
  .chart-container {
    display: flex;
    justify-content: space-between;
    align-items: flex-end;
    height: 260rpx;
    
    .chart-bar-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      width: calc(100% / 6 - 10rpx);
      
      .chart-bar {
        width: 30rpx;
        border-radius: 15rpx;
      }
      
      .chart-label {
        margin-top: 10rpx;
        font-size: 22rpx;
        color: #666666;
      }
    }
  }
}

.downline-list {
  .downline-item {
    display: flex;
    align-items: center;
    padding: 20rpx 0;
    border-bottom: 1px solid #EEEEEE;
    
    &:last-child {
      border-bottom: none;
    }
    
    .downline-avatar {
      width: 80rpx;
      height: 80rpx;
      border-radius: 50%;
      margin-right: 20rpx;
    }
    
    .downline-info {
      flex: 1;
      
      .downline-name-level {
        display: flex;
        align-items: center;
        margin-bottom: 10rpx;
        
        .downline-name {
          font-size: 28rpx;
          color: #333333;
          margin-right: 16rpx;
        }
        
        .downline-level {
          padding: 2rpx 12rpx;
          border-radius: 16rpx;
          
          text {
            color: #FFFFFF;
            font-size: 20rpx;
          }
        }
      }
      
      .downline-stats {
        display: flex;
        align-items: center;
        
        .downline-stat {
          font-size: 24rpx;
          color: #666666;
        }
        
        .downline-stat-divider {
          margin: 0 10rpx;
          color: #CCCCCC;
        }
      }
    }
    
    .arrow-icon {
      margin-left: 10rpx;
    }
  }
}

.order-list {
  .order-item {
    background-color: #F9F9F9;
    border-radius: 20rpx;
    padding: 20rpx;
    margin-bottom: 20rpx;
    
    &:last-child {
      margin-bottom: 0;
    }
    
    .order-header {
      display: flex;
      justify-content: space-between;
      margin-bottom: 20rpx;
      
      .order-id {
        font-size: 24rpx;
        color: #666666;
      }
      
      .order-status {
        font-size: 24rpx;
      }
    }
    
    .order-product {
      display: flex;
      margin-bottom: 20rpx;
      
      .product-image {
        width: 120rpx;
        height: 120rpx;
        border-radius: 12rpx;
        margin-right: 20rpx;
      }
      
      .product-info {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        
        .product-name {
          font-size: 28rpx;
          color: #333333;
        }
        
        .product-price-qty {
          display: flex;
          justify-content: space-between;
          
          .product-price {
            font-size: 28rpx;
            color: #333333;
          }
          
          .product-qty {
            font-size: 24rpx;
            color: #999999;
          }
        }
      }
    }
    
    .order-footer {
      display: flex;
      justify-content: space-between;
      align-items: center;
      
      .order-time {
        font-size: 24rpx;
        color: #999999;
      }
      
      .order-commission {
        display: flex;
        align-items: center;
        
        .commission-label {
          font-size: 24rpx;
          color: #666666;
        }
        
        .commission-value {
          font-size: 28rpx;
          color: #FF3B69;
          font-weight: 600;
        }
      }
    }
  }
}

.empty-state {
  padding: 60rpx 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  
  .empty-text {
    font-size: 28rpx;
    color: #999999;
  }
}

.safe-area-bottom {
  height: 50rpx;
}

/* 适配小屏幕手机 */
@media screen and (max-width: 375px) {
  .card-header {
    .card-title {
      font-size: 28rpx;
    }
  }
  
  .contribution-grid {
    .contribution-grid-item {
      .grid-value {
        font-size: 32rpx;
      }
      
      .grid-label {
        font-size: 22rpx;
      }
    }
  }
}
</style> 