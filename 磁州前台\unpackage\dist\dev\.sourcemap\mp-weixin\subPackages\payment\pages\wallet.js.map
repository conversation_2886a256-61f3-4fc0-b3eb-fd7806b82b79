{"version": 3, "file": "wallet.js", "sources": ["subPackages/payment/pages/wallet.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/c3ViUGFja2FnZXNccGF5bWVudFxwYWdlc1x3YWxsZXQudnVl"], "sourcesContent": ["<template>\n  <wallet-page \n    scene=\"main\" \n    path-prefix=\"/subPackages/payment/pages\">\n  </wallet-page>\n</template>\n\n<script setup>\nimport WalletPage from '@/components/wallet/WalletPage.vue';\n\n// 设置页面标题栏\nuni.setNavigationBarColor({\n  frontColor: '#ffffff',\n  backgroundColor: '#1677FF'\n});\n</script>\n\n<style>\n/* 页面样式已移至组件中 */\n</style> \n", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/subPackages/payment/pages/wallet.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni", "MiniProgramPage"], "mappings": ";;;;;AAQA,MAAM,aAAa,MAAW;;;;AAG9BA,kBAAG,MAAC,sBAAsB;AAAA,MACxB,YAAY;AAAA,MACZ,iBAAiB;AAAA,IACnB,CAAC;;;;;;;;;;;ACbD,GAAG,WAAWC,SAAe;"}