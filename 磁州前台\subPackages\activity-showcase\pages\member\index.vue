<template>
  <view class="member-container">
    <!-- 自定义导航栏 -->
    <view class="custom-navbar">
      <view class="navbar-content">
        <view class="back-btn" @click="goBack">
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <polyline points="15 18 9 12 15 6"></polyline>
          </svg>
        </view>
        <view class="navbar-title">会员中心</view>
        <view class="navbar-right">
          <view class="upgrade-btn" @click="showUpgradeModal" v-if="!isVip">
            <text class="upgrade-text">升级</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 内容区域 -->
    <scroll-view class="content-scroll" scroll-y>
      <!-- 会员状态卡片 -->
      <view class="member-status-card">
        <view class="status-background">
          <view class="status-pattern"></view>
        </view>
        <view class="status-content">
          <view class="member-info">
            <view class="member-avatar">
              <image class="avatar-img" :src="userInfo.avatar" mode="aspectFill"></image>
              <view class="vip-badge" v-if="isVip">
                <text class="badge-text">VIP</text>
              </view>
            </view>
            <view class="member-details">
              <text class="member-name">{{ userInfo.name }}</text>
              <text class="member-level">{{ memberLevel.name }}</text>
              <text class="member-expire" v-if="isVip">有效期至：{{ vipExpireDate }}</text>
            </view>
          </view>
          
          <view class="member-progress" v-if="!isVip">
            <view class="progress-header">
              <text class="progress-title">升级进度</text>
              <text class="progress-desc">再消费¥{{ needAmount }}升级至{{ nextLevel.name }}</text>
            </view>
            <view class="progress-bar">
              <view class="progress-fill" :style="{ width: `${upgradeProgress}%` }"></view>
            </view>
          </view>
        </view>
      </view>

      <!-- 会员权益 -->
      <view class="member-benefits">
        <view class="section-header">
          <text class="section-title">会员权益</text>
          <text class="section-desc">专属特权，尊享体验</text>
        </view>

        <view class="benefits-grid">
          <view 
            class="benefit-item" 
            v-for="(benefit, index) in memberBenefits" 
            :key="index"
            :class="{ active: benefit.available }"
          >
            <view class="benefit-icon">
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path v-if="benefit.type === 'discount'" d="M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z"></path>
                <path v-else-if="benefit.type === 'shipping'" d="M16 3h5v5M4 20L20 4M21 16v5h-5M4 8V3h5"></path>
                <path v-else-if="benefit.type === 'priority'" d="M12 2l3.09 6.26L22 9l-5 4.87L18.18 21 12 17.27 5.82 21 7 13.87 2 9l6.91-.74L12 2z"></path>
                <path v-else-if="benefit.type === 'service'" d="M22 16.92V18C22 18.55 21.55 19 21 19C9.4 19 0 9.6 0 -2C0 -2.55 0.45 -3 1 -3H2.08C2.6 -3 3.02 -2.64 3.07 -2.12L3.4 0.88C3.44 1.35 3.23 1.8 2.84 2.07L1.37 3.17C2.93 6.34 5.66 9.07 8.83 10.63L9.93 9.16C10.2 8.77 10.65 8.56 11.12 8.6L14.12 8.93C14.64 8.98 15 9.4 15 9.92V11C15 11.55 14.55 12 14 12C12.4 12 11 10.6 11 9"></path>
                <circle v-else cx="12" cy="12" r="10"></circle>
              </svg>
            </view>
            <text class="benefit-title">{{ benefit.title }}</text>
            <text class="benefit-desc">{{ benefit.description }}</text>
            <view class="benefit-status">
              <text class="status-text" v-if="benefit.available">已享受</text>
              <text class="status-text unavailable" v-else>升级可享</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 会员等级 -->
      <view class="member-levels">
        <view class="section-header">
          <text class="section-title">会员等级</text>
          <text class="section-desc">不同等级享受不同权益</text>
        </view>

        <view class="levels-list">
          <view 
            class="level-item" 
            v-for="(level, index) in memberLevels" 
            :key="index"
            :class="{ current: level.id === memberLevel.id }"
          >
            <view class="level-icon">
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <polygon points="12 2 15.09 8.26 22 9 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9 8.91 8.26 12 2"></polygon>
              </svg>
            </view>
            <view class="level-content">
              <text class="level-name">{{ level.name }}</text>
              <text class="level-requirement">{{ level.requirement }}</text>
            </view>
            <view class="level-benefits">
              <text class="benefits-text">{{ level.benefits.join('、') }}</text>
            </view>
            <view class="level-status">
              <view class="current-badge" v-if="level.id === memberLevel.id">
                <text class="badge-text">当前等级</text>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 会员数据 -->
      <view class="member-stats">
        <view class="section-header">
          <text class="section-title">我的数据</text>
          <text class="section-desc">会员消费及权益使用情况</text>
        </view>

        <view class="stats-grid">
          <view class="stat-item">
            <text class="stat-value">¥{{ totalSpent }}</text>
            <text class="stat-label">累计消费</text>
          </view>
          <view class="stat-item">
            <text class="stat-value">{{ totalOrders }}</text>
            <text class="stat-label">订单数量</text>
          </view>
          <view class="stat-item">
            <text class="stat-value">¥{{ totalSaved }}</text>
            <text class="stat-label">节省金额</text>
          </view>
          <view class="stat-item">
            <text class="stat-value">{{ memberDays }}</text>
            <text class="stat-label">会员天数</text>
          </view>
        </view>
      </view>

      <!-- 专属服务 -->
      <view class="member-services">
        <view class="section-header">
          <text class="section-title">专属服务</text>
          <text class="section-desc">会员专享服务通道</text>
        </view>

        <view class="services-list">
          <view 
            class="service-item" 
            v-for="(service, index) in memberServices" 
            :key="index"
            @click="navigateToService(service)"
          >
            <view class="service-icon">
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path v-if="service.type === 'customer'" d="M22 16.92V18C22 18.55 21.55 19 21 19C9.4 19 0 9.6 0 -2C0 -2.55 0.45 -3 1 -3H2.08C2.6 -3 3.02 -2.64 3.07 -2.12L3.4 0.88C3.44 1.35 3.23 1.8 2.84 2.07L1.37 3.17C2.93 6.34 5.66 9.07 8.83 10.63L9.93 9.16C10.2 8.77 10.65 8.56 11.12 8.6L14.12 8.93C14.64 8.98 15 9.4 15 9.92V11C15 11.55 14.55 12 14 12C12.4 12 11 10.6 11 9"></path>
                <path v-else-if="service.type === 'return'" d="M9 14L4 9L9 4M20 20V13C20 10.79 18.21 9 16 9H4"></path>
                <path v-else-if="service.type === 'exclusive'" d="M12 2L15.09 8.26L22 9L17 14.14L18.18 21L12 17.77L5.82 21L7 14.14L2 9L8.91 8.26L12 2Z"></path>
                <circle v-else cx="12" cy="12" r="10"></circle>
              </svg>
            </view>
            <view class="service-content">
              <text class="service-title">{{ service.title }}</text>
              <text class="service-desc">{{ service.description }}</text>
            </view>
            <view class="service-arrow">
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <polyline points="9 18 15 12 9 6"></polyline>
              </svg>
            </view>
          </view>
        </view>
      </view>

      <!-- 底部安全区域 -->
      <view class="safe-area-bottom"></view>
    </scroll-view>

    <!-- 升级会员弹窗 -->
    <view class="upgrade-modal" v-if="showUpgrade" @click="hideUpgradeModal">
      <view class="modal-content" @click.stop>
        <view class="modal-header">
          <text class="modal-title">升级会员</text>
          <view class="close-btn" @click="hideUpgradeModal">
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <line x1="18" y1="6" x2="6" y2="18"></line>
              <line x1="6" y1="6" x2="18" y2="18"></line>
            </svg>
          </view>
        </view>
        <view class="modal-body">
          <view class="upgrade-plans">
            <view 
              class="plan-item" 
              v-for="(plan, index) in upgradePlans" 
              :key="index"
              :class="{ selected: selectedPlan === plan.id }"
              @click="selectPlan(plan.id)"
            >
              <view class="plan-header">
                <text class="plan-name">{{ plan.name }}</text>
                <text class="plan-price">¥{{ plan.price }}/{{ plan.period }}</text>
              </view>
              <view class="plan-benefits">
                <text class="benefits-text">{{ plan.benefits.join('、') }}</text>
              </view>
            </view>
          </view>
        </view>
        <view class="modal-actions">
          <view class="action-btn cancel" @click="hideUpgradeModal">
            <text class="btn-text">取消</text>
          </view>
          <view class="action-btn confirm" @click="confirmUpgrade">
            <text class="btn-text">立即升级</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'

// 响应式数据
const isVip = ref(false)
const showUpgrade = ref(false)
const selectedPlan = ref(1)

// 用户信息
const userInfo = ref({
  name: '张三',
  avatar: '/static/images/avatar/default.jpg'
})

// 会员等级
const memberLevel = ref({
  id: 1,
  name: '普通会员'
})

// 下一等级
const nextLevel = ref({
  id: 2,
  name: '银卡会员'
})

// 升级所需金额
const needAmount = ref(500)
const currentSpent = ref(200)

// VIP到期时间
const vipExpireDate = ref('2024-12-31')

// 会员数据
const totalSpent = ref(1580)
const totalOrders = ref(28)
const totalSaved = ref(320)
const memberDays = ref(365)

// 计算升级进度
const upgradeProgress = computed(() => {
  return Math.min((currentSpent.value / (currentSpent.value + needAmount.value)) * 100, 100)
})

// 会员权益
const memberBenefits = ref([
  {
    title: '专属折扣',
    description: '享受会员专属价格',
    type: 'discount',
    available: isVip.value
  },
  {
    title: '免费配送',
    description: '全场包邮，快速到达',
    type: 'shipping',
    available: isVip.value
  },
  {
    title: '优先客服',
    description: '专属客服，优先响应',
    type: 'service',
    available: isVip.value
  },
  {
    title: '生日特权',
    description: '生日月专属优惠',
    type: 'priority',
    available: isVip.value
  }
])

// 会员等级列表
const memberLevels = ref([
  {
    id: 1,
    name: '普通会员',
    requirement: '注册即可获得',
    benefits: ['基础折扣', '积分奖励']
  },
  {
    id: 2,
    name: '银卡会员',
    requirement: '累计消费满500元',
    benefits: ['9.5折优惠', '免费配送', '生日礼品']
  },
  {
    id: 3,
    name: '金卡会员',
    requirement: '累计消费满2000元',
    benefits: ['9折优惠', '专属客服', '优先退换货']
  },
  {
    id: 4,
    name: '钻石会员',
    requirement: '累计消费满5000元',
    benefits: ['8.5折优惠', '专属活动', '定制服务']
  }
])

// 专属服务
const memberServices = ref([
  {
    title: '专属客服',
    description: '7×24小时专属客服服务',
    type: 'customer',
    url: '/pages/service/customer'
  },
  {
    title: '极速退换',
    description: '会员专享快速退换货通道',
    type: 'return',
    url: '/pages/service/return'
  },
  {
    title: '专属活动',
    description: '会员专享活动和优惠',
    type: 'exclusive',
    url: '/pages/service/exclusive'
  }
])

// 升级套餐
const upgradePlans = ref([
  {
    id: 1,
    name: '月度会员',
    price: 15,
    period: '月',
    benefits: ['9.5折优惠', '免费配送', '专属客服']
  },
  {
    id: 2,
    name: '季度会员',
    price: 39,
    period: '季',
    benefits: ['9折优惠', '免费配送', '专属客服', '生日礼品']
  },
  {
    id: 3,
    name: '年度会员',
    price: 128,
    period: '年',
    benefits: ['8.5折优惠', '免费配送', '专属客服', '生日礼品', '专属活动']
  }
])

// 页面加载
onMounted(() => {
  console.log('会员中心页面加载')
  loadMemberData()
})

// 方法
function goBack() {
  uni.navigateBack()
}

function showUpgradeModal() {
  showUpgrade.value = true
}

function hideUpgradeModal() {
  showUpgrade.value = false
}

function selectPlan(planId) {
  selectedPlan.value = planId
}

function confirmUpgrade() {
  const plan = upgradePlans.value.find(p => p.id === selectedPlan.value)
  
  uni.showModal({
    title: '确认升级',
    content: `确定要升级为${plan.name}吗？费用为¥${plan.price}`,
    success: (res) => {
      if (res.confirm) {
        // 执行升级逻辑
        isVip.value = true
        memberLevel.value = { id: 2, name: '银卡会员' }
        hideUpgradeModal()
        
        uni.showToast({
          title: '升级成功',
          icon: 'success'
        })
      }
    }
  })
}

function navigateToService(service) {
  if (!isVip.value) {
    uni.showModal({
      title: '提示',
      content: '该服务需要升级会员后才能使用',
      confirmText: '立即升级',
      success: (res) => {
        if (res.confirm) {
          showUpgradeModal()
        }
      }
    })
    return
  }
  
  uni.navigateTo({
    url: service.url
  })
}

function loadMemberData() {
  // 模拟加载会员数据
  setTimeout(() => {
    console.log('会员数据加载完成')
  }, 500)
}
</script>

<style scoped>
/* 会员中心样式开始 */
.member-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #795548 0%, #5D4037 100%);
}

/* 自定义导航栏样式 */
.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: rgba(121, 85, 72, 0.95);
  backdrop-filter: blur(10px);
  padding-top: var(--status-bar-height, 44px);
}

.navbar-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 44px;
  padding: 0 16px;
}

.back-btn {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 16px;
  background: rgba(255, 255, 255, 0.2);
}

.back-btn svg {
  color: white;
}

.navbar-title {
  font-size: 18px;
  font-weight: 600;
  color: white;
}

.upgrade-btn {
  padding: 6px 16px;
  background: linear-gradient(135deg, #FFD700, #FFA500);
  border-radius: 16px;
}

.upgrade-text {
  font-size: 14px;
  color: #333;
  font-weight: 600;
}

/* 内容区域样式 */
.content-scroll {
  padding-top: calc(var(--status-bar-height, 44px) + 44px);
  height: 100vh;
}

/* 会员状态卡片样式 */
.member-status-card {
  position: relative;
  margin: 20px 16px;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
}

.status-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, #FFD700, #FFA500);
}

.status-pattern {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: radial-gradient(circle at 20% 80%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
                    radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
}

.status-content {
  position: relative;
  padding: 24px;
  z-index: 1;
}

.member-info {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 20px;
}

.member-avatar {
  position: relative;
  width: 64px;
  height: 64px;
  border-radius: 32px;
  overflow: hidden;
  border: 3px solid rgba(255, 255, 255, 0.3);
}

.avatar-img {
  width: 100%;
  height: 100%;
}

.vip-badge {
  position: absolute;
  bottom: -2px;
  right: -2px;
  width: 24px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #E91E63;
  border-radius: 8px;
}

.vip-badge .badge-text {
  font-size: 10px;
  color: white;
  font-weight: 600;
}

.member-details {
  flex: 1;
}

.member-name {
  display: block;
  font-size: 20px;
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
}

.member-level {
  display: block;
  font-size: 16px;
  color: rgba(0, 0, 0, 0.7);
  margin-bottom: 4px;
}

.member-expire {
  display: block;
  font-size: 14px;
  color: rgba(0, 0, 0, 0.6);
}

.member-progress {
  background: rgba(255, 255, 255, 0.9);
  padding: 16px;
  border-radius: 12px;
}

.progress-header {
  margin-bottom: 8px;
}

.progress-title {
  display: block;
  font-size: 14px;
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
}

.progress-desc {
  display: block;
  font-size: 12px;
  color: #666;
}

.progress-bar {
  height: 6px;
  background: rgba(0, 0, 0, 0.1);
  border-radius: 3px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #795548, #5D4037);
  border-radius: 3px;
  transition: width 0.3s ease;
}

/* 会员权益样式 */
.member-benefits {
  margin: 0 16px 20px;
  padding: 20px;
  background: white;
  border-radius: 16px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
}

.section-header {
  margin-bottom: 16px;
}

.section-title {
  display: block;
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
}

.section-desc {
  display: block;
  font-size: 14px;
  color: #666;
}

.benefits-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
}

.benefit-item {
  padding: 16px;
  background: #f8f9fa;
  border-radius: 12px;
  text-align: center;
  border: 2px solid transparent;
  transition: all 0.3s ease;
}

.benefit-item.active {
  background: rgba(121, 85, 72, 0.1);
  border-color: #795548;
}

.benefit-icon {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #e0e0e0;
  border-radius: 20px;
  margin: 0 auto 8px;
}

.benefit-item.active .benefit-icon {
  background: #795548;
}

.benefit-icon svg {
  color: #666;
}

.benefit-item.active .benefit-icon svg {
  color: white;
}

.benefit-title {
  display: block;
  font-size: 14px;
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
}

.benefit-desc {
  display: block;
  font-size: 12px;
  color: #666;
  margin-bottom: 8px;
}

.benefit-status {
  margin-top: 8px;
}

.status-text {
  font-size: 12px;
  color: #795548;
  font-weight: 500;
}

.status-text.unavailable {
  color: #999;
}

/* 会员等级样式 */
.member-levels {
  margin: 0 16px 20px;
  padding: 20px;
  background: white;
  border-radius: 16px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
}

.levels-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.level-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 12px;
  border: 2px solid transparent;
}

.level-item.current {
  background: rgba(121, 85, 72, 0.1);
  border-color: #795548;
}

.level-icon {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #e0e0e0;
  border-radius: 16px;
}

.level-item.current .level-icon {
  background: #795548;
}

.level-icon svg {
  color: #666;
}

.level-item.current .level-icon svg {
  color: white;
}

.level-content {
  flex: 1;
}

.level-name {
  display: block;
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
}

.level-requirement {
  display: block;
  font-size: 12px;
  color: #666;
}

.level-benefits {
  margin-right: 12px;
}

.benefits-text {
  font-size: 12px;
  color: #795548;
}

.current-badge {
  padding: 4px 8px;
  background: #795548;
  border-radius: 8px;
}

.current-badge .badge-text {
  font-size: 10px;
  color: white;
  font-weight: 500;
}

/* 会员数据样式 */
.member-stats {
  margin: 0 16px 20px;
  padding: 20px;
  background: white;
  border-radius: 16px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
}

.stat-item {
  text-align: center;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 12px;
}

.stat-value {
  display: block;
  font-size: 20px;
  font-weight: 700;
  color: #795548;
  margin-bottom: 4px;
}

.stat-label {
  display: block;
  font-size: 12px;
  color: #666;
}

/* 专属服务样式 */
.member-services {
  margin: 0 16px 20px;
  padding: 20px;
  background: white;
  border-radius: 16px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
}

.services-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.service-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 12px;
  transition: all 0.3s ease;
}

.service-item:active {
  background: #e9ecef;
}

.service-icon {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #795548;
  border-radius: 20px;
}

.service-icon svg {
  color: white;
}

.service-content {
  flex: 1;
}

.service-title {
  display: block;
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
}

.service-desc {
  display: block;
  font-size: 14px;
  color: #666;
}

.service-arrow svg {
  color: #999;
}

/* 升级弹窗样式 */
.upgrade-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
  padding: 20px;
}

.modal-content {
  background: white;
  border-radius: 16px;
  width: 100%;
  max-width: 400px;
  max-height: 80vh;
  overflow: hidden;
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px;
  border-bottom: 1px solid #f0f0f0;
}

.modal-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.close-btn {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 16px;
  background: #f5f5f5;
}

.close-btn svg {
  color: #666;
}

.modal-body {
  padding: 20px;
  max-height: 50vh;
  overflow-y: auto;
}

.upgrade-plans {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.plan-item {
  padding: 16px;
  background: #f8f9fa;
  border-radius: 12px;
  border: 2px solid transparent;
  transition: all 0.3s ease;
}

.plan-item.selected {
  background: rgba(121, 85, 72, 0.1);
  border-color: #795548;
}

.plan-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8px;
}

.plan-name {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.plan-price {
  font-size: 18px;
  font-weight: 700;
  color: #795548;
}

.plan-benefits .benefits-text {
  font-size: 14px;
  color: #666;
}

.modal-actions {
  display: flex;
  gap: 12px;
  padding: 20px;
  border-top: 1px solid #f0f0f0;
}

.action-btn {
  flex: 1;
  padding: 12px;
  border-radius: 8px;
  text-align: center;
}

.action-btn.cancel {
  background: #f5f5f5;
}

.action-btn.confirm {
  background: #795548;
}

.btn-text {
  font-size: 16px;
  color: #666;
  font-weight: 500;
}

.action-btn.confirm .btn-text {
  color: white;
}

/* 底部安全区域 */
.safe-area-bottom {
  height: env(safe-area-inset-bottom);
  background: transparent;
}
/* 会员中心样式结束 */
</style>
