<template>
  <view class="premium-actions-container">
    <!-- 主要操作面板 -->
    <view class="premium-panel" :class="{ 'panel-expanded': isPanelExpanded }">
      <!-- 面板头部 -->
      <view class="panel-header" @click="togglePanel">
        <text class="panel-title">{{ title }}</text>
        <view class="panel-icon">
          <text class="iconfont" :class="isPanelExpanded ? 'icon-arrow-up' : 'icon-arrow-down'"></text>
        </view>
      </view>
      
      <!-- 面板内容 -->
      <view class="panel-content" v-if="isPanelExpanded">
        <view class="action-grid">
          <!-- 发布操作 -->
          <view class="action-item" @click="handleAction('publish')">
            <view class="action-icon publish-icon">
              <image src="/static/images/premium/publish.png" mode="aspectFit"></image>
            </view>
            <text class="action-label">发布信息</text>
          </view>
          
          <!-- 置顶操作 -->
          <view class="action-item" @click="handleAction('top')">
            <view class="action-icon top-icon">
              <image src="/static/images/premium/top.png" mode="aspectFit"></image>
            </view>
            <text class="action-label">置顶信息</text>
          </view>
          
          <!-- 刷新操作 -->
          <view class="action-item" @click="handleAction('refresh')">
            <view class="action-icon refresh-icon">
              <image src="/static/images/premium/refresh.png" mode="aspectFit"></image>
            </view>
            <text class="action-label">刷新信息</text>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 模态框 -->
    <view class="modal-overlay" v-if="showModal" @click="closeModal"></view>
    <view class="modal-container" v-if="showModal">
      <view class="modal-content">
        <view class="modal-header">
          <text class="modal-title">{{ modalConfig.title }}</text>
          <view class="modal-close" @click="closeModal">
            <text class="iconfont icon-close"></text>
          </view>
        </view>
        
        <view class="modal-body">
          <!-- 操作说明 -->
          <view class="modal-description">
            <text>{{ modalConfig.description }}</text>
          </view>
          
          <!-- 选项列表 -->
          <view class="option-list">
            <view 
              class="option-item" 
              v-for="(option, index) in modalConfig.options" 
              :key="index"
              @click="selectOption(option)"
              :class="{ 'option-selected': selectedOption === option }"
            >
              <view class="option-content">
                <view class="option-icon">
                  <image :src="option.icon" mode="aspectFit"></image>
                </view>
                <view class="option-details">
                  <text class="option-title">{{ option.title }}</text>
                  <text class="option-subtitle">{{ option.subtitle }}</text>
                </view>
              </view>
              <view class="option-price">
                <text>{{ option.price }}</text>
              </view>
            </view>
          </view>
        </view>
        
        <!-- 底部按钮 -->
        <view class="modal-footer">
          <button class="btn-cancel" @click="closeModal">取消</button>
          <button class="btn-confirm" @click="confirmAction" :disabled="!selectedOption">确认</button>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, computed, reactive } from 'vue';

// 组件属性
const props = defineProps({
  title: {
    type: String,
    default: '推广操作'
  },
  infoId: {
    type: String,
    default: ''
  }
});

// 组件事件
const emit = defineEmits(['action-completed']);

// 响应式状态
const isPanelExpanded = ref(false);
const showModal = ref(false);
const selectedOption = ref(null);
const currentAction = ref('');

// 模态框配置
const modalConfig = reactive({
  title: '',
  description: '',
  options: []
});

// 切换面板展开/折叠
const togglePanel = () => {
  isPanelExpanded.value = !isPanelExpanded.value;
};

// 处理操作
const handleAction = (action) => {
  currentAction.value = action;
  selectedOption.value = null;
  
  // 根据不同操作配置模态框
  switch(action) {
    case 'publish':
      configurePublishModal();
      break;
    case 'top':
      configureTopModal();
      break;
    case 'refresh':
      configureRefreshModal();
      break;
  }
  
  showModal.value = true;
};

// 配置发布模态框
const configurePublishModal = () => {
  modalConfig.title = '选择发布方式';
  modalConfig.description = '选择一种方式发布您的信息';
  modalConfig.options = [
    {
      title: '免费发布',
      subtitle: '观看15秒广告后发布',
      price: '免费',
      icon: '/static/images/premium/ad-publish.png',
      type: 'ad'
    },
    {
      title: '付费发布',
      subtitle: '立即发布，无需观看广告',
      price: '¥2.00',
      icon: '/static/images/premium/paid-publish.png',
      type: 'paid'
    }
  ];
};

// 配置置顶模态框
const configureTopModal = () => {
  modalConfig.title = '选择置顶方式';
  modalConfig.description = '选择一种方式将您的信息置顶';
  modalConfig.options = [
    {
      title: '广告置顶',
      subtitle: '观看30秒广告获得2小时置顶',
      price: '免费',
      icon: '/static/images/premium/ad-top.png',
      type: 'ad',
      duration: '2小时'
    },
    {
      title: '付费置顶12小时',
      subtitle: '信息将在首页置顶显示12小时',
      price: '¥5.00',
      icon: '/static/images/premium/paid-top.png',
      type: 'paid',
      duration: '12小时'
    },
    {
      title: '付费置顶24小时',
      subtitle: '信息将在首页置顶显示24小时',
      price: '¥8.00',
      icon: '/static/images/premium/paid-top.png',
      type: 'paid',
      duration: '24小时'
    }
  ];
};

// 配置刷新模态框
const configureRefreshModal = () => {
  modalConfig.title = '选择刷新方式';
  modalConfig.description = '选择一种方式刷新您的信息';
  modalConfig.options = [
    {
      title: '广告刷新',
      subtitle: '观看15秒广告刷新一次',
      price: '免费',
      icon: '/static/images/premium/ad-refresh.png',
      type: 'ad'
    },
    {
      title: '付费刷新',
      subtitle: '立即刷新信息至列表顶部',
      price: '¥1.00',
      icon: '/static/images/premium/paid-refresh.png',
      type: 'paid'
    }
  ];
};

// 选择选项
const selectOption = (option) => {
  selectedOption.value = option;
};

// 关闭模态框
const closeModal = () => {
  showModal.value = false;
};

// 确认操作
const confirmAction = () => {
  if (!selectedOption.value) return;
  
  const { type } = selectedOption.value;
  
  if (type === 'ad') {
    showAd();
  } else if (type === 'paid') {
    processPaidAction();
  }
};

// 显示广告
const showAd = () => {
  // 模拟广告显示
  uni.showLoading({
    title: '正在加载广告...'
  });
  
  setTimeout(() => {
    uni.hideLoading();
    
    // 模拟广告播放完成
    uni.showModal({
      title: '广告观看完成',
      content: '您已成功观看广告，即将完成操作',
      showCancel: false,
      success: () => {
        processActionCompletion();
      }
    });
  }, 1500);
};

// 处理付费操作
const processPaidAction = () => {
  // 模拟支付流程
  uni.showLoading({
    title: '正在处理支付...'
  });
  
  setTimeout(() => {
    uni.hideLoading();
    
    // 模拟支付成功
    uni.showModal({
      title: '支付成功',
      content: '您已成功支付，即将完成操作',
      showCancel: false,
      success: () => {
        processActionCompletion();
      }
    });
  }, 1500);
};

// 处理操作完成
const processActionCompletion = () => {
  // 根据不同操作类型进行处理
  const actionType = currentAction.value;
  const optionType = selectedOption.value.type;
  
  // 关闭模态框
  showModal.value = false;
  
  // 发送操作完成事件
  emit('action-completed', {
    action: actionType,
    type: optionType,
    infoId: props.infoId,
    option: selectedOption.value
  });
  
  // 显示操作成功提示
  let successMessage = '';
  switch(actionType) {
    case 'publish':
      successMessage = '发布成功！';
      break;
    case 'top':
      successMessage = `置顶成功！有效期${selectedOption.value.duration || ''}`;
      break;
    case 'refresh':
      successMessage = '刷新成功！';
      break;
  }
  
  uni.showToast({
    title: successMessage,
    icon: 'success'
  });
};
</script>

<style lang="scss" scoped>
/* 主容器 */
.premium-actions-container {
  width: 100%;
  margin: 20rpx 0;
  font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'SF Pro Text', 'Helvetica Neue', Helvetica, Arial, sans-serif;
}

/* 面板样式 */
.premium-panel {
  background: #FFFFFF;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
  overflow: hidden;
  transition: all 0.3s ease;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx;
  background: linear-gradient(to right, #f8f9fa, #ffffff);
  border-bottom: 1px solid #f0f0f0;
}

.panel-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.panel-icon {
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #666;
  transition: transform 0.3s ease;
}

.panel-expanded .panel-icon {
  transform: rotate(180deg);
}

.panel-content {
  padding: 24rpx;
}

/* 操作网格 */
.action-grid {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -10rpx;
}

.action-item {
  width: calc(33.33% - 20rpx);
  margin: 10rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx 0;
  border-radius: 12rpx;
  background: #f8f9fa;
  transition: all 0.2s ease;
}

.action-item:active {
  transform: scale(0.96);
  background: #f0f0f0;
}

.action-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 12rpx;
}

.action-icon image {
  width: 60%;
  height: 60%;
}

.publish-icon {
  background: linear-gradient(135deg, #4facfe, #00f2fe);
}

.top-icon {
  background: linear-gradient(135deg, #ff9a9e, #fad0c4);
}

.refresh-icon {
  background: linear-gradient(135deg, #a1c4fd, #c2e9fb);
}

.action-label {
  font-size: 26rpx;
  color: #333;
  font-weight: 500;
}

/* 模态框样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  z-index: 1000;
  backdrop-filter: blur(4px);
}

.modal-container {
  position: fixed;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  width: 90%;
  max-width: 650rpx;
  z-index: 1001;
}

.modal-content {
  background: #FFFFFF;
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.15);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx 30rpx;
  border-bottom: 1px solid #f0f0f0;
}

.modal-title {
  font-size: 34rpx;
  font-weight: 600;
  color: #333;
}

.modal-close {
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #999;
}

.modal-body {
  padding: 30rpx;
}

.modal-description {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 30rpx;
}

/* 选项列表 */
.option-list {
  margin-bottom: 20rpx;
}

.option-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx;
  margin-bottom: 20rpx;
  border-radius: 12rpx;
  background: #f8f9fa;
  border: 2rpx solid transparent;
  transition: all 0.2s ease;
}

.option-selected {
  border-color: #007aff;
  background: rgba(0, 122, 255, 0.05);
}

.option-content {
  display: flex;
  align-items: center;
}

.option-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 12rpx;
  overflow: hidden;
  margin-right: 20rpx;
}

.option-icon image {
  width: 100%;
  height: 100%;
}

.option-details {
  display: flex;
  flex-direction: column;
}

.option-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 6rpx;
}

.option-subtitle {
  font-size: 24rpx;
  color: #999;
}

.option-price {
  font-size: 30rpx;
  font-weight: 600;
  color: #ff6b6b;
}

/* 底部按钮 */
.modal-footer {
  display: flex;
  padding: 20rpx 30rpx 30rpx;
}

.modal-footer button {
  flex: 1;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  font-size: 28rpx;
  border-radius: 40rpx;
  margin: 0 10rpx;
}

.btn-cancel {
  background: #f2f2f2;
  color: #666;
}

.btn-confirm {
  background: linear-gradient(135deg, #007aff, #5856d6);
  color: #FFFFFF;
}

.btn-confirm:disabled {
  background: #cccccc;
  color: #999999;
}
</style> 