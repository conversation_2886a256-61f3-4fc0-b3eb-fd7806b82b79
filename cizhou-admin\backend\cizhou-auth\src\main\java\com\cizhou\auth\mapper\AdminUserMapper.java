package com.cizhou.auth.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cizhou.auth.entity.AdminUser;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 管理员用户Mapper
 *
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Mapper
public interface AdminUserMapper extends BaseMapper<AdminUser> {

    /**
     * 根据用户名查询用户
     */
    @Select("SELECT * FROM admin_users WHERE username = #{username} AND is_deleted = 0")
    AdminUser findByUsername(@Param("username") String username);

    /**
     * 根据用户ID查询用户角色
     */
    @Select("SELECT r.role_code FROM roles r " +
            "INNER JOIN user_roles ur ON r.id = ur.role_id " +
            "WHERE ur.user_id = #{userId} AND r.is_deleted = 0")
    List<String> findRolesByUserId(@Param("userId") Long userId);

    /**
     * 根据用户ID查询用户权限
     */
    @Select("SELECT DISTINCT p.permission_code FROM permissions p " +
            "INNER JOIN role_permissions rp ON p.id = rp.permission_id " +
            "INNER JOIN user_roles ur ON rp.role_id = ur.role_id " +
            "WHERE ur.user_id = #{userId} AND p.is_deleted = 0")
    List<String> findPermissionsByUserId(@Param("userId") Long userId);
}
