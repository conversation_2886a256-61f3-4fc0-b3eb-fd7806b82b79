<template>
  <view class="product-detail">
    <!-- 商品图片轮播 -->
    <swiper class="product-swiper" indicator-dots autoplay circular>
      <swiper-item v-for="(image, index) in product.images" :key="index">
        <image :src="image" mode="aspectFill" class="slide-image"></image>
      </swiper-item>
    </swiper>
    
    <!-- 商品信息 -->
    <view class="product-info">
      <view class="product-price">
        <text class="price">¥{{product.price}}</text>
        <text class="original-price" v-if="product.originalPrice">¥{{product.originalPrice}}</text>
      </view>
      <view class="product-title">{{product.name}}</view>
      <view class="product-sales">销量 {{product.sales}} | 好评率 {{product.goodRate}}%</view>
    </view>
    
    <!-- 促销信息 -->
    <view class="promotion-section" v-if="product.promotions && product.promotions.length > 0">
      <view class="section-title">促销</view>
      <view class="promotion-item" v-for="(promo, index) in product.promotions" :key="index">
        <view class="promo-tag">{{promo.type}}</view>
        <view class="promo-text">{{promo.desc}}</view>
      </view>
    </view>
    
    <!-- 积分信息 -->
    <view class="points-section">
      <view class="section-title">积分</view>
      <view class="points-info">
        <text>购买可得 <text class="points-value">{{product.points}}</text> 积分</text>
        <text>分享可得 <text class="points-value">5</text> 积分</text>
      </view>
    </view>
    
    <!-- 商品详情 -->
    <view class="product-detail-section">
      <view class="detail-tabs">
        <view class="tab" :class="{active: activeTab === 'detail'}" @tap="switchTab('detail')">商品详情</view>
        <view class="tab" :class="{active: activeTab === 'params'}" @tap="switchTab('params')">规格参数</view>
        <view class="tab" :class="{active: activeTab === 'comments'}" @tap="switchTab('comments')">用户评价</view>
      </view>
      
      <view class="tab-content">
        <!-- 商品详情内容 -->
        <view v-if="activeTab === 'detail'" class="detail-content">
          <rich-text :nodes="product.detailHtml"></rich-text>
        </view>
        
        <!-- 规格参数内容 -->
        <view v-if="activeTab === 'params'" class="params-content">
          <view class="param-item" v-for="(param, index) in product.params" :key="index">
            <text class="param-name">{{param.name}}</text>
            <text class="param-value">{{param.value}}</text>
          </view>
        </view>
        
        <!-- 用户评价内容 -->
        <view v-if="activeTab === 'comments'" class="comments-content">
          <view class="comment-item" v-for="(comment, index) in product.comments" :key="index">
            <view class="comment-header">
              <image class="user-avatar" :src="comment.userAvatar"></image>
              <view class="comment-user">
                <text class="user-name">{{comment.userName}}</text>
                <view class="rating">
                  <text class="star" v-for="i in 5" :key="i">★</text>
                </view>
              </view>
              <text class="comment-time">{{comment.time}}</text>
            </view>
            <view class="comment-text">{{comment.content}}</view>
            <view class="comment-images" v-if="comment.images && comment.images.length > 0">
              <image v-for="(img, imgIndex) in comment.images" :key="imgIndex" :src="img" mode="aspectFill" class="comment-image"></image>
            </view>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 底部操作栏 -->
    <view class="bottom-bar">
      <view class="action-buttons">
        <view class="action-button" @tap="goToHome">
          <view class="button-icon home"></view>
          <text>首页</text>
        </view>
        <view class="action-button" @tap="toggleFavorite">
          <view class="button-icon" :class="isFavorite ? 'favorite-active' : 'favorite'"></view>
          <text>收藏</text>
        </view>
        <view class="action-button" @tap="contactMerchant">
          <view class="button-icon contact"></view>
          <text>客服</text>
        </view>
        <view class="action-button" @tap="shareProduct">
          <view class="button-icon share"></view>
          <text>分享</text>
        </view>
      </view>
      <view class="buy-buttons">
        <view class="add-cart-button" @tap="addToCart">加入购物车</view>
        <view class="buy-now-button" @tap="buyNow">立即购买</view>
      </view>
    </view>
    
    <!-- 分享成功提示 -->
    <view class="share-toast" v-if="showShareToast">
      <view class="toast-content">
        <view class="toast-icon success"></view>
        <view class="toast-message">
          <text class="toast-title">分享成功</text>
          <text class="toast-desc">获得5积分奖励</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import { openSharePanel, ShareType } from '../../utils/shareService';

// 商品ID
const productId = ref('');

// 商品数据
const product = reactive({
  id: '123456',
  name: '高品质保温杯 304不锈钢真空保温水杯',
  price: '69.00',
  originalPrice: '99.00',
  sales: 1580,
  goodRate: 98,
  points: 69, // 购买可得积分
  images: [
    '/static/images/product1.jpg',
    '/static/images/product2.jpg',
    '/static/images/product3.jpg',
  ],
  promotions: [
    { type: '满减', desc: '满100减10' },
    { type: '优惠', desc: '新用户立减5元' }
  ],
  detailHtml: '<div style="text-align:center;"><img src="/static/images/product-detail1.jpg" style="width:100%"><img src="/static/images/product-detail2.jpg" style="width:100%"></div>',
  params: [
    { name: '品牌', value: 'Example Brand' },
    { name: '材质', value: '304不锈钢' },
    { name: '容量', value: '500ml' },
    { name: '颜色', value: '银色/黑色/蓝色' },
    { name: '保温效果', value: '12小时' },
  ],
  comments: [
    {
      userName: '用户123',
      userAvatar: '/static/images/avatar1.jpg',
      rating: 5,
      time: '2023-12-10',
      content: '非常好用的保温杯，保温效果很棒，12小时后水还是热的。',
      images: ['/static/images/comment1.jpg', '/static/images/comment2.jpg']
    },
    {
      userName: '用户456',
      userAvatar: '/static/images/avatar2.jpg',
      rating: 4,
      time: '2023-12-05',
      content: '质量不错，就是有点重。',
      images: []
    }
  ]
});

// 页面状态
const activeTab = ref('detail');
const isFavorite = ref(false);
const showShareToast = ref(false);

// 切换标签
const switchTab = (tab) => {
  activeTab.value = tab;
};

// 收藏商品
const toggleFavorite = () => {
  isFavorite.value = !isFavorite.value;
  if (isFavorite.value) {
    uni.showToast({
      title: '收藏成功',
      icon: 'success'
    });
  } else {
    uni.showToast({
      title: '已取消收藏',
      icon: 'none'
    });
  }
};

// 联系商家
const contactMerchant = () => {
  uni.showToast({
    title: '正在连接客服...',
    icon: 'none'
  });
};

// 分享商品
const shareProduct = async () => {
  try {
    // 调用分享服务打开分享面板
    const result = await openSharePanel(product, ShareType.PRODUCT);
    
    if (result && result.success && result.points > 0) {
      // 显示分享成功提示
      showShareToast.value = true;
      setTimeout(() => {
        showShareToast.value = false;
      }, 3000);
    }
  } catch (error) {
    console.error('分享失败:', error);
  }
};

// 添加到购物车
const addToCart = () => {
  uni.showToast({
    title: '已加入购物车',
    icon: 'success'
  });
};

// 立即购买
const buyNow = () => {
  uni.navigateTo({
    url: `/pages/order/confirm?productId=${product.id}`
  });
};

// 前往首页
const goToHome = () => {
  uni.switchTab({
    url: '/pages/index/index'
  });
};

// 获取商品详情
const getProductDetail = async (id) => {
  // 实际应用中应该调用API获取商品详情
  console.log('获取商品详情, ID:', id);
  // 这里使用mock数据，实际应用中应该替换为API调用
};

// 页面加载
onMounted(() => {
  // 获取页面参数
  const pages = getCurrentPages();
  const currentPage = pages[pages.length - 1];
  const options = currentPage.$page?.options || {};
  
  if (options.id) {
    productId.value = options.id;
    getProductDetail(options.id);
  }
});
</script>

<style lang="scss">
.product-detail {
  background-color: #f5f5f5;
  min-height: 100vh;
  padding-bottom: 60px;
}

/* 商品轮播图 */
.product-swiper {
  width: 100%;
  height: 750rpx;
}

.slide-image {
  width: 100%;
  height: 100%;
}

/* 商品信息 */
.product-info {
  background-color: #fff;
  padding: 20rpx;
  margin-bottom: 20rpx;
}

.product-price {
  display: flex;
  align-items: baseline;
  margin-bottom: 10rpx;
}

.price {
  color: #ff3b30;
  font-size: 40rpx;
  font-weight: bold;
  margin-right: 10rpx;
}

.original-price {
  color: #999;
  font-size: 28rpx;
  text-decoration: line-through;
}

.product-title {
  font-size: 32rpx;
  font-weight: bold;
  line-height: 1.4;
  margin-bottom: 10rpx;
}

.product-sales {
  font-size: 24rpx;
  color: #999;
}

/* 促销信息 */
.promotion-section {
  background-color: #fff;
  padding: 20rpx;
  margin-bottom: 20rpx;
}

.section-title {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 10rpx;
}

.promotion-item {
  display: flex;
  align-items: center;
  margin-bottom: 10rpx;
}

.promo-tag {
  background-color: #ff3b30;
  color: #fff;
  font-size: 24rpx;
  padding: 4rpx 10rpx;
  border-radius: 4rpx;
  margin-right: 10rpx;
}

.promo-text {
  font-size: 28rpx;
  color: #333;
}

/* 积分信息 */
.points-section {
  background-color: #fff;
  padding: 20rpx;
  margin-bottom: 20rpx;
}

.points-info {
  font-size: 28rpx;
  color: #666;
  display: flex;
  flex-direction: column;
}

.points-value {
  color: #ff7600;
  font-weight: bold;
}

/* 商品详情 */
.product-detail-section {
  background-color: #fff;
  margin-bottom: 20rpx;
}

.detail-tabs {
  display: flex;
  border-bottom: 1rpx solid #eee;
}

.tab {
  flex: 1;
  text-align: center;
  padding: 20rpx 0;
  font-size: 28rpx;
  color: #666;
  position: relative;
}

.tab.active {
  color: #ff7600;
}

.tab.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 60rpx;
  height: 4rpx;
  background-color: #ff7600;
}

.tab-content {
  padding: 20rpx;
}

/* 规格参数 */
.param-item {
  display: flex;
  padding: 10rpx 0;
  font-size: 28rpx;
  border-bottom: 1rpx solid #f5f5f5;
}

.param-name {
  width: 200rpx;
  color: #999;
}

.param-value {
  flex: 1;
  color: #333;
}

/* 用户评价 */
.comment-item {
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f5f5f5;
}

.comment-header {
  display: flex;
  align-items: center;
  margin-bottom: 10rpx;
}

.user-avatar {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  margin-right: 10rpx;
}

.comment-user {
  flex: 1;
}

.user-name {
  font-size: 28rpx;
  color: #333;
}

.rating {
  font-size: 24rpx;
  color: #ff7600;
}

.comment-time {
  font-size: 24rpx;
  color: #999;
}

.comment-text {
  font-size: 28rpx;
  color: #333;
  line-height: 1.5;
  margin-bottom: 10rpx;
}

.comment-images {
  display: flex;
  flex-wrap: wrap;
}

.comment-image {
  width: 150rpx;
  height: 150rpx;
  margin-right: 10rpx;
  margin-bottom: 10rpx;
}

/* 底部操作栏 */
.bottom-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 100rpx;
  background-color: #fff;
  display: flex;
  align-items: center;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  z-index: 100;
}

.action-buttons {
  display: flex;
  flex: 3;
}

.action-button {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  font-size: 20rpx;
  color: #666;
}

.button-icon {
  width: 40rpx;
  height: 40rpx;
  margin-bottom: 6rpx;
  background-color: #ccc; /* 实际应用中应该使用图标 */
}

.button-icon.home {
  background-color: #333;
}

.button-icon.favorite {
  background-color: #999;
}

.button-icon.favorite-active {
  background-color: #ff3b30;
}

.button-icon.contact {
  background-color: #007aff;
}

.button-icon.share {
  background-color: #ff7600;
}

.buy-buttons {
  flex: 4;
  display: flex;
  height: 100%;
}

.add-cart-button, .buy-now-button {
  flex: 1;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  color: #fff;
}

.add-cart-button {
  background-color: #ff9500;
}

.buy-now-button {
  background-color: #ff3b30;
}

/* 分享成功提示 */
.share-toast {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background-color: rgba(0, 0, 0, 0.7);
  border-radius: 12rpx;
  padding: 30rpx;
  z-index: 1000;
}

.toast-content {
  display: flex;
  align-items: center;
}

.toast-icon {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  margin-right: 20rpx;
}

.toast-icon.success {
  background-color: #34c759;
}

.toast-message {
  display: flex;
  flex-direction: column;
}

.toast-title {
  font-size: 32rpx;
  color: #fff;
  margin-bottom: 6rpx;
}

.toast-desc {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
}
</style> 