"use strict";
const common_vendor = require("../../../../common/vendor.js");
const common_assets = require("../../../../common/assets.js");
const _sfc_main = {
  __name: "expired-list",
  setup(__props) {
    const expiredList = common_vendor.ref([]);
    const page = common_vendor.ref(1);
    common_vendor.ref(10);
    const hasMore = common_vendor.ref(true);
    const isLoading = common_vendor.ref(false);
    const isRefreshing = common_vendor.ref(false);
    common_vendor.onMounted(() => {
      loadData();
    });
    const loadData = () => {
      if (isLoading.value)
        return;
      isLoading.value = true;
      setTimeout(() => {
        const mockData = [
          {
            id: "3001",
            type: "长途拼车",
            publishTime: "2023-10-10 16:30",
            expiredTime: "2023-10-12 16:30",
            startPoint: "磁县政府",
            endPoint: "石家庄火车站",
            departureTime: "2023-10-12 10:30",
            seatCount: 3,
            price: 50
          },
          {
            id: "3002",
            type: "上下班拼车",
            publishTime: "2023-10-05 14:15",
            expiredTime: "2023-10-09 14:15",
            startPoint: "磁县老城区",
            endPoint: "邯郸科技学院",
            departureTime: "2023-10-09 07:30",
            seatCount: 4,
            price: 12
          },
          {
            id: "3003",
            type: "短途拼车",
            publishTime: "2023-09-30 11:40",
            expiredTime: "2023-10-02 11:40",
            startPoint: "磁县体育场",
            endPoint: "磁县汽车站",
            departureTime: "2023-10-01 16:00",
            seatCount: 2,
            price: 5
          }
        ];
        if (page.value === 1) {
          expiredList.value = mockData;
        } else {
          expiredList.value = [...expiredList.value, ...mockData];
        }
        if (page.value >= 2) {
          hasMore.value = false;
        }
        isLoading.value = false;
        isRefreshing.value = false;
      }, 1e3);
    };
    const loadMore = () => {
      if (!hasMore.value || isLoading.value)
        return;
      page.value++;
      loadData();
    };
    const onRefresh = () => {
      isRefreshing.value = true;
      page.value = 1;
      hasMore.value = true;
      loadData();
    };
    const goBack = () => {
      common_vendor.index.navigateBack();
    };
    const deleteItem = (item) => {
      common_vendor.index.showModal({
        title: "提示",
        content: "确定要删除此条信息吗？",
        success: (res) => {
          if (res.confirm) {
            expiredList.value = expiredList.value.filter((i) => i.id !== item.id);
            common_vendor.index.showToast({
              title: "删除成功",
              icon: "success"
            });
          }
        }
      });
    };
    const republishItem = (item) => {
      common_vendor.index.navigateTo({
        url: `/carpool-package/pages/carpool/publish/index?id=${item.id}&type=republish`
      });
    };
    const showClearConfirm = () => {
      if (expiredList.value.length === 0) {
        common_vendor.index.showToast({
          title: "暂无数据可清空",
          icon: "none"
        });
        return;
      }
      common_vendor.index.showModal({
        title: "提示",
        content: "确定要清空所有过期信息吗？此操作不可恢复。",
        success: (res) => {
          if (res.confirm) {
            clearAll();
          }
        }
      });
    };
    const clearAll = () => {
      expiredList.value = [];
      common_vendor.index.showToast({
        title: "已清空",
        icon: "success"
      });
    };
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_assets._imports_0$28,
        b: common_vendor.o(goBack),
        c: common_vendor.o(showClearConfirm),
        d: common_vendor.f(expiredList.value, (item, index, i0) => {
          return common_vendor.e({
            a: common_vendor.t(item.type),
            b: common_vendor.t(item.publishTime),
            c: common_vendor.t(item.startPoint),
            d: common_vendor.t(item.endPoint),
            e: common_vendor.t(item.departureTime),
            f: common_vendor.t(item.seatCount),
            g: item.price
          }, item.price ? {
            h: common_assets._imports_3$27,
            i: common_vendor.t(item.price)
          } : {}, {
            j: common_vendor.t(item.expiredTime),
            k: common_vendor.o(($event) => deleteItem(item), item.id),
            l: common_vendor.o(($event) => republishItem(item), item.id),
            m: item.id
          });
        }),
        e: common_assets._imports_1$33,
        f: common_assets._imports_2$29,
        g: common_assets._imports_4$22,
        h: expiredList.value.length === 0 && !isLoading.value
      }, expiredList.value.length === 0 && !isLoading.value ? {
        i: common_assets._imports_5$20
      } : {}, {
        j: isLoading.value && !isRefreshing.value
      }, isLoading.value && !isRefreshing.value ? {} : {}, {
        k: expiredList.value.length > 0 && !hasMore.value
      }, expiredList.value.length > 0 && !hasMore.value ? {} : {}, {
        l: common_vendor.o(loadMore),
        m: common_vendor.o(onRefresh),
        n: isRefreshing.value
      });
    };
  }
};
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-3545965b"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../../.sourcemap/mp-weixin/carpool-package/pages/carpool/my/expired-list.js.map
