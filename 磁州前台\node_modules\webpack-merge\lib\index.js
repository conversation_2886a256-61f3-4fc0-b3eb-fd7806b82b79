'use strict';

var _values2 = require('lodash/values');

var _values3 = _interopRequireDefault(_values2);

var _unionWith2 = require('lodash/unionWith');

var _unionWith3 = _interopRequireDefault(_unionWith2);

var _mergeWith2 = require('lodash/mergeWith');

var _mergeWith3 = _interopRequireDefault(_mergeWith2);

var _differenceWith2 = require('lodash/differenceWith');

var _differenceWith3 = _interopRequireDefault(_differenceWith2);

var _joinArrays = require('./join-arrays');

var _joinArrays2 = _interopRequireDefault(_joinArrays);

var _joinArraysSmart = require('./join-arrays-smart');

var _unique = require('./unique');

var _unique2 = _interopRequireDefault(_unique);

function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }

function _toConsumableArray(arr) { if (Array.isArray(arr)) { for (var i = 0, arr2 = Array(arr.length); i < arr.length; i++) { arr2[i] = arr[i]; } return arr2; } else { return Array.from(arr); } }

function merge() {
  for (var _len = arguments.length, sources = Array(_len), _key = 0; _key < _len; _key++) {
    sources[_key] = arguments[_key];
  }

  // This supports
  // merge([<object>] | ...<object>)
  // merge({ customizeArray: <fn>, customizeObject: <fn>})([<object>] | ...<object>)
  // where fn = (a, b, key)
  if (sources.length === 1) {
    if (Array.isArray(sources[0])) {
      return _mergeWith3.default.apply(undefined, [{}].concat(_toConsumableArray(sources[0]), [(0, _joinArrays2.default)(sources[0])]));
    }

    if (sources[0].customizeArray || sources[0].customizeObject) {
      return function () {
        for (var _len2 = arguments.length, structures = Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {
          structures[_key2] = arguments[_key2];
        }

        if (Array.isArray(structures[0])) {
          return _mergeWith3.default.apply(undefined, [{}].concat(_toConsumableArray(structures[0]), [(0, _joinArrays2.default)(sources[0])]));
        }

        return _mergeWith3.default.apply(undefined, [{}].concat(structures, [(0, _joinArrays2.default)(sources[0])]));
      };
    }

    return sources[0];
  }

  return _mergeWith3.default.apply(undefined, [{}].concat(sources, [(0, _joinArrays2.default)()]));
}

var mergeSmart = merge({
  customizeArray: function customizeArray(a, b, key) {
    if (isRule(key.split('.').slice(-1)[0])) {
      return (0, _unionWith3.default)(a, b, _joinArraysSmart.uniteRules.bind(null, {}, key));
    }

    return null;
  }
});

var mergeMultiple = function mergeMultiple() {
  for (var _len3 = arguments.length, sources = Array(_len3), _key3 = 0; _key3 < _len3; _key3++) {
    sources[_key3] = arguments[_key3];
  }

  return (0, _values3.default)(merge(sources));
};

// rules: { <field>: <'append'|'prepend'|'replace'> }
// All default to append but you can override here
var mergeStrategy = function mergeStrategy() {
  var rules = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};
  return merge({
    customizeArray: _customizeArray(rules),
    customizeObject: customizeObject(rules)
  });
};
var mergeSmartStrategy = function mergeSmartStrategy() {
  var rules = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};
  return merge({
    customizeArray: function customizeArray(a, b, key) {
      var topKey = key.split('.').slice(-1)[0];

      if (isRule(topKey)) {
        switch (rules[key]) {
          case 'prepend':
            return [].concat(_toConsumableArray((0, _differenceWith3.default)(b, a, function (newRule, seenRule) {
              return (0, _joinArraysSmart.uniteRules)(rules, key, newRule, seenRule, 'prepend');
            })), _toConsumableArray(a));
          case 'replace':
            return b;
          default:
            // append
            return (0, _unionWith3.default)(a, b, _joinArraysSmart.uniteRules.bind(null, rules, key));
        }
      }

      return _customizeArray(rules)(a, b, key);
    },
    customizeObject: customizeObject(rules)
  });
};

function _customizeArray(rules) {
  return function (a, b, key) {
    switch (rules[key]) {
      case 'prepend':
        return [].concat(_toConsumableArray(b), _toConsumableArray(a));
      case 'replace':
        return b;
      default:
        // append
        return false;
    }
  };
}

function customizeObject(rules) {
  return function (a, b, key) {
    switch (rules[key]) {
      case 'prepend':
        return (0, _mergeWith3.default)({}, b, a, (0, _joinArrays2.default)());
      case 'replace':
        return b;
      default:
        // append
        return false;
    }
  };
}

function isRule(key) {
  return ['preLoaders', 'loaders', 'postLoaders', 'rules'].indexOf(key) >= 0;
}

module.exports = merge;
module.exports.multiple = mergeMultiple;
module.exports.smart = mergeSmart;
module.exports.strategy = mergeStrategy;
module.exports.smartStrategy = mergeSmartStrategy;
module.exports.unique = _unique2.default;