/**
 * 基础推广能力混入
 * 为所有页面提供基本的推广功能
 */
export default {
  data() {
    return {
      // 是否有推广权限
      hasPromotionPermission: false,
      // 是否可以获取佣金
      canEarnCommission: false,
      // 页面类型，用于确定推广内容类型
      pageType: '',
      // 推广数据
      promotionData: {},
      // 是否显示悬浮推广按钮
      showFloatPromotionButton: false
    };
  },

  created() {
    // 检查页面是否支持推广
    this.initPromotion();
  },
  
  mounted() {
    // 在mounted阶段再次检查，确保DOM已经加载完成
    this.checkAndEnablePromotion();
  },

  methods: {
    /**
     * 初始化推广能力
     */
    async initPromotion() {
      // 确定页面类型
      this.detectPageType();

      if (!this.pageType) {
        return;
      }

      // 检查权限
      await this.checkPromotionPermission();

      // 生成推广数据
      this.generatePromotionData();
    },
    
    /**
     * 检查并启用推广功能
     * 自动检测当前页面内容，并决定是否显示推广按钮
     */
    checkAndEnablePromotion() {
      try {
        // 获取当前页面路径
        const pages = getCurrentPages();
        const currentPage = pages[pages.length - 1];
        const route = currentPage?.route || '';
        
        // 检测是否是详情页
        if (route.includes('detail')) {
          // 获取页面数据
          const pageData = currentPage.data || {};
          
          // 尝试从页面数据中获取内容ID和类型
          let contentId = '';
          let contentType = '';
          
          // 检测商品详情
          if (pageData.product || pageData.productInfo || pageData.goodsInfo) {
            const product = pageData.product || pageData.productInfo || pageData.goodsInfo;
            contentId = product.id || product._id;
            contentType = 'product';
          } 
          // 检测拼车详情
          else if (pageData.carpool || pageData.carpoolInfo) {
            const carpool = pageData.carpool || pageData.carpoolInfo;
            contentId = carpool.id || carpool._id;
            contentType = 'carpool';
          }
          // 检测商家详情
          else if (pageData.shop || pageData.shopData || pageData.merchant) {
            const shop = pageData.shop || pageData.shopData || pageData.merchant;
            contentId = shop.id || shop._id;
            contentType = 'merchant';
          }
          // 检测房产详情
          else if (pageData.house || pageData.houseInfo) {
            const house = pageData.house || pageData.houseInfo;
            contentId = house.id || house._id;
            contentType = 'house';
          }
          // 检测服务详情
          else if (pageData.service || pageData.serviceInfo) {
            const service = pageData.service || pageData.serviceInfo;
            contentId = service.id || service._id;
            contentType = 'service';
          }
          // 检测内容详情
          else if (pageData.article || pageData.content || pageData.news) {
            const content = pageData.article || pageData.content || pageData.news;
            contentId = content.id || content._id;
            contentType = 'content';
          }
          
          // 如果找到了内容ID和类型，自动添加推广按钮
          if (contentId && contentType) {
            this.pageType = contentType;
            this.promotionData = { id: contentId };
            
            // 设置推广权限
            this.hasPromotionPermission = true;
            
            // 添加悬浮推广按钮
            this.addFloatPromotionButton();
          }
        }
      } catch (error) {
        console.error('自动检测推广功能失败', error);
      }
    },
    
    /**
     * 添加悬浮推广按钮
     */
    addFloatPromotionButton() {
      // 设置显示悬浮按钮标志
      this.showFloatPromotionButton = true;
      
      // 如果是Vue3环境，可以使用动态组件
      if (this.$nextTick) {
        this.$nextTick(() => {
          // 确保在DOM更新后执行
          console.log('添加悬浮推广按钮成功');
        });
      }
    },

    /**
     * 检测页面类型
     */
    detectPageType() {
      // 获取当前页面路径
      const pages = getCurrentPages();
      const currentPage = pages[pages.length - 1];
      const route = currentPage?.route || '';

      // 根据路径判断页面类型
      if (route.includes('carpool')) {
        this.pageType = 'carpool';
      } else if (route.includes('secondhand')) {
        this.pageType = 'secondhand';
      } else if (route.includes('house')) {
        this.pageType = 'house';
      } else if (route.includes('service')) {
        this.pageType = 'service';
      } else if (route.includes('community')) {
        this.pageType = 'community';
      } else if (route.includes('product') || route.includes('goods')) {
        this.pageType = 'product';
      } else if (route.includes('article') || route.includes('news')) {
        this.pageType = 'content';
      } else if (route.includes('activity')) {
        this.pageType = 'activity';
      }

      // 也可以从页面meta中获取
      this.pageType = this.pageType || this.$route?.meta?.pageType || '';
    },

    /**
     * 检查用户推广权限
     */
    async checkPromotionPermission() {
      try {
        // 这里可以调用API获取权限
        // 暂时简化处理，默认有基础权限
        this.hasPromotionPermission = this.isContentOwner();

        // 检查是否可以获取佣金
        this.canEarnCommission = this.isDistributor() && this.isCommissionContent();
      } catch (e) {
        console.error('获取推广权限失败', e);
        this.hasPromotionPermission = false;
        this.canEarnCommission = false;
      }
    },

    /**
     * 判断当前用户是否是内容所有者
     */
    isContentOwner() {
      // 这里需要子类根据具体业务实现
      // 基础实现默认返回true，方便测试
      return true;
    },

    /**
     * 判断当前用户是否是分销员
     */
    isDistributor() {
      // 这里可以检查用户角色
      // 暂时返回true用于演示
      return true;
    },

    /**
     * 判断当前内容是否支持佣金
     */
    isCommissionContent() {
      // 子类需要重写此方法，判断该内容是否支持分佣
      // 默认支持
      return true;
    },

    /**
     * 生成推广数据
     */
    generatePromotionData() {
      // 子类需要重写此方法，提供具体的推广数据
      this.promotionData = {};
    },

    /**
     * 打开推广工具
     */
    openPromotionTools() {
      if (!this.hasPromotionPermission) {
        uni.showToast({
          title: '暂无推广权限',
          icon: 'none'
        });
        return;
      }

      // 导入推广服务
      const promotionService = require('@/utils/promotionService').default;
      promotionService.showPromotionTools(this.pageType, this.promotionData);
    },
    
    /**
     * 通用推广方法，可以在任何页面调用
     */
    showPromotion() {
      if (this.pageType && this.promotionData.id) {
        this.openPromotionTools();
      } else {
        uni.showToast({
          title: '当前内容不支持推广',
          icon: 'none'
        });
      }
    }
  }
}; 