<view class="my-red-packets"><view class="tabs"><view wx:for="{{a}}" wx:for-item="tab" wx:key="b" class="{{['tab-item', tab.c && 'active']}}" bindtap="{{tab.d}}">{{tab.a}}</view></view><scroll-view class="red-packet-list" scroll-y bindscrolltolower="{{g}}" refresher-enabled="{{true}}" bindrefresherrefresh="{{h}}"><view wx:for="{{b}}" wx:for-item="item" wx:key="h" class="red-packet-item" bindtap="{{item.i}}"><view class="red-packet-info"><view class="title">{{item.a}}</view><view class="time">{{item.b}}</view><view wx:if="{{item.c}}" class="amount"> 获得 {{item.d}}元 </view><view wx:else class="amount"> 发出 {{item.e}}元 </view></view><view class="{{['status', item.g]}}">{{item.f}}</view></view><view wx:if="{{c}}" class="loading">加载中...</view><view wx:if="{{d}}" class="no-more">没有更多了</view><view wx:if="{{e}}" class="empty"><image src="{{f}}" mode="aspectFit"></image><text>暂无红包记录</text></view></scroll-view></view>