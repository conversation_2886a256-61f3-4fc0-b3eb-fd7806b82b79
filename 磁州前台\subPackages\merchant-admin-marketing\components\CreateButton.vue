<template>
  <view class="create-button" :class="themeClass" @tap="handleTap">
    <view class="button-content">
      <text class="plus-icon">+</text>
      <text class="button-text">{{ text }}</text>
    </view>
  </view>
</template>

<script>
export default {
  name: 'CreateButton',
  props: {
    text: {
      type: String,
      default: '创建'
    },
    url: {
      type: String,
      default: ''
    },
    theme: {
      type: String,
      default: 'default' // 可以是 'coupon', 'discount', 'group', 'flash' 或默认
    }
  },
  computed: {
    themeClass() {
      return `theme-${this.theme}`;
    }
  },
  methods: {
    handleTap() {
      if (this.url) {
        uni.navigateTo({
          url: this.url
        });
      } else {
        this.$emit('click');
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.create-button {
  height: 40px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: 20px;
  padding: 0 20px;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.15);
  cursor: pointer;
  transition: all 0.3s ease;
}

/* 默认主题 - 紫色渐变 */
.theme-default {
  background: linear-gradient(135deg, #9254de, #7a36d6);
  box-shadow: 0 4px 10px rgba(122, 54, 214, 0.2);
}

/* 优惠券主题 - 红橙色渐变 */
.theme-coupon {
  background: linear-gradient(135deg, #FF9966, #FF5E62);
  box-shadow: 0 4px 10px rgba(255, 94, 98, 0.2);
}

/* 满减活动主题 - 黄色渐变 */
.theme-discount {
  background: linear-gradient(135deg, #FDEB71, #F8D800);
  box-shadow: 0 4px 10px rgba(248, 216, 0, 0.2);
}

/* 拼团活动主题 - 紫色渐变 */
.theme-group {
  background: linear-gradient(135deg, #9040FF, #5E35B1);
  box-shadow: 0 4px 10px rgba(144, 64, 255, 0.2);
}

/* 秒杀活动主题 - 橙红色渐变 */
.theme-flash {
  background: linear-gradient(135deg, #FF7600, #FF3C00);
  box-shadow: 0 4px 10px rgba(255, 118, 0, 0.2);
}

/* 积分商城主题 - 橙色渐变 */
.theme-points {
  background: linear-gradient(135deg, #FF7600, #FF3C00);
  box-shadow: 0 4px 10px rgba(255, 118, 0, 0.2);
}

.create-button:active {
  transform: scale(0.98);
  opacity: 0.9;
}

.button-content {
  display: flex;
  align-items: center;
  justify-content: center;
}

.plus-icon {
  font-size: 18px;
  font-weight: bold;
  color: #fff;
  margin-right: 4px;
}

.button-text {
  font-size: 14px;
  font-weight: 500;
  color: #fff;
}
</style> 