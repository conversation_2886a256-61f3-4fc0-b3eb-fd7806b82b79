/**
 * 高级功能配置
 * 管理价格、广告时长等参数
 */

// 发布功能配置
export const publishConfig = {
  // 广告发布
  ad: {
    duration: 15, // 广告时长(秒)
    cooldown: 3600, // 冷却时间(秒)
    dailyLimit: 5 // 每日限制次数
  },
  // 付费发布
  paid: {
    price: 2.00, // 价格(元)
    vipDiscount: 0.8 // VIP折扣
  }
};

// 置顶功能配置
export const topConfig = {
  // 广告置顶
  ad: {
    duration: 30, // 广告时长(秒)
    effectDuration: '2小时', // 效果持续时间
    effectDurationSeconds: 7200, // 效果持续时间(秒)
    cooldown: 7200, // 冷却时间(秒)
    dailyLimit: 3 // 每日限制次数
  },
  // 付费置顶
  paid: [
    {
      id: 'top_1h',
      name: '置顶1小时',
      price: 2.00, // 价格(元)
      duration: '1小时',
      durationSeconds: 3600, // 持续时间(秒)
      vipDiscount: 0.9 // VIP折扣
    },
    {
      id: 'top_3h',
      name: '置顶3小时',
      price: 5.00, // 价格(元)
      duration: '3小时',
      durationSeconds: 10800, // 持续时间(秒)
      vipDiscount: 0.9 // VIP折扣
    },
    {
      id: 'top_7h',
      name: '置顶7小时',
      price: 10.00, // 价格(元)
      duration: '7小时',
      durationSeconds: 25200, // 持续时间(秒)
      vipDiscount: 0.85 // VIP折扣
    }
  ]
};

// 刷新功能配置
export const refreshConfig = {
  // 广告刷新
  ad: {
    duration: 15, // 广告时长(秒)
    cooldown: 3600, // 冷却时间(秒)
    dailyLimit: 5 // 每日限制次数
  },
  // 付费刷新
  paid: {
    price: 1.00, // 价格(元)
    vipDiscount: 0.8 // VIP折扣
  }
};

// VIP等级配置
export const vipConfig = {
  // VIP等级特权
  levels: [
    {
      level: 1,
      name: 'VIP 1',
      extraAdCount: 2, // 额外广告次数
      discount: 0.9 // 付费折扣
    },
    {
      level: 2,
      name: 'VIP 2',
      extraAdCount: 5, // 额外广告次数
      discount: 0.8 // 付费折扣
    },
    {
      level: 3,
      name: 'VIP 3',
      extraAdCount: 10, // 额外广告次数
      discount: 0.7 // 付费折扣
    }
  ]
};

// 图标配置
export const iconConfig = {
  publish: '/static/images/premium/publish.png',
  top: '/static/images/premium/top.png',
  refresh: '/static/images/premium/refresh.png',
  adPublish: '/static/images/premium/ad-publish.png',
  paidPublish: '/static/images/premium/paid-publish.png',
  adTop: '/static/images/premium/ad-top.png',
  paidTop: '/static/images/premium/paid-top.png',
  adRefresh: '/static/images/premium/ad-refresh.png',
  paidRefresh: '/static/images/premium/paid-refresh.png'
};

// 获取当前用户适用的价格
export const getUserPrice = (basePrice, userVipLevel) => {
  // 获取VIP折扣
  let discount = 1;
  for (const vipLevel of vipConfig.levels) {
    if (vipLevel.level === userVipLevel) {
      discount = vipLevel.discount;
      break;
    }
  }
  
  // 应用折扣
  return (basePrice * discount).toFixed(2);
};

// 获取置顶选项
export const getTopOptions = (userVipLevel) => {
  // 广告置顶选项
  const adOption = {
    title: '广告置顶',
    subtitle: `观看${topConfig.ad.duration}秒广告获得${topConfig.ad.effectDuration}置顶`,
    price: '免费',
    icon: iconConfig.adTop,
    type: 'ad',
    duration: topConfig.ad.effectDuration,
    adDuration: topConfig.ad.duration
  };
  
  // 付费置顶选项
  const paidOptions = topConfig.paid.map(option => {
    const price = getUserPrice(option.price, userVipLevel);
    return {
      title: `付费置顶${option.duration}`,
      subtitle: `信息将在首页置顶显示${option.duration}`,
      price: `¥${price}`,
      originalPrice: option.price !== parseFloat(price) ? `¥${option.price.toFixed(2)}` : null,
      icon: iconConfig.paidTop,
      type: 'paid',
      duration: option.duration,
      durationSeconds: option.durationSeconds,
      id: option.id
    };
  });
  
  return [adOption, ...paidOptions];
}; 