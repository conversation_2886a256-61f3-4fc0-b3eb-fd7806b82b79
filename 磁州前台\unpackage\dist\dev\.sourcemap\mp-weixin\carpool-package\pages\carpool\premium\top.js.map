{"version": 3, "file": "top.js", "sources": ["carpool-package/pages/carpool/premium/top.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/Y2FycG9vbC1wYWNrYWdlXHBhZ2VzXGNhcnBvb2xccHJlbWl1bVx0b3AudnVl"], "sourcesContent": ["<template>\r\n  <view class=\"top-container\">\r\n    <!-- 导航栏 -->\r\n    <carpool-nav title=\"置顶信息\"></carpool-nav>\r\n    \r\n    <!-- 页面主体 -->\r\n    <view class=\"top-content\">\r\n      <!-- 信息卡片 -->\r\n      <view class=\"info-card\">\r\n        <view class=\"card-header\">\r\n          <text class=\"card-title\">您的拼车信息</text>\r\n          <view class=\"info-tag\" :class=\"typeClass\">{{typeText}}</view>\r\n        </view>\r\n        \r\n        <view class=\"route-section\">\r\n          <view class=\"route-points\">\r\n            <view class=\"route-point\">\r\n              <view class=\"point-dot start\"></view>\r\n              <text class=\"point-text\">{{infoData.startPoint}}</text>\r\n            </view>\r\n            <view class=\"route-divider\">\r\n              <view class=\"divider-line\"></view>\r\n              <view class=\"divider-arrow\">\r\n                <image src=\"/static/images/tabbar/arrow-right.png\" mode=\"aspectFit\" class=\"arrow-icon\"></image>\r\n              </view>\r\n            </view>\r\n            <view class=\"route-point\">\r\n              <view class=\"point-dot end\"></view>\r\n              <text class=\"point-text\">{{infoData.endPoint}}</text>\r\n            </view>\r\n          </view>\r\n        </view>\r\n      </view>\r\n      \r\n      <!-- 拼车置顶推广操作 -->\r\n      <view class=\"premium-card\">\r\n        <view class=\"premium-header\">\r\n          <image src=\"/static/images/tabbar/crown.png\" mode=\"aspectFit\" class=\"premium-icon\"></image>\r\n          <text class=\"premium-title\">置顶服务</text>\r\n        </view>\r\n\r\n        <view class=\"premium-desc\">\r\n          <text>置顶后您的信息将会展示在列表顶部，获得更多曝光</text>\r\n        </view>\r\n\r\n        <ConfigurablePremiumActions\r\n          pageType=\"carpool_top\"\r\n          showMode=\"direct\"\r\n          :itemData=\"carpoolData\"\r\n          @action-completed=\"handleTopCompleted\"\r\n          @action-cancelled=\"handleTopCancelled\"\r\n        />\r\n      </view>\r\n      \r\n      <!-- 温馨提示 -->\r\n      <view class=\"tips-card\">\r\n        <view class=\"tips-header\">\r\n          <image src=\"/static/images/tabbar/info.png\" mode=\"aspectFit\" class=\"tips-icon\"></image>\r\n          <text class=\"tips-title\">温馨提示</text>\r\n        </view>\r\n        <view class=\"tips-content\">\r\n          <text class=\"tips-text\">· 置顶服务在您支付成功后立即生效</text>\r\n          <text class=\"tips-text\">· 置顶期间，您的信息将始终保持在列表顶部</text>\r\n          <text class=\"tips-text\">· 如有疑问，请联系客服：************</text>\r\n        </view>\r\n      </view>\r\n    </view>\r\n    \r\n\r\n  </view>\r\n</template>\r\n\r\n<script setup>\r\nimport { ref, computed, onMounted } from 'vue';\r\nimport ConfigurablePremiumActions from '@/components/premium/ConfigurablePremiumActions.vue';\r\n\r\n// 信息ID\r\nconst infoId = ref('');\r\n\r\n// 信息数据\r\nconst infoData = ref({\r\n  startPoint: '磁州城区',\r\n  endPoint: '邯郸站',\r\n  type: 'people-to-car'\r\n});\r\n\r\n// 拼车置顶数据\r\nconst carpoolData = ref({\r\n  id: infoId.value || 'carpool_123',\r\n  title: '拼车置顶',\r\n  description: `${infoData.value.startPoint} → ${infoData.value.endPoint}`,\r\n  type: infoData.value.type\r\n});\r\n\r\n// 置顶选项\r\nconst topOptions = ref([\r\n  {\r\n    title: '置顶1天',\r\n    desc: '24小时置顶显示',\r\n    price: '2.00',\r\n    days: 1\r\n  },\r\n  {\r\n    title: '置顶3天',\r\n    desc: '72小时置顶显示',\r\n    price: '5.00',\r\n    days: 3\r\n  },\r\n  {\r\n    title: '置顶7天',\r\n    desc: '7天置顶显示',\r\n    price: '10.00',\r\n    days: 7\r\n  }\r\n]);\r\n\r\n// 支付方式\r\nconst paymentMethods = ref([\r\n  {\r\n    name: '微信支付',\r\n    icon: '/static/images/tabbar/wechat-pay.png',\r\n    id: 'wxpay'\r\n  },\r\n  {\r\n    name: '余额支付',\r\n    icon: '/static/images/tabbar/wallet.png',\r\n    id: 'balance'\r\n  }\r\n]);\r\n\r\n// 选中的选项\r\nconst selectedOption = ref(1); // 默认选中3天\r\nconst selectedPayment = ref(0); // 默认选中微信支付\r\n\r\n// 计算属性\r\nconst typeText = computed(() => {\r\n  const typeMap = {\r\n    'people-to-car': '人找车',\r\n    'car-to-people': '车找人',\r\n    'goods-to-car': '货找车',\r\n    'car-to-goods': '车找货'\r\n  };\r\n  return typeMap[infoData.value.type] || '人找车';\r\n});\r\n\r\nconst typeClass = computed(() => {\r\n  return infoData.value.type;\r\n});\r\n\r\nconst selectedOptionPrice = computed(() => {\r\n  return topOptions.value[selectedOption.value].price;\r\n});\r\n\r\n// 页面加载\r\nonMounted(() => {\r\n  const pages = getCurrentPages();\r\n  const currentPage = pages[pages.length - 1];\r\n  const options = currentPage.options || {};\r\n  \r\n  // 获取信息ID\r\n  if (options && options.id) {\r\n    infoId.value = options.id;\r\n    getInfoDetail();\r\n  }\r\n});\r\n\r\n// 获取信息详情\r\nconst getInfoDetail = () => {\r\n  // 这里应该是真实的API调用\r\n  // 目前使用模拟数据\r\n  console.log('获取信息ID:', infoId.value);\r\n  \r\n  // 模拟获取数据\r\n  setTimeout(() => {\r\n    infoData.value = {\r\n      startPoint: '磁州城区',\r\n      endPoint: '邯郸站',\r\n      type: 'car-to-people'\r\n    };\r\n  }, 500);\r\n};\r\n\r\n// 选择置顶选项\r\nconst selectOption = (index) => {\r\n  selectedOption.value = index;\r\n};\r\n\r\n// 选择支付方式\r\nconst selectPayment = (index) => {\r\n  selectedPayment.value = index;\r\n};\r\n\r\n// 处理置顶完成\r\nconst handleTopCompleted = (result) => {\r\n  console.log('置顶完成:', result);\r\n\r\n  if (result.type === 'ad') {\r\n    // 看广告置顶成功\r\n    uni.showToast({\r\n      title: '置顶成功',\r\n      icon: 'success',\r\n      duration: 2000\r\n    });\r\n  } else if (result.type === 'payment') {\r\n    // 付费置顶成功\r\n    uni.showToast({\r\n      title: '支付成功，置顶生效',\r\n      icon: 'success',\r\n      duration: 2000\r\n    });\r\n  }\r\n\r\n  // 延迟跳转回详情页\r\n  setTimeout(() => {\r\n    uni.navigateBack();\r\n  }, 2000);\r\n};\r\n\r\n// 处理置顶取消\r\nconst handleTopCancelled = (result) => {\r\n  console.log('置顶取消:', result);\r\n  if (result.type === 'ad') {\r\n    uni.showToast({\r\n      title: '已取消观看广告',\r\n      icon: 'none'\r\n    });\r\n  } else if (result.type === 'payment') {\r\n    uni.showToast({\r\n      title: '已取消支付',\r\n      icon: 'none'\r\n    });\r\n  }\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.top-container {\r\n  min-height: 100vh;\r\n  background-color: #f5f7fa;\r\n  padding-bottom: 120rpx; /* 为底部支付栏留出空间 */\r\n}\r\n\r\n.top-content {\r\n  padding: 30rpx;\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 30rpx;\r\n}\r\n\r\n/* 信息卡片 */\r\n.info-card {\r\n  background-color: #ffffff;\r\n  border-radius: 24rpx;\r\n  padding: 30rpx;\r\n  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.04);\r\n}\r\n\r\n.card-header {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-bottom: 20rpx;\r\n}\r\n\r\n.card-title {\r\n  font-size: 32rpx;\r\n  font-weight: 600;\r\n  color: #333333;\r\n}\r\n\r\n.info-tag {\r\n  margin-left: 15rpx;\r\n  font-size: 22rpx;\r\n  color: #ffffff;\r\n  padding: 4rpx 12rpx;\r\n  border-radius: 6rpx;\r\n}\r\n\r\n.people-to-car {\r\n  background-color: #0A84FF;\r\n}\r\n\r\n.car-to-people {\r\n  background-color: #FF453A;\r\n}\r\n\r\n.goods-to-car {\r\n  background-color: #30D158;\r\n}\r\n\r\n.car-to-goods {\r\n  background-color: #FF9F0A;\r\n}\r\n\r\n/* 路线信息 */\r\n.route-section {\r\n  margin-bottom: 10rpx;\r\n}\r\n\r\n.route-points {\r\n  margin-bottom: 10rpx;\r\n}\r\n\r\n.route-point {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-bottom: 16rpx;\r\n}\r\n\r\n.point-dot {\r\n  width: 16rpx;\r\n  height: 16rpx;\r\n  border-radius: 50%;\r\n  margin-right: 15rpx;\r\n  flex-shrink: 0;\r\n}\r\n\r\n.start {\r\n  background-color: #0A84FF;\r\n}\r\n\r\n.end {\r\n  background-color: #FF453A;\r\n}\r\n\r\n.point-text {\r\n  font-size: 32rpx;\r\n  color: #333333;\r\n  font-weight: 500;\r\n}\r\n\r\n.route-divider {\r\n  padding-left: 7rpx;\r\n  margin: 10rpx 0;\r\n  display: flex;\r\n}\r\n\r\n.divider-line {\r\n  width: 2rpx;\r\n  height: 30rpx;\r\n  background-color: #dddddd;\r\n}\r\n\r\n.divider-arrow {\r\n  margin-left: -7rpx;\r\n  margin-top: 30rpx;\r\n}\r\n\r\n.arrow-icon {\r\n  width: 24rpx;\r\n  height: 24rpx;\r\n  opacity: 0.6;\r\n}\r\n\r\n/* 置顶选项卡片 */\r\n.premium-card {\r\n  background-color: #ffffff;\r\n  border-radius: 24rpx;\r\n  padding: 30rpx;\r\n  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.04);\r\n}\r\n\r\n.premium-header {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-bottom: 20rpx;\r\n}\r\n\r\n.premium-icon {\r\n  width: 36rpx;\r\n  height: 36rpx;\r\n  margin-right: 15rpx;\r\n}\r\n\r\n.premium-title {\r\n  font-size: 32rpx;\r\n  font-weight: 600;\r\n  color: #333333;\r\n}\r\n\r\n.premium-desc {\r\n  font-size: 28rpx;\r\n  color: #666666;\r\n  margin-bottom: 30rpx;\r\n  line-height: 1.5;\r\n}\r\n\r\n.options-list {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 20rpx;\r\n}\r\n\r\n.option-item {\r\n  background-color: #f8f8f8;\r\n  border-radius: 16rpx;\r\n  padding: 24rpx;\r\n  position: relative;\r\n  transition: all 0.3s;\r\n}\r\n\r\n.option-selected {\r\n  background-color: rgba(10, 132, 255, 0.05);\r\n  border: 1rpx solid #0A84FF;\r\n}\r\n\r\n.option-content {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.option-title {\r\n  font-size: 30rpx;\r\n  font-weight: 600;\r\n  color: #333333;\r\n  margin-bottom: 8rpx;\r\n}\r\n\r\n.option-desc {\r\n  font-size: 24rpx;\r\n  color: #999999;\r\n}\r\n\r\n.option-price {\r\n  font-size: 36rpx;\r\n  font-weight: 700;\r\n  color: #FF3B30;\r\n}\r\n\r\n.option-price::before {\r\n  content: '¥';\r\n  font-size: 24rpx;\r\n  font-weight: 400;\r\n}\r\n\r\n.option-check {\r\n  position: absolute;\r\n  right: 24rpx;\r\n  top: 50%;\r\n  transform: translateY(-50%);\r\n  width: 36rpx;\r\n  height: 36rpx;\r\n  border-radius: 50%;\r\n  background-color: #0A84FF;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.check-icon {\r\n  width: 20rpx;\r\n  height: 20rpx;\r\n}\r\n\r\n/* 支付方式卡片 */\r\n.payment-card {\r\n  background-color: #ffffff;\r\n  border-radius: 24rpx;\r\n  padding: 30rpx;\r\n  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.04);\r\n}\r\n\r\n.payment-header {\r\n  margin-bottom: 20rpx;\r\n}\r\n\r\n.payment-title {\r\n  font-size: 32rpx;\r\n  font-weight: 600;\r\n  color: #333333;\r\n}\r\n\r\n.payment-options {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 20rpx;\r\n}\r\n\r\n.payment-item {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 24rpx;\r\n  background-color: #f8f8f8;\r\n  border-radius: 16rpx;\r\n  transition: all 0.3s;\r\n}\r\n\r\n.payment-selected {\r\n  background-color: rgba(10, 132, 255, 0.05);\r\n  border: 1rpx solid #0A84FF;\r\n}\r\n\r\n.payment-left {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.payment-icon {\r\n  width: 40rpx;\r\n  height: 40rpx;\r\n  margin-right: 20rpx;\r\n}\r\n\r\n.payment-name {\r\n  font-size: 28rpx;\r\n  color: #333333;\r\n}\r\n\r\n.payment-check {\r\n  width: 36rpx;\r\n  height: 36rpx;\r\n  border-radius: 50%;\r\n  background-color: #0A84FF;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n/* 温馨提示 */\r\n.tips-card {\r\n  background-color: rgba(10, 132, 255, 0.05);\r\n  border-radius: 24rpx;\r\n  padding: 20rpx 30rpx;\r\n}\r\n\r\n.tips-header {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-bottom: 15rpx;\r\n}\r\n\r\n.tips-icon {\r\n  width: 28rpx;\r\n  height: 28rpx;\r\n  margin-right: 10rpx;\r\n  opacity: 0.7;\r\n}\r\n\r\n.tips-title {\r\n  font-size: 28rpx;\r\n  font-weight: 600;\r\n  color: #333333;\r\n}\r\n\r\n.tips-content {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 10rpx;\r\n}\r\n\r\n.tips-text {\r\n  font-size: 24rpx;\r\n  color: #666666;\r\n  line-height: 1.5;\r\n}\r\n\r\n/* 底部支付栏 */\r\n.bottom-bar {\r\n  position: fixed;\r\n  bottom: 0;\r\n  left: 0;\r\n  right: 0;\r\n  height: 120rpx;\r\n  background-color: #ffffff;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n  padding: 0 30rpx;\r\n  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);\r\n  z-index: 100;\r\n}\r\n\r\n.price-info {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.price-label {\r\n  font-size: 28rpx;\r\n  color: #666666;\r\n}\r\n\r\n.price-value {\r\n  font-size: 36rpx;\r\n  font-weight: 700;\r\n  color: #FF3B30;\r\n}\r\n\r\n.pay-button {\r\n  height: 80rpx;\r\n  border-radius: 40rpx;\r\n  background: linear-gradient(135deg, #0A84FF, #0066CC);\r\n  color: #ffffff;\r\n  font-size: 30rpx;\r\n  font-weight: 600;\r\n  padding: 0 60rpx;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  box-shadow: 0 4rpx 10rpx rgba(10, 132, 255, 0.3);\r\n}\r\n</style> ", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/carpool-package/pages/carpool/premium/top.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "computed", "onMounted", "uni", "MiniProgramPage"], "mappings": ";;;;;;;;;;AA0EA,MAAM,6BAA6B,MAAW;;;;AAG9C,UAAM,SAASA,cAAAA,IAAI,EAAE;AAGrB,UAAM,WAAWA,cAAAA,IAAI;AAAA,MACnB,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAGD,UAAM,cAAcA,cAAAA,IAAI;AAAA,MACtB,IAAI,OAAO,SAAS;AAAA,MACpB,OAAO;AAAA,MACP,aAAa,GAAG,SAAS,MAAM,UAAU,MAAM,SAAS,MAAM,QAAQ;AAAA,MACtE,MAAM,SAAS,MAAM;AAAA,IACvB,CAAC;AAGD,UAAM,aAAaA,cAAAA,IAAI;AAAA,MACrB;AAAA,QACE,OAAO;AAAA,QACP,MAAM;AAAA,QACN,OAAO;AAAA,QACP,MAAM;AAAA,MACP;AAAA,MACD;AAAA,QACE,OAAO;AAAA,QACP,MAAM;AAAA,QACN,OAAO;AAAA,QACP,MAAM;AAAA,MACP;AAAA,MACD;AAAA,QACE,OAAO;AAAA,QACP,MAAM;AAAA,QACN,OAAO;AAAA,QACP,MAAM;AAAA,MACP;AAAA,IACH,CAAC;AAGsBA,kBAAAA,IAAI;AAAA,MACzB;AAAA,QACE,MAAM;AAAA,QACN,MAAM;AAAA,QACN,IAAI;AAAA,MACL;AAAA,MACD;AAAA,QACE,MAAM;AAAA,QACN,MAAM;AAAA,QACN,IAAI;AAAA,MACL;AAAA,IACH,CAAC;AAGD,UAAM,iBAAiBA,cAAAA,IAAI,CAAC;AACJA,kBAAG,IAAC,CAAC;AAG7B,UAAM,WAAWC,cAAQ,SAAC,MAAM;AAC9B,YAAM,UAAU;AAAA,QACd,iBAAiB;AAAA,QACjB,iBAAiB;AAAA,QACjB,gBAAgB;AAAA,QAChB,gBAAgB;AAAA,MACpB;AACE,aAAO,QAAQ,SAAS,MAAM,IAAI,KAAK;AAAA,IACzC,CAAC;AAED,UAAM,YAAYA,cAAQ,SAAC,MAAM;AAC/B,aAAO,SAAS,MAAM;AAAA,IACxB,CAAC;AAE2BA,kBAAAA,SAAS,MAAM;AACzC,aAAO,WAAW,MAAM,eAAe,KAAK,EAAE;AAAA,IAChD,CAAC;AAGDC,kBAAAA,UAAU,MAAM;AACd,YAAM,QAAQ;AACd,YAAM,cAAc,MAAM,MAAM,SAAS,CAAC;AAC1C,YAAM,UAAU,YAAY,WAAW;AAGvC,UAAI,WAAW,QAAQ,IAAI;AACzB,eAAO,QAAQ,QAAQ;AACvB;MACD;AAAA,IACH,CAAC;AAGD,UAAM,gBAAgB,MAAM;AAG1BC,oBAAY,MAAA,MAAA,OAAA,wDAAA,WAAW,OAAO,KAAK;AAGnC,iBAAW,MAAM;AACf,iBAAS,QAAQ;AAAA,UACf,YAAY;AAAA,UACZ,UAAU;AAAA,UACV,MAAM;AAAA,QACZ;AAAA,MACG,GAAE,GAAG;AAAA,IACR;AAaA,UAAM,qBAAqB,CAAC,WAAW;AACrCA,oBAAY,MAAA,MAAA,OAAA,wDAAA,SAAS,MAAM;AAE3B,UAAI,OAAO,SAAS,MAAM;AAExBA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,UACN,UAAU;AAAA,QAChB,CAAK;AAAA,MACL,WAAa,OAAO,SAAS,WAAW;AAEpCA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,UACN,UAAU;AAAA,QAChB,CAAK;AAAA,MACF;AAGD,iBAAW,MAAM;AACfA,sBAAG,MAAC,aAAY;AAAA,MACjB,GAAE,GAAI;AAAA,IACT;AAGA,UAAM,qBAAqB,CAAC,WAAW;AACrCA,oBAAY,MAAA,MAAA,OAAA,wDAAA,SAAS,MAAM;AAC3B,UAAI,OAAO,SAAS,MAAM;AACxBA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AAAA,MACL,WAAa,OAAO,SAAS,WAAW;AACpCA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AAAA,MACF;AAAA,IACH;;;;;;;;;;;;;;;;;;;;;;;;ACvOA,GAAG,WAAWC,SAAe;"}