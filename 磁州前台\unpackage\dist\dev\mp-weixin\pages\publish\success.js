"use strict";
const common_vendor = require("../../common/vendor.js");
const common_assets = require("../../common/assets.js");
if (!Array) {
  const _component_path = common_vendor.resolveComponent("path");
  const _component_svg = common_vendor.resolveComponent("svg");
  (_component_path + _component_svg)();
}
if (!Math) {
  ConfigurablePremiumActions();
}
const ConfigurablePremiumActions = () => "../../components/premium/ConfigurablePremiumActions.js";
const _sfc_main = {
  __name: "success",
  setup(__props) {
    const btnList = [
      { text1: "管理", text2: "信息", action: () => common_vendor.index.reLaunch({ url: "/pages/my/publish" }) },
      { text1: "查看", text2: "信息", action: viewInfo },
      { text1: "再发", text2: "一条", action: () => common_vendor.index.navigateTo({ url: "/pages/publish/publish" }) },
      { text1: "分享", text2: "信息", action: shareInfo },
      { text1: "首", text2: "页", action: () => common_vendor.index.switchTab({ url: "/pages/index/index" }) }
    ];
    const topResultVisible = common_vendor.ref(false);
    const topSuccess = common_vendor.ref(false);
    const topResultText = common_vendor.ref("");
    const shareTipsVisible = common_vendor.ref(false);
    const qrcodeVisible = common_vendor.ref(false);
    const wechatId = common_vendor.ref("cishangtc");
    const publishId = common_vendor.ref("");
    const publishData = common_vendor.ref({
      id: "",
      title: "信息置顶",
      description: "置顶您的信息，获得更多曝光"
    });
    const originalPublishData = common_vendor.ref({
      title: "",
      content: "",
      images: [],
      category: ""
    });
    common_vendor.onMounted(() => {
      const pubId = common_vendor.index.getStorageSync("lastPublishId");
      publishId.value = pubId;
      const pubData = common_vendor.index.getStorageSync("lastPublishData");
      if (pubData) {
        updateHomePageData(pubData);
        originalPublishData.value = {
          title: pubData.title || pubData.content || "磁州同城信息",
          content: pubData.content || pubData.description || "",
          images: pubData.images || [],
          category: pubData.category || pubData.categoryName || "同城信息"
        };
        publishData.value = {
          id: pubId || "publish_" + Date.now(),
          title: "信息置顶",
          description: `置顶"${pubData.title || pubData.content || "您的信息"}"，获得更多曝光`
        };
      }
      checkTopStatus(pubId);
      setTimeout(() => {
        shareTipsVisible.value = true;
      }, 1e3);
    });
    const checkTopStatus = async (infoId) => {
      if (!infoId)
        return;
      try {
        common_vendor.index.showLoading({ title: "加载中" });
        const res = await common_vendor.index.request({
          url: "/api/info/top/status",
          method: "GET",
          data: { infoId }
        });
        if (res.data && res.data.isTopped) {
          common_vendor.index.showToast({
            title: `信息已置顶，剩余${res.data.remainingDays}天`,
            icon: "none",
            duration: 3e3
          });
        }
        common_vendor.index.hideLoading();
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/publish/success.vue:269", "检查置顶状态失败", error);
        common_vendor.index.hideLoading();
      }
    };
    const updateHomePageData = (publishData2) => {
      try {
        const allInfoList = common_vendor.index.getStorageSync("homeAllInfoList") || [];
        const newInfo = {
          id: Date.now(),
          category: publishData2.category || publishData2.categoryName,
          content: publishData2.content || publishData2.title,
          time: (/* @__PURE__ */ new Date()).toLocaleString(),
          views: 0
        };
        allInfoList.unshift(newInfo);
        common_vendor.index.setStorageSync("homeAllInfoList", allInfoList);
        common_vendor.index.__f__("log", "at pages/publish/success.vue:295", "成功将发布的信息同步到首页", newInfo);
      } catch (e) {
        common_vendor.index.__f__("error", "at pages/publish/success.vue:297", "同步数据到首页失败", e);
      }
    };
    const viewInfo = () => {
      const pubId = common_vendor.index.getStorageSync("lastPublishId");
      common_vendor.index.navigateTo({
        url: "/pages/publish/detail?id=" + pubId
      });
    };
    const handleTopActionCompleted = (result) => {
      common_vendor.index.__f__("log", "at pages/publish/success.vue:313", "置顶操作完成", result);
      topResultVisible.value = true;
      topSuccess.value = true;
      if (result.type === "ad") {
        topResultText.value = "广告置顶成功！信息已置顶2小时";
      } else if (result.type === "paid") {
        topResultText.value = `付费置顶成功！信息已置顶${result.option.duration}`;
      }
      setTimeout(() => {
        topResultVisible.value = false;
      }, 2e3);
    };
    const handleTopActionCancelled = (result) => {
      common_vendor.index.__f__("log", "at pages/publish/success.vue:333", "置顶操作取消", result);
      if (result.type === "ad") {
        common_vendor.index.showToast({
          title: "已取消观看广告",
          icon: "none"
        });
      } else if (result.type === "payment") {
        common_vendor.index.showToast({
          title: "已取消支付",
          icon: "none"
        });
      }
    };
    const goBack = () => common_vendor.index.navigateBack();
    const hideShareTips = () => {
      shareTipsVisible.value = false;
    };
    const shareInfo = () => {
      beforeShare();
      common_vendor.index.showShareMenu({
        withShareTicket: true,
        menus: ["shareAppMessage", "shareTimeline"],
        success: () => {
          common_vendor.index.__f__("log", "at pages/publish/success.vue:365", "显示分享菜单成功");
        },
        fail: (err) => {
          common_vendor.index.__f__("error", "at pages/publish/success.vue:368", "显示分享菜单失败", err);
          shareTipsVisible.value = true;
        }
      });
    };
    const beforeShare = () => {
      common_vendor.index.__f__("log", "at pages/publish/success.vue:377", "准备分享数据");
      const pubId = common_vendor.index.getStorageSync("lastPublishId");
      if (!pubId) {
        common_vendor.index.__f__("error", "at pages/publish/success.vue:383", "未找到发布ID，无法获取分享内容");
        return;
      }
      const pubData = common_vendor.index.getStorageSync("lastPublishData");
      let shareTitle = "";
      let shareImage = "";
      if (pubData) {
        if (pubData.title) {
          shareTitle = pubData.title;
        } else if (pubData.content) {
          shareTitle = pubData.content.substring(0, 20) + (pubData.content.length > 20 ? "..." : "");
        } else {
          shareTitle = pubData.category || pubData.categoryName || "磁州同城信息";
        }
        if (pubData.images && pubData.images.length > 0) {
          shareImage = pubData.images[0];
        }
      } else {
        shareTitle = "磁州同城信息";
      }
      const app = getApp();
      if (app.globalData) {
        app.globalData.shareInfo = {
          title: shareTitle,
          path: "/pages/publish/detail?id=" + pubId,
          imageUrl: shareImage
        };
      } else {
        app.globalData = {
          shareInfo: {
            title: shareTitle,
            path: "/pages/publish/detail?id=" + pubId,
            imageUrl: shareImage
          }
        };
      }
      common_vendor.index.__f__("log", "at pages/publish/success.vue:432", "分享数据已准备:", app.globalData.shareInfo);
    };
    const jumpToDetailAndShare = () => {
      const pubId = common_vendor.index.getStorageSync("lastPublishId");
      if (pubId) {
        beforeShare();
        hideShareTips();
        common_vendor.index.navigateTo({
          url: `/pages/publish/info-detail?id=${pubId}&autoShare=true`,
          success: () => {
            common_vendor.index.__f__("log", "at pages/publish/success.vue:447", "已跳转到信息展示详情页，准备自动分享");
          },
          fail: (err) => {
            common_vendor.index.__f__("error", "at pages/publish/success.vue:450", "跳转信息展示详情页失败", err);
            common_vendor.index.showToast({
              title: "跳转失败，请重试",
              icon: "none"
            });
          }
        });
      } else {
        common_vendor.index.showToast({
          title: "信息ID不存在",
          icon: "none"
        });
      }
    };
    const contactService = () => {
      qrcodeVisible.value = true;
      hideShareTips();
    };
    const hideQrcode = () => {
      qrcodeVisible.value = false;
    };
    const copyWechatId = () => {
      common_vendor.index.setClipboardData({
        data: wechatId.value,
        success: function() {
          common_vendor.index.showToast({
            title: "客服微信号已复制",
            icon: "success"
          });
        }
      });
    };
    const saveQrcode = () => {
      common_vendor.index.getSetting({
        success: (res) => {
          if (!res.authSetting["scope.writePhotosAlbum"]) {
            common_vendor.index.authorize({
              scope: "scope.writePhotosAlbum",
              success: () => {
                saveQrcodeToAlbum();
              },
              fail: () => {
                common_vendor.index.showModal({
                  title: "提示",
                  content: "需要授权保存图片到相册",
                  success: (res2) => {
                    if (res2.confirm) {
                      common_vendor.index.openSetting();
                    }
                  }
                });
              }
            });
          } else {
            saveQrcodeToAlbum();
          }
        }
      });
    };
    const saveQrcodeToAlbum = () => {
      common_vendor.index.showLoading({ title: "保存中..." });
      common_vendor.index.downloadFile({
        url: "/static/images/qrcode.png",
        // 这里应该是完整的URL路径
        success: (res) => {
          if (res.statusCode === 200) {
            common_vendor.index.saveImageToPhotosAlbum({
              filePath: res.tempFilePath,
              success: () => {
                common_vendor.index.hideLoading();
                common_vendor.index.showToast({
                  title: "二维码已保存到相册",
                  icon: "success"
                });
              },
              fail: (err) => {
                common_vendor.index.hideLoading();
                common_vendor.index.__f__("error", "at pages/publish/success.vue:541", "保存失败", err);
                common_vendor.index.showToast({
                  title: "保存失败",
                  icon: "none"
                });
              }
            });
          } else {
            common_vendor.index.hideLoading();
            common_vendor.index.showToast({
              title: "下载图片失败",
              icon: "none"
            });
          }
        },
        fail: () => {
          common_vendor.index.hideLoading();
          common_vendor.index.showToast({
            title: "下载图片失败",
            icon: "none"
          });
        }
      });
    };
    const showKefu = () => {
      qrcodeVisible.value = true;
    };
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_assets._imports_0$13,
        b: common_vendor.o(goBack),
        c: common_assets._imports_1$17,
        d: common_assets._imports_2$14,
        e: common_vendor.f(btnList, (btn, idx, i0) => {
          return {
            a: common_vendor.t(btn.text1),
            b: common_vendor.t(btn.text2),
            c: idx,
            d: common_vendor.n(idx === 1 ? "main-btn-active" : ""),
            e: common_vendor.o(btn.action, idx)
          };
        }),
        f: common_assets._imports_3$12,
        g: common_vendor.o(handleTopActionCompleted),
        h: common_vendor.o(handleTopActionCancelled),
        i: common_vendor.p({
          pageType: "publish_top",
          showMode: "direct",
          itemData: publishData.value
        }),
        j: common_assets._imports_0$15,
        k: common_vendor.o(beforeShare),
        l: common_assets._imports_13$2,
        m: common_vendor.o(showKefu),
        n: topResultVisible.value
      }, topResultVisible.value ? {
        o: topSuccess.value ? "/static/images/pay/success.png" : "/static/images/pay/fail.png",
        p: common_vendor.t(topResultText.value)
      } : {}, {
        q: shareTipsVisible.value
      }, shareTipsVisible.value ? {
        r: common_assets._imports_6$7,
        s: common_vendor.o(hideShareTips),
        t: common_vendor.o(jumpToDetailAndShare),
        v: common_vendor.o(contactService),
        w: common_vendor.o(() => {
        })
      } : {}, {
        x: qrcodeVisible.value
      }, qrcodeVisible.value ? {
        y: common_vendor.p({
          d: "M572.16 512l183.466667-183.04a42.666667 42.666667 0 1 0-60.586667-60.586667L512 451.84 328.96 268.373333a42.666667 42.666667 0 0 0-60.586667 60.586667l183.04 183.04-183.04 183.466667a42.666667 42.666667 0 0 0 60.586667 60.586666L512 572.16l183.04 183.466667a42.666667 42.666667 0 0 0 60.586667-60.586667z",
          fill: "#999999"
        }),
        z: common_vendor.p({
          t: "1692586074385",
          viewBox: "0 0 1024 1024",
          version: "1.1",
          xmlns: "http://www.w3.org/2000/svg",
          width: "20",
          height: "20"
        }),
        A: common_vendor.o(hideQrcode),
        B: common_assets._imports_14$4,
        C: common_assets._imports_15$3,
        D: common_vendor.p({
          fill: "currentColor",
          d: "M9.5,6.5v3h-3v-3H9.5 M11,5H5v6h6V5L11,5z M9.5,14.5v3h-3v-3H9.5 M11,13H5v6h6V13z M17.5,6.5v3h-3v-3H17.5 M19,5h-6v6h6V5L19,5z M13,13h1.5v1.5H13V13z M14.5,14.5H16V16h-1.5V14.5z M16,13h1.5v1.5H16V13z M13,16h1.5v1.5H13V16z M14.5,17.5H16V19h-1.5V17.5z M16,16h1.5v1.5H16V16z M17.5,14.5H19V16h-1.5V14.5z M17.5,17.5H19V19h-1.5V17.5z M22,7h-2V4h-3V2h5V7z M22,22v-5h-2v3h-3v2H22z M2,22h5v-2H4v-3H2V22z M2,2v5h2V4h3V2H2z"
        }),
        E: common_vendor.p({
          viewBox: "0 0 24 24",
          width: "16",
          height: "16"
        }),
        F: common_vendor.p({
          fill: "currentColor",
          d: "M12,2A10,10 0 0,1 22,12A10,10 0 0,1 12,22A10,10 0 0,1 2,12A10,10 0 0,1 12,2M12,4A8,8 0 0,0 4,12A8,8 0 0,0 12,20A8,8 0 0,0 20,12A8,8 0 0,0 12,4M11,7H13V13H11V7M11,15H13V17H11V15Z"
        }),
        G: common_vendor.p({
          viewBox: "0 0 24 24",
          width: "16",
          height: "16"
        }),
        H: common_vendor.p({
          fill: "currentColor",
          d: "M12,4A4,4 0 0,1 16,8A4,4 0 0,1 12,12A4,4 0 0,1 8,8A4,4 0 0,1 12,4M12,6A2,2 0 0,0 10,8A2,2 0 0,0 12,10A2,2 0 0,0 14,8A2,2 0 0,0 12,6M12,13C14.67,13 20,14.33 20,17V20H4V17C4,14.33 9.33,13 12,13M12,14.9C9.03,14.9 5.9,16.36 5.9,17V18.1H18.1V17C18.1,16.36 14.97,14.9 12,14.9Z"
        }),
        I: common_vendor.p({
          viewBox: "0 0 24 24",
          width: "16",
          height: "16"
        }),
        J: common_vendor.p({
          fill: "currentColor",
          d: "M19,21H8V7H19M19,5H8A2,2 0 0,0 6,7V21A2,2 0 0,0 8,23H19A2,2 0 0,0 21,21V7A2,2 0 0,0 19,5M16,1H4A2,2 0 0,0 2,3V17H4V3H16V1Z"
        }),
        K: common_vendor.p({
          viewBox: "0 0 24 24",
          width: "16",
          height: "16"
        }),
        L: common_vendor.o(copyWechatId),
        M: common_vendor.p({
          fill: "currentColor",
          d: "M15,9H5V5H15M12,19A3,3 0 0,1 9,16A3,3 0 0,1 12,13A3,3 0 0,1 15,16A3,3 0 0,1 12,19M17,3H5C3.89,3 3,3.9 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V7L17,3Z"
        }),
        N: common_vendor.p({
          viewBox: "0 0 24 24",
          width: "16",
          height: "16"
        }),
        O: common_vendor.o(saveQrcode),
        P: common_vendor.o(() => {
        }),
        Q: common_vendor.o(hideQrcode)
      } : {});
    };
  }
};
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-1e37871e"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/publish/success.js.map
