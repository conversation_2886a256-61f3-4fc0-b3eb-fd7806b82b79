{"version": 3, "file": "share.js", "sources": ["utils/share.js"], "sourcesContent": ["/**\r\n * 分享工具函数\r\n */\r\n\r\n// 分享配置\r\nconst shareConfig = {\r\n  // 分享标题\r\n  getTitle(type, data) {\r\n    const titles = {\r\n      redPacket: `${data.sender.nickname}的红包`,\r\n      post: data.title || '分享一个有趣的动态',\r\n      promotion: data.title || '分享一个优惠活动'\r\n    };\r\n    return titles[type] || '分享';\r\n  },\r\n  \r\n  // 分享描述\r\n  getDesc(type, data) {\r\n    const descs = {\r\n      redPacket: '快来抢红包啦！',\r\n      post: data.content || '快来看看吧',\r\n      promotion: data.description || '优惠活动等你来'\r\n    };\r\n    return descs[type] || '';\r\n  },\r\n  \r\n  // 分享图片\r\n  getImage(type, data) {\r\n    const images = {\r\n      redPacket: '/static/images/share-red-packet.png',\r\n      post: data.images?.[0] || '/static/images/share-post.png',\r\n      promotion: data.image || '/static/images/share-promotion.png'\r\n    };\r\n    return images[type] || '/static/images/share-default.png';\r\n  },\r\n  \r\n  // 分享链接\r\n  getPath(type, data) {\r\n    const paths = {\r\n      redPacket: `/pages/red-packet/grab?id=${data.id}`,\r\n      post: `/pages/post/detail?id=${data.id}`,\r\n      promotion: `/pages/promotion/detail?id=${data.id}`\r\n    };\r\n    return paths[type] || '/pages/index/index';\r\n  }\r\n};\r\n\r\n/**\r\n * 分享到微信好友\r\n * @param {Object} options 分享选项\r\n * @param {string} options.type 分享类型\r\n * @param {Object} options.data 分享数据\r\n * @returns {Promise}\r\n */\r\nexport function shareToWechat(options) {\r\n  return new Promise((resolve, reject) => {\r\n    uni.share({\r\n      provider: 'weixin',\r\n      scene: 'WXSceneSession',\r\n      type: 0,\r\n      title: shareConfig.getTitle(options.type, options.data),\r\n      summary: shareConfig.getDesc(options.type, options.data),\r\n      imageUrl: shareConfig.getImage(options.type, options.data),\r\n      href: shareConfig.getPath(options.type, options.data),\r\n      success: function (res) {\r\n        resolve(res);\r\n      },\r\n      fail: function (err) {\r\n        reject(err);\r\n      }\r\n    });\r\n  });\r\n}\r\n\r\n/**\r\n * 分享到朋友圈\r\n * @param {Object} options 分享选项\r\n * @param {string} options.type 分享类型\r\n * @param {Object} options.data 分享数据\r\n * @returns {Promise}\r\n */\r\nexport function shareToTimeline(options) {\r\n  return new Promise((resolve, reject) => {\r\n    uni.share({\r\n      provider: 'weixin',\r\n      scene: 'WXSceneTimeline',\r\n      type: 0,\r\n      title: shareConfig.getTitle(options.type, options.data),\r\n      summary: shareConfig.getDesc(options.type, options.data),\r\n      imageUrl: shareConfig.getImage(options.type, options.data),\r\n      href: shareConfig.getPath(options.type, options.data),\r\n      success: function (res) {\r\n        resolve(res);\r\n      },\r\n      fail: function (err) {\r\n        reject(err);\r\n      }\r\n    });\r\n  });\r\n}\r\n\r\n/**\r\n * 显示分享菜单\r\n * @param {Object} options 分享选项\r\n * @param {string} options.type 分享类型\r\n * @param {Object} options.data 分享数据\r\n * @returns {Promise}\r\n */\r\nexport function showShareMenu(options) {\r\n  return new Promise((resolve, reject) => {\r\n    uni.showActionSheet({\r\n      itemList: ['分享到微信好友', '分享到朋友圈'],\r\n      success: function (res) {\r\n        if (res.tapIndex === 0) {\r\n          shareToWechat(options).then(resolve).catch(reject);\r\n        } else if (res.tapIndex === 1) {\r\n          shareToTimeline(options).then(resolve).catch(reject);\r\n        }\r\n      },\r\n      fail: function (err) {\r\n        reject(err);\r\n      }\r\n    });\r\n  });\r\n}\r\n\r\nexport default {\r\n  shareToWechat,\r\n  shareToTimeline,\r\n  showShareMenu\r\n}; "], "names": ["uni"], "mappings": ";;AAKA,MAAM,cAAc;AAAA;AAAA,EAElB,SAAS,MAAM,MAAM;AACnB,UAAM,SAAS;AAAA,MACb,WAAW,GAAG,KAAK,OAAO,QAAQ;AAAA,MAClC,MAAM,KAAK,SAAS;AAAA,MACpB,WAAW,KAAK,SAAS;AAAA,IAC/B;AACI,WAAO,OAAO,IAAI,KAAK;AAAA,EACxB;AAAA;AAAA,EAGD,QAAQ,MAAM,MAAM;AAClB,UAAM,QAAQ;AAAA,MACZ,WAAW;AAAA,MACX,MAAM,KAAK,WAAW;AAAA,MACtB,WAAW,KAAK,eAAe;AAAA,IACrC;AACI,WAAO,MAAM,IAAI,KAAK;AAAA,EACvB;AAAA;AAAA,EAGD,SAAS,MAAM,MAAM;;AACnB,UAAM,SAAS;AAAA,MACb,WAAW;AAAA,MACX,QAAM,UAAK,WAAL,mBAAc,OAAM;AAAA,MAC1B,WAAW,KAAK,SAAS;AAAA,IAC/B;AACI,WAAO,OAAO,IAAI,KAAK;AAAA,EACxB;AAAA;AAAA,EAGD,QAAQ,MAAM,MAAM;AAClB,UAAM,QAAQ;AAAA,MACZ,WAAW,6BAA6B,KAAK,EAAE;AAAA,MAC/C,MAAM,yBAAyB,KAAK,EAAE;AAAA,MACtC,WAAW,8BAA8B,KAAK,EAAE;AAAA,IACtD;AACI,WAAO,MAAM,IAAI,KAAK;AAAA,EACvB;AACH;AASO,SAAS,cAAc,SAAS;AACrC,SAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtCA,kBAAAA,MAAI,MAAM;AAAA,MACR,UAAU;AAAA,MACV,OAAO;AAAA,MACP,MAAM;AAAA,MACN,OAAO,YAAY,SAAS,QAAQ,MAAM,QAAQ,IAAI;AAAA,MACtD,SAAS,YAAY,QAAQ,QAAQ,MAAM,QAAQ,IAAI;AAAA,MACvD,UAAU,YAAY,SAAS,QAAQ,MAAM,QAAQ,IAAI;AAAA,MACzD,MAAM,YAAY,QAAQ,QAAQ,MAAM,QAAQ,IAAI;AAAA,MACpD,SAAS,SAAU,KAAK;AACtB,gBAAQ,GAAG;AAAA,MACZ;AAAA,MACD,MAAM,SAAU,KAAK;AACnB,eAAO,GAAG;AAAA,MACX;AAAA,IACP,CAAK;AAAA,EACL,CAAG;AACH;AASO,SAAS,gBAAgB,SAAS;AACvC,SAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtCA,kBAAAA,MAAI,MAAM;AAAA,MACR,UAAU;AAAA,MACV,OAAO;AAAA,MACP,MAAM;AAAA,MACN,OAAO,YAAY,SAAS,QAAQ,MAAM,QAAQ,IAAI;AAAA,MACtD,SAAS,YAAY,QAAQ,QAAQ,MAAM,QAAQ,IAAI;AAAA,MACvD,UAAU,YAAY,SAAS,QAAQ,MAAM,QAAQ,IAAI;AAAA,MACzD,MAAM,YAAY,QAAQ,QAAQ,MAAM,QAAQ,IAAI;AAAA,MACpD,SAAS,SAAU,KAAK;AACtB,gBAAQ,GAAG;AAAA,MACZ;AAAA,MACD,MAAM,SAAU,KAAK;AACnB,eAAO,GAAG;AAAA,MACX;AAAA,IACP,CAAK;AAAA,EACL,CAAG;AACH;AASO,SAAS,cAAc,SAAS;AACrC,SAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtCA,kBAAAA,MAAI,gBAAgB;AAAA,MAClB,UAAU,CAAC,WAAW,QAAQ;AAAA,MAC9B,SAAS,SAAU,KAAK;AACtB,YAAI,IAAI,aAAa,GAAG;AACtB,wBAAc,OAAO,EAAE,KAAK,OAAO,EAAE,MAAM,MAAM;AAAA,QAC3D,WAAmB,IAAI,aAAa,GAAG;AAC7B,0BAAgB,OAAO,EAAE,KAAK,OAAO,EAAE,MAAM,MAAM;AAAA,QACpD;AAAA,MACF;AAAA,MACD,MAAM,SAAU,KAAK;AACnB,eAAO,GAAG;AAAA,MACX;AAAA,IACP,CAAK;AAAA,EACL,CAAG;AACH;;"}