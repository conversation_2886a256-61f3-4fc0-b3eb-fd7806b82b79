{"version": 3, "file": "shop-detail.js", "sources": ["pages/business/shop-detail.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvYnVzaW5lc3Mvc2hvcC1kZXRhaWwudnVl"], "sourcesContent": ["<template>\n\t<view class=\"shop-detail-page\">\n\t\t<!-- 隐藏的Canvas用于绘制海报 -->\n\t\t<canvas canvas-id=\"posterCanvas\" class=\"poster-canvas\" style=\"width: 600px; height: 900px; position: fixed; top: -9999px; left: -9999px;\"></canvas>\n\t\t\n\t\t<!-- 状态栏占位和导航栏 -->\n\t\t<view class=\"blue-header-container\">\n\t\t\t<view class=\"blue-header\" :style=\"{'padding-top': isIOS ? (safeAreaInsetTop + 'px') : (statusBarHeight + 'px')}\">\n\t\t<view class=\"navbar\">\n\t\t\t<view class=\"navbar-left\" @click=\"goBack\">\n\t\t\t\t<view class=\"back-icon-wrap\">\n\t\t\t\t\t<image src=\"/static/images/tabbar/返回.png\" class=\"back-icon\"></image>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t\t\t<view class=\"navbar-title\">商家详情</view>\n\t\t\t\t\t<view class=\"navbar-right\"></view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t<!-- 添加悬浮转发按钮组件 -->\n\t\t<FabButtons \n\t\t\tpageName=\"shop-detail\" \n\t\t\t:pageInfo=\"{\n\t\t\t\ttitle: shopData.shopName + ' - 商家详情，点击查看更多信息',\n\t\t\t\tpath: '/pages/business/shop-detail?id=' + shopData.id,\n\t\t\t\timageUrl: shopData.images && shopData.images.length > 0 ? shopData.images[0] : ''\n\t\t\t}\" \n\t\t/>\n\t\t\n\t\t<!-- 添加悬浮推广按钮 -->\n\t\t<FloatPromotionButton @click=\"showMerchantPromotion\" />\n\t\t\n\t\t<!-- 悬浮海报按钮 -->\n\t\t<view class=\"float-poster-btn\" @click=\"generateShareImage\">\n\t\t\t<image src=\"/static/images/tabbar/海报.png\" class=\"poster-icon\"></image>\n\t\t\t<text class=\"poster-text\">海报</text>\n\t\t</view>\n\t\t\n\t\t<!-- 顶部图片区域 -->\n\t\t<view class=\"shop-gallery\">\n\t\t\t<swiper class=\"gallery-swiper\" circular :indicator-dots=\"true\" :autoplay=\"true\" :interval=\"4000\" \n\t\t\t\tindicator-color=\"rgba(255,255,255,0.4)\" indicator-active-color=\"#ffffff\">\n\t\t\t\t<swiper-item v-for=\"(img, index) in shopData.images\" :key=\"index\">\n\t\t\t\t\t<image class=\"gallery-image\" :src=\"img\" mode=\"aspectFill\"></image>\n\t\t\t\t</swiper-item>\n\t\t\t</swiper>\n\t\t</view>\n\t\t\n\t\t<!-- 商家基本信息 -->\n\t\t<view class=\"shop-info-card\">\n\t\t\t<view class=\"shop-basic-info\">\n\t\t\t\t<view class=\"shop-logo-container\">\n\t\t\t\t\t<image :src=\"shopData.logo || '/static/images/tabbar/商家入驻.png'\" class=\"shop-logo\"></image>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"shop-title-container\">\n\t\t\t\t\t<text class=\"shop-name\">{{shopData.shopName}}</text>\n\t\t\t\t\t<view class=\"shop-category\">\n\t\t\t\t\t\t<text class=\"category-tag\">{{shopData.category}}</text>\n\t\t\t\t\t\t<text class=\"shop-scale\">{{shopData.scale}}</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"shop-stats\">\n\t\t\t\t\t\t<text class=\"stat-item\">浏览 {{shopData.viewCount}}</text>\n\t\t\t\t\t\t<text class=\"stat-divider\">|</text>\n\t\t\t\t\t\t<text class=\"stat-item\">收藏 {{shopData.favoriteCount}}</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t\n\t\t\t<view class=\"action-buttons\">\n\t\t\t\t<button class=\"action-btn follow-btn\" :class=\"{'following': isFollowing}\" @click=\"toggleFollow\">\n\t\t\t\t\t<view class=\"btn-content\">\n\t\t\t\t\t<image :src=\"isFollowing ? '/static/images/tabbar/已关注.png' : '/static/images/tabbar/关注.png'\" class=\"btn-icon\"></image>\n\t\t\t\t\t\t<text class=\"btn-text\">{{isFollowing ? '已关注' : '关注'}}</text>\n\t\t\t\t\t</view>\n\t\t\t\t</button>\n\t\t\t\t<button class=\"action-btn contact-btn\" @click=\"contactShop\">\n\t\t\t\t\t<view class=\"btn-content\">\n\t\t\t\t\t\t<image src=\"/static/images/tabbar/电话.png\" class=\"btn-icon phone-icon\"></image>\n\t\t\t\t\t\t<text class=\"btn-text\">联系商家</text>\n\t\t\t\t\t</view>\n\t\t\t\t</button>\n\t\t\t\t<!-- 添加推广按钮 -->\n\t\t\t\t<PromotionToolButton \n\t\t\t\t\tv-if=\"hasPromotionPermission\" \n\t\t\t\t\t@click=\"showMerchantPromotion\" \n\t\t\t\t\tbuttonText=\"推广\" \n\t\t\t\t/>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t<!-- 商家信息卡片 -->\n\t\t<view class=\"detail-section\">\n\t\t\t<view class=\"section-header\">\n\t\t\t\t<view class=\"header-left\">\n\t\t\t\t\t<view class=\"section-icon-wrap\">\n\t\t\t\t\t\t<image src=\"/static/images/tabbar/商家信息.png\" class=\"section-icon\" mode=\"aspectFit\"></image>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"section-title-text\">商家信息</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t\n\t\t\t<view class=\"info-list\">\n\t\t\t\t<view class=\"info-item location-item\" @click=\"openLocation\">\n\t\t\t\t\t<view class=\"info-icon-wrap\">\n\t\t\t\t\t\t<image src=\"/static/images/tabbar/商家地址.png\" class=\"info-icon\"></image>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"info-content\">\n\t\t\t\t\t\t<text class=\"info-label\">商家地址</text>\n\t\t\t\t\t\t<text class=\"info-value\">{{shopData.address}}</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"navigate-btn\">\n\t\t\t\t\t\t<text>导航</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<view class=\"info-item\" @click=\"makePhoneCall\">\n\t\t\t\t\t<view class=\"info-icon-wrap\">\n\t\t\t\t\t\t<image src=\"/static/images/tabbar/电话.png\" class=\"info-icon\"></image>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"info-content\">\n\t\t\t\t\t\t<text class=\"info-label\">联系电话</text>\n\t\t\t\t\t\t<text class=\"info-value\">{{shopData.contactPhone}}</text>\n\t\t\t\t\t\t<text class=\"info-tip\">打电话时请说在磁州生活网看到的</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"action-icon-wrap\">\n\t\t\t\t\t\t<image src=\"/static/images/tabbar/箭头.png\" class=\"action-icon\"></image>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<view class=\"info-item\">\n\t\t\t\t\t<view class=\"info-icon-wrap\">\n\t\t\t\t\t\t<image src=\"/static/images/tabbar/营业时间.png\" class=\"info-icon\"></image>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"info-content\">\n\t\t\t\t\t\t<text class=\"info-label\">营业时间</text>\n\t\t\t\t\t\t<text class=\"info-value\">{{shopData.businessTime}}</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t<!-- 举报提示卡片 -->\n\t\t<view class=\"report-tip-section\" @click=\"showReportOptions\">\n\t\t\t<view class=\"report-tip-content\">\n\t\t\t\t<image src=\"/static/images/tabbar/举报.png\" class=\"report-tip-icon\"></image>\n\t\t\t\t<text class=\"report-tip-text\">如遇无效、虚假、诈骗信息，请立即举报</text>\n\t\t\t\t<image src=\"/static/images/tabbar/箭头.png\" class=\"report-arrow-icon\"></image>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t<!-- 商家介绍卡片 -->\n\t\t<view class=\"detail-section\">\n\t\t\t<view class=\"section-header\">\n\t\t\t\t<view class=\"header-left\">\n\t\t\t\t\t<view class=\"section-icon-wrap\">\n\t\t\t\t\t\t<image src=\"/static/images/tabbar/商家介绍.png\" class=\"section-icon\" mode=\"aspectFit\"></image>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"section-title-text\">商家介绍</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t<view class=\"description-content\">\n\t\t\t\t<text class=\"description-text\">{{shopData.description}}</text>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t<!-- 消费满额红包卡片 -->\n\t\t<view class=\"detail-section\" v-if=\"shopData.hasConsumeRedPacket\">\n\t\t\t<view class=\"section-header\">\n\t\t\t\t<view class=\"header-left\">\n\t\t\t\t\t<view class=\"section-icon-wrap\">\n\t\t\t\t\t\t<image src=\"/static/images/red-packet-icon.png\" class=\"section-icon\" mode=\"aspectFit\"></image>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"section-title-text\">消费红包</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"header-right\" v-if=\"shopData.hasConsumeRedPacket\">\n\t\t\t\t\t<text class=\"more-text\">活动中</text>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t<view class=\"consume-red-packet-container\">\n\t\t\t\t<view class=\"consume-activity-card\">\n\t\t\t\t\t<view class=\"activity-header\">\n\t\t\t\t\t\t<image class=\"merchant-logo\" :src=\"shopData.merchantLogo || shopData.logo\" mode=\"aspectFill\"></image>\n\t\t\t\t\t\t<view class=\"activity-info\">\n\t\t\t\t\t\t\t<text class=\"activity-title\">消费满额抽红包</text>\n\t\t\t\t\t\t\t<text class=\"activity-desc\">到店消费满100元，最高可得88元红包</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"activity-details\">\n\t\t\t\t\t\t<text class=\"detail-item\">活动时间：{{formatDate(new Date().getTime() + 30*24*60*60*1000)}}</text>\n\t\t\t\t\t\t<view class=\"button-area\">\n\t\t\t\t\t\t\t<button class=\"draw-button\" @click=\"handleUseRedPacket\">立即抽红包</button>\n\t\t\t\t\t\t\t<text class=\"detail-text\">已有{{Math.floor(Math.random()*200+50)}}人参与</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t<!-- 商家相册 -->\n\t\t<view class=\"detail-section\">\n\t\t\t<view class=\"section-header\">\n\t\t\t\t<view class=\"header-left\">\n\t\t\t\t\t<view class=\"section-icon-wrap\">\n\t\t\t\t\t\t<image src=\"/static/images/tabbar/商家相册.png\" class=\"section-icon\" mode=\"aspectFit\"></image>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"section-title-text\">商家相册</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"view-all\" @click=\"viewAllPhotos\">\n\t\t\t\t\t<text>查看全部</text>\n\t\t\t\t\t<image src=\"/static/images/tabbar/箭头.png\" class=\"arrow-icon\"></image>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t<view class=\"photos-grid\">\n\t\t\t\t<view class=\"photo-item\" v-for=\"(photo, index) in shopData.albumImages.slice(0, 6)\" :key=\"index\" @click=\"previewImage(index)\">\n\t\t\t\t\t<image :src=\"photo\" class=\"photo-image\" mode=\"aspectFill\"></image>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t<!-- 客服微信二维码 -->\n\t\t<view class=\"detail-section\">\n\t\t\t<view class=\"section-header\">\n\t\t\t\t<view class=\"header-left\">\n\t\t\t\t\t<view class=\"section-icon-wrap\">\n\t\t\t\t\t\t<image src=\"/static/images/tabbar/客服微信.png\" class=\"section-icon\" mode=\"aspectFit\"></image>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"section-title-text\">客服微信</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t<view class=\"qrcode-container\">\n\t\t\t\t<image :src=\"shopData.qrcode\" class=\"qrcode-image\" @click=\"previewQRCode\"></image>\n\t\t\t\t<text class=\"qrcode-tip\">点击保存图片，微信扫码添加</text>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t<!-- 抽奖动画弹窗 -->\n\t\t<SimplePopup ref=\"redPacketPopup\" type=\"center\">\n\t\t\t<view class=\"red-packet-popup\">\n\t\t\t\t<view class=\"drawing-area\" v-if=\"isDrawing\">\n\t\t\t\t\t<view class=\"red-packet-animation\">\n\t\t\t\t\t\t<image class=\"red-packet-img\" src=\"/static/images/red-packet-icon.png\" mode=\"aspectFit\"></image>\n\t\t\t\t\t\t<text class=\"drawing-text\">正在抽取红包...</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<view class=\"result-area\" v-if=\"hasDrawn\">\n\t\t\t\t\t<view class=\"result-content\">\n\t\t\t\t\t\t<image class=\"result-icon\" src=\"/static/images/red-packet-icon.png\" mode=\"aspectFit\"></image>\n\t\t\t\t\t\t<text class=\"result-title\">恭喜获得红包</text>\n\t\t\t\t\t\t<text class=\"result-amount\">¥{{drawResult.amount}}</text>\n\t\t\t\t\t\t<text class=\"result-desc\">{{drawResult.message || '红包已发放到您的账户'}}</text>\n\t\t\t\t\t\t<view class=\"result-buttons\">\n\t\t\t\t\t\t\t<button class=\"use-now-btn\" @click=\"useDrawnRedPacket\">立即使用</button>\n\t\t\t\t\t\t\t<button class=\"close-btn\" @click=\"closeRedPacketPopup\">关闭</button>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</SimplePopup>\n\t\t\n\t\t<!-- 底部功能区 -->\n\t\t<view class=\"bottom-toolbar\">\n\t\t\t<view class=\"toolbar-btn\" @click=\"backToHome\">\n\t\t\t\t<image src=\"/static/images/tabbar/a首页.png\" class=\"toolbar-icon\"></image>\n\t\t\t\t\t<text class=\"toolbar-text\">首页</text>\n\t\t\t\t</view>\n\t\t\t<view class=\"toolbar-btn\" @click=\"shareShop\">\n\t\t\t\t<image src=\"/static/images/tabbar/a分享.png\" class=\"toolbar-icon\"></image>\n\t\t\t\t<text class=\"toolbar-text\">分享</text>\n\t\t\t\t</view>\n\t\t\t<view class=\"toolbar-btn message-btn\" @click=\"contactShop\">\n\t\t\t\t<image src=\"/static/images/tabbar/a消息.png\" class=\"toolbar-icon\"></image>\n\t\t\t\t<text class=\"toolbar-text\">私信</text>\n\t\t\t</view>\n\t\t\t<view class=\"toolbar-call-btn\" @click=\"callShop\">\n\t\t\t\t<text class=\"call-text\">打电话</text>\n\t\t\t\t<text class=\"call-subtext\">请说在磁州生活网看到的</text>\n\t\t\t</view>\n\t\t</view>\n\t</view>\n</template>\n\n<script setup>\nimport { ref, reactive } from 'vue';\nimport { onLoad, onShareAppMessage, onReady, onMounted } from '@dcloudio/uni-app';\n\n// 导入FabButtons组件\nimport FabButtons from '@/components/FabButtons.vue';\n// 导入自定义弹窗组件\nimport SimplePopup from '@/components/SimplePopup.vue';\n// 导入推广按钮组件\nimport PromotionToolButton from '@/components/PromotionToolButton.vue';\n// 导入商家推广混入\nimport merchantPromotionMixin from '@/mixins/merchantPromotionMixin';\n\n// 页面数据\nconst shopId = ref(null);\nconst isFollowing = ref(false);\nconst isFavorite = ref(false);\nconst statusBarHeight = ref(20);\nconst navbarHeight = ref(44);\nconst safeAreaInsetTop = ref(0);\nconst isIOS = ref(false);\nconst showPoster = ref(false); // 是否显示海报\nconst posterPath = ref(''); // 海报图片路径\n\n// 弹窗ref\nconst redPacketPopup = ref(null);\n\n// 商家列表数据\nconst shopList = ref([\n\t{\n\t\tid: \"1\",\n\t\tshopName: \"五分利电器\",\n\t\tcategory: \"数码电器\",\n\t\tscale: \"10-20人\",\n\t\taddress: \"河北省邯郸市磁县祥和路\",\n\t\tcontactPhone: \"188-8888-8888\",\n\t\tbusinessTime: \"09:00-21:00\",\n\t\tdescription: '五分利电器是本地知名的电器销售与维修服务商。我们提供各类家用电器，包括冰箱、洗衣机、空调、电视等。全场特价，送货上门，并提供专业安装和维修服务。我们的技术人员经验丰富，能够高效解决各类电器故障问题。\\n\\n本店支持分期付款，以旧换新，并提供长期的售后保障。欢迎新老顾客前来选购！',\n\t\tviewCount: 2345,\n\t\tfavoriteCount: 128,\n\t\tlogo: \"/static/images/tabbar/商家入驻.png\",\n\t\tqrcode: \"/static/images/tabbar/二维码示例.png\",\n\t\tsupportMessage: true,\n\t\timages: [\n\t\t\t\"/static/images/banner/banner-1.png\",\n\t\t\t\"/static/images/banner/banner-2.png\",\n\t\t\t\"/static/images/banner/banner-3.jpg\"\n\t\t],\n\t\talbumImages: [\n\t\t\t\"/static/images/banner/banner-1.png\",\n\t\t\t\"/static/images/banner/banner-2.png\",\n\t\t\t\"/static/images/banner/banner-3.jpg\",\n\t\t\t\"/static/images/banner/banner-1.png\",\n\t\t\t\"/static/images/banner/banner-2.png\",\n\t\t\t\"/static/images/banner/banner-3.jpg\",\n\t\t]\n\t},\n\t// ... 其他商家数据\n]);\n\n// 当前商家数据\nconst shopData = ref({});\n\n// 抽奖相关\nconst isDrawing = ref(false);\nconst hasDrawn = ref(false);\nconst drawResult = ref({\n\tamount: 0,\n\tmessage: ''\n});\n\n// 使用商家推广混入\nconst promotionMixin = merchantPromotionMixin;\nconst hasPromotionPermission = ref(false);\nconst promotionData = ref({});\n\n// 页面加载\nonLoad((options) => {\n\t// 获取商家ID\n\tif (options.id) {\n\t\tshopId.value = options.id;\n\t\t// 根据ID查找商家数据\n\t\tconst shop = shopList.value.find(item => item.id === options.id);\n\tif (shop) {\n\t\tshopData.value = shop;\n\t\t\t// 为演示目的，添加商家推广相关字段\n\t\t\tshopData.value.canDistribute = true; // 允许推广\n\t\t\tshopData.value.ownerId = '123456'; // 假设当前用户ID为123456\n\t\t}\n\t}\n\t\n\t// 获取系统信息\n\tconst systemInfo = uni.getSystemInfoSync();\n\tstatusBarHeight.value = systemInfo.statusBarHeight;\n\tisIOS.value = systemInfo.platform === 'ios';\n\tsafeAreaInsetTop.value = systemInfo.safeAreaInsets ? systemInfo.safeAreaInsets.top : 0;\n\t\n\t// 初始化推广功能\n\tinitPromotion();\n});\n\n// 初始化推广功能\nconst initPromotion = () => {\n\t// 检查推广权限\n\thasPromotionPermission.value = promotionMixin.methods.isContentOwner.call({\n\t\t$store: {\n\t\t\tstate: {\n\t\t\t\tuser: {\n\t\t\t\t\tuserId: '123456' // 假设当前用户ID\n\t\t\t\t}\n\t\t\t}\n\t\t},\n\t\tshopData: shopData.value\n\t}) || promotionMixin.methods.isCommissionContent.call({\n\t\tshopData: shopData.value\n\t});\n\t\n\t// 生成推广数据\n\tif (hasPromotionPermission.value) {\n\t\tpromotionData.value = promotionMixin.methods.generatePromotionData.call({\n\t\t\tshopData: shopData.value,\n\t\t\tpromotionData: {}\n\t\t}).promotionData;\n\t}\n};\n\n// 显示商家推广\nconst showMerchantPromotion = () => {\n\tpromotionMixin.methods.showMerchantPromotion.call({\n\t\thasPromotionPermission: hasPromotionPermission.value,\n\t\topenPromotionTools: () => {\n\t\t\tuni.navigateTo({\n\t\t\t\turl: '/subPackages/promotion/pages/promotion-tool?type=merchant&id=' + shopData.value.id\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t});\n};\n\n// 页面准备完成\nonReady(() => {\n\t// 页面准备完成后的逻辑\n});\n\n// 分享功能\nonShareAppMessage(() => {\n\treturn {\n\t\ttitle: shopData.value.shopName + ' - 磁州生活网推荐商家',\n\t\tpath: '/pages/business/shop-detail?id=' + shopData.value.id,\n\t\timageUrl: shopData.value.images && shopData.value.images.length > 0 ? shopData.value.images[0] : ''\n\t};\n});\n\n// 返回上一页\nconst goBack = () => {\n\tuni.navigateBack();\n};\n\n// 查看全部照片\nconst viewAllPhotos = () => {\n\tuni.previewImage({\n\t\turls: shopData.value.albumImages,\n\t\tcurrent: 0\n\t});\n};\n\n// 预览图片\nconst previewImage = (index) => {\n\t\t\t\tuni.previewImage({\n\t\turls: shopData.value.albumImages,\n\t\tcurrent: index\n\t});\n};\n\n// 预览二维码\nconst previewQRCode = () => {\n\tuni.previewImage({\n\t\turls: [shopData.value.qrcode],\n\t\tcurrent: 0\n\t});\n};\n\n// 打开地图导航\nconst openLocation = () => {\n\t// 这里应该使用真实的经纬度，为了演示使用固定值\n\tuni.openLocation({\n\t\tlatitude: 36.3427,\n\t\tlongitude: 114.7582,\n\t\tname: shopData.value.shopName,\n\t\taddress: shopData.value.address,\n\t\tscale: 18\n\t});\n};\n\n// 拨打电话\nconst makePhoneCall = () => {\n\tuni.makePhoneCall({\n\t\tphoneNumber: shopData.value.contactPhone\n\t});\n};\n\n// 切换关注状态\nconst toggleFollow = () => {\n\tisFollowing.value = !isFollowing.value;\n\tuni.showToast({\n\t\ttitle: isFollowing.value ? '已关注' : '已取消关注',\n\t\ticon: 'none'\n\t});\n};\n\n// 联系商家\nconst contactShop = () => {\n\tif (!shopData.value.supportMessage) {\n\t\tmakePhoneCall();\n\t\treturn;\n\t}\n\t\n\tuni.navigateTo({\n\t\turl: '/pages/message/chat?id=' + shopData.value.id + '&name=' + encodeURIComponent(shopData.value.shopName)\n\t});\n};\n\n// 拨打电话\nconst callShop = () => {\n\tmakePhoneCall();\n};\n\n// 返回首页\nconst backToHome = () => {\n\tuni.switchTab({\n\t\turl: '/pages/index/index'\n\t});\n};\n\n// 分享商家\nconst shareShop = () => {\n\tuni.showShareMenu({\n\t\twithShareTicket: true,\n\t\tmenus: ['shareAppMessage', 'shareTimeline']\n\t});\n};\n\n// 生成分享图片\nconst generateShareImage = () => {\n\tuni.showLoading({\n\t\ttitle: '生成中...'\n\t});\n\t\n\t// 这里应该是真实的海报生成逻辑\n\t// 为了演示，使用setTimeout模拟\n\tsetTimeout(() => {\n\t\tuni.hideLoading();\n\t\tuni.showToast({\n\t\t\ttitle: '海报生成成功',\n\t\t\ticon: 'success'\n\t\t});\n\t\t\n\t\t// 模拟保存图片\n\t\tuni.saveImageToPhotosAlbum({\n\t\t\tfilePath: shopData.value.images[0],\n\t\t\tsuccess: () => {\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '已保存到相册',\n\t\t\t\t\ticon: 'success'\n\t\t\t\t});\n\t\t\t},\n\t\t\tfail: () => {\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '保存失败',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t}\n\t\t});\n\t}, 1500);\n};\n\n// 显示举报选项\nconst showReportOptions = () => {\n\tuni.showActionSheet({\n\t\titemList: ['虚假信息', '诈骗信息', '违法信息', '侵权信息', '其他问题'],\n\t\tsuccess: (res) => {\n\t\t\tconst selectedType = ['虚假信息', '诈骗信息', '违法信息', '侵权信息', '其他问题'][res.tapIndex];\n\t\t\t\n\t\t\t// 检查登录状态\n\t\t\tconst userInfo = uni.getStorageSync('userInfo');\n\t\t\tif (!userInfo) {\n\t\t\t\tuni.showModal({\n\t\t\t\t\ttitle: '提示',\n\t\t\t\t\tcontent: '举报需要先登录，是否前往登录？',\n\t\t\t\t\tsuccess: (res) => {\n\t\t\t\t\t\tif (res.confirm) {\n\t\t\t\t\t\t\tuni.navigateTo({\n\t\t\t\t\t\t\t\turl: '/pages/login/login?redirect=' + encodeURIComponent('/pages/business/shop-detail?id=' + shopData.value.id)\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t\treturn;\n\t\t\t}\n\t\t\t\n\t\t\tif (selectedType === '其他问题') {\n\t\t\t\tshowReportInputDialog();\n\t\t\t} else {\n\t\t\t\tsubmitReport(selectedType);\n\t\t\t}\n\t\t}\n\t});\n};\n\nconst showReportInputDialog = () => {\n\tuni.showModal({\n\t\ttitle: '请描述问题',\n\t\tplaceholderText: '请详细描述您遇到的问题',\n\t\teditable: true,\n\t\tsuccess: (res) => {\n\t\t\tif (res.confirm && res.content) {\n\t\t\t\tsubmitReport('其他问题: ' + res.content);\n\t\t\t}\n\t\t}\n\t});\n};\n\nconst submitReport = (reportReason) => {\n\tuni.showLoading({\n\t\ttitle: '提交中...'\n\t});\n\t\n\tsetTimeout(() => {\n\t\tuni.hideLoading();\n\t\tuni.showToast({\n\t\t\ttitle: '举报成功',\n\t\t\ticon: 'success'\n\t\t});\n\t}, 1000);\n};\n\nconst handleUseRedPacket = () => {\n\tconst userInfo = uni.getStorageSync('userInfo');\n\tif (!userInfo) {\n\t\tuni.showToast({\n\t\t\ttitle: '请先登录',\n\t\t\ticon: 'none'\n\t\t});\n\t\t\n\t\tsetTimeout(() => {\n\t\t\tuni.navigateTo({\n\t\t\t\turl: '/pages/login/login?redirect=' + encodeURIComponent('/pages/business/shop-detail?id=' + shopData.value.id)\n\t\t\t});\n\t\t}, 1500);\n\t\treturn;\n\t}\n\t\n\tisDrawing.value = true;\n\thasDrawn.value = false;\n\tredPacketPopup.value.open();\n\t\n\tsetTimeout(() => {\n\t\ttry {\n\t\t\tconst amount = (Math.random() * 87 + 1).toFixed(2);\n\t\t\t\n\t\t\tdrawResult.value = {\n\t\t\t\tamount: amount,\n\t\t\t\tmessage: amount >= 20 ? '恭喜您，手气不错！' : '谢谢参与，下次再来！'\n\t\t\t};\n\t\t} catch (error) {\n\t\t\tconsole.error('抽红包失败', error);\n\t\t\tdrawResult.value = {\n\t\t\t\tamount: 0,\n\t\t\t\tmessage: '网络异常，请重试'\n\t\t\t};\n\t\t} finally {\n\t\t\tisDrawing.value = false;\n\t\t\thasDrawn.value = true;\n\t\t}\n\t}, 2000);\n};\n\nconst useDrawnRedPacket = () => {\n\tuni.showToast({\n\t\ttitle: `红包已添加到您的账户`,\n\t\ticon: 'success'\n\t});\n\tcloseRedPacketPopup();\n};\n\nconst closeRedPacketPopup = () => {\n\tredPacketPopup.value.close();\n\tsetTimeout(() => {\n\t\tisDrawing.value = false;\n\t\thasDrawn.value = false;\n\t}, 300);\n};\n\nconst formatDate = (timestamp) => {\n\tconst date = new Date(timestamp);\n\tconst year = date.getFullYear();\n\tconst month = (date.getMonth() + 1).toString().padStart(2, '0');\n\tconst day = date.getDate().toString().padStart(2, '0');\n\treturn `${year}-${month}-${day}`;\n};\n</script>\n\n<style lang=\"scss\">\n/* 覆盖整个顶部区域的蓝色容器 */\n.blue-header-container {\n\tposition: fixed;\n\ttop: 0;\n\tleft: 0;\n\tright: 0;\n\twidth: 100%;\n\tz-index: 100;\n\tpointer-events: auto;\n}\n\n/* 蓝色头部区域 */\n.blue-header {\n\tposition: absolute;\n\ttop: 0;\n\tleft: 0;\n\tright: 0;\n\tz-index: 10;\n\tbackground-color: #3846cd;\n\twidth: 100%;\n\t/* 这里的padding-top会被动态设置 */\n\tpointer-events: auto;\n}\n\n.safe-area-inset-top {\n\tdisplay: none; /* 不再需要这个元素 */\n}\n\n/* 页面整体样式 */\n.shop-detail-page {\n\tmin-height: 100vh;\n\twidth: 100vw;\n\tbackground-color: #f7f8fc;\n\tposition: relative;\n\tpadding-bottom: 120rpx;\n\toverflow-x: hidden;\n\tfont-family: -apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, Helvetica, Arial, sans-serif;\n}\n\n/* 系统标题栏 */\n.navbar {\n\theight: 90rpx;\n\tdisplay: flex;\n\talign-items: center;\n\tpadding: 0 30rpx 0 20rpx;\n\twidth: 100%;\n\tbox-sizing: border-box;\n}\n\n.navbar-left, .navbar-right {\n\twidth: 80rpx;\n\theight: 80rpx;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n}\n\n.back-icon-wrap {\n\twidth: 72rpx;\n\theight: 72rpx;\n\tborder-radius: 50%;\n\tbackground-color: transparent;\n\tbackdrop-filter: none;\n\t-webkit-backdrop-filter: none;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\ttransition: all 0.2s ease;\n}\n\n.back-icon-wrap:active {\n\ttransform: scale(0.92);\n\tbackground-color: transparent;\n}\n\n.back-icon {\n\twidth: 32rpx;\n\theight: 32rpx;\n\topacity: 1;\n\tfilter: brightness(0) invert(1);\n}\n\n.navbar-title {\n\tflex: 1;\n\ttext-align: center;\n\tfont-size: 34rpx;\n\tfont-weight: 600;\n\tcolor: #ffffff;\n\ttext-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.1);\n\tfont-family: -apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, Helvetica, Arial, sans-serif;\n}\n\n/* 顶部图片轮播 */\n.shop-gallery {\n\tposition: relative;\n\twidth: 100%;\n\theight: 520rpx;\n\tmargin-top: calc(var(--status-bar-height, 44px) + 90rpx);\n\tborder-radius: 0 0 40rpx 40rpx;\n\toverflow: hidden;\n\tbox-shadow: 0 12rpx 30rpx rgba(0, 0, 0, 0.08);\n}\n\n.gallery-swiper {\n\twidth: 100%;\n\theight: 100%;\n}\n\n.gallery-image {\n\twidth: 100%;\n\theight: 100%;\n\tobject-fit: cover;\n}\n\n/* 商家基本信息卡片 */\n.shop-info-card {\n\tmargin: -70rpx 30rpx 30rpx;\n\tbackground-color: rgba(255, 255, 255, 0.95);\n\tborder-radius: 32rpx;\n\tpadding: 36rpx;\n\tbox-shadow: 0 20rpx 40rpx rgba(0, 0, 0, 0.08);\n\tposition: relative;\n\tz-index: 2;\n\tbackdrop-filter: blur(20px);\n\t-webkit-backdrop-filter: blur(20px);\n\tborder: 1rpx solid rgba(255, 255, 255, 0.6);\n}\n\n.shop-basic-info {\n\tdisplay: flex;\n\talign-items: center;\n\tmargin-bottom: 30rpx;\n}\n\n.shop-logo-container {\n\twidth: 130rpx;\n\theight: 130rpx;\n\tborder-radius: 24rpx;\n\tbackground-color: #f5f7fa;\n\toverflow: hidden;\n\tmargin-right: 24rpx;\n\tbox-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.12);\n\tborder: 2rpx solid rgba(255, 255, 255, 0.8);\n\tposition: relative;\n}\n\n.shop-logo-container::after {\n\tcontent: '';\n\tposition: absolute;\n\ttop: 0;\n\tleft: 0;\n\tright: 0;\n\tbottom: 0;\n\tborder-radius: 24rpx;\n\tbox-shadow: inset 0 0 0 1rpx rgba(255, 255, 255, 0.4);\n\tpointer-events: none;\n}\n\n.shop-logo {\n\twidth: 100%;\n\theight: 100%;\n\tobject-fit: cover;\n}\n\n.shop-title-container {\n\tflex: 1;\n}\n\n.shop-name {\n\tfont-size: 40rpx;\n\tfont-weight: 600;\n\tcolor: #222;\n\tmargin-bottom: 16rpx;\n\tdisplay: block;\n\tletter-spacing: -0.5rpx;\n}\n\n.shop-category {\n\tdisplay: flex;\n\talign-items: center;\n\tmargin-bottom: 16rpx;\n}\n\n.category-tag {\n\tfont-size: 24rpx;\n\tcolor: #2738C0;\n\tbackground-color: rgba(39, 56, 192, 0.08);\n\tpadding: 6rpx 20rpx;\n\tborder-radius: 20rpx;\n\tmargin-right: 16rpx;\n\tfont-weight: 500;\n}\n\n.shop-scale {\n\tfont-size: 24rpx;\n\tcolor: #666;\n\tbackground-color: #f0f2f5;\n\tpadding: 6rpx 20rpx;\n\tborder-radius: 20rpx;\n}\n\n.shop-stats {\n\tdisplay: flex;\n\talign-items: center;\n}\n\n.stat-item {\n\tfont-size: 24rpx;\n\tcolor: #888;\n}\n\n.stat-divider {\n\tmargin: 0 16rpx;\n\tcolor: #ddd;\n}\n\n/* 操作按钮 */\n.action-buttons {\n\tdisplay: flex;\n\tjustify-content: space-between;\n\tmargin-top: 36rpx;\n}\n\n.action-btn {\n\tflex: 1;\n\theight: 70rpx;\n\tline-height: 70rpx;\n\tborder-radius: 35rpx;\n\ttext-align: center;\n\tmargin: 0 10rpx;\n\tfont-size: 28rpx;\n\tcolor: #FFFFFF;\n\tbackground: linear-gradient(to right, #007AFF, #5AC8FA);\n\tbox-shadow: 0 4rpx 12rpx rgba(0, 122, 255, 0.2);\n}\n\n.follow-btn {\n\tbackground: linear-gradient(to right, #007AFF, #5AC8FA);\n\tbox-shadow: 0 4rpx 12rpx rgba(0, 122, 255, 0.2);\n}\n\n.following {\n\tbackground: linear-gradient(to right, #8E8E93, #C7C7CC);\n\tbox-shadow: 0 4rpx 12rpx rgba(142, 142, 147, 0.2);\n}\n\n.contact-btn {\n\tbackground: linear-gradient(to right, #007AFF, #5AC8FA);\n\tbox-shadow: 0 4rpx 12rpx rgba(0, 122, 255, 0.2);\n}\n\n.btn-content {\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\twidth: 100%;\n\theight: 100%;\n\tposition: relative;\n}\n\n.btn-icon {\n\twidth: 28rpx;\n\theight: 28rpx;\n\tmargin-right: 8rpx;\n\tflex-shrink: 0;\n}\n\n.phone-icon {\n\tfilter: brightness(0) invert(1);\n}\n\n.btn-text {\n\tcolor: inherit;\n\tfont-family: 'AlimamaShuHeiTi', sans-serif;\n\tfont-size: 28rpx;\n\tline-height: 28rpx;\n\tpadding-top: 2rpx;\n}\n\n/* 详情卡片通用样式 */\n.detail-section {\n\tmargin: 30rpx 30rpx 0;\n\tbackground-color: rgba(255, 255, 255, 0.95);\n\tborder-radius: 32rpx;\n\tpadding: 36rpx;\n\tbox-shadow: 0 15rpx 35rpx rgba(0, 0, 0, 0.06);\n\tbackdrop-filter: blur(20px);\n\t-webkit-backdrop-filter: blur(20px);\n\tborder: 1rpx solid rgba(255, 255, 255, 0.6);\n\ttransition: all 0.3s ease;\n}\n\n.section-header {\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: space-between;\n\tmargin-bottom: 24rpx;\n}\n\n.header-left {\n\tdisplay: flex;\n\talign-items: center;\n}\n\n.section-icon-wrap {\n\twidth: 56rpx;\n\theight: 56rpx;\n\tborder-radius: 16rpx;\n\tbackground-color: rgba(39, 56, 192, 0.08);\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\tmargin-right: 16rpx;\n}\n\n.section-icon {\n\twidth: 32rpx;\n\theight: 32rpx;\n\tdisplay: block;\n}\n\n.section-title {\n\tdisplay: none !important;\n}\n\n.section-title-text {\n\tfont-size: 34rpx;\n\tfont-weight: 600;\n\tcolor: #222;\n\tletter-spacing: -0.5rpx;\n\tline-height: 1.2;\n\tpadding: 0 4rpx;\n\tdisplay: inline-block;\n\tfont-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;\n}\n\n.view-all {\n\tdisplay: flex;\n\talign-items: center;\n\tfont-size: 26rpx;\n\tcolor: #2738C0;\n\tbackground-color: rgba(39, 56, 192, 0.08);\n\tpadding: 8rpx 20rpx;\n\tborder-radius: 28rpx;\n}\n\n.arrow-icon {\n\twidth: 24rpx;\n\theight: 24rpx;\n\tmargin-left: 8rpx;\n}\n\n/* 信息列表 */\n.info-list {\n\tbackground-color: rgba(249, 250, 252, 0.7);\n\tborder-radius: 28rpx;\n\toverflow: hidden;\n\tbackdrop-filter: blur(10px);\n\t-webkit-backdrop-filter: blur(10px);\n\tborder: 1rpx solid rgba(255, 255, 255, 0.3);\n}\n\n.info-item {\n\tdisplay: flex;\n\talign-items: center;\n\tpadding: 28rpx;\n\tborder-bottom: 1rpx solid rgba(0, 0, 0, 0.04);\n}\n\n.info-item:last-child {\n\tborder-bottom: none;\n}\n\n.info-icon-wrap {\n\twidth: 68rpx;\n\theight: 68rpx;\n\tborder-radius: 20rpx;\n\tbackground-color: rgba(39, 56, 192, 0.08);\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\tmargin-right: 24rpx;\n}\n\n.info-icon {\n\twidth: 36rpx;\n\theight: 36rpx;\n}\n\n.info-content {\n\tflex: 1;\n\tdisplay: flex;\n\tflex-direction: column;\n}\n\n.info-label {\n\tfont-size: 26rpx;\n\tcolor: #888;\n\tmargin-bottom: 8rpx;\n}\n\n.info-value {\n\tfont-size: 30rpx;\n\tcolor: #222;\n\tfont-weight: 500;\n}\n\n.navigate-btn {\n\tpadding: 6rpx 24rpx;\n\tbackground-color: rgba(39, 56, 192, 0.08);\n\tborder-radius: 30rpx;\n\tfont-size: 26rpx;\n\tcolor: #2738C0;\n\tmargin-left: 20rpx;\n\tfont-weight: 500;\n}\n\n.action-icon-wrap {\n\tmargin-left: 20rpx;\n}\n\n.action-icon {\n\twidth: 32rpx;\n\theight: 32rpx;\n\topacity: 0.3;\n}\n\n.location-item {\n\talign-items: flex-start;\n}\n\n/* 商家介绍 */\n.description-content {\n\tbackground-color: rgba(249, 250, 252, 0.7);\n\tborder-radius: 28rpx;\n\tpadding: 28rpx;\n\tbackdrop-filter: blur(10px);\n\t-webkit-backdrop-filter: blur(10px);\n\tborder: 1rpx solid rgba(255, 255, 255, 0.3);\n}\n\n.description-text {\n\tfont-size: 30rpx;\n\tcolor: #444;\n\tline-height: 1.7;\n\twhite-space: pre-wrap;\n\tfont-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;\n}\n\n/* 商家相册 */\n.photos-grid {\n\tdisplay: flex;\n\tflex-wrap: wrap;\n\tmargin: 0 -8rpx;\n}\n\n.photo-item {\n\twidth: calc(33.33% - 16rpx);\n\taspect-ratio: 1;\n\tmargin: 8rpx;\n\tborder-radius: 24rpx;\n\toverflow: hidden;\n\tbackground-color: #f5f7fa;\n\tbox-shadow: 0 8rpx 15rpx rgba(0, 0, 0, 0.05);\n\tposition: relative;\n\ttransform: translateZ(0);\n\ttransition: transform 0.2s ease;\n}\n\n.photo-item:active {\n\ttransform: scale(0.97);\n}\n\n.photo-image {\n\twidth: 100%;\n\theight: 100%;\n\tobject-fit: cover;\n}\n\n/* 二维码 */\n.qrcode-container {\n\tdisplay: flex;\n\tflex-direction: column;\n\talign-items: center;\n\tpadding: 30rpx 0;\n}\n\n.qrcode-image {\n\twidth: 320rpx;\n\theight: 320rpx;\n\tmargin-bottom: 24rpx;\n\tbox-shadow: 0 15rpx 30rpx rgba(0, 0, 0, 0.1);\n\tborder-radius: 28rpx;\n\tbackground-color: white;\n\tpadding: 20rpx;\n}\n\n.qrcode-tip {\n\tfont-size: 26rpx;\n\tcolor: #888;\n\tbackground-color: rgba(39, 56, 192, 0.08);\n\tpadding: 12rpx 30rpx;\n\tborder-radius: 30rpx;\n}\n\n/* 底部工具栏 */\n.bottom-toolbar {\n\tposition: fixed;\n\tleft: 0;\n\tright: 0;\n\tbottom: 0;\n\theight: 110rpx;\n\tbackground-color: rgba(255, 255, 255, 0.95);\n\tbox-shadow: 0 -5rpx 20rpx rgba(0, 0, 0, 0.05);\n\tdisplay: flex;\n\talign-items: center;\n\tpadding: 0;\n\tz-index: 100;\n\tborder-top: 1rpx solid rgba(0, 0, 0, 0.03);\n\tbackdrop-filter: blur(20px);\n\t-webkit-backdrop-filter: blur(20px);\n}\n\n.toolbar-btn {\n\tdisplay: flex;\n\tflex-direction: column;\n\talign-items: center;\n\tjustify-content: center;\n\theight: 100%;\n\tflex: 1;\n\tposition: relative;\n\ttransition: opacity 0.2s ease;\n}\n\n.toolbar-btn:active {\n\topacity: 0.7;\n}\n\n.toolbar-call-btn {\n\tdisplay: flex;\n\tflex-direction: column;\n\talign-items: center;\n\tjustify-content: center;\n\theight: 85%;\n\tflex: 2.5;\n\tbackground-color: #2738C0;\n\tborder-radius: 36rpx;\n\tmargin: 12rpx 15rpx;\n\tbox-shadow: 0 6rpx 15rpx rgba(39, 56, 192, 0.2);\n\ttransition: transform 0.2s ease, box-shadow 0.2s ease;\n}\n\n.toolbar-call-btn:active {\n\ttransform: scale(0.97);\n\tbox-shadow: 0 4rpx 10rpx rgba(39, 56, 192, 0.2);\n}\n\n.call-text {\n\tfont-size: 28rpx;\n\tcolor: #ffffff;\n\tfont-weight: 600;\n}\n\n.call-subtext {\n\tfont-size: 18rpx;\n\tcolor: rgba(255, 255, 255, 0.85);\n\tmargin-top: 2rpx;\n\twhite-space: nowrap;\n}\n\n.toolbar-icon {\n\twidth: 44rpx;\n\theight: 44rpx;\n\tmargin-bottom: 6rpx;\n}\n\n.toolbar-text {\n\tfont-size: 22rpx;\n\tcolor: #444;\n}\n\n/* 添加联系电话下方的提示样式 */\n.info-tip {\n\tfont-size: 22rpx;\n\tcolor: #3846cd;\n\tmargin-top: 6rpx;\n}\n\n/* 悬浮海报按钮 */\n.float-poster-btn {\n\tposition: fixed;\n\tright: 30rpx;\n\tbottom: 200rpx;\n\twidth: 100rpx;\n\theight: 100rpx;\n\tbackground: rgba(240, 240, 240, 0.9);\n\tborder-radius: 50%;\n\tdisplay: flex;\n\tflex-direction: column;\n\talign-items: center;\n\tjustify-content: center;\n\tbox-shadow: 0 6rpx 20rpx rgba(0,0,0,0.08);\n\tz-index: 90;\n\tbackdrop-filter: blur(10px);\n\t-webkit-backdrop-filter: blur(10px);\n\tborder: 1rpx solid rgba(230, 230, 230, 0.6);\n\ttransition: all 0.2s ease;\n}\n\n.float-poster-btn:active {\n\ttransform: scale(0.95);\n\tbox-shadow: 0 3rpx 10rpx rgba(0,0,0,0.08);\n}\n\n.poster-icon {\n\twidth: 40rpx;\n\theight: 40rpx;\n\tmargin-bottom: 4rpx;\n}\n\n.poster-text {\n\tfont-size: 20rpx;\n\tcolor: #444;\n\tline-height: 1;\n}\n\n/* 添加举报图标样式 */\n.report-icon-wrap {\n\twidth: 72rpx;\n\theight: 72rpx;\n\tborder-radius: 50%;\n\tbackground-color: rgba(255, 255, 255, 0.15);\n\tbackdrop-filter: blur(10px);\n\t-webkit-backdrop-filter: blur(10px);\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\ttransition: all 0.2s ease;\n}\n\n.report-icon-wrap:active {\n\ttransform: scale(0.92);\n\tbackground-color: rgba(255, 255, 255, 0.25);\n}\n\n.report-icon {\n\twidth: 36rpx;\n\theight: 36rpx;\n\topacity: 1;\n\tfilter: brightness(0) invert(1);\n}\n\n/* 举报提示卡片样式 */\n.report-tip-section {\n\tmargin: 30rpx 30rpx 0;\n\tbackground-color: #f9fafe;\n\tborder-radius: 32rpx;\n\tpadding: 20rpx 24rpx;\n\tborder: 1rpx solid rgba(0, 0, 0, 0.05);\n}\n\n.report-tip-content {\n\tdisplay: flex;\n\talign-items: center;\n}\n\n.report-tip-icon {\n\twidth: 28rpx;\n\theight: 28rpx;\n\tmargin-right: 12rpx;\n}\n\n.report-tip-text {\n\tflex: 1;\n\tfont-size: 24rpx;\n\tcolor: #666;\n\tfont-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;\n}\n\n.report-arrow-icon {\n\twidth: 24rpx;\n\theight: 24rpx;\n\topacity: 0.3;\n}\n\n/* 确保所有文本在真机上正确显示的全局设置 */\ntext, view {\n\tfont-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;\n}\n\n/* 消费红包相关样式 */\n.consume-red-packet-container {\n\tpadding: 20rpx;\n}\n\n.consume-activity-card {\n\tbackground: linear-gradient(135deg, #fff5f5, #fff1f0);\n\tborder-radius: 28rpx;\n\tpadding: 30rpx;\n\tbox-shadow: 0 4rpx 12rpx rgba(255, 69, 58, 0.1);\n\tborder: 1px solid rgba(255, 69, 58, 0.15);\n}\n\n.activity-header {\n\tdisplay: flex;\n\talign-items: center;\n\tmargin-bottom: 20rpx;\n}\n\n.merchant-logo {\n\twidth: 80rpx;\n\theight: 80rpx;\n\tborder-radius: 12rpx;\n\tmargin-right: 20rpx;\n\tborder: 1px solid rgba(255, 255, 255, 0.6);\n}\n\n.activity-info {\n\tflex: 1;\n}\n\n.activity-title {\n\tfont-size: 32rpx;\n\tfont-weight: bold;\n\tcolor: #ff4538;\n\tmargin-bottom: 10rpx;\n\tdisplay: block;\n}\n\n.activity-desc {\n\tfont-size: 24rpx;\n\tcolor: #666;\n\tdisplay: block;\n}\n\n.activity-details {\n\tmargin-top: 20rpx;\n}\n\n.detail-item {\n\tfont-size: 24rpx;\n\tcolor: #666;\n\tmargin-bottom: 20rpx;\n\tdisplay: block;\n}\n\n.button-area {\n\tdisplay: flex;\n\tjustify-content: space-between;\n\talign-items: center;\n\tmargin-top: 20rpx;\n}\n\n.draw-button {\n\tbackground: linear-gradient(135deg, #ff4538, #ff7b6c);\n\tcolor: #fff;\n\tfont-size: 28rpx;\n\tpadding: 10rpx 40rpx;\n\tborder-radius: 30rpx;\n\tborder: none;\n\tbox-shadow: 0 4rpx 8rpx rgba(255, 69, 58, 0.3);\n}\n\n.detail-text {\n\tfont-size: 24rpx;\n\tcolor: #999;\n}\n\n.more-text {\n\tfont-size: 24rpx;\n\tcolor: #ff4538;\n\tpadding: 4rpx 12rpx;\n\tbackground: rgba(255, 69, 58, 0.1);\n\tborder-radius: 20rpx;\n}\n\n/* 红包弹窗样式 */\n.red-packet-popup {\n\tbackground-color: transparent;\n\twidth: 600rpx;\n}\n\n.drawing-area {\n\tbackground: rgba(0, 0, 0, 0.6);\n\tborder-radius: 20rpx;\n\tpadding: 60rpx 40rpx;\n\tdisplay: flex;\n\tflex-direction: column;\n\talign-items: center;\n\tjustify-content: center;\n}\n\n.red-packet-animation {\n\ttext-align: center;\n}\n\n.red-packet-img {\n\twidth: 200rpx;\n\theight: 200rpx;\n\tmargin-bottom: 30rpx;\n}\n\n.drawing-text {\n\tfont-size: 32rpx;\n\tcolor: #fff;\n\tmargin-top: 20rpx;\n}\n\n.result-area {\n\tbackground: linear-gradient(135deg, #FA2A2D, #FF4E4E);\n\tborder-radius: 20rpx;\n\tpadding: 40rpx;\n\tbox-shadow: 0 8rpx 16rpx rgba(255, 0, 0, 0.2);\n}\n\n.result-content {\n\ttext-align: center;\n}\n\n.result-icon {\n\twidth: 160rpx;\n\theight: 160rpx;\n\tmargin-bottom: 20rpx;\n}\n\n.result-title {\n\tfont-size: 36rpx;\n\tcolor: #FFE4B5;\n\tmargin-bottom: 20rpx;\n\tdisplay: block;\n}\n\n.result-amount {\n\tfont-size: 80rpx;\n\tfont-weight: bold;\n\tcolor: #FFFFFF;\n\tmargin: 20rpx 0;\n\tdisplay: block;\n\ttext-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);\n}\n\n.result-desc {\n\tfont-size: 28rpx;\n\tcolor: #FFE4B5;\n\tmargin-bottom: 40rpx;\n\tdisplay: block;\n}\n\n.result-buttons {\n\tdisplay: flex;\n\tjustify-content: space-between;\n\tmargin-top: 30rpx;\n}\n\n.use-now-btn, .close-btn {\n\tflex: 1;\n\theight: 80rpx;\n\tline-height: 80rpx;\n\tborder-radius: 40rpx;\n\tfont-size: 28rpx;\n\tmargin: 0 10rpx;\n}\n\n.use-now-btn {\n\tbackground-color: #FFFFFF;\n\tcolor: #FA2A2D;\n\tborder: none;\n}\n\n.close-btn {\n\tbackground-color: rgba(255, 255, 255, 0.2);\n\tcolor: #FFFFFF;\n\tborder: 1rpx solid rgba(255, 255, 255, 0.5);\n}\n</style>", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/pages/business/shop-detail.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "merchantPromotionMixin", "onLoad", "uni", "onReady", "onShareAppMessage", "res", "MiniProgramPage"], "mappings": ";;;;;;;;;;;AAgSA,MAAA,aAAA,MAAA;AAEA,MAAA,cAAA,MAAA;AAEA,MAAA,sBAAA,MAAA;;;;AAKA,UAAA,SAAAA,cAAAA,IAAA,IAAA;AACA,UAAA,cAAAA,cAAAA,IAAA,KAAA;AACAA,kBAAA,IAAA,KAAA;AACA,UAAA,kBAAAA,cAAAA,IAAA,EAAA;AACAA,kBAAA,IAAA,EAAA;AACA,UAAA,mBAAAA,cAAAA,IAAA,CAAA;AACA,UAAA,QAAAA,cAAAA,IAAA,KAAA;AACAA,kBAAA,IAAA,KAAA;AACAA,kBAAA,IAAA,EAAA;AAGA,UAAA,iBAAAA,cAAAA,IAAA,IAAA;AAGA,UAAA,WAAAA,cAAAA,IAAA;AAAA,MACA;AAAA,QACA,IAAA;AAAA,QACA,UAAA;AAAA,QACA,UAAA;AAAA,QACA,OAAA;AAAA,QACA,SAAA;AAAA,QACA,cAAA;AAAA,QACA,cAAA;AAAA,QACA,aAAA;AAAA,QACA,WAAA;AAAA,QACA,eAAA;AAAA,QACA,MAAA;AAAA,QACA,QAAA;AAAA,QACA,gBAAA;AAAA,QACA,QAAA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACA;AAAA,QACA,aAAA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACA;AAAA,MACA;AAAA;AAAA,IAEA,CAAA;AAGA,UAAA,WAAAA,cAAAA,IAAA,CAAA,CAAA;AAGA,UAAA,YAAAA,cAAAA,IAAA,KAAA;AACA,UAAA,WAAAA,cAAAA,IAAA,KAAA;AACA,UAAA,aAAAA,cAAAA,IAAA;AAAA,MACA,QAAA;AAAA,MACA,SAAA;AAAA,IACA,CAAA;AAGA,UAAA,iBAAAC,8BAAAA;AACA,UAAA,yBAAAD,cAAAA,IAAA,KAAA;AACA,UAAA,gBAAAA,cAAAA,IAAA,CAAA,CAAA;AAGAE,kBAAA,OAAA,CAAA,YAAA;AAEA,UAAA,QAAA,IAAA;AACA,eAAA,QAAA,QAAA;AAEA,cAAA,OAAA,SAAA,MAAA,KAAA,UAAA,KAAA,OAAA,QAAA,EAAA;AACA,YAAA,MAAA;AACA,mBAAA,QAAA;AAEA,mBAAA,MAAA,gBAAA;AACA,mBAAA,MAAA,UAAA;AAAA,QACA;AAAA,MACA;AAGA,YAAA,aAAAC,oBAAA;AACA,sBAAA,QAAA,WAAA;AACA,YAAA,QAAA,WAAA,aAAA;AACA,uBAAA,QAAA,WAAA,iBAAA,WAAA,eAAA,MAAA;AAGA;IACA,CAAA;AAGA,UAAA,gBAAA,MAAA;AAEA,6BAAA,QAAA,eAAA,QAAA,eAAA,KAAA;AAAA,QACA,QAAA;AAAA,UACA,OAAA;AAAA,YACA,MAAA;AAAA,cACA,QAAA;AAAA;AAAA,YACA;AAAA,UACA;AAAA,QACA;AAAA,QACA,UAAA,SAAA;AAAA,MACA,CAAA,KAAA,eAAA,QAAA,oBAAA,KAAA;AAAA,QACA,UAAA,SAAA;AAAA,MACA,CAAA;AAGA,UAAA,uBAAA,OAAA;AACA,sBAAA,QAAA,eAAA,QAAA,sBAAA,KAAA;AAAA,UACA,UAAA,SAAA;AAAA,UACA,eAAA,CAAA;AAAA,QACA,CAAA,EAAA;AAAA,MACA;AAAA,IACA;AAGA,UAAA,wBAAA,MAAA;AACA,qBAAA,QAAA,sBAAA,KAAA;AAAA,QACA,wBAAA,uBAAA;AAAA,QACA,oBAAA,MAAA;AACAA,wBAAAA,MAAA,WAAA;AAAA,YACA,KAAA,kEAAA,SAAA,MAAA;AAAA,UACA,CAAA;AAAA,QACA;AAAA,MACA,CAAA;AAAA,IACA;AAGAC,kBAAAA,QAAA,MAAA;AAAA,IAEA,CAAA;AAGAC,kBAAAA,kBAAA,MAAA;AACA,aAAA;AAAA,QACA,OAAA,SAAA,MAAA,WAAA;AAAA,QACA,MAAA,oCAAA,SAAA,MAAA;AAAA,QACA,UAAA,SAAA,MAAA,UAAA,SAAA,MAAA,OAAA,SAAA,IAAA,SAAA,MAAA,OAAA,CAAA,IAAA;AAAA,MACA;AAAA,IACA,CAAA;AAGA,UAAA,SAAA,MAAA;AACAF,oBAAA,MAAA,aAAA;AAAA,IACA;AAGA,UAAA,gBAAA,MAAA;AACAA,oBAAAA,MAAA,aAAA;AAAA,QACA,MAAA,SAAA,MAAA;AAAA,QACA,SAAA;AAAA,MACA,CAAA;AAAA,IACA;AAGA,UAAA,eAAA,CAAA,UAAA;AACAA,oBAAAA,MAAA,aAAA;AAAA,QACA,MAAA,SAAA,MAAA;AAAA,QACA,SAAA;AAAA,MACA,CAAA;AAAA,IACA;AAGA,UAAA,gBAAA,MAAA;AACAA,oBAAAA,MAAA,aAAA;AAAA,QACA,MAAA,CAAA,SAAA,MAAA,MAAA;AAAA,QACA,SAAA;AAAA,MACA,CAAA;AAAA,IACA;AAGA,UAAA,eAAA,MAAA;AAEAA,oBAAAA,MAAA,aAAA;AAAA,QACA,UAAA;AAAA,QACA,WAAA;AAAA,QACA,MAAA,SAAA,MAAA;AAAA,QACA,SAAA,SAAA,MAAA;AAAA,QACA,OAAA;AAAA,MACA,CAAA;AAAA,IACA;AAGA,UAAA,gBAAA,MAAA;AACAA,oBAAAA,MAAA,cAAA;AAAA,QACA,aAAA,SAAA,MAAA;AAAA,MACA,CAAA;AAAA,IACA;AAGA,UAAA,eAAA,MAAA;AACA,kBAAA,QAAA,CAAA,YAAA;AACAA,oBAAAA,MAAA,UAAA;AAAA,QACA,OAAA,YAAA,QAAA,QAAA;AAAA,QACA,MAAA;AAAA,MACA,CAAA;AAAA,IACA;AAGA,UAAA,cAAA,MAAA;AACA,UAAA,CAAA,SAAA,MAAA,gBAAA;AACA;AACA;AAAA,MACA;AAEAA,oBAAAA,MAAA,WAAA;AAAA,QACA,KAAA,4BAAA,SAAA,MAAA,KAAA,WAAA,mBAAA,SAAA,MAAA,QAAA;AAAA,MACA,CAAA;AAAA,IACA;AAGA,UAAA,WAAA,MAAA;AACA;IACA;AAGA,UAAA,aAAA,MAAA;AACAA,oBAAAA,MAAA,UAAA;AAAA,QACA,KAAA;AAAA,MACA,CAAA;AAAA,IACA;AAGA,UAAA,YAAA,MAAA;AACAA,oBAAAA,MAAA,cAAA;AAAA,QACA,iBAAA;AAAA,QACA,OAAA,CAAA,mBAAA,eAAA;AAAA,MACA,CAAA;AAAA,IACA;AAGA,UAAA,qBAAA,MAAA;AACAA,oBAAAA,MAAA,YAAA;AAAA,QACA,OAAA;AAAA,MACA,CAAA;AAIA,iBAAA,MAAA;AACAA,sBAAA,MAAA,YAAA;AACAA,sBAAAA,MAAA,UAAA;AAAA,UACA,OAAA;AAAA,UACA,MAAA;AAAA,QACA,CAAA;AAGAA,sBAAAA,MAAA,uBAAA;AAAA,UACA,UAAA,SAAA,MAAA,OAAA,CAAA;AAAA,UACA,SAAA,MAAA;AACAA,0BAAAA,MAAA,UAAA;AAAA,cACA,OAAA;AAAA,cACA,MAAA;AAAA,YACA,CAAA;AAAA,UACA;AAAA,UACA,MAAA,MAAA;AACAA,0BAAAA,MAAA,UAAA;AAAA,cACA,OAAA;AAAA,cACA,MAAA;AAAA,YACA,CAAA;AAAA,UACA;AAAA,QACA,CAAA;AAAA,MACA,GAAA,IAAA;AAAA,IACA;AAGA,UAAA,oBAAA,MAAA;AACAA,oBAAAA,MAAA,gBAAA;AAAA,QACA,UAAA,CAAA,QAAA,QAAA,QAAA,QAAA,MAAA;AAAA,QACA,SAAA,CAAA,QAAA;AACA,gBAAA,eAAA,CAAA,QAAA,QAAA,QAAA,QAAA,MAAA,EAAA,IAAA,QAAA;AAGA,gBAAA,WAAAA,cAAAA,MAAA,eAAA,UAAA;AACA,cAAA,CAAA,UAAA;AACAA,0BAAAA,MAAA,UAAA;AAAA,cACA,OAAA;AAAA,cACA,SAAA;AAAA,cACA,SAAA,CAAAG,SAAA;AACA,oBAAAA,KAAA,SAAA;AACAH,gCAAAA,MAAA,WAAA;AAAA,oBACA,KAAA,iCAAA,mBAAA,oCAAA,SAAA,MAAA,EAAA;AAAA,kBACA,CAAA;AAAA,gBACA;AAAA,cACA;AAAA,YACA,CAAA;AACA;AAAA,UACA;AAEA,cAAA,iBAAA,QAAA;AACA;UACA,OAAA;AACA,yBAAA;AAAA,UACA;AAAA,QACA;AAAA,MACA,CAAA;AAAA,IACA;AAEA,UAAA,wBAAA,MAAA;AACAA,oBAAAA,MAAA,UAAA;AAAA,QACA,OAAA;AAAA,QACA,iBAAA;AAAA,QACA,UAAA;AAAA,QACA,SAAA,CAAA,QAAA;AACA,cAAA,IAAA,WAAA,IAAA,SAAA;AACA,yBAAA,WAAA,IAAA,OAAA;AAAA,UACA;AAAA,QACA;AAAA,MACA,CAAA;AAAA,IACA;AAEA,UAAA,eAAA,CAAA,iBAAA;AACAA,oBAAAA,MAAA,YAAA;AAAA,QACA,OAAA;AAAA,MACA,CAAA;AAEA,iBAAA,MAAA;AACAA,sBAAA,MAAA,YAAA;AACAA,sBAAAA,MAAA,UAAA;AAAA,UACA,OAAA;AAAA,UACA,MAAA;AAAA,QACA,CAAA;AAAA,MACA,GAAA,GAAA;AAAA,IACA;AAEA,UAAA,qBAAA,MAAA;AACA,YAAA,WAAAA,cAAAA,MAAA,eAAA,UAAA;AACA,UAAA,CAAA,UAAA;AACAA,sBAAAA,MAAA,UAAA;AAAA,UACA,OAAA;AAAA,UACA,MAAA;AAAA,QACA,CAAA;AAEA,mBAAA,MAAA;AACAA,wBAAAA,MAAA,WAAA;AAAA,YACA,KAAA,iCAAA,mBAAA,oCAAA,SAAA,MAAA,EAAA;AAAA,UACA,CAAA;AAAA,QACA,GAAA,IAAA;AACA;AAAA,MACA;AAEA,gBAAA,QAAA;AACA,eAAA,QAAA;AACA,qBAAA,MAAA;AAEA,iBAAA,MAAA;AACA,YAAA;AACA,gBAAA,UAAA,KAAA,OAAA,IAAA,KAAA,GAAA,QAAA,CAAA;AAEA,qBAAA,QAAA;AAAA,YACA;AAAA,YACA,SAAA,UAAA,KAAA,cAAA;AAAA,UACA;AAAA,QACA,SAAA,OAAA;AACAA,wBAAA,MAAA,MAAA,SAAA,yCAAA,SAAA,KAAA;AACA,qBAAA,QAAA;AAAA,YACA,QAAA;AAAA,YACA,SAAA;AAAA,UACA;AAAA,QACA,UAAA;AACA,oBAAA,QAAA;AACA,mBAAA,QAAA;AAAA,QACA;AAAA,MACA,GAAA,GAAA;AAAA,IACA;AAEA,UAAA,oBAAA,MAAA;AACAA,oBAAAA,MAAA,UAAA;AAAA,QACA,OAAA;AAAA,QACA,MAAA;AAAA,MACA,CAAA;AACA;IACA;AAEA,UAAA,sBAAA,MAAA;AACA,qBAAA,MAAA;AACA,iBAAA,MAAA;AACA,kBAAA,QAAA;AACA,iBAAA,QAAA;AAAA,MACA,GAAA,GAAA;AAAA,IACA;AAEA,UAAA,aAAA,CAAA,cAAA;AACA,YAAA,OAAA,IAAA,KAAA,SAAA;AACA,YAAA,OAAA,KAAA;AACA,YAAA,SAAA,KAAA,aAAA,GAAA,SAAA,EAAA,SAAA,GAAA,GAAA;AACA,YAAA,MAAA,KAAA,QAAA,EAAA,SAAA,EAAA,SAAA,GAAA,GAAA;AACA,aAAA,GAAA,IAAA,IAAA,KAAA,IAAA,GAAA;AAAA,IACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACxqBA,GAAG,WAAWI,SAAe;"}