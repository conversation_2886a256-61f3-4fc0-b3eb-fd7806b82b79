{"version": 3, "file": "business.js", "sources": ["pages/business/business.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvYnVzaW5lc3MvYnVzaW5lc3MudnVl"], "sourcesContent": ["<template>\n\t<view class=\"business-page\">\n\t\t<!-- 顶部蓝色背景 -->\n\t\t<view class=\"top-blue-bg\"></view>\n\t\t\n\t\t<!-- 状态栏占位 -->\n\t\t<view class=\"status-bar\" :style=\"{ height: statusBarHeight + 'px' }\"></view>\n\t\t\n\t\t<!-- 系统标题栏 -->\n\t\t<view class=\"navbar\">\n\t\t\t<text class=\"navbar-title\">同城商圈</text>\n\t\t</view>\n\t\t\n\t\t<!-- 轮播图 -->\n\t\t<view class=\"swiper-container\">\n\t\t\t<swiper class=\"banner-swiper\" circular :indicator-dots=\"true\" :autoplay=\"true\" :interval=\"3000\" :duration=\"500\" indicator-color=\"rgba(255,255,255,0.4)\" indicator-active-color=\"#ffffff\">\n\t\t\t\t<swiper-item v-for=\"(banner, index) in bannerList\" :key=\"index\">\n\t\t\t\t\t<image class=\"banner-image\" :src=\"banner.image\" mode=\"aspectFill\"></image>\n\t\t\t\t</swiper-item>\n\t\t\t</swiper>\n\t\t</view>\n\t\t\n\t\t<!-- 曝光量和入驻数 -->\n\t\t<view class=\"stats-bar\">\n\t\t\t<view class=\"stat-item\">\n\t\t\t\t<image class=\"stat-icon\" src=\"/static/images/tabbar/喇叭.png\"></image>\n\t\t\t<text class=\"exposure-text\">曝光量: 123.4万次</text>\n\t\t\t</view>\n\t\t\t<text class=\"stat-divider\">|</text>\n\t\t\t<view class=\"stat-item\">\n\t\t\t<text class=\"in-row\">已入驻: 567家</text>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t<!-- 商家推荐模块 -->\n\t\t<MerchantRecommend />\n\t\t\n\t\t<!-- 商家入驻卡片 -->\n\t\t<view class=\"join-card\" @click=\"navigateToJoin\">\n\t\t\t<view class=\"join-card-left\">\n\t\t\t\t<view class=\"join-icon-wrap\">\n\t\t\t\t\t<image class=\"join-icon\" src=\"/static/images/tabbar/入驻卡片.png\" />\n\t\t\t\t</view>\n\t\t\t\t<view class=\"join-info\">\n\t\t\t\t\t<text class=\"join-title\">商家入驻</text>\n\t\t\t\t\t<text class=\"join-desc\">免费申请入驻，获取更多商机</text>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t<button class=\"join-btn\" @click.stop=\"navigateToJoin\">立即申请</button>\n\t\t\t<view class=\"hot-badge\">热门</view>\n\t\t</view>\n\t\t\n\t\t<!-- 悬浮按钮 -->\n\t\t<FabButtons \n\t\t\tpageName=\"business\" \n\t\t\t:pageInfo=\"{\n\t\t\t\ttitle: '磁州生活网 - 同城商圈',\n\t\t\t\tpath: '/pages/business/business',\n\t\t\t\timageUrl: '/static/images/banner/banner-1.png'\n\t\t\t}\" \n\t\t/>\n\t\t\n\t\t<!-- 分类宫格 -->\n\t\t<view class=\"category-grid\">\n\t\t\t<!-- 搜索框移到分类宫格内部上方 -->\n\t\t\t<view class=\"search-container-inner\">\n\t\t\t\t<view class=\"search-box\">\n\t\t\t\t\t<input class=\"search-input\" type=\"text\" v-model=\"searchKeyword\" placeholder=\"搜索或筛选商家\" confirm-type=\"search\" />\n\t\t\t\t\t<image class=\"search-icon\" src=\"/static/images/tabbar/放大镜.png\"></image>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t\n\t\t\t<view class=\"category-row\">\n\t\t\t\t<view class=\"category-item\" @click=\"navigateToFilter('房产楼盘')\">\n\t\t\t\t\t<image class=\"category-icon\" src=\"/static/images/tabbar/房产楼盘.png\"></image>\n\t\t\t\t\t<text class=\"category-name\">房产楼盘</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"category-item\" @click=\"navigateToFilter('美食小吃')\">\n\t\t\t\t\t<image class=\"category-icon\" src=\"/static/images/tabbar/美食小吃.png\"></image>\n\t\t\t\t\t<text class=\"category-name\">美食小吃</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"category-item\" @click=\"navigateToFilter('装修家居')\">\n\t\t\t\t\t<image class=\"category-icon\" src=\"/static/images/tabbar/装修家居.png\"></image>\n\t\t\t\t\t<text class=\"category-name\">装修家居</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"category-item\" @click=\"navigateToFilter('母婴专区')\">\n\t\t\t\t\t<image class=\"category-icon\" src=\"/static/images/tabbar/母婴专区.png\"></image>\n\t\t\t\t\t<text class=\"category-name\">母婴专区</text>\n\t\t\t\t\t<view class=\"hot-tag\">热门</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t\n\t\t\t<view class=\"category-row\">\n\t\t\t\t<view class=\"category-item\" @click=\"navigateToFilter('休闲娱乐')\">\n\t\t\t\t\t<image class=\"category-icon\" src=\"/static/images/tabbar/休闲娱乐.png\"></image>\n\t\t\t\t\t<text class=\"category-name\">休闲娱乐</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"category-item\" @click=\"navigateToFilter('到家服务')\">\n\t\t\t\t\t<image class=\"category-icon\" src=\"/static/images/tabbar/商到家服务.png\"></image>\n\t\t\t\t\t<text class=\"category-name\">到家服务</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"category-item\" @click=\"navigateToFilter('开锁换锁')\">\n\t\t\t\t\t<image class=\"category-icon\" src=\"/static/images/tabbar/开锁换锁.png\"></image>\n\t\t\t\t\t<text class=\"category-name\">开锁换锁</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"category-item\" @click=\"navigateToFilter('数码通讯')\">\n\t\t\t\t\t<image class=\"category-icon\" src=\"/static/images/tabbar/数码通讯.png\"></image>\n\t\t\t\t\t<text class=\"category-name\">数码通讯</text>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t\n\t\t\t<view class=\"category-row\">\n\t\t\t\t<view class=\"category-item\" @click=\"navigateToFilter('车辆服务')\">\n\t\t\t\t\t<image class=\"category-icon\" src=\"/static/images/tabbar/商车辆服务.png\"></image>\n\t\t\t\t\t<text class=\"category-name\">车辆服务</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"category-item\" @click=\"navigateToFilter('教育培训')\">\n\t\t\t\t\t<image class=\"category-icon\" src=\"/static/images/tabbar/商教育培训.png\"></image>\n\t\t\t\t\t<text class=\"category-name\">教育培训</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"category-item\" @click=\"navigateToFilter('婚纱摄影')\">\n\t\t\t\t\t<image class=\"category-icon\" src=\"/static/images/tabbar/婚纱摄影.png\"></image>\n\t\t\t\t\t<text class=\"category-name\">婚纱摄影</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"category-item\" @click=\"navigateToFilter('农林牧渔')\">\n\t\t\t\t\t<image class=\"category-icon\" src=\"/static/images/tabbar/农林牧渔.png\"></image>\n\t\t\t\t\t<text class=\"category-name\">农林牧渔</text>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t\n\t\t\t<view class=\"category-row\">\n\t\t\t\t<view class=\"category-item\" @click=\"navigateToFilter('广告传媒')\">\n\t\t\t\t\t<image class=\"category-icon\" src=\"/static/images/tabbar/广告传媒.png\"></image>\n\t\t\t\t\t<text class=\"category-name\">广告传媒</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"category-item\" @click=\"navigateToFilter('其他行业')\">\n\t\t\t\t\t<image class=\"category-icon\" src=\"/static/images/tabbar/其他.png\"></image>\n\t\t\t\t\t<text class=\"category-name\">其他行业</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"category-item\"></view>\n\t\t\t\t<view class=\"category-item\"></view>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t<!-- 标签页切换 -->\n\t\t<view class=\"tab-bar\">\n\t\t\t<view class=\"tab-item\" :class=\"{active: currentTab === 0}\" @click=\"changeTab(0)\">\n\t\t\t\t<text class=\"tab-text\">推荐</text>\n\t\t\t\t<view class=\"tab-line\" v-if=\"currentTab === 0\"></view>\n\t\t\t</view>\n\t\t\t<view class=\"tab-item\" :class=\"{active: currentTab === 1}\" @click=\"changeTab(1)\">\n\t\t\t\t<text class=\"tab-text\">新入</text>\n\t\t\t\t<view class=\"tab-line\" v-if=\"currentTab === 1\"></view>\n\t\t\t</view>\n\t\t\t<view class=\"tab-item\" :class=\"{active: currentTab === 2}\" @click=\"changeTab(2)\">\n\t\t\t\t<text class=\"tab-text\">附近</text>\n\t\t\t\t<view class=\"tab-line\" v-if=\"currentTab === 2\"></view>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t<!-- 商家列表 -->\n\t\t<view class=\"business-list\">\n\t\t\t<view class=\"business-item\" v-for=\"business in businessList\" :key=\"business.id\" @click=\"navigateToShopDetail(business.id)\">\n\t\t\t\t<view class=\"business-item-content\">\n\t\t\t\t\t<!-- 商家Logo和基本信息区域 -->\n\t\t\t\t\t<view class=\"business-header\">\n\t\t\t\t\t\t<view class=\"business-logo-container\">\n\t\t\t\t\t\t\t<image class=\"business-logo\" :src=\"business.logo\" mode=\"aspectFill\"></image>\n\t\t\t\t\t\t\t<view class=\"business-status\" v-if=\"business.isOpen\">营业中</view>\n\t\t\t\t\t\t\t<view class=\"business-status closed\" v-else>休息中</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t<view class=\"business-info\">\n\t\t\t\t\t\t\t<view class=\"business-name-row\">\n\t\t\t\t\t<text class=\"business-name\">{{ business.name }}</text>\n\t\t\t\t\t\t\t\t<view class=\"business-verified\" v-if=\"business.isVerified\">\n\t\t\t\t\t\t\t\t\t<image class=\"verified-icon\" src=\"/static/images/tabbar/已认证.png\"></image>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<view class=\"business-rating-row\">\n\t\t\t\t\t\t\t\t<view class=\"rating-stars\">\n\t\t\t\t\t\t\t\t\t<text class=\"rating-score\">{{ business.rating || '4.8' }}</text>\n\t\t\t\t\t\t\t\t\t<view class=\"stars\">\n\t\t\t\t\t\t\t\t\t\t<text class=\"star\" v-for=\"i in 5\" :key=\"i\">★</text>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t<text class=\"business-reviews\">{{ business.reviewCount || '128' }}条评价</text>\n\t\t\t\t\t\t\t\t<text class=\"business-distance\" v-if=\"business.distance\">{{ business.distance }}km</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t<text class=\"business-desc\">{{ business.description }}</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t\n\t\t\t\t\t<!-- 商家标签区域 -->\n\t\t\t\t\t<view class=\"business-tags-row\">\n\t\t\t\t\t\t<scroll-view class=\"tags-scroll-view\" scroll-x show-scrollbar=\"false\">\n\t\t\t\t\t<view class=\"business-tags\">\n\t\t\t\t\t\t\t\t<view class=\"business-tag\" v-if=\"business.category\">\n\t\t\t\t\t\t\t\t\t<text class=\"tag-text\">{{ business.category }}</text>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"business-tag red-packet-tag\" v-if=\"business.hasConsumeRedPacket\">\n\t\t\t\t\t\t\t<image class=\"tag-icon\" src=\"/static/images/red-packet-icon.png\"></image>\n\t\t\t\t\t\t\t<text class=\"tag-text\">消费满100元抽红包</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t<view class=\"business-tag new-tag\" v-if=\"business.isNew\">\n\t\t\t\t\t\t\t\t\t<text class=\"tag-text\">新入驻</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t<view class=\"business-tag hot-business-tag\" v-if=\"business.isHot\">\n\t\t\t\t\t\t\t\t\t<text class=\"tag-text\">热门商家</text>\n\t\t\t\t</view>\n\t\t\t\t\t\t\t\t<view class=\"business-tag discount-tag\" v-if=\"business.hasDiscount\">\n\t\t\t\t\t\t\t\t\t<text class=\"tag-text\">优惠活动</text>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</scroll-view>\n\t\t\t\t\t</view>\n\t\t\t\t\t\n\t\t\t\t\t<!-- 商家特色服务区域 -->\n\t\t\t\t\t<view class=\"business-features\" v-if=\"business.features && business.features.length\">\n\t\t\t\t\t\t<view class=\"feature-item\" v-for=\"(feature, idx) in business.features\" :key=\"idx\">\n\t\t\t\t\t\t\t<image class=\"feature-icon\" :src=\"feature.icon\"></image>\n\t\t\t\t\t\t\t<text class=\"feature-text\">{{ feature.text }}</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t\n\t\t\t\t\t<!-- 商家操作区域 -->\n\t\t\t\t\t<view class=\"business-actions\">\n\t\t\t\t\t\t<view class=\"action-btn call-btn\" @click.stop=\"callBusiness(business.contactPhone)\">\n\t\t\t\t\t\t\t<image class=\"action-icon\" src=\"/static/images/tabbar/电话.png\"></image>\n\t\t\t\t\t\t\t<text class=\"action-text\">电话</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"action-btn nav-btn\" @click.stop=\"navigateToBusiness(business.address, business.name)\">\n\t\t\t\t\t\t\t<image class=\"action-icon\" src=\"/static/images/tabbar/定位.png\"></image>\n\t\t\t\t\t\t\t<text class=\"action-text\">导航</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"action-btn share-btn\" @click.stop=\"shareBusiness(business)\">\n\t\t\t\t\t\t\t<image class=\"action-icon\" src=\"/static/images/tabbar/分享.png\"></image>\n\t\t\t\t\t\t\t<text class=\"action-text\">分享</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<button class=\"follow-btn\" :class=\"{'following': business.isFollowing}\" @click.stop=\"followBusiness(business.id)\">\n\t\t\t\t\t\t\t{{ business.isFollowing ? '已关注' : '+ 关注' }}\n\t\t\t\t\t\t</button>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t</view>\n</template>\n\n<script setup>\nimport { ref, reactive, onMounted, computed } from 'vue';\nimport FabButtons from '@/components/FabButtons.vue';\nimport MerchantRecommend from '@/components/index/MerchantRecommend.vue';\n\n// --- 响应式状态 ---\nconst currentTab = ref(0);\nconst statusBarHeight = ref(20);\nconst searchKeyword = ref('');\nconst locationAuthChecked = ref(false);\n\nconst bannerList = reactive([\n\t\t\t\t\t{ image: '/static/images/banner/banner-1.png' },\n\t\t\t\t\t{ image: '/static/images/banner/banner-2.png' },\n\t\t\t\t\t{ image: '/static/images/banner/banner-3.jpg' }\n]);\n\nconst categories = reactive([\n\t\t\t\t\t{ name: '房产楼盘', icon: '/static/images/tabbar/房产楼盘.png' },\n\t\t\t\t\t{ name: '美食小吃', icon: '/static/images/tabbar/美食小吃.png' },\n\t\t\t\t\t{ name: '装修家居', icon: '/static/images/tabbar/装修家居.png' },\n\t\t\t\t\t{ name: '母婴专区', icon: '/static/images/tabbar/母婴专区.png', hot: true },\n\t\t\t\t\t{ name: '休闲娱乐', icon: '/static/images/tabbar/休闲娱乐.png' },\n\t\t\t\t\t{ name: '商到家服务', icon: '/static/images/tabbar/商到家服务.png' },\n\t\t\t\t\t{ name: '开锁换锁', icon: '/static/images/tabbar/开锁换锁.png' },\n\t\t\t\t\t{ name: '数码通讯', icon: '/static/images/tabbar/数码通讯.png' },\n\t\t\t\t\t{ name: '商车辆服务', icon: '/static/images/tabbar/商车辆服务.png' },\n\t\t\t\t\t{ name: '教育培训', icon: '/static/images/tabbar/商教育培训.png' },\n\t\t\t\t\t{ name: '婚纱摄影', icon: '/static/images/tabbar/婚纱摄影.png' },\n\t\t\t\t\t{ name: '农林牧渔', icon: '/static/images/tabbar/农林牧渔.png' },\n\t\t\t\t\t{ name: '广告传媒', icon: '/static/images/tabbar/广告传媒.png' },\n\t\t\t\t\t{ name: '其他行业', icon: '/static/images/tabbar/其他.png' }\n]);\n\nconst allBusinessList = reactive([\n\t\t\t\t\t{\n\t\t\t\t\t\tid: \"1\",\n\t\t\t\t\t\tlogo: '/static/images/cizhou.png',\n\t\t\t\t\t\tname: '五分利电器',\n\t\t\t\t\t\tdescription: '家电全网调货，全场特价，送货上门',\n\t\t\t\t\t\tcategory: '数码电器',\n\t\t\t\t\t\tscale: '10-20人',\n\t\thasConsumeRedPacket: true,\n\t\tisNew: false,\n\t\t\t\t\t\tdistance: 1.2,\n\t\t\t\t\t\tisOpen: true,\n\t\t\t\t\t\tisVerified: true,\n\t\t\t\t\t\tisFollowing: false,\n\t\t\t\t\t\tisHot: true,\n\t\t\t\t\t\thasDiscount: true,\n\t\t\t\t\t\trating: '4.9',\n\t\t\t\t\t\treviewCount: 156,\n\t\t\t\t\t\tcontactPhone: '188-8888-8888',\n\t\t\t\t\t\taddress: '河北省邯郸市磁县祥和路',\n\t\t\t\t\t\tfeatures: [\n\t\t\t\t\t\t\t{ icon: '/static/images/tabbar/送货.png', text: '免费送货' },\n\t\t\t\t\t\t\t{ icon: '/static/images/tabbar/安装.png', text: '上门安装' },\n\t\t\t\t\t\t\t{ icon: '/static/images/tabbar/保修.png', text: '全国联保' }\n\t\t\t\t\t\t]\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tid: \"2\",\n\t\t\t\t\t\tlogo: '/static/images/cizhou.png',\n\t\t\t\t\t\tname: '金鼎家居',\n\t\t\t\t\t\tdescription: '全屋定制、软装搭配、设计施工一站式服务',\n\t\t\t\t\t\tcategory: '家居家装',\n\t\t\t\t\t\tscale: '20-50人',\n\t\thasConsumeRedPacket: true,\n\t\tisNew: true,\n\t\t\t\t\t\tdistance: 0.8,\n\t\t\t\t\t\tisOpen: true,\n\t\t\t\t\t\tisVerified: true,\n\t\t\t\t\t\tisFollowing: false,\n\t\t\t\t\t\tisHot: false,\n\t\t\t\t\t\thasDiscount: false,\n\t\t\t\t\t\trating: '4.7',\n\t\t\t\t\t\treviewCount: 98,\n\t\t\t\t\t\tcontactPhone: '133-3333-3333',\n\t\t\t\t\t\taddress: '河北省邯郸市磁县滏阳路68号',\n\t\t\t\t\t\tfeatures: [\n\t\t\t\t\t\t\t{ icon: '/static/images/tabbar/设计.png', text: '免费设计' },\n\t\t\t\t\t\t\t{ icon: '/static/images/tabbar/测量.png', text: '上门测量' },\n\t\t\t\t\t\t\t{ icon: '/static/images/tabbar/保障.png', text: '质保5年' }\n\t\t\t\t\t\t]\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tid: \"3\",\n\t\t\t\t\t\tlogo: '/static/images/cizhou.png',\n\t\t\t\t\t\tname: '鲜丰水果',\n\t\t\t\t\t\tdescription: '新鲜水果，每日直采，支持微信下单，全城配送',\n\t\t\t\t\t\tcategory: '生鲜果蔬',\n\t\t\t\t\t\tscale: '5-10人',\n\t\t\t\t\t\thasConsumeRedPacket: true,\n\t\t\t\t\t\tisNew: false,\n\t\t\t\t\t\tdistance: 2.5,\n\t\t\t\t\t\tisOpen: true,\n\t\t\t\t\t\tisVerified: false,\n\t\t\t\t\t\tisFollowing: false,\n\t\t\t\t\t\tisHot: false,\n\t\t\t\t\t\thasDiscount: true,\n\t\t\t\t\t\trating: '4.8',\n\t\t\t\t\t\treviewCount: 215,\n\t\t\t\t\t\tcontactPhone: '177-7777-7777',\n\t\t\t\t\t\taddress: '河北省邯郸市磁县时代广场A区112号',\n\t\t\t\t\t\tfeatures: [\n\t\t\t\t\t\t\t{ icon: '/static/images/tabbar/配送.png', text: '全城配送' },\n\t\t\t\t\t\t\t{ icon: '/static/images/tabbar/新鲜.png', text: '每日新鲜' }\n\t\t\t\t\t\t]\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tid: \"4\",\n\t\t\t\t\t\tlogo: '/static/images/cizhou.png',\n\t\t\t\t\t\tname: '磁州书院',\n\t\t\t\t\t\tdescription: '综合性文化教育机构，少儿教育、成人培训、艺术培训',\n\t\t\t\t\t\tcategory: '文化教育',\n\t\t\t\t\t\tscale: '10-15人',\n\t\t\t\t\t\thasConsumeRedPacket: true,\n\t\t\t\t\t\tisNew: false,\n\t\t\t\t\t\tdistance: 3.1,\n\t\t\t\t\t\tisOpen: false,\n\t\t\t\t\t\tisVerified: true,\n\t\t\t\t\t\tisFollowing: false,\n\t\t\t\t\t\tisHot: false,\n\t\t\t\t\t\thasDiscount: false,\n\t\t\t\t\t\trating: '4.9',\n\t\t\t\t\t\treviewCount: 87,\n\t\t\t\t\t\tcontactPhone: '155-5555-5555',\n\t\t\t\t\t\taddress: '河北省邯郸市磁县文化路29号',\n\t\t\t\t\t\tfeatures: [\n\t\t\t\t\t\t\t{ icon: '/static/images/tabbar/教育.png', text: '专业教师' },\n\t\t\t\t\t\t\t{ icon: '/static/images/tabbar/证书.png', text: '官方认证' }\n\t\t\t\t\t\t]\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tid: \"5\",\n\t\t\t\t\t\tlogo: '/static/images/cizhou.png',\n\t\t\t\t\t\tname: '康美大药房',\n\t\t\t\t\t\tdescription: '全天24小时营业，医保定点药房，送药上门服务',\n\t\t\t\t\t\tcategory: '医疗健康',\n\t\t\t\t\t\tscale: '15-20人',\n\t\t\t\t\t\thasConsumeRedPacket: true,\n\t\t\t\t\t\tisNew: false,\n\t\t\t\t\t\tdistance: 1.8,\n\t\t\t\t\t\tisOpen: true,\n\t\t\t\t\t\tisVerified: true,\n\t\t\t\t\t\tisFollowing: false,\n\t\t\t\t\t\tisHot: true,\n\t\t\t\t\t\thasDiscount: true,\n\t\t\t\t\t\trating: '4.8',\n\t\t\t\t\t\treviewCount: 176,\n\t\t\t\t\t\tcontactPhone: '199-9999-9999',\n\t\t\t\t\t\taddress: '河北省邯郸市磁县健康路45号',\n\t\t\t\t\t\tfeatures: [\n\t\t\t\t\t\t\t{ icon: '/static/images/tabbar/医保.png', text: '医保定点' },\n\t\t\t\t\t\t\t{ icon: '/static/images/tabbar/送药.png', text: '送药上门' },\n\t\t\t\t\t\t\t{ icon: '/static/images/tabbar/24小时.png', text: '24小时营业' }\n\t\t\t\t\t\t]\n\t\t\t\t\t}\n]);\n\nconst businessList = computed(() => {\n\tswitch (currentTab.value) {\n\t\tcase 1: // 新入驻\n\t\t\treturn allBusinessList.filter(b => b.isNew).sort((a, b) => new Date(b.joinDate) - new Date(a.joinDate));\n\t\tcase 2: // 附近\n\t\t\treturn [...allBusinessList].sort((a, b) => a.distance - b.distance);\n\t\tcase 0: // 推荐\n\t\tdefault:\n\t\t\treturn allBusinessList;\n\t\t\t\t}\n\t\t\t});\n\n// --- 方法 ---\n\nconst changeTab = (index) => {\n\tcurrentTab.value = index;\n\tif (index === 2 && !locationAuthChecked.value) {\n\t\tcheckLocationPermission();\n\t}\n};\n\nconst checkLocationPermission = () => {\n\t// 实际项目中应使用 uni.getSetting 和 uni.authorize\n\tuni.showModal({\n\t\ttitle: '位置信息授权',\n\t\tcontent: '我们需要获取您的位置信息以展示附近的商家，是否同意？',\n\t\tsuccess: (res) => {\n\t\t\tif (res.confirm) {\n\t\t\t\tuni.getLocation({\n\t\t\t\t\ttype: 'wgs84',\n\t\t\t\t\tsuccess: (locRes) => {\n\t\t\t\t\t\tconsole.log('当前位置：', locRes.latitude, locRes.longitude);\n\t\t\t\t\t\t// 此处可以根据经纬度重新排序商家列表\n\t\t\t\t\t\tuni.showToast({ title: '已获取位置', icon: 'none' });\n\t\t\t},\n\t\t\t\t\tfail: () => {\n\t\t\t\t\t\tuni.showToast({ title: '获取位置失败', icon: 'error' });\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t}\n\t\t\tlocationAuthChecked.value = true;\n\t\t}\n\t\t\t\t});\n};\n\nconst navigateToJoin = () => {\n\tuni.navigateTo({ url: '/pages/business/join' });\n};\n\nconst navigateToFilter = (category) => {\n\tuni.navigateTo({ url: `/pages/business/filter?category=${category}` });\n};\n\nconst navigateToShopDetail = (id) => {\n\tuni.navigateTo({ url: `/pages/business/shop-detail?id=${id}` });\n};\n\nconst followBusiness = (id) => {\n\tconsole.log(`关注商家 ID: ${id}`);\n\tuni.showToast({ title: '关注成功', icon: 'success' });\n};\n\nconst callBusiness = (phoneNumber) => {\n\tif (!phoneNumber) {\n\t\tuni.showToast({\n\t\t\ttitle: '暂无联系电话',\n\t\t\ticon: 'none'\n\t\t});\n\t\treturn;\n\t}\n\t\n\tuni.makePhoneCall({\n\t\tphoneNumber: phoneNumber,\n\t\tsuccess: () => {\n\t\t\tconsole.log('拨打电话成功');\n\t\t},\n\t\tfail: (err) => {\n\t\t\tconsole.log('拨打电话失败', err);\n\t\t}\n\t});\n};\n\nconst navigateToBusiness = (address, name) => {\n\tif (!address) {\n\t\tuni.showToast({\n\t\t\ttitle: '暂无地址信息',\n\t\t\ticon: 'none'\n\t\t});\n\t\treturn;\n\t}\n\t\n\t// 这里使用默认坐标，实际项目中应该从后端获取经纬度\n\tuni.openLocation({\n\t\tlatitude: 36.313076,\n\t\tlongitude: 114.347312,\n\t\tname: name,\n\t\taddress: address,\n\t\tscale: 18\n\t});\n};\n\nconst shareBusiness = (business) => {\n\tif (!business) return;\n\t\n\tuni.showShareMenu({\n\t\twithShareTicket: true,\n\t\tmenus: ['shareAppMessage', 'shareTimeline']\n\t});\n\t\n\t// 设置分享内容\n\tuni.onShareAppMessage(() => {\n\t\treturn {\n\t\t\ttitle: business.name + ' - ' + business.description,\n\t\t\tpath: '/pages/business/shop-detail?id=' + business.id,\n\t\t\timageUrl: business.logo\n\t\t};\n\t});\n\t\n\t// 小程序环境下，显示更多分享选项\n\t// #ifdef MP-WEIXIN\n\tuni.showActionSheet({\n\t\titemList: ['分享给朋友', '分享到朋友圈', '生成分享图片'],\n\t\tsuccess: (res) => {\n\t\t\tif (res.tapIndex === 2) {\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '生成分享图片功能开发中',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t}\n\t\t}\n\t});\n\t// #endif\n};\n\n// --- 生命周期 ---\nonMounted(() => {\n\tconst systemInfo = uni.getSystemInfoSync();\n\tstatusBarHeight.value = systemInfo.statusBarHeight || 20;\n});\n</script>\n\n<style scoped>\n/* 使用系统默认字体 */\n.business-container {\n  font-family: -apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, Helvetica, Arial, sans-serif;\n}\n\n/* 其他需要使用阿里妈妈字体的地方也替换为系统字体 */\n.title, .heading, .subtitle {\n  font-family: -apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, Helvetica, Arial, sans-serif;\n  font-weight: bold; /* 使用系统字体的粗体代替阿里妈妈黑体 */\n}\n\n.business-page {\n\tmin-height: 100vh;\n\tbackground: #f5f7fa;\n\tposition: relative;\n\tpadding-bottom: 30rpx;\n\toverflow: hidden;\n\tfont-family: -apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, Helvetica, Arial, sans-serif;\n}\n\n/* 顶部蓝色背景 */\n.top-blue-bg {\n\tposition: absolute;\n\ttop: 0;\n\tleft: 0;\n\tright: 0;\n\theight: 320rpx;\n\tbackground: linear-gradient(135deg, #0052CC, #0066FF);\n\tz-index: 0;\n}\n\n/* 状态栏占位 */\n.status-bar {\n\tposition: relative;\n\twidth: 100%;\n\tz-index: 1;\n}\n\n/* 导航栏样式 */\n.navbar {\n\tposition: relative;\n\tz-index: 1;\n\theight: 44px;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n}\n\n.navbar-title {\n\tcolor: #ffffff;\n\tfont-size: 18px;\n\tfont-weight: 700;\n\tfont-family: -apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, Helvetica, Arial, sans-serif;\n}\n\n/* 统计栏 */\n.stats-bar {\n\tposition: relative;\n\tz-index: 1;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: flex-start;\n\tpadding: 20rpx 40rpx 5rpx;\n\tcolor: #333333;\n\tfont-size: 24rpx;\n}\n\n.stat-item {\n\tdisplay: flex;\n\talign-items: center;\n}\n\n.stat-icon {\n\twidth: 36rpx;\n\theight: 36rpx;\n\tmargin-right: 8rpx;\n\topacity: 0.95;\n}\n\n.stat-divider {\n\tmargin: 0 16rpx;\n\tcolor: rgba(0, 0, 0, 0.3);\n\tfont-weight: 200;\n}\n\n.exposure-text, .in-row {\n\tcolor: #999999;\n\tfont-weight: bold;\n\tfont-family: -apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, Helvetica, Arial, sans-serif;\n}\n\n/* 商家入驻卡片 */\n.join-card {\n\tmargin: 30rpx 30rpx 40rpx;\n\tpadding: 20rpx 30rpx;\n\tbackground: #fff;\n\tborder-radius: 24rpx;\n\tbox-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: space-between;\n\tposition: relative;\n}\n\n.join-card::before {\n\tcontent: \"\";\n\tposition: absolute;\n\ttop: 0;\n\tleft: 0;\n\tright: 0;\n\tbottom: 0;\n\tbackground: linear-gradient(135deg, rgba(255, 255, 255, 0.3) 0%, rgba(255, 255, 255, 0.1) 50%, rgba(255, 255, 255, 0) 100%);\n\tz-index: 0;\n}\n\n.join-card-left {\n\tdisplay: flex;\n\talign-items: center;\n\tposition: relative;\n\tz-index: 2;\n}\n\n.join-icon-wrap {\n\twidth: 76rpx;\n\theight: 76rpx;\n\tbackground: #EEF1F6;\n\tborder-radius: 50%;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\tmargin-right: 20rpx;\n\tbox-shadow: 6rpx 6rpx 12rpx rgba(174, 184, 210, 0.6),\n\t            -6rpx -6rpx 12rpx rgba(255, 255, 255, 0.9),\n\t            inset 1rpx 1rpx 2rpx rgba(255, 255, 255, 0.4);\n\tposition: relative;\n\tz-index: 3;\n\tborder: none;\n}\n\n.join-icon {\n\twidth: 46rpx;\n\theight: 46rpx;\n\topacity: 1;\n\tfilter: none;\n}\n\n.join-info {\n\tdisplay: flex;\n\tflex-direction: column;\n}\n\n.join-title {\n\tcolor: #3D56C1;\n\tfont-size: 32rpx;\n\tfont-weight: 700;\n\tmargin-bottom: 8rpx;\n\ttext-shadow: 1rpx 1rpx 2rpx rgba(255, 255, 255, 0.8);\n\tfont-family: -apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, Helvetica, Arial, sans-serif;\n\tletter-spacing: 1rpx;\n}\n\n.join-desc {\n\tcolor: #5F6A8A;\n\tfont-size: 24rpx;\n\ttext-shadow: 1rpx 1rpx 2rpx rgba(255, 255, 255, 0.6);\n}\n\n.join-btn {\n\tbackground: linear-gradient(to right, #007AFF, #5AC8FA);\n\tcolor: #ffffff;\n\tfont-size: 28rpx;\n\tpadding: 0 30rpx;\n\theight: 64rpx;\n\tline-height: 64rpx;\n\tborder-radius: 32rpx;\n\tmargin: 0;\n\tbox-shadow: 0 4rpx 12rpx rgba(0, 122, 255, 0.2);\n\ttransition: transform 0.2s ease;\n}\n\n.join-btn:active {\n\ttransform: scale(0.95);\n\tbox-shadow: 0 2rpx 5rpx rgba(0, 122, 255, 0.15);\n}\n\n.hot-badge {\n\tposition: absolute;\n\ttop: -3rpx;\n\tright: 8rpx;\n\tbackground: #FF5757;\n\tcolor: #ffffff;\n\tfont-size: 20rpx;\n\tfont-weight: bold;\n\tpadding: 6rpx 12rpx;\n\tborder-radius: 0 0 12rpx 12rpx;\n\tbox-shadow: 0 4rpx 8rpx rgba(255, 87, 87, 0.4);\n\tdisplay: block;\n}\n\n/* 分类宫格 */\n.category-grid {\n\tbackground: rgba(255, 255, 255, 0.95);\n\tborder-radius: 28rpx;\n\tmargin: 15rpx 24rpx 24rpx;\n\tpadding: 20rpx 15rpx;\n\tposition: relative;\n\tz-index: 2;\n\tbox-shadow: 0 15rpx 30rpx rgba(0, 0, 0, 0.1), 0 8rpx 15rpx rgba(0, 0, 0, 0.05);\n\tbackdrop-filter: blur(10px);\n\t-webkit-backdrop-filter: blur(10px);\n\tborder: 1px solid rgba(255, 255, 255, 0.6);\n\ttransform: translateZ(0);\n}\n\n.category-row {\n\tdisplay: flex;\n\tjustify-content: space-between;\n\tmargin-bottom: 20rpx;\n}\n\n.category-row:last-child {\n\tmargin-bottom: 0;\n}\n\n.category-item {\n\tflex: 1;\n\tdisplay: flex;\n\tflex-direction: column;\n\talign-items: center;\n\tjustify-content: center;\n\tposition: relative;\n\tpadding: 10rpx 0;\n\ttransition: transform 0.3s ease;\n}\n\n.category-item:active {\n\ttransform: scale(0.95);\n}\n\n.category-icon {\n\twidth: 80rpx;\n\theight: 80rpx;\n\tmargin-bottom: 6rpx;\n\tfilter: drop-shadow(0 2rpx 4rpx rgba(0, 0, 0, 0.1));\n}\n\n.category-name {\n\tfont-size: 22rpx;\n\tcolor: #333;\n\tfont-family: -apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, Helvetica, Arial, sans-serif;\n}\n\n.hot-tag {\n\tposition: absolute;\n\ttop: -3rpx;\n\tright: 8rpx;\n\tbackground: #ff4d4f;\n\tcolor: #ffffff;\n\tfont-size: 16rpx;\n\tpadding: 0 6rpx;\n\tborder-radius: 6rpx;\n\ttransform: rotate(10deg);\n}\n\n/* 标签页切换 */\n.tab-bar {\n\tdisplay: flex;\n\tbackground: rgba(255, 255, 255, 0.95);\n\tpadding: 15rpx 0;\n\tmargin: 30rpx 24rpx;\n\tposition: relative;\n\tz-index: 2;\n\tbox-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.08);\n\tborder-radius: 24rpx;\n\tbackdrop-filter: blur(10px);\n\t-webkit-backdrop-filter: blur(10px);\n\tborder: 1px solid rgba(255, 255, 255, 0.6);\n\ttransform: translateZ(0);\n}\n\n.tab-item {\n\tflex: 1;\n\tdisplay: flex;\n\tflex-direction: column;\n\talign-items: center;\n\tposition: relative;\n\tpadding: 3rpx 0;\n}\n\n.tab-text {\n\tfont-size: 30rpx;\n\tcolor: #999;\n\tfont-family: -apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, Helvetica, Arial, sans-serif;\n\tpadding: 6rpx 0;\n}\n\n.tab-item.active .tab-text {\n\tcolor: #0052cc;\n\tfont-weight: 500;\n}\n\n.tab-line {\n\twidth: 40rpx;\n\theight: 4rpx;\n\tbackground: #0052cc;\n\tborder-radius: 2rpx;\n\tposition: absolute;\n\tbottom: -3rpx;\n}\n\n/* 商家列表 */\n.business-list {\n\tpadding: 0 24rpx;\n\tposition: relative;\n\tz-index: 2;\n}\n\n.business-item {\n\tbackground: rgba(255, 255, 255, 0.98);\n\tborder-radius: 32rpx;\n\tmargin-bottom: 30rpx;\n\tbox-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.06), 0 2rpx 10rpx rgba(0, 0, 0, 0.04);\n\toverflow: hidden;\n\ttransform: translateZ(0);\n\ttransition: transform 0.3s ease, box-shadow 0.3s ease;\n\tposition: relative;\n}\n\n.business-item::after {\n\tcontent: '';\n\tposition: absolute;\n\ttop: 0;\n\tleft: 0;\n\tright: 0;\n\tbottom: 0;\n\tborder-radius: 32rpx;\n\tborder: 1px solid rgba(0, 0, 0, 0.03);\n\tpointer-events: none;\n}\n\n.business-item:active {\n\ttransform: translateY(2rpx) scale(0.995);\n\tbox-shadow: 0 5rpx 15rpx rgba(0, 0, 0, 0.04), 0 2rpx 5rpx rgba(0, 0, 0, 0.02);\n}\n\n.business-item-content {\n\tpadding: 24rpx;\n}\n\n.business-header {\n\tdisplay: flex;\n\tmargin-bottom: 16rpx;\n}\n\n.business-logo-container {\n\tposition: relative;\n\tmargin-right: 20rpx;\n\tflex-shrink: 0;\n}\n\n.business-logo {\n\twidth: 120rpx;\n\theight: 120rpx;\n\tborder-radius: 24rpx;\n\tobject-fit: cover;\n\tbox-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);\n\tborder: 1rpx solid rgba(255, 255, 255, 0.6);\n}\n\n.business-status {\n\tposition: absolute;\n\tbottom: -6rpx;\n\tleft: 50%;\n\ttransform: translateX(-50%);\n\tbackground: linear-gradient(to right, #4CD964, #32CD32);\n\tcolor: white;\n\tfont-size: 20rpx;\n\tpadding: 4rpx 12rpx;\n\tborder-radius: 10rpx;\n\twhite-space: nowrap;\n\tbox-shadow: 0 2rpx 6rpx rgba(76, 217, 100, 0.2);\n}\n\n.business-status.closed {\n\tbackground: linear-gradient(to right, #8E8E93, #AEAEB2);\n\tbox-shadow: 0 2rpx 6rpx rgba(142, 142, 147, 0.2);\n}\n\n.business-info {\n\tflex: 1;\n\tdisplay: flex;\n\tflex-direction: column;\n\toverflow: hidden;\n}\n\n.business-name-row {\n\tdisplay: flex;\n\talign-items: center;\n\tmargin-bottom: 8rpx;\n}\n\n.business-name {\n\tfont-size: 34rpx;\n\tcolor: #000;\n\tfont-weight: 600;\n\tmargin-right: 8rpx;\n\tflex: 1;\n\toverflow: hidden;\n\ttext-overflow: ellipsis;\n\twhite-space: nowrap;\n\tfont-family: -apple-system, BlinkMacSystemFont, \"SF Pro Display\", \"Helvetica Neue\", Helvetica, Arial, sans-serif;\n}\n\n.business-verified {\n\tdisplay: flex;\n\talign-items: center;\n\tmargin-left: 8rpx;\n}\n\n.verified-icon {\n\twidth: 32rpx;\n\theight: 32rpx;\n}\n\n.business-rating-row {\n\tdisplay: flex;\n\talign-items: center;\n\tmargin-bottom: 10rpx;\n}\n\n.rating-stars {\n\tdisplay: flex;\n\talign-items: center;\n\tmargin-right: 12rpx;\n}\n\n.rating-score {\n\tfont-size: 24rpx;\n\tcolor: #FF9500;\n\tfont-weight: 600;\n\tmargin-right: 6rpx;\n}\n\n.stars {\n\tdisplay: flex;\n}\n\n.star {\n\tfont-size: 22rpx;\n\tcolor: #FF9500;\n\tmargin-right: 2rpx;\n}\n\n.business-reviews {\n\tfont-size: 22rpx;\n\tcolor: #8E8E93;\n\tmargin-right: 12rpx;\n}\n\n.business-distance {\n\tfont-size: 22rpx;\n\tcolor: #8E8E93;\n\tbackground: #F2F2F7;\n\tpadding: 2rpx 10rpx;\n\tborder-radius: 10rpx;\n}\n\n.business-desc {\n\tfont-size: 26rpx;\n\tcolor: #666;\n\tline-height: 1.5;\n\toverflow: hidden;\n\ttext-overflow: ellipsis;\n\tdisplay: -webkit-box;\n\t-webkit-line-clamp: 2;\n\t-webkit-box-orient: vertical;\n}\n\n.business-tags-row {\n\tmargin: 12rpx 0;\n}\n\n.tags-scroll-view {\n\twidth: 100%;\n\twhite-space: nowrap;\n}\n\n.business-tags {\n\tdisplay: inline-flex;\n\tflex-wrap: nowrap;\n}\n\n.business-tag {\n\tdisplay: inline-flex;\n\talign-items: center;\n\tpadding: 6rpx 16rpx;\n\tborder-radius: 16rpx;\n\tmargin-right: 12rpx;\n\tbackground: #F2F2F7;\n}\n\n.business-tag:last-child {\n\tmargin-right: 0;\n}\n\n.tag-icon {\n\twidth: 24rpx;\n\theight: 24rpx;\n\tmargin-right: 6rpx;\n}\n\n.tag-text {\n\tfont-size: 22rpx;\n\tcolor: #666;\n\tline-height: 1.2;\n}\n\n.red-packet-tag {\n\tbackground: rgba(255, 69, 58, 0.1);\n}\n\n.red-packet-tag .tag-text {\n\tcolor: #FF4538;\n}\n\n.new-tag {\n\tbackground: rgba(0, 122, 255, 0.1);\n}\n\n.new-tag .tag-text {\n\tcolor: #007AFF;\n}\n\n.hot-business-tag {\n\tbackground: rgba(255, 149, 0, 0.1);\n}\n\n.hot-business-tag .tag-text {\n\tcolor: #FF9500;\n}\n\n.discount-tag {\n\tbackground: rgba(175, 82, 222, 0.1);\n}\n\n.discount-tag .tag-text {\n\tcolor: #AF52DE;\n}\n\n.business-features {\n\tdisplay: flex;\n\tflex-wrap: wrap;\n\tpadding: 12rpx 0;\n\tborder-top: 1rpx solid rgba(0, 0, 0, 0.03);\n\tmargin-top: 12rpx;\n}\n\n.feature-item {\n\tdisplay: flex;\n\talign-items: center;\n\tmargin-right: 24rpx;\n\tmargin-top: 8rpx;\n}\n\n.feature-icon {\n\twidth: 28rpx;\n\theight: 28rpx;\n\tmargin-right: 6rpx;\n}\n\n.feature-text {\n\tfont-size: 24rpx;\n\tcolor: #666;\n}\n\n.business-actions {\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: space-between;\n\tmargin-top: 16rpx;\n\tpadding-top: 16rpx;\n\tborder-top: 1rpx solid rgba(0, 0, 0, 0.03);\n}\n\n.action-btn {\n\tdisplay: flex;\n\tflex-direction: column;\n\talign-items: center;\n\tpadding: 10rpx 0;\n\tflex: 1;\n}\n\n.action-icon {\n\twidth: 36rpx;\n\theight: 36rpx;\n\tmargin-bottom: 6rpx;\n}\n\n.action-text {\n\tfont-size: 22rpx;\n\tcolor: #666;\n}\n\n.follow-btn {\n\tbackground: linear-gradient(to right, #007AFF, #5AC8FA);\n\tcolor: #ffffff;\n\tfont-size: 26rpx;\n\tpadding: 0 32rpx;\n\theight: 64rpx;\n\tline-height: 64rpx;\n\tborder-radius: 32rpx;\n\tmargin: 0;\n\tbox-shadow: 0 4rpx 12rpx rgba(0, 122, 255, 0.2);\n\ttransition: all 0.2s ease;\n\tfont-family: -apple-system, BlinkMacSystemFont, \"SF Pro Text\", sans-serif;\n\tfont-weight: 500;\n}\n\n.follow-btn.following {\n\tbackground: #F2F2F7;\n\tcolor: #8E8E93;\n\tbox-shadow: none;\n\tborder: 1rpx solid rgba(0, 0, 0, 0.05);\n}\n\n.follow-btn:active {\n\ttransform: scale(0.95);\n\topacity: 0.9;\n}\n\n/* 轮播图 */\n.swiper-container {\n\tposition: relative;\n\tz-index: 1;\n\twidth: 650rpx;\n\theight: 230rpx;\n\tmargin: 15rpx auto 15rpx;\n\tbox-sizing: content-box;\n\tborder: 12rpx solid #f0f5ff;\n\tborder-radius: 28rpx;\n\tbackground: #f0f5ff;\n\tbox-shadow: 0 25rpx 35rpx -15rpx rgba(0, 0, 0, 0.2),\n\t            0 15rpx 20rpx -15rpx rgba(0, 0, 0, 0.15),\n\t            inset 0 -2rpx 8rpx rgba(255, 255, 255, 0.7);\n\ttransform: translateY(0);\n\ttransition: transform 0.3s ease, box-shadow 0.3s ease;\n\tanimation: float 6s ease-in-out infinite;\n}\n\n@keyframes float {\n\t0% {\n\t\ttransform: translateY(0);\n\t}\n\t50% {\n\t\ttransform: translateY(-10rpx);\n\t}\n\t100% {\n\t\ttransform: translateY(0);\n\t}\n}\n\n.banner-swiper {\n\twidth: 100%;\n\theight: 100%;\n\tborder-radius: 8rpx;\n\toverflow: hidden;\n\tborder: none;\n\tbox-shadow: inset 0 2rpx 15rpx rgba(0, 0, 0, 0.1);\n}\n\n.banner-image {\n\twidth: 100%;\n\theight: 100%;\n\tobject-fit: cover;\n\ttransform: scale(1.01); /* 轻微放大，防止边缘出现空白 */\n\ttransition: transform 0.3s ease;\n}\n\n.swiper-container:hover {\n\tbox-shadow: 0 30rpx 40rpx -15rpx rgba(0, 0, 0, 0.25),\n\t            0 20rpx 25rpx -15rpx rgba(0, 0, 0, 0.18),\n\t            inset 0 -2rpx 8rpx rgba(255, 255, 255, 0.7);\n\ttransform: translateY(-5rpx);\n}\n\n/* 搜索框 - 更新样式 */\n.search-container-inner {\n\tposition: relative;\n\tz-index: 2;\n\tmargin: 0 20rpx 20rpx;\n\twidth: 75%;\n\tmargin-left: auto;\n\tmargin-right: auto;\n}\n\n.search-box {\n\tbackground: rgba(255, 255, 255, 0.95);\n\tborder-radius: 36rpx;\n\tpadding: 10rpx 20rpx;\n\tdisplay: flex;\n\talign-items: center;\n\tbox-shadow: 0 6rpx 15rpx rgba(0, 0, 0, 0.08);\n\tborder: 1px solid rgba(0, 0, 0, 0.05);\n}\n\n.search-input {\n\tflex: 1;\n\tfont-size: 26rpx;\n\theight: 60rpx;\n\tcolor: #333;\n\tpadding-left: 10rpx;\n}\n\n.search-icon {\n\twidth: 32rpx;\n\theight: 32rpx;\n\topacity: 0.6;\n}\n\n/* 商家卡片轮播 */\n.merchant-recommend-section {\n\tmargin: 24rpx 30rpx 30rpx;\n\tposition: relative;\n\tz-index: 2;\n\tbackground: #ffffff;\n\tborder-radius: 20rpx;\n\tpadding: 24rpx 20rpx 30rpx;\n\tbox-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);\n\toverflow: hidden;\n}\n\n.section-header {\n\tdisplay: flex;\n\tjustify-content: space-between;\n\talign-items: center;\n\tmargin-bottom: 20rpx;\n}\n\n.section-title-wrap {\n\tdisplay: flex;\n\talign-items: center;\n}\n\n.section-bar {\n\twidth: 6rpx;\n\theight: 36rpx;\n\tbackground: linear-gradient(180deg, #0052CC, #0066FF);\n\tborder-radius: 3rpx;\n\tmargin-right: 16rpx;\n}\n\n.section-title {\n\tfont-size: 32rpx;\n\tfont-weight: 600;\n\tcolor: #333;\n\tfont-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', sans-serif;\n}\n\n.section-more {\n\tdisplay: flex;\n\talign-items: center;\n\tpadding: 6rpx 12rpx;\n\tborder-radius: 16rpx;\n\ttransition: all 0.2s ease;\n}\n\n.section-more:active {\n\tbackground: rgba(0, 0, 0, 0.05);\n\ttransform: scale(0.96);\n}\n\n.more-text {\n\tfont-size: 26rpx;\n\tcolor: #007AFF;\n}\n\n.more-icon {\n\tfont-size: 26rpx;\n\tcolor: #007AFF;\n\tmargin-left: 2rpx;\n}\n\n.merchant-swiper {\n\twidth: 100%;\n\theight: 280rpx;\n\tmargin-top: 20rpx;\n}\n\n.merchant-swiper-page {\n\tdisplay: flex;\n\tjustify-content: space-between;\n\tpadding: 0 5rpx;\n}\n\n.merchant-item {\n\twidth: 260rpx;\n\theight: 280rpx;\n\tbackground: #FFFFFF;\n\tborder-radius: 16rpx;\n\toverflow: hidden;\n\tbox-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);\n\tborder: 1rpx solid #F2F2F7;\n\tpadding: 12rpx;\n}\n\n.merchant-card {\n\twidth: 100%;\n\theight: 100%;\n\tdisplay: flex;\n\tflex-direction: column;\n\talign-items: center;\n\tpadding: 15rpx;\n\tbox-sizing: border-box;\n\tposition: relative;\n\tbackground: #FFFFFF;\n\tborder-radius: 12rpx;\n\tborder: 1rpx solid #E6EDFA;\n}\n\n.merchant-logo {\n\twidth: 90rpx;\n\theight: 90rpx;\n\tborder-radius: 45rpx;\n\tmargin-bottom: 12rpx;\n\tborder: 2rpx solid #F0F4FF;\n\tbackground: #F9F9F9;\n}\n\n.merchant-info {\n\twidth: 100%;\n\tdisplay: flex;\n\tflex-direction: column;\n\talign-items: center;\n}\n\n.merchant-name-wrap {\n\twidth: 100%;\n\ttext-align: center;\n\tmargin-bottom: 6rpx;\n}\n\n.merchant-name {\n\tfont-size: 26rpx;\n\tcolor: #222;\n\tfont-weight: 700;\n\ttext-align: center;\n\toverflow: hidden;\n\ttext-overflow: ellipsis;\n\twhite-space: nowrap;\n\tmax-width: 100%;\n}\n\n.merchant-desc {\n\tfont-size: 22rpx;\n\tcolor: #8a97b2;\n\ttext-align: center;\n\toverflow: hidden;\n\ttext-overflow: ellipsis;\n\twhite-space: nowrap;\n\twidth: 100%;\n\tmargin-bottom: 8rpx;\n}\n\n.merchant-category {\n\tfont-size: 20rpx;\n\tcolor: #007AFF;\n\tbackground: rgba(0, 122, 255, 0.1);\n\tpadding: 4rpx 12rpx;\n\tborder-radius: 12rpx;\n\tmargin-bottom: 12rpx;\n}\n\n.merchant-collect-btn {\n\twidth: 80%;\n\theight: 56rpx;\n\tline-height: 56rpx;\n\tfont-size: 24rpx;\n\tcolor: #FFFFFF;\n\tbackground: linear-gradient(to right, #007AFF, #5AC8FA) !important;\n\tborder-radius: 28rpx;\n\tpadding: 0 10rpx;\n\tmargin: 0;\n\tposition: absolute;\n\tbottom: 15rpx;\n\tbox-shadow: 0 4rpx 12rpx rgba(0, 122, 255, 0.2);\n\ttext-align: center;\n}\n\n.collect-btn-text {\n\tfont-size: 24rpx;\n\tcolor: #FFFFFF;\n}\n\n.merchant-indicators {\n\tdisplay: flex;\n\tjustify-content: center;\n\tmargin-top: 15rpx;\n}\n\n.merchant-dot {\n\twidth: 12rpx;\n\theight: 12rpx;\n\tborder-radius: 6rpx;\n\tbackground-color: #D1D1D6;\n\tmargin: 0 6rpx;\n\ttransition: all 0.3s ease;\n}\n\n.merchant-dot.active {\n\twidth: 24rpx;\n\tbackground: #007AFF;\n}\n</style> \n", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/pages/business/business.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "reactive", "computed", "uni", "onMounted"], "mappings": ";;;;;;AA0PA,MAAA,aAAA,MAAA;AACA,MAAA,oBAAA,MAAA;;;;AAGA,UAAA,aAAAA,cAAAA,IAAA,CAAA;AACA,UAAA,kBAAAA,cAAAA,IAAA,EAAA;AACA,UAAA,gBAAAA,cAAAA,IAAA,EAAA;AACA,UAAA,sBAAAA,cAAAA,IAAA,KAAA;AAEA,UAAA,aAAAC,cAAAA,SAAA;AAAA,MACA,EAAA,OAAA,qCAAA;AAAA,MACA,EAAA,OAAA,qCAAA;AAAA,MACA,EAAA,OAAA,qCAAA;AAAA,IACA,CAAA;AAEAA,kBAAAA,SAAA;AAAA,MACA,EAAA,MAAA,QAAA,MAAA,iCAAA;AAAA,MACA,EAAA,MAAA,QAAA,MAAA,iCAAA;AAAA,MACA,EAAA,MAAA,QAAA,MAAA,iCAAA;AAAA,MACA,EAAA,MAAA,QAAA,MAAA,kCAAA,KAAA,KAAA;AAAA,MACA,EAAA,MAAA,QAAA,MAAA,iCAAA;AAAA,MACA,EAAA,MAAA,SAAA,MAAA,kCAAA;AAAA,MACA,EAAA,MAAA,QAAA,MAAA,iCAAA;AAAA,MACA,EAAA,MAAA,QAAA,MAAA,iCAAA;AAAA,MACA,EAAA,MAAA,SAAA,MAAA,kCAAA;AAAA,MACA,EAAA,MAAA,QAAA,MAAA,kCAAA;AAAA,MACA,EAAA,MAAA,QAAA,MAAA,iCAAA;AAAA,MACA,EAAA,MAAA,QAAA,MAAA,iCAAA;AAAA,MACA,EAAA,MAAA,QAAA,MAAA,iCAAA;AAAA,MACA,EAAA,MAAA,QAAA,MAAA,+BAAA;AAAA,IACA,CAAA;AAEA,UAAA,kBAAAA,cAAAA,SAAA;AAAA,MACA;AAAA,QACA,IAAA;AAAA,QACA,MAAA;AAAA,QACA,MAAA;AAAA,QACA,aAAA;AAAA,QACA,UAAA;AAAA,QACA,OAAA;AAAA,QACA,qBAAA;AAAA,QACA,OAAA;AAAA,QACA,UAAA;AAAA,QACA,QAAA;AAAA,QACA,YAAA;AAAA,QACA,aAAA;AAAA,QACA,OAAA;AAAA,QACA,aAAA;AAAA,QACA,QAAA;AAAA,QACA,aAAA;AAAA,QACA,cAAA;AAAA,QACA,SAAA;AAAA,QACA,UAAA;AAAA,UACA,EAAA,MAAA,gCAAA,MAAA,OAAA;AAAA,UACA,EAAA,MAAA,gCAAA,MAAA,OAAA;AAAA,UACA,EAAA,MAAA,gCAAA,MAAA,OAAA;AAAA,QACA;AAAA,MACA;AAAA,MACA;AAAA,QACA,IAAA;AAAA,QACA,MAAA;AAAA,QACA,MAAA;AAAA,QACA,aAAA;AAAA,QACA,UAAA;AAAA,QACA,OAAA;AAAA,QACA,qBAAA;AAAA,QACA,OAAA;AAAA,QACA,UAAA;AAAA,QACA,QAAA;AAAA,QACA,YAAA;AAAA,QACA,aAAA;AAAA,QACA,OAAA;AAAA,QACA,aAAA;AAAA,QACA,QAAA;AAAA,QACA,aAAA;AAAA,QACA,cAAA;AAAA,QACA,SAAA;AAAA,QACA,UAAA;AAAA,UACA,EAAA,MAAA,gCAAA,MAAA,OAAA;AAAA,UACA,EAAA,MAAA,gCAAA,MAAA,OAAA;AAAA,UACA,EAAA,MAAA,gCAAA,MAAA,OAAA;AAAA,QACA;AAAA,MACA;AAAA,MACA;AAAA,QACA,IAAA;AAAA,QACA,MAAA;AAAA,QACA,MAAA;AAAA,QACA,aAAA;AAAA,QACA,UAAA;AAAA,QACA,OAAA;AAAA,QACA,qBAAA;AAAA,QACA,OAAA;AAAA,QACA,UAAA;AAAA,QACA,QAAA;AAAA,QACA,YAAA;AAAA,QACA,aAAA;AAAA,QACA,OAAA;AAAA,QACA,aAAA;AAAA,QACA,QAAA;AAAA,QACA,aAAA;AAAA,QACA,cAAA;AAAA,QACA,SAAA;AAAA,QACA,UAAA;AAAA,UACA,EAAA,MAAA,gCAAA,MAAA,OAAA;AAAA,UACA,EAAA,MAAA,gCAAA,MAAA,OAAA;AAAA,QACA;AAAA,MACA;AAAA,MACA;AAAA,QACA,IAAA;AAAA,QACA,MAAA;AAAA,QACA,MAAA;AAAA,QACA,aAAA;AAAA,QACA,UAAA;AAAA,QACA,OAAA;AAAA,QACA,qBAAA;AAAA,QACA,OAAA;AAAA,QACA,UAAA;AAAA,QACA,QAAA;AAAA,QACA,YAAA;AAAA,QACA,aAAA;AAAA,QACA,OAAA;AAAA,QACA,aAAA;AAAA,QACA,QAAA;AAAA,QACA,aAAA;AAAA,QACA,cAAA;AAAA,QACA,SAAA;AAAA,QACA,UAAA;AAAA,UACA,EAAA,MAAA,gCAAA,MAAA,OAAA;AAAA,UACA,EAAA,MAAA,gCAAA,MAAA,OAAA;AAAA,QACA;AAAA,MACA;AAAA,MACA;AAAA,QACA,IAAA;AAAA,QACA,MAAA;AAAA,QACA,MAAA;AAAA,QACA,aAAA;AAAA,QACA,UAAA;AAAA,QACA,OAAA;AAAA,QACA,qBAAA;AAAA,QACA,OAAA;AAAA,QACA,UAAA;AAAA,QACA,QAAA;AAAA,QACA,YAAA;AAAA,QACA,aAAA;AAAA,QACA,OAAA;AAAA,QACA,aAAA;AAAA,QACA,QAAA;AAAA,QACA,aAAA;AAAA,QACA,cAAA;AAAA,QACA,SAAA;AAAA,QACA,UAAA;AAAA,UACA,EAAA,MAAA,gCAAA,MAAA,OAAA;AAAA,UACA,EAAA,MAAA,gCAAA,MAAA,OAAA;AAAA,UACA,EAAA,MAAA,kCAAA,MAAA,SAAA;AAAA,QACA;AAAA,MACA;AAAA,IACA,CAAA;AAEA,UAAA,eAAAC,cAAA,SAAA,MAAA;AACA,cAAA,WAAA,OAAA;AAAA,QACA,KAAA;AACA,iBAAA,gBAAA,OAAA,OAAA,EAAA,KAAA,EAAA,KAAA,CAAA,GAAA,MAAA,IAAA,KAAA,EAAA,QAAA,IAAA,IAAA,KAAA,EAAA,QAAA,CAAA;AAAA,QACA,KAAA;AACA,iBAAA,CAAA,GAAA,eAAA,EAAA,KAAA,CAAA,GAAA,MAAA,EAAA,WAAA,EAAA,QAAA;AAAA,QACA,KAAA;AAAA,QACA;AACA,iBAAA;AAAA,MACA;AAAA,IACA,CAAA;AAIA,UAAA,YAAA,CAAA,UAAA;AACA,iBAAA,QAAA;AACA,UAAA,UAAA,KAAA,CAAA,oBAAA,OAAA;AACA;MACA;AAAA,IACA;AAEA,UAAA,0BAAA,MAAA;AAEAC,oBAAAA,MAAA,UAAA;AAAA,QACA,OAAA;AAAA,QACA,SAAA;AAAA,QACA,SAAA,CAAA,QAAA;AACA,cAAA,IAAA,SAAA;AACAA,0BAAAA,MAAA,YAAA;AAAA,cACA,MAAA;AAAA,cACA,SAAA,CAAA,WAAA;AACAA,oCAAA,MAAA,OAAA,sCAAA,SAAA,OAAA,UAAA,OAAA,SAAA;AAEAA,8BAAA,MAAA,UAAA,EAAA,OAAA,SAAA,MAAA,OAAA,CAAA;AAAA,cACA;AAAA,cACA,MAAA,MAAA;AACAA,8BAAA,MAAA,UAAA,EAAA,OAAA,UAAA,MAAA,QAAA,CAAA;AAAA,cACA;AAAA,YACA,CAAA;AAAA,UACA;AACA,8BAAA,QAAA;AAAA,QACA;AAAA,MACA,CAAA;AAAA,IACA;AAEA,UAAA,iBAAA,MAAA;AACAA,oBAAAA,MAAA,WAAA,EAAA,KAAA,uBAAA,CAAA;AAAA,IACA;AAEA,UAAA,mBAAA,CAAA,aAAA;AACAA,oBAAA,MAAA,WAAA,EAAA,KAAA,mCAAA,QAAA,GAAA,CAAA;AAAA,IACA;AAEA,UAAA,uBAAA,CAAA,OAAA;AACAA,oBAAA,MAAA,WAAA,EAAA,KAAA,kCAAA,EAAA,GAAA,CAAA;AAAA,IACA;AAEA,UAAA,iBAAA,CAAA,OAAA;AACAA,oBAAA,MAAA,MAAA,OAAA,sCAAA,YAAA,EAAA,EAAA;AACAA,oBAAA,MAAA,UAAA,EAAA,OAAA,QAAA,MAAA,UAAA,CAAA;AAAA,IACA;AAEA,UAAA,eAAA,CAAA,gBAAA;AACA,UAAA,CAAA,aAAA;AACAA,sBAAAA,MAAA,UAAA;AAAA,UACA,OAAA;AAAA,UACA,MAAA;AAAA,QACA,CAAA;AACA;AAAA,MACA;AAEAA,oBAAAA,MAAA,cAAA;AAAA,QACA;AAAA,QACA,SAAA,MAAA;AACAA,wBAAAA,MAAA,MAAA,OAAA,sCAAA,QAAA;AAAA,QACA;AAAA,QACA,MAAA,CAAA,QAAA;AACAA,wBAAA,MAAA,MAAA,OAAA,sCAAA,UAAA,GAAA;AAAA,QACA;AAAA,MACA,CAAA;AAAA,IACA;AAEA,UAAA,qBAAA,CAAA,SAAA,SAAA;AACA,UAAA,CAAA,SAAA;AACAA,sBAAAA,MAAA,UAAA;AAAA,UACA,OAAA;AAAA,UACA,MAAA;AAAA,QACA,CAAA;AACA;AAAA,MACA;AAGAA,oBAAAA,MAAA,aAAA;AAAA,QACA,UAAA;AAAA,QACA,WAAA;AAAA,QACA;AAAA,QACA;AAAA,QACA,OAAA;AAAA,MACA,CAAA;AAAA,IACA;AAEA,UAAA,gBAAA,CAAA,aAAA;AACA,UAAA,CAAA;AAAA;AAEAA,oBAAAA,MAAA,cAAA;AAAA,QACA,iBAAA;AAAA,QACA,OAAA,CAAA,mBAAA,eAAA;AAAA,MACA,CAAA;AAGAA,oBAAA,MAAA,kBAAA,MAAA;AACA,eAAA;AAAA,UACA,OAAA,SAAA,OAAA,QAAA,SAAA;AAAA,UACA,MAAA,oCAAA,SAAA;AAAA,UACA,UAAA,SAAA;AAAA,QACA;AAAA,MACA,CAAA;AAIAA,oBAAAA,MAAA,gBAAA;AAAA,QACA,UAAA,CAAA,SAAA,UAAA,QAAA;AAAA,QACA,SAAA,CAAA,QAAA;AACA,cAAA,IAAA,aAAA,GAAA;AACAA,0BAAAA,MAAA,UAAA;AAAA,cACA,OAAA;AAAA,cACA,MAAA;AAAA,YACA,CAAA;AAAA,UACA;AAAA,QACA;AAAA,MACA,CAAA;AAAA,IAEA;AAGAC,kBAAAA,UAAA,MAAA;AACA,YAAA,aAAAD,oBAAA;AACA,sBAAA,QAAA,WAAA,mBAAA;AAAA,IACA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACjiBA,GAAG,WAAW,eAAe;"}