{"version": 3, "file": "job-seeking-detail.js", "sources": ["pages/publish/job-seeking-detail.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvcHVibGlzaC9qb2Itc2Vla2luZy1kZXRhaWwudnVl"], "sourcesContent": ["<template>\n  <view class=\"job-seeking-container\">\n    <!-- 自定义导航栏 -->\n    <view class=\"custom-navbar\" :style=\"{ paddingTop: statusBarHeight + 'px' }\">\n      <view class=\"navbar-left\" @click=\"goBack\">\n        <image src=\"/static/images/tabbar/返回键.png\" class=\"back-icon\"></image>\n      </view>\n      <view class=\"navbar-title\">求职信息详情</view>\n      <view class=\"navbar-right\">\n        <!-- 举报按钮已删除 -->\n      </view>\n    </view>\n    \n    <!-- 隐藏的分享按钮，用于自动触发 -->\n    <button id=\"shareButton\" class=\"hidden-share-btn\" open-type=\"share\"></button>\n    \n    <view class=\"job-seeking-wrapper\">\n      <!-- 求职基本信息卡片 -->\n      <view class=\"content-card seeking-info-card\">\n        <view class=\"seeking-header\">\n          <view class=\"seeking-title\">{{seekingData.title}}</view>\n          <view class=\"seeking-salary\">{{seekingData.salary}}</view>\n          <view class=\"seeking-meta\">\n            <view class=\"seeking-tag-group\">\n              <view class=\"seeking-tag\" v-for=\"(tag, index) in seekingData.tags\" :key=\"index\">{{tag}}</view>\n            </view>\n            <text class=\"seeking-publish-time\">发布于 {{formatTime(seekingData.publishTime)}}</text>\n          </view>\n        </view>\n        \n        <!-- 基本信息 -->\n        <view class=\"seeking-basic-info\">\n          <view class=\"info-item\">\n            <text class=\"info-label\">求职者</text>\n            <text class=\"info-value\">{{seekingData.name}}</text>\n          </view>\n          <view class=\"info-item\">\n            <text class=\"info-label\">年龄</text>\n            <text class=\"info-value\">{{seekingData.age}}岁</text>\n          </view>\n          <view class=\"info-item\">\n            <text class=\"info-label\">学历</text>\n            <text class=\"info-value\">{{seekingData.education}}</text>\n          </view>\n          <view class=\"info-item\">\n            <text class=\"info-label\">工作经验</text>\n            <text class=\"info-value\">{{seekingData.experience}}</text>\n          </view>\n        </view>\n      </view>\n      \n      <!-- 求职意向 -->\n      <view class=\"content-card seeking-intention-card\">\n        <view class=\"section-title\">求职意向</view>\n        <view class=\"intention-list\">\n          <view class=\"intention-item\" v-for=\"(item, index) in seekingData.intentions\" :key=\"index\">\n            <text class=\"intention-label\">{{item.label}}</text>\n            <text class=\"intention-value\">{{item.value}}</text>\n          </view>\n        </view>\n      </view>\n      \n      <!-- 工作经验 -->\n      <view class=\"content-card work-experience-card\">\n        <view class=\"section-title\">工作经验</view>\n        <view class=\"experience-list\">\n          <view class=\"experience-item\" v-for=\"(item, index) in seekingData.workExperience\" :key=\"index\">\n            <view class=\"experience-header\">\n              <text class=\"experience-company\">{{item.company}}</text>\n              <text class=\"experience-time\">{{item.time}}</text>\n            </view>\n            <view class=\"experience-position\">{{item.position}}</view>\n            <view class=\"experience-desc\">{{item.description}}</view>\n          </view>\n        </view>\n      </view>\n      \n      <!-- 教育背景 -->\n      <view class=\"content-card education-card\">\n        <view class=\"section-title\">教育背景</view>\n        <view class=\"education-list\">\n          <view class=\"education-item\">\n              <view class=\"education-header\">\n              <text class=\"education-school\">某大学</text>\n              <text class=\"education-time\">2018.09-2022.06</text>\n              </view>\n            <view class=\"education-major\">专业: 行政管理</view>\n            <view class=\"education-degree\">学历: 本科</view>\n            </view>\n        </view>\n      </view>\n      \n      <!-- 技能特长 -->\n      <view class=\"content-card skills-card\">\n        <view class=\"section-title\">技能特长</view>\n        <view class=\"skills-content\">\n          <view class=\"skill-tag\" v-for=\"(skill, index) in seekingData.skills\" :key=\"index\">{{skill}}</view>\n        </view>\n      </view>\n      \n      <!-- 自我评价 -->\n      <view class=\"content-card self-evaluation-card\">\n        <view class=\"section-title\">自我评价</view>\n        <view class=\"evaluation-content\">\n          <text class=\"evaluation-text\">{{seekingData.selfEvaluation}}</text>\n        </view>\n      </view>\n      \n      <!-- 联系方式 -->\n      <view class=\"content-card contact-card\">\n        <view class=\"section-title\">联系方式</view>\n        <view class=\"contact-content\">\n          <view class=\"contact-item\">\n            <text class=\"contact-label\">联系人</text>\n            <text class=\"contact-value\">{{seekingData.contact.name}}</text>\n          </view>\n          <view class=\"contact-item\">\n            <text class=\"contact-label\">电话</text>\n            <text class=\"contact-value contact-phone\" @click=\"callPhone\">{{seekingData.contact.phone}}</text>\n          </view>\n          <view class=\"contact-tips\">\n            <text class=\"tips-icon iconfont icon-info\"></text>\n            <text class=\"tips-text\">请说明在\"磁州生活网\"看到的信息</text>\n          </view>\n        </view>\n      </view>\n      \n      <!-- 举报卡片 -->\n      <report-card></report-card>\n      \n      <!-- 相关求职推荐 -->\n      <view class=\"content-card related-seekings-card\">\n        <!-- 标题栏 -->\n        <view class=\"collapsible-header\">\n          <view class=\"section-title\">相关求职推荐</view>\n        </view>\n        \n        <!-- 内容区 -->\n        <view class=\"collapsible-content\">\n          <!-- 简洁的求职列表 -->\n          <view class=\"related-seekings-list\">\n            <view class=\"related-seeking-item\" \n                 v-for=\"(seeking, index) in relatedSeekings.slice(0, 3)\" \n                 :key=\"index\" \n                 @click=\"navigateToSeekingDetail(seeking.id)\">\n              <view class=\"seeking-item-content\">\n                <view class=\"seeking-item-left\">\n                  <image class=\"seeker-avatar\" :src=\"seeking.avatar\" mode=\"aspectFill\"></image>\n                </view>\n                <view class=\"seeking-item-middle\">\n                  <text class=\"seeking-item-title\">{{seeking.title}}</text>\n                  <view class=\"seeking-item-person\">{{seeking.name}} | {{seeking.education}} | {{seeking.experience}}</view>\n                  <view class=\"seeking-item-tags\">\n                    <text class=\"seeking-item-tag\" v-for=\"(tag, tagIndex) in seeking.tags.slice(0, 2)\" :key=\"tagIndex\">{{tag}}</text>\n                    <text class=\"seeking-item-tag-more\" v-if=\"seeking.tags.length > 2\">+{{seeking.tags.length - 2}}</text>\n                  </view>\n                </view>\n                <view class=\"seeking-item-right\">\n                  <text class=\"seeking-item-salary\">{{seeking.salary}}</text>\n                </view>\n              </view>\n            </view>\n            \n            <!-- 暂无数据提示 -->\n            <view class=\"empty-related-seekings\" v-if=\"relatedSeekings.length === 0\">\n              <image src=\"/static/images/empty.png\" class=\"empty-image\" mode=\"aspectFit\"></image>\n              <text class=\"empty-text\">暂无相关求职</text>\n            </view>\n          </view>\n          \n          <!-- 查看更多按钮 -->\n          <view class=\"view-more-btn\" v-if=\"relatedSeekings.length > 0\" @click.stop=\"navigateToSeekingList\">\n            <text class=\"view-more-text\">查看更多求职信息</text>\n            <text class=\"view-more-icon iconfont icon-right\"></text>\n          </view>\n        </view>\n      </view>\n    </view>\n    \n    <!-- 底部操作栏 -->\n    <view class=\"interaction-toolbar\">\n      <view class=\"toolbar-item\" @click=\"goToHome\">\n        <image src=\"/static/images/tabbar/a首页.png\" class=\"toolbar-icon\"></image>\n        <text class=\"toolbar-text\">首页</text>\n      </view>\n      <view class=\"toolbar-item\" @click=\"toggleCollect\">\n        <image src=\"/static/images/tabbar/a收藏.png\" class=\"toolbar-icon\"></image>\n        <text class=\"toolbar-text\">收藏</text>\n      </view>\n      <button class=\"share-button toolbar-item\" open-type=\"share\">\n        <image src=\"/static/images/tabbar/a分享.png\" class=\"toolbar-icon\"></image>\n        <text class=\"toolbar-text\">分享</text>\n      </button>\n      <view class=\"toolbar-item\" @click=\"openChat\">\n        <image src=\"/static/images/tabbar/a消息.png\" class=\"toolbar-icon\"></image>\n        <text class=\"toolbar-text\">私信</text>\n      </view>\n      <view class=\"toolbar-item call-button\" @click=\"callPhone\">\n        <view class=\"call-button-content\">\n          <text class=\"call-text\">打电话</text>\n          <text class=\"call-subtitle\">请说在磁州生活网看到的</text>\n        </view>\n      </view>\n    </view>\n    \n    <!-- 悬浮海报按钮 -->\n    <view class=\"float-poster-btn\" @click=\"generateShareImage\">\n      <image src=\"/static/images/tabbar/海报.png\" class=\"poster-icon\"></image>\n      <text class=\"poster-text\">海报</text>\n    </view>\n  </view>\n</template>\n\n<script setup>\nimport { ref, onMounted } from 'vue'\nimport ReportCard from '@/components/ReportCard.vue'\n\n// 格式化时间\nconst formatTime = (timestamp) => {\n  const date = new Date(timestamp);\n  return `${date.getMonth() + 1}月${date.getDate()}日`;\n};\n\n// 定义解析教育背景函数\nconst parseEducation = (education) => {\n  // 如果已经是对象或数组，直接返回\n  if (typeof education !== 'string') {\n    return education;\n  }\n  \n  // 教育程度映射\n  const educationMap = {\n    '1': '小学',\n    '2': '初中',\n    '3': '高中',\n    '4': '中专',\n    '5': '大专',\n    '6': '本科',\n    '7': '硕士',\n    '8': '博士'\n  };\n  \n  // 如果是数字字符串，尝试从映射中获取\n  if (!isNaN(education) && educationMap[education]) {\n    return educationMap[education];\n  }\n  \n  // 否则直接返回原字符串\n  return education;\n};\n\n// 获取状态栏高度\nconst statusBarHeight = ref(20); // 默认值\n\n// 返回上一页\nconst goBack = () => {\n  uni.navigateBack();\n};\n\n// 响应式数据\nconst isCollected = ref(false);\nconst seekingData = ref({\n  id: 'seeking12345',\n  title: '求职行政文员',\n  salary: '3000-4000元/月',\n  tags: ['行政文员', '应届生', '可立即上岗'],\n  publishTime: Date.now() - 86400000, // 1天前\n  name: '张女士',\n  age: 24,\n  education: '本科',\n  experience: '1年',\n  intentions: [\n    { label: '期望职位', value: '行政文员' },\n    { label: '期望薪资', value: '3000-4000元/月' },\n    { label: '期望城市', value: '磁县' },\n    { label: '期望行业', value: '不限' }\n  ],\n  workExperience: [\n    {\n      company: '某科技有限公司',\n      time: '2022.07-2023.06',\n      position: '行政助理',\n      description: '负责公司日常行政事务，包括文件整理、会议安排、接待来访等工作。'\n    }\n  ],\n  educationInfo: [\n    {\n      school: '某大学',\n      time: '2018.09-2022.06',\n      major: '行政管理',\n      degree: '本科'\n    }\n  ],\n  skills: ['办公软件', '文档处理', '会议组织', '沟通协调'],\n  selfEvaluation: '性格开朗，工作认真负责，有较强的沟通能力和团队协作精神。熟悉办公软件操作，能够独立完成日常行政工作。',\n  contact: {\n    name: '张女士',\n    phone: '13912345678'\n  }\n});\n\n// 相关求职列表功能\nconst relatedSeekings = ref([]);\n\n// 加载相关求职信息\nconst loadRelatedSeekings = () => {\n  // 这里可以调用API获取数据\n  // 实际项目中应该根据当前求职信息的分类、标签等进行相关性匹配\n  \n  // 模拟数据\n  setTimeout(() => {\n    relatedSeekings.value = [\n      {\n        id: 'seeking001',\n        title: '求职销售代表',\n        salary: '4000-6000元/月',\n        name: '李先生',\n        avatar: '/static/images/avatar/男1.png',\n        education: '大专',\n        experience: '3年',\n        tags: ['销售经验', '有车', '形象好']\n      },\n      {\n        id: 'seeking002',\n        title: '求职行政助理',\n        salary: '3500-4500元/月',\n        name: '王女士',\n        avatar: '/static/images/avatar/女1.png',\n        education: '本科',\n        experience: '应届生',\n        tags: ['办公软件熟练', '英语四级', '形象气质佳']\n      },\n      {\n        id: 'seeking003',\n        title: '求职前台文员',\n        salary: '3000-4000元/月',\n        name: '赵女士',\n        avatar: '/static/images/avatar/女2.png',\n        education: '大专',\n        experience: '1年',\n        tags: ['有相关经验', '普通话标准', '较强沟通能力']\n      }\n    ];\n  }, 500);\n};\n\n// 跳转到求职详情页\nconst navigateToSeekingDetail = (id) => {\n  // 避免重复跳转当前页面\n  const pages = getCurrentPages();\n  const currentPage = pages[pages.length - 1];\n  const options = currentPage.options || {};\n  \n  if (id === options.id) {\n    return;\n  }\n  \n  uni.navigateTo({\n    url: `/pages/publish/job-seeking-detail?id=${id}`\n  });\n};\n\n// 跳转到求职列表页\nconst navigateToSeekingList = (e) => {\n  if (e) e.stopPropagation();\n  const seekingCategory = seekingData.value.tags?.[0] || '';\n  uni.navigateTo({\n    url: `/subPackages/service/pages/filter?type=job-seeking&title=${encodeURIComponent('求职信息')}&category=${encodeURIComponent(seekingCategory)}&active=resume`\n  });\n};\n\n// 方法\nconst toggleCollect = () => {\n  isCollected.value = !isCollected.value;\n  if (isCollected.value) {\n    uni.showToast({\n      title: '收藏成功',\n      icon: 'success'\n    });\n  }\n};\n\nconst showShareOptions = () => {\n  uni.showShareMenu({\n    withShareTicket: true,\n    menus: ['shareAppMessage', 'shareTimeline']\n  });\n};\n\nconst callPhone = () => {\n  uni.makePhoneCall({\n    phoneNumber: seekingData.value.contact.phone,\n    fail: () => {\n      uni.showToast({\n        title: '拨打电话失败',\n        icon: 'none'\n      });\n    }\n  });\n};\n\n// 跳转到首页\nconst goToHome = () => {\n  uni.switchTab({\n    url: '/pages/index/index'\n  });\n};\n\n// 打开私信聊天\nconst openChat = () => {\n  if (!seekingData.value.contact || !seekingData.value.contact.id) {\n    uni.showToast({\n      title: '无法获取求职者信息',\n      icon: 'none'\n    });\n    return;\n  }\n  \n  // 跳转到聊天页面\n  uni.navigateTo({\n    url: `/pages/chat/index?userId=${seekingData.value.contact.id}&username=${encodeURIComponent(seekingData.value.contact.name || '求职者')}`\n  });\n};\n\n// 生命周期钩子\nonMounted(() => {\n  // 修改页面标题\n  uni.setNavigationBarTitle({\n    title: '求职详情'\n  });\n  \n  // 获取状态栏高度\n  statusBarHeight.value = uni.getSystemInfoSync().statusBarHeight || 20;\n  \n  // 获取路由参数，并请求数据\n  const pages = getCurrentPages();\n  const currentPage = pages[pages.length - 1];\n  const options = currentPage.options || {};\n  \n  console.log('求职详情页参数:', options);\n  \n  // 如果有ID参数，则根据ID获取详情\n  if (options.id) {\n    // 尝试从缓存中获取数据\n    const cacheKey = `job_seeking_${options.id}`;\n    const cachedData = uni.getStorageSync(cacheKey);\n    \n    if (cachedData) {\n      console.log('从缓存获取求职数据:', cachedData);\n      try {\n        // 处理缓存的数据\n        const parsedData = typeof cachedData === 'string' ? JSON.parse(cachedData) : cachedData;\n        \n        // 更新到响应式数据中\n        seekingData.value = {\n          ...seekingData.value,\n          ...parsedData\n        };\n        \n        // 确保education字段正确解析\n        if (typeof seekingData.value.education === 'string') {\n          seekingData.value.education = parseEducation(seekingData.value.education);\n        }\n        \n        console.log('处理后的求职数据:', seekingData.value);\n      } catch (e) {\n        console.error('解析缓存数据失败:', e);\n      }\n    } else {\n      // 从服务器获取数据\n      console.log('从服务器获取求职详情:', options.id);\n      // 这里是模拟数据，实际项目中应该调用API获取\n      setTimeout(() => {\n        // 假设这是从服务器获取的数据\n        const serverData = {\n          // 服务端返回的数据...\n        };\n        \n        // 更新到响应式数据中\n        seekingData.value = {\n          ...seekingData.value,\n          ...serverData\n        };\n        \n        // 确保education字段正确解析\n        if (typeof seekingData.value.education === 'string') {\n          seekingData.value.education = parseEducation(seekingData.value.education);\n        }\n        \n        // 保存到缓存\n        uni.setStorageSync(cacheKey, JSON.stringify(seekingData.value));\n      }, 100);\n    }\n  }\n  \n  // 触发自动分享按钮 - 在小程序环境中使用小程序API\n  setTimeout(() => {\n    // 小程序环境不支持document.getElementById，注释掉这部分代码\n    // 或者使用uniapp提供的选择器API\n    try {\n      const query = uni.createSelectorQuery();\n      query.select('#shareButton').boundingClientRect(data => {\n        if (data) {\n          console.log('获取到分享按钮元素');\n          // 在小程序中可以使用其他方式触发分享\n        }\n      }).exec();\n    } catch (err) {\n      console.error('获取分享按钮失败:', err);\n    }\n  }, 500);\n  \n  // 加载相关求职推荐\n  loadRelatedSeekings();\n});\n\n// 提供页面分享信息\nconst onShareAppMessage = () => {\n  return {\n    title: seekingData.value.title,\n    path: `/pages/publish/job-seeking-detail?id=${seekingData.value.id}`\n  };\n};\n\ndefineExpose({\n  onShareAppMessage\n});\n\n// 海报相关数据\nconst posterImagePath = ref('');\nconst showPosterFlag = ref(false);\n\n// 生成海报的方法\nconst generateShareImage = () => {\n  uni.showLoading({\n    title: '生成中...'\n  });\n  \n  // 创建海报数据对象\n  const posterData = {\n    title: seekingData.value.title,\n    salary: seekingData.value.salary,\n    name: seekingData.value.name,\n    gender: '女',\n    age: seekingData.value.age,\n    education: seekingData.value.education,\n    experience: seekingData.value.experience,\n    phone: seekingData.value.contact.phone,\n    skills: seekingData.value.skills ? seekingData.value.skills.substring(0, 60) + '...' : '',\n    qrcode: '/static/images/tabbar/客服微信.png',\n    logo: '/static/images/tabbar/求职.png',\n    bgImage: '/static/images/banner/banner-1.png'\n  };\n  \n  // #ifdef H5\n  // H5环境不支持canvas绘制图片保存，提示用户\n  setTimeout(() => {\n    uni.hideLoading();\n    uni.showModal({\n      title: '提示',\n      content: 'H5环境暂不支持保存海报，请使用App或小程序',\n      showCancel: false\n    });\n  }, 1000);\n  return;\n  // #endif\n  \n  // 绘制海报\n  const ctx = uni.createCanvasContext('posterCanvas');\n  \n  // 绘制背景\n  ctx.save();\n  ctx.drawImage(posterData.bgImage, 0, 0, 600, 400);\n  // 添加半透明蒙层\n  ctx.setFillStyle('rgba(0, 0, 0, 0.35)');\n  ctx.fillRect(0, 0, 600, 900);\n  ctx.restore();\n  \n  // 绘制白色卡片背景\n  ctx.save();\n  ctx.setFillStyle('#ffffff');\n  ctx.fillRect(30, 280, 540, 550);\n  ctx.restore();\n  \n  // 绘制Logo\n  ctx.save();\n  ctx.beginPath();\n  ctx.arc(300, 200, 80, 0, 2 * Math.PI);\n  ctx.setFillStyle('#ffffff');\n  ctx.fill();\n  // 在圆形内绘制Logo\n  ctx.clip();\n  ctx.drawImage(posterData.logo, 220, 120, 160, 160);\n  ctx.restore();\n  \n  // 绘制期望岗位\n  ctx.setFillStyle('#333333');\n  ctx.setFontSize(32);\n  ctx.setTextAlign('center');\n  ctx.fillText(posterData.title, 300, 350);\n  \n  // 绘制期望薪资\n  ctx.setFillStyle('#FF6B6B');\n  ctx.setFontSize(28);\n  ctx.fillText(posterData.salary, 300, 400);\n  \n  // 分割线\n  ctx.beginPath();\n  ctx.setStrokeStyle('#eeeeee');\n  ctx.setLineWidth(2);\n  ctx.moveTo(100, 430);\n  ctx.lineTo(500, 430);\n  ctx.stroke();\n  \n  // 绘制个人信息\n  ctx.setFillStyle('#666666');\n  ctx.setFontSize(24);\n  ctx.setTextAlign('left');\n  \n  const personInfo = `${posterData.name} | ${posterData.gender} | ${posterData.age}岁`;\n  ctx.fillText(personInfo, 80, 480);\n  \n  // 绘制学历和工作经验\n  const eduExp = `${posterData.education} | ${posterData.experience}`;\n  ctx.fillText(eduExp, 80, 520);\n  \n  // A wrap text function\n  const wrapText = (ctx, text, x, y, maxWidth, lineHeight) => {\n    if (text.length === 0) return;\n    \n    const words = text.split('');\n    let line = '';\n    let testLine = '';\n    let lineCount = 0;\n    \n    for (let n = 0; n < words.length; n++) {\n      testLine += words[n];\n      const metrics = ctx.measureText(testLine);\n      const testWidth = metrics.width;\n      \n      if (testWidth > maxWidth && n > 0) {\n        ctx.fillText(line, x, y + (lineCount * lineHeight));\n        line = words[n];\n        testLine = words[n];\n        lineCount++;\n        \n        if (lineCount >= 3) {\n          line += '...';\n          ctx.fillText(line, x, y + (lineCount * lineHeight));\n          break;\n        }\n      } else {\n        line = testLine;\n      }\n    }\n    \n    if (lineCount < 3) {\n      ctx.fillText(line, x, y + (lineCount * lineHeight));\n    }\n  };\n  \n  // 绘制技能特长\n  ctx.setFillStyle('#666666');\n  ctx.fillText('技能特长:', 80, 560);\n  wrapText(ctx, posterData.skills, 80, 600, 440, 35);\n  \n  // 绘制电话\n  if (posterData.phone) {\n    ctx.fillText('联系电话: ' + posterData.phone, 80, 680);\n  }\n  \n  // 绘制小程序码\n  ctx.drawImage(posterData.qrcode, 225, 720, 150, 150);\n  \n  // 提示文字\n  ctx.setFillStyle('#999999');\n  ctx.setFontSize(20);\n  ctx.setTextAlign('center');\n  ctx.fillText('长按识别二维码查看详情', 300, 880);\n  \n  // 应用平台Logo\n  ctx.setFillStyle('#333333');\n  ctx.setFontSize(24);\n  ctx.fillText('磁县同城 - 求职信息', 300, 840);\n  \n  // 绘制完成，输出图片\n  ctx.draw(false, () => {\n    setTimeout(() => {\n      // 延迟确保canvas已完成渲染\n      uni.canvasToTempFilePath({\n        canvasId: 'posterCanvas',\n        success: (res) => {\n          uni.hideLoading();\n          showPosterModal(res.tempFilePath);\n        },\n        fail: (err) => {\n          console.error('生成海报失败', err);\n          uni.hideLoading();\n          uni.showToast({\n            title: '生成海报失败',\n            icon: 'none'\n          });\n        }\n      });\n    }, 800);\n  });\n};\n\n// 显示海报预览和保存选项\nconst showPosterModal = (posterPath) => {\n  posterImagePath.value = posterPath;\n  showPosterFlag.value = true;\n  \n  uni.showModal({\n    title: '海报已生成',\n    content: '海报已生成，是否保存到相册？',\n    confirmText: '保存',\n    success: (res) => {\n      if (res.confirm) {\n        savePosterToAlbum(posterPath);\n      } else {\n        // 预览图片\n        uni.previewImage({\n          urls: [posterPath],\n          current: posterPath\n        });\n      }\n    }\n  });\n};\n\n// 保存海报到相册\nconst savePosterToAlbum = (posterPath) => {\n  uni.showLoading({\n    title: '正在保存...'\n  });\n  \n  uni.saveImageToPhotosAlbum({\n    filePath: posterPath,\n    success: () => {\n      uni.hideLoading();\n      uni.showToast({\n        title: '已保存到相册',\n        icon: 'success'\n      });\n    },\n    fail: (err) => {\n      uni.hideLoading();\n      console.error('保存失败', err);\n      \n      if (err.errMsg.indexOf('auth deny') > -1) {\n        uni.showModal({\n          title: '提示',\n          content: '保存失败，请授权相册权限后重试',\n          confirmText: '去设置',\n          success: (res) => {\n            if (res.confirm) {\n              uni.openSetting();\n            }\n          }\n        });\n      } else {\n        uni.showToast({\n          title: '保存失败',\n          icon: 'none'\n        });\n      }\n    }\n  });\n};\n\n// 举报相关\nconst showReportOptions = () => {\n  uni.showActionSheet({\n    itemList: ['虚假信息', '违法内容', '色情内容', '侵权投诉', '诱导欺骗', '其他问题'],\n    success: (res) => {\n      const reportReasonIndex = res.tapIndex;\n      const reasons = ['虚假信息', '违法内容', '色情内容', '侵权投诉', '诱导欺骗', '其他问题'];\n      showReportInputDialog(reasons[reportReasonIndex]);\n    }\n  });\n};\n\nconst showReportInputDialog = (reason) => {\n  // 检查是否登录\n  const hasLogin = uni.getStorageSync('token') || false;\n  if (!hasLogin) {\n    uni.showModal({\n      title: '提示',\n      content: '请先登录后再进行举报',\n      confirmText: '去登录',\n      success: (res) => {\n        if (res.confirm) {\n          uni.navigateTo({\n            url: '/pages/login/login'\n          });\n        }\n      }\n    });\n    return;\n  }\n  \n  uni.showModal({\n    title: '举报内容',\n    content: `您选择的举报原因是: ${reason}，请确认是否提交举报？`,\n    confirmText: '确认举报',\n    success: (res) => {\n      if (res.confirm) {\n        submitReport(reason, '');\n      }\n    }\n  });\n};\n\nconst submitReport = (reason, content) => {\n  uni.showLoading({\n    title: '提交中...'\n  });\n  \n  // 这里模拟提交举报信息到服务器\n  setTimeout(() => {\n    uni.hideLoading();\n    \n    uni.showToast({\n      title: '举报成功',\n      icon: 'success'\n    });\n    \n    // 实际开发中，这里应该调用API提交举报信息\n    // const reportData = {\n    //   type: 'jobSeeking',\n    //   id: seekingData.value.id,\n    //   reason: reason,\n    //   content: content\n    // };\n    // submitReportAPI(reportData).then(() => {\n    //   uni.showToast({\n    //     title: '举报成功',\n    //     icon: 'success'\n    //   });\n    // });\n  }, 1500);\n};\n</script>\n\n<style>\n.job-seeking-container {\n  min-height: 100vh;\n  background-color: #f5f7fa;\n  padding-bottom: 110rpx;\n}\n\n.job-seeking-wrapper {\n  padding: 150rpx 20rpx 120rpx; /* 增加顶部内边距，为固定导航栏留出空间 */\n}\n\n/* 自定义导航栏样式 */\n.custom-navbar {\n  background: linear-gradient(135deg, #0066FF, #0052CC);\n  height: 88rpx;\n  padding-top: 44px; /* 状态栏高度 */\n  display: flex;\n  align-items: center;\n  position: fixed; /* 改为固定定位 */\n  top: 0;\n  left: 0;\n  right: 0;\n  padding-bottom: 10rpx;\n  box-shadow: 0 4rpx 12rpx rgba(0, 82, 204, 0.2);\n  z-index: 100; /* 提高z-index确保在最上层 */\n}\n\n.navbar-left {\n  width: 60rpx;\n  height: 44px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  background-color: transparent;\n  border-radius: 50%;\n}\n\n.navbar-title {\n  flex: 1;\n  text-align: center;\n  font-size: 16px;\n  font-weight: 500;\n  color: #FFFFFF; /* 修改为白色字体 */\n}\n\n.navbar-right {\n  display: flex;\n  align-items: center;\n  padding-right: 20rpx;\n}\n\n/* 悬浮举报按钮样式已删除 */\n/*\n.report-btn {\n  background-color: rgba(255, 255, 255, 0.8);\n  border-radius: 30rpx;\n  padding: 6rpx 16rpx;\n  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.1);\n}\n\n.report-text {\n  font-size: 24rpx;\n  color: #FF5151;\n}\n*/\n\n.content-card {\n  background-color: #fff;\n  border-radius: 16rpx;\n  margin-bottom: 24rpx;\n  padding: 30rpx;\n  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);\n  margin-top: 20rpx; /* 添加与标题栏的间隙 */\n}\n\n/* 求职基本信息卡片 */\n.seeking-title-row {\n  display: none;\n}\n\n.seeking-title {\n  font-size: 36rpx;\n  font-weight: 600;\n  color: #333;\n  margin-bottom: 16rpx;\n}\n\n.seeking-salary {\n  font-size: 36rpx;\n  font-weight: 600;\n  color: #ff4d4f;\n  margin-bottom: 16rpx;\n}\n\n.seeking-meta {\n  margin-bottom: 24rpx;\n}\n\n.seeking-tag-group {\n  display: flex;\n  flex-wrap: wrap;\n  margin-bottom: 12rpx;\n}\n\n.seeking-tag {\n  font-size: 24rpx;\n  color: #1890ff;\n  background-color: rgba(24, 144, 255, 0.1);\n  padding: 4rpx 16rpx;\n  border-radius: 6rpx;\n  margin-right: 16rpx;\n  margin-bottom: 12rpx;\n}\n\n.seeking-publish-time {\n  font-size: 24rpx;\n  color: #999;\n}\n\n/* 基本信息 */\n.seeking-basic-info {\n  display: flex;\n  flex-wrap: wrap;\n  padding: 24rpx;\n  background-color: #f9fafc;\n  border-radius: 12rpx;\n}\n\n.info-item {\n  width: 50%;\n  padding: 12rpx 24rpx;\n  box-sizing: border-box;\n}\n\n.info-label {\n  font-size: 26rpx;\n  color: #999;\n  margin-bottom: 8rpx;\n  display: block;\n}\n\n.info-value {\n  font-size: 28rpx;\n  color: #333;\n  font-weight: 500;\n}\n\n/* 求职意向 */\n.intention-list {\n  display: flex;\n  flex-direction: column;\n}\n\n.intention-item {\n  display: flex;\n  padding: 16rpx 0;\n  border-bottom: 1rpx solid #f0f0f0;\n}\n\n.intention-item:last-child {\n  border-bottom: none;\n}\n\n.intention-label {\n  width: 160rpx;\n  font-size: 28rpx;\n  color: #999;\n}\n\n.intention-value {\n  flex: 1;\n  font-size: 28rpx;\n  color: #333;\n}\n\n/* 工作经验 */\n.experience-list {\n  display: flex;\n  flex-direction: column;\n}\n\n.experience-item {\n  padding: 20rpx 0;\n  border-bottom: 1rpx solid #f0f0f0;\n}\n\n.experience-item:last-child {\n  border-bottom: none;\n}\n\n.experience-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 8rpx;\n}\n\n.experience-company {\n  font-size: 30rpx;\n  font-weight: 500;\n  color: #333;\n}\n\n.experience-time {\n  font-size: 24rpx;\n  color: #999;\n}\n\n.experience-position {\n  font-size: 28rpx;\n  color: #666;\n  margin-bottom: 8rpx;\n}\n\n.experience-desc {\n  font-size: 26rpx;\n  color: #666;\n  line-height: 1.6;\n}\n\n/* 教育背景 */\n.education-list {\n  display: flex;\n  flex-direction: column;\n}\n\n.education-item {\n  padding: 20rpx 0;\n  border-bottom: 1rpx solid #f0f0f0;\n}\n\n.education-item:last-child {\n  border-bottom: none;\n}\n\n.education-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 8rpx;\n}\n\n.education-school {\n  font-size: 30rpx;\n  font-weight: 500;\n  color: #333;\n}\n\n.education-time {\n  font-size: 24rpx;\n  color: #999;\n}\n\n.education-major {\n  font-size: 28rpx;\n  color: #666;\n  margin-bottom: 8rpx;\n}\n\n.education-degree {\n  font-size: 26rpx;\n  color: #666;\n}\n\n/* 技能特长 */\n.skills-content {\n  display: flex;\n  flex-wrap: wrap;\n  margin: 0 -8rpx;\n}\n\n.skill-tag {\n  font-size: 26rpx;\n  color: #1890ff;\n  background-color: rgba(24, 144, 255, 0.1);\n  padding: 8rpx 20rpx;\n  border-radius: 6rpx;\n  margin: 8rpx;\n}\n\n/* 自我评价 */\n.evaluation-content {\n  padding: 24rpx;\n  background-color: #f9fafc;\n  border-radius: 12rpx;\n}\n\n.evaluation-text {\n  font-size: 28rpx;\n  color: #666;\n  line-height: 1.6;\n}\n\n/* 相似求职 */\n.similar-list {\n  display: flex;\n  flex-direction: column;\n}\n\n.similar-item {\n  padding: 20rpx;\n  background-color: #f9fafc;\n  border-radius: 12rpx;\n  margin-bottom: 16rpx;\n}\n\n.similar-seeking-info {\n  display: flex;\n  flex-direction: column;\n}\n\n.similar-seeking-title {\n  font-size: 28rpx;\n  font-weight: 500;\n  color: #333;\n  margin-bottom: 8rpx;\n}\n\n.similar-seeking-salary {\n  font-size: 28rpx;\n  color: #ff4d4f;\n  margin-bottom: 8rpx;\n}\n\n.similar-seeking-meta {\n  font-size: 24rpx;\n  color: #999;\n}\n\n/* 无数据提示 */\n.no-data-tip {\n  font-size: 28rpx;\n  color: #999;\n  text-align: center;\n  padding: 30rpx 0;\n}\n\n/* 底部互动工具栏 */\n.interaction-toolbar {\n  position: fixed;\n  bottom: 0;\n  left: 0;\n  right: 0;\n  background-color: #fff;\n  padding: 10rpx 10rpx;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  border-top: 1rpx solid #f0f0f0;\n  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);\n  height: 120rpx;\n  z-index: 100;\n}\n\n.toolbar-item {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: 6rpx 0;\n  margin: 0 4rpx;\n}\n\n.toolbar-icon {\n  width: 44rpx;\n  height: 44rpx;\n  margin-bottom: 6rpx;\n}\n\n.toolbar-text {\n  font-size: 22rpx;\n  color: #666;\n}\n\n.share-button {\n  background: transparent;\n  border: none;\n  margin: 0;\n  padding: 0;\n  line-height: normal;\n  border-radius: 0;\n  flex: 1;\n}\n\n.share-button::after {\n  display: none;\n}\n\n.call-button {\n  flex: 3; /* 占据更多空间，原来是2，现在改为3让按钮更长 */\n  background: linear-gradient(135deg, #0052CC, #0066FF);\n  height: 90rpx;\n  margin: 0 0 0 10rpx;\n  border-radius: 45rpx;\n  box-shadow: 0 4rpx 12rpx rgba(0, 82, 204, 0.2);\n}\n\n.call-button-content {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  height: 100%;\n}\n\n.call-text {\n  color: #fff;\n  font-size: 30rpx;\n  font-weight: bold;\n  line-height: 1.2;\n}\n\n.call-subtitle {\n  color: rgba(255, 255, 255, 0.9);\n  font-size: 20rpx;\n  line-height: 1.2;\n}\n\n/* 隐藏原来的底部操作栏 */\n.action-bar {\n  display: none;\n}\n\n/* 隐藏的分享按钮 */\n.hidden-share-btn {\n  position: absolute;\n  top: -9999rpx;\n  left: -9999rpx;\n  width: 0;\n  height: 0;\n  padding: 0;\n  margin: 0;\n  opacity: 0;\n}\n\n.back-icon {\n  width: 36rpx;\n  height: 36rpx;\n  display: block;\n}\n\n/* 悬浮海报按钮 */\n.float-poster-btn {\n  position: fixed;\n  right: 30rpx;\n  bottom: 200rpx;\n  width: 100rpx;\n  height: 100rpx;\n  background: rgba(240, 240, 240, 0.9);\n  border-radius: 50%;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  box-shadow: 0 6rpx 20rpx rgba(0,0,0,0.08);\n  z-index: 90;\n  backdrop-filter: blur(10px);\n  -webkit-backdrop-filter: blur(10px);\n  border: 1rpx solid rgba(230, 230, 230, 0.6);\n  transition: all 0.2s ease;\n}\n\n.float-poster-btn:active {\n  transform: scale(0.95);\n  box-shadow: 0 3rpx 10rpx rgba(0,0,0,0.08);\n}\n\n.poster-icon {\n  width: 40rpx;\n  height: 40rpx;\n  margin-bottom: 4rpx;\n}\n\n.poster-text {\n  font-size: 20rpx;\n  color: #444;\n  line-height: 1;\n}\n\n/* 区块标题样式优化 - 添加蓝色竖线 */\n.section-title {\n  font-size: 30rpx;\n  font-weight: 600;\n  color: #333;\n  margin-bottom: 20rpx;\n  position: relative;\n  padding-left: 16rpx;\n}\n\n.section-title::before {\n  content: '';\n  position: absolute;\n  left: 0;\n  top: 6rpx;\n  width: 6rpx;\n  height: 28rpx;\n  background-color: #1890ff;\n  border-radius: 3rpx;\n}\n\n/* 联系方式样式 - 电话显示为绿色，提示为黄色 */\n.contact-phone {\n  color: #52c41a; /* 绿色 */\n  font-weight: 500;\n}\n\n.contact-tips {\n  display: flex;\n  align-items: center;\n  margin-top: 16rpx;\n  padding: 10rpx 16rpx;\n  background-color: rgba(255, 152, 0, 0.1);\n  border-radius: 8rpx;\n}\n\n.tips-icon {\n  font-size: 24rpx;\n  color: #ff9800; /* 黄色 */\n  margin-right: 8rpx;\n}\n\n.tips-text {\n  font-size: 24rpx;\n  color: #ff9800; /* 黄色 */\n}\n\n/* 相关求职推荐样式 */\n.related-seekings-card {\n  margin-top: 12px;\n  background-color: #fff;\n  border-radius: 12px;\n  overflow: hidden;\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);\n}\n\n/* 可折叠标题栏样式 */\n.collapsible-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 16px;\n  background-color: #fff;\n  position: relative;\n  cursor: pointer;\n}\n\n/* 相关求职列表样式 */\n.related-seekings-list {\n  margin-bottom: 12px;\n}\n\n.related-seeking-item {\n  padding: 12px 0;\n  border-bottom: 1px solid #f5f5f5;\n}\n\n.related-seeking-item:last-child {\n  border-bottom: none;\n}\n\n.seeking-item-content {\n  display: flex;\n  align-items: center;\n}\n\n.seeking-item-left {\n  margin-right: 12px;\n}\n\n.seeker-avatar {\n  width: 40px;\n  height: 40px;\n  border-radius: 8px;\n  background-color: #f5f7fa;\n}\n\n.seeking-item-middle {\n  flex: 1;\n  overflow: hidden;\n}\n\n.seeking-item-title {\n  font-size: 15px;\n  font-weight: 500;\n  color: #333;\n  margin-bottom: 4px;\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n}\n\n.seeking-item-person {\n  font-size: 13px;\n  color: #666;\n  margin-bottom: 6px;\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n}\n\n.seeking-item-tags {\n  display: flex;\n  flex-wrap: wrap;\n}\n\n.seeking-item-tag {\n  font-size: 11px;\n  color: #1890ff;\n  background-color: rgba(24, 144, 255, 0.1);\n  padding: 2px 6px;\n  border-radius: 4px;\n  margin-right: 6px;\n}\n\n.seeking-item-tag-more {\n  font-size: 11px;\n  color: #999;\n}\n\n.seeking-item-right {\n  min-width: 80px;\n  text-align: right;\n}\n\n.seeking-item-salary {\n  font-size: 15px;\n  font-weight: 500;\n  color: #ff5252;\n}\n\n/* 查看更多按钮样式 */\n.view-more-btn {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  height: 44px;\n  background-color: #f7f9fc;\n  border-radius: 8px;\n  margin-top: 8px;\n}\n\n.view-more-text {\n  font-size: 14px;\n  color: #1890ff;\n}\n\n.view-more-icon {\n  margin-left: 4px;\n  font-size: 12px;\n  color: #1890ff;\n}\n\n/* 折叠内容区样式 */\n.collapsible-content {\n  padding: 0 16px 16px;\n  overflow: hidden;\n}\n\n/* 空数据提示样式 */\n.empty-related-seekings {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: 24px 0;\n}\n\n.empty-image {\n  width: 80px;\n  height: 80px;\n  margin-bottom: 12px;\n}\n\n.empty-text {\n  font-size: 14px;\n  color: #999;\n}\n</style> ", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/pages/publish/job-seeking-detail.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "uni", "onMounted", "ctx", "MiniProgramPage"], "mappings": ";;;;;;AAuNA,MAAA,aAAA,MAAA;;;;AAGA,UAAA,aAAA,CAAA,cAAA;AACA,YAAA,OAAA,IAAA,KAAA,SAAA;AACA,aAAA,GAAA,KAAA,aAAA,CAAA,IAAA,KAAA,SAAA;AAAA,IACA;AAGA,UAAA,iBAAA,CAAA,cAAA;AAEA,UAAA,OAAA,cAAA,UAAA;AACA,eAAA;AAAA,MACA;AAGA,YAAA,eAAA;AAAA,QACA,KAAA;AAAA,QACA,KAAA;AAAA,QACA,KAAA;AAAA,QACA,KAAA;AAAA,QACA,KAAA;AAAA,QACA,KAAA;AAAA,QACA,KAAA;AAAA,QACA,KAAA;AAAA,MACA;AAGA,UAAA,CAAA,MAAA,SAAA,KAAA,aAAA,SAAA,GAAA;AACA,eAAA,aAAA,SAAA;AAAA,MACA;AAGA,aAAA;AAAA,IACA;AAGA,UAAA,kBAAAA,cAAAA,IAAA,EAAA;AAGA,UAAA,SAAA,MAAA;AACAC,oBAAA,MAAA,aAAA;AAAA,IACA;AAGA,UAAA,cAAAD,cAAAA,IAAA,KAAA;AACA,UAAA,cAAAA,cAAAA,IAAA;AAAA,MACA,IAAA;AAAA,MACA,OAAA;AAAA,MACA,QAAA;AAAA,MACA,MAAA,CAAA,QAAA,OAAA,OAAA;AAAA,MACA,aAAA,KAAA,IAAA,IAAA;AAAA;AAAA,MACA,MAAA;AAAA,MACA,KAAA;AAAA,MACA,WAAA;AAAA,MACA,YAAA;AAAA,MACA,YAAA;AAAA,QACA,EAAA,OAAA,QAAA,OAAA,OAAA;AAAA,QACA,EAAA,OAAA,QAAA,OAAA,eAAA;AAAA,QACA,EAAA,OAAA,QAAA,OAAA,KAAA;AAAA,QACA,EAAA,OAAA,QAAA,OAAA,KAAA;AAAA,MACA;AAAA,MACA,gBAAA;AAAA,QACA;AAAA,UACA,SAAA;AAAA,UACA,MAAA;AAAA,UACA,UAAA;AAAA,UACA,aAAA;AAAA,QACA;AAAA,MACA;AAAA,MACA,eAAA;AAAA,QACA;AAAA,UACA,QAAA;AAAA,UACA,MAAA;AAAA,UACA,OAAA;AAAA,UACA,QAAA;AAAA,QACA;AAAA,MACA;AAAA,MACA,QAAA,CAAA,QAAA,QAAA,QAAA,MAAA;AAAA,MACA,gBAAA;AAAA,MACA,SAAA;AAAA,QACA,MAAA;AAAA,QACA,OAAA;AAAA,MACA;AAAA,IACA,CAAA;AAGA,UAAA,kBAAAA,cAAAA,IAAA,CAAA,CAAA;AAGA,UAAA,sBAAA,MAAA;AAKA,iBAAA,MAAA;AACA,wBAAA,QAAA;AAAA,UACA;AAAA,YACA,IAAA;AAAA,YACA,OAAA;AAAA,YACA,QAAA;AAAA,YACA,MAAA;AAAA,YACA,QAAA;AAAA,YACA,WAAA;AAAA,YACA,YAAA;AAAA,YACA,MAAA,CAAA,QAAA,MAAA,KAAA;AAAA,UACA;AAAA,UACA;AAAA,YACA,IAAA;AAAA,YACA,OAAA;AAAA,YACA,QAAA;AAAA,YACA,MAAA;AAAA,YACA,QAAA;AAAA,YACA,WAAA;AAAA,YACA,YAAA;AAAA,YACA,MAAA,CAAA,UAAA,QAAA,OAAA;AAAA,UACA;AAAA,UACA;AAAA,YACA,IAAA;AAAA,YACA,OAAA;AAAA,YACA,QAAA;AAAA,YACA,MAAA;AAAA,YACA,QAAA;AAAA,YACA,WAAA;AAAA,YACA,YAAA;AAAA,YACA,MAAA,CAAA,SAAA,SAAA,QAAA;AAAA,UACA;AAAA,QACA;AAAA,MACA,GAAA,GAAA;AAAA,IACA;AAGA,UAAA,0BAAA,CAAA,OAAA;AAEA,YAAA,QAAA;AACA,YAAA,cAAA,MAAA,MAAA,SAAA,CAAA;AACA,YAAA,UAAA,YAAA,WAAA;AAEA,UAAA,OAAA,QAAA,IAAA;AACA;AAAA,MACA;AAEAC,oBAAAA,MAAA,WAAA;AAAA,QACA,KAAA,wCAAA,EAAA;AAAA,MACA,CAAA;AAAA,IACA;AAGA,UAAA,wBAAA,CAAA,MAAA;;AACA,UAAA;AAAA,UAAA;AACA,YAAA,oBAAA,iBAAA,MAAA,SAAA,mBAAA,OAAA;AACAA,oBAAAA,MAAA,WAAA;AAAA,QACA,KAAA,4DAAA,mBAAA,MAAA,CAAA,aAAA,mBAAA,eAAA,CAAA;AAAA,MACA,CAAA;AAAA,IACA;AAGA,UAAA,gBAAA,MAAA;AACA,kBAAA,QAAA,CAAA,YAAA;AACA,UAAA,YAAA,OAAA;AACAA,sBAAAA,MAAA,UAAA;AAAA,UACA,OAAA;AAAA,UACA,MAAA;AAAA,QACA,CAAA;AAAA,MACA;AAAA,IACA;AASA,UAAA,YAAA,MAAA;AACAA,oBAAAA,MAAA,cAAA;AAAA,QACA,aAAA,YAAA,MAAA,QAAA;AAAA,QACA,MAAA,MAAA;AACAA,wBAAAA,MAAA,UAAA;AAAA,YACA,OAAA;AAAA,YACA,MAAA;AAAA,UACA,CAAA;AAAA,QACA;AAAA,MACA,CAAA;AAAA,IACA;AAGA,UAAA,WAAA,MAAA;AACAA,oBAAAA,MAAA,UAAA;AAAA,QACA,KAAA;AAAA,MACA,CAAA;AAAA,IACA;AAGA,UAAA,WAAA,MAAA;AACA,UAAA,CAAA,YAAA,MAAA,WAAA,CAAA,YAAA,MAAA,QAAA,IAAA;AACAA,sBAAAA,MAAA,UAAA;AAAA,UACA,OAAA;AAAA,UACA,MAAA;AAAA,QACA,CAAA;AACA;AAAA,MACA;AAGAA,oBAAAA,MAAA,WAAA;AAAA,QACA,KAAA,4BAAA,YAAA,MAAA,QAAA,EAAA,aAAA,mBAAA,YAAA,MAAA,QAAA,QAAA,KAAA,CAAA;AAAA,MACA,CAAA;AAAA,IACA;AAGAC,kBAAAA,UAAA,MAAA;AAEAD,oBAAAA,MAAA,sBAAA;AAAA,QACA,OAAA;AAAA,MACA,CAAA;AAGA,sBAAA,QAAAA,cAAA,MAAA,kBAAA,EAAA,mBAAA;AAGA,YAAA,QAAA;AACA,YAAA,cAAA,MAAA,MAAA,SAAA,CAAA;AACA,YAAA,UAAA,YAAA,WAAA;AAEAA,oBAAA,MAAA,MAAA,OAAA,+CAAA,YAAA,OAAA;AAGA,UAAA,QAAA,IAAA;AAEA,cAAA,WAAA,eAAA,QAAA,EAAA;AACA,cAAA,aAAAA,cAAAA,MAAA,eAAA,QAAA;AAEA,YAAA,YAAA;AACAA,wBAAA,MAAA,MAAA,OAAA,+CAAA,cAAA,UAAA;AACA,cAAA;AAEA,kBAAA,aAAA,OAAA,eAAA,WAAA,KAAA,MAAA,UAAA,IAAA;AAGA,wBAAA,QAAA;AAAA,cACA,GAAA,YAAA;AAAA,cACA,GAAA;AAAA,YACA;AAGA,gBAAA,OAAA,YAAA,MAAA,cAAA,UAAA;AACA,0BAAA,MAAA,YAAA,eAAA,YAAA,MAAA,SAAA;AAAA,YACA;AAEAA,0BAAA,MAAA,MAAA,OAAA,+CAAA,aAAA,YAAA,KAAA;AAAA,UACA,SAAA,GAAA;AACAA,0BAAA,MAAA,MAAA,SAAA,+CAAA,aAAA,CAAA;AAAA,UACA;AAAA,QACA,OAAA;AAEAA,wBAAA,MAAA,MAAA,OAAA,+CAAA,eAAA,QAAA,EAAA;AAEA,qBAAA,MAAA;AAEA,kBAAA,aAAA;AAAA;AAAA,YAEA;AAGA,wBAAA,QAAA;AAAA,cACA,GAAA,YAAA;AAAA,cACA,GAAA;AAAA,YACA;AAGA,gBAAA,OAAA,YAAA,MAAA,cAAA,UAAA;AACA,0BAAA,MAAA,YAAA,eAAA,YAAA,MAAA,SAAA;AAAA,YACA;AAGAA,0BAAA,MAAA,eAAA,UAAA,KAAA,UAAA,YAAA,KAAA,CAAA;AAAA,UACA,GAAA,GAAA;AAAA,QACA;AAAA,MACA;AAGA,iBAAA,MAAA;AAGA,YAAA;AACA,gBAAA,QAAAA,oBAAA;AACA,gBAAA,OAAA,cAAA,EAAA,mBAAA,UAAA;AACA,gBAAA,MAAA;AACAA,4BAAAA,MAAA,MAAA,OAAA,+CAAA,WAAA;AAAA,YAEA;AAAA,UACA,CAAA,EAAA,KAAA;AAAA,QACA,SAAA,KAAA;AACAA,wBAAA,MAAA,MAAA,SAAA,+CAAA,aAAA,GAAA;AAAA,QACA;AAAA,MACA,GAAA,GAAA;AAGA;IACA,CAAA;AAGA,UAAA,oBAAA,MAAA;AACA,aAAA;AAAA,QACA,OAAA,YAAA,MAAA;AAAA,QACA,MAAA,wCAAA,YAAA,MAAA,EAAA;AAAA,MACA;AAAA,IACA;AAEA,aAAA;AAAA,MACA;AAAA,IACA,CAAA;AAGA,UAAA,kBAAAD,cAAAA,IAAA,EAAA;AACA,UAAA,iBAAAA,cAAAA,IAAA,KAAA;AAGA,UAAA,qBAAA,MAAA;AACAC,oBAAAA,MAAA,YAAA;AAAA,QACA,OAAA;AAAA,MACA,CAAA;AAGA,YAAA,aAAA;AAAA,QACA,OAAA,YAAA,MAAA;AAAA,QACA,QAAA,YAAA,MAAA;AAAA,QACA,MAAA,YAAA,MAAA;AAAA,QACA,QAAA;AAAA,QACA,KAAA,YAAA,MAAA;AAAA,QACA,WAAA,YAAA,MAAA;AAAA,QACA,YAAA,YAAA,MAAA;AAAA,QACA,OAAA,YAAA,MAAA,QAAA;AAAA,QACA,QAAA,YAAA,MAAA,SAAA,YAAA,MAAA,OAAA,UAAA,GAAA,EAAA,IAAA,QAAA;AAAA,QACA,QAAA;AAAA,QACA,MAAA;AAAA,QACA,SAAA;AAAA,MACA;AAgBA,YAAA,MAAAA,cAAAA,MAAA,oBAAA,cAAA;AAGA,UAAA,KAAA;AACA,UAAA,UAAA,WAAA,SAAA,GAAA,GAAA,KAAA,GAAA;AAEA,UAAA,aAAA,qBAAA;AACA,UAAA,SAAA,GAAA,GAAA,KAAA,GAAA;AACA,UAAA,QAAA;AAGA,UAAA,KAAA;AACA,UAAA,aAAA,SAAA;AACA,UAAA,SAAA,IAAA,KAAA,KAAA,GAAA;AACA,UAAA,QAAA;AAGA,UAAA,KAAA;AACA,UAAA,UAAA;AACA,UAAA,IAAA,KAAA,KAAA,IAAA,GAAA,IAAA,KAAA,EAAA;AACA,UAAA,aAAA,SAAA;AACA,UAAA,KAAA;AAEA,UAAA,KAAA;AACA,UAAA,UAAA,WAAA,MAAA,KAAA,KAAA,KAAA,GAAA;AACA,UAAA,QAAA;AAGA,UAAA,aAAA,SAAA;AACA,UAAA,YAAA,EAAA;AACA,UAAA,aAAA,QAAA;AACA,UAAA,SAAA,WAAA,OAAA,KAAA,GAAA;AAGA,UAAA,aAAA,SAAA;AACA,UAAA,YAAA,EAAA;AACA,UAAA,SAAA,WAAA,QAAA,KAAA,GAAA;AAGA,UAAA,UAAA;AACA,UAAA,eAAA,SAAA;AACA,UAAA,aAAA,CAAA;AACA,UAAA,OAAA,KAAA,GAAA;AACA,UAAA,OAAA,KAAA,GAAA;AACA,UAAA,OAAA;AAGA,UAAA,aAAA,SAAA;AACA,UAAA,YAAA,EAAA;AACA,UAAA,aAAA,MAAA;AAEA,YAAA,aAAA,GAAA,WAAA,IAAA,MAAA,WAAA,MAAA,MAAA,WAAA,GAAA;AACA,UAAA,SAAA,YAAA,IAAA,GAAA;AAGA,YAAA,SAAA,GAAA,WAAA,SAAA,MAAA,WAAA,UAAA;AACA,UAAA,SAAA,QAAA,IAAA,GAAA;AAGA,YAAA,WAAA,CAAAE,MAAA,MAAA,GAAA,GAAA,UAAA,eAAA;AACA,YAAA,KAAA,WAAA;AAAA;AAEA,cAAA,QAAA,KAAA,MAAA,EAAA;AACA,YAAA,OAAA;AACA,YAAA,WAAA;AACA,YAAA,YAAA;AAEA,iBAAA,IAAA,GAAA,IAAA,MAAA,QAAA,KAAA;AACA,sBAAA,MAAA,CAAA;AACA,gBAAA,UAAAA,KAAA,YAAA,QAAA;AACA,gBAAA,YAAA,QAAA;AAEA,cAAA,YAAA,YAAA,IAAA,GAAA;AACA,YAAAA,KAAA,SAAA,MAAA,GAAA,IAAA,YAAA,UAAA;AACA,mBAAA,MAAA,CAAA;AACA,uBAAA,MAAA,CAAA;AACA;AAEA,gBAAA,aAAA,GAAA;AACA,sBAAA;AACA,cAAAA,KAAA,SAAA,MAAA,GAAA,IAAA,YAAA,UAAA;AACA;AAAA,YACA;AAAA,UACA,OAAA;AACA,mBAAA;AAAA,UACA;AAAA,QACA;AAEA,YAAA,YAAA,GAAA;AACA,UAAAA,KAAA,SAAA,MAAA,GAAA,IAAA,YAAA,UAAA;AAAA,QACA;AAAA,MACA;AAGA,UAAA,aAAA,SAAA;AACA,UAAA,SAAA,SAAA,IAAA,GAAA;AACA,eAAA,KAAA,WAAA,QAAA,IAAA,KAAA,KAAA,EAAA;AAGA,UAAA,WAAA,OAAA;AACA,YAAA,SAAA,WAAA,WAAA,OAAA,IAAA,GAAA;AAAA,MACA;AAGA,UAAA,UAAA,WAAA,QAAA,KAAA,KAAA,KAAA,GAAA;AAGA,UAAA,aAAA,SAAA;AACA,UAAA,YAAA,EAAA;AACA,UAAA,aAAA,QAAA;AACA,UAAA,SAAA,eAAA,KAAA,GAAA;AAGA,UAAA,aAAA,SAAA;AACA,UAAA,YAAA,EAAA;AACA,UAAA,SAAA,eAAA,KAAA,GAAA;AAGA,UAAA,KAAA,OAAA,MAAA;AACA,mBAAA,MAAA;AAEAF,wBAAAA,MAAA,qBAAA;AAAA,YACA,UAAA;AAAA,YACA,SAAA,CAAA,QAAA;AACAA,4BAAA,MAAA,YAAA;AACA,8BAAA,IAAA,YAAA;AAAA,YACA;AAAA,YACA,MAAA,CAAA,QAAA;AACAA,4BAAA,MAAA,MAAA,SAAA,+CAAA,UAAA,GAAA;AACAA,4BAAA,MAAA,YAAA;AACAA,4BAAAA,MAAA,UAAA;AAAA,gBACA,OAAA;AAAA,gBACA,MAAA;AAAA,cACA,CAAA;AAAA,YACA;AAAA,UACA,CAAA;AAAA,QACA,GAAA,GAAA;AAAA,MACA,CAAA;AAAA,IACA;AAGA,UAAA,kBAAA,CAAA,eAAA;AACA,sBAAA,QAAA;AACA,qBAAA,QAAA;AAEAA,oBAAAA,MAAA,UAAA;AAAA,QACA,OAAA;AAAA,QACA,SAAA;AAAA,QACA,aAAA;AAAA,QACA,SAAA,CAAA,QAAA;AACA,cAAA,IAAA,SAAA;AACA,8BAAA,UAAA;AAAA,UACA,OAAA;AAEAA,0BAAAA,MAAA,aAAA;AAAA,cACA,MAAA,CAAA,UAAA;AAAA,cACA,SAAA;AAAA,YACA,CAAA;AAAA,UACA;AAAA,QACA;AAAA,MACA,CAAA;AAAA,IACA;AAGA,UAAA,oBAAA,CAAA,eAAA;AACAA,oBAAAA,MAAA,YAAA;AAAA,QACA,OAAA;AAAA,MACA,CAAA;AAEAA,oBAAAA,MAAA,uBAAA;AAAA,QACA,UAAA;AAAA,QACA,SAAA,MAAA;AACAA,wBAAA,MAAA,YAAA;AACAA,wBAAAA,MAAA,UAAA;AAAA,YACA,OAAA;AAAA,YACA,MAAA;AAAA,UACA,CAAA;AAAA,QACA;AAAA,QACA,MAAA,CAAA,QAAA;AACAA,wBAAA,MAAA,YAAA;AACAA,wBAAA,MAAA,MAAA,SAAA,+CAAA,QAAA,GAAA;AAEA,cAAA,IAAA,OAAA,QAAA,WAAA,IAAA,IAAA;AACAA,0BAAAA,MAAA,UAAA;AAAA,cACA,OAAA;AAAA,cACA,SAAA;AAAA,cACA,aAAA;AAAA,cACA,SAAA,CAAA,QAAA;AACA,oBAAA,IAAA,SAAA;AACAA,gCAAA,MAAA,YAAA;AAAA,gBACA;AAAA,cACA;AAAA,YACA,CAAA;AAAA,UACA,OAAA;AACAA,0BAAAA,MAAA,UAAA;AAAA,cACA,OAAA;AAAA,cACA,MAAA;AAAA,YACA,CAAA;AAAA,UACA;AAAA,QACA;AAAA,MACA,CAAA;AAAA,IACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AChwBA,GAAG,WAAWG,SAAe;"}