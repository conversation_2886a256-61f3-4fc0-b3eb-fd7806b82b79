{"version": 3, "file": "index.js", "sources": ["subPackages/cashback/pages/index/index.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/c3ViUGFja2FnZXNcY2FzaGJhY2tccGFnZXNcaW5kZXhcaW5kZXgudnVl"], "sourcesContent": ["<template>\n  <view class=\"cashback-container\">\n    <!-- 自定义导航栏 -->\n    <custom-navbar title=\"返利商城\" :show-close=\"true\"></custom-navbar>\n    \n    <!-- 内容区域 -->\n    <view class=\"content-container\">\n      <!-- 搜索框 -->\n      <view class=\"search-section\">\n        <view class=\"search-bar\" @tap=\"navigateToSearch\">\n          <svg class=\"search-icon\" viewBox=\"0 0 24 24\" width=\"20\" height=\"20\">\n            <path fill=\"#999999\" d=\"M9.5,3A6.5,6.5 0 0,1 16,9.5C16,11.11 15.41,12.59 14.44,13.73L14.71,14H15.5L20.5,19L19,20.5L14,15.5V14.71L13.73,14.44C12.59,15.41 11.11,16 9.5,16A6.5,6.5 0 0,1 3,9.5A6.5,6.5 0 0,1 9.5,3M9.5,5C7,5 5,7 5,9.5C5,12 7,14 9.5,14C12,14 14,12 14,9.5C14,7 12,5 9.5,5Z\" />\n          </svg>\n          <text class=\"search-placeholder\">粘贴商品链接，立即查询返利</text>\n      </view>\n    </view>\n    \n      <!-- 商品链接粘贴模块 -->\n      <view class=\"paste-link-section\">\n        <view class=\"paste-link-info\">\n          <text class=\"paste-link-title\">复制商品链接返<text class=\"highlight\">购物平均返5%</text></text>\n          <text class=\"paste-link-tutorial\" @tap=\"showTutorial\">教程</text>\n          </view>\n        <view class=\"paste-link-input\">\n          <text class=\"input-placeholder\">粘贴商品链接或口令</text>\n          <view class=\"example-link\">\n            <text class=\"example-text\">例：</text>\n            <text class=\"example-url\">https://m.tb.cn/h.5CEEn2LD3uJBsC0?tk=aUfiWotnuJv</text>\n          </view>\n        </view>\n        <view class=\"platform-icons\">\n          <image class=\"platform-icon\" src=\"/static/images/cashback/platform-taobao.png\" mode=\"aspectFit\"></image>\n          <image class=\"platform-icon\" src=\"/static/images/cashback/platform-jd.png\" mode=\"aspectFit\"></image>\n          <image class=\"platform-icon\" src=\"/static/images/cashback/platform-pdd.png\" mode=\"aspectFit\"></image>\n          <image class=\"platform-icon\" src=\"/static/images/cashback/platform-douyin.png\" mode=\"aspectFit\"></image>\n        </view>\n        <button class=\"paste-button\" @tap=\"pasteLink\">粘贴</button>\n      </view>\n      \n      <!-- 平台导航 -->\n      <view class=\"platforms-section\">\n        <view class=\"section-header\">\n          <text class=\"section-title\">热门平台</text>\n          <view class=\"section-more\" @tap=\"navigateToPlatforms\">\n            <text>全部</text>\n            <svg class=\"arrow-icon\" viewBox=\"0 0 24 24\" width=\"16\" height=\"16\">\n              <path fill=\"#999999\" d=\"M8.59,16.58L13.17,12L8.59,7.41L10,6L16,12L10,18L8.59,16.58Z\" />\n            </svg>\n          </view>\n        </view>\n        <scroll-view scroll-x class=\"platforms-scroll\" show-scrollbar=\"false\">\n          <view class=\"platforms-container\">\n            <view class=\"platform-item\" v-for=\"(platform, index) in platforms\" :key=\"index\" @tap=\"navigateToPlatformDetail(platform)\">\n              <image class=\"platform-icon\" :src=\"platform.icon\" mode=\"aspectFill\"></image>\n              <text class=\"platform-name\">{{ platform.name }}</text>\n            </view>\n          </view>\n        </scroll-view>\n      </view>\n      \n      <!-- 生活返利 -->\n      <view class=\"life-cashback-section\">\n        <view class=\"section-header\">\n          <text class=\"section-title\">生活返现</text>\n          <view class=\"section-more\" @tap=\"navigateToLifeCashback\">\n            <text>全部特权</text>\n            <svg class=\"arrow-icon\" viewBox=\"0 0 24 24\" width=\"16\" height=\"16\">\n              <path fill=\"#999999\" d=\"M8.59,16.58L13.17,12L8.59,7.41L10,6L16,12L10,18L8.59,16.58Z\" />\n            </svg>\n          </view>\n        </view>\n        <view class=\"life-cashback-grid\">\n          <!-- 领外卖红包 -->\n          <view class=\"life-cashback-item\" @tap=\"navigateToLifeService('takeout')\">\n            <view class=\"item-icon-container\" style=\"background-color: #FFE8E0;\">\n              <svg class=\"item-icon\" viewBox=\"0 0 24 24\" width=\"24\" height=\"24\">\n                <path fill=\"#FF6B6B\" d=\"M15.5,21L14,8H16.23L15.1,3.46L16.84,3L18.09,8H22L20.5,21H15.5M5,11H10A3,3 0 0,1 13,14H2A3,3 0 0,1 5,11M13,18A3,3 0 0,1 10,21H5A3,3 0 0,1 2,18H13M3,15H8L9.5,16.5L11,15H12A1,1 0 0,1 13,16A1,1 0 0,1 12,17H3A1,1 0 0,1 2,16A1,1 0 0,1 3,15Z\" />\n              </svg>\n              </view>\n            <view class=\"item-content\">\n              <text class=\"item-title\">领外卖红包</text>\n              <text class=\"item-desc\">最高返2%-5%</text>\n                </view>\n                </view>\n          \n          <!-- 领打车红包 -->\n          <view class=\"life-cashback-item\" @tap=\"navigateToLifeService('taxi')\">\n            <view class=\"item-icon-container\" style=\"background-color: #FFF2D6;\">\n              <svg class=\"item-icon\" viewBox=\"0 0 24 24\" width=\"24\" height=\"24\">\n                <path fill=\"#FFA726\" d=\"M5,11L6.5,6.5H17.5L19,11M17.5,16A1.5,1.5 0 0,1 16,14.5A1.5,1.5 0 0,1 17.5,13A1.5,1.5 0 0,1 19,14.5A1.5,1.5 0 0,1 17.5,16M6.5,16A1.5,1.5 0 0,1 5,14.5A1.5,1.5 0 0,1 6.5,13A1.5,1.5 0 0,1 8,14.5A1.5,1.5 0 0,1 6.5,16M18.92,6C18.72,5.42 18.16,5 17.5,5H6.5C5.84,5 5.28,5.42 5.08,6L3,12V20A1,1 0 0,0 4,21H5A1,1 0 0,0 6,20V19H18V20A1,1 0 0,0 19,21H20A1,1 0 0,0 21,20V12L18.92,6Z\" />\n              </svg>\n              </view>\n            <view class=\"item-content\">\n              <text class=\"item-title\">领打车红包</text>\n              <text class=\"item-desc\">最高返2.4%-5%</text>\n        </view>\n      </view>\n      \n          <!-- 淘宝 -->\n          <view class=\"life-cashback-item\" @tap=\"navigateToLifeService('movie')\">\n            <view class=\"item-icon-container\" style=\"background-color: #FFE0EC;\">\n              <svg class=\"item-icon\" viewBox=\"0 0 24 24\" width=\"24\" height=\"24\">\n                <path fill=\"#E91E63\" d=\"M18,9H16V7H18M18,13H16V11H18M18,17H16V15H18M8,9H6V7H8M8,13H6V11H8M8,17H6V15H8M18,3V5H16V3H8V5H6V3H4V21H6V19H8V21H16V19H18V21H20V3H18Z\" />\n              </svg>\n          </view>\n            <view class=\"item-content\">\n              <text class=\"item-title\">电影票8折起</text>\n              <text class=\"item-desc\">返10%</text>\n        </view>\n            </view>\n          \n          <!-- 京东 -->\n          <view class=\"life-cashback-item\" @tap=\"navigateToLifeService('express')\">\n            <view class=\"item-icon-container\" style=\"background-color: #E3F1FF;\">\n              <svg class=\"item-icon\" viewBox=\"0 0 24 24\" width=\"24\" height=\"24\">\n                <path fill=\"#2196F3\" d=\"M3,14H5V20H19V14H21V21A1,1 0 0,1 20,22H4A1,1 0 0,1 3,21V14M17,4H7V2H17V4M17.5,5L12,10.5L6.5,5H17.5M20,6.4L17.9,8.5L15.5,6.1L16.9,4.7L20,7.8V6.4M5.93,4.7L7.33,6.1L4.93,8.5L2.83,6.4V7.8L5.93,4.7Z\" />\n              </svg>\n          </view>\n            <view class=\"item-content\">\n              <text class=\"item-title\">寄快递返现</text>\n              <text class=\"item-desc\">返15%</text>\n        </view>\n      </view>\n      \n          <!-- 优惠券 -->\n          <view class=\"life-cashback-item special-item\" @tap=\"navigateToLifeService('coupon')\">\n            <view class=\"special-content\">\n              <text class=\"special-title\">淘宝-搜了么</text>\n              <text class=\"special-desc\">免费奶茶喝到爽</text>\n              <text class=\"special-subdesc\">下单再返3元</text>\n          </view>\n            <view class=\"special-price\">\n              <text class=\"price-value\">15元</text>\n              <text class=\"price-tag\">券</text>\n        </view>\n            <image class=\"special-image\" src=\"/static/images/cashback/coupon-mascot.png\" mode=\"aspectFit\"></image>\n                  </view>\n          \n          <!-- 会员充值 -->\n          <view class=\"life-cashback-item\" @tap=\"navigateToLifeService('vip')\">\n            <view class=\"item-icon-container\" style=\"background-color: #E8F5E9;\">\n              <svg class=\"item-icon\" viewBox=\"0 0 24 24\" width=\"24\" height=\"24\">\n                <path fill=\"#4CAF50\" d=\"M12,8H4A2,2 0 0,0 2,10V14A2,2 0 0,0 4,16H5V20A1,1 0 0,0 6,21H8A1,1 0 0,0 9,20V16H12L17,20V4L12,8M21.5,12C21.5,13.71 20.54,15.26 19,16V8C20.53,8.75 21.5,10.3 21.5,12Z\" />\n              </svg>\n                  </view>\n            <view class=\"item-content\">\n              <text class=\"item-title\">会员充值</text>\n              <text class=\"item-desc\">3.6元起</text>\n                </view>\n              </view>\n            </view>\n          </view>\n      \n      <!-- 红包区域 -->\n      <view class=\"coupon-section\">\n        <image class=\"coupon-banner\" src=\"/static/images/cashback/coupon-banner.png\" mode=\"widthFix\"></image>\n      </view>\n      \n      <!-- 推荐商品 -->\n      <view class=\"products-section\">\n        <view class=\"section-header\">\n          <text class=\"section-title\">精选好物</text>\n          <view class=\"section-more\" @tap=\"navigateToCategory\">\n            <text>更多</text>\n            <svg class=\"arrow-icon\" viewBox=\"0 0 24 24\" width=\"16\" height=\"16\">\n              <path fill=\"#999999\" d=\"M8.59,16.58L13.17,12L8.59,7.41L10,6L16,12L10,18L8.59,16.58Z\" />\n            </svg>\n          </view>\n        </view>\n        <view class=\"products-grid\">\n          <product-card\n            v-for=\"(product, index) in products\"\n            :key=\"index\"\n            :product=\"product\"\n            @tap=\"navigateToDetail(product)\"\n          ></product-card>\n                </view>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script>\nimport CustomNavbar from '../../components/CustomNavbar.vue';\nimport ProductCard from '../../components/ProductCard.vue';\n\nexport default {\n  components: {\n    CustomNavbar,\n    ProductCard\n  },\n  data() {\n    return {\n      platforms: [\n        { id: 1, name: '淘宝', icon: '/static/images/cashback/platform-taobao.png' },\n        { id: 2, name: '京东', icon: '/static/images/cashback/platform-jd.png' },\n        { id: 3, name: '拼多多', icon: '/static/images/cashback/platform-pdd.png' },\n        { id: 4, name: '唯品会', icon: '/static/images/cashback/platform-vip.png' },\n        { id: 5, name: '抖音', icon: '/static/images/cashback/platform-douyin.png' },\n        { id: 6, name: '天猫', icon: '/static/images/cashback/platform-tmall.png' },\n        { id: 7, name: '苏宁', icon: '/static/images/cashback/platform-suning.png' },\n        { id: 8, name: '小红书', icon: '/static/images/cashback/platform-xiaohongshu.png' }\n      ],\n      products: [\n        {\n          id: 1,\n          title: 'Apple iPhone 15 Pro Max (A2850) 256GB 原色钛金属',\n          image: '/static/images/cashback/product-1.png',\n          price: 9999.00,\n          cashback: 300.00,\n          platform: '京东'\n        },\n        {\n          id: 2,\n          title: 'HUAWEI Mate 60 Pro 12+512GB 雅黑色',\n          image: '/static/images/cashback/product-2.png',\n          price: 6999.00,\n          cashback: 200.00,\n          platform: '华为商城'\n        },\n        {\n          id: 3,\n          title: '小米14 Ultra 16+1T 黑色 徕卡光学',\n          image: '/static/images/cashback/product-3.png',\n          price: 7999.00,\n          cashback: 240.00,\n          platform: '小米商城'\n        },\n        {\n          id: 4,\n          title: 'OPPO Find X7 Ultra 16+512GB 棕色',\n          image: '/static/images/cashback/product-4.png',\n          price: 6999.00,\n          cashback: 210.00,\n          platform: '京东'\n        }\n      ],\n      clipboardContent: '',\n      hasDetectedLink: false,\n      detectedPlatform: ''\n    };\n  },\n  onLoad() {\n    // 设置页面不显示系统导航栏\n    uni.setNavigationBarColor({\n      frontColor: '#ffffff',\n      backgroundColor: '#9C27B0'\n    });\n    \n    // #ifdef APP-PLUS\n    // 获取剪贴板权限并禁用提示\n    this.requestClipboardPermission();\n    // #endif\n    \n    // 检测剪贴板是否有商品链接\n      setTimeout(() => {\n      this.checkClipboard();\n    }, 500);\n  },\n  onShow() {\n    // 每次页面显示时检测剪贴板\n    setTimeout(() => {\n      this.checkClipboard();\n    }, 500);\n  },\n  methods: {\n    navigateToSearch() {\n      uni.navigateTo({\n        url: '/subPackages/cashback/pages/search/index'\n      });\n    },\n    navigateToPlatforms() {\n      uni.navigateTo({\n        url: '/subPackages/cashback/pages/platforms/index'\n      });\n    },\n    navigateToPlatformDetail(platform) {\n      uni.navigateTo({\n        url: `/subPackages/cashback/pages/platform-detail/index?id=${platform.id}&name=${platform.name}`\n      });\n    },\n    navigateToCategory() {\n      uni.navigateTo({\n        url: '/subPackages/cashback/pages/category/index'\n      });\n    },\n    navigateToDetail(product) {\n      uni.navigateTo({\n        url: `/subPackages/cashback/pages/detail/index?id=${product.id}`\n      });\n    },\n    // 请求剪贴板权限并禁用提示\n    requestClipboardPermission() {\n      // #ifdef APP-PLUS\n      try {\n        // 尝试使用plus API禁用剪贴板提示\n        if (plus && plus.pasteboard) {\n          // 先尝试读取一次剪贴板内容，获取权限\n          plus.pasteboard.getClipboard();\n          \n          // 尝试禁用提示\n          if (plus.os.name.toLowerCase() === 'android') {\n            // Android平台\n            const context = plus.android.importClass('android.content.Context');\n            const activity = plus.android.runtimeMainActivity();\n            const clipboard = activity.getSystemService(context.CLIPBOARD_SERVICE);\n            \n            if (clipboard) {\n              // 尝试清除剪贴板监听\n              clipboard.removePrimaryClipChangedListener && \n              clipboard.removePrimaryClipChangedListener();\n            }\n          } else if (plus.os.name.toLowerCase() === 'ios') {\n            // iOS平台\n            // iOS可能需要特殊处理\n          }\n        }\n      } catch (e) {\n        console.error('禁用剪贴板提示失败:', e);\n      }\n      // #endif\n    },\n    // 检测剪贴板内容\n    checkClipboard() {\n      // #ifdef APP-PLUS\n      try {\n        if (plus && plus.pasteboard) {\n          // 使用plus API直接获取剪贴板内容，避免系统提示\n          const content = plus.pasteboard.getClipboard();\n          if (content && this.isProductLink(content)) {\n            this.clipboardContent = content;\n            this.hasDetectedLink = true;\n            this.detectedPlatform = this.detectPlatform(content);\n            \n            // 显示检测到链接的提示\n            uni.showToast({\n              title: `检测到${this.detectedPlatform}商品链接`,\n              icon: 'none',\n              duration: 2000\n            });\n          }\n          return;\n        }\n      } catch (e) {\n        console.error('获取剪贴板内容失败:', e);\n      }\n      // #endif\n      \n      // 兼容其他平台或fallback\n      uni.getClipboardData({\n        success: (res) => {\n          if (res.data && this.isProductLink(res.data)) {\n            this.clipboardContent = res.data;\n            this.hasDetectedLink = true;\n            this.detectedPlatform = this.detectPlatform(res.data);\n            \n            // 显示检测到链接的提示\n            uni.showToast({\n              title: `检测到${this.detectedPlatform}商品链接`,\n              icon: 'none',\n              duration: 2000\n            });\n          }\n        }\n      });\n    },\n    isProductLink(link) {\n      // 简单判断是否包含常见电商平台域名或短链接\n      const patterns = [\n        /taobao\\.com/i, /tmall\\.com/i, /jd\\.com/i, /pinduoduo\\.com/i, \n        /yangkeduo\\.com/i, /vip\\.com/i, /suning\\.com/i, /kaola\\.com/i,\n        /tb\\.cn/i, /m\\.tb\\.cn/i, /t\\.cn/i, /dwz\\.cn/i, /douyin\\.com/i\n      ];\n      \n      return patterns.some(pattern => pattern.test(link));\n    },\n    detectPlatform(link) {\n      if (/taobao\\.com/i.test(link) || /tb\\.cn/i.test(link) || /m\\.tb\\.cn/i.test(link)) {\n        return '淘宝';\n      } else if (/tmall\\.com/i.test(link)) {\n        return '天猫';\n      } else if (/jd\\.com/i.test(link)) {\n        return '京东';\n      } else if (/pinduoduo\\.com/i.test(link) || /yangkeduo\\.com/i.test(link)) {\n        return '拼多多';\n      } else if (/douyin\\.com/i.test(link)) {\n        return '抖音';\n      } else if (/vip\\.com/i.test(link)) {\n        return '唯品会';\n      } else if (/suning\\.com/i.test(link)) {\n        return '苏宁';\n      } else if (/kaola\\.com/i.test(link)) {\n        return '考拉';\n      } else {\n        return '电商';\n      }\n    },\n    pasteLink() {\n      if (this.hasDetectedLink && this.clipboardContent) {\n        // 如果已经检测到链接，直接处理\n        this.processLink(this.clipboardContent);\n      } else {\n        // #ifdef APP-PLUS\n        try {\n          if (plus && plus.pasteboard) {\n            // 使用plus API直接获取剪贴板内容\n            const content = plus.pasteboard.getClipboard();\n            if (content) {\n              this.processLink(content);\n            } else {\n              uni.showToast({\n                title: '剪贴板为空',\n                icon: 'none'\n              });\n            }\n            return;\n          }\n        } catch (e) {\n          console.error('获取剪贴板内容失败:', e);\n        }\n        // #endif\n        \n        // 兼容其他平台或fallback\n        uni.getClipboardData({\n          success: (res) => {\n            if (res.data) {\n              // 处理粘贴的链接\n              this.processLink(res.data);\n            } else {\n              uni.showToast({\n                title: '剪贴板为空',\n                icon: 'none'\n              });\n            }\n          },\n          fail: () => {\n            uni.showToast({\n              title: '获取剪贴板失败',\n              icon: 'none'\n            });\n          }\n        });\n      }\n    },\n    processLink(link) {\n      // 这里处理商品链接，实际项目中可能需要发送到后端进行处理\n      uni.showLoading({\n        title: '解析链接中...'\n      });\n      \n      // 模拟解析过程\n      setTimeout(() => {\n        uni.hideLoading();\n        \n        // 处理完成后重置状态\n        this.clipboardContent = '';\n        this.hasDetectedLink = false;\n        \n        uni.navigateTo({\n          url: `/subPackages/cashback/pages/product-detail/index?link=${encodeURIComponent(link)}`\n        });\n      }, 1000);\n    },\n    showTutorial() {\n      uni.showToast({\n        title: '使用教程功能正在开发中',\n        icon: 'none',\n        duration: 2000\n      });\n    },\n    navigateToLifeCashback() {\n      uni.navigateTo({\n        url: '/subPackages/cashback/pages/life-cashback/index'\n      });\n    },\n    \n    navigateToLifeService(type) {\n      uni.navigateTo({\n        url: `/subPackages/cashback/pages/life-service/index?type=${type}`\n      });\n    }\n  }\n};\n</script>\n\n<style lang=\"scss\" scoped>\n.cashback-container {\n  min-height: 100vh;\n  background-color: #f5f5f5;\n}\n\n.content-container {\n  padding-top: calc(var(--status-bar-height) + 44px);\n  padding-bottom: 20px;\n}\n\n.search-section {\n  padding: 16px;\n  background: linear-gradient(135deg, #AB47BC 0%, #9C27B0 100%);\n  border-bottom-left-radius: 20px;\n  border-bottom-right-radius: 20px;\n  padding-top: 24px;\n}\n\n.search-bar {\n  display: flex;\n  align-items: center;\n  background-color: #FFFFFF;\n  border-radius: 20px;\n  padding: 10px 16px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n  \n  .search-icon {\n    margin-right: 8px;\n}\n\n.search-placeholder {\n  color: #999999;\n    font-size: 14px;\n  }\n}\n\n/* 商品链接粘贴模块 */\n.paste-link-section {\n  margin: 16px;\n  padding: 16px;\n  background-color: #FFFFFF;\n  border-radius: 16px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);\n  position: relative;\n  \n  &::after {\n    content: '';\n    display: v-bind('hasDetectedLink ? \"block\" : \"none\"');\n    position: absolute;\n    top: 0;\n    right: 0;\n    width: 60px;\n    height: 60px;\n    background-color: #9C27B0;\n    border-radius: 0 16px 0 60px;\n    z-index: 1;\n  }\n  \n  &::before {\n    content: '已检测';\n    display: v-bind('hasDetectedLink ? \"block\" : \"none\"');\n    position: absolute;\n    top: 12px;\n    right: 8px;\n    color: #FFFFFF;\n    font-size: 12px;\n    font-weight: 500;\n    transform: rotate(45deg);\n    z-index: 2;\n  }\n}\n\n.paste-link-info {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 12px;\n\n  .paste-link-title {\n    font-size: 14px;\n  color: #333333;\n    \n    .highlight {\n      color: #9C27B0;\n      font-weight: 500;\n    }\n  }\n  \n  .paste-link-tutorial {\n    font-size: 14px;\n    color: #9C27B0;\n    background-color: rgba(156, 39, 176, 0.1);\n    padding: 2px 8px;\n    border-radius: 12px;\n  }\n}\n\n.paste-link-input {\n  background-color: #F5F5F5;\n  border-radius: 12px;\n  padding: 12px;\n  margin-bottom: 12px;\n  \n  .input-placeholder {\n    font-size: 14px;\n    color: #999999;\n    display: block;\n    margin-bottom: 4px;\n  }\n  \n  .example-link {\n  display: flex;\n    \n    .example-text {\n      font-size: 12px;\n      color: #999999;\n      margin-right: 4px;\n    }\n    \n    .example-url {\n      font-size: 12px;\n      color: #999999;\n      flex: 1;\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n    }\n  }\n}\n\n.platform-icons {\n  display: flex;\n  margin-bottom: 12px;\n  margin-top: 12px;\n  justify-content: center;\n  \n  .platform-icon {\n    width: 32px;\n    height: 32px;\n    border-radius: 50%;\n    margin-right: 8px;\n    margin-left: 8px;\n  }\n}\n\n.paste-button {\n  width: 100%;\n  height: 44px;\n  background-color: #9C27B0;\n  color: #FFFFFF;\n  font-size: 16px;\n  font-weight: 500;\n  border-radius: 22px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  border: none;\n  position: relative;\n  overflow: hidden;\n  \n  &::after {\n    content: '';\n    display: v-bind('hasDetectedLink ? \"block\" : \"none\"');\n    position: absolute;\n    top: 0;\n    left: 0;\n    right: 0;\n    bottom: 0;\n    background: linear-gradient(90deg, rgba(255,255,255,0.1), rgba(255,255,255,0.2), rgba(255,255,255,0.1));\n    animation: shimmer 1.5s infinite;\n  }\n  \n  @keyframes shimmer {\n    0% {\n      transform: translateX(-100%);\n    }\n    100% {\n      transform: translateX(100%);\n    }\n  }\n}\n\n.platforms-section {\n  margin: 16px;\n  background-color: #FFFFFF;\n  border-radius: 16px;\n  padding: 16px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);\n}\n\n.section-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 16px;\n  \n  .section-title {\n    font-size: 18px;\n    font-weight: 600;\n    color: #333333;\n  }\n  \n  .section-more {\n  display: flex;\n  align-items: center;\n    color: #999999;\n    font-size: 14px;\n  }\n}\n\n.platforms-scroll {\n  width: 100%;\n  white-space: nowrap;\n}\n\n.platforms-container {\n  display: inline-flex;\n  padding-bottom: 8px;\n}\n\n.platform-item {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  margin-right: 24px;\n  \n  &:last-child {\n    margin-right: 0;\n  }\n  \n  .platform-icon {\n    width: 48px;\n    height: 48px;\n    border-radius: 12px;\n    margin-bottom: 8px;\n  }\n  \n  .platform-name {\n    font-size: 12px;\n    color: #333333;\n  }\n}\n\n.coupon-section {\n  margin: 16px;\n  border-radius: 16px;\n  overflow: hidden;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);\n\n  .coupon-banner {\n  width: 100%;\n    border-radius: 16px;\n  }\n}\n\n.products-section {\n  margin: 16px;\n  background-color: #FFFFFF;\n  border-radius: 16px;\n  padding: 16px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);\n}\n\n.products-grid {\n  display: grid;\n  grid-template-columns: repeat(2, 1fr);\n  gap: 12px;\n}\n\n/* 生活返利模块 */\n.life-cashback-section {\n  margin: 16px;\n  background-color: #FFFFFF;\n  border-radius: 16px;\n  padding: 16px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);\n}\n\n.life-cashback-grid {\n  display: grid;\n  grid-template-columns: repeat(3, 1fr);\n  grid-template-rows: auto auto;\n  gap: 12px;\n}\n\n.life-cashback-item {\n  background-color: #FFFFFF;\n  border-radius: 12px;\n  padding: 12px;\n  display: flex;\n  flex-direction: column;\n  position: relative;\n  border: 1px solid #F0F0F0;\n  \n  .item-icon-container {\n    width: 40px;\n    height: 40px;\n    border-radius: 8px;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    margin-bottom: 8px;\n  }\n  \n  .item-content {\n  display: flex;\n    flex-direction: column;\n    \n    .item-title {\n      font-size: 14px;\n      color: #333333;\n      margin-bottom: 4px;\n    }\n    \n    .item-desc {\n      font-size: 12px;\n      color: #9C27B0;\n    }\n  }\n  \n  &.special-item {\n    grid-column: span 2;\n    background-color: #F5F0FF;\n    padding: 12px 16px;\n    flex-direction: row;\n    align-items: center;\n    position: relative;\n    overflow: hidden;\n    \n    .special-content {\n  flex: 1;\n      \n      .special-title {\n        font-size: 14px;\n        color: #333333;\n        margin-bottom: 4px;\n      }\n      \n      .special-desc {\n        font-size: 14px;\n        color: #9C27B0;\n  font-weight: 500;\n        margin-bottom: 2px;\n      }\n      \n      .special-subdesc {\n        font-size: 12px;\n        color: #666666;\n      }\n    }\n    \n    .special-price {\n      background-color: #FF5252;\n      border-radius: 20px;\n      padding: 4px 8px;\n  display: flex;\n  align-items: center;\n      margin-right: 12px;\n      \n      .price-value {\n        font-size: 16px;\n        font-weight: 600;\n        color: #FFFFFF;\n      }\n      \n      .price-tag {\n        font-size: 12px;\n        color: #FFFFFF;\n        background-color: rgba(255, 255, 255, 0.3);\n        border-radius: 10px;\n        padding: 0 4px;\n        margin-left: 2px;\n      }\n    }\n    \n    .special-image {\n      position: absolute;\n      right: 0;\n      bottom: 0;\n      width: 60px;\n      height: 60px;\n    }\n  }\n}\n</style> ", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/subPackages/cashback/pages/index/index.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni"], "mappings": ";;;AAuLA,MAAK,eAAgB,MAAW;AAChC,MAAK,cAAe,MAAW;AAE/B,MAAK,YAAU;AAAA,EACb,YAAY;AAAA,IACV;AAAA,IACA;AAAA,EACD;AAAA,EACD,OAAO;AACL,WAAO;AAAA,MACL,WAAW;AAAA,QACT,EAAE,IAAI,GAAG,MAAM,MAAM,MAAM,8CAA+C;AAAA,QAC1E,EAAE,IAAI,GAAG,MAAM,MAAM,MAAM,0CAA2C;AAAA,QACtE,EAAE,IAAI,GAAG,MAAM,OAAO,MAAM,2CAA4C;AAAA,QACxE,EAAE,IAAI,GAAG,MAAM,OAAO,MAAM,2CAA4C;AAAA,QACxE,EAAE,IAAI,GAAG,MAAM,MAAM,MAAM,8CAA+C;AAAA,QAC1E,EAAE,IAAI,GAAG,MAAM,MAAM,MAAM,6CAA8C;AAAA,QACzE,EAAE,IAAI,GAAG,MAAM,MAAM,MAAM,8CAA+C;AAAA,QAC1E,EAAE,IAAI,GAAG,MAAM,OAAO,MAAM,mDAAmD;AAAA,MAChF;AAAA,MACD,UAAU;AAAA,QACR;AAAA,UACE,IAAI;AAAA,UACJ,OAAO;AAAA,UACP,OAAO;AAAA,UACP,OAAO;AAAA,UACP,UAAU;AAAA,UACV,UAAU;AAAA,QACX;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,OAAO;AAAA,UACP,OAAO;AAAA,UACP,OAAO;AAAA,UACP,UAAU;AAAA,UACV,UAAU;AAAA,QACX;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,OAAO;AAAA,UACP,OAAO;AAAA,UACP,OAAO;AAAA,UACP,UAAU;AAAA,UACV,UAAU;AAAA,QACX;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,OAAO;AAAA,UACP,OAAO;AAAA,UACP,OAAO;AAAA,UACP,UAAU;AAAA,UACV,UAAU;AAAA,QACZ;AAAA,MACD;AAAA,MACD,kBAAkB;AAAA,MAClB,iBAAiB;AAAA,MACjB,kBAAkB;AAAA;EAErB;AAAA,EACD,SAAS;AAEPA,kBAAAA,MAAI,sBAAsB;AAAA,MACxB,YAAY;AAAA,MACZ,iBAAiB;AAAA,IACnB,CAAC;AAQC,eAAW,MAAM;AACjB,WAAK,eAAc;AAAA,IACpB,GAAE,GAAG;AAAA,EACP;AAAA,EACD,SAAS;AAEP,eAAW,MAAM;AACf,WAAK,eAAc;AAAA,IACpB,GAAE,GAAG;AAAA,EACP;AAAA,EACD,SAAS;AAAA,IACP,mBAAmB;AACjBA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK;AAAA,MACP,CAAC;AAAA,IACF;AAAA,IACD,sBAAsB;AACpBA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK;AAAA,MACP,CAAC;AAAA,IACF;AAAA,IACD,yBAAyB,UAAU;AACjCA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,wDAAwD,SAAS,EAAE,SAAS,SAAS,IAAI;AAAA,MAChG,CAAC;AAAA,IACF;AAAA,IACD,qBAAqB;AACnBA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK;AAAA,MACP,CAAC;AAAA,IACF;AAAA,IACD,iBAAiB,SAAS;AACxBA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,+CAA+C,QAAQ,EAAE;AAAA,MAChE,CAAC;AAAA,IACF;AAAA;AAAA,IAED,6BAA6B;AAAA,IA6B5B;AAAA;AAAA,IAED,iBAAiB;AA0BfA,oBAAAA,MAAI,iBAAiB;AAAA,QACnB,SAAS,CAAC,QAAQ;AAChB,cAAI,IAAI,QAAQ,KAAK,cAAc,IAAI,IAAI,GAAG;AAC5C,iBAAK,mBAAmB,IAAI;AAC5B,iBAAK,kBAAkB;AACvB,iBAAK,mBAAmB,KAAK,eAAe,IAAI,IAAI;AAGpDA,0BAAAA,MAAI,UAAU;AAAA,cACZ,OAAO,MAAM,KAAK,gBAAgB;AAAA,cAClC,MAAM;AAAA,cACN,UAAU;AAAA,YACZ,CAAC;AAAA,UACH;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACF;AAAA,IACD,cAAc,MAAM;AAElB,YAAM,WAAW;AAAA,QACf;AAAA,QAAgB;AAAA,QAAe;AAAA,QAAY;AAAA,QAC3C;AAAA,QAAmB;AAAA,QAAa;AAAA,QAAgB;AAAA,QAChD;AAAA,QAAW;AAAA,QAAc;AAAA,QAAU;AAAA,QAAY;AAAA;AAGjD,aAAO,SAAS,KAAK,aAAW,QAAQ,KAAK,IAAI,CAAC;AAAA,IACnD;AAAA,IACD,eAAe,MAAM;AACnB,UAAI,eAAe,KAAK,IAAI,KAAK,UAAU,KAAK,IAAI,KAAK,aAAa,KAAK,IAAI,GAAG;AAChF,eAAO;AAAA,MACT,WAAW,cAAc,KAAK,IAAI,GAAG;AACnC,eAAO;AAAA,MACT,WAAW,WAAW,KAAK,IAAI,GAAG;AAChC,eAAO;AAAA,MACT,WAAW,kBAAkB,KAAK,IAAI,KAAK,kBAAkB,KAAK,IAAI,GAAG;AACvE,eAAO;AAAA,MACT,WAAW,eAAe,KAAK,IAAI,GAAG;AACpC,eAAO;AAAA,MACT,WAAW,YAAY,KAAK,IAAI,GAAG;AACjC,eAAO;AAAA,MACT,WAAW,eAAe,KAAK,IAAI,GAAG;AACpC,eAAO;AAAA,MACT,WAAW,cAAc,KAAK,IAAI,GAAG;AACnC,eAAO;AAAA,aACF;AACL,eAAO;AAAA,MACT;AAAA,IACD;AAAA,IACD,YAAY;AACV,UAAI,KAAK,mBAAmB,KAAK,kBAAkB;AAEjD,aAAK,YAAY,KAAK,gBAAgB;AAAA,aACjC;AAsBLA,sBAAAA,MAAI,iBAAiB;AAAA,UACnB,SAAS,CAAC,QAAQ;AAChB,gBAAI,IAAI,MAAM;AAEZ,mBAAK,YAAY,IAAI,IAAI;AAAA,mBACpB;AACLA,4BAAAA,MAAI,UAAU;AAAA,gBACZ,OAAO;AAAA,gBACP,MAAM;AAAA,cACR,CAAC;AAAA,YACH;AAAA,UACD;AAAA,UACD,MAAM,MAAM;AACVA,0BAAAA,MAAI,UAAU;AAAA,cACZ,OAAO;AAAA,cACP,MAAM;AAAA,YACR,CAAC;AAAA,UACH;AAAA,QACF,CAAC;AAAA,MACH;AAAA,IACD;AAAA,IACD,YAAY,MAAM;AAEhBA,oBAAAA,MAAI,YAAY;AAAA,QACd,OAAO;AAAA,MACT,CAAC;AAGD,iBAAW,MAAM;AACfA,sBAAG,MAAC,YAAW;AAGf,aAAK,mBAAmB;AACxB,aAAK,kBAAkB;AAEvBA,sBAAAA,MAAI,WAAW;AAAA,UACb,KAAK,yDAAyD,mBAAmB,IAAI,CAAC;AAAA,QACxF,CAAC;AAAA,MACF,GAAE,GAAI;AAAA,IACR;AAAA,IACD,eAAe;AACbA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,QACN,UAAU;AAAA,MACZ,CAAC;AAAA,IACF;AAAA,IACD,yBAAyB;AACvBA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK;AAAA,MACP,CAAC;AAAA,IACF;AAAA,IAED,sBAAsB,MAAM;AAC1BA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,uDAAuD,IAAI;AAAA,MAClE,CAAC;AAAA,IACH;AAAA,EACF;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACjeA,GAAG,WAAW,eAAe;"}