"use strict";
const common_vendor = require("../../common/vendor.js");
const common_assets = require("../../common/assets.js");
if (!Array) {
  const _component_uni_popup = common_vendor.resolveComponent("uni-popup");
  _component_uni_popup();
}
const _sfc_main = {
  __name: "RedPacketEntry",
  props: { modelValue: Object },
  emits: ["update:modelValue"],
  setup(__props, { emit: __emit }) {
    const props = __props;
    const emit = __emit;
    const showDialog = common_vendor.ref(false);
    const form = common_vendor.ref({
      amount: "",
      count: "",
      taskType: 0
    });
    const taskOptions = ["转发到群", "邀请助力", "完成浏览", "自定义"];
    const isSet = common_vendor.computed(() => !!form.value.amount && !!form.value.count);
    common_vendor.watch(
      () => props.modelValue,
      (val) => {
        if (val)
          Object.assign(form.value, val);
      },
      { immediate: true }
    );
    function confirmRedPacket() {
      if (!form.value.amount || !form.value.count) {
        common_vendor.index.showToast({ title: "请填写完整", icon: "none" });
        return;
      }
      emit("update:modelValue", { ...form.value });
      showDialog.value = false;
    }
    function onTaskTypeChange(e) {
      form.value.taskType = e.detail.value;
    }
    return (_ctx, _cache) => {
      return {
        a: common_assets._imports_0$66,
        b: common_vendor.t(isSet.value ? "已设置" : "未设置"),
        c: isSet.value ? 1 : "",
        d: common_assets._imports_1$59,
        e: common_vendor.o(($event) => showDialog.value = true),
        f: form.value.amount,
        g: common_vendor.o(($event) => form.value.amount = $event.detail.value),
        h: form.value.count,
        i: common_vendor.o(($event) => form.value.count = $event.detail.value),
        j: common_vendor.t(taskOptions[form.value.taskType]),
        k: taskOptions,
        l: form.value.taskType,
        m: common_vendor.o(onTaskTypeChange),
        n: common_vendor.o(confirmRedPacket),
        o: common_vendor.o(($event) => showDialog.value = $event),
        p: common_vendor.p({
          type: "bottom",
          modelValue: showDialog.value
        })
      };
    };
  }
};
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-851f2d80"]]);
wx.createComponent(Component);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/components/RedPacket/RedPacketEntry.js.map
