"use strict";
const common_vendor = require("../../../../../common/vendor.js");
const _sfc_main = {
  data() {
    return {
      // 成长值规则
      growthRules: {
        consumptionRatio: 5,
        checkInGrowth: 2,
        profileGrowth: 50,
        firstPurchaseGrowth: 100,
        inviteGrowth: 30,
        description: "成长值是会员等级的重要依据，通过消费、签到、完成任务等方式获取成长值，成长值越高，会员等级越高，享受的权益越多。",
        isVisible: true,
        levelProtection: true,
        protectionDays: 30
      },
      // 成长值等级
      growthLevels: [
        {
          id: 1,
          name: "普通会员",
          minGrowth: 0,
          maxGrowth: 999,
          benefits: ["基础会员权益"]
        },
        {
          id: 2,
          name: "银卡会员",
          minGrowth: 1e3,
          maxGrowth: 4999,
          benefits: ["9.8折优惠", "生日礼包"]
        },
        {
          id: 3,
          name: "金卡会员",
          minGrowth: 5e3,
          maxGrowth: 19999,
          benefits: ["9.5折优惠", "生日礼包", "专属客服"]
        },
        {
          id: 4,
          name: "钻石会员",
          minGrowth: 2e4,
          maxGrowth: 99999,
          benefits: ["9折优惠", "生日礼包", "专属客服", "免费配送"]
        }
      ],
      // 成长值任务
      growthTasks: [
        {
          id: 1,
          name: "连续签到7天",
          reward: 50,
          enabled: true
        },
        {
          id: 2,
          name: "完善个人资料",
          reward: 50,
          enabled: true
        },
        {
          id: 3,
          name: "绑定手机号",
          reward: 30,
          enabled: true
        },
        {
          id: 4,
          name: "首次购买",
          reward: 100,
          enabled: true
        },
        {
          id: 5,
          name: "邀请好友注册",
          reward: 30,
          enabled: true
        }
      ]
    };
  },
  methods: {
    goBack() {
      common_vendor.index.navigateBack();
    },
    toggleVisibility(e) {
      this.growthRules.isVisible = e.detail.value;
    },
    toggleLevelProtection(e) {
      this.growthRules.levelProtection = e.detail.value;
    },
    editLevel(level) {
      common_vendor.index.showToast({
        title: "编辑等级功能开发中",
        icon: "none"
      });
    },
    deleteLevel(level) {
      common_vendor.index.showModal({
        title: "删除确认",
        content: `确定要删除"${level.name}"等级吗？`,
        success: (res) => {
          if (res.confirm) {
            const index = this.growthLevels.findIndex((item) => item.id === level.id);
            if (index !== -1) {
              this.growthLevels.splice(index, 1);
              common_vendor.index.showToast({
                title: "删除成功",
                icon: "success"
              });
            }
          }
        }
      });
    },
    addLevel() {
      common_vendor.index.showToast({
        title: "添加等级功能开发中",
        icon: "none"
      });
    },
    toggleTask(task, e) {
      const index = this.growthTasks.findIndex((item) => item.id === task.id);
      if (index !== -1) {
        this.growthTasks[index].enabled = e.detail.value;
      }
      common_vendor.index.showToast({
        title: e.detail.value ? `${task.name}已启用` : `${task.name}已禁用`,
        icon: "none"
      });
    },
    addTask() {
      common_vendor.index.showToast({
        title: "添加任务功能开发中",
        icon: "none"
      });
    },
    saveRules() {
      common_vendor.index.showLoading({
        title: "保存中..."
      });
      setTimeout(() => {
        common_vendor.index.hideLoading();
        common_vendor.index.showToast({
          title: "成长值规则保存成功",
          icon: "success"
        });
      }, 1e3);
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.o((...args) => $options.goBack && $options.goBack(...args)),
    b: $data.growthRules.consumptionRatio,
    c: common_vendor.o(($event) => $data.growthRules.consumptionRatio = $event.detail.value),
    d: $data.growthRules.checkInGrowth,
    e: common_vendor.o(($event) => $data.growthRules.checkInGrowth = $event.detail.value),
    f: $data.growthRules.profileGrowth,
    g: common_vendor.o(($event) => $data.growthRules.profileGrowth = $event.detail.value),
    h: $data.growthRules.firstPurchaseGrowth,
    i: common_vendor.o(($event) => $data.growthRules.firstPurchaseGrowth = $event.detail.value),
    j: $data.growthRules.inviteGrowth,
    k: common_vendor.o(($event) => $data.growthRules.inviteGrowth = $event.detail.value),
    l: common_vendor.f($data.growthLevels, (level, index, i0) => {
      return common_vendor.e({
        a: common_vendor.t(level.name),
        b: common_vendor.t(level.minGrowth),
        c: common_vendor.t(level.maxGrowth),
        d: common_vendor.f(level.benefits, (benefit, bIndex, i1) => {
          return {
            a: common_vendor.t(benefit),
            b: bIndex
          };
        }),
        e: common_vendor.o(($event) => $options.editLevel(level), index),
        f: index > 0
      }, index > 0 ? {
        g: common_vendor.o(($event) => $options.deleteLevel(level), index)
      } : {}, {
        h: index
      });
    }),
    m: common_vendor.o((...args) => $options.addLevel && $options.addLevel(...args)),
    n: $data.growthRules.description,
    o: common_vendor.o(($event) => $data.growthRules.description = $event.detail.value),
    p: $data.growthRules.isVisible,
    q: common_vendor.o((...args) => $options.toggleVisibility && $options.toggleVisibility(...args)),
    r: $data.growthRules.levelProtection,
    s: common_vendor.o((...args) => $options.toggleLevelProtection && $options.toggleLevelProtection(...args)),
    t: $data.growthRules.levelProtection
  }, $data.growthRules.levelProtection ? {
    v: $data.growthRules.protectionDays,
    w: common_vendor.o(($event) => $data.growthRules.protectionDays = $event.detail.value)
  } : {}, {
    x: common_vendor.f($data.growthTasks, (task, index, i0) => {
      return {
        a: common_vendor.t(task.name),
        b: common_vendor.t(task.reward),
        c: task.enabled,
        d: common_vendor.o((e) => $options.toggleTask(task, e), index),
        e: index
      };
    }),
    y: common_vendor.o((...args) => $options.addTask && $options.addTask(...args)),
    z: common_vendor.o((...args) => $options.saveRules && $options.saveRules(...args))
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../../../.sourcemap/mp-weixin/subPackages/merchant-admin-marketing/pages/marketing/member/growth-rule.js.map
