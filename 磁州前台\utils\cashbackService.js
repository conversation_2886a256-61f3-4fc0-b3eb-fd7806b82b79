import request from '@/utils/request';

/**
 * 返利系统服务类
 */
class CashbackService {
  constructor() {
    // API基础路径
    this.baseUrl = '/api'; // 直接使用相对路径
    // 支持的电商平台
    this.platforms = [
      { id: 'taobao', name: '淘宝', icon: 'https://via.placeholder.com/40', color: '#FF5000' },
      { id: 'jd', name: '京东', icon: 'https://via.placeholder.com/40', color: '#D71C1E' },
      { id: 'pdd', name: '拼多多', icon: 'https://via.placeholder.com/40', color: '#E22E1F' },
      { id: 'vip', name: '唯品会', icon: 'https://via.placeholder.com/40', color: '#B620E0' },
      { id: 'suning', name: '苏宁易购', icon: 'https://via.placeholder.com/40', color: '#FF8200' },
      { id: 'kaola', name: '考拉海购', icon: 'https://via.placeholder.com/40', color: '#D7000F' },
      { id: 'tmall', name: '天猫', icon: 'https://via.placeholder.com/40', color: '#FF0036' },
      { id: 'douyin', name: '抖音电商', icon: 'https://via.placeholder.com/40', color: '#000000' }
    ];
  }

  /**
   * 获取支持的电商平台列表
   */
  getPlatforms() {
    return this.platforms;
  }

  /**
   * 获取返利系统首页数据
   */
  async getHomeData() {
    try {
      const response = await request.get(`${this.baseUrl}/cashback/home`);
      console.log('获取返利系统首页数据成功');
      return response.data;
    } catch (error) {
      console.error('获取返利系统首页数据失败', error);
      throw error;
    }
  }

  /**
   * 搜索商品
   * @param {Object} params 搜索参数
   */
  async searchProducts(params) {
    try {
      // 不再加密参数
      const response = await request.post(`${this.baseUrl}/cashback/search`, params);
      console.log('搜索返利商品成功');
      return response.data;
    } catch (error) {
      console.error('搜索返利商品失败', error);
      throw error;
    }
  }

  /**
   * 获取商品详情
   * @param {string} productId 商品ID
   * @param {string} platform 平台标识
   * @returns {Promise} 商品详情
   */
  async getProductDetail(productId, platform) {
    try {
      const res = await request({
        url: '/api/cashback/product/detail',
        method: 'GET',
        data: { productId, platform }
      });
      
      if (res.code === 0 && res.data) {
        return res.data;
      }
      
      return null;
    } catch (error) {
      console.error('获取商品详情失败', error);
      return null;
    }
  }

  /**
   * 获取商品价格历史
   * @param {Object} params 请求参数
   * @param {string} params.productId 商品ID
   * @param {string} params.platform 平台标识
   * @param {string} params.timeRange 时间范围
   * @returns {Promise} 价格历史数据
   */
  async getProductPriceHistory(params) {
    try {
      const res = await request({
        url: '/api/cashback/product/price-history',
        method: 'GET',
        data: params
      });
      
      if (res.code === 0 && res.data) {
        return res.data;
      }
      
      // 如果是开发环境，返回模拟数据
      if (process.env.NODE_ENV === 'development') {
        return this.getMockPriceHistory(params.timeRange);
      }
      
      return null;
    } catch (error) {
      console.error('获取价格历史失败', error);
      
      // 如果是开发环境，返回模拟数据
      if (process.env.NODE_ENV === 'development') {
        return this.getMockPriceHistory(params.timeRange);
      }
      
      return null;
    }
  }
  
  /**
   * 获取模拟价格历史数据（仅用于开发测试）
   * @param {string} timeRange 时间范围
   * @returns {Object} 模拟价格历史数据
   */
  getMockPriceHistory(timeRange) {
    // 根据时间范围生成不同数量的数据点
    let days = 30;
    switch (timeRange) {
      case '7days':
        days = 7;
        break;
      case '30days':
        days = 30;
        break;
      case '90days':
        days = 90;
        break;
      case '180days':
        days = 180;
        break;
      default:
        days = 30;
    }
    
    // 生成模拟数据
    const basePrice = 100;
    const priceList = [];
    let lowestPrice = basePrice;
    let highestPrice = basePrice;
    let totalPrice = 0;
    
    for (let i = 0; i < days; i++) {
      // 生成随机价格波动
      const randomChange = Math.random() * 20 - 10; // -10 到 10 之间的随机数
      const price = parseFloat((basePrice + randomChange).toFixed(2));
      
      // 计算前一天价格（如果有）
      const prevPrice = i > 0 ? priceList[i - 1].price : price;
      const priceChange = parseFloat((price - prevPrice).toFixed(2));
      
      // 计算随机优惠券金额
      const hasCoupon = Math.random() > 0.5;
      const couponPrice = hasCoupon ? parseFloat((price - Math.random() * 10).toFixed(2)) : null;
      
      // 生成日期
      const date = new Date();
      date.setDate(date.getDate() - (days - i));
      
      // 添加到价格列表
      priceList.push({
        date: date.toISOString(),
        price,
        couponPrice,
        priceChange
      });
      
      // 更新统计数据
      if (price < lowestPrice) lowestPrice = price;
      if (price > highestPrice) highestPrice = price;
      totalPrice += price;
    }
    
    // 计算平均价格
    const averagePrice = parseFloat((totalPrice / days).toFixed(2));
    
    return {
      priceList,
      lowestPrice: lowestPrice.toFixed(2),
      highestPrice: highestPrice.toFixed(2),
      averagePrice: averagePrice.toFixed(2)
    };
  }

  /**
   * 生成商品购买链接
   * @param {String} productId 商品ID
   * @param {String} platform 平台ID
   */
  async generateBuyLink(productId, platform) {
    try {
      const response = await request.post(`${this.baseUrl}/cashback/generateLink`, {
        productId,
        platform
      });
      console.log('生成购买链接成功');
      return response.data;
    } catch (error) {
      console.error('生成购买链接失败', error);
      throw error;
    }
  }

  /**
   * 获取订单列表
   * @param {Object} params 查询参数
   */
  async getOrderList(params) {
    try {
      const response = await request.get(`${this.baseUrl}/cashback/orders`, {
        params
      });
      console.log('获取订单列表成功');
      return response.data;
    } catch (error) {
      console.error('获取订单列表失败', error);
      throw error;
    }
  }

  /**
   * 获取订单详情
   * @param {String} orderId 订单ID
   */
  async getOrderDetail(orderId) {
    try {
      const response = await request.get(`${this.baseUrl}/cashback/order/${orderId}`);
      console.log('获取订单详情成功');
      return response.data;
    } catch (error) {
      console.error('获取订单详情失败', error);
      throw error;
    }
  }

  /**
   * 获取钱包信息
   */
  async getWalletInfo() {
    try {
      const response = await request.get(`${this.baseUrl}/cashback/wallet`);
      console.log('获取钱包信息成功');
      return response.data;
    } catch (error) {
      console.error('获取钱包信息失败', error);
      throw error;
    }
  }

  /**
   * 获取钱包交易记录
   * @param {Object} params 查询参数
   */
  async getWalletTransactions(params) {
    try {
      const response = await request.get(`${this.baseUrl}/cashback/wallet/transactions`, {
        params
      });
      console.log('获取钱包交易记录成功');
      return response.data;
    } catch (error) {
      console.error('获取钱包交易记录失败', error);
      throw error;
    }
  }

  /**
   * 提现申请
   * @param {Object} params 提现参数
   */
  async withdrawCashback(params) {
    try {
      const response = await request.post(`${this.baseUrl}/cashback/withdraw`, params);
      console.log('提交提现申请成功');
      return response.data;
    } catch (error) {
      console.error('提交提现申请失败', error);
      throw error;
    }
  }

  /**
   * 获取提现记录
   * @param {Object} params 查询参数
   */
  async getWithdrawRecords(params) {
    try {
      const response = await request.get(`${this.baseUrl}/cashback/withdraw/records`, {
        params
      });
      console.log('获取提现记录成功');
      return response.data;
    } catch (error) {
      console.error('获取提现记录失败', error);
      throw error;
    }
  }

  /**
   * 获取邀请数据
   */
  async getInviteData() {
    try {
      const response = await request.get(`${this.baseUrl}/cashback/invite`);
      console.log('获取邀请数据成功');
      return response.data;
    } catch (error) {
      console.error('获取邀请数据失败', error);
      throw error;
    }
  }

  /**
   * 获取团队概览数据
   */
  async getTeamOverview() {
    try {
      const response = await request.get(`${this.baseUrl}/cashback/team/overview`);
      console.log('获取团队概览成功');
      return response.data;
    } catch (error) {
      console.error('获取团队概览失败', error);
      throw error;
    }
  }

  /**
   * 获取团队成员列表
   * @param {Object} params 查询参数
   */
  async getTeamMembers(params) {
    try {
      const response = await request.get(`${this.baseUrl}/cashback/team/members`, {
        params
      });
      console.log('获取团队成员成功');
      return response.data;
    } catch (error) {
      console.error('获取团队成员失败', error);
      throw error;
    }
  }

  /**
   * 生成分享海报
   */
  async generatePoster() {
    try {
      const response = await request.post(`${this.baseUrl}/cashback/poster`);
      console.log('生成分享海报成功');
      return response.data;
    } catch (error) {
      console.error('生成分享海报失败', error);
      throw error;
    }
  }

  /**
   * 记录分享行为
   * @param {Object} params 分享参数
   */
  async recordShare(params) {
    try {
      const response = await request.post(`${this.baseUrl}/cashback/share`, params);
      console.log('记录分享行为成功');
      return response.data;
    } catch (error) {
      console.error('记录分享行为失败', error);
      throw error;
    }
  }

  /**
   * 获取推荐商品列表
   * @param {Object} params 查询参数
   */
  async getRecommendProducts(params) {
    try {
      const response = await request.get(`${this.baseUrl}/cashback/recommend`, {
        params
      });
      console.log('获取推荐商品成功');
      return response.data;
    } catch (error) {
      console.error('获取推荐商品失败', error);
      throw error;
    }
  }

  /**
   * 获取平台优惠券列表
   * @param {String} platform 平台ID
   */
  async getPlatformCoupons(platform) {
    try {
      const response = await request.get(`${this.baseUrl}/cashback/coupons`, {
        params: { platform }
      });
      console.log('获取平台优惠券成功');
      return response.data;
    } catch (error) {
      console.error('获取平台优惠券失败', error);
      throw error;
    }
  }

  /**
   * 领取优惠券
   * @param {String} couponId 优惠券ID
   * @param {String} platform 平台ID
   */
  async receiveCoupon(couponId, platform) {
    try {
      const response = await request.post(`${this.baseUrl}/cashback/coupon/receive`, {
        couponId,
        platform
      });
      console.log('领取优惠券成功');
      return response.data;
    } catch (error) {
      console.error('领取优惠券失败', error);
      throw error;
    }
  }

  /**
   * 获取用户优惠券列表
   */
  async getUserCoupons() {
    try {
      const response = await request.get(`${this.baseUrl}/cashback/user/coupons`);
      console.log('获取用户优惠券成功');
      return response.data;
    } catch (error) {
      console.error('获取用户优惠券失败', error);
      throw error;
    }
  }

  /**
   * 绑定平台账号
   * @param {Object} params 绑定参数
   * @returns {Promise} 绑定结果
   */
  async bindPlatform(params) {
    try {
      const res = await request({
        url: '/api/cashback/platform/bind',
        method: 'POST',
        data: params
      });
      
      return res;
    } catch (error) {
      console.error('绑定平台账号失败', error);
      return { code: -1, msg: '绑定平台账号失败' };
    }
  }

  /**
   * 获取已绑定的平台账号列表
   */
  async getBoundPlatforms() {
    try {
      const response = await request.get(`${this.baseUrl}/cashback/bound/platforms`);
      console.log('获取已绑定平台成功');
      return response.data;
    } catch (error) {
      console.error('获取已绑定平台失败', error);
      throw error;
    }
  }

  /**
   * 解绑平台账号
   * @param {string} platformId 平台ID
   * @returns {Promise} 解绑结果
   */
  async unbindPlatform(platformId) {
    try {
      const res = await request({
        url: '/api/cashback/platform/unbind',
        method: 'POST',
        data: { platformId }
      });
      
      return res;
    } catch (error) {
      console.error('解绑平台账号失败', error);
      return { code: -1, msg: '解绑平台账号失败' };
    }
  }

  /**
   * 同步平台订单
   * @param {string} platformId 平台ID
   * @returns {Promise} 同步结果
   */
  async syncPlatformOrders(platformId) {
    try {
      const res = await request({
        url: '/api/cashback/platform/sync-orders',
        method: 'POST',
        data: { platformId }
      });
      
      return res;
    } catch (error) {
      console.error('同步平台订单失败', error);
      return { code: -1, msg: '同步平台订单失败' };
    }
  }

  /**
   * 获取用户分销员等级信息
   */
  async getDistributorLevel() {
    try {
      const response = await request.get(`${this.baseUrl}/cashback/distributor/level`);
      console.log('获取分销员等级成功');
      return response.data;
    } catch (error) {
      console.error('获取分销员等级失败', error);
      throw error;
    }
  }

  /**
   * 申请成为分销员
   * @param {Object} params 申请参数
   */
  async applyDistributor(params) {
    try {
      const response = await request.post(`${this.baseUrl}/cashback/apply/distributor`, params);
      console.log('申请成为分销员成功');
      return response.data;
    } catch (error) {
      console.error('申请成为分销员失败', error);
      throw error;
    }
  }

  /**
   * 获取支付渠道列表
   */
  async getPaymentChannels() {
    try {
      const response = await request.get(`${this.baseUrl}/cashback/payment/channels`);
      console.log('获取支付渠道成功');
      return response.data;
    } catch (error) {
      console.error('获取支付渠道失败', error);
      throw error;
    }
  }

  /**
   * 创建支付订单
   * @param {Object} params 支付参数
   */
  async createPayment(params) {
    try {
      const response = await request.post(`${this.baseUrl}/cashback/create/payment`, params);
      console.log('创建支付订单成功');
      return response.data;
    } catch (error) {
      console.error('创建支付订单失败', error);
      throw error;
    }
  }

  /**
   * 查询支付结果
   * @param {String} paymentId 支付ID
   */
  async queryPaymentResult(paymentId) {
    try {
      const response = await request.get(`${this.baseUrl}/cashback/payment/result`, {
        params: { paymentId }
      });
      console.log('查询支付结果成功');
      return response.data;
    } catch (error) {
      console.error('查询支付结果失败', error);
      throw error;
    }
  }

  /**
   * 获取用户邀请记录
   * @param {Object} params 查询参数
   */
  async getInviteRecords(params) {
    try {
      const response = await request.get(`${this.baseUrl}/cashback/invite/records`, {
        params
      });
      console.log('获取邀请记录成功');
      return response.data;
    } catch (error) {
      console.error('获取邀请记录失败', error);
      throw error;
    }
  }

  /**
   * 获取分销排行榜
   * @param {Object} params 查询参数
   */
  async getDistributorRanking(params) {
    try {
      const response = await request.get(`${this.baseUrl}/cashback/distributor/ranking`, {
        params
      });
      console.log('获取分销排行榜成功');
      return response.data;
    } catch (error) {
      console.error('获取分销排行榜失败', error);
      throw error;
    }
  }

  /**
   * 获取商品分类列表
   */
  async getProductCategories() {
    try {
      const response = await request.get(`${this.baseUrl}/cashback/categories`);
      console.log('获取商品分类成功');
      return response.data;
    } catch (error) {
      console.error('获取商品分类失败', error);
      throw error;
    }
  }

  /**
   * 获取分类商品列表
   * @param {Object} params 查询参数
   */
  async getCategoryProducts(params) {
    try {
      const response = await request.get(`${this.baseUrl}/cashback/category/products`, {
        params
      });
      console.log('获取分类商品成功');
      return response.data;
    } catch (error) {
      console.error('获取分类商品失败', error);
      throw error;
    }
  }

  /**
   * 获取钱包明细
   * @param {Object} params 查询参数
   * @returns {Promise} 钱包明细
   */
  async getWalletDetail(params) {
    try {
      const res = await request({
        url: '/api/cashback/wallet/detail',
        method: 'GET',
        data: params
      });
      
      if (res.code === 0 && res.data) {
        return res.data;
      }
      
      return { list: [], total: 0 };
    } catch (error) {
      console.error('获取钱包明细失败', error);
      return { list: [], total: 0 };
    }
  }
}

// 导出单例
const cashbackService = new CashbackService();
export default cashbackService; 