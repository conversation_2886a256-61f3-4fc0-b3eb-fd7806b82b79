// 模拟API服务 - 演示版本
// 这样就不需要后端服务了

export class MockAPI {
  // 模拟登录
  static async login(username: string, password: string) {
    // 模拟网络延迟
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    if (username === 'admin' && password === 'admin123') {
      const token = 'mock-jwt-token-' + Date.now();
      const userInfo = {
        id: 1,
        username: 'admin',
        realName: '系统管理员',
        email: '<EMAIL>',
        phone: '13800138000',
        avatar: 'https://avatars.githubusercontent.com/u/1?v=4',
        roles: ['admin'],
        permissions: ['*']
      };
      
      // 保存到本地存储
      localStorage.setItem('token', token);
      localStorage.setItem('userInfo', JSON.stringify(userInfo));
      
      return {
        code: 200,
        message: '登录成功',
        data: {
          token,
          userInfo
        }
      };
    } else {
      return {
        code: 401,
        message: '用户名或密码错误',
        data: null
      };
    }
  }
  
  // 模拟获取用户信息
  static async getUserInfo() {
    await new Promise(resolve => setTimeout(resolve, 500));
    
    const token = localStorage.getItem('token');
    if (!token) {
      return {
        code: 401,
        message: '未登录',
        data: null
      };
    }
    
    const userInfo = JSON.parse(localStorage.getItem('userInfo') || '{}');
    return {
      code: 200,
      message: '获取成功',
      data: userInfo
    };
  }
  
  // 模拟登出
  static async logout() {
    await new Promise(resolve => setTimeout(resolve, 300));
    
    localStorage.removeItem('token');
    localStorage.removeItem('userInfo');
    
    return {
      code: 200,
      message: '登出成功',
      data: null
    };
  }
  
  // 模拟获取菜单
  static async getMenus() {
    await new Promise(resolve => setTimeout(resolve, 500));
    
    return {
      code: 200,
      message: '获取成功',
      data: [
        {
          id: 1,
          name: '系统管理',
          path: '/system',
          icon: 'Setting',
          children: [
            { id: 11, name: '用户管理', path: '/system/user', icon: 'User' },
            { id: 12, name: '角色管理', path: '/system/role', icon: 'UserFilled' },
            { id: 13, name: '权限管理', path: '/system/permission', icon: 'Lock' }
          ]
        },
        {
          id: 2,
          name: '用户管理',
          path: '/user',
          icon: 'User',
          children: [
            { id: 21, name: 'C端用户', path: '/user/customer', icon: 'User' },
            { id: 22, name: '分销员管理', path: '/user/distributor', icon: 'UserFilled' },
            { id: 23, name: '合伙人管理', path: '/user/partner', icon: 'Crown' }
          ]
        },
        {
          id: 3,
          name: '商家管理',
          path: '/merchant',
          icon: 'Shop',
          children: [
            { id: 31, name: '商家列表', path: '/merchant/list', icon: 'Shop' },
            { id: 32, name: '商家审核', path: '/merchant/audit', icon: 'DocumentChecked' },
            { id: 33, name: '店铺管理', path: '/merchant/store', icon: 'House' }
          ]
        },
        {
          id: 4,
          name: '内容管理',
          path: '/content',
          icon: 'Document',
          children: [
            { id: 41, name: '信息发布', path: '/content/publish', icon: 'EditPen' },
            { id: 42, name: '内容审核', path: '/content/audit', icon: 'View' },
            { id: 43, name: '分类管理', path: '/content/category', icon: 'Menu' }
          ]
        },
        {
          id: 5,
          name: '拼车管理',
          path: '/carpool',
          icon: 'Van',
          children: [
            { id: 51, name: '拼车信息', path: '/carpool/info', icon: 'Van' },
            { id: 52, name: '司机认证', path: '/carpool/driver', icon: 'UserFilled' },
            { id: 53, name: '拼车订单', path: '/carpool/order', icon: 'Tickets' }
          ]
        },
        {
          id: 6,
          name: '营销管理',
          path: '/marketing',
          icon: 'Present',
          children: [
            { id: 61, name: '活动管理', path: '/marketing/activity', icon: 'Present' },
            { id: 62, name: '优惠券', path: '/marketing/coupon', icon: 'Ticket' },
            { id: 63, name: '分销管理', path: '/marketing/distribution', icon: 'Share' }
          ]
        },
        {
          id: 7,
          name: '订单管理',
          path: '/order',
          icon: 'Tickets',
          children: [
            { id: 71, name: '订单列表', path: '/order/list', icon: 'Tickets' },
            { id: 72, name: '售后管理', path: '/order/service', icon: 'CustomerService' },
            { id: 73, name: '退款管理', path: '/order/refund', icon: 'RefreshLeft' }
          ]
        },
        {
          id: 8,
          name: '财务管理',
          path: '/finance',
          icon: 'Money',
          children: [
            { id: 81, name: '钱包管理', path: '/finance/wallet', icon: 'Wallet' },
            { id: 82, name: '提现审核', path: '/finance/withdraw', icon: 'Money' },
            { id: 83, name: '佣金结算', path: '/finance/commission', icon: 'CreditCard' }
          ]
        },
        {
          id: 9,
          name: '数据分析',
          path: '/data',
          icon: 'DataAnalysis',
          children: [
            { id: 91, name: '数据概览', path: '/data/overview', icon: 'DataBoard' },
            { id: 92, name: '用户分析', path: '/data/user', icon: 'User' },
            { id: 93, name: '营收分析', path: '/data/revenue', icon: 'TrendCharts' }
          ]
        }
      ]
    };
  }
  
  // 模拟获取统计数据
  static async getStatistics() {
    await new Promise(resolve => setTimeout(resolve, 800));
    
    return {
      code: 200,
      message: '获取成功',
      data: {
        totalUsers: 12580,
        totalMerchants: 856,
        totalOrders: 3420,
        totalRevenue: 1580000,
        todayUsers: 128,
        todayOrders: 45,
        todayRevenue: 15800
      }
    };
  }
}

// 设置演示模式标志
window.DEMO_MODE = true;

export default MockAPI;
