{"name": "@dcloudio/vue-cli-plugin-uni", "version": "2.0.2-4060620250520001", "description": "uni-app plugin for vue-cli 3", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/dcloudio/uni-app.git", "directory": "packages/vue-cli-plugin-uni"}, "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "bin": {"uniapp-cli": "./bin/uniapp-cli.js"}, "author": "fxy060608", "license": "Apache-2.0", "dependencies": {"@dcloudio/uni-stat": "^2.0.2-4060620250520001", "buffer-json": "^2.0.0", "clone-deep": "^4.0.1", "cross-env": "^5.2.0", "envinfo": "^6.0.1", "hash-sum": "^1.0.2", "loader-utils": "^1.1.0", "lru-cache": "^4.1.2", "mkdirp": "^0.5.1", "module-alias": "^2.1.0", "neo-async": "^2.6.1", "postcss-import": "^12.0.1", "postcss-selector-parser": "^5.0.0", "postcss-value-parser": "^3.3.1", "strip-json-comments": "^2.0.1", "update-check": "^1.5.3", "webpack-merge": "^4.1.4", "wrap-loader": "^0.2.0", "xregexp": "4.0.0"}, "peerDependencies": {"copy-webpack-plugin": ">=5", "postcss": ">=7"}, "gitHead": "f8e1b7ce5a0f6b98e42e137b04287b99fafa51e8"}