<template>
  <view class="conditions-container">
    <!-- 自定义导航栏 -->
    <view class="navbar">
      <view class="navbar-back" @click="goBack">
        <view class="back-icon"></view>
      </view>
      <text class="navbar-title">成为分销员条件</text>
      <view class="navbar-right">
        <view class="help-icon" @click="showHelp">?</view>
      </view>
    </view>
    
    <!-- 条件选择卡片 -->
    <view class="conditions-card">
      <view class="condition-item" @click="selectCondition('none')">
        <view class="radio-button" :class="{ active: selectedCondition === 'none' }">
          <view class="radio-inner" v-if="selectedCondition === 'none'"></view>
        </view>
        <view class="condition-content">
          <text class="condition-title">无条件</text>
          <text class="condition-desc">任何用户都可以成为分销员</text>
        </view>
      </view>
      
      <view class="condition-item" @click="selectCondition('apply')">
        <view class="radio-button" :class="{ active: selectedCondition === 'apply' }">
          <view class="radio-inner" v-if="selectedCondition === 'apply'"></view>
        </view>
        <view class="condition-content">
          <text class="condition-title">申请成为分销员</text>
          <text class="condition-desc">用户需要提交申请，审核通过后成为分销员</text>
        </view>
      </view>
      
      <view class="condition-item" @click="selectCondition('purchase')">
        <view class="radio-button" :class="{ active: selectedCondition === 'purchase' }">
          <view class="radio-inner" v-if="selectedCondition === 'purchase'"></view>
        </view>
        <view class="condition-content">
          <text class="condition-title">购买商品并申请</text>
          <text class="condition-desc">用户需要购买指定商品并提交申请</text>
        </view>
      </view>
      
      <view class="condition-item" @click="selectCondition('invite')">
        <view class="radio-button" :class="{ active: selectedCondition === 'invite' }">
          <view class="radio-inner" v-if="selectedCondition === 'invite'"></view>
        </view>
        <view class="condition-content">
          <text class="condition-title">邀请成为分销员</text>
          <text class="condition-desc">现有分销员邀请新用户才能成为分销员</text>
        </view>
      </view>
    </view>
    
    <!-- 附加条件卡片 -->
    <view class="additional-card" v-if="selectedCondition === 'purchase'">
      <view class="card-header">
        <text class="card-title">购买商品设置</text>
      </view>
      
      <view class="form-item">
        <text class="form-label">购买金额要求</text>
        <view class="form-input-group">
          <text class="input-prefix">¥</text>
          <input type="digit" v-model="purchaseAmount" class="form-input" placeholder="请输入金额" />
        </view>
      </view>
      
      <view class="form-item">
        <text class="form-label">指定商品</text>
        <view class="form-switch">
          <switch :checked="specificProducts" @change="toggleSpecificProducts" color="#6B0FBE" />
        </view>
      </view>
      
      <view class="product-list" v-if="specificProducts">
        <view class="product-item" v-for="(product, index) in selectedProducts" :key="index">
          <image class="product-image" :src="product.image" mode="aspectFill"></image>
          <view class="product-info">
            <text class="product-name">{{product.name}}</text>
            <text class="product-price">¥{{product.price}}</text>
          </view>
          <view class="product-remove" @click="removeProduct(index)">×</view>
        </view>
        
        <view class="add-product" @click="addProduct">
          <view class="add-icon">+</view>
          <text class="add-text">添加商品</text>
        </view>
      </view>
    </view>
    
    <!-- 审核设置卡片 -->
    <view class="approval-card" v-if="selectedCondition === 'apply' || selectedCondition === 'purchase'">
      <view class="card-header">
        <text class="card-title">审核设置</text>
      </view>
      
      <view class="form-item">
        <text class="form-label">需要审核</text>
        <view class="form-switch">
          <switch :checked="requireApproval" @change="toggleRequireApproval" color="#6B0FBE" />
        </view>
      </view>
      
      <view class="form-item" v-if="requireApproval">
        <text class="form-label">自动通过审核</text>
        <view class="form-switch">
          <switch :checked="autoApprove" @change="toggleAutoApprove" color="#6B0FBE" />
        </view>
      </view>
    </view>
    
    <!-- 保存按钮 -->
    <view class="button-container">
      <button class="save-button" @click="saveSettings">保存设置</button>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted } from 'vue';

// 选中的条件
const selectedCondition = ref('purchase');

// 购买金额
const purchaseAmount = ref('100');

// 是否指定商品
const specificProducts = ref(true);

// 是否需要审核
const requireApproval = ref(true);

// 是否自动通过审核
const autoApprove = ref(false);

// 选中的商品
const selectedProducts = ref([
  {
    id: '1',
    name: '分销员入门套餐',
    price: '99.00',
    image: '/static/images/products/product-1.jpg'
  },
  {
    id: '2',
    name: '分销员高级套餐',
    price: '199.00',
    image: '/static/images/products/product-2.jpg'
  }
]);

// 页面加载
onMounted(() => {
  // 获取分销员条件设置
  getConditionSettings();
});

// 获取分销员条件设置
const getConditionSettings = () => {
  // 这里应该从API获取设置
  // 暂时使用模拟数据
};

// 选择条件
const selectCondition = (condition) => {
  selectedCondition.value = condition;
};

// 切换是否指定商品
const toggleSpecificProducts = (e) => {
  specificProducts.value = e.detail.value;
};

// 切换是否需要审核
const toggleRequireApproval = (e) => {
  requireApproval.value = e.detail.value;
  
  // 如果不需要审核，则不自动通过
  if (!requireApproval.value) {
    autoApprove.value = false;
  }
};

// 切换是否自动通过审核
const toggleAutoApprove = (e) => {
  autoApprove.value = e.detail.value;
};

// 添加商品
const addProduct = () => {
  uni.showToast({
    title: '添加商品功能开发中',
    icon: 'none'
  });
};

// 移除商品
const removeProduct = (index) => {
  selectedProducts.value.splice(index, 1);
};

// 保存设置
const saveSettings = () => {
  // 这里应该调用API保存设置
  
  uni.showLoading({
    title: '保存中...'
  });
  
  setTimeout(() => {
    uni.hideLoading();
    uni.showToast({
      title: '保存成功',
      icon: 'success'
    });
    
    // 返回上一页
    setTimeout(() => {
      uni.navigateBack();
    }, 1500);
  }, 1000);
};

// 返回上一页
const goBack = () => {
  uni.navigateBack();
};

// 显示帮助
const showHelp = () => {
  uni.showModal({
    title: '分销员条件帮助',
    content: '您可以设置用户成为分销员的条件，包括无条件、申请、购买商品和邀请等方式。',
    showCancel: false
  });
};
</script>

<style lang="scss">
.conditions-container {
  min-height: 100vh;
  background-color: #F5F7FA;
  padding-bottom: env(safe-area-inset-bottom);
}

/* 导航栏样式 */
.navbar {
  position: sticky;
  top: 0;
  left: 0;
  right: 0;
  background: linear-gradient(135deg, #A764CA, #6B0FBE);
  color: #fff;
  padding: 44px 16px 15px;
  display: flex;
  align-items: center;
  z-index: 100;
  box-shadow: 0 2px 10px rgba(107, 15, 190, 0.15);
}

.navbar-back {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-icon {
  width: 12px;
  height: 12px;
  border-left: 2px solid #fff;
  border-bottom: 2px solid #fff;
  transform: rotate(45deg);
}

.navbar-title {
  flex: 1;
  text-align: center;
  font-size: 18px;
  font-weight: 600;
  letter-spacing: 0.5px;
}

.navbar-right {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.help-icon {
  width: 24px;
  height: 24px;
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: bold;
}

/* 条件卡片样式 */
.conditions-card {
  margin: 16px;
  background-color: #FFFFFF;
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.04);
  padding: 8px 0;
}

.condition-item {
  display: flex;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #F0F0F0;
}

.condition-item:last-child {
  border-bottom: none;
}

.radio-button {
  width: 20px;
  height: 20px;
  border-radius: 10px;
  border: 2px solid #CCCCCC;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  flex-shrink: 0;
}

.radio-button.active {
  border-color: #6B0FBE;
}

.radio-inner {
  width: 10px;
  height: 10px;
  border-radius: 5px;
  background-color: #6B0FBE;
}

.condition-content {
  flex: 1;
}

.condition-title {
  font-size: 15px;
  color: #333;
  margin-bottom: 4px;
}

.condition-desc {
  font-size: 12px;
  color: #999;
}

/* 附加条件卡片样式 */
.additional-card, .approval-card {
  margin: 16px;
  background-color: #FFFFFF;
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.04);
}

.card-header {
  padding: 16px;
  border-bottom: 1px solid #F0F0F0;
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.form-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #F0F0F0;
}

.form-item:last-child {
  border-bottom: none;
}

.form-label {
  font-size: 15px;
  color: #333;
}

.form-input-group {
  display: flex;
  align-items: center;
  background-color: #F5F7FA;
  border-radius: 8px;
  padding: 0 12px;
  height: 36px;
}

.input-prefix {
  font-size: 15px;
  color: #333;
  margin-right: 4px;
}

.form-input {
  height: 36px;
  width: 80px;
  font-size: 15px;
  color: #333;
  text-align: right;
}

.form-switch {
  height: 36px;
  display: flex;
  align-items: center;
}

/* 商品列表样式 */
.product-list {
  padding: 16px;
}

.product-item {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  background-color: #F5F7FA;
  border-radius: 12px;
  padding: 10px;
}

.product-image {
  width: 50px;
  height: 50px;
  border-radius: 8px;
  margin-right: 12px;
}

.product-info {
  flex: 1;
}

.product-name {
  font-size: 14px;
  color: #333;
  margin-bottom: 4px;
}

.product-price {
  font-size: 13px;
  color: #FF3B30;
}

.product-remove {
  width: 24px;
  height: 24px;
  border-radius: 12px;
  background-color: rgba(255, 59, 48, 0.1);
  color: #FF3B30;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
}

.add-product {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 12px;
  border: 1px dashed #CCCCCC;
  border-radius: 12px;
}

.add-icon {
  width: 20px;
  height: 20px;
  border-radius: 10px;
  background-color: #6B0FBE;
  color: #FFFFFF;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 8px;
  font-size: 16px;
}

.add-text {
  font-size: 14px;
  color: #6B0FBE;
}

/* 按钮样式 */
.button-container {
  margin: 24px 16px;
}

.save-button {
  height: 44px;
  border-radius: 22px;
  background: linear-gradient(135deg, #A764CA, #6B0FBE);
  color: #FFFFFF;
  font-size: 16px;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
}
</style>