"use strict";
const common_vendor = require("../../../common/vendor.js");
const common_assets = require("../../../common/assets.js");
const utils_userProfile = require("../../../utils/userProfile.js");
const utils_navigation = require("../../../utils/navigation.js");
const _sfc_main = {
  __name: "my-benefits",
  setup(__props) {
    const statusBarHeight = common_vendor.ref(20);
    const userInfo = common_vendor.ref({
      nickname: "",
      avatar: ""
    });
    const statistics = common_vendor.ref({
      couponCount: 3,
      redPacketCount: 2,
      pointsCount: 520,
      activityRewardCount: 1
    });
    const recentBenefits = common_vendor.ref([
      {
        id: "1",
        type: "coupon",
        name: "满100减20优惠券",
        description: "适用于全部商家",
        time: "2023-05-15 获得",
        status: "available"
      },
      {
        id: "2",
        type: "redPacket",
        name: "5元现金红包",
        description: '来自"品味咖啡"',
        time: "2023-05-14 获得",
        status: "available"
      },
      {
        id: "3",
        type: "points",
        name: "签到积分",
        description: "+10积分",
        time: "2023-05-13 获得",
        status: "used"
      },
      {
        id: "4",
        type: "coupon",
        name: "满50减10优惠券",
        description: "适用于餐饮美食",
        time: "2023-05-10 获得",
        status: "expired"
      }
    ]);
    const recommendedActivities = common_vendor.ref([
      {
        id: "1",
        name: "夏日狂欢购物节",
        description: "满100送50，多重好礼等你来",
        time: "05-20 至 05-30",
        tag: "购物",
        image: "/static/images/activity/shopping.jpg"
      },
      {
        id: "2",
        name: "美食品鉴会",
        description: "新店开业，免费品尝",
        time: "05-25 19:00",
        tag: "美食",
        image: "/static/images/activity/food.jpg"
      },
      {
        id: "3",
        name: "亲子嘉年华",
        description: "儿童乐园门票半价",
        time: "05-28 全天",
        tag: "亲子",
        image: "/static/images/activity/family.jpg"
      }
    ]);
    common_vendor.onMounted(() => {
      const sysInfo = common_vendor.index.getSystemInfoSync();
      statusBarHeight.value = sysInfo.statusBarHeight;
      getUserInfo();
      loadBenefitsData();
    });
    const goBack = () => {
      common_vendor.index.navigateBack();
    };
    const navigateTo = (url) => {
      utils_navigation.smartNavigate(url).catch((err) => {
        common_vendor.index.__f__("error", "at subPackages/user/pages/my-benefits.vue:238", "智能导航失败:", err);
      });
    };
    const getUserInfo = () => {
      const localUserInfo = utils_userProfile.getLocalUserInfo();
      if (localUserInfo) {
        userInfo.value = localUserInfo;
      }
    };
    const loadBenefitsData = () => {
      common_vendor.index.__f__("log", "at subPackages/user/pages/my-benefits.vue:255", "加载福利数据");
    };
    const showAllBenefits = () => {
      common_vendor.index.showToast({
        title: "功能开发中",
        icon: "none"
      });
    };
    const viewBenefitDetail = (item) => {
      switch (item.type) {
        case "coupon":
          navigateTo("/pages/services/coupon");
          break;
        case "redPacket":
          navigateTo("/pages/user/my-red-packets");
          break;
        case "points":
          navigateTo("/subPackages/checkin/pages/points");
          break;
        default:
          common_vendor.index.showToast({
            title: "查看详情",
            icon: "none"
          });
      }
    };
    const getBenefitIcon = (type) => {
      switch (type) {
        case "coupon":
          return "/static/images/tabbar/卡券.png";
        case "redPacket":
          return "/static/images/tabbar/我的红包.png";
        case "points":
          return "/static/images/tabbar/每日签到.png";
        default:
          return "/static/images/tabbar/活动.png";
      }
    };
    const getBenefitStatus = (status) => {
      switch (status) {
        case "available":
          return "可使用";
        case "used":
          return "已使用";
        case "expired":
          return "已过期";
        default:
          return "未知";
      }
    };
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_assets._imports_0$5,
        b: common_vendor.o(goBack),
        c: statusBarHeight.value + "px",
        d: common_vendor.t(userInfo.value.nickname || "微信用户"),
        e: common_vendor.t(statistics.value.couponCount || 0),
        f: common_vendor.o(($event) => navigateTo("/pages/services/coupon")),
        g: common_vendor.t(statistics.value.redPacketCount || 0),
        h: common_vendor.o(($event) => navigateTo("/pages/user/my-red-packets")),
        i: common_vendor.t(statistics.value.pointsCount || 0),
        j: common_vendor.o(($event) => navigateTo("/subPackages/checkin/pages/points")),
        k: common_assets._imports_1$12,
        l: statistics.value.couponCount > 0
      }, statistics.value.couponCount > 0 ? {
        m: common_vendor.t(statistics.value.couponCount)
      } : {}, {
        n: common_assets._imports_0$14,
        o: common_vendor.o(($event) => navigateTo("/pages/services/coupon")),
        p: common_assets._imports_3$23,
        q: statistics.value.redPacketCount > 0
      }, statistics.value.redPacketCount > 0 ? {
        r: common_vendor.t(statistics.value.redPacketCount)
      } : {}, {
        s: common_assets._imports_0$14,
        t: common_vendor.o(($event) => navigateTo("/pages/user/my-red-packets")),
        v: common_assets._imports_4$5,
        w: common_assets._imports_0$14,
        x: common_vendor.o(($event) => navigateTo("/subPackages/checkin/pages/points")),
        y: common_assets._imports_5$15,
        z: statistics.value.activityRewardCount > 0
      }, statistics.value.activityRewardCount > 0 ? {
        A: common_vendor.t(statistics.value.activityRewardCount)
      } : {}, {
        B: common_assets._imports_0$14,
        C: common_vendor.o(($event) => navigateTo("/pages/user-center/activity-rewards")),
        D: common_vendor.o(showAllBenefits),
        E: recentBenefits.value.length > 0
      }, recentBenefits.value.length > 0 ? {
        F: common_vendor.f(recentBenefits.value, (item, index, i0) => {
          return {
            a: getBenefitIcon(item.type),
            b: common_vendor.t(item.name),
            c: common_vendor.t(item.description),
            d: common_vendor.t(item.time),
            e: common_vendor.t(getBenefitStatus(item.status)),
            f: item.status === "expired" ? 1 : "",
            g: index,
            h: common_vendor.o(($event) => viewBenefitDetail(item), index)
          };
        })
      } : {
        G: common_assets._imports_1$3
      }, {
        H: common_vendor.o(($event) => navigateTo("/pages/activity/list")),
        I: common_vendor.f(recommendedActivities.value, (item, index, i0) => {
          return {
            a: item.image,
            b: common_vendor.t(item.name),
            c: common_vendor.t(item.description),
            d: common_vendor.t(item.time),
            e: common_vendor.t(item.tag),
            f: index,
            g: common_vendor.o(($event) => navigateTo("/pages/activity/detail?id=" + item.id), index)
          };
        })
      });
    };
  }
};
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-d81dfc96"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/subPackages/user/pages/my-benefits.js.map
