"use strict";
const common_vendor = require("../../common/vendor.js");
const common_assets = require("../../common/assets.js");
if (!Math) {
  ReportCard();
}
const ReportCard = () => "../../components/ReportCard.js";
const _sfc_main = {
  __name: "house-rent-detail",
  setup(__props) {
    const statusBarHeight = common_vendor.ref(20);
    common_vendor.onMounted(() => {
      try {
        const sysInfo = common_vendor.index.getSystemInfoSync();
        statusBarHeight.value = sysInfo.statusBarHeight || 20;
      } catch (e) {
        common_vendor.index.__f__("error", "at pages/publish/house-rent-detail.vue:245", "获取状态栏高度失败", e);
      }
      common_vendor.index.getSystemInfo({
        success: (res) => {
          if (common_vendor.index.canIUse("setNavigationBarColor")) {
            common_vendor.index.setNavigationBarColor({
              frontColor: "#000000",
              backgroundColor: "#ffffff"
            });
          }
        }
      });
    });
    const goBack = () => {
      common_vendor.index.navigateBack({
        fail: () => {
          common_vendor.index.switchTab({
            url: "/pages/index/index"
          });
        }
      });
    };
    const formatTime = (timestamp) => {
      const date = new Date(timestamp);
      return `${date.getMonth() + 1}月${date.getDate()}日`;
    };
    const isCollected = common_vendor.ref(false);
    const houseData = common_vendor.ref({
      id: "house12345",
      title: "精装两室一厅出租",
      price: "2000元/月",
      tags: ["精装修", "拎包入住", "可短租"],
      publishTime: Date.now() - 864e5 * 2,
      // 2天前
      images: [
        "/static/images/house1.jpg",
        "/static/images/house2.jpg",
        "/static/images/house3.jpg"
      ],
      type: "两室一厅",
      area: "磁县城区",
      size: "80平方米",
      orientation: "南北通透",
      configs: [
        { name: "空调", icon: "icon-ac" },
        { name: "热水器", icon: "icon-water-heater" },
        { name: "洗衣机", icon: "icon-washer" },
        { name: "冰箱", icon: "icon-fridge" },
        { name: "电视", icon: "icon-tv" },
        { name: "宽带", icon: "icon-wifi" }
      ],
      details: [
        { label: "装修情况", value: "精装修" },
        { label: "所在楼层", value: "6/18层" },
        { label: "建筑年代", value: "2018年" },
        { label: "房屋用途", value: "住宅" }
      ],
      location: {
        address: "磁县城区XX路XX号",
        latitude: 36.123456,
        longitude: 114.123456,
        surroundings: [
          { name: "地铁站", distance: "500米", icon: "icon-subway" },
          { name: "公交站", distance: "200米", icon: "icon-bus" },
          { name: "超市", distance: "300米", icon: "icon-supermarket" },
          { name: "学校", distance: "800米", icon: "icon-school" }
        ]
      },
      requirements: [
        { label: "租期要求", value: "半年起租" },
        { label: "付款方式", value: "押一付三" },
        { label: "入住时间", value: "随时" },
        { label: "其他要求", value: "爱干净，不养宠物" }
      ],
      landlord: {
        name: "王先生",
        avatar: "/static/images/avatar.png",
        type: "个人",
        rating: "A+",
        isVerified: true
      },
      contact: {
        name: "王先生",
        phone: "13912345678"
      }
    });
    const markers = common_vendor.ref([{
      id: 1,
      latitude: houseData.value.location.latitude,
      longitude: houseData.value.location.longitude,
      title: houseData.value.title
    }]);
    const relatedHouses = common_vendor.ref([
      {
        id: "house001",
        title: "精装两室一厅出租",
        price: "1800元/月",
        type: "两室一厅",
        area: "磁县城区",
        image: "/static/images/house-similar1.jpg",
        tags: ["精装修", "拎包入住", "可短租"]
      },
      {
        id: "house002",
        title: "南北通透三居",
        price: "2200元/月",
        type: "三室两厅",
        area: "磁县城区",
        image: "/static/images/house-similar2.jpg",
        tags: ["南北通透", "带电梯"]
      },
      {
        id: "house003",
        title: "高层景观房出租",
        price: "2500元/月",
        type: "三室两厅",
        area: "磁县城区",
        image: "/static/images/house-similar3.jpg",
        tags: ["高层", "景观好"]
      }
    ]);
    const toggleCollect = () => {
      isCollected.value = !isCollected.value;
      if (isCollected.value) {
        common_vendor.index.showToast({
          title: "收藏成功",
          icon: "success"
        });
      }
    };
    const callPhone = () => {
      common_vendor.index.makePhoneCall({
        phoneNumber: houseData.value.contact.phone,
        fail: () => {
          common_vendor.index.showToast({
            title: "拨打电话失败",
            icon: "none"
          });
        }
      });
    };
    const navigateToHouse = (id) => {
      if (id === houseData.value.id)
        return;
      common_vendor.index.navigateTo({ url: `/pages/publish/house-rent-detail?id=${id}` });
    };
    const navigateToHouseList = (e) => {
      var _a;
      if (e)
        e.stopPropagation();
      const houseCategory = ((_a = houseData.value.tags) == null ? void 0 : _a[0]) || "";
      common_vendor.index.navigateTo({
        url: `/subPackages/service/pages/filter?type=house_rent&title=${encodeURIComponent("房屋出租")}&category=${encodeURIComponent(houseCategory)}&active=house_rent`
      });
    };
    const goToHome = () => {
      common_vendor.index.switchTab({
        url: "/pages/index/index"
      });
    };
    const openChat = () => {
      if (!houseData.value.landlord || !houseData.value.landlord.id) {
        common_vendor.index.showToast({
          title: "无法获取房东信息",
          icon: "none"
        });
        return;
      }
      common_vendor.index.navigateTo({
        url: `/pages/chat/index?userId=${houseData.value.landlord.id}&username=${encodeURIComponent(houseData.value.landlord.name || "房东")}`
      });
    };
    const posterImagePath = common_vendor.ref("");
    const showPosterFlag = common_vendor.ref(false);
    const generateShareImage = () => {
      var _a;
      common_vendor.index.showLoading({
        title: "正在生成海报...",
        mask: true
      });
      const posterData = {
        title: houseData.value.title,
        rent: houseData.value.price,
        type: houseData.value.type,
        area: houseData.value.area,
        address: houseData.value.location.address,
        phone: houseData.value.contact.phone,
        description: ((_a = houseData.value.details.find((item) => item.label === "房屋用途")) == null ? void 0 : _a.value) === "住宅" ? "适合家庭居住，环境优美" : "",
        qrcode: "/static/images/tabbar/客服微信.png",
        logo: "/static/images/tabbar/房屋租赁.png",
        bgImage: houseData.value.images[0] || "/static/images/banner/banner-1.png"
      };
      const ctx = common_vendor.index.createCanvasContext("posterCanvas");
      ctx.save();
      ctx.drawImage(posterData.bgImage, 0, 0, 600, 400);
      ctx.setFillStyle("rgba(0, 0, 0, 0.35)");
      ctx.fillRect(0, 0, 600, 900);
      ctx.restore();
      ctx.save();
      ctx.setFillStyle("#ffffff");
      ctx.fillRect(30, 280, 540, 550);
      ctx.restore();
      ctx.save();
      ctx.beginPath();
      ctx.arc(300, 200, 80, 0, 2 * Math.PI);
      ctx.setFillStyle("#ffffff");
      ctx.fill();
      ctx.clip();
      ctx.drawImage(posterData.logo, 220, 120, 160, 160);
      ctx.restore();
      ctx.setFillStyle("#333333");
      ctx.setFontSize(32);
      ctx.setTextAlign("center");
      ctx.fillText(posterData.title, 300, 350);
      ctx.setFillStyle("#FF6B6B");
      ctx.setFontSize(28);
      ctx.fillText(posterData.rent + "/月", 300, 400);
      ctx.beginPath();
      ctx.setStrokeStyle("#eeeeee");
      ctx.setLineWidth(2);
      ctx.moveTo(100, 430);
      ctx.lineTo(500, 430);
      ctx.stroke();
      ctx.setFillStyle("#666666");
      ctx.setFontSize(24);
      ctx.setTextAlign("left");
      ctx.fillText("房屋类型: " + posterData.type, 80, 480);
      ctx.fillText("面积: " + posterData.area, 80, 520);
      ctx.fillText("位置: " + posterData.address, 80, 560);
      const wrapText = (ctx2, text, x, y, maxWidth, lineHeight) => {
        if (text.length === 0)
          return;
        const words = text.split("");
        let line = "";
        let testLine = "";
        let lineCount = 0;
        for (let n = 0; n < words.length; n++) {
          testLine += words[n];
          const metrics = ctx2.measureText(testLine);
          const testWidth = metrics.width;
          if (testWidth > maxWidth && n > 0) {
            ctx2.fillText(line, x, y + lineCount * lineHeight);
            line = words[n];
            testLine = words[n];
            lineCount++;
            if (lineCount >= 2) {
              line += "...";
              ctx2.fillText(line, x, y + lineCount * lineHeight);
              break;
            }
          } else {
            line = testLine;
          }
        }
        if (lineCount < 2) {
          ctx2.fillText(line, x, y + lineCount * lineHeight);
        }
      };
      ctx.setFillStyle("#666666");
      ctx.fillText("描述:", 80, 600);
      wrapText(ctx, posterData.description, 80, 630, 440, 35);
      if (posterData.phone) {
        ctx.fillText("联系电话: " + posterData.phone, 80, 680);
      }
      ctx.drawImage(posterData.qrcode, 225, 720, 150, 150);
      ctx.setFillStyle("#999999");
      ctx.setFontSize(20);
      ctx.setTextAlign("center");
      ctx.fillText("长按识别二维码查看详情", 300, 880);
      ctx.setFillStyle("#333333");
      ctx.setFontSize(24);
      ctx.fillText("磁县同城 - 房屋租赁", 300, 840);
      ctx.draw(false, () => {
        setTimeout(() => {
          common_vendor.index.canvasToTempFilePath({
            canvasId: "posterCanvas",
            success: (res) => {
              common_vendor.index.hideLoading();
              showPosterModal(res.tempFilePath);
            },
            fail: (err) => {
              common_vendor.index.__f__("error", "at pages/publish/house-rent-detail.vue:610", "生成海报失败", err);
              common_vendor.index.hideLoading();
              common_vendor.index.showToast({
                title: "生成海报失败",
                icon: "none"
              });
            }
          });
        }, 800);
      });
    };
    const showPosterModal = (posterPath) => {
      posterImagePath.value = posterPath;
      showPosterFlag.value = true;
      common_vendor.index.showModal({
        title: "海报已生成",
        content: "海报已生成，是否保存到相册？",
        confirmText: "保存",
        success: (res) => {
          if (res.confirm) {
            savePosterToAlbum(posterPath);
          } else {
            common_vendor.index.previewImage({
              urls: [posterPath],
              current: posterPath
            });
          }
        }
      });
    };
    const savePosterToAlbum = (posterPath) => {
      common_vendor.index.showLoading({
        title: "正在保存..."
      });
      common_vendor.index.saveImageToPhotosAlbum({
        filePath: posterPath,
        success: () => {
          common_vendor.index.hideLoading();
          common_vendor.index.showToast({
            title: "已保存到相册",
            icon: "success"
          });
        },
        fail: (err) => {
          common_vendor.index.hideLoading();
          common_vendor.index.__f__("error", "at pages/publish/house-rent-detail.vue:662", "保存失败", err);
          if (err.errMsg.indexOf("auth deny") > -1) {
            common_vendor.index.showModal({
              title: "提示",
              content: "保存失败，请授权相册权限后重试",
              confirmText: "去设置",
              success: (res) => {
                if (res.confirm) {
                  common_vendor.index.openSetting();
                }
              }
            });
          } else {
            common_vendor.index.showToast({
              title: "保存失败",
              icon: "none"
            });
          }
        }
      });
    };
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_assets._imports_0$5,
        b: common_vendor.o(goBack),
        c: statusBarHeight.value + "px",
        d: common_vendor.t(houseData.value.title),
        e: common_vendor.t(houseData.value.price),
        f: common_vendor.f(houseData.value.tags, (tag, index, i0) => {
          return {
            a: common_vendor.t(tag),
            b: index
          };
        }),
        g: common_vendor.t(formatTime(houseData.value.publishTime)),
        h: common_vendor.f(houseData.value.images, (image, index, i0) => {
          return {
            a: image,
            b: index
          };
        }),
        i: common_vendor.t(houseData.value.type),
        j: common_vendor.t(houseData.value.area),
        k: common_vendor.t(houseData.value.size),
        l: common_vendor.t(houseData.value.orientation),
        m: common_vendor.f(houseData.value.configs, (item, index, i0) => {
          return {
            a: common_vendor.n(item.icon),
            b: common_vendor.t(item.name),
            c: index
          };
        }),
        n: common_vendor.f(houseData.value.details, (item, index, i0) => {
          return {
            a: common_vendor.t(item.label),
            b: common_vendor.t(item.value),
            c: index
          };
        }),
        o: common_vendor.t(houseData.value.location.address),
        p: houseData.value.location.latitude,
        q: houseData.value.location.longitude,
        r: markers.value,
        s: common_vendor.f(houseData.value.location.surroundings, (item, index, i0) => {
          return {
            a: common_vendor.n(item.icon),
            b: common_vendor.t(item.name),
            c: common_vendor.t(item.distance),
            d: index
          };
        }),
        t: common_vendor.f(houseData.value.requirements, (item, index, i0) => {
          return {
            a: common_vendor.t(item.label),
            b: common_vendor.t(item.value),
            c: index
          };
        }),
        v: houseData.value.landlord.avatar,
        w: common_vendor.t(houseData.value.landlord.name),
        x: common_vendor.t(houseData.value.landlord.type),
        y: common_vendor.t(houseData.value.landlord.rating),
        z: houseData.value.landlord.isVerified
      }, houseData.value.landlord.isVerified ? {} : {}, {
        A: common_vendor.t(houseData.value.contact.name),
        B: common_vendor.t(houseData.value.contact.phone),
        C: common_vendor.o(callPhone),
        D: common_vendor.p({
          ["content-id"]: houseData.value.id,
          ["content-type"]: "house-rent"
        }),
        E: common_vendor.f(relatedHouses.value.slice(0, 3), (house, index, i0) => {
          return common_vendor.e({
            a: house.image,
            b: common_vendor.t(house.title),
            c: common_vendor.t(house.type),
            d: common_vendor.t(house.area),
            e: common_vendor.f(house.tags.slice(0, 2), (tag, tagIndex, i1) => {
              return {
                a: common_vendor.t(tag),
                b: tagIndex
              };
            }),
            f: house.tags && house.tags.length > 2
          }, house.tags && house.tags.length > 2 ? {
            g: common_vendor.t(house.tags.length - 2)
          } : {}, {
            h: common_vendor.t(house.price),
            i: index,
            j: common_vendor.o(($event) => navigateToHouse(house.id), index)
          });
        }),
        F: relatedHouses.value.length === 0
      }, relatedHouses.value.length === 0 ? {
        G: common_assets._imports_1$3
      } : {}, {
        H: relatedHouses.value.length > 0
      }, relatedHouses.value.length > 0 ? {
        I: common_vendor.o(navigateToHouseList)
      } : {}, {
        J: common_assets._imports_12,
        K: common_vendor.o(goToHome),
        L: common_assets._imports_3$2,
        M: common_vendor.o(toggleCollect),
        N: common_assets._imports_3$3,
        O: common_assets._imports_14,
        P: common_vendor.o(openChat),
        Q: common_vendor.o(callPhone),
        R: common_assets._imports_10,
        S: common_vendor.o(generateShareImage)
      });
    };
  }
};
wx.createPage(_sfc_main);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/publish/house-rent-detail.js.map
