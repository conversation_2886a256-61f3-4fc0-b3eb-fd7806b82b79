/*!
  * @intlify/core-base v9.1.7
  * (c) 2021 ka<PERSON><PERSON> ka<PERSON>
  * Released under the MIT License.
  */
const e=/\{([0-9a-zA-Z]+)\}/g;const t=e=>JSON.stringify(e).replace(/\u2028/g,"\\u2028").replace(/\u2029/g,"\\u2029").replace(/\u0027/g,"\\u0027"),n=e=>"number"==typeof e&&isFinite(e),r=e=>"[object RegExp]"===k(e),o=e=>g(e)&&0===Object.keys(e).length;function c(e,t){"undefined"!=typeof console&&(console.warn("[intlify] "+e),t&&console.warn(t.stack))}const s=Object.assign;function a(e){return e.replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&apos;")}const u=Object.prototype.hasOwnProperty;function l(e,t){return u.call(e,t)}const i=Array.isArray,f=e=>"function"==typeof e,p=e=>"string"==typeof e,m=e=>"boolean"==typeof e,d=e=>null!==e&&"object"==typeof e,h=Object.prototype.toString,k=e=>h.call(e),g=e=>"[object Object]"===k(e),b=[];b[0]={w:[0],i:[3,0],"[":[4],o:[7]},b[1]={w:[1],".":[2],"[":[4],o:[7]},b[2]={w:[2],i:[3,0],0:[3,0]},b[3]={i:[3,0],0:[3,0],w:[1,1],".":[2,1],"[":[4,1],o:[7,1]},b[4]={"'":[5,0],'"':[6,0],"[":[4,2],"]":[1,3],o:8,l:[4,0]},b[5]={"'":[4,0],o:8,l:[5,0]},b[6]={'"':[4,0],o:8,l:[6,0]};const y=/^\s?(?:true|false|-?[\d.]+|'[^']*'|"[^"]*")\s?$/;function x(e){if(null==e)return"o";switch(e.charCodeAt(0)){case 91:case 93:case 46:case 34:case 39:return e;case 95:case 36:case 45:return"i";case 9:case 10:case 13:case 160:case 65279:case 8232:case 8233:return"w"}return"i"}function w(e){const t=e.trim();return("0"!==e.charAt(0)||!isNaN(parseInt(e)))&&(y.test(t)?function(e){const t=e.charCodeAt(0);return t!==e.charCodeAt(e.length-1)||34!==t&&39!==t?e:e.slice(1,-1)}(t):"*"+t)}function C(e){const t=[];let n,r,o,c,s,a,u,l=-1,i=0,f=0;const p=[];function m(){const t=e[l+1];if(5===i&&"'"===t||6===i&&'"'===t)return l++,o="\\"+t,p[0](),!0}for(p[0]=()=>{void 0===r?r=o:r+=o},p[1]=()=>{void 0!==r&&(t.push(r),r=void 0)},p[2]=()=>{p[0](),f++},p[3]=()=>{if(f>0)f--,i=4,p[0]();else{if(f=0,void 0===r)return!1;if(r=w(r),!1===r)return!1;p[1]()}};null!==i;)if(l++,n=e[l],"\\"!==n||!m()){if(c=x(n),u=b[i],s=u[c]||u.l||8,8===s)return;if(i=s[0],void 0!==s[1]&&(a=p[s[1]],a&&(o=n,!1===a())))return;if(7===i)return t}}const _=new Map;function v(e,t){if(!d(e))return null;let n=_.get(t);if(n||(n=C(t),n&&_.set(t,n)),!n)return null;const r=n.length;let o=e,c=0;for(;c<r;){const e=o[n[c]];if(void 0===e)return null;o=e,c++}return o}function L(e){if(!d(e))return e;for(const t in e)if(l(e,t))if(t.includes(".")){const n=t.split("."),r=n.length-1;let o=e;for(let e=0;e<r;e++)n[e]in o||(o[n[e]]={}),o=o[n[e]];o[n[r]]=e[t],delete e[t],d(o[n[r]])&&L(o[n[r]])}else d(e[t])&&L(e[t]);return e}const T=e=>e,P=e=>"",O="text",F=e=>0===e.length?"":e.join(""),N=e=>null==e?"":i(e)||g(e)&&e.toString===h?JSON.stringify(e,null,2):String(e);function $(e,t){return e=Math.abs(e),2===t?e?e>1?1:0:1:e?Math.min(e,2):0}function S(e={}){const t=e.locale,r=function(e){const t=n(e.pluralIndex)?e.pluralIndex:-1;return e.named&&(n(e.named.count)||n(e.named.n))?n(e.named.count)?e.named.count:n(e.named.n)?e.named.n:t:t}(e),o=d(e.pluralRules)&&p(t)&&f(e.pluralRules[t])?e.pluralRules[t]:$,c=d(e.pluralRules)&&p(t)&&f(e.pluralRules[t])?$:void 0,s=e.list||[],a=e.named||{};n(e.pluralIndex)&&function(e,t){t.count||(t.count=e),t.n||(t.n=e)}(r,a);function u(t){const n=f(e.messages)?e.messages(t):!!d(e.messages)&&e.messages[t];return n||(e.parent?e.parent.message(t):P)}const l=g(e.processor)&&f(e.processor.normalize)?e.processor.normalize:F,i=g(e.processor)&&f(e.processor.interpolate)?e.processor.interpolate:N,m={list:e=>s[e],named:e=>a[e],plural:e=>e[o(r,e.length,c)],linked:(t,n)=>{const r=u(t)(m);return p(n)?(o=n,e.modifiers?e.modifiers[o]:T)(r):r;var o},message:u,type:g(e.processor)&&p(e.processor.type)?e.processor.type:"text",interpolate:i,normalize:l};return m}function I(e,t,n={}){const{domain:r}=n,o=new SyntaxError(String(e));return o.code=e,t&&(o.location=t),o.domain=r,o}function E(e){throw e}function A(e,t,n){const r={start:e,end:t};return null!=n&&(r.source=n),r}const W=String.fromCharCode(8232),j=String.fromCharCode(8233);function M(e){const t=e;let n=0,r=1,o=1,c=0;const s=e=>"\r"===t[e]&&"\n"===t[e+1],a=e=>t[e]===j,u=e=>t[e]===W,l=e=>s(e)||(e=>"\n"===t[e])(e)||a(e)||u(e),i=e=>s(e)||a(e)||u(e)?"\n":t[e];function f(){return c=0,l(n)&&(r++,o=0),s(n)&&n++,n++,o++,t[n]}return{index:()=>n,line:()=>r,column:()=>o,peekOffset:()=>c,charAt:i,currentChar:()=>i(n),currentPeek:()=>i(n+c),next:f,peek:function(){return s(n+c)&&c++,c++,t[n+c]},reset:function(){n=0,r=1,o=1,c=0},resetPeek:function(e=0){c=e},skipToPeek:function(){const e=n+c;for(;e!==n;)f();c=0}}}const R=void 0;function J(e,t={}){const n=!1!==t.location,r=M(e),o=()=>r.index(),c=()=>{return e=r.line(),t=r.column(),n=r.index(),{line:e,column:t,offset:n};var e,t,n},s=c(),a=o(),u={currentType:14,offset:a,startLoc:s,endLoc:s,lastType:14,lastOffset:a,lastStartLoc:s,lastEndLoc:s,braceNest:0,inLinked:!1,text:""},l=()=>u,{onError:i}=t;function f(e,t,r){e.endLoc=c(),e.currentType=t;const o={type:t};return n&&(o.loc=A(e.startLoc,e.endLoc)),null!=r&&(o.value=r),o}const p=e=>f(e,14);function m(e,t){return e.currentChar()===t?(e.next(),t):(c(),"")}function d(e){let t="";for(;" "===e.currentPeek()||"\n"===e.currentPeek();)t+=e.currentPeek(),e.peek();return t}function h(e){const t=d(e);return e.skipToPeek(),t}function k(e){if(e===R)return!1;const t=e.charCodeAt(0);return t>=97&&t<=122||t>=65&&t<=90||95===t}function g(e,t){const{currentType:n}=t;if(2!==n)return!1;d(e);const r=function(e){if(e===R)return!1;const t=e.charCodeAt(0);return t>=48&&t<=57}("-"===e.currentPeek()?e.peek():e.currentPeek());return e.resetPeek(),r}function b(e){d(e);const t="|"===e.currentPeek();return e.resetPeek(),t}function y(e,t=!0){const n=(t=!1,r="",o=!1)=>{const c=e.currentPeek();return"{"===c?"%"!==r&&t:"@"!==c&&c?"%"===c?(e.peek(),n(t,"%",!0)):"|"===c?!("%"!==r&&!o)||!(" "===r||"\n"===r):" "===c?(e.peek(),n(!0," ",o)):"\n"!==c||(e.peek(),n(!0,"\n",o)):"%"===r||t},r=n();return t&&e.resetPeek(),r}function x(e,t){const n=e.currentChar();return n===R?R:t(n)?(e.next(),n):null}function w(e){return x(e,(e=>{const t=e.charCodeAt(0);return t>=97&&t<=122||t>=65&&t<=90||t>=48&&t<=57||95===t||36===t}))}function C(e){return x(e,(e=>{const t=e.charCodeAt(0);return t>=48&&t<=57}))}function _(e){return x(e,(e=>{const t=e.charCodeAt(0);return t>=48&&t<=57||t>=65&&t<=70||t>=97&&t<=102}))}function v(e){let t="",n="";for(;t=C(e);)n+=t;return n}function L(e){const t=e.currentChar();switch(t){case"\\":case"'":return e.next(),`\\${t}`;case"u":return T(e,t,4);case"U":return T(e,t,6);default:return c(),""}}function T(e,t,n){m(e,t);let r="";for(let t=0;t<n;t++){const t=_(e);if(!t){c(),e.currentChar();break}r+=t}return`\\${t}${r}`}function P(e){h(e);const t=m(e,"|");return h(e),t}function O(e,t){let n=null;switch(e.currentChar()){case"{":return t.braceNest>=1&&c(),e.next(),n=f(t,2,"{"),h(e),t.braceNest++,n;case"}":return t.braceNest>0&&2===t.currentType&&c(),e.next(),n=f(t,3,"}"),t.braceNest--,t.braceNest>0&&h(e),t.inLinked&&0===t.braceNest&&(t.inLinked=!1),n;case"@":return t.braceNest>0&&c(),n=F(e,t)||p(t),t.braceNest=0,n;default:let r=!0,o=!0,s=!0;if(b(e))return t.braceNest>0&&c(),n=f(t,1,P(e)),t.braceNest=0,t.inLinked=!1,n;if(t.braceNest>0&&(5===t.currentType||6===t.currentType||7===t.currentType))return c(),t.braceNest=0,N(e,t);if(r=function(e,t){const{currentType:n}=t;if(2!==n)return!1;d(e);const r=k(e.currentPeek());return e.resetPeek(),r}(e,t))return n=f(t,5,function(e){h(e);let t="",n="";for(;t=w(e);)n+=t;return e.currentChar()===R&&c(),n}(e)),h(e),n;if(o=g(e,t))return n=f(t,6,function(e){h(e);let t="";return"-"===e.currentChar()?(e.next(),t+=`-${v(e)}`):t+=v(e),e.currentChar()===R&&c(),t}(e)),h(e),n;if(s=function(e,t){const{currentType:n}=t;if(2!==n)return!1;d(e);const r="'"===e.currentPeek();return e.resetPeek(),r}(e,t))return n=f(t,7,function(e){h(e),m(e,"'");let t="",n="";const r=e=>"'"!==e&&"\n"!==e;for(;t=x(e,r);)n+="\\"===t?L(e):t;const o=e.currentChar();return"\n"===o||o===R?(c(),"\n"===o&&(e.next(),m(e,"'")),n):(m(e,"'"),n)}(e)),h(e),n;if(!r&&!o&&!s)return n=f(t,13,function(e){h(e);let t="",n="";const r=e=>"{"!==e&&"}"!==e&&" "!==e&&"\n"!==e;for(;t=x(e,r);)n+=t;return n}(e)),c(),h(e),n}return n}function F(e,t){const{currentType:n}=t;let r=null;const o=e.currentChar();switch(8!==n&&9!==n&&12!==n&&10!==n||"\n"!==o&&" "!==o||c(),o){case"@":return e.next(),r=f(t,8,"@"),t.inLinked=!0,r;case".":return h(e),e.next(),f(t,9,".");case":":return h(e),e.next(),f(t,10,":");default:return b(e)?(r=f(t,1,P(e)),t.braceNest=0,t.inLinked=!1,r):function(e,t){const{currentType:n}=t;if(8!==n)return!1;d(e);const r="."===e.currentPeek();return e.resetPeek(),r}(e,t)||function(e,t){const{currentType:n}=t;if(8!==n&&12!==n)return!1;d(e);const r=":"===e.currentPeek();return e.resetPeek(),r}(e,t)?(h(e),F(e,t)):function(e,t){const{currentType:n}=t;if(9!==n)return!1;d(e);const r=k(e.currentPeek());return e.resetPeek(),r}(e,t)?(h(e),f(t,12,function(e){let t="",n="";for(;t=w(e);)n+=t;return n}(e))):function(e,t){const{currentType:n}=t;if(10!==n)return!1;const r=()=>{const t=e.currentPeek();return"{"===t?k(e.peek()):!("@"===t||"%"===t||"|"===t||":"===t||"."===t||" "===t||!t)&&("\n"===t?(e.peek(),r()):k(t))},o=r();return e.resetPeek(),o}(e,t)?(h(e),"{"===o?O(e,t)||r:f(t,11,function(e){const t=(n=!1,r)=>{const o=e.currentChar();return"{"!==o&&"%"!==o&&"@"!==o&&"|"!==o&&o?" "===o?r:"\n"===o?(r+=o,e.next(),t(n,r)):(r+=o,e.next(),t(!0,r)):r};return t(!1,"")}(e))):(8===n&&c(),t.braceNest=0,t.inLinked=!1,N(e,t))}}function N(e,t){let n={type:14};if(t.braceNest>0)return O(e,t)||p(t);if(t.inLinked)return F(e,t)||p(t);const r=e.currentChar();switch(r){case"{":return O(e,t)||p(t);case"}":return c(),e.next(),f(t,3,"}");case"@":return F(e,t)||p(t);default:if(b(e))return n=f(t,1,P(e)),t.braceNest=0,t.inLinked=!1,n;if(y(e))return f(t,0,function(e){const t=n=>{const r=e.currentChar();return"{"!==r&&"}"!==r&&"@"!==r&&r?"%"===r?y(e)?(n+=r,e.next(),t(n)):n:"|"===r?n:" "===r||"\n"===r?y(e)?(n+=r,e.next(),t(n)):b(e)?n:(n+=r,e.next(),t(n)):(n+=r,e.next(),t(n)):n};return t("")}(e));if("%"===r)return e.next(),f(t,4,"%")}return n}return{nextToken:function(){const{currentType:e,offset:t,startLoc:n,endLoc:s}=u;return u.lastType=e,u.lastOffset=t,u.lastStartLoc=n,u.lastEndLoc=s,u.offset=o(),u.startLoc=c(),r.currentChar()===R?f(u,14):N(r,u)},currentOffset:o,currentPosition:c,context:l}}const z=/(?:\\\\|\\'|\\u([0-9a-fA-F]{4})|\\U([0-9a-fA-F]{6}))/g;function D(e,t,n){switch(e){case"\\\\":return"\\";case"\\'":return"'";default:{const e=parseInt(t||n,16);return e<=55295||e>=57344?String.fromCodePoint(e):"�"}}}function H(e={}){const t=!1!==e.location,{onError:n}=e;function r(e,n,r){const o={type:e,start:n,end:n};return t&&(o.loc={start:r,end:r}),o}function o(e,n,r,o){e.end=n,o&&(e.type=o),t&&e.loc&&(e.loc.end=r)}function c(e,t){const n=e.context(),c=r(3,n.offset,n.startLoc);return c.value=t,o(c,e.currentOffset(),e.currentPosition()),c}function a(e,t){const n=e.context(),{lastOffset:c,lastStartLoc:s}=n,a=r(5,c,s);return a.index=parseInt(t,10),e.nextToken(),o(a,e.currentOffset(),e.currentPosition()),a}function u(e,t){const n=e.context(),{lastOffset:c,lastStartLoc:s}=n,a=r(4,c,s);return a.key=t,e.nextToken(),o(a,e.currentOffset(),e.currentPosition()),a}function l(e,t){const n=e.context(),{lastOffset:c,lastStartLoc:s}=n,a=r(9,c,s);return a.value=t.replace(z,D),e.nextToken(),o(a,e.currentOffset(),e.currentPosition()),a}function i(e){const t=e.context(),n=r(6,t.offset,t.startLoc);let c=e.nextToken();if(9===c.type){const t=function(e){const t=e.nextToken(),n=e.context(),{lastOffset:c,lastStartLoc:s}=n,a=r(8,c,s);return 12!==t.type?(a.value="",o(a,c,s),{nextConsumeToken:t,node:a}):(null==t.value&&U(t),a.value=t.value||"",o(a,e.currentOffset(),e.currentPosition()),{node:a})}(e);n.modifier=t.node,c=t.nextConsumeToken||e.nextToken()}switch(10!==c.type&&U(c),c=e.nextToken(),2===c.type&&(c=e.nextToken()),c.type){case 11:null==c.value&&U(c),n.key=function(e,t){const n=e.context(),c=r(7,n.offset,n.startLoc);return c.value=t,o(c,e.currentOffset(),e.currentPosition()),c}(e,c.value||"");break;case 5:null==c.value&&U(c),n.key=u(e,c.value||"");break;case 6:null==c.value&&U(c),n.key=a(e,c.value||"");break;case 7:null==c.value&&U(c),n.key=l(e,c.value||"");break;default:const t=e.context(),s=r(7,t.offset,t.startLoc);return s.value="",o(s,t.offset,t.startLoc),n.key=s,o(n,t.offset,t.startLoc),{nextConsumeToken:c,node:n}}return o(n,e.currentOffset(),e.currentPosition()),{node:n}}function f(e){const t=e.context(),n=r(2,1===t.currentType?e.currentOffset():t.offset,1===t.currentType?t.endLoc:t.startLoc);n.items=[];let s=null;do{const t=s||e.nextToken();switch(s=null,t.type){case 0:null==t.value&&U(t),n.items.push(c(e,t.value||""));break;case 6:null==t.value&&U(t),n.items.push(a(e,t.value||""));break;case 5:null==t.value&&U(t),n.items.push(u(e,t.value||""));break;case 7:null==t.value&&U(t),n.items.push(l(e,t.value||""));break;case 8:const r=i(e);n.items.push(r.node),s=r.nextConsumeToken||null}}while(14!==t.currentType&&1!==t.currentType);return o(n,1===t.currentType?t.lastOffset:e.currentOffset(),1===t.currentType?t.lastEndLoc:e.currentPosition()),n}function p(e){const t=e.context(),{offset:n,startLoc:c}=t,s=f(e);return 14===t.currentType?s:function(e,t,n,c){const s=e.context();let a=0===c.items.length;const u=r(1,t,n);u.cases=[],u.cases.push(c);do{const t=f(e);a||(a=0===t.items.length),u.cases.push(t)}while(14!==s.currentType);return o(u,e.currentOffset(),e.currentPosition()),u}(e,n,c,s)}return{parse:function(n){const c=J(n,s({},e)),a=c.context(),u=r(0,a.offset,a.startLoc);return t&&u.loc&&(u.loc.source=n),u.body=p(c),o(u,c.currentOffset(),c.currentPosition()),u}}}function U(e){if(14===e.type)return"EOF";const t=(e.value||"").replace(/\r?\n/gu,"\\n");return t.length>10?t.slice(0,9)+"…":t}function K(e,t){for(let n=0;n<e.length;n++)q(e[n],t)}function q(e,t){switch(e.type){case 1:K(e.cases,t),t.helper("plural");break;case 2:K(e.items,t);break;case 6:q(e.key,t),t.helper("linked");break;case 5:t.helper("interpolate"),t.helper("list");break;case 4:t.helper("interpolate"),t.helper("named")}}function Z(e,t={}){const n=function(e,t={}){const n={ast:e,helpers:new Set};return{context:()=>n,helper:e=>(n.helpers.add(e),e)}}(e);n.helper("normalize"),e.body&&q(e.body,n);const r=n.context();e.helpers=Array.from(r.helpers)}function B(e,t){const{helper:n}=e;switch(t.type){case 0:!function(e,t){t.body?B(e,t.body):e.push("null")}(e,t);break;case 1:!function(e,t){const{helper:n,needIndent:r}=e;if(t.cases.length>1){e.push(`${n("plural")}([`),e.indent(r());const o=t.cases.length;for(let n=0;n<o&&(B(e,t.cases[n]),n!==o-1);n++)e.push(", ");e.deindent(r()),e.push("])")}}(e,t);break;case 2:!function(e,t){const{helper:n,needIndent:r}=e;e.push(`${n("normalize")}([`),e.indent(r());const o=t.items.length;for(let n=0;n<o&&(B(e,t.items[n]),n!==o-1);n++)e.push(", ");e.deindent(r()),e.push("])")}(e,t);break;case 6:!function(e,t){const{helper:n}=e;e.push(`${n("linked")}(`),B(e,t.key),t.modifier&&(e.push(", "),B(e,t.modifier)),e.push(")")}(e,t);break;case 8:case 7:e.push(JSON.stringify(t.value),t);break;case 5:e.push(`${n("interpolate")}(${n("list")}(${t.index}))`,t);break;case 4:e.push(`${n("interpolate")}(${n("named")}(${JSON.stringify(t.key)}))`,t);break;case 9:case 3:e.push(JSON.stringify(t.value),t)}}function G(e,t={}){const n=s({},t),r=H(n).parse(e);return Z(r,n),((e,t={})=>{const n=p(t.mode)?t.mode:"normal",r=p(t.filename)?t.filename:"message.intl",o=t.needIndent?t.needIndent:"arrow"!==n,c=e.helpers||[],s=function(e,t){const{filename:n,breakLineCode:r,needIndent:o}=t,c={source:e.loc.source,filename:n,code:"",column:1,line:1,offset:0,map:void 0,breakLineCode:r,needIndent:o,indentLevel:0};function s(e,t){c.code+=e}function a(e,t=!0){const n=t?r:"";s(o?n+"  ".repeat(e):n)}return{context:()=>c,push:s,indent:function(e=!0){const t=++c.indentLevel;e&&a(t)},deindent:function(e=!0){const t=--c.indentLevel;e&&a(t)},newline:function(){a(c.indentLevel)},helper:e=>`_${e}`,needIndent:()=>c.needIndent}}(e,{mode:n,filename:r,sourceMap:!!t.sourceMap,breakLineCode:null!=t.breakLineCode?t.breakLineCode:"arrow"===n?";":"\n",needIndent:o});s.push("normal"===n?"function __msg__ (ctx) {":"(ctx) => {"),s.indent(o),c.length>0&&(s.push(`const { ${c.map((e=>`${e}: _${e}`)).join(", ")} } = ctx`),s.newline()),s.push("return "),B(s,e),s.deindent(o),s.push("}");const{code:a,map:u}=s.context();return{ast:e,code:a,map:u?u.toJSON():void 0}})(r,n)}const Q="i18n:init";let V=null;function X(e){V=e}function Y(){return V}function ee(e,t,n){V&&V.emit(Q,{timestamp:Date.now(),i18n:e,version:t,meta:n})}const te=ne("function:translate");function ne(e){return t=>V&&V.emit(e,t)}const re={0:"Not found '{key}' key in '{locale}' locale messages.",1:"Fall back to translate '{key}' key with '{target}' locale.",2:"Cannot format a number value due to not supported Intl.NumberFormat.",3:"Fall back to number format '{key}' key with '{target}' locale.",4:"Cannot format a date value due to not supported Intl.DateTimeFormat.",5:"Fall back to datetime format '{key}' key with '{target}' locale."};function oe(t,...n){return function(t,...n){return 1===n.length&&d(n[0])&&(n=n[0]),n&&n.hasOwnProperty||(n={}),t.replace(e,((e,t)=>n.hasOwnProperty(t)?n[t]:""))}(re[t],...n)}const ce="9.1.7",se=-1,ae="";let ue;function le(e){ue=e}let ie=null;const fe=e=>{ie=e},pe=()=>ie;let me=0;function de(e={}){const t=p(e.version)?e.version:"9.1.7",n=p(e.locale)?e.locale:"en-US",o=i(e.fallbackLocale)||g(e.fallbackLocale)||p(e.fallbackLocale)||!1===e.fallbackLocale?e.fallbackLocale:n,a=g(e.messages)?e.messages:{[n]:{}},u=g(e.datetimeFormats)?e.datetimeFormats:{[n]:{}},l=g(e.numberFormats)?e.numberFormats:{[n]:{}},h=s({},e.modifiers||{},{upper:e=>p(e)?e.toUpperCase():e,lower:e=>p(e)?e.toLowerCase():e,capitalize:e=>p(e)?`${e.charAt(0).toLocaleUpperCase()}${e.substr(1)}`:e}),k=e.pluralRules||{},b=f(e.missing)?e.missing:null,y=!m(e.missingWarn)&&!r(e.missingWarn)||e.missingWarn,x=!m(e.fallbackWarn)&&!r(e.fallbackWarn)||e.fallbackWarn,w=!!e.fallbackFormat,C=!!e.unresolving,_=f(e.postTranslation)?e.postTranslation:null,v=g(e.processor)?e.processor:null,L=!m(e.warnHtmlMessage)||e.warnHtmlMessage,T=!!e.escapeParameter,P=f(e.messageCompiler)?e.messageCompiler:ue,O=f(e.onWarn)?e.onWarn:c,F=e,N=d(F.__datetimeFormatters)?F.__datetimeFormatters:new Map,$=d(F.__numberFormatters)?F.__numberFormatters:new Map,S=d(F.__meta)?F.__meta:{};me++;return{version:t,cid:me,locale:n,fallbackLocale:o,messages:a,datetimeFormats:u,numberFormats:l,modifiers:h,pluralRules:k,missing:b,missingWarn:y,fallbackWarn:x,fallbackFormat:w,unresolving:C,postTranslation:_,processor:v,warnHtmlMessage:L,escapeParameter:T,messageCompiler:P,onWarn:O,__datetimeFormatters:N,__numberFormatters:$,__meta:S}}function he(e,t){return e instanceof RegExp?e.test(t):e}function ke(e,t){return e instanceof RegExp?e.test(t):e}function ge(e,t,n,r,o){const{missing:c}=e;if(null!==c){const r=c(e,n,t,o);return p(r)?r:t}return t}function be(e,t,n){const r=e;r.__localeChainCache||(r.__localeChainCache=new Map);let o=r.__localeChainCache.get(n);if(!o){o=[];let e=[n];for(;i(e);)e=ye(o,e,t);const c=i(t)?t:g(t)?t.default?t.default:null:t;e=p(c)?[c]:c,i(e)&&ye(o,e,!1),r.__localeChainCache.set(n,o)}return o}function ye(e,t,n){let r=!0;for(let o=0;o<t.length&&m(r);o++){p(t[o])&&(r=xe(e,t[o],n))}return r}function xe(e,t,n){let r;const o=t.split("-");do{r=we(e,o.join("-"),n),o.splice(-1,1)}while(o.length&&!0===r);return r}function we(e,t,n){let r=!1;if(!e.includes(t)&&(r=!0,t)){r="!"!==t[t.length-1];const o=t.replace(/!/g,"");e.push(o),(i(n)||g(n))&&n[o]&&(r=n[o])}return r}function Ce(e,t,n){e.__localeChainCache=new Map,be(e,n,t)}const _e=e=>e;let ve=Object.create(null);function Le(){ve=Object.create(null)}function Te(e,t={}){{const n=(t.onCacheKey||_e)(e),r=ve[n];if(r)return r;let o=!1;const c=t.onError||E;t.onError=e=>{o=!0,c(e)};const{code:s}=G(e,t),a=new Function(`return ${s}`)();return o?a:ve[n]=a}}function Pe(e){return I(e,null,void 0)}const Oe=()=>"",Fe=e=>f(e);function Ne(e,...t){const{fallbackFormat:r,postTranslation:o,unresolving:c,fallbackLocale:s,messages:u}=e,[l,h]=Se(...t),k=(m(h.missingWarn),m(h.fallbackWarn),m(h.escapeParameter)?h.escapeParameter:e.escapeParameter),g=!!h.resolvedMessage,b=p(h.default)||m(h.default)?m(h.default)?l:h.default:r?l:"",y=r||""!==b,x=p(h.locale)?h.locale:e.locale;k&&function(e){i(e.list)?e.list=e.list.map((e=>p(e)?a(e):e)):d(e.named)&&Object.keys(e.named).forEach((t=>{p(e.named[t])&&(e.named[t]=a(e.named[t]))}))}(h);let[w,C,_]=g?[l,x,u[x]||{}]:function(e,t,n,r,o,c){const{messages:s}=e,a=be(e,r,n);let u,l={},i=null;const m="translate";for(let n=0;n<a.length&&(u=a[n],l=s[u]||{},null===(i=v(l,t))&&(i=l[t]),!p(i)&&!f(i));n++){const n=ge(e,t,u,0,m);n!==t&&(i=n)}return[i,u,l]}(e,l,x,s),L=l;if(g||p(w)||Fe(w)||y&&(w=b,L=w),!(g||(p(w)||Fe(w))&&p(C)))return c?-1:l;let T=!1;const P=Fe(w)?w:$e(e,l,C,w,L,(()=>{T=!0}));if(T)return w;const O=function(e,t,n){return t(n)}(0,P,S(function(e,t,r,o){const{modifiers:c,pluralRules:s}=e,a={locale:t,modifiers:c,pluralRules:s,messages:n=>{const o=v(r,n);if(p(o)){let r=!1;const c=$e(e,n,t,o,n,(()=>{r=!0}));return r?Oe:c}return Fe(o)?o:Oe}};e.processor&&(a.processor=e.processor);o.list&&(a.list=o.list);o.named&&(a.named=o.named);n(o.plural)&&(a.pluralIndex=o.plural);return a}(e,C,_,h)));return o?o(O):O}function $e(e,n,r,o,c,s){const{messageCompiler:a,warnHtmlMessage:u}=e;if(Fe(o)){const e=o;return e.locale=e.locale||r,e.key=e.key||n,e}const l=a(o,function(e,n,r,o,c,s){return{warnHtmlMessage:c,onError:e=>{throw s&&s(e),e},onCacheKey:e=>((e,n,r)=>t({l:e,k:n,s:r}))(n,r,e)}}(0,r,c,0,u,s));return l.locale=r,l.key=n,l.source=o,l}function Se(...e){const[t,r,c]=e,a={};if(!p(t)&&!n(t)&&!Fe(t))throw Error(14);const u=n(t)?String(t):(Fe(t),t);return n(r)?a.plural=r:p(r)?a.default=r:g(r)&&!o(r)?a.named=r:i(r)&&(a.list=r),n(c)?a.plural=c:p(c)?a.default=c:g(c)&&s(a,c),[u,a]}function Ie(e,...t){const{datetimeFormats:n,unresolving:r,fallbackLocale:c}=e,{__datetimeFormatters:a}=e,[u,l,i,f]=Ee(...t);m(i.missingWarn);m(i.fallbackWarn);const d=!!i.part,h=p(i.locale)?i.locale:e.locale,k=be(e,c,h);if(!p(u)||""===u)return new Intl.DateTimeFormat(h).format(l);let b,y={},x=null;for(let t=0;t<k.length&&(b=k[t],y=n[b]||{},x=y[u],!g(x));t++)ge(e,u,b,0,"datetime format");if(!g(x)||!p(b))return r?-1:u;let w=`${b}__${u}`;o(f)||(w=`${w}__${JSON.stringify(f)}`);let C=a.get(w);return C||(C=new Intl.DateTimeFormat(b,s({},x,f)),a.set(w,C)),d?C.formatToParts(l):C.format(l)}function Ee(...e){const[t,r,o,c]=e;let s,a={},u={};if(p(t)){if(!/\d{4}-\d{2}-\d{2}(T.*)?/.test(t))throw Error(16);s=new Date(t);try{s.toISOString()}catch(e){throw Error(16)}}else if("[object Date]"===k(t)){if(isNaN(t.getTime()))throw Error(15);s=t}else{if(!n(t))throw Error(14);s=t}return p(r)?a.key=r:g(r)&&(a=r),p(o)?a.locale=o:g(o)&&(u=o),g(c)&&(u=c),[a.key||"",s,a,u]}function Ae(e,t,n){const r=e;for(const e in n){const n=`${t}__${e}`;r.__datetimeFormatters.has(n)&&r.__datetimeFormatters.delete(n)}}function We(e,...t){const{numberFormats:n,unresolving:r,fallbackLocale:c}=e,{__numberFormatters:a}=e,[u,l,i,f]=je(...t);m(i.missingWarn);m(i.fallbackWarn);const d=!!i.part,h=p(i.locale)?i.locale:e.locale,k=be(e,c,h);if(!p(u)||""===u)return new Intl.NumberFormat(h).format(l);let b,y={},x=null;for(let t=0;t<k.length&&(b=k[t],y=n[b]||{},x=y[u],!g(x));t++)ge(e,u,b,0,"number format");if(!g(x)||!p(b))return r?-1:u;let w=`${b}__${u}`;o(f)||(w=`${w}__${JSON.stringify(f)}`);let C=a.get(w);return C||(C=new Intl.NumberFormat(b,s({},x,f)),a.set(w,C)),d?C.formatToParts(l):C.format(l)}function je(...e){const[t,r,o,c]=e;let s={},a={};if(!n(t))throw Error(14);const u=t;return p(r)?s.key=r:g(r)&&(s=r),p(o)?s.locale=o:g(o)&&(a=o),g(c)&&(a=c),[s.key||"",u,s,a]}function Me(e,t,n){const r=e;for(const e in n){const n=`${t}__${e}`;r.__numberFormatters.has(n)&&r.__numberFormatters.delete(n)}}export{O as DEFAULT_MESSAGE_DATA_TYPE,ae as MISSING_RESOLVE_VALUE,se as NOT_REOSLVED,ce as VERSION,Le as clearCompileCache,Ae as clearDateTimeFormat,Me as clearNumberFormat,Te as compileToFunction,I as createCompileError,de as createCoreContext,Pe as createCoreError,S as createMessageContext,Ie as datetime,pe as getAdditionalMeta,Y as getDevToolsHook,be as getLocaleChain,oe as getWarnMessage,L as handleFlatJson,ge as handleMissing,ee as initI18nDevTools,Fe as isMessageFunction,he as isTranslateFallbackWarn,ke as isTranslateMissingWarn,We as number,C as parse,Ee as parseDateTimeArgs,je as parseNumberArgs,Se as parseTranslateArgs,le as registerMessageCompiler,v as resolveValue,fe as setAdditionalMeta,X as setDevToolsHook,Ne as translate,te as translateDevTools,Ce as updateFallbackLocale};
