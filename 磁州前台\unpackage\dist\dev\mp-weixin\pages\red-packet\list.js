"use strict";
const common_vendor = require("../../common/vendor.js");
const utils_format = require("../../utils/format.js");
const common_assets = require("../../common/assets.js");
const _sfc_main = {
  data() {
    return {
      activeTab: "all",
      redPackets: [],
      loading: true,
      isRefreshing: false,
      loadMoreStatus: "more",
      // more, loading, noMore
      page: 1,
      pageSize: 10,
      totalAmount: "0.00",
      myAmount: "0.00",
      todayCount: 0,
      loadingText: {
        contentdown: "上拉加载更多",
        contentrefresh: "加载中...",
        contentnomore: "没有更多了"
      },
      loadMoreText: {
        contentdown: "上拉加载更多",
        contentrefresh: "加载中...",
        contentnomore: "没有更多了"
      }
    };
  },
  onLoad(options) {
    if (options && options.tab) {
      this.activeTab = options.tab;
    }
    this.loadRedPackets();
    this.loadStats();
  },
  methods: {
    goBack() {
      common_vendor.index.navigateBack();
    },
    showFilter() {
      common_vendor.index.showToast({
        title: "筛选功能开发中",
        icon: "none"
      });
    },
    switchTab(tab) {
      if (this.activeTab === tab)
        return;
      this.activeTab = tab;
      this.redPackets = [];
      this.page = 1;
      this.loadRedPackets();
    },
    async loadStats() {
      try {
        this.totalAmount = "8,888.88";
        this.myAmount = "128.88";
        this.todayCount = 56;
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/red-packet/list.vue:257", "加载统计数据失败", error);
      }
    },
    async loadRedPackets(isRefresh = false) {
      if (isRefresh) {
        this.page = 1;
      }
      if (this.page === 1) {
        this.loading = true;
      } else {
        this.loadMoreStatus = "loading";
      }
      try {
        await new Promise((resolve) => setTimeout(resolve, 1e3));
        const mockData = this.getMockData();
        if (this.page === 1) {
          this.redPackets = mockData;
        } else {
          this.redPackets = [...this.redPackets, ...mockData];
        }
        if (this.page >= 3) {
          this.loadMoreStatus = "noMore";
        } else {
          this.loadMoreStatus = "more";
          this.page++;
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/red-packet/list.vue:293", "加载红包列表失败", error);
        common_vendor.index.showToast({
          title: "加载失败，请重试",
          icon: "none"
        });
      } finally {
        this.loading = false;
        if (isRefresh) {
          this.isRefreshing = false;
        }
      }
    },
    // 获取模拟数据
    getMockData() {
      const types = ["normal", "lucky", "fixed"];
      const statuses = [0, 0, 0, 0, 1, 2];
      let mockData = [];
      if (this.activeTab === "info") {
        mockData = Array.from({ length: 10 }, (_, i) => {
          const infoType = this.getRandomInfoType();
          const title = this.getRandomInfoTitle();
          const description = this.getRandomInfoDescription();
          return {
            id: `info_rp_${this.page}_${i}`,
            merchantId: `user_${i}`,
            merchantName: `${this.getRandomUserName()}`,
            merchantAvatar: `/static/images/avatar_${i % 5 + 1}.png`,
            title,
            description,
            images: this.getRandomImages(i),
            amount: (Math.random() * 50 + 5).toFixed(2),
            packetType: types[Math.floor(Math.random() * types.length)],
            status: statuses[Math.floor(Math.random() * statuses.length)],
            publishTime: new Date(Date.now() - Math.random() * 864e5 * 7).getTime(),
            distance: Math.random() * 5e3,
            viewCount: Math.floor(Math.random() * 1e3),
            commentCount: Math.floor(Math.random() * 50),
            likeCount: Math.floor(Math.random() * 100),
            hasGrabbed: Math.random() > 0.7,
            infoType,
            // 信息类型
            isInfoRedPacket: true,
            // 标记为信息红包
            // 添加与首页信息一致的字段
            category: infoType,
            content: title,
            time: utils_format.formatTime(new Date(Date.now() - Math.random() * 864e5 * 7).getTime(), "YYYY-MM-DD HH:mm"),
            views: Math.floor(Math.random() * 1e3),
            hasRedPacket: true,
            redPacketAmount: (Math.random() * 50 + 5).toFixed(2),
            redPacketType: types[Math.floor(Math.random() * types.length)],
            redPacketCount: Math.floor(Math.random() * 50) + 10,
            redPacketRemain: Math.floor(Math.random() * 10) + 1
          };
        });
      } else {
        mockData = Array.from({ length: 10 }, (_, i) => ({
          id: `rp_${this.page}_${i}`,
          merchantId: `m_${i}`,
          merchantName: `${this.getRandomBusinessName()}`,
          merchantAvatar: `/static/images/avatar_${i % 5 + 1}.png`,
          title: this.getRandomTitle(),
          description: this.getRandomDescription(),
          images: this.getRandomImages(i),
          amount: (Math.random() * 100 + 5).toFixed(2),
          packetType: types[Math.floor(Math.random() * types.length)],
          status: statuses[Math.floor(Math.random() * statuses.length)],
          publishTime: new Date(Date.now() - Math.random() * 864e5 * 7).getTime(),
          distance: Math.random() * 5e3,
          viewCount: Math.floor(Math.random() * 1e3),
          commentCount: Math.floor(Math.random() * 50),
          likeCount: Math.floor(Math.random() * 100),
          hasGrabbed: Math.random() > 0.7,
          isInfoRedPacket: false
          // 标记为非信息红包
        }));
      }
      return mockData;
    },
    getRandomBusinessName() {
      const names = [
        "品味咖啡馆",
        "悦享美食城",
        "鲜果工坊",
        "时尚服饰店",
        "健康生活馆",
        "数码科技店",
        "宠物乐园",
        "美丽花坊",
        "家居生活馆",
        "创意礼品店"
      ];
      return names[Math.floor(Math.random() * names.length)];
    },
    getRandomTitle() {
      const titles = [
        "开业大酬宾，红包雨来袭！",
        "周年庆典，感恩回馈！",
        "新品上市，抢先体验！",
        "限时特惠，错过等一年！",
        "会员专享，双倍红包！",
        "春节特辑，财神送礼！",
        "夏日清凉，冰爽红包！",
        "金秋送爽，丰收好礼！",
        "冬季温暖，暖心红包！",
        "五一劳动节，犒劳自己！"
      ];
      return titles[Math.floor(Math.random() * titles.length)];
    },
    getRandomDescription() {
      const descriptions = [
        "店庆活动期间，凡是到店消费满100元即可参与抽红包活动，最高可得88元现金红包！",
        "为回馈新老顾客，本店特推出线上抢红包活动，抢到红包可直接抵扣消费金额！",
        "关注我们的公众号，参与互动即可获得神秘红包，数量有限，先到先得！",
        "新店开业，发放100个幸运红包，金额随机，最高可得200元！",
        "五星好评送红包，晒图送红包，分享朋友圈再送红包，红包不停！",
        "年终促销，下单立减，还有机会获得平台补贴的超级红包！"
      ];
      return descriptions[Math.floor(Math.random() * descriptions.length)];
    },
    getRandomImages(seed) {
      const count = Math.floor(Math.random() * 5) + 1;
      return Array.from(
        { length: count },
        (_, i) => `/static/images/sample_${(seed + i) % 8 + 1}.jpg`
      );
    },
    formatTime(timestamp) {
      return utils_format.formatTime(timestamp);
    },
    formatDistance(meters) {
      return utils_format.formatDistance(meters);
    },
    getPacketTypeText(type) {
      const typeMap = {
        "normal": "普通红包",
        "lucky": "拼手气红包",
        "fixed": "固定金额"
      };
      return typeMap[type] || "红包";
    },
    getStatusText(status) {
      const statusMap = {
        0: "进行中",
        1: "已领完",
        2: "已过期"
      };
      return statusMap[status] || "未知状态";
    },
    getStatusClass(status) {
      const classMap = {
        0: "status-active",
        1: "status-finished",
        2: "status-expired"
      };
      return classMap[status] || "";
    },
    navigateToDetail(item) {
      if (item.isInfoRedPacket) {
        try {
          let params = {
            id: encodeURIComponent(item.id),
            category: encodeURIComponent(item.infoType),
            content: encodeURIComponent(item.description || "")
          };
          const detailPageMap = {
            "到家服务": "home-service-detail",
            "寻找服务": "find-service-detail",
            "生意转让": "business-transfer-detail",
            "商业转让": "business-transfer-detail",
            "招聘信息": "job-detail",
            "求职信息": "job-seeking-detail",
            "房屋出租": "house-rent-detail",
            "房屋出售": "house-sale-detail",
            "二手车辆": "car-detail",
            "宠物信息": "pet-detail",
            "车辆服务": "vehicle-service-detail",
            "二手闲置": "second-hand-detail",
            "磁州拼车": "carpool-detail",
            "教育培训": "education-detail",
            "其他服务": "info-detail"
          };
          const detailPage = detailPageMap[item.infoType] || "info-detail";
          const url = `/pages/publish/${detailPage}?${Object.entries(params).map(([key, value]) => `${key}=${value}`).join("&")}`;
          common_vendor.index.navigateTo({
            url,
            success: () => {
              common_vendor.index.__f__("log", "at pages/red-packet/list.vue:496", "成功跳转到信息详情页:", url);
            },
            fail: (e) => {
              common_vendor.index.__f__("error", "at pages/red-packet/list.vue:499", "跳转信息详情页失败", e);
              common_vendor.index.showToast({
                title: "跳转失败，请重试",
                icon: "none"
              });
            }
          });
        } catch (error) {
          common_vendor.index.__f__("error", "at pages/red-packet/list.vue:507", "导航异常", error);
          common_vendor.index.showToast({
            title: "页面跳转出错",
            icon: "none"
          });
        }
      } else {
        common_vendor.index.navigateTo({
          url: `/pages/red-packet/detail?id=${item.id}`
        });
      }
    },
    navigateToPublish() {
      common_vendor.index.navigateTo({
        url: "/pages/publish/publish?type=redpacket"
      });
    },
    async grabRedPacket(item, index) {
      if (item.status !== 0 || item.hasGrabbed)
        return;
      try {
        await new Promise((resolve) => setTimeout(resolve, 800));
        const success = Math.random() > 0.3;
        if (success) {
          const amount = (Math.random() * 10 + 0.5).toFixed(2);
          common_vendor.index.showModal({
            title: "恭喜您",
            content: `抢到了${amount}元红包`,
            showCancel: false,
            success: () => {
              this.redPackets[index].hasGrabbed = true;
              this.myAmount = (parseFloat(this.myAmount) + parseFloat(amount)).toFixed(2);
            }
          });
        } else {
          common_vendor.index.showToast({
            title: "手慢了，红包被抢光了",
            icon: "none"
          });
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/red-packet/list.vue:559", "抢红包失败", error);
        common_vendor.index.showToast({
          title: "网络异常，请重试",
          icon: "none"
        });
      }
    },
    previewImage(images, current) {
      common_vendor.index.previewImage({
        urls: images,
        current: images[current]
      });
    },
    loadMore() {
      if (this.loadMoreStatus !== "more")
        return;
      this.loadRedPackets();
    },
    onRefresh() {
      this.isRefreshing = true;
      this.loadRedPackets(true);
    },
    getRandomUserName() {
      const names = [
        "张先生",
        "李女士",
        "王师傅",
        "赵经理",
        "刘老师",
        "陈总",
        "杨女士",
        "周先生",
        "吴小姐",
        "郑经理"
      ];
      return names[Math.floor(Math.random() * names.length)];
    },
    getRandomInfoTitle() {
      const titles = [
        "急招送餐员，日结工资+红包奖励",
        "招聘销售人员，底薪3500+提成+红包",
        "高薪诚聘厨师，包吃住，红包福利",
        "招聘保洁人员，待遇优厚，红包补贴",
        "诚聘前台接待，形象气质佳，红包奖励",
        "招聘美容师，有经验者优先，红包补助",
        "招聘仓库管理员，有红包福利",
        "诚聘电工，持证上岗，红包奖励",
        "招聘会计，经验丰富，红包补贴",
        "招聘司机，C1以上，红包奖励"
      ];
      return titles[Math.floor(Math.random() * titles.length)];
    },
    getRandomInfoDescription() {
      const descriptions = [
        "工作地点：市中心，工作时间：9:00-18:00，周末双休，节假日三倍工资，面试通过即可领取入职红包！",
        "有责任心，能吃苦耐劳，有相关工作经验者优先，完成业绩有额外红包奖励！",
        "要求有相关工作经验，能够独立完成工作任务，有团队合作精神，推荐成功有红包奖励！",
        "工作轻松，环境优雅，五险一金，带薪年假，节日福利，红包不断！",
        "要求：形象气质佳，沟通能力强，有相关工作经验，入职即发红包！",
        "薪资待遇：3500-6000元/月，提供住宿，有红包奖励计划！",
        "招聘岗位多个，薪资面议，有意者带上简历前来面试，红包等你拿！",
        "有相关工作经验者优先，能够适应倒班，有责任心，推荐成功有红包！",
        "要求会基本办公软件操作，有相关工作经验，红包福利多多！",
        "工作稳定，待遇优厚，有晋升空间，入职就有红包奖励！"
      ];
      return descriptions[Math.floor(Math.random() * descriptions.length)];
    },
    getRandomInfoType() {
      const types = [
        "招聘信息",
        "求职信息",
        "房屋出租",
        "二手闲置",
        "生意转让",
        "车辆服务",
        "到家服务",
        "教育培训"
      ];
      return types[Math.floor(Math.random() * types.length)];
    }
  }
};
if (!Array) {
  const _component_uni_load_more = common_vendor.resolveComponent("uni-load-more");
  _component_uni_load_more();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.o((...args) => $options.goBack && $options.goBack(...args)),
    b: common_vendor.o((...args) => $options.showFilter && $options.showFilter(...args)),
    c: common_vendor.t($data.totalAmount),
    d: common_vendor.t($data.myAmount),
    e: common_vendor.t($data.todayCount),
    f: $data.activeTab === "all" ? 1 : "",
    g: common_vendor.o(($event) => $options.switchTab("all")),
    h: $data.activeTab === "merchant" ? 1 : "",
    i: common_vendor.o(($event) => $options.switchTab("merchant")),
    j: $data.activeTab === "platform" ? 1 : "",
    k: common_vendor.o(($event) => $options.switchTab("platform")),
    l: $data.activeTab === "info" ? 1 : "",
    m: common_vendor.o(($event) => $options.switchTab("info")),
    n: $data.activeTab === "nearby" ? 1 : "",
    o: common_vendor.o(($event) => $options.switchTab("nearby")),
    p: $data.loading && !$data.redPackets.length
  }, $data.loading && !$data.redPackets.length ? {
    q: common_vendor.p({
      status: "loading",
      contentText: $data.loadingText
    })
  } : !$data.redPackets.length ? {
    s: common_assets._imports_2$11
  } : {
    t: common_vendor.f($data.redPackets, (item, index, i0) => {
      return common_vendor.e({
        a: item.merchantAvatar,
        b: common_vendor.t(item.merchantName),
        c: common_vendor.t(item.isInfoRedPacket ? item.time : $options.formatTime(item.publishTime)),
        d: item.isInfoRedPacket
      }, item.isInfoRedPacket ? {
        e: common_vendor.t(item.category)
      } : {}, {
        f: item.isInfoRedPacket
      }, item.isInfoRedPacket ? {
        g: common_vendor.t(item.content),
        h: common_assets._imports_0$4,
        i: common_vendor.t(item.redPacketAmount),
        j: common_vendor.t(item.redPacketRemain)
      } : common_vendor.e({
        k: common_vendor.t(item.title),
        l: common_vendor.t(item.description),
        m: item.images && item.images.length
      }, item.images && item.images.length ? common_vendor.e({
        n: common_vendor.f(item.images.slice(0, 3), (img, imgIndex, i1) => {
          return {
            a: imgIndex,
            b: img,
            c: common_vendor.o(($event) => $options.previewImage(item.images, imgIndex), imgIndex)
          };
        }),
        o: item.images.length > 3
      }, item.images.length > 3 ? {
        p: common_vendor.t(item.images.length - 3)
      } : {}) : {}, {
        q: common_assets._imports_0$11,
        r: common_vendor.t(item.amount),
        s: common_vendor.t($options.getPacketTypeText(item.packetType)),
        t: common_vendor.t($options.getStatusText(item.status)),
        v: common_vendor.n($options.getStatusClass(item.status))
      }), {
        w: item.isInfoRedPacket ? 1 : "",
        x: !item.isInfoRedPacket
      }, !item.isInfoRedPacket ? {
        y: common_vendor.t(item.viewCount),
        z: common_vendor.t(item.commentCount),
        A: common_vendor.t(item.likeCount),
        B: common_vendor.t(item.hasGrabbed ? "已领取" : item.status === 0 ? "抢红包" : "已结束"),
        C: item.status !== 0 || item.hasGrabbed ? 1 : "",
        D: common_vendor.o(($event) => $options.grabRedPacket(item, index), item.id)
      } : {}, {
        E: item.isInfoRedPacket ? 1 : "",
        F: item.id,
        G: common_vendor.o(($event) => $options.navigateToDetail(item), item.id)
      });
    }),
    v: common_vendor.p({
      status: $data.loadMoreStatus,
      contentText: $data.loadMoreText
    })
  }, {
    r: !$data.redPackets.length,
    w: common_vendor.o((...args) => $options.loadMore && $options.loadMore(...args)),
    x: $data.isRefreshing,
    y: common_vendor.o((...args) => $options.onRefresh && $options.onRefresh(...args)),
    z: common_vendor.o((...args) => $options.navigateToPublish && $options.navigateToPublish(...args))
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/red-packet/list.js.map
