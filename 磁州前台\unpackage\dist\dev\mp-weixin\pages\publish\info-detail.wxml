<view class="post-detail-container"><button id="shareButton" class="hidden-share-btn" open-type="share"></button><view class="post-detail-wrapper"><view class="content-card main-info"><view class="post-header"><view class="post-title">{{a}}</view><view class="post-meta"><text class="post-time">发布于 {{b}}</text><text class="post-category">{{c}}</text><view class="post-views"><text class="iconfont icon-eye"></text><text>{{d}}</text></view></view></view><view wx:if="{{e}}" class="post-gallery"><swiper wx:if="{{f}}" class="swiper" indicator-dots autoplay="{{true}}" interval="{{4000}}" duration="{{500}}" circular><swiper-item wx:for="{{g}}" wx:for-item="image" wx:key="b" bindtap="{{image.c}}"><image src="{{image.a}}" class="carousel-image" mode="aspectFill"></image></swiper-item></swiper><view wx:else class="single-image"><image src="{{h}}" bindtap="{{i}}" mode="widthFix"></image></view></view><view class="divider"></view><view class="post-content"><rich-text nodes="{{j}}"></rich-text></view><view wx:if="{{k}}" class="post-tags"><uni-tag wx:for="{{l}}" wx:for-item="tag" wx:key="a" class="tag" u-i="{{tag.b}}" bind:__l="__l" u-p="{{tag.c}}"></uni-tag></view></view><view class="content-card publisher-info"><view class="publisher-header"><text class="card-title">发布者信息</text></view><view class="publisher-content"><view class="publisher-avatar"><image class="avatar-image" src="{{m}}" mode="aspectFill"></image></view><view class="publisher-details"><view class="publisher-name">{{n}}</view><view class="publisher-stats"><view class="stat-item"><view class="stat-value">{{o}}</view><view class="stat-label">发布</view></view><view class="stat-item"><view class="stat-value">{{p}}</view><view class="stat-label">粉丝</view></view><view class="stat-item"><view class="stat-value">{{q}}</view><view class="stat-label">评分</view></view></view></view><button class="contact-btn" type="primary" size="mini" bindtap="{{r}}">联系TA</button></view></view><view wx:if="{{s}}" class="content-card location-info"><view class="location-header"><text class="card-title">位置信息</text></view><view class="location-content"><text class="iconfont icon-location"></text><text>{{t}}</text></view><view class="location-map"><image src="https://via.placeholder.com/600x200?text=Map+Preview" mode="widthFix" class="map-preview"></image></view></view><view class="content-card comment-section"><view class="comment-header"><text class="card-title">评论区</text></view><view class="comment-list"><view wx:if="{{v}}" class="comment-empty"><text>暂无评论，快来发表第一条评论吧！</text></view><view wx:for="{{w}}" wx:for-item="comment" wx:key="e" class="comment-item"><view class="comment-user"><image class="comment-avatar" src="{{comment.a}}" mode="aspectFill"></image><view class="comment-user-info"><text class="comment-username">{{comment.b}}</text><text class="comment-time">{{comment.c}}</text></view></view><view class="comment-content">{{comment.d}}</view></view></view><view class="comment-input-area"><input class="comment-input" type="text" placeholder="写下你的评论..." confirm-type="send" bindconfirm="{{x}}" value="{{y}}" bindinput="{{z}}"/><button class="comment-submit" type="primary" size="mini" bindtap="{{A}}">发送</button></view></view><view class="content-card related-posts-card"><view class="section-title">相关信息推荐</view><view class="related-posts-content"><view class="related-posts-list"><view wx:for="{{B}}" wx:for-item="relatedPost" wx:key="g" class="related-post-item" bindtap="{{relatedPost.h}}"><view class="post-item-content"><view wx:if="{{relatedPost.a}}" class="post-item-left"><image class="post-image" src="{{relatedPost.b}}" mode="aspectFill"></image></view><view class="post-item-middle"><text class="post-item-title">{{relatedPost.c}}</text><view class="post-item-category">{{relatedPost.d}}</view><view class="post-item-meta"><text class="post-item-time">{{relatedPost.e}}</text><text class="post-item-views">{{relatedPost.f}}浏览</text></view></view></view></view><view wx:if="{{C}}" class="empty-related-posts"><image src="{{D}}" class="empty-image" mode="aspectFit"></image><text class="empty-text">暂无相关信息</text></view></view><view wx:if="{{E}}" class="view-more-btn" catchtap="{{F}}"><text class="view-more-text">查看更多信息</text><text class="view-more-icon iconfont icon-right"></text></view></view></view><red-packet-entry wx:if="{{H}}" u-i="19056fca-1" bind:__l="__l" bindupdateModelValue="{{G}}" u-p="{{H}}"/><view wx:if="{{I}}" class="content-card red-packet-card"><view class="section-title">红包福利</view><view class="red-packet-details"><view class="red-packet-amount"><text class="amount-label">红包金额</text><text class="amount-value">{{J}}元</text></view><view class="red-packet-usage"><text class="usage-label">红包个数</text><text class="usage-value">共{{K}}个 剩余{{L}}个</text></view><view class="red-packet-method"><text class="method-label">红包类型</text><text class="method-value">{{M}}</text></view></view><view class="red-packet-receive" bindtap="{{N}}"> 立即领取 </view></view></view><view class="interaction-toolbar"><view class="toolbar-item" bindtap="{{P}}"><image src="{{O}}" class="toolbar-icon"></image><text class="toolbar-text">首页</text></view><view class="toolbar-item" bindtap="{{R}}"><image src="{{Q}}" class="toolbar-icon"></image><text class="toolbar-text">收藏</text></view><button class="share-button toolbar-item" open-type="share"><image src="{{S}}" class="toolbar-icon"></image><text class="toolbar-text">分享</text></button><view class="toolbar-item" bindtap="{{U}}"><image src="{{T}}" class="toolbar-icon"></image><text class="toolbar-text">私信</text></view><view class="toolbar-item call-button" bindtap="{{V}}"><view class="call-button-content"><text class="call-text">打电话</text><text class="call-subtitle">请说在磁州生活网看到的</text></view></view></view><view wx:if="{{W}}" class="share-guide-layer" bindtap="{{Z}}"><view class="guide-content" catchtap="{{Y}}"><view class="guide-close" bindtap="{{X}}">×</view><view class="guide-title">一键分享</view><view class="guide-desc">点击下方按钮，即可分享给朋友或群聊</view><view class="guide-tips">分享可获得信息置顶特权</view><button class="guide-share-btn" open-type="share"> 立即分享 </button></view></view><view class="custom-navbar"><text class="navbar-title">信息详情</text></view></view>