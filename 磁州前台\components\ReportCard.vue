<template>
  <view class="report-card" @click="navigateToReport">
    <view class="report-icon">
      <image src="/static/images/tabbar/举报.png" mode="aspectFit"></image>
    </view>
    <view class="report-content">
      <view class="report-title">如遇无效、虚假信息，请立即举报</view>
      <view class="report-subtitle">平台将快速核实处理</view>
    </view>
    <view class="report-arrow">
      <image src="/static/images/tabbar/箭头.png" class="arrow-image" mode="aspectFit"></image>
    </view>
  </view>
</template>

<script>
export default {
  name: 'ReportCard',
  props: {
    contentId: {
      type: String,
      default: ''
    },
    contentType: {
      type: String,
      default: 'info' // 默认为普通信息类型
    }
  },
  methods: {
    navigateToReport() {
      // 获取当前页面路径和参数
      const pages = getCurrentPages();
      const currentPage = pages[pages.length - 1];
      const options = currentPage.options || {};
      
      // 优先使用props传入的contentId，如果没有则尝试从当前页面获取id
      const id = this.contentId || options.id || '';
      
      // 跳转到举报页面，并传递内容ID和类型
      uni.navigateTo({
        url: `/pages/common/report?id=${id}&type=${this.contentType}`
      });
    }
  }
}
</script>

<style lang="scss" scoped>
.report-card {
  display: flex;
  align-items: center;
  background-color: #fff;
  padding: 24rpx 30rpx;
  margin: 20rpx;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
}

.report-icon {
  width: 60rpx;
  height: 60rpx;
  margin-right: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  
  image {
    width: 40rpx;
    height: 40rpx;
  }
}

.report-content {
  flex: 1;
}

.report-title {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 4rpx;
  font-weight: 500;
}

.report-subtitle {
  font-size: 24rpx;
  color: #999;
}

.report-arrow {
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.arrow-image {
  width: 24rpx;
  height: 24rpx;
  opacity: 0.5;
}
</style> 