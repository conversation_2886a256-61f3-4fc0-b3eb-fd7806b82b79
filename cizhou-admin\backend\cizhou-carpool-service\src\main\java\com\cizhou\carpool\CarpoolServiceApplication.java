package com.cizhou.carpool;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;

/**
 * 拼车服务启动类
 *
 * <AUTHOR> Team
 * @since 1.0.0
 */
@SpringBootApplication(scanBasePackages = {"com.cizhou.carpool", "com.cizhou.common"})
@EnableDiscoveryClient
@MapperScan("com.cizhou.carpool.mapper")
public class CarpoolServiceApplication {

    public static void main(String[] args) {
        SpringApplication.run(CarpoolServiceApplication.class, args);
        System.out.println("🚗 磁州生活网拼车服务启动成功!");
    }
}
