<template>
  <view class="operation-info-container">
    <!-- 顶部导航栏 -->
    <view class="header-area">
      <!-- 顶部安全区域 -->
      <view class="safe-area-top"></view>
      
      <!-- 自定义导航栏 -->
      <view class="custom-navbar">
        <view class="navbar-left" @click="goBack">
          <image src="/static/images/tabbar/最新返回键.png" class="back-icon" style="filter: brightness(0) invert(1);"></image>
        </view>
        <view class="navbar-title">经营信息</view>
        <view class="navbar-right">
          <!-- 占位元素保持导航栏平衡 -->
        </view>
      </view>
    </view>
    
    <!-- 背景装饰 -->
    <view class="bg-decoration bg-circle-1"></view>
    <view class="bg-decoration bg-circle-2"></view>
    <view class="bg-decoration bg-circle-3"></view>
    
    <!-- 内容区域 -->
    <scroll-view class="content-scroll" scroll-y>
      <form @submit="saveOperationInfo">
        <!-- 经营时间设置 -->
        <view class="form-section">
          <view class="section-header">
            <text class="section-title">营业时间</text>
          </view>
          
          <view class="business-time-settings">
            <view class="time-period" v-for="(period, index) in formData.businessHours" :key="index">
              <view class="time-period-header">
                <view class="day-selection">
                  <checkbox-group @change="(e) => onDayChange(e, index)">
                    <label class="day-checkbox" v-for="(day, dayIndex) in days" :key="dayIndex">
                      <checkbox :checked="period.days.includes(day.value)" :value="day.value" color="#0A84FF" />
                      <text class="day-label">{{day.label}}</text>
                    </label>
                  </checkbox-group>
                </view>
                <view class="period-actions">
                  <text class="delete-period" @click="removePeriod(index)" v-if="formData.businessHours.length > 1">删除</text>
                </view>
              </view>
              
              <view class="time-selection">
                <view class="time-picker">
                  <text class="time-label">开始时间</text>
                  <picker mode="time" :value="period.startTime" @change="(e) => onTimeChange(e, index, 'start')">
                    <view class="picker-content">
                      <text class="time-value">{{period.startTime}}</text>
                      <text class="picker-arrow">›</text>
                    </view>
                  </picker>
                </view>
                <view class="time-seperator">至</view>
                <view class="time-picker">
                  <text class="time-label">结束时间</text>
                  <picker mode="time" :value="period.endTime" @change="(e) => onTimeChange(e, index, 'end')">
                    <view class="picker-content">
                      <text class="time-value">{{period.endTime}}</text>
                      <text class="picker-arrow">›</text>
                    </view>
                  </picker>
                </view>
              </view>
            </view>
            
            <view class="add-time-period" @click="addTimePeriod">
              <text class="add-icon">+</text>
              <text class="add-text">添加时间段</text>
            </view>
            
            <view class="operation-status">
              <text class="status-label">营业状态</text>
              <switch :checked="formData.isOpen" @change="onStatusChange" color="#0A84FF" />
            </view>
          </view>
        </view>
        
        <!-- 配送范围设置 -->
        <view class="form-section">
          <view class="section-header">
            <text class="section-title">配送服务</text>
          </view>
          
          <view class="delivery-settings">
            <view class="switch-item">
              <text class="switch-label">提供外卖配送</text>
              <switch :checked="formData.deliveryService.enabled" @change="onDeliveryChange" color="#0A84FF" />
            </view>
            
            <view class="delivery-options" v-if="formData.deliveryService.enabled">
              <view class="form-item">
                <text class="form-label">起送金额</text>
                <view class="price-input-wrapper">
                  <text class="currency-symbol">¥</text>
                  <input type="digit" v-model="formData.deliveryService.minOrderAmount" class="price-input" placeholder="0" maxlength="6" />
                </view>
              </view>
              
              <view class="form-item">
                <text class="form-label">配送费用</text>
                <view class="price-input-wrapper">
                  <text class="currency-symbol">¥</text>
                  <input type="digit" v-model="formData.deliveryService.deliveryFee" class="price-input" placeholder="0" maxlength="5" />
                </view>
              </view>
              
              <view class="form-item">
                <text class="form-label">配送范围（公里）</text>
                <slider :value="formData.deliveryService.deliveryRange" @change="onRangeChange" min="1" max="20" show-value activeColor="#0A84FF" block-color="#0A84FF" />
              </view>
              
              <view class="form-item">
                <text class="form-label">预计送达时间</text>
                <view class="time-range-picker">
                  <input type="number" v-model="formData.deliveryService.minDeliveryTime" class="time-input" maxlength="3" />
                  <text class="time-separator">-</text>
                  <input type="number" v-model="formData.deliveryService.maxDeliveryTime" class="time-input" maxlength="3" />
                  <text class="time-unit">分钟</text>
                </view>
              </view>
            </view>
          </view>
        </view>
        
        <!-- 自提服务设置 -->
        <view class="form-section">
          <view class="section-header">
            <text class="section-title">自提服务</text>
          </view>
          
          <view class="pickup-settings">
            <view class="switch-item">
              <text class="switch-label">提供自提服务</text>
              <switch :checked="formData.selfPickup.enabled" @change="onPickupChange" color="#0A84FF" />
            </view>
            
            <view class="pickup-options" v-if="formData.selfPickup.enabled">
              <view class="form-item">
                <text class="form-label">自提时间</text>
                <view class="time-range-picker">
                  <input type="number" v-model="formData.selfPickup.minPickupTime" class="time-input" maxlength="3" />
                  <text class="time-separator">-</text>
                  <input type="number" v-model="formData.selfPickup.maxPickupTime" class="time-input" maxlength="3" />
                  <text class="time-unit">分钟</text>
                </view>
              </view>
              
              <view class="form-item">
                <text class="form-label">自提说明</text>
                <textarea class="form-textarea" v-model="formData.selfPickup.pickupInstructions" placeholder="请输入自提说明，如：到店后请联系店员..." maxlength="200" />
                <text class="input-counter">{{formData.selfPickup.pickupInstructions.length}}/200</text>
              </view>
            </view>
          </view>
        </view>
        
        <!-- 服务标签 -->
        <view class="form-section">
          <view class="section-header">
            <text class="section-title">服务标签</text>
          </view>
          
          <view class="service-tags">
            <checkbox-group @change="onServiceTagsChange">
              <view class="tag-row">
                <label class="service-tag-item" v-for="(tag, index) in serviceTags" :key="index">
                  <checkbox :value="tag.value" :checked="formData.serviceTags.includes(tag.value)" color="#0A84FF" />
                  <text class="service-tag-label">{{tag.label}}</text>
                </label>
              </view>
            </checkbox-group>
          </view>
        </view>
        
        <!-- 底部安全区域 -->
        <view class="safe-area-bottom"></view>
      </form>
    </scroll-view>
    
    <!-- 底部保存按钮 -->
    <view class="footer">
      <view class="safe-area-bottom-footer"></view>
      <view class="footer-content">
        <button class="save-btn" @click="saveOperationInfo">保存修改</button>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      formData: {
        businessHours: [
          {
            days: ['1', '2', '3', '4', '5'],
            startTime: '09:00',
            endTime: '18:00'
          },
          {
            days: ['6', '0'],
            startTime: '10:00',
            endTime: '20:00'
          }
        ],
        isOpen: true,
        deliveryService: {
          enabled: true,
          minOrderAmount: '20',
          deliveryFee: '5',
          deliveryRange: 5,
          minDeliveryTime: '30',
          maxDeliveryTime: '45'
        },
        selfPickup: {
          enabled: true,
          minPickupTime: '15',
          maxPickupTime: '30',
          pickupInstructions: '到店后请联系店员，出示订单号领取商品。'
        },
        serviceTags: ['wifi', 'parking', 'takeaway', 'cards']
      },
      days: [
        { label: '周一', value: '1' },
        { label: '周二', value: '2' },
        { label: '周三', value: '3' },
        { label: '周四', value: '4' },
        { label: '周五', value: '5' },
        { label: '周六', value: '6' },
        { label: '周日', value: '0' }
      ],
      serviceTags: [
        { label: '免费WiFi', value: 'wifi' },
        { label: '停车位', value: 'parking' },
        { label: '打包服务', value: 'takeaway' },
        { label: '支持刷卡', value: 'cards' },
        { label: '支持预约', value: 'reservation' },
        { label: '送货上门', value: 'delivery' },
        { label: '上门安装', value: 'installation' },
        { label: '售后保障', value: 'afterSales' }
      ],
      statusBarHeight: 20
    }
  },
  onLoad() {
    // 页面加载完成后的处理
    this.setStatusBarHeight();
  },
  methods: {
    // 设置状态栏高度
    setStatusBarHeight() {
      // 获取系统信息设置状态栏高度
      uni.getSystemInfo({
        success: (res) => {
          this.statusBarHeight = res.statusBarHeight;
          // 将状态栏高度设置为CSS变量
          if (typeof document !== 'undefined') {
            document.documentElement.style.setProperty('--status-bar-height', `${this.statusBarHeight}px`);
          }
        }
      });
    },
    
    // 返回上一页
    goBack() {
      uni.navigateBack();
    },
    
    // 添加时间段
    addTimePeriod() {
      this.formData.businessHours.push({
        days: [],
        startTime: '09:00',
        endTime: '18:00'
      });
    },
    
    // 删除时间段
    removePeriod(index) {
      this.formData.businessHours.splice(index, 1);
    },
    
    // 选择星期几
    onDayChange(e, periodIndex) {
      this.formData.businessHours[periodIndex].days = e.detail.value;
    },
    
    // 选择时间
    onTimeChange(e, periodIndex, type) {
      if (type === 'start') {
        this.formData.businessHours[periodIndex].startTime = e.detail.value;
      } else {
        this.formData.businessHours[periodIndex].endTime = e.detail.value;
      }
    },
    
    // 营业状态变化
    onStatusChange(e) {
      this.formData.isOpen = e.detail.value;
    },
    
    // 配送服务开关
    onDeliveryChange(e) {
      this.formData.deliveryService.enabled = e.detail.value;
    },
    
    // 自提服务开关
    onPickupChange(e) {
      this.formData.selfPickup.enabled = e.detail.value;
    },
    
    // 配送范围变化
    onRangeChange(e) {
      this.formData.deliveryService.deliveryRange = e.detail.value;
    },
    
    // 服务标签变化
    onServiceTagsChange(e) {
      this.formData.serviceTags = e.detail.value;
    },
    
    // 保存经营信息
    saveOperationInfo() {
      // 表单验证
      if (this.formData.businessHours.length === 0) {
        uni.showToast({
          title: '请添加至少一个营业时间段',
          icon: 'none'
        });
        return;
      }
      
      for (let i = 0; i < this.formData.businessHours.length; i++) {
        const period = this.formData.businessHours[i];
        if (period.days.length === 0) {
          uni.showToast({
            title: '请选择营业天数',
            icon: 'none'
          });
          return;
        }
      }
      
      if (this.formData.deliveryService.enabled) {
        if (!this.formData.deliveryService.minOrderAmount) {
          uni.showToast({
            title: '请输入起送金额',
            icon: 'none'
          });
          return;
        }
        
        if (!this.formData.deliveryService.deliveryFee) {
          uni.showToast({
            title: '请输入配送费用',
            icon: 'none'
          });
          return;
        }
        
        if (!this.formData.deliveryService.minDeliveryTime || !this.formData.deliveryService.maxDeliveryTime) {
          uni.showToast({
            title: '请输入预计送达时间',
            icon: 'none'
          });
          return;
        }
      }
      
      // 显示加载中
      uni.showLoading({
        title: '保存中...',
        mask: true
      });
      
      // 模拟保存过程
      setTimeout(() => {
        // 这里应该是实际的保存逻辑
        uni.hideLoading();
        uni.showToast({
          title: '保存成功',
          icon: 'success',
          duration: 2000,
          success: () => {
            // 延迟返回上一页
            setTimeout(() => {
              this.goBack();
            }, 2000);
          }
        });
      }, 1500);
    }
  }
}
</script>

<style lang="scss">
.operation-info-container {
  min-height: 100vh;
  background-color: #F5F8FC;
  position: relative;
  padding-top: calc(110rpx + var(--status-bar-height, 20px));
  padding-bottom: 180rpx;
}

/* 顶部导航栏样式 */
.header-area {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  background-color: #0A84FF;
  color: #fff;
}

.safe-area-top {
  height: var(--status-bar-height, 20px);
  width: 100%;
}

.custom-navbar {
  height: 110rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 30rpx;
}

.navbar-title {
  font-size: 36rpx;
  font-weight: 600;
}

.navbar-left, .navbar-right {
  width: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-icon {
  width: 48rpx;
  height: 48rpx;
}

/* 背景装饰样式 */
.bg-decoration {
  position: absolute;
  z-index: -1;
  border-radius: 50%;
  opacity: 0.07;
  background-color: #0A84FF;
}

.bg-circle-1 {
  top: -100rpx;
  right: -150rpx;
  width: 400rpx;
  height: 400rpx;
}

.bg-circle-2 {
  top: 20%;
  left: -200rpx;
  width: 500rpx;
  height: 500rpx;
}

.bg-circle-3 {
  bottom: 10%;
  right: -100rpx;
  width: 300rpx;
  height: 300rpx;
}

/* 内容区域 */
.content-scroll {
  padding: 30rpx;
}

/* 表单区块样式 */
.form-section {
  background-color: #FFFFFF;
  border-radius: 24rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.section-header {
  margin-bottom: 30rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  position: relative;
  padding-left: 20rpx;
  display: inline-block;
}

.section-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 6rpx;
  width: 8rpx;
  height: 32rpx;
  background-color: #0A84FF;
  border-radius: 4rpx;
}

/* 营业时间设置 */
.business-time-settings {
  margin-bottom: 20rpx;
}

.time-period {
  background-color: #F5F8FC;
  border-radius: 16rpx;
  padding: 24rpx;
  margin-bottom: 20rpx;
}

.time-period-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.day-selection {
  flex: 1;
}

.day-checkbox {
  display: inline-flex;
  align-items: center;
  margin-right: 16rpx;
  margin-bottom: 16rpx;
}

.day-label {
  font-size: 26rpx;
  color: #333;
  margin-left: 6rpx;
}

.period-actions {
  margin-left: 20rpx;
}

.delete-period {
  color: #FF3B30;
  font-size: 26rpx;
}

.time-selection {
  display: flex;
  align-items: center;
}

.time-picker {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.time-label {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 8rpx;
}

.picker-content {
  background-color: #FFFFFF;
  height: 80rpx;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20rpx;
}

.time-value {
  font-size: 28rpx;
  color: #333;
}

.picker-arrow {
  font-size: 36rpx;
  color: #999;
  transform: rotate(90deg);
}

.time-seperator {
  margin: 0 20rpx;
  font-size: 28rpx;
  color: #999;
  margin-top: 30rpx;
}

.add-time-period {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 88rpx;
  background-color: #F5F8FC;
  border-radius: 12rpx;
  margin-bottom: 30rpx;
}

.add-icon {
  font-size: 32rpx;
  color: #0A84FF;
  margin-right: 10rpx;
}

.add-text {
  font-size: 28rpx;
  color: #0A84FF;
}

.operation-status {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 10rpx;
}

.status-label {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

/* 配送服务设置 */
.switch-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 10rpx;
  margin-bottom: 20rpx;
}

.switch-label {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.delivery-options, .pickup-options {
  background-color: #F5F8FC;
  border-radius: 16rpx;
  padding: 24rpx;
}

.form-item {
  margin-bottom: 30rpx;
  position: relative;
}

.form-label {
  display: block;
  font-size: 26rpx;
  color: #333;
  margin-bottom: 16rpx;
}

.price-input-wrapper {
  display: flex;
  align-items: center;
  background-color: #FFFFFF;
  border-radius: 12rpx;
  padding: 0 20rpx;
  height: 88rpx;
}

.currency-symbol {
  font-size: 28rpx;
  color: #333;
  margin-right: 10rpx;
}

.price-input {
  flex: 1;
  height: 100%;
  font-size: 28rpx;
  color: #333;
}

.time-range-picker {
  display: flex;
  align-items: center;
  height: 88rpx;
}

.time-input {
  width: 120rpx;
  height: 88rpx;
  background-color: #FFFFFF;
  border-radius: 12rpx;
  text-align: center;
  font-size: 28rpx;
  color: #333;
}

.time-separator {
  margin: 0 20rpx;
  font-size: 28rpx;
  color: #999;
}

.time-unit {
  margin-left: 20rpx;
  font-size: 28rpx;
  color: #999;
}

.form-textarea {
  width: 100%;
  height: 160rpx;
  background-color: #FFFFFF;
  border-radius: 12rpx;
  padding: 20rpx;
  font-size: 28rpx;
  color: #333;
  box-sizing: border-box;
  line-height: 1.5;
}

.input-counter {
  position: absolute;
  right: 20rpx;
  bottom: 20rpx;
  font-size: 24rpx;
  color: #999;
}

/* 服务标签 */
.tag-row {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -10rpx;
}

.service-tag-item {
  display: flex;
  align-items: center;
  margin: 10rpx;
  padding: 16rpx 20rpx;
  background-color: #F5F8FC;
  border-radius: 12rpx;
  width: calc(50% - 20rpx);
  box-sizing: border-box;
}

.service-tag-label {
  font-size: 26rpx;
  color: #333;
  margin-left: 10rpx;
}

/* 底部按钮区域 */
.footer {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #FFFFFF;
  padding: 20rpx 30rpx;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  z-index: 99;
}

.safe-area-bottom-footer {
  height: constant(safe-area-inset-bottom);
  height: env(safe-area-inset-bottom);
}

.footer-content {
  display: flex;
  justify-content: center;
}

.save-btn {
  width: 90%;
  height: 88rpx;
  background: linear-gradient(135deg, #0A84FF, #0055FF);
  color: #FFFFFF;
  font-size: 32rpx;
  font-weight: 500;
  border-radius: 44rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 6rpx 16rpx rgba(10, 132, 255, 0.3);
}

.save-btn:active {
  transform: scale(0.98);
  box-shadow: 0 4rpx 8rpx rgba(10, 132, 255, 0.3);
}

/* 底部安全区域 */
.safe-area-bottom {
  height: 100rpx;
}

/* 适配暗黑模式 */
@media (prefers-color-scheme: dark) {
  .operation-info-container {
    background-color: #1C1C1E;
  }
  
  .form-section {
    background-color: #2C2C2E;
  }
  
  .section-title {
    color: #FFFFFF;
  }
  
  .time-period {
    background-color: #3A3A3C;
  }
  
  .day-label {
    color: #FFFFFF;
  }
  
  .time-value, 
  .status-label, 
  .switch-label, 
  .form-label, 
  .price-input, 
  .time-input, 
  .form-textarea,
  .service-tag-label {
    color: #FFFFFF;
  }
  
  .picker-content, 
  .price-input-wrapper, 
  .time-input, 
  .form-textarea {
    background-color: #2C2C2E;
  }
  
  .delivery-options, 
  .pickup-options, 
  .service-tag-item {
    background-color: #3A3A3C;
  }
  
  .footer {
    background-color: #2C2C2E;
  }
}
</style> 