"use strict";
const common_vendor = require("../../../../../common/vendor.js");
const common_assets = require("../../../../../common/assets.js");
const _sfc_main = {
  data() {
    return {
      activeTab: "orders",
      ordersList: [],
      commissionList: []
    };
  },
  onLoad() {
    this.loadData();
  },
  methods: {
    goBack() {
      common_vendor.index.navigateBack();
    },
    loadData() {
      setTimeout(() => {
        this.ordersList = [];
        this.commissionList = [];
      }, 500);
    }
  }
};
if (!Array) {
  const _component_uni_icons = common_vendor.resolveComponent("uni-icons");
  const _component_uni_nav_bar = common_vendor.resolveComponent("uni-nav-bar");
  (_component_uni_icons + _component_uni_nav_bar)();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.p({
      type: "arrow-left",
      color: "#fff",
      size: "22"
    }),
    b: common_vendor.o((...args) => $options.goBack && $options.goBack(...args)),
    c: common_vendor.p({
      title: "推广报表",
      border: false,
      backgroundColor: "#8A2BE2",
      color: "#fff",
      statusBar: true,
      fixed: true
    }),
    d: $data.activeTab === "orders" ? 1 : "",
    e: common_vendor.o(($event) => $data.activeTab = "orders"),
    f: $data.activeTab === "commission" ? 1 : "",
    g: common_vendor.o(($event) => $data.activeTab = "commission"),
    h: $data.activeTab === "orders"
  }, $data.activeTab === "orders" ? common_vendor.e({
    i: $data.ordersList.length === 0
  }, $data.ordersList.length === 0 ? {
    j: common_assets._imports_1$3
  } : {
    k: common_vendor.f($data.ordersList, (item, index, i0) => {
      return {
        a: common_vendor.t(item.orderNo),
        b: common_vendor.t(item.status),
        c: item.goodsImage,
        d: common_vendor.t(item.goodsName),
        e: common_vendor.t(item.price),
        f: common_vendor.t(item.commission),
        g: common_vendor.t(item.time),
        h: index
      };
    })
  }) : {}, {
    l: $data.activeTab === "commission"
  }, $data.activeTab === "commission" ? common_vendor.e({
    m: $data.commissionList.length === 0
  }, $data.commissionList.length === 0 ? {
    n: common_assets._imports_1$3
  } : {
    o: common_vendor.f($data.commissionList, (item, index, i0) => {
      return {
        a: common_vendor.t(item.title),
        b: common_vendor.t(item.type === "income" ? "+" : "-"),
        c: common_vendor.t(item.amount),
        d: item.type === "income" ? 1 : "",
        e: common_vendor.t(item.time),
        f: index
      };
    })
  }) : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-27964770"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../../../.sourcemap/mp-weixin/subPackages/merchant-admin-marketing/pages/marketing/distribution/reports.js.map
