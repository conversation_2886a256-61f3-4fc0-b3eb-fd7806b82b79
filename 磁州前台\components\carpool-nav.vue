<template>
  <view class="custom-navbar" :style="{paddingTop: statusBarHeight + 'px'}">
    <view class="navbar-left" @click="goBack">
      <image src="/static/images/tabbar/最新返回键.png" mode="aspectFit" style="width: 48rpx; height: 48rpx; filter: brightness(0) invert(1);"></image>
    </view>
    <view class="navbar-title">{{ title }}</view>
    <view class="navbar-right">
      <slot name="right"></slot>
    </view>
  </view>
</template>

<script>
export default {
  props: {
    title: {
      type: String,
      default: '拼车'
    }
  },
  data() {
    return {
      statusBarHeight: 20
    }
  },
  created() {
    this.setStatusBarHeight();
  },
  methods: {
    // 设置状态栏高度
    setStatusBarHeight() {
      const systemInfo = uni.getSystemInfoSync();
      this.statusBarHeight = systemInfo.statusBarHeight;
    },
    
    // 返回上一页
    goBack() {
      uni.navigateBack();
    }
  }
}
</script>

<style lang="scss">
/* 自定义导航栏样式 */
.custom-navbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 120rpx;
  padding: 0 30rpx;
  background-color: #0A84FF;
  position: relative;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
  z-index: 100;
  border-bottom-left-radius: 40rpx;
  border-bottom-right-radius: 40rpx;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.navbar-left {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
}

.navbar-title {
  position: absolute;
  left: 0;
  right: 0;
  text-align: center;
  font-size: 36rpx;
  font-weight: 600;
  color: #ffffff;
  letter-spacing: 1rpx;
}

.navbar-right {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
}
</style> 