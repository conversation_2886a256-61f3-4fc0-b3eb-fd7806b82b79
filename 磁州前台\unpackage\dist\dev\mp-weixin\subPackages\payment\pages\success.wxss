
.success-container {
		min-height: 100vh;
		background-color: #f5f7fa;
		padding: 40rpx;
}
.success-content {
		background-color: #FFFFFF;
		border-radius: 24rpx;
		padding: 60rpx 40rpx;
		box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
		display: flex;
		flex-direction: column;
		align-items: center;
}
.success-icon {
		width: 160rpx;
		height: 160rpx;
		margin-bottom: 40rpx;
}
.success-title {
		font-size: 40rpx;
		color: #333;
		font-weight: 600;
		margin-bottom: 20rpx;
}
.success-desc {
		font-size: 28rpx;
		color: #666;
		margin-bottom: 60rpx;
}
.order-info {
		width: 100%;
		background-color: #f8f9fa;
		border-radius: 16rpx;
		padding: 30rpx;
		margin-bottom: 40rpx;
}
.info-item {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 20rpx 0;
		border-bottom: 2rpx solid #f0f0f0;
}
.info-item:last-child {
		border-bottom: none;
}
.label {
		font-size: 28rpx;
		color: #666;
}
.value {
		font-size: 28rpx;
		color: #333;
		font-weight: 500;
}
.action-buttons {
		width: 100%;
		display: flex;
		justify-content: space-between;
		margin-top: 40rpx;
}
.action-btn {
		width: 45%;
		height: 88rpx;
		border-radius: 44rpx;
		font-size: 32rpx;
		font-weight: 500;
		display: flex;
		align-items: center;
		justify-content: center;
		transition: all 0.3s;
}
.view-btn {
		background-color: #0052CC;
		color: #FFFFFF;
		box-shadow: 0 8rpx 16rpx rgba(0, 82, 204, 0.2);
}
.back-btn {
		background-color: #f5f7fa;
		color: #666;
		border: 2rpx solid #e5e5e5;
}
.action-btn:active {
		transform: scale(0.98);
}
