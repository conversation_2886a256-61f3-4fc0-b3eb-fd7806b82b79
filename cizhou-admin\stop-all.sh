#!/bin/bash

echo "🛑 停止磁州生活网后台管理系统"

# 检查当前目录
if [ ! -d "backend" ]; then
    echo "❌ 请在项目根目录（cizhou-admin）下运行此脚本"
    exit 1
fi

# 停止Docker服务
echo "📦 停止后端Docker服务..."
cd backend

if [ -f "docker-compose.dev.yml" ]; then
    docker-compose -f docker-compose.dev.yml down
    echo "✅ Docker服务已停止"
else
    echo "⚠️  docker-compose.dev.yml文件不存在"
fi

# 清理Docker资源（可选）
read -p "是否清理Docker数据卷？这将删除所有数据 (y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo "🧹 清理Docker数据卷..."
    docker-compose -f docker-compose.dev.yml down -v
    docker system prune -f
    echo "✅ Docker数据卷已清理"
fi

# 检查并停止可能运行的Java进程
echo "🔍 检查Java进程..."
JAVA_PIDS=$(ps aux | grep java | grep cizhou | grep -v grep | awk '{print $2}')

if [ ! -z "$JAVA_PIDS" ]; then
    echo "🛑 停止Java进程..."
    echo "$JAVA_PIDS" | xargs kill -15
    sleep 3
    
    # 强制停止仍在运行的进程
    JAVA_PIDS=$(ps aux | grep java | grep cizhou | grep -v grep | awk '{print $2}')
    if [ ! -z "$JAVA_PIDS" ]; then
        echo "🔨 强制停止Java进程..."
        echo "$JAVA_PIDS" | xargs kill -9
    fi
    echo "✅ Java进程已停止"
else
    echo "✅ 没有发现运行中的Java进程"
fi

# 检查并停止可能运行的Node.js进程
echo "🔍 检查Node.js进程..."
NODE_PIDS=$(ps aux | grep node | grep -E "(vite|dev)" | grep -v grep | awk '{print $2}')

if [ ! -z "$NODE_PIDS" ]; then
    echo "🛑 停止Node.js进程..."
    echo "$NODE_PIDS" | xargs kill -15
    sleep 2
    
    # 强制停止仍在运行的进程
    NODE_PIDS=$(ps aux | grep node | grep -E "(vite|dev)" | grep -v grep | awk '{print $2}')
    if [ ! -z "$NODE_PIDS" ]; then
        echo "🔨 强制停止Node.js进程..."
        echo "$NODE_PIDS" | xargs kill -9
    fi
    echo "✅ Node.js进程已停止"
else
    echo "✅ 没有发现运行中的Node.js进程"
fi

echo ""
echo "🎉 系统已完全停止！"
echo ""
echo "📋 已停止的服务："
echo "   ✅ MySQL数据库"
echo "   ✅ Redis缓存"
echo "   ✅ Nacos注册中心"
echo "   ✅ 后端Java服务"
echo "   ✅ 前端Node.js服务"
echo ""
echo "💡 如需重新启动，请运行："
echo "   ./start-all.sh"
