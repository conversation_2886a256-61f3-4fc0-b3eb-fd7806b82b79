<template>
  <view class="withdraw-container">
    <!-- 可提现金额卡片 -->
    <view class="balance-card" :style="{
      borderRadius: '35px',
      boxShadow: '0 8px 20px rgba(172,57,255,0.15)',
      background: 'linear-gradient(135deg, #AC39FF 0%, #B87AFF 100%)',
      padding: '30rpx',
      marginBottom: '30rpx',
      position: 'relative',
      overflow: 'hidden'
    }">
      <!-- 背景装饰 -->
      <view class="bg-decoration" :style="{
        position: 'absolute',
        top: '-50rpx',
        right: '-50rpx',
        width: '300rpx',
        height: '300rpx',
        borderRadius: '50%',
        background: 'rgba(255,255,255,0.1)',
        zIndex: '1'
      }"></view>
      <view class="bg-decoration" :style="{
        position: 'absolute',
        bottom: '-80rpx',
        left: '-80rpx',
        width: '250rpx',
        height: '250rpx',
        borderRadius: '50%',
        background: 'rgba(255,255,255,0.08)',
        zIndex: '1'
      }"></view>
      
      <!-- 可提现金额 -->
      <view class="available-balance" :style="{
        position: 'relative',
        zIndex: '2',
        textAlign: 'center'
      }">
        <text class="label" :style="{
          fontSize: '28rpx',
          color: 'rgba(255,255,255,0.9)',
          marginBottom: '10rpx',
          display: 'block'
        }">可提现金额(元)</text>
        <text class="value" :style="{
          fontSize: '60rpx',
          fontWeight: 'bold',
          color: '#FFFFFF',
          display: 'block',
          textShadow: '0 2rpx 4rpx rgba(0,0,0,0.1)'
        }">{{ availableBalance }}</text>
      </view>
      
      <!-- 提现信息 -->
      <view class="withdraw-info" :style="{
        display: 'flex',
        justifyContent: 'space-around',
        marginTop: '30rpx',
        position: 'relative',
        zIndex: '2',
        background: 'rgba(255,255,255,0.1)',
        borderRadius: '20rpx',
        padding: '20rpx 0'
      }">
        <view class="info-item">
          <text class="info-value" :style="{
            fontSize: '32rpx',
            fontWeight: 'bold',
            color: '#FFFFFF',
            display: 'block',
            textAlign: 'center'
          }">{{ withdrawLimit }}</text>
          <text class="info-label" :style="{
            fontSize: '24rpx',
            color: 'rgba(255,255,255,0.8)',
            display: 'block',
            textAlign: 'center'
          }">提现门槛</text>
        </view>
        
        <view class="info-item">
          <text class="info-value" :style="{
            fontSize: '32rpx',
            fontWeight: 'bold',
            color: '#FFFFFF',
            display: 'block',
            textAlign: 'center'
          }">{{ withdrawCount }}/{{ maxWithdrawCount }}</text>
          <text class="info-label" :style="{
            fontSize: '24rpx',
            color: 'rgba(255,255,255,0.8)',
            display: 'block',
            textAlign: 'center'
          }">本月提现次数</text>
        </view>
        
        <view class="info-item">
          <text class="info-value" :style="{
            fontSize: '32rpx',
            fontWeight: 'bold',
            color: '#FFFFFF',
            display: 'block',
            textAlign: 'center'
          }">{{ processingDays }}</text>
          <text class="info-label" :style="{
            fontSize: '24rpx',
            color: 'rgba(255,255,255,0.8)',
            display: 'block',
            textAlign: 'center'
          }">处理天数</text>
        </view>
      </view>
    </view>
    
    <!-- 提现表单 -->
    <view class="withdraw-form" :style="{
      borderRadius: '35px',
      boxShadow: '0 8px 20px rgba(0,0,0,0.08)',
      background: '#FFFFFF',
      padding: '30rpx',
      marginBottom: '30rpx'
    }">
      <view class="form-header" :style="{
        marginBottom: '30rpx'
      }">
        <text class="form-title" :style="{
          fontSize: '32rpx',
          fontWeight: '600',
          color: '#333333'
        }">提现申请</text>
      </view>
      
      <!-- 提现金额 -->
      <view class="form-item" :style="{
        marginBottom: '30rpx'
      }">
        <text class="form-label" :style="{
          fontSize: '28rpx',
          color: '#333333',
          marginBottom: '15rpx',
          display: 'block'
        }">提现金额</text>
        
        <view class="amount-input-wrapper" :style="{
          display: 'flex',
          alignItems: 'center',
          borderBottom: '1rpx solid #E5E5EA',
          paddingBottom: '15rpx'
        }">
          <text :style="{
            fontSize: '36rpx',
            fontWeight: '600',
            color: '#333333',
            marginRight: '10rpx'
          }">¥</text>
          <input 
            type="digit" 
            v-model="withdrawAmount" 
            placeholder="请输入提现金额" 
            :style="{
              flex: '1',
              fontSize: '36rpx',
              fontWeight: '500'
            }"
          />
        </view>
        
        <view class="amount-tips" :style="{
          display: 'flex',
          justifyContent: 'space-between',
          marginTop: '15rpx'
        }">
          <text :style="{
            fontSize: '24rpx',
            color: '#999999'
          }">提现金额不低于{{ withdrawLimit }}元</text>
          
          <text 
            @click="setMaxAmount" 
            :style="{
              fontSize: '24rpx',
              color: '#AC39FF',
              fontWeight: '500'
            }"
          >全部提现</text>
        </view>
      </view>
      
      <!-- 提现方式 -->
      <view class="form-item" :style="{
        marginBottom: '30rpx'
      }">
        <text class="form-label" :style="{
          fontSize: '28rpx',
          color: '#333333',
          marginBottom: '15rpx',
          display: 'block'
        }">提现方式</text>
        
        <view class="withdraw-methods">
          <view 
            v-for="(method, index) in withdrawMethods" 
            :key="index"
            class="method-item"
            :class="{ active: currentMethod === index }"
            @click="selectMethod(index)"
            :style="{
              padding: '20rpx',
              borderRadius: '20rpx',
              marginBottom: '15rpx',
              display: 'flex',
              alignItems: 'center',
              background: currentMethod === index ? 'rgba(172,57,255,0.1)' : '#F8F8F8',
              border: currentMethod === index ? '1rpx solid #AC39FF' : '1rpx solid transparent'
            }"
          >
            <view class="method-icon" :style="{
              width: '60rpx',
              height: '60rpx',
              borderRadius: '50%',
              background: method.iconBg,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              marginRight: '20rpx'
            }">
              <svg class="icon" viewBox="0 0 24 24" width="24" height="24">
                <path :d="method.icon" :stroke="method.iconColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
              </svg>
            </view>
            
            <view class="method-info" :style="{ flex: '1' }">
              <text class="method-name" :style="{
                fontSize: '28rpx',
                fontWeight: '500',
                color: '#333333',
                display: 'block',
                marginBottom: '5rpx'
              }">{{ method.name }}</text>
              
              <text class="method-desc" :style="{
                fontSize: '24rpx',
                color: '#999999',
                display: 'block'
              }">{{ method.description }}</text>
            </view>
            
            <view class="method-check" :style="{
              width: '40rpx',
              height: '40rpx',
              borderRadius: '50%',
              border: currentMethod === index ? '0' : '1rpx solid #CCCCCC',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              background: currentMethod === index ? '#AC39FF' : 'transparent'
            }">
              <svg v-if="currentMethod === index" class="icon" viewBox="0 0 24 24" width="16" height="16">
                <path d="M5 12l5 5L20 7" stroke="#FFFFFF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
              </svg>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 提现账户 -->
      <view class="form-item" :style="{
        marginBottom: '30rpx'
      }">
        <text class="form-label" :style="{
          fontSize: '28rpx',
          color: '#333333',
          marginBottom: '15rpx',
          display: 'block'
        }">提现账户</text>
        
        <view class="account-info" :style="{
          padding: '20rpx',
          borderRadius: '20rpx',
          background: '#F8F8F8',
          display: 'flex',
          alignItems: 'center'
        }">
          <view class="account-avatar" :style="{
            width: '80rpx',
            height: '80rpx',
            borderRadius: '50%',
            overflow: 'hidden',
            marginRight: '20rpx'
          }">
            <image :src="accountInfo.avatar" mode="aspectFill" :style="{
              width: '100%',
              height: '100%'
            }"></image>
          </view>
          
          <view class="account-details" :style="{ flex: '1' }">
            <text class="account-name" :style="{
              fontSize: '28rpx',
              fontWeight: '500',
              color: '#333333',
              display: 'block',
              marginBottom: '5rpx'
            }">{{ accountInfo.name }}</text>
            
            <text class="account-number" :style="{
              fontSize: '24rpx',
              color: '#999999',
              display: 'block'
            }">{{ accountInfo.number }}</text>
          </view>
          
          <view class="change-account" @click="changeAccount" :style="{
            fontSize: '26rpx',
            color: '#AC39FF',
            padding: '10rpx 20rpx',
            borderRadius: '30rpx',
            background: 'rgba(172,57,255,0.1)'
          }">
            更换
          </view>
        </view>
      </view>
      
      <!-- 提现须知 -->
      <view class="withdraw-notice" :style="{
        padding: '20rpx',
        borderRadius: '20rpx',
        background: 'rgba(255,149,0,0.1)',
        marginBottom: '30rpx'
      }">
        <view class="notice-header" :style="{
          display: 'flex',
          alignItems: 'center',
          marginBottom: '10rpx'
        }">
          <svg class="icon" viewBox="0 0 24 24" width="20" height="20">
            <path d="M12 22c5.523 0 10-4.477 10-10S17.523 2 12 2 2 6.477 2 12s4.477 10 10 10z" stroke="#FF9500" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
            <path d="M12 8v4M12 16h.01" stroke="#FF9500" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
          </svg>
          <text :style="{
            fontSize: '26rpx',
            fontWeight: '500',
            color: '#FF9500',
            marginLeft: '10rpx'
          }">提现须知</text>
        </view>
        
        <text :style="{
          fontSize: '24rpx',
          color: '#FF9500',
          lineHeight: '1.6'
        }">{{ withdrawNotice }}</text>
      </view>
      
      <!-- 提交按钮 -->
      <button 
        class="submit-btn" 
        @click="submitWithdraw"
        :disabled="!isFormValid"
        :style="{
          width: '100%',
          height: '90rpx',
          borderRadius: '45rpx',
          background: isFormValid ? 'linear-gradient(135deg, #AC39FF 0%, #B87AFF 100%)' : '#CCCCCC',
          color: '#FFFFFF',
          fontSize: '32rpx',
          fontWeight: '500',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          boxShadow: isFormValid ? '0 5px 15px rgba(172,57,255,0.3)' : 'none'
        }"
      >
        提交申请
      </button>
    </view>
    
    <!-- 提现记录 -->
    <view class="withdraw-records" :style="{
      borderRadius: '35px',
      boxShadow: '0 8px 20px rgba(0,0,0,0.08)',
      background: '#FFFFFF',
      padding: '30rpx',
      marginBottom: '30rpx'
    }">
      <view class="records-header" :style="{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: '20rpx'
      }">
        <text class="records-title" :style="{
          fontSize: '32rpx',
          fontWeight: '600',
          color: '#333333'
        }">提现记录</text>
        
        <view class="view-all" @click="navigateTo('/subPackages/activity-showcase/pages/distribution/records/index')" :style="{
          fontSize: '26rpx',
          color: '#AC39FF',
          display: 'flex',
          alignItems: 'center'
        }">
          <text>查看全部</text>
          <svg class="icon" viewBox="0 0 24 24" width="16" height="16">
            <path d="M9 18l6-6-6-6" stroke="#AC39FF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
          </svg>
        </view>
      </view>
      
      <!-- 记录列表 -->
      <view class="records-list">
        <view 
          v-for="(record, index) in withdrawRecords" 
          :key="index"
          class="record-item"
          :style="{
            padding: '20rpx 0',
            borderBottom: index < withdrawRecords.length - 1 ? '1rpx solid #F2F2F7' : 'none',
            display: 'flex',
            alignItems: 'center'
          }"
        >
          <view class="record-icon" :style="{
            width: '80rpx',
            height: '80rpx',
            borderRadius: '50%',
            background: getStatusColor(record.status, 0.1),
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            marginRight: '20rpx'
          }">
            <svg class="icon" viewBox="0 0 24 24" width="24" height="24">
              <path :d="getStatusIcon(record.status)" :stroke="getStatusColor(record.status)" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
            </svg>
          </view>
          
          <view class="record-info" :style="{ flex: '1' }">
            <view class="record-top" :style="{
              display: 'flex',
              justifyContent: 'space-between',
              marginBottom: '5rpx'
            }">
              <text class="record-title" :style="{
                fontSize: '28rpx',
                fontWeight: '500',
                color: '#333333'
              }">提现到{{ record.method }}</text>
              
              <text class="record-amount" :style="{
                fontSize: '28rpx',
                fontWeight: '600',
                color: '#333333'
              }">¥{{ record.amount }}</text>
            </view>
            
            <view class="record-bottom" :style="{
              display: 'flex',
              justifyContent: 'space-between'
            }">
              <text class="record-time" :style="{
                fontSize: '24rpx',
                color: '#999999'
              }">{{ record.time }}</text>
              
              <text class="record-status" :style="{
                fontSize: '24rpx',
                color: getStatusColor(record.status)
              }">{{ getStatusText(record.status) }}</text>
            </view>
          </view>
        </view>
        
        <!-- 空状态 -->
        <view v-if="withdrawRecords.length === 0" class="empty-state" :style="{
          padding: '50rpx 0',
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center'
        }">
          <image src="/static/images/empty/empty-records.png" mode="aspectFit" :style="{
            width: '200rpx',
            height: '200rpx',
            marginBottom: '20rpx'
          }"></image>
          <text :style="{
            fontSize: '26rpx',
            color: '#999999'
          }">暂无提现记录</text>
        </view>
      </view>
    </view>
    
    <!-- 底部安全区域 -->
    <view class="safe-area-bottom" :style="{
      height: '100rpx',
      paddingBottom: 'env(safe-area-inset-bottom)'
    }"></view>
  </view>
</template>

<script setup>
import { ref, computed } from 'vue';

// 提现相关数据
const availableBalance = ref('311.11');
const withdrawLimit = ref('10.00');
const withdrawCount = ref(1);
const maxWithdrawCount = ref(3);
const processingDays = ref('1-3天');
const withdrawAmount = ref('');

// 提现方式
const withdrawMethods = ref([
  {
    name: '微信零钱',
    description: '提现到微信零钱，实时到账',
    icon: 'M6 9h12v12H6z M6 5V3h12v2 M16 11v8 M12 11v8 M8 11v8',
    iconColor: '#07C160',
    iconBg: 'rgba(7,193,96,0.1)'
  },
  {
    name: '支付宝',
    description: '提现到支付宝账户，实时到账',
    icon: 'M22 8v8a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h16a2 2 0 0 1 2 2z M6 12l4 4 8-8',
    iconColor: '#1677FF',
    iconBg: 'rgba(22,119,255,0.1)'
  },
  {
    name: '银行卡',
    description: '提现到银行卡，1-3个工作日到账',
    icon: 'M3 10h18M7 15h1m4 0h1m4 0h1M21 4v16a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V4',
    iconColor: '#FF9500',
    iconBg: 'rgba(255,149,0,0.1)'
  }
]);
const currentMethod = ref(0);

// 账户信息
const accountInfo = ref({
  avatar: 'https://via.placeholder.com/80',
  name: '张三',
  number: '微信账号已绑定'
});

// 提现须知
const withdrawNotice = ref('1. 提现金额不低于10元，单日最高提现金额为5000元；\n2. 每月可免费提现3次，超出次数将收取1%手续费；\n3. 提现申请提交后，将在1-3个工作日内处理完成；\n4. 如有问题，请联系客服。');

// 提现记录
const withdrawRecords = ref([
  {
    method: '微信零钱',
    amount: '100.00',
    time: '2023-05-15 14:30:25',
    status: 'success'
  },
  {
    method: '支付宝',
    amount: '200.00',
    time: '2023-05-10 09:15:36',
    status: 'processing'
  },
  {
    method: '银行卡',
    amount: '500.00',
    time: '2023-05-01 16:42:18',
    status: 'failed'
  }
]);

// 表单验证
const isFormValid = computed(() => {
  const amount = parseFloat(withdrawAmount.value);
  const limit = parseFloat(withdrawLimit.value);
  return amount && amount >= limit && amount <= parseFloat(availableBalance.value);
});

// 选择提现方式
function selectMethod(index) {
  currentMethod.value = index;
  
  // 更新账户信息
  if (index === 0) {
    accountInfo.value.name = '张三';
    accountInfo.value.number = '微信账号已绑定';
  } else if (index === 1) {
    accountInfo.value.name = '张三';
    accountInfo.value.number = '支付宝账号已绑定';
  } else {
    accountInfo.value.name = '张三';
    accountInfo.value.number = '工商银行 (1234)';
  }
}

// 设置最大提现金额
function setMaxAmount() {
  withdrawAmount.value = availableBalance.value;
}

// 更换账户
function changeAccount() {
  uni.showToast({
    title: '功能开发中',
    icon: 'none'
  });
}

// 提交提现申请
function submitWithdraw() {
  if (!isFormValid.value) {
    uni.showToast({
      title: '请检查提现金额',
      icon: 'none'
    });
    return;
  }
  
  uni.showLoading({
    title: '提交中...'
  });
  
  setTimeout(() => {
    uni.hideLoading();
    uni.showToast({
      title: '提现申请已提交',
      icon: 'success'
    });
    
    // 模拟提交成功后重置表单
    withdrawAmount.value = '';
    
    // 模拟添加新记录
    const newRecord = {
      method: withdrawMethods.value[currentMethod.value].name,
      amount: withdrawAmount.value,
      time: new Date().toLocaleString(),
      status: 'processing'
    };
    withdrawRecords.value.unshift(newRecord);
  }, 1500);
}

// 获取状态颜色
function getStatusColor(status, alpha = 1) {
  switch (status) {
    case 'success':
      return alpha === 1 ? '#34C759' : 'rgba(52,199,89,' + alpha + ')';
    case 'processing':
      return alpha === 1 ? '#007AFF' : 'rgba(0,122,255,' + alpha + ')';
    case 'failed':
      return alpha === 1 ? '#FF3B30' : 'rgba(255,59,48,' + alpha + ')';
    default:
      return alpha === 1 ? '#8E8E93' : 'rgba(142,142,147,' + alpha + ')';
  }
}

// 获取状态图标
function getStatusIcon(status) {
  switch (status) {
    case 'success':
      return 'M22 11.08V12a10 10 0 1 1-5.93-9.14';
    case 'processing':
      return 'M12 22c5.523 0 10-4.477 10-10S17.523 2 12 2 2 6.477 2 12s4.477 10 10 10z';
    case 'failed':
      return 'M12 22c5.523 0 10-4.477 10-10S17.523 2 12 2 2 6.477 2 12s4.477 10 10 10z';
    default:
      return 'M12 22c5.523 0 10-4.477 10-10S17.523 2 12 2 2 6.477 2 12s4.477 10 10 10z';
  }
}

// 获取状态文本
function getStatusText(status) {
  switch (status) {
    case 'success':
      return '提现成功';
    case 'processing':
      return '处理中';
    case 'failed':
      return '提现失败';
    default:
      return '未知状态';
  }
}

// 页面导航
function navigateTo(url) {
  uni.navigateTo({ url });
}
</script>

<style scoped>
.withdraw-container {
  padding: 30rpx;
  background-color: #F2F2F7;
  min-height: 100vh;
}

.submit-btn:active {
  opacity: 0.9;
  transform: scale(0.98);
}
</style> 