<template>
  <view class="franchise-container">
    <!-- 自定义导航栏 -->
    <view class="custom-navbar">
      <view class="navbar-left" @click="goBack">
        <image src="/static/images/tabbar/最新返回键.png" class="back-icon"></image>
      </view>
      <view class="navbar-title">区域加盟</view>
      <view class="navbar-right">
        <!-- 预留位置与发布页面保持一致 -->
      </view>
    </view>
    
    <!-- 添加顶部安全区域 -->
    <view class="safe-area-top"></view>
    
    <!-- 加盟介绍卡片 -->
    <view class="franchise-card intro-card">
      <view class="intro-header">
        <view class="intro-title">
          <text class="title-text">区域加盟招募</text>
          <view class="title-tag">招募中</view>
        </view>
        <view class="intro-subtitle">成为区域合伙人，共享千亿市场</view>
      </view>
      <view class="intro-content">
        <view class="intro-item">
          <image class="intro-icon" src="/static/images/tabbar/合伙人.png" mode="aspectFit"></image>
          <view class="intro-info">
            <view class="intro-name">专属收益</view>
            <view class="intro-desc">区域内30%服务收入分成</view>
          </view>
        </view>
        <view class="intro-item">
          <image class="intro-icon" src="/static/images/tabbar/商家信息.png" mode="aspectFit"></image>
          <view class="intro-info">
            <view class="intro-name">运营支持</view>
            <view class="intro-desc">总部提供全方位技术与营销支持</view>
          </view>
        </view>
        <view class="intro-item">
          <image class="intro-icon" src="/static/images/tabbar/商家活动.png" mode="aspectFit"></image>
          <view class="intro-info">
            <view class="intro-name">资源赋能</view>
            <view class="intro-desc">商家资源与流量扶持</view>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 区域选择卡片 -->
    <view class="franchise-card region-card">
      <view class="region-header">
        <text class="region-title">选择加盟区域</text>
        <text class="region-subtitle">每个县区限3个加盟名额</text>
      </view>
      
      <!-- 区域选择器 -->
      <view class="region-selector">
        <view class="selector-row">
          <view class="selector-item">
            <view class="selector-label">省份</view>
            <picker mode="selector" :range="provinces" @change="provinceChange" class="selector-picker">
              <view class="picker-value">
                <text>{{ currentProvince || '请选择' }}</text>
                <text class="cuIcon-unfold"></text>
              </view>
            </picker>
          </view>
          <view class="selector-item">
            <view class="selector-label">城市</view>
            <picker mode="selector" :range="cities" @change="cityChange" :disabled="!currentProvince" class="selector-picker">
              <view class="picker-value" :class="{'disabled': !currentProvince}">
                <text>{{ currentCity || '请选择' }}</text>
                <text class="cuIcon-unfold"></text>
              </view>
            </picker>
          </view>
        </view>
        <view class="selector-row">
          <view class="selector-item full-width">
            <view class="selector-label">区/县</view>
            <picker mode="selector" :range="districts" @change="districtChange" :disabled="!currentCity" class="selector-picker">
              <view class="picker-value" :class="{'disabled': !currentCity}">
                <text>{{ currentDistrict || '请选择' }}</text>
                <text class="cuIcon-unfold"></text>
              </view>
            </picker>
          </view>
        </view>
      </view>
      
      <!-- 区域地图 -->
      <view class="region-map">
        <view class="map-title">
          <view class="map-title-dot"></view>
          <text>区域地图</text>
        </view>
        <view class="map-image">
          <text v-if="!selectedRegion">请先选择区域查看地图</text>
          <text v-else>{{ selectedRegion }} 区域地图</text>
        </view>
      </view>
      
      <!-- 状态指示器 -->
      <view class="status-indicators">
        <view class="status-item">
          <view class="status-dot available-dot"></view>
          <text class="status-text">可申请</text>
        </view>
        <view class="status-item">
          <view class="status-dot pending-dot"></view>
          <text class="status-text">审核中</text>
        </view>
        <view class="status-item">
          <view class="status-dot occupied-dot"></view>
          <text class="status-text">已占用</text>
        </view>
      </view>
      
      <!-- 已选区域展示 -->
      <view class="selected-region" v-if="selectedRegion">
        <view class="region-info">
          <text class="region-name">{{ selectedRegion }}</text>
          <view class="region-status" :class="{'available': regionStatus === '可申请', 'pending': regionStatus === '审核中', 'occupied': regionStatus === '已占用'}">{{ regionStatus }}</view>
        </view>
        <view class="region-data">
          <view class="data-box">
            <text class="data-value">{{ regionData.merchants }}</text>
            <text class="data-label">商家数</text>
          </view>
          <view class="data-box">
            <text class="data-value">{{ regionData.users }}</text>
            <text class="data-label">用户数</text>
          </view>
          <view class="data-box">
            <text class="data-value">{{ regionData.orders }}万</text>
            <text class="data-label">月订单</text>
          </view>
          <view class="data-box">
            <text class="data-value">{{ regionData.income }}万</text>
            <text class="data-label">月收入</text>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 申请要求卡片 -->
    <view class="franchise-card requirements-card">
      <view class="card-header">
        <text class="card-title">申请要求</text>
      </view>
      <view class="requirements-list">
        <view class="requirement-item">
          <view class="requirement-dot"></view>
          <text class="requirement-text">有当地资源或渠道，能够发展商家</text>
        </view>
        <view class="requirement-item">
          <view class="requirement-dot"></view>
          <text class="requirement-text">有运营团队，能够提供本地化服务</text>
        </view>
        <view class="requirement-item">
          <view class="requirement-dot"></view>
          <text class="requirement-text">注册有效营业执照的企业或个体工商户</text>
        </view>
        <view class="requirement-item">
          <view class="requirement-dot"></view>
          <text class="requirement-text">资金实力：县级20万，市级50万，省级200万</text>
        </view>
      </view>
    </view>
    
    <!-- 加盟流程卡片 -->
    <view class="franchise-card process-card">
      <view class="card-header">
        <text class="card-title">加盟流程</text>
      </view>
      <view class="process-steps">
        <view class="process-step">
          <view class="step-circle">1</view>
          <view class="step-line" v-if="showStepLine(1)"></view>
          <view class="step-content">
            <view class="step-title">提交申请</view>
            <view class="step-desc">填写基本信息</view>
          </view>
        </view>
        <view class="process-step">
          <view class="step-circle">2</view>
          <view class="step-line" v-if="showStepLine(2)"></view>
          <view class="step-content">
            <view class="step-title">资质审核</view>
            <view class="step-desc">3个工作日内</view>
          </view>
        </view>
        <view class="process-step">
          <view class="step-circle">3</view>
          <view class="step-line" v-if="showStepLine(3)"></view>
          <view class="step-content">
            <view class="step-title">签约授权</view>
            <view class="step-desc">线上签署协议</view>
          </view>
        </view>
        <view class="process-step">
          <view class="step-circle">4</view>
          <view class="step-content">
            <view class="step-title">系统培训</view>
            <view class="step-desc">运营能力提升</view>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 底部按钮 -->
    <view class="bottom-actions">
      <button class="action-btn consult-btn" @tap="contactConsultant">
        <text class="btn-icon cuIcon-service"></text>
        <text>咨询顾问</text>
      </button>
      <button class="action-btn apply-btn" @tap="submitApplication" :disabled="!canApply">
        <text>立即申请</text>
      </button>
    </view>
  </view>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue';
import { province_list, city_list, county_list, city_province_map, county_city_map } from '@/static/data/china-area-data.js';

// 响应式状态
const provinces = ref(Object.values(province_list));
const cities = ref([]);
const districts = ref([]);
const provinceCodes = ref(Object.keys(province_list));
const cityCodes = ref([]);
const districtCodes = ref([]);
const currentProvince = ref('');
const currentCity = ref('');
const currentDistrict = ref('');
const selectedRegion = ref('');
const regionStatus = ref('');
const regionData = reactive({
  merchants: 0,
  users: 0,
  orders: 0,
  income: 0
});

// 计算属性
const canApply = computed(() => {
  return selectedRegion.value && regionStatus.value === '可申请';
});

// 方法
const goBack = () => {
  uni.navigateBack({
    fail: () => {
      uni.switchTab({ url: '/pages/my/my' });
    }
  });
};

const provinceChange = (e) => {
  const index = e.detail.value;
  const provinceCode = provinceCodes.value[index];
  currentProvince.value = province_list[provinceCode];
  // 获取该省下所有城市
  cityCodes.value = Object.keys(city_list).filter(code => city_province_map[code] == provinceCode);
  cities.value = cityCodes.value.map(code => city_list[code]);
  currentCity.value = '';
  currentDistrict.value = '';
  selectedRegion.value = '';
  districts.value = [];
};

const cityChange = (e) => {
  const index = e.detail.value;
  const cityCode = cityCodes.value[index];
  currentCity.value = city_list[cityCode];
  // 获取该市下所有区县
  districtCodes.value = Object.keys(county_list).filter(code => county_city_map[code] == cityCode);
  districts.value = districtCodes.value.map(code => county_list[code]);
  currentDistrict.value = '';
  selectedRegion.value = '';
};

const districtChange = (e) => {
  const index = e.detail.value;
  const districtCode = districtCodes.value[index];
  currentDistrict.value = county_list[districtCode];
  selectedRegion.value = `${currentProvince.value} ${currentCity.value} ${currentDistrict.value}`;
  generateRegionData();
};

const generateRegionData = () => {
  const randomIndex = Math.floor(Math.random() * 10);
  if (randomIndex < 7) {
    regionStatus.value = '可申请';
  } else if (randomIndex < 9) {
    regionStatus.value = '审核中';
  } else {
    regionStatus.value = '已占用';
  }
  regionData.merchants = Math.floor(Math.random() * 2000) + 500;
  regionData.users = Math.floor(Math.random() * 50000) + 10000;
  regionData.orders = (Math.floor(Math.random() * 100) + 20) / 10;
  regionData.income = (Math.floor(Math.random() * 500) + 50) / 10;
};

const submitApplication = () => {
  if (!selectedRegion.value || !canApply.value) {
    uni.showToast({ title: '请先选择可申请的区域', icon: 'none' });
    return;
  }
  uni.navigateTo({ url: '/subPackages/franchise/pages/application-form?region=' + encodeURIComponent(selectedRegion.value) });
};

const contactConsultant = () => {
  uni.showModal({
    title: '联系加盟顾问',
    content: '您可以通过以下方式联系我们的加盟顾问：\n\n电话：400-888-8888\n微信：tcqy_jmzx\n工作时间：9:00-18:00（周一至周五）',
    showCancel: false,
    confirmText: '我知道了'
  });
};

const showStepLine = (step) => {
  return step < 4;
};

// 生命周期钩子
onMounted(() => {
  // 初始化省份列表
  provinces.value = Object.values(province_list);
  provinceCodes.value = Object.keys(province_list);
});
</script>

<style lang="scss" scoped>
.franchise-container {
  padding-bottom: 120rpx; // 为底部按钮预留空间
  min-height: 100vh;
  background-color: #F5F7FA;
}

.safe-area {
  height: 20rpx;
  width: 100%;
}

.safe-area-top {
  height: 180rpx;
  width: 100%;
}

.franchise-card {
  margin: 30rpx;
  border-radius: 20rpx;
  background: #FFFFFF;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
}

.intro-card {
  background: linear-gradient(135deg, #FFFFFF, #F9FCFF);
  border: 1px solid rgba(22, 119, 255, 0.1);
  margin-top: 30rpx; /* 调整顶部边距 */
}

.intro-header {
  margin-bottom: 30rpx;
}

.intro-title {
  display: flex;
  align-items: center;
  margin-bottom: 10rpx;
}

.title-text {
  font-size: 36rpx;
  font-weight: 600;
  color: #333333;
}

.title-tag {
  margin-left: 20rpx;
  font-size: 22rpx;
  color: #FF6B00;
  background-color: rgba(255, 107, 0, 0.1);
  padding: 4rpx 14rpx;
  border-radius: 4rpx;
}

.intro-subtitle {
  font-size: 26rpx;
  color: #666666;
}

.intro-content {
  display: flex;
  flex-direction: column;
}

.intro-item {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1px solid #F5F5F5;
  
  &:last-child {
    border-bottom: none;
  }
}

.intro-icon {
  width: 80rpx;
  height: 80rpx;
  margin-right: 20rpx;
  background-color: rgba(22, 119, 255, 0.06);
  padding: 15rpx;
  border-radius: 50%;
}

.intro-info {
  flex: 1;
}

.intro-name {
  font-size: 28rpx;
  color: #333333;
  font-weight: 500;
  margin-bottom: 6rpx;
}

.intro-desc {
  font-size: 24rpx;
  color: #999999;
}

.region-header {
  margin-bottom: 30rpx;
}

.region-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  display: block;
  margin-bottom: 10rpx;
}

.region-subtitle {
  font-size: 24rpx;
  color: #999999;
}

.region-selector {
  padding: 20rpx;
  background-color: #F8F9FC;
  border-radius: 12rpx;
  margin-bottom: 30rpx;
}

.selector-row {
  display: flex;
  margin-bottom: 20rpx;
}

.selector-item {
  flex: 1;
  margin-right: 20rpx;
}

.selector-item:last-child {
  margin-right: 0;
}

.full-width {
  width: 100%;
}

.selector-label {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 10rpx;
  font-weight: 500;
}

.selector-picker {
  background-color: #fff;
  height: 80rpx;
  border-radius: 8rpx;
  padding: 0 20rpx;
  display: flex;
  align-items: center;
  border: 1px solid #EAEDF2;
}

.picker-value {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  color: #333;
  font-size: 28rpx;
}

.picker-value.disabled {
  color: #bbb;
}

.picker-value text:last-child {
  color: #999;
  font-size: 24rpx;
}

/* 新增区域图示 */
.region-map {
  padding: 0 20rpx;
  margin-bottom: 30rpx;
}

.map-title {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 15rpx;
  display: flex;
  align-items: center;
}

.map-title-dot {
  width: 6rpx;
  height: 6rpx;
  border-radius: 50%;
  background-color: #0066FF;
  margin-right: 10rpx;
}

.map-image {
  width: 100%;
  height: 300rpx;
  background-color: #F8F9FC;
  border-radius: 12rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 24rpx;
  color: #999;
}

/* 状态指示器样式 */
.status-indicators {
  display: flex;
  justify-content: space-between;
  margin-top: 20rpx;
  padding: 0 20rpx;
}

.status-item {
  display: flex;
  align-items: center;
}

.status-dot {
  width: 16rpx;
  height: 16rpx;
  border-radius: 50%;
  margin-right: 8rpx;
}

.available-dot {
  background-color: #34C759;
}

.pending-dot {
  background-color: #FF9500;
}

.occupied-dot {
  background-color: #FF3B30;
}

.status-text {
  font-size: 24rpx;
  color: #666;
}

.selected-region {
  padding: 30rpx;
  background-color: #F8FCFF;
  border-radius: 12rpx;
  border: 1px solid rgba(0, 102, 255, 0.1);
  margin-top: 20rpx;
}

.region-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.region-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.region-status {
  padding: 4rpx 12rpx;
  border-radius: 6rpx;
  font-size: 24rpx;
  font-weight: 500;
}

.available {
  background-color: rgba(52, 199, 89, 0.1);
  color: #34C759;
}

.pending {
  background-color: rgba(255, 149, 0, 0.1);
  color: #FF9500;
}

.occupied {
  background-color: rgba(255, 59, 48, 0.1);
  color: #FF3B30;
}

.region-data {
  display: flex;
  justify-content: space-between;
  margin-top: 10rpx;
}

.data-box {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 25%;
}

.data-value {
  font-size: 32rpx;
  font-weight: 700;
  color: #333;
  margin-bottom: 4rpx;
}

.data-label {
  font-size: 24rpx;
  color: #999;
}

.card-header {
  margin-bottom: 20rpx;
}

.card-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  position: relative;
  padding-left: 20rpx;
  
  &::before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 6rpx;
    height: 30rpx;
    background-color: #1677FF;
    border-radius: 3rpx;
  }
}

.requirements-list {
  padding: 10rpx 0;
}

.requirement-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 20rpx;
  
  &:last-child {
    margin-bottom: 0;
  }
}

.requirement-dot {
  width: 12rpx;
  height: 12rpx;
  border-radius: 50%;
  background-color: #1677FF;
  margin-top: 10rpx;
  margin-right: 15rpx;
  flex-shrink: 0;
}

.requirement-text {
  font-size: 26rpx;
  color: #666666;
  line-height: 1.5;
  flex: 1;
}

.process-steps {
  padding: 20rpx 0;
}

.process-step {
  display: flex;
  align-items: flex-start;
  position: relative;
  padding-bottom: 40rpx;
  
  &:last-child {
    padding-bottom: 0;
  }
}

.step-circle {
  width: 50rpx;
  height: 50rpx;
  border-radius: 25rpx;
  background-color: #1677FF;
  color: #FFFFFF;
  font-size: 28rpx;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
  position: relative;
  z-index: 2;
}

.step-line {
  position: absolute;
  left: 25rpx;
  top: 50rpx;
  width: 2rpx;
  height: calc(100% - 50rpx);
  background-color: #E5E5E5;
  z-index: 1;
}

.step-content {
  flex: 1;
}

.step-title {
  font-size: 28rpx;
  color: #333333;
  font-weight: 500;
  margin-bottom: 6rpx;
}

.step-desc {
  font-size: 24rpx;
  color: #999999;
}

.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 110rpx;
  display: flex;
  align-items: center;
  padding: 0 30rpx;
  background-color: #FFFFFF;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  z-index: 100;
}

.action-btn {
  height: 80rpx;
  line-height: 80rpx;
  border-radius: 40rpx;
  font-size: 28rpx;
  font-weight: 500;
  margin: 0;
  
  &.consult-btn {
    flex: 1;
    background-color: #F5F7FA;
    color: #666666;
    margin-right: 20rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    
    .btn-icon {
      margin-right: 8rpx;
    }
  }
  
  &.apply-btn {
    flex: 2;
    background: linear-gradient(90deg, #1677FF, #4F9DFF);
    color: #FFFFFF;
    
    &[disabled] {
      background: linear-gradient(90deg, #CCCCCC, #E5E5E5);
      color: #FFFFFF;
      opacity: 1;
    }
  }
}

/* 修改自定义导航栏样式，匹配发布页 */
:deep(.bg-gradient-blue) {
  background: linear-gradient(135deg, #0066FF, #0052CC);
  box-shadow: 0 4rpx 12rpx rgba(0, 82, 204, 0.2);
}

:deep(.cu-custom .cu-bar) {
  height: 88rpx !important;
  padding-top: 44px !important; /* 状态栏高度 */
}

:deep(.cu-custom .content) {
  top: calc(44px + 40rpx) !important; /* 调整标题位置上移 */
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  width: 100% !important;
}

:deep(.cu-custom .action) {
  margin-top: 8rpx !important; /* 调整关闭键位置与标题对齐 */
}

:deep(.cu-bar .action:first-child) {
  margin-left: 30rpx;
  font-size: 30rpx;
}

/* 导航栏样式 */
.navbar {
  position: relative;
  z-index: 1;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 自定义导航栏 */
.custom-navbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 88rpx;
  padding: 0 30rpx;
  padding-top: 44px; /* 状态栏高度 */
  position: fixed; /* 改为固定定位 */
  top: 0;
  left: 0;
  right: 0;
  background-image: linear-gradient(135deg, #0066FF, #0052CC); /* 改为与发布页一致的渐变角度 */
  box-shadow: 0 4rpx 12rpx rgba(0, 82, 204, 0.15);
  z-index: 100; /* 提高z-index确保在最上层 */
}

.navbar-title {
  position: absolute;
  left: 0;
  right: 0;
  color: #ffffff;
  font-size: 36rpx;
  font-weight: 700;
  font-family: 'AlimamaShuHeiTi', sans-serif;
  text-align: center;
}

.navbar-right {
  width: 40rpx;
  height: 40rpx;
}

.navbar-left {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  z-index: 20; /* 确保在标题上层，可以被点击 */
}

.back-icon {
  width: 100%;
  height: 100%;
}
</style>