<view class="activity-management" style="{{I}}"><view class="status-bar" style="{{'height:' + a}}"></view><view class="navbar"><view class="navbar-left" catchtap="{{d}}"><view class="back-btn"><svg wx:if="{{c}}" u-s="{{['d']}}" u-i="36c93d94-0" bind:__l="__l" u-p="{{c}}"><path wx:if="{{b}}" u-i="36c93d94-1,36c93d94-0" bind:__l="__l" u-p="{{b}}"/></svg></view></view><view class="navbar-title">活动管理</view><view class="navbar-right"><view class="navbar-action" catchtap="{{i}}"><svg wx:if="{{h}}" u-s="{{['d']}}" u-i="36c93d94-2" bind:__l="__l" u-p="{{h}}"><path wx:if="{{e}}" u-i="36c93d94-3,36c93d94-2" bind:__l="__l" u-p="{{e}}"/><path wx:if="{{f}}" u-i="36c93d94-4,36c93d94-2" bind:__l="__l" u-p="{{f}}"/><path wx:if="{{g}}" u-i="36c93d94-5,36c93d94-2" bind:__l="__l" u-p="{{g}}"/></svg></view></view></view><scroll-view class="content-area" scroll-y refresher-enabled refresher-triggered="{{F}}" bindrefresherrefresh="{{G}}"><view class="statistics-card"><view class="statistics-item"><text class="statistics-value">{{j}}</text><text class="statistics-label">活动总数</text></view><view class="statistics-divider"></view><view class="statistics-item"><text class="statistics-value">{{k}}</text><text class="statistics-label">进行中</text></view><view class="statistics-divider"></view><view class="statistics-item"><text class="statistics-value">{{l}}</text><text class="statistics-label">未开始</text></view><view class="statistics-divider"></view><view class="statistics-item"><text class="statistics-value">{{m}}</text><text class="statistics-label">已结束</text></view></view><view class="filter-section"><view class="filter-category"><view class="category-title">活动类型</view><scroll-view class="filter-tabs" scroll-x show-scrollbar="false"><view wx:for="{{n}}" wx:for-item="type" wx:key="b" class="{{['filter-tab', type.c && 'active']}}" catchtap="{{type.d}}"><text>{{type.a}}</text></view></scroll-view></view><view class="filter-category"><view class="category-title">活动状态</view><scroll-view class="filter-tabs" scroll-x show-scrollbar="false"><view wx:for="{{o}}" wx:for-item="tab" wx:key="b" class="{{['filter-tab', tab.c && 'active']}}" catchtap="{{tab.d}}"><text>{{tab.a}}</text></view></scroll-view></view></view><view wx:if="{{p}}" class="loading-container"><view class="loading-spinner"></view><text class="loading-text">加载中...</text></view><view wx:else><view wx:if="{{q}}" class="empty-container"><image src="{{r}}" class="empty-image"></image><text class="empty-text">暂无活动数据</text><view class="empty-action" catchtap="{{s}}"><text>立即创建</text></view></view><view wx:else class="activity-list"><view wx:for="{{t}}" wx:for-item="item" wx:key="w" class="activity-card" catchtap="{{item.x}}"><view class="card-header"><view class="status-tags"><view class="{{['type-tag', item.b]}}"><text>{{item.a}}</text></view><view class="{{['status-tag', item.d]}}"><text>{{item.c}}</text></view><view wx:if="{{item.e}}" class="top-tag"><text>已置顶</text></view></view></view><view class="card-content"><view class="card-image"><image src="{{item.f}}" mode="aspectFill"></image></view><view class="card-info"><text class="activity-title">{{item.g}}</text><view class="activity-time"><svg wx:if="{{w}}" u-s="{{['d']}}" u-i="{{item.i}}" bind:__l="__l" u-p="{{w}}"><path wx:if="{{v}}" u-i="{{item.h}}" bind:__l="__l" u-p="{{v}}"/></svg><text>{{item.j}}</text></view><view class="activity-stats"><view class="stat-item"><svg wx:if="{{z}}" u-s="{{['d']}}" u-i="{{item.m}}" bind:__l="__l" u-p="{{z}}"><path wx:if="{{x}}" u-i="{{item.k}}" bind:__l="__l" u-p="{{x}}"/><path wx:if="{{y}}" u-i="{{item.l}}" bind:__l="__l" u-p="{{y}}"/></svg><text>{{item.n}}</text></view><view class="stat-item"><svg wx:if="{{B}}" u-s="{{['d']}}" u-i="{{item.p}}" bind:__l="__l" u-p="{{B}}"><path wx:if="{{A}}" u-i="{{item.o}}" bind:__l="__l" u-p="{{A}}"/></svg><text>{{item.q}}</text></view></view></view></view><view class="card-actions"><view class="action-button edit" catchtap="{{item.r}}"><text>编辑</text></view><view class="action-button promote" catchtap="{{item.s}}"><text>推广</text></view><view class="action-button share" catchtap="{{item.t}}"><text>转发</text></view><view class="action-button more" catchtap="{{item.v}}"><text>更多</text></view></view></view></view><view wx:if="{{C}}" class="load-more" catchtap="{{D}}"><text>加载更多</text></view><view wx:elif="{{E}}" class="no-more"><text>已加载全部数据</text></view></view></scroll-view><view class="fab-button" catchtap="{{H}}"><text class="fab-text">发布</text></view></view>