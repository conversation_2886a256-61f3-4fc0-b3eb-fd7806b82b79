# 🗄️ 磁州生活网后台管理系统 - 数据库设计方案

## 🎯 **数据库设计原则**

### **设计理念**
```yaml
规范化设计: 遵循数据库范式，减少数据冗余
性能优先: 合理的反范式设计，提升查询性能
扩展性强: 支持水平扩展和垂直扩展
安全可靠: 数据完整性约束和安全防护
易于维护: 清晰的命名规范和文档说明
```

### **分库分表策略**
```yaml
垂直分库:
  用户库 (cizhou_user): 用户相关数据
  商家库 (cizhou_merchant): 商家相关数据
  订单库 (cizhou_order): 订单交易数据
  内容库 (cizhou_content): 内容信息数据
  营销库 (cizhou_marketing): 营销活动数据
  系统库 (cizhou_system): 系统配置数据
  日志库 (cizhou_log): 日志审计数据

水平分表:
  用户表: 按用户ID取模分16表
  订单表: 按创建时间分月表
  日志表: 按日期分日表
  消息表: 按用户ID取模分8表
```

## 📊 **核心数据表设计**

### **用户库 (cizhou_user)**

#### **用户基础信息表 (t_user)**
```sql
CREATE TABLE `t_user` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '用户ID',
  `user_no` varchar(32) NOT NULL COMMENT '用户编号',
  `username` varchar(50) DEFAULT NULL COMMENT '用户名',
  `phone` varchar(20) NOT NULL COMMENT '手机号',
  `email` varchar(100) DEFAULT NULL COMMENT '邮箱',
  `password` varchar(128) NOT NULL COMMENT '密码(加密)',
  `salt` varchar(32) NOT NULL COMMENT '密码盐值',
  `nickname` varchar(50) DEFAULT NULL COMMENT '昵称',
  `avatar` varchar(255) DEFAULT NULL COMMENT '头像URL',
  `gender` tinyint(1) DEFAULT '0' COMMENT '性别:0-未知,1-男,2-女',
  `birthday` date DEFAULT NULL COMMENT '生日',
  `province` varchar(50) DEFAULT NULL COMMENT '省份',
  `city` varchar(50) DEFAULT NULL COMMENT '城市',
  `district` varchar(50) DEFAULT NULL COMMENT '区县',
  `address` varchar(255) DEFAULT NULL COMMENT '详细地址',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态:0-禁用,1-正常,2-冻结',
  `level` tinyint(1) NOT NULL DEFAULT '1' COMMENT '用户等级:1-普通,2-VIP,3-SVIP',
  `points` int(11) NOT NULL DEFAULT '0' COMMENT '积分余额',
  `balance` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '账户余额',
  `register_source` varchar(20) DEFAULT NULL COMMENT '注册来源',
  `register_ip` varchar(45) DEFAULT NULL COMMENT '注册IP',
  `last_login_time` datetime DEFAULT NULL COMMENT '最后登录时间',
  `last_login_ip` varchar(45) DEFAULT NULL COMMENT '最后登录IP',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `is_deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除:0-否,1-是',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_no` (`user_no`),
  UNIQUE KEY `uk_phone` (`phone`),
  KEY `idx_status` (`status`),
  KEY `idx_level` (`level`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户基础信息表';
```

#### **用户认证信息表 (t_user_auth)**
```sql
CREATE TABLE `t_user_auth` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '认证ID',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `auth_type` tinyint(1) NOT NULL COMMENT '认证类型:1-身份证,2-营业执照',
  `real_name` varchar(50) NOT NULL COMMENT '真实姓名',
  `id_card` varchar(18) DEFAULT NULL COMMENT '身份证号',
  `id_card_front` varchar(255) DEFAULT NULL COMMENT '身份证正面照',
  `id_card_back` varchar(255) DEFAULT NULL COMMENT '身份证背面照',
  `business_license` varchar(50) DEFAULT NULL COMMENT '营业执照号',
  `business_license_img` varchar(255) DEFAULT NULL COMMENT '营业执照图片',
  `status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '认证状态:0-待审核,1-已通过,2-已拒绝',
  `auditor_id` bigint(20) DEFAULT NULL COMMENT '审核员ID',
  `audit_time` datetime DEFAULT NULL COMMENT '审核时间',
  `audit_remark` varchar(500) DEFAULT NULL COMMENT '审核备注',
  `apply_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '申请时间',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_status` (`status`),
  KEY `idx_apply_time` (`apply_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户认证信息表';
```

#### **管理员表 (t_admin)**
```sql
CREATE TABLE `t_admin` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '管理员ID',
  `username` varchar(50) NOT NULL COMMENT '用户名',
  `password` varchar(128) NOT NULL COMMENT '密码(加密)',
  `salt` varchar(32) NOT NULL COMMENT '密码盐值',
  `real_name` varchar(50) NOT NULL COMMENT '真实姓名',
  `phone` varchar(20) DEFAULT NULL COMMENT '手机号',
  `email` varchar(100) DEFAULT NULL COMMENT '邮箱',
  `avatar` varchar(255) DEFAULT NULL COMMENT '头像',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态:0-禁用,1-正常',
  `last_login_time` datetime DEFAULT NULL COMMENT '最后登录时间',
  `last_login_ip` varchar(45) DEFAULT NULL COMMENT '最后登录IP',
  `login_count` int(11) NOT NULL DEFAULT '0' COMMENT '登录次数',
  `creator_id` bigint(20) DEFAULT NULL COMMENT '创建人ID',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `is_deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除:0-否,1-是',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_username` (`username`),
  KEY `idx_status` (`status`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='管理员表';
```

### **商家库 (cizhou_merchant)**

#### **商家基础信息表 (t_merchant)**
```sql
CREATE TABLE `t_merchant` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '商家ID',
  `merchant_no` varchar(32) NOT NULL COMMENT '商家编号',
  `merchant_name` varchar(100) NOT NULL COMMENT '商家名称',
  `business_license` varchar(50) NOT NULL COMMENT '营业执照号',
  `legal_person` varchar(50) NOT NULL COMMENT '法人代表',
  `contact_person` varchar(50) NOT NULL COMMENT '联系人',
  `contact_phone` varchar(20) NOT NULL COMMENT '联系电话',
  `contact_email` varchar(100) DEFAULT NULL COMMENT '联系邮箱',
  `province` varchar(50) NOT NULL COMMENT '省份',
  `city` varchar(50) NOT NULL COMMENT '城市',
  `district` varchar(50) NOT NULL COMMENT '区县',
  `business_address` varchar(255) NOT NULL COMMENT '经营地址',
  `business_scope` text COMMENT '经营范围',
  `business_hours` varchar(100) DEFAULT NULL COMMENT '营业时间',
  `description` text COMMENT '商家描述',
  `logo` varchar(255) DEFAULT NULL COMMENT '商家Logo',
  `images` text COMMENT '商家图片(JSON数组)',
  `status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '状态:0-待审核,1-正常,2-暂停,3-关闭',
  `credit_score` int(3) NOT NULL DEFAULT '100' COMMENT '信用分数',
  `deposit_amount` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '保证金金额',
  `commission_rate` decimal(5,4) NOT NULL DEFAULT '0.0500' COMMENT '佣金费率',
  `settlement_cycle` tinyint(1) NOT NULL DEFAULT '1' COMMENT '结算周期:1-周结,2-月结',
  `bank_name` varchar(100) DEFAULT NULL COMMENT '开户银行',
  `bank_account` varchar(50) DEFAULT NULL COMMENT '银行账号',
  `account_name` varchar(50) DEFAULT NULL COMMENT '账户名称',
  `auditor_id` bigint(20) DEFAULT NULL COMMENT '审核员ID',
  `audit_time` datetime DEFAULT NULL COMMENT '审核时间',
  `audit_remark` varchar(500) DEFAULT NULL COMMENT '审核备注',
  `apply_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '申请时间',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `is_deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除:0-否,1-是',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_merchant_no` (`merchant_no`),
  UNIQUE KEY `uk_business_license` (`business_license`),
  KEY `idx_status` (`status`),
  KEY `idx_city` (`city`),
  KEY `idx_apply_time` (`apply_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商家基础信息表';
```

### **订单库 (cizhou_order)**

#### **订单主表 (t_order)**
```sql
CREATE TABLE `t_order` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '订单ID',
  `order_no` varchar(32) NOT NULL COMMENT '订单号',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `merchant_id` bigint(20) NOT NULL COMMENT '商家ID',
  `service_id` bigint(20) NOT NULL COMMENT '服务ID',
  `service_name` varchar(100) NOT NULL COMMENT '服务名称',
  `service_price` decimal(10,2) NOT NULL COMMENT '服务价格',
  `quantity` int(11) NOT NULL DEFAULT '1' COMMENT '数量',
  `order_amount` decimal(10,2) NOT NULL COMMENT '订单金额',
  `discount_amount` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '优惠金额',
  `actual_amount` decimal(10,2) NOT NULL COMMENT '实付金额',
  `coupon_id` bigint(20) DEFAULT NULL COMMENT '使用的优惠券ID',
  `order_status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '订单状态:1-待支付,2-已支付,3-服务中,4-已完成,5-已取消,6-已退款',
  `payment_status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '支付状态:0-未支付,1-已支付,2-支付失败',
  `payment_method` varchar(20) DEFAULT NULL COMMENT '支付方式',
  `payment_no` varchar(64) DEFAULT NULL COMMENT '支付流水号',
  `remark` varchar(500) DEFAULT NULL COMMENT '订单备注',
  `contact_name` varchar(50) DEFAULT NULL COMMENT '联系人姓名',
  `contact_phone` varchar(20) DEFAULT NULL COMMENT '联系人电话',
  `service_address` varchar(255) DEFAULT NULL COMMENT '服务地址',
  `service_time` datetime DEFAULT NULL COMMENT '服务时间',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `pay_time` datetime DEFAULT NULL COMMENT '支付时间',
  `complete_time` datetime DEFAULT NULL COMMENT '完成时间',
  `cancel_time` datetime DEFAULT NULL COMMENT '取消时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_order_no` (`order_no`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_merchant_id` (`merchant_id`),
  KEY `idx_order_status` (`order_status`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='订单主表';
```

### **内容库 (cizhou_content)**

#### **信息分类表 (t_info_category)**
```sql
CREATE TABLE `t_info_category` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '分类ID',
  `category_name` varchar(50) NOT NULL COMMENT '分类名称',
  `category_code` varchar(50) NOT NULL COMMENT '分类编码',
  `parent_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '父分类ID',
  `level` tinyint(1) NOT NULL DEFAULT '1' COMMENT '分类层级',
  `sort_order` int(11) NOT NULL DEFAULT '0' COMMENT '排序',
  `icon` varchar(255) DEFAULT NULL COMMENT '分类图标',
  `description` varchar(255) DEFAULT NULL COMMENT '分类描述',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态:0-禁用,1-启用',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_category_code` (`category_code`),
  KEY `idx_parent_id` (`parent_id`),
  KEY `idx_level` (`level`),
  KEY `idx_sort_order` (`sort_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='信息分类表';
```

#### **信息内容表 (t_info_content)**
```sql
CREATE TABLE `t_info_content` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '信息ID',
  `info_no` varchar(32) NOT NULL COMMENT '信息编号',
  `category_id` bigint(20) NOT NULL COMMENT '分类ID',
  `user_id` bigint(20) NOT NULL COMMENT '发布用户ID',
  `title` varchar(200) NOT NULL COMMENT '标题',
  `content` text NOT NULL COMMENT '内容',
  `images` text COMMENT '图片列表(JSON数组)',
  `province` varchar(50) DEFAULT NULL COMMENT '省份',
  `city` varchar(50) DEFAULT NULL COMMENT '城市',
  `district` varchar(50) DEFAULT NULL COMMENT '区县',
  `address` varchar(255) DEFAULT NULL COMMENT '详细地址',
  `longitude` decimal(10,6) DEFAULT NULL COMMENT '经度',
  `latitude` decimal(10,6) DEFAULT NULL COMMENT '纬度',
  `contact_name` varchar(50) DEFAULT NULL COMMENT '联系人',
  `contact_phone` varchar(20) DEFAULT NULL COMMENT '联系电话',
  `contact_wechat` varchar(50) DEFAULT NULL COMMENT '微信号',
  `price` decimal(10,2) DEFAULT NULL COMMENT '价格',
  `price_unit` varchar(20) DEFAULT NULL COMMENT '价格单位',
  `tags` varchar(255) DEFAULT NULL COMMENT '标签(逗号分隔)',
  `status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '状态:0-待审核,1-已发布,2-已下架,3-已删除',
  `audit_status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '审核状态:0-待审核,1-审核通过,2-审核拒绝',
  `auditor_id` bigint(20) DEFAULT NULL COMMENT '审核员ID',
  `audit_time` datetime DEFAULT NULL COMMENT '审核时间',
  `audit_remark` varchar(500) DEFAULT NULL COMMENT '审核备注',
  `view_count` int(11) NOT NULL DEFAULT '0' COMMENT '浏览次数',
  `like_count` int(11) NOT NULL DEFAULT '0' COMMENT '点赞次数',
  `comment_count` int(11) NOT NULL DEFAULT '0' COMMENT '评论次数',
  `share_count` int(11) NOT NULL DEFAULT '0' COMMENT '分享次数',
  `is_top` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否置顶:0-否,1-是',
  `is_recommend` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否推荐:0-否,1-是',
  `expire_time` datetime DEFAULT NULL COMMENT '过期时间',
  `publish_time` datetime DEFAULT NULL COMMENT '发布时间',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_info_no` (`info_no`),
  KEY `idx_category_id` (`category_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_status` (`status`),
  KEY `idx_audit_status` (`audit_status`),
  KEY `idx_city` (`city`),
  KEY `idx_publish_time` (`publish_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='信息内容表';
```

### **营销库 (cizhou_marketing)**

#### **活动信息表 (t_activity)**
```sql
CREATE TABLE `t_activity` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '活动ID',
  `activity_no` varchar(32) NOT NULL COMMENT '活动编号',
  `activity_name` varchar(100) NOT NULL COMMENT '活动名称',
  `activity_type` tinyint(1) NOT NULL COMMENT '活动类型:1-签到,2-抽奖,3-拼团,4-秒杀',
  `description` text COMMENT '活动描述',
  `rules` text COMMENT '活动规则(JSON)',
  `start_time` datetime NOT NULL COMMENT '开始时间',
  `end_time` datetime NOT NULL COMMENT '结束时间',
  `status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '状态:0-未开始,1-进行中,2-已结束,3-已取消',
  `participant_count` int(11) NOT NULL DEFAULT '0' COMMENT '参与人数',
  `max_participants` int(11) DEFAULT NULL COMMENT '最大参与人数',
  `banner_image` varchar(255) DEFAULT NULL COMMENT '活动横幅',
  `detail_images` text COMMENT '详情图片(JSON数组)',
  `creator_id` bigint(20) NOT NULL COMMENT '创建人ID',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_activity_no` (`activity_no`),
  KEY `idx_activity_type` (`activity_type`),
  KEY `idx_status` (`status`),
  KEY `idx_start_time` (`start_time`),
  KEY `idx_end_time` (`end_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='活动信息表';
```

## 🔧 **索引优化策略**

### **主键索引**
```yaml
设计原则:
  - 使用自增长整型作为主键
  - 避免使用UUID作为主键
  - 主键长度尽可能短
  - 主键值不可修改
```

### **唯一索引**
```yaml
业务唯一性约束:
  - 用户手机号唯一索引
  - 商家营业执照号唯一索引
  - 订单号唯一索引
  - 活动编号唯一索引
```

### **复合索引**
```yaml
查询优化索引:
  - (user_id, create_time) 用户时间范围查询
  - (merchant_id, status) 商家状态查询
  - (category_id, status, publish_time) 内容列表查询
  - (activity_id, user_id) 活动参与查询
```

### **覆盖索引**
```yaml
减少回表查询:
  - 用户列表查询覆盖索引
  - 订单列表查询覆盖索引
  - 商家列表查询覆盖索引
```

## 📈 **分区表设计**

### **按时间分区**
```sql
-- 订单表按月分区
CREATE TABLE `t_order` (
  -- 字段定义...
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
PARTITION BY RANGE (YEAR(create_time)*100 + MONTH(create_time)) (
  PARTITION p202401 VALUES LESS THAN (202402),
  PARTITION p202402 VALUES LESS THAN (202403),
  PARTITION p202403 VALUES LESS THAN (202404),
  -- 继续添加分区...
  PARTITION p_max VALUES LESS THAN MAXVALUE
);
```

### **按哈希分区**
```sql
-- 用户表按用户ID哈希分区
CREATE TABLE `t_user` (
  -- 字段定义...
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
PARTITION BY HASH(id) PARTITIONS 16;
```

## 🛡️ **数据安全设计**

### **敏感数据加密**
```yaml
字段级加密:
  - 用户密码: BCrypt加密
  - 身份证号: AES加密存储
  - 银行账号: AES加密存储
  - 手机号: 部分脱敏显示

传输加密:
  - 数据库连接SSL加密
  - API接口HTTPS传输
  - 敏感参数加密传输
```

### **数据备份策略**
```yaml
备份方案:
  - 全量备份: 每日凌晨执行
  - 增量备份: 每小时执行
  - 日志备份: 实时备份
  - 异地备份: 每日同步

恢复策略:
  - 点对点恢复: 支持任意时间点恢复
  - 表级恢复: 支持单表数据恢复
  - 库级恢复: 支持整库恢复
  - 灾难恢复: 异地快速恢复
```

这个数据库设计方案为后台管理系统提供了完整的数据存储解决方案，确保数据的安全性、完整性和高性能。
