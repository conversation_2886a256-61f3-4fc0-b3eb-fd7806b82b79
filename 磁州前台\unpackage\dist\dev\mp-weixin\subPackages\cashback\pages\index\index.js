"use strict";
const common_vendor = require("../../../../common/vendor.js");
const common_assets = require("../../../../common/assets.js");
const CustomNavbar = () => "../../components/CustomNavbar.js";
const ProductCard = () => "../../components/ProductCard.js";
const _sfc_main = {
  components: {
    CustomNavbar,
    ProductCard
  },
  data() {
    return {
      platforms: [
        { id: 1, name: "淘宝", icon: "/static/images/cashback/platform-taobao.png" },
        { id: 2, name: "京东", icon: "/static/images/cashback/platform-jd.png" },
        { id: 3, name: "拼多多", icon: "/static/images/cashback/platform-pdd.png" },
        { id: 4, name: "唯品会", icon: "/static/images/cashback/platform-vip.png" },
        { id: 5, name: "抖音", icon: "/static/images/cashback/platform-douyin.png" },
        { id: 6, name: "天猫", icon: "/static/images/cashback/platform-tmall.png" },
        { id: 7, name: "苏宁", icon: "/static/images/cashback/platform-suning.png" },
        { id: 8, name: "小红书", icon: "/static/images/cashback/platform-xiaohongshu.png" }
      ],
      products: [
        {
          id: 1,
          title: "Apple iPhone 15 Pro Max (A2850) 256GB 原色钛金属",
          image: "/static/images/cashback/product-1.png",
          price: 9999,
          cashback: 300,
          platform: "京东"
        },
        {
          id: 2,
          title: "HUAWEI Mate 60 Pro 12+512GB 雅黑色",
          image: "/static/images/cashback/product-2.png",
          price: 6999,
          cashback: 200,
          platform: "华为商城"
        },
        {
          id: 3,
          title: "小米14 Ultra 16+1T 黑色 徕卡光学",
          image: "/static/images/cashback/product-3.png",
          price: 7999,
          cashback: 240,
          platform: "小米商城"
        },
        {
          id: 4,
          title: "OPPO Find X7 Ultra 16+512GB 棕色",
          image: "/static/images/cashback/product-4.png",
          price: 6999,
          cashback: 210,
          platform: "京东"
        }
      ],
      clipboardContent: "",
      hasDetectedLink: false,
      detectedPlatform: ""
    };
  },
  onLoad() {
    common_vendor.index.setNavigationBarColor({
      frontColor: "#ffffff",
      backgroundColor: "#9C27B0"
    });
    setTimeout(() => {
      this.checkClipboard();
    }, 500);
  },
  onShow() {
    setTimeout(() => {
      this.checkClipboard();
    }, 500);
  },
  methods: {
    navigateToSearch() {
      common_vendor.index.navigateTo({
        url: "/subPackages/cashback/pages/search/index"
      });
    },
    navigateToPlatforms() {
      common_vendor.index.navigateTo({
        url: "/subPackages/cashback/pages/platforms/index"
      });
    },
    navigateToPlatformDetail(platform) {
      common_vendor.index.navigateTo({
        url: `/subPackages/cashback/pages/platform-detail/index?id=${platform.id}&name=${platform.name}`
      });
    },
    navigateToCategory() {
      common_vendor.index.navigateTo({
        url: "/subPackages/cashback/pages/category/index"
      });
    },
    navigateToDetail(product) {
      common_vendor.index.navigateTo({
        url: `/subPackages/cashback/pages/detail/index?id=${product.id}`
      });
    },
    // 请求剪贴板权限并禁用提示
    requestClipboardPermission() {
    },
    // 检测剪贴板内容
    checkClipboard() {
      common_vendor.index.getClipboardData({
        success: (res) => {
          if (res.data && this.isProductLink(res.data)) {
            this.clipboardContent = res.data;
            this.hasDetectedLink = true;
            this.detectedPlatform = this.detectPlatform(res.data);
            common_vendor.index.showToast({
              title: `检测到${this.detectedPlatform}商品链接`,
              icon: "none",
              duration: 2e3
            });
          }
        }
      });
    },
    isProductLink(link) {
      const patterns = [
        /taobao\.com/i,
        /tmall\.com/i,
        /jd\.com/i,
        /pinduoduo\.com/i,
        /yangkeduo\.com/i,
        /vip\.com/i,
        /suning\.com/i,
        /kaola\.com/i,
        /tb\.cn/i,
        /m\.tb\.cn/i,
        /t\.cn/i,
        /dwz\.cn/i,
        /douyin\.com/i
      ];
      return patterns.some((pattern) => pattern.test(link));
    },
    detectPlatform(link) {
      if (/taobao\.com/i.test(link) || /tb\.cn/i.test(link) || /m\.tb\.cn/i.test(link)) {
        return "淘宝";
      } else if (/tmall\.com/i.test(link)) {
        return "天猫";
      } else if (/jd\.com/i.test(link)) {
        return "京东";
      } else if (/pinduoduo\.com/i.test(link) || /yangkeduo\.com/i.test(link)) {
        return "拼多多";
      } else if (/douyin\.com/i.test(link)) {
        return "抖音";
      } else if (/vip\.com/i.test(link)) {
        return "唯品会";
      } else if (/suning\.com/i.test(link)) {
        return "苏宁";
      } else if (/kaola\.com/i.test(link)) {
        return "考拉";
      } else {
        return "电商";
      }
    },
    pasteLink() {
      if (this.hasDetectedLink && this.clipboardContent) {
        this.processLink(this.clipboardContent);
      } else {
        common_vendor.index.getClipboardData({
          success: (res) => {
            if (res.data) {
              this.processLink(res.data);
            } else {
              common_vendor.index.showToast({
                title: "剪贴板为空",
                icon: "none"
              });
            }
          },
          fail: () => {
            common_vendor.index.showToast({
              title: "获取剪贴板失败",
              icon: "none"
            });
          }
        });
      }
    },
    processLink(link) {
      common_vendor.index.showLoading({
        title: "解析链接中..."
      });
      setTimeout(() => {
        common_vendor.index.hideLoading();
        this.clipboardContent = "";
        this.hasDetectedLink = false;
        common_vendor.index.navigateTo({
          url: `/subPackages/cashback/pages/product-detail/index?link=${encodeURIComponent(link)}`
        });
      }, 1e3);
    },
    showTutorial() {
      common_vendor.index.showToast({
        title: "使用教程功能正在开发中",
        icon: "none",
        duration: 2e3
      });
    },
    navigateToLifeCashback() {
      common_vendor.index.navigateTo({
        url: "/subPackages/cashback/pages/life-cashback/index"
      });
    },
    navigateToLifeService(type) {
      common_vendor.index.navigateTo({
        url: `/subPackages/cashback/pages/life-service/index?type=${type}`
      });
    }
  }
};
const __injectCSSVars__ = () => {
  common_vendor.useCssVars((_ctx) => ({
    "009dcc79": _ctx.hasDetectedLink ? "block" : "none"
  }));
};
const __setup__ = _sfc_main.setup;
_sfc_main.setup = __setup__ ? (props, ctx) => {
  __injectCSSVars__();
  return __setup__(props, ctx);
} : __injectCSSVars__;
if (!Array) {
  const _component_custom_navbar = common_vendor.resolveComponent("custom-navbar");
  const _component_path = common_vendor.resolveComponent("path");
  const _component_svg = common_vendor.resolveComponent("svg");
  const _component_product_card = common_vendor.resolveComponent("product-card");
  (_component_custom_navbar + _component_path + _component_svg + _component_product_card)();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {
    a: common_vendor.p({
      title: "返利商城",
      ["show-close"]: true
    }),
    b: common_vendor.p({
      fill: "#999999",
      d: "M9.5,3A6.5,6.5 0 0,1 16,9.5C16,11.11 15.41,12.59 14.44,13.73L14.71,14H15.5L20.5,19L19,20.5L14,15.5V14.71L13.73,14.44C12.59,15.41 11.11,16 9.5,16A6.5,6.5 0 0,1 3,9.5A6.5,6.5 0 0,1 9.5,3M9.5,5C7,5 5,7 5,9.5C5,12 7,14 9.5,14C12,14 14,12 14,9.5C14,7 12,5 9.5,5Z"
    }),
    c: common_vendor.p({
      viewBox: "0 0 24 24",
      width: "20",
      height: "20"
    }),
    d: common_vendor.o((...args) => $options.navigateToSearch && $options.navigateToSearch(...args)),
    e: common_vendor.o((...args) => $options.showTutorial && $options.showTutorial(...args)),
    f: common_assets._imports_0$52,
    g: common_assets._imports_1$49,
    h: common_assets._imports_2$44,
    i: common_assets._imports_3$37,
    j: common_vendor.o((...args) => $options.pasteLink && $options.pasteLink(...args)),
    k: common_vendor.p({
      fill: "#999999",
      d: "M8.59,16.58L13.17,12L8.59,7.41L10,6L16,12L10,18L8.59,16.58Z"
    }),
    l: common_vendor.p({
      viewBox: "0 0 24 24",
      width: "16",
      height: "16"
    }),
    m: common_vendor.o((...args) => $options.navigateToPlatforms && $options.navigateToPlatforms(...args)),
    n: common_vendor.f($data.platforms, (platform, index, i0) => {
      return {
        a: platform.icon,
        b: common_vendor.t(platform.name),
        c: index,
        d: common_vendor.o(($event) => $options.navigateToPlatformDetail(platform), index)
      };
    }),
    o: common_vendor.p({
      fill: "#999999",
      d: "M8.59,16.58L13.17,12L8.59,7.41L10,6L16,12L10,18L8.59,16.58Z"
    }),
    p: common_vendor.p({
      viewBox: "0 0 24 24",
      width: "16",
      height: "16"
    }),
    q: common_vendor.o((...args) => $options.navigateToLifeCashback && $options.navigateToLifeCashback(...args)),
    r: common_vendor.p({
      fill: "#FF6B6B",
      d: "M15.5,21L14,8H16.23L15.1,3.46L16.84,3L18.09,8H22L20.5,21H15.5M5,11H10A3,3 0 0,1 13,14H2A3,3 0 0,1 5,11M13,18A3,3 0 0,1 10,21H5A3,3 0 0,1 2,18H13M3,15H8L9.5,16.5L11,15H12A1,1 0 0,1 13,16A1,1 0 0,1 12,17H3A1,1 0 0,1 2,16A1,1 0 0,1 3,15Z"
    }),
    s: common_vendor.p({
      viewBox: "0 0 24 24",
      width: "24",
      height: "24"
    }),
    t: common_vendor.o(($event) => $options.navigateToLifeService("takeout")),
    v: common_vendor.p({
      fill: "#FFA726",
      d: "M5,11L6.5,6.5H17.5L19,11M17.5,16A1.5,1.5 0 0,1 16,14.5A1.5,1.5 0 0,1 17.5,13A1.5,1.5 0 0,1 19,14.5A1.5,1.5 0 0,1 17.5,16M6.5,16A1.5,1.5 0 0,1 5,14.5A1.5,1.5 0 0,1 6.5,13A1.5,1.5 0 0,1 8,14.5A1.5,1.5 0 0,1 6.5,16M18.92,6C18.72,5.42 18.16,5 17.5,5H6.5C5.84,5 5.28,5.42 5.08,6L3,12V20A1,1 0 0,0 4,21H5A1,1 0 0,0 6,20V19H18V20A1,1 0 0,0 19,21H20A1,1 0 0,0 21,20V12L18.92,6Z"
    }),
    w: common_vendor.p({
      viewBox: "0 0 24 24",
      width: "24",
      height: "24"
    }),
    x: common_vendor.o(($event) => $options.navigateToLifeService("taxi")),
    y: common_vendor.p({
      fill: "#E91E63",
      d: "M18,9H16V7H18M18,13H16V11H18M18,17H16V15H18M8,9H6V7H8M8,13H6V11H8M8,17H6V15H8M18,3V5H16V3H8V5H6V3H4V21H6V19H8V21H16V19H18V21H20V3H18Z"
    }),
    z: common_vendor.p({
      viewBox: "0 0 24 24",
      width: "24",
      height: "24"
    }),
    A: common_vendor.o(($event) => $options.navigateToLifeService("movie")),
    B: common_vendor.p({
      fill: "#2196F3",
      d: "M3,14H5V20H19V14H21V21A1,1 0 0,1 20,22H4A1,1 0 0,1 3,21V14M17,4H7V2H17V4M17.5,5L12,10.5L6.5,5H17.5M20,6.4L17.9,8.5L15.5,6.1L16.9,4.7L20,7.8V6.4M5.93,4.7L7.33,6.1L4.93,8.5L2.83,6.4V7.8L5.93,4.7Z"
    }),
    C: common_vendor.p({
      viewBox: "0 0 24 24",
      width: "24",
      height: "24"
    }),
    D: common_vendor.o(($event) => $options.navigateToLifeService("express")),
    E: common_assets._imports_4$27,
    F: common_vendor.o(($event) => $options.navigateToLifeService("coupon")),
    G: common_vendor.p({
      fill: "#4CAF50",
      d: "M12,8H4A2,2 0 0,0 2,10V14A2,2 0 0,0 4,16H5V20A1,1 0 0,0 6,21H8A1,1 0 0,0 9,20V16H12L17,20V4L12,8M21.5,12C21.5,13.71 20.54,15.26 19,16V8C20.53,8.75 21.5,10.3 21.5,12Z"
    }),
    H: common_vendor.p({
      viewBox: "0 0 24 24",
      width: "24",
      height: "24"
    }),
    I: common_vendor.o(($event) => $options.navigateToLifeService("vip")),
    J: common_assets._imports_5$24,
    K: common_vendor.p({
      fill: "#999999",
      d: "M8.59,16.58L13.17,12L8.59,7.41L10,6L16,12L10,18L8.59,16.58Z"
    }),
    L: common_vendor.p({
      viewBox: "0 0 24 24",
      width: "16",
      height: "16"
    }),
    M: common_vendor.o((...args) => $options.navigateToCategory && $options.navigateToCategory(...args)),
    N: common_vendor.f($data.products, (product, index, i0) => {
      return {
        a: index,
        b: common_vendor.o(($event) => $options.navigateToDetail(product), index),
        c: "3d430b98-19-" + i0,
        d: common_vendor.p({
          product
        })
      };
    }),
    O: common_vendor.s(_ctx.__cssVars())
  };
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-3d430b98"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../../.sourcemap/mp-weixin/subPackages/cashback/pages/index/index.js.map
