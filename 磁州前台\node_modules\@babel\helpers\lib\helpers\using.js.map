{"version": 3, "names": ["_using", "stack", "value", "isAwait", "Object", "TypeError", "dispose", "Symbol", "asyncDispose", "for", "push", "v", "d", "a"], "sources": ["../../src/helpers/using.js"], "sourcesContent": ["/* @minVersion 7.22.0 */\n/* @onlyBabel7 */\n\nexport default function _using(stack, value, isAwait) {\n  if (value === null || value === void 0) return value;\n  if (Object(value) !== value) {\n    throw new TypeError(\n      \"using declarations can only be used with objects, functions, null, or undefined.\",\n    );\n  }\n  // core-js-pure uses Symbol.for for polyfilling well-known symbols\n  if (isAwait) {\n    var dispose =\n      value[Symbol.asyncDispose || Symbol.for(\"Symbol.asyncDispose\")];\n  }\n  if (dispose === null || dispose === void 0) {\n    dispose = value[Symbol.dispose || Symbol.for(\"Symbol.dispose\")];\n  }\n  if (typeof dispose !== \"function\") {\n    throw new TypeError(`Property [Symbol.dispose] is not a function.`);\n  }\n  stack.push({ v: value, d: dispose, a: isAwait });\n  return value;\n}\n"], "mappings": ";;;;;;AAGe,SAASA,MAAMA,CAACC,KAAK,EAAEC,KAAK,EAAEC,OAAO,EAAE;EACpD,IAAID,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,KAAK,CAAC,EAAE,OAAOA,KAAK;EACpD,IAAIE,MAAM,CAACF,KAAK,CAAC,KAAKA,KAAK,EAAE;IAC3B,MAAM,IAAIG,SAAS,CACjB,kFACF,CAAC;EACH;EAEA,IAAIF,OAAO,EAAE;IACX,IAAIG,OAAO,GACTJ,KAAK,CAACK,MAAM,CAACC,YAAY,IAAID,MAAM,CAACE,GAAG,CAAC,qBAAqB,CAAC,CAAC;EACnE;EACA,IAAIH,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,EAAE;IAC1CA,OAAO,GAAGJ,KAAK,CAACK,MAAM,CAACD,OAAO,IAAIC,MAAM,CAACE,GAAG,CAAC,gBAAgB,CAAC,CAAC;EACjE;EACA,IAAI,OAAOH,OAAO,KAAK,UAAU,EAAE;IACjC,MAAM,IAAID,SAAS,CAAC,8CAA8C,CAAC;EACrE;EACAJ,KAAK,CAACS,IAAI,CAAC;IAAEC,CAAC,EAAET,KAAK;IAAEU,CAAC,EAAEN,OAAO;IAAEO,CAAC,EAAEV;EAAQ,CAAC,CAAC;EAChD,OAAOD,KAAK;AACd", "ignoreList": []}