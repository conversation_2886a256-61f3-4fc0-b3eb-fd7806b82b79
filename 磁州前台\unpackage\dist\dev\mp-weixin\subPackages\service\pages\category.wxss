
.service-category-container {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-bottom: 30rpx;
}
.custom-navbar {
  display: flex;
  align-items: center;
  height: 44px;
  background-color: #1677FF;
  padding-left: 10px;
  padding-right: 10px;
  position: relative;
  z-index: 100;
}
.back-btn {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.back-icon {
  width: 12px;
  height: 12px;
  border-top: 2px solid #fff;
  border-left: 2px solid #fff;
  transform: rotate(-45deg);
}
.navbar-title {
  flex: 1;
  text-align: center;
  color: #fff;
  font-size: 18px;
  font-weight: 500;
}
.navbar-right {
  width: 40px;
  height: 40px;
}
.service-tip {
  padding: 30rpx;
  background-color: #fff;
}
.tip-text {
  font-size: 36rpx;
  font-weight: 500;
  color: #333;
  display: block;
  margin-bottom: 10rpx;
}
.tip-desc {
  font-size: 28rpx;
  color: #999;
}
.service-grid {
  display: flex;
  flex-wrap: wrap;
  padding: 20rpx;
  background-color: #fff;
  margin-top: 20rpx;
}
.service-item {
  width: 20%;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 40rpx;
}
.service-icon-wrapper {
  width: 140rpx;
  height: 140rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 16rpx;
}
.service-icon {
  width: 86rpx;
  height: 86rpx;
  object-fit: contain;
}
.service-name {
  font-size: 26rpx;
  color: #333;
  text-align: center;
}
