/**
 * 拼车推广混入
 * 为拼车详情页提供推广能力
 */
import basePromotionMixin from './basePromotionMixin';

export default {
  mixins: [basePromotionMixin],

  data() {
    return {
      // 设置页面类型为拼车
      pageType: 'carpool'
    };
  },

  methods: {
    /**
     * 重写：判断当前用户是否是内容所有者
     */
    isContentOwner() {
      // 获取当前用户ID
      const currentUserId = this.$store?.state?.user?.userId || '';
      // 获取拼车发布者ID
      const publisherId = this.carpoolInfo?.publisherId || this.carpool?.publisherId || '';

      // 判断当前用户是否是拼车发布者
      return currentUserId && publisherId && currentUserId === publisherId;
    },

    /**
     * 重写：判断当前内容是否支持佣金
     */
    isCommissionContent() {
      // 拼车通常不支持佣金，除非特殊设置
      const canDistribute = this.carpoolInfo?.canDistribute || this.carpool?.canDistribute || false;
      
      return canDistribute;
    },

    /**
     * 重写：生成推广数据
     */
    generatePromotionData() {
      // 获取拼车数据
      const carpool = this.carpoolInfo || this.carpool || {};
      
      // 构建推广数据
      this.promotionData = {
        id: carpool.id || '',
        title: `${carpool.departure || ''}到${carpool.destination || ''}拼车`,
        image: carpool.image || '/static/images/carpool-default.jpg',
        departure: carpool.departure || '',
        destination: carpool.destination || '',
        departureTime: carpool.departureTime || '',
        seats: carpool.seats || 0,
        price: carpool.price || 0,
        // 如果有更多拼车特定字段，可以在这里添加
      };
    },

    /**
     * 显示拼车推广浮层
     */
    showCarpoolPromotion() {
      // 如果没有推广权限，显示提示
      if (!this.hasPromotionPermission) {
        uni.showToast({
          title: '暂无推广权限',
          icon: 'none'
        });
        return;
      }

      // 打开推广工具
      this.openPromotionTools();
    }
  }
}; 