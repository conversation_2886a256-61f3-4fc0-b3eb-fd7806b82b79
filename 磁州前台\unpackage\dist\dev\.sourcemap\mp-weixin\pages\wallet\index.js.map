{"version": 3, "file": "index.js", "sources": ["pages/wallet/index.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvd2FsbGV0L2luZGV4LnZ1ZQ"], "sourcesContent": ["<template>\r\n  <wallet-page \r\n    scene=\"main\" \r\n    path-prefix=\"/pages/wallet\">\r\n  </wallet-page>\r\n</template>\r\n\r\n<script setup>\r\nimport WalletPage from '@/components/wallet/WalletPage.vue';\r\n</script>\r\n\r\n<style>\r\n/* 页面样式已移至组件中 */\r\n</style> ", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/pages/wallet/index.vue'\nwx.createPage(MiniProgramPage)"], "names": ["MiniProgramPage"], "mappings": ";;;;;AAQA,MAAM,aAAa,MAAW;;;;;;;;;;;;;;ACP9B,GAAG,WAAWA,SAAe;"}