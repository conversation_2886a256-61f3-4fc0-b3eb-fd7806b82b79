<view class="user-auth"><block wx:if="{{a}}"><button wx:if="{{b}}" class="auth-btn" open-type="chooseAvatar" bindchooseavatar="{{d}}"><text class="auth-btn-text">{{c}}</text></button><slot wx:else></slot></block><block wx:else><button wx:if="{{e}}" class="auth-btn" bindtap="{{g}}"><text class="auth-btn-text">{{f}}</text></button><slot wx:else></slot></block><view wx:if="{{h}}" class="nickname-popup"><view class="nickname-container"><view class="nickname-header"><text class="nickname-title">设置昵称</text></view><view class="nickname-content"><text class="nickname-tip">请设置您的昵称</text><input class="nickname-input" placeholder="请输入昵称" maxlength="12" value="{{i}}" bindinput="{{j}}"/></view><view class="nickname-footer"><button class="cancel-btn" bindtap="{{k}}">取消</button><button class="confirm-btn" bindtap="{{l}}">确定</button></view></view></view></view>