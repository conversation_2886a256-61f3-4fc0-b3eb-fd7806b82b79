# 磁州生活网推广工具系统

磁州生活网推广工具系统是一套通用的内容分享与推广解决方案，可以轻松集成到平台的各个业务模块中，包括但不限于：商家店铺、商品、拼车、房产、服务、社区信息等。

## 功能特点

- **多业务支持**：支持商家、商品、拼车、房产等多种业务类型的推广
- **多种推广方式**：支持生成分享链接、小程序码、海报等多种推广方式
- **佣金分销**：支持分销员推广赚取佣金
- **数据统计**：记录分享、浏览、转化等数据
- **权限控制**：根据用户角色和内容所有权控制推广权限

## 系统架构

推广工具系统采用以下架构设计：

1. **分包设计**：
   - 通用推广工具：`subPackages/promotion/`
   - 商家营销推广工具：`subPackages/merchant-admin-marketing/pages/marketing/distribution/`

2. **组件复用**：
   - 推广混入文件：`mixins/basePromotionMixin.js` 等
   - 推广按钮组件：`components/PromotionToolButton.vue`
   - 悬浮推广按钮：`components/FloatPromotionButton.vue`

3. **服务层**：
   - 推广服务：`utils/promotionService.js`

## 代码结构详解

### 核心目录结构

```
├── components/                  # 组件目录
│   ├── FloatPromotionButton.vue # 悬浮推广按钮组件
│   ├── PromotionToolButton.vue  # 推广工具按钮组件
│   └── index/                   # 首页相关组件
│       ├── Banner.vue           # 轮播图组件
│       ├── InfoList.vue         # 信息列表组件(43KB)
│       ├── MerchantRecommend.vue # 商家推荐组件
│       └── ServiceGrid.vue      # 服务网格组件
├── mixins/                      # 混入文件目录
│   ├── basePromotionMixin.js    # 基础推广混入(7.6KB)
│   ├── carpoolPromotionMixin.js # 拼车推广混入
│   ├── merchantPromotionMixin.js # 商家推广混入
│   ├── productPromotionMixin.js # 商品推广混入
│   └── ...                      # 其他业务类型推广混入
├── pages/                       # 页面目录
├── services/                    # 服务层目录
│   ├── platformPromotionService.js # 平台级推广服务(4.7KB)
│   └── AdPaymentService.js      # 广告支付服务
├── store/                       # 状态管理
│   └── index.js                 # Vuex状态管理(841B)
├── subPackages/                 # 分包目录
│   ├── distribution/            # 分销分包
│   │   └── pages/               # 分销相关页面
│   │       ├── apply.vue        # 申请分销员页面(15KB)
│   │       ├── commission.vue   # 佣金明细页面(12KB)
│   │       ├── index.vue        # 分销中心页面(16KB)
│   │       ├── products.vue     # 可分销商品页面(15KB)
│   │       ├── promotion.vue    # 推广工具页面(14KB)
│   │       ├── team.vue         # 团队管理页面(15KB)
│   │       └── withdraw.vue     # 佣金提现页面(20KB)
│   └── promotion/               # 推广分包
│       └── pages/               # 推广相关页面
│           ├── promotion-tool.vue # 推广工具页面(26KB)
│           └── qrcode.vue       # 二维码生成页面(7.1KB)
├── utils/                       # 工具类目录
│   ├── cashbackService.js       # 返利服务(18KB)
│   ├── distributionService.js   # 分销服务(22KB)
│   ├── promotionService.js      # 推广服务(16KB)
│   ├── recommendationService.js # 推荐服务(15KB)
│   ├── paymentService.js        # 支付服务(11KB)
│   └── ...                      # 其他工具类
└── App.vue                      # 应用入口(14KB)
```

### 核心服务文件详解

#### 1. promotionService.js (推广服务)

推广服务是整个推广工具系统的核心，提供以下功能：

- **showPromotionTools(type, data)**: 打开推广工具页面，支持各种业务类型
- **generateShareLink(type, id, userId)**: 生成分享链接，支持添加推广用户ID
- **generateQrcode(type, id, userId)**: 生成小程序码，通过调用云函数实现
- **generatePoster(type, data, userId, options)**: 生成推广海报，支持自定义主题
- **savePosterToAlbum(filePath)**: 保存海报到相册，处理用户授权
- **recordPromotion(type, id, method)**: 记录推广行为，用于数据统计

内部实现了多种辅助方法：
- **_getPagePath(type)**: 根据内容类型获取对应的小程序页面路径
- **_truncateText(text, maxLength)**: 截断文本，用于海报生成
- **_drawWrappedText(ctx, text, x, y, maxWidth, lineHeight, maxLines)**: 绘制自动换行文本
- **_darkenColor(hex, percent)** / **_lightenColor(hex, percent)**: 颜色处理工具方法

示例代码:
```js
// 打开推广工具
promotionService.showPromotionTools('product', {
  id: '123',
  title: '商品名称',
  price: '99.00'
});

// 生成分享链接
const shareLink = promotionService.generateShareLink('merchant', '456', 'user789');
```

#### 2. distributionService.js (分销服务)

分销服务处理分销员管理、佣金计算等功能，是一个完整的分销系统实现：

- **isDistributor(options)**: 检查用户是否是分销员
- **getDistributorInfo(options)**: 获取分销员信息
- **applyDistributor(data)**: 申请成为分销员
- **getCommissionList(params)**: 获取佣金明细
- **getTeamMembers(params)**: 获取团队成员
- **withdrawCommission(data)**: 提交佣金提现申请
- **getDistributionConfig()**: 获取分销配置
- **getDistributionPoster(params)**: 获取分销海报
- **applyMerchantDistributor(data)**: 商家申请成为分销商
- **getDistributionLevels()**: 获取分销等级设置
- **getCommissionRules()**: 获取佣金规则
- **getDistributorsList(params)**: 获取分销员列表
- **reviewDistributorApplication(data)**: 审核分销员申请
- **getDistributionStats(params)**: 获取分销统计数据
- **getWithdrawalSettings()**: 获取提现设置
- **generatePromotionLink(options)**: 生成推广链接
- **formatCommission(amount)**: 格式化佣金金额

#### 3. cashbackService.js (返利服务)

返利服务是一个完整的电商返利系统实现，处理电商平台对接、返利计算等功能：

- **getPlatforms()**: 获取支持的电商平台列表
- **getHomeData()**: 获取返利系统首页数据
- **searchProducts(params)**: 搜索商品
- **getProductDetail(productId, platform)**: 获取商品详情
- **getProductPriceHistory(params)**: 获取商品价格历史
- **generateBuyLink(productId, platform)**: 生成购买链接
- **getOrderList(params)**: 获取订单列表
- **getOrderDetail(orderId)**: 获取订单详情
- **getWalletInfo()**: 获取钱包信息
- **getWalletTransactions(params)**: 获取钱包交易记录
- **withdrawCashback(params)**: 提现返利
- **getTeamOverview()**: 获取团队概览
- **generatePoster()**: 生成推广海报
- **getPlatformCoupons(platform)**: 获取平台优惠券
- **receiveCoupon(couponId, platform)**: 领取优惠券
- **bindPlatform(params)**: 绑定电商平台账号
- **getBoundPlatforms()**: 获取已绑定的平台
- **syncPlatformOrders(platformId)**: 同步平台订单
- **getDistributorLevel()**: 获取分销员等级
- **applyDistributor(params)**: 申请成为分销员

#### 4. platformPromotionService.js (平台级推广服务)

平台级推广服务提供全平台通用的推广能力和配置：

- **getGlobalConfig()**: 获取全局推广配置
- **isModuleEnabled(moduleType)**: 检查特定业务模块推广工具是否启用
- **getUserPromotionPermissions()**: 获取当前用户的推广权限
- **getContentPromotionConfig(contentType, contentId)**: 获取内容的推广配置
- **logPromotionGeneration(params)**: 记录推广内容生成事件
- **logPromotionSharing(params)**: 记录推广内容分享事件
- **getUserPromotionStats()**: 获取用户的推广数据统计
- **getPromotionTemplates(type)**: 获取推广模板列表
- **validatePromotionEligibility(type, id)**: 验证内容推广资格

### 混入文件详解

#### basePromotionMixin.js (基础推广混入)

基础推广混入提供所有业务类型共用的推广方法：

- **initPromotion()**: 初始化推广能力
- **checkAndEnablePromotion()**: 检查并启用推广功能
- **addFloatPromotionButton()**: 添加悬浮推广按钮
- **detectPageType()**: 检测页面类型
- **checkPromotionPermission()**: 检查用户推广权限
- **isContentOwner()**: 判断当前用户是否是内容所有者
- **isDistributor()**: 判断当前用户是否是分销员
- **isCommissionContent()**: 判断内容是否支持分销佣金
- **generatePromotionData()**: 生成推广数据
- **openPromotionTools()**: 打开推广工具
- **showPromotion()**: 显示推广选项

混入文件通过自动检测页面类型和内容，为页面添加推广功能，实现了推广能力的即插即用。

#### 业务特定混入 (如productPromotionMixin.js)

继承基础混入，并提供业务特定的推广逻辑：

- **getProductInfo()**: 获取商品信息
- **generateProductPoster()**: 生成商品海报
- **calculateCommission()**: 计算商品佣金
- **isContentOwner()**: 重写判断商品所有者的方法
- **isCommissionContent()**: 重写判断商品是否支持佣金的方法

### 状态管理分析

项目使用Vuex进行状态管理，主要管理以下状态：

```js
// store/index.js
state: {
  user: null,        // 用户信息
  token: null,       // 用户令牌
  distributorInfo: null  // 分销员信息
}
```

提供以下getter：
- **user**: 获取用户信息
- **token**: 获取用户令牌
- **distributorInfo**: 获取分销员信息
- **isDistributor**: 判断是否是分销员

通过这些状态，各个组件可以共享用户信息和分销员信息，实现权限控制和数据共享。

### 分销中心页面分析

分销中心页面(`subPackages/distribution/pages/index.vue`)是分销系统的核心页面：

- **用户信息卡片**: 显示用户基本信息和分销员等级
- **数据概览卡片**: 展示累计佣金、可提现、待结算、已提现等数据
- **功能菜单**: 提供分销商品、我的团队、推广工具、佣金明细等功能入口
- **推荐商品卡片**: 展示推荐分销商品
- **分销员申请引导**: 对非分销员用户展示成为分销员的好处和申请入口

页面通过调用`distributionService`获取分销员信息和推荐商品数据，实现了分销中心的核心功能。

### 数据流分析

系统数据流转路径：

1. **用户行为触发**:
   - 用户点击推广按钮 → 调用`showPromotion()`方法 → 打开推广工具页面
   - 用户生成推广内容 → 调用`logPromotionGeneration()`记录事件
   - 用户分享内容 → 调用`logPromotionSharing()`记录事件

2. **推广转化流程**:
   - 用户A分享内容 → 用户B点击链接访问 → 系统记录访问来源
   - 用户B产生转化行为(如购买) → 系统记录转化并计算佣金
   - 佣金结算 → 更新用户A的分销员账户

3. **分销数据流**:
   - 申请分销员 → 审核通过 → 获取分销资格
   - 推广商品 → 产生订单 → 计算佣金 → 佣金结算
   - 发展团队 → 团队成员推广 → 获得团队佣金

4. **返利数据流**:
   - 搜索商品 → 生成购买链接 → 用户购买
   - 平台同步订单 → 计算返利 → 返利结算
   - 用户提现 → 审核通过 → 资金到账

## 返利系统集成方案

为了将全平台返利系统与现有的磁州生活网推广工具系统集成，我们设计了以下方案：

### 1. 系统架构整合

将返利系统作为推广工具系统的扩展模块，复用现有的分销体系和用户系统。

```mermaid
flowchart TB
    A[用户层] --> B[应用层]
    B --> C[服务层]
    C --> D[数据层]
    
    subgraph "应用层"
    B1[推广工具] --- B2[分销系统]
    B2 --- B3[返利系统]
    end
    
    subgraph "服务层"
    C1[推广服务] --- C2[分销服务]
    C2 --- C3[返利服务]
    C3 --- C4[订单同步服务]
    end
```

### 2. 代码集成方案

1. **添加返利分包**:
   - 在 `subPackages` 目录下创建 `cashback` 分包
   - 添加返利相关页面：商品搜索、商品详情、订单管理等

2. **扩展现有服务**:
   - 已实现 `cashbackService.js` 提供返利相关API
   - 扩展 `distributionService.js` 支持电商平台分销

3. **UI组件复用**:
   - 复用现有的 `PromotionToolButton` 和 `FloatPromotionButton`
   - 为返利场景添加特定样式和行为

### 3. 数据流转

1. **用户浏览商品** → 记录用户行为 → 推荐系统提供个性化推荐
2. **用户购买商品** → 订单同步服务捕获订单 → 计算返利金额
3. **返利结算** → 更新用户钱包 → 提供提现服务

### 4. 接口扩展

需要扩展以下接口以支持返利功能：

1. **电商平台API接入**:
   - 淘宝联盟API
   - 京东联盟API
   - 拼多多多多客API
   - 其他平台API

2. **订单同步接口**:
   - 实时订单状态更新
   - 订单关联与匹配

3. **返利计算接口**:
   - 佣金比例配置
   - 多级分销计算

## 快速开始

### 1. 安装依赖

确保项目中已安装所需依赖：

```bash
npm install
```

### 2. 引入组件和混入

在需要集成推广功能的页面中，引入以下组件和混入：

```js
// 推广按钮组件
import PromotionToolButton from '@/components/PromotionToolButton.vue';
// 悬浮推广按钮组件
import FloatPromotionButton from '@/components/FloatPromotionButton.vue';
// 根据业务类型引入对应的推广混入
import xxxPromotionMixin from '@/mixins/xxxPromotionMixin';
```

### 3. 初始化推广功能

在页面加载时，初始化推广功能：

```js
// 推广相关
const hasPromotionPermission = ref(false);
const promotionData = ref({});

// 初始化推广功能
const initPromotion = () => {
  // 检查推广权限
  hasPromotionPermission.value = xxxPromotionMixin.methods.isContentOwner.call({
    $store: {
      state: {
        user: {
          userId: 'xxx' // 当前用户ID
        }
      }
    },
    xxxInfo: xxxInfo // 业务数据
  }) || xxxPromotionMixin.methods.isCommissionContent.call({
    xxxInfo: xxxInfo // 业务数据
  });
  
  // 生成推广数据
  if (hasPromotionPermission.value) {
    promotionData.value = xxxPromotionMixin.methods.generatePromotionData.call({
      xxxInfo: xxxInfo,
      promotionData: {}
    }).promotionData;
  }
};
```

### 4. 添加推广按钮

在页面模板中添加推广按钮：

```html
<!-- 普通推广按钮 -->
<PromotionToolButton 
  v-if="hasPromotionPermission" 
  @click="showXxxPromotion"
  buttonText="推广"
/>

<!-- 悬浮推广按钮 -->
<FloatPromotionButton 
  v-if="hasPromotionPermission" 
  @click="showXxxPromotion" 
/>
```

## 测试推广功能

### 1. 运行测试脚本

```bash
node test-promotion.js
```

这个脚本会模拟测试拼车、商品和商家推广功能。

### 2. 在实际环境中测试

1. 在业务页面中添加推广按钮
2. 确认按钮正确显示并且点击后能够正确跳转到推广工具页面
3. 测试二维码和海报生成功能
4. 测试分享功能
5. 测试权限控制

## 集成指南

详细的集成指南请参考 [推广功能集成指南](./docs/promotion-integration-guide.md)。

## 应用方案

推广工具全平台应用方案请参考 [推广工具全平台应用方案](./推广工具全平台应用方案.md)。

## 支持的业务类型

- 商家店铺 (`merchant`)
- 商品 (`product`)
- 拼车 (`carpool`)
- 房产 (`house`)
- 服务 (`service`)
- 社区信息 (`community`)
- 活动 (`activity`)
- 内容 (`content`)

## 知识产权与保密声明

© 2023-2024 磁州生活网。保留所有权利。

本软件系统及其相关文档是磁州生活网的专有和保密财产。未经磁州生活网明确书面许可，严禁以任何形式或方法使用、复制、修改、分发、出版、传播、展示或披露本软件系统的任何部分。

本系统包含商业机密和专有信息，受中华人民共和国著作权法、商业秘密法和其他知识产权法律法规保护。

任何未经授权使用本系统的行为将被视为侵权，磁州生活网将保留追究法律责任的权利。