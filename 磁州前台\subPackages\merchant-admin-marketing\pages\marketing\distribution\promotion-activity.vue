<template>
  <view class="activity-container">
    <!-- 自定义导航栏 -->
    <view class="navbar">
      <view class="navbar-back" @click="goBack">
        <view class="back-icon"></view>
      </view>
      <text class="navbar-title">推广活动</text>
      <view class="navbar-right">
        <view class="help-icon" @click="showHelp">?</view>
      </view>
    </view>
    
    <!-- 活动卡片部分 -->
    <view class="activity-list-section">
      <!-- 活动创建卡片 -->
      <view class="create-activity-card" @click="createActivity">
        <view class="create-icon">
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <circle cx="12" cy="12" r="10" stroke="#6B0FBE" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            <line x1="12" y1="8" x2="12" y2="16" stroke="#6B0FBE" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            <line x1="8" y1="12" x2="16" y2="12" stroke="#6B0FBE" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
        </view>
        <text class="create-text">创建新活动</text>
      </view>
      
      <!-- 活动卡片 -->
      <view class="activity-card" v-for="(activity, index) in activities" :key="index">
        <view class="card-header" :class="activity.status === 'active' ? 'active' : 'inactive'">
          <view class="card-status">
            <view class="status-dot"></view>
            <text class="status-text">{{activity.status === 'active' ? '进行中' : '已结束'}}</text>
          </view>
          <view class="card-actions">
            <view class="action-item edit" @click="editActivity(activity)" v-if="activity.status === 'active'">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M11 4H4C3.46957 4 2.96086 4.21071 2.58579 4.58579C2.21071 4.96086 2 5.46957 2 6V20C2 20.5304 2.21071 21.0391 2.58579 21.4142C2.96086 21.7893 3.46957 22 4 22H18C18.5304 22 19.0391 21.7893 19.4142 21.4142C19.7893 21.0391 20 20.5304 20 20V13" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M18.5 2.5C18.8978 2.10217 19.4374 1.87868 20 1.87868C20.5626 1.87868 21.1022 2.10217 21.5 2.5C21.8978 2.89782 22.1213 3.43739 22.1213 4C22.1213 4.56261 21.8978 5.10217 21.5 5.5L12 15L8 16L9 12L18.5 2.5Z" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </view>
            <view class="action-item delete" @click="deleteActivity(index)">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M3 6H5H21" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M8 6V4C8 3.46957 8.21071 2.96086 8.58579 2.58579C8.96086 2.21071 9.46957 2 10 2H14C14.5304 2 15.0391 2.21071 15.4142 2.58579C15.7893 2.96086 16 3.46957 16 4V6M19 6V20C19 20.5304 18.7893 21.0391 18.4142 21.4142C18.0391 21.7893 17.5304 22 17 22H7C6.46957 22 5.96086 21.7893 5.58579 21.4142C5.21071 21.0391 5 20.5304 5 20V6H19Z" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </view>
          </view>
        </view>
        
        <view class="card-content">
          <view class="activity-info">
            <text class="activity-title">{{activity.title}}</text>
            <text class="activity-desc">{{activity.description}}</text>
            <view class="activity-time">
              <svg width="14" height="14" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <circle cx="12" cy="12" r="10" stroke="#666666" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <polyline points="12 6 12 12 16 14" stroke="#666666" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
              <text>{{activity.startDate}} - {{activity.endDate}}</text>
            </view>
          </view>
          
          <view class="activity-stats">
            <view class="stat-item">
              <text class="stat-value">{{activity.views}}</text>
              <text class="stat-label">浏览量</text>
            </view>
            <view class="stat-divider"></view>
            <view class="stat-item">
              <text class="stat-value">{{activity.participants}}</text>
              <text class="stat-label">参与人数</text>
            </view>
            <view class="stat-divider"></view>
            <view class="stat-item">
              <text class="stat-value">{{activity.conversion}}%</text>
              <text class="stat-label">转化率</text>
            </view>
          </view>
        </view>
        
        <view class="card-footer">
          <button class="footer-button promote" @click="promoteActivity(activity)">
            <svg width="14" height="14" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M23 4V10H17" stroke="#6B0FBE" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              <path d="M20.49 15C19.9828 16.8299 18.9209 18.4671 17.4567 19.6614C15.9925 20.8558 14.2053 21.5457 12.3392 21.6244C10.4731 21.7032 8.63269 21.1656 7.07917 20.0886C5.52566 19.0115 4.33439 17.4449 3.68508 15.6371C3.03577 13.8293 2.96585 11.8724 3.48355 10.0233C4.00126 8.17428 5.0784 6.52467 6.57854 5.30981C8.07868 4.09496 9.93 3.37511 11.8677 3.24935C13.8053 3.12359 15.7249 3.59798 17.35 4.6" stroke="#6B0FBE" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
            <text>推广</text>
          </button>
          <button class="footer-button view" @click="viewActivityDetail(activity)">
            <svg width="14" height="14" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M1 12C1 12 5 4 12 4C19 4 23 12 23 12C23 12 19 20 12 20C5 20 1 12 1 12Z" stroke="#6B0FBE" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              <circle cx="12" cy="12" r="3" stroke="#6B0FBE" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
            <text>查看</text>
          </button>
        </view>
      </view>
    </view>
    
    <!-- 活动创建提示 -->
    <view class="activity-tips-section">
      <view class="section-header">
        <text class="section-title">创建活动小贴士</text>
      </view>
      <view class="tips-list">
        <view class="tip-item">
          <view class="tip-icon">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <circle cx="12" cy="12" r="10" stroke="#6B0FBE" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              <path d="M12 16V12" stroke="#6B0FBE" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              <circle cx="12" cy="8" r="0.5" stroke="#6B0FBE" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
          </view>
          <text class="tip-text">设定明确的活动目标和奖励机制，提高用户参与积极性</text>
        </view>
        <view class="tip-item">
          <view class="tip-icon">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <circle cx="12" cy="12" r="10" stroke="#6B0FBE" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              <path d="M12 16V12" stroke="#6B0FBE" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              <circle cx="12" cy="8" r="0.5" stroke="#6B0FBE" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
          </view>
          <text class="tip-text">活动时间不宜过长，2-7天为宜，保持用户热情</text>
        </view>
        <view class="tip-item">
          <view class="tip-icon">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <circle cx="12" cy="12" r="10" stroke="#6B0FBE" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              <path d="M12 16V12" stroke="#6B0FBE" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              <circle cx="12" cy="8" r="0.5" stroke="#6B0FBE" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
          </view>
          <text class="tip-text">结合节假日和热点事件，提高活动关注度</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, reactive } from 'vue';

// 活动列表
const activities = reactive([
  {
    id: 1,
    title: '618年中大促',
    description: '全场商品低至5折，满300减50，新用户额外获得100元优惠券',
    startDate: '2023-06-01',
    endDate: '2023-06-18',
    status: 'active',
    views: 1286,
    participants: 356,
    conversion: 27.6
  },
  {
    id: 2,
    title: '新人专享礼',
    description: '新注册用户下单立减30元，无门槛使用',
    startDate: '2023-05-15',
    endDate: '2023-06-30',
    status: 'active',
    views: 982,
    participants: 214,
    conversion: 21.8
  },
  {
    id: 3,
    title: '五一假期特惠',
    description: '五一劳动节特惠活动，全场满200减40，部分商品8折',
    startDate: '2023-04-28',
    endDate: '2023-05-07',
    status: 'inactive',
    views: 1572,
    participants: 483,
    conversion: 30.7
  },
  {
    id: 4,
    title: '春季上新活动',
    description: '春季新品上市，购买新品送精美礼品，同时参与抽奖',
    startDate: '2023-03-01',
    endDate: '2023-03-31',
    status: 'inactive',
    views: 1103,
    participants: 275,
    conversion: 24.9
  }
]);

// 方法
const goBack = () => {
  uni.navigateBack();
};

const showHelp = () => {
  uni.showModal({
    title: '推广活动帮助',
    content: '推广活动是提高店铺和商品曝光的有效方式，可以增加用户粘性和转化率。您可以创建多种类型的活动，并通过推广工具进行分享。',
    showCancel: false
  });
};

const createActivity = () => {
  uni.navigateTo({
    url: '/subPackages/merchant-admin-marketing/pages/marketing/distribution/create-activity'
  });
};

const editActivity = (activity) => {
  uni.navigateTo({
    url: `/subPackages/merchant-admin-marketing/pages/marketing/distribution/create-activity?id=${activity.id}`
  });
};

const deleteActivity = (index) => {
  uni.showModal({
    title: '确认删除',
    content: '是否确认删除该活动？删除后无法恢复。',
    success: (res) => {
      if (res.confirm) {
        activities.splice(index, 1);
        uni.showToast({
          title: '活动已删除',
          icon: 'success'
        });
      }
    }
  });
};

const promoteActivity = (activity) => {
  uni.showActionSheet({
    itemList: ['分享到微信', '生成推广海报', '分享到朋友圈', '复制活动链接'],
    success: (res) => {
      switch (res.tapIndex) {
        case 0:
          uni.showShareMenu({
            withShareTicket: true,
            menus: ['shareAppMessage']
          });
          break;
        case 1:
          uni.navigateTo({
            url: '/subPackages/merchant-admin-marketing/pages/marketing/distribution/promotion-tool'
          });
          break;
        case 2:
          uni.showShareMenu({
            withShareTicket: true,
            menus: ['shareTimeline']
          });
          break;
        case 3:
          uni.setClipboardData({
            data: `https://example.com/activity/${activity.id}`,
            success: () => {
              uni.showToast({
                title: '链接已复制',
                icon: 'success'
              });
            }
          });
          break;
      }
    }
  });
};

const viewActivityDetail = (activity) => {
  uni.navigateTo({
    url: `/subPackages/merchant-admin-marketing/pages/marketing/distribution/activity-detail?id=${activity.id}`
  });
};
</script>

<style lang="scss">
.activity-container {
  min-height: 100vh;
  background-color: #F5F7FA;
  padding-bottom: env(safe-area-inset-bottom);
}

/* 导航栏样式 */
.navbar {
  position: sticky;
  top: 0;
  left: 0;
  right: 0;
  background: linear-gradient(135deg, #A764CA, #6B0FBE);
  color: #fff;
  padding: 44px 16px 15px;
  display: flex;
  align-items: center;
  z-index: 100;
  box-shadow: 0 2px 10px rgba(107, 15, 190, 0.15);
}

.navbar-back {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-icon {
  width: 12px;
  height: 12px;
  border-left: 2px solid #fff;
  border-bottom: 2px solid #fff;
  transform: rotate(45deg);
}

.navbar-title {
  flex: 1;
  text-align: center;
  font-size: 18px;
  font-weight: 600;
  letter-spacing: 0.5px;
}

.navbar-right {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.help-icon {
  width: 24px;
  height: 24px;
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: bold;
}

/* 活动列表部分 */
.activity-list-section {
  margin: 16px;
}

.create-activity-card {
  background-color: #FFFFFF;
  border-radius: 20px;
  padding: 24px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.04);
  margin-bottom: 16px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border: 2px dashed rgba(107, 15, 190, 0.2);
}

.create-icon {
  width: 48px;
  height: 48px;
  border-radius: 24px;
  background-color: rgba(107, 15, 190, 0.05);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 12px;
}

.create-text {
  font-size: 16px;
  color: #6B0FBE;
  font-weight: 500;
}

.activity-card {
  background-color: #FFFFFF;
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.04);
  margin-bottom: 16px;
}

.card-header {
  padding: 12px 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header.active {
  background: linear-gradient(135deg, #A764CA, #6B0FBE);
}

.card-header.inactive {
  background: linear-gradient(135deg, #9E9E9E, #616161);
}

.card-status {
  display: flex;
  align-items: center;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 4px;
  background-color: #FFFFFF;
  margin-right: 8px;
}

.status-text {
  font-size: 14px;
  color: #FFFFFF;
  font-weight: 500;
}

.card-actions {
  display: flex;
}

.action-item {
  width: 28px;
  height: 28px;
  border-radius: 14px;
  background-color: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 8px;
}

.card-content {
  padding: 16px;
  border-bottom: 1px solid #F0F0F0;
}

.activity-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 8px;
}

.activity-desc {
  font-size: 14px;
  color: #666;
  line-height: 1.5;
  margin-bottom: 12px;
}

.activity-time {
  display: flex;
  align-items: center;
}

.activity-time svg {
  margin-right: 6px;
}

.activity-time text {
  font-size: 12px;
  color: #666;
}

.activity-stats {
  display: flex;
  justify-content: space-around;
  padding-top: 16px;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.stat-value {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 12px;
  color: #999;
}

.stat-divider {
  width: 1px;
  height: 30px;
  background-color: #F0F0F0;
}

.card-footer {
  padding: 12px 16px;
  display: flex;
  justify-content: space-between;
}

.footer-button {
  flex: 1;
  height: 40px;
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: 500;
  border: none;
  margin: 0 6px;
}

.footer-button svg {
  margin-right: 6px;
}

.footer-button.promote {
  background-color: rgba(107, 15, 190, 0.05);
  color: #6B0FBE;
}

.footer-button.view {
  background-color: #F5F7FA;
  color: #6B0FBE;
}

/* 活动提示部分 */
.activity-tips-section {
  margin: 16px;
  background-color: #FFFFFF;
  border-radius: 20px;
  padding: 20px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.04);
  margin-bottom: 24px;
}

.section-header {
  margin-bottom: 16px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.tips-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.tip-item {
  display: flex;
  align-items: flex-start;
}

.tip-icon {
  margin-right: 12px;
  margin-top: 2px;
}

.tip-text {
  font-size: 14px;
  color: #666;
  line-height: 1.5;
  flex: 1;
}
</style>