<template>
  <view class="advanced-marketing-container">
    <!-- 自定义导航栏 -->
    <view class="navbar">
      <view class="navbar-back" @click="goBack">
        <view class="back-icon"></view>
      </view>
      <text class="navbar-title">高级营销功能</text>
      <view class="navbar-right">
        <!-- 已移除帮助按钮 -->
      </view>
    </view>
    
    <!-- 页面内容 -->
    <scroll-view scroll-y class="page-content">
      <view class="page-header">
        <text class="header-title">高级营销功能</text>
        <text class="header-desc">提升销售和管理效率的专业营销工具</text>
      </view>
      
      <!-- 营销工具列表 -->
      <view class="marketing-tools">
        <!-- 智能营销助手 -->
        <view class="tool-card" @click="navigateTo('ai')">
          <view class="tool-icon purple">
            <view class="icon-circle"></view>
          </view>
          <view class="tool-content">
            <view class="tool-header">
              <text class="tool-title">智能营销助手</text>
              <text class="tool-subtitle">AI驱动的营销策划工具</text>
            </view>
            <text class="tool-desc">基于历史数据和行业洞察，智能生成最优营销策略与执行方案</text>
            <view class="tool-tags">
              <text class="tag ai">AI驱动</text>
              <text class="tag analysis">数据分析</text>
              <text class="tag auto">自动化</text>
            </view>
          </view>
          <view class="arrow-right"></view>
        </view>
        
        <!-- 客户群体细分 -->
        <view class="tool-card" @click="navigateTo('segment')">
          <view class="tool-icon blue">
            <view class="icon-circle"></view>
          </view>
          <view class="tool-content">
            <view class="tool-header">
              <text class="tool-title">客户群体细分</text>
              <text class="tool-subtitle">精准细分客户群体</text>
            </view>
            <text class="tool-desc">对客户进行多维度分析与分类，实现更精准的营销触达</text>
            <view class="tool-tags">
              <text class="tag customer">客户分析</text>
              <text class="tag portrait">标签画像</text>
              <text class="tag precision">精准化</text>
            </view>
          </view>
          <view class="arrow-right"></view>
        </view>
        
        <!-- 营销自动化 -->
        <view class="tool-card" @click="navigateTo('automation')">
          <view class="tool-icon orange">
            <view class="icon-circle"></view>
          </view>
          <view class="tool-content">
            <view class="tool-header">
              <text class="tool-title">营销自动化</text>
              <text class="tool-subtitle">设置自动触发营销流</text>
            </view>
            <text class="tool-desc">通过预设条件自动触发营销活动，提高效率并减少人工干预</text>
            <view class="tool-tags">
              <text class="tag workflow">工作流</text>
              <text class="tag trigger">触发器</text>
              <text class="tag efficiency">效率提升</text>
            </view>
          </view>
          <view class="arrow-right"></view>
        </view>
      </view>
      
      <!-- 使用指南 -->
      <view class="usage-guide">
        <view class="guide-header">
          <text class="guide-title">如何使用高级营销功能</text>
          <text class="guide-subtitle">三步提升您的营销效果</text>
        </view>
        <view class="steps">
          <view class="step-item">
            <view class="step-number">1</view>
            <text class="step-text">选择适合您业务的营销工具</text>
          </view>
          <view class="step-item">
            <view class="step-number">2</view>
            <text class="step-text">根据提示完成设置或分析</text>
          </view>
          <view class="step-item">
            <view class="step-number">3</view>
            <text class="step-text">应用生成的策略或自动化流程</text>
          </view>
        </view>
      </view>
      
      <!-- 营销成功案例 -->
      <view class="success-cases">
        <view class="cases-header">
          <text class="cases-title">成功案例</text>
          <text class="view-all">查看全部</text>
        </view>
        <scroll-view class="cases-scroll" scroll-x>
          <view class="case-item">
            <view class="case-content">
              <text class="case-title">服装店客户细分</text>
              <text class="case-desc">通过客户群体细分，精准推送季节性产品，销售额提升32%</text>
              <view class="case-tags">
                <text class="case-tag">客户细分</text>
                <text class="case-tag">精准营销</text>
              </view>
            </view>
          </view>
          <view class="case-item">
            <view class="case-content">
              <text class="case-title">生鲜超市自动化营销</text>
              <text class="case-desc">设置库存预警自动触发促销，减少损耗同时提高客流量18%</text>
              <view class="case-tags">
                <text class="case-tag">自动化</text>
                <text class="case-tag">库存优化</text>
              </view>
            </view>
          </view>
          <view class="case-item">
            <view class="case-content">
              <text class="case-title">美妆品牌AI营销</text>
              <text class="case-desc">利用AI分析潮流趋势，优化产品组合，复购率提升25%</text>
              <view class="case-tags">
                <text class="case-tag">AI驱动</text>
                <text class="case-tag">趋势分析</text>
              </view>
            </view>
          </view>
        </scroll-view>
      </view>
      
      <!-- 底部空间 -->
      <view style="height: 20px;"></view>
    </scroll-view>
    
    <!-- 底部导航栏 -->
    <tab-bar active-tab="marketing" @tab-change="handleTabChange"></tab-bar>
  </view>
</template>

<script>
import TabBar from '../../../../../components/TabBar.vue'

export default {
  components: {
    TabBar
  },
  data() {
    return {
      // 数据
    }
  },
  methods: {
    goBack() {
      uni.navigateBack();
    },
    showHelp() {
      uni.showToast({
        title: '高级营销功能帮助',
        icon: 'none'
      });
    },
    navigateTo(type) {
      switch(type) {
        case 'ai':
          uni.navigateTo({
            url: '/subPackages/merchant-admin/pages/marketing/ai/index'
          });
          break;
        case 'segment':
          uni.navigateTo({
            url: '/subPackages/merchant-admin/pages/marketing/segment/index'
          });
          break;
        case 'automation':
          uni.navigateTo({
            url: '/subPackages/merchant-admin/pages/marketing/automation/index'
          });
          break;
        default:
          uni.showToast({
            title: '功能开发中',
            icon: 'none'
          });
      }
    },
    handleTabChange(tabId) {
      // 处理底部标签页切换事件
      console.log('切换到标签:', tabId);
    }
  }
}
</script>

<style lang="scss">
.advanced-marketing-container {
  min-height: 100vh;
  background-color: #F5F7FA;
}

/* 导航栏样式 */
.navbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 30rpx;
  height: 180rpx; /* 固定高度包含状态栏和关闭按钮, +20rpx (10px) */
  padding-top: var(--status-bar-height);
  background: linear-gradient(135deg, #4A6CF7, #0E156B);
  position: relative;
  width: 100%;
  box-sizing: border-box;
  z-index: 999;
  box-shadow: 0 2px 10px rgba(74, 108, 247, 0.15);
}

.navbar-back {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-icon {
  width: 12px;
  height: 12px;
  border-left: 2px solid #fff;
  border-bottom: 2px solid #fff;
  transform: rotate(45deg);
}

.navbar-title {
  font-size: 36rpx;
  font-weight: 500;
  color: #ffffff;
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, 30%); /* 向下调整以对齐关闭键, 适应更高的导航栏 */
  white-space: nowrap;
}

.navbar-right {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.help-icon {
  width: 24px;
  height: 24px;
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: bold;
}

.page-content {
  height: calc(100vh - var(--status-bar-height) - 50px);
  box-sizing: border-box;
  padding: 15px;
  padding-top: 10px;
}

/* 页面头部 */
.page-header {
  padding: 10px 0 20px;
}

.header-title {
  font-size: 22px;
  font-weight: 700;
  color: #333;
  margin-bottom: 8px;
  display: block;
}

.header-desc {
  font-size: 14px;
  color: #666;
  display: block;
}

/* 营销工具卡片 */
.marketing-tools {
  margin-bottom: 20px;
}

.tool-card {
  background: #FFFFFF;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  padding: 20px;
  margin-bottom: 15px;
  display: flex;
  align-items: flex-start;
  position: relative;
}

.tool-icon {
  width: 44px;
  height: 44px;
  border-radius: 22px;
  flex-shrink: 0;
  margin-right: 15px;
  position: relative;
}

.tool-icon.purple {
  background: #6366F1;
}

.tool-icon.blue {
  background: #0EA5E9;
}

.tool-icon.orange {
  background: #F59E0B;
}

.icon-circle {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  position: relative;
}

.tool-content {
  flex: 1;
}

.tool-header {
  margin-bottom: 8px;
}

.tool-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
  display: block;
}

.tool-subtitle {
  font-size: 12px;
  color: #666;
}

.tool-desc {
  font-size: 13px;
  line-height: 1.5;
  color: #666;
  margin-bottom: 10px;
  display: block;
}

.tool-tags {
  display: flex;
  flex-wrap: wrap;
}

.tag {
  font-size: 10px;
  padding: 2px 8px;
  border-radius: 10px;
  margin-right: 6px;
}

.tag.ai {
  background: rgba(99, 102, 241, 0.1);
  color: #6366F1;
}

.tag.analysis {
  background: rgba(236, 72, 153, 0.1);
  color: #EC4899;
}

.tag.auto {
  background: rgba(245, 158, 11, 0.1);
  color: #F59E0B;
}

.tag.customer {
  background: rgba(14, 165, 233, 0.1);
  color: #0EA5E9;
}

.tag.portrait {
  background: rgba(236, 72, 153, 0.1);
  color: #EC4899;
}

.tag.precision {
  background: rgba(16, 185, 129, 0.1);
  color: #10B981;
}

.tag.workflow {
  background: rgba(245, 158, 11, 0.1);
  color: #F59E0B;
}

.tag.trigger {
  background: rgba(236, 72, 153, 0.1);
  color: #EC4899;
}

.tag.efficiency {
  background: rgba(16, 185, 129, 0.1);
  color: #10B981;
}

.arrow-right {
  width: 12px;
  height: 12px;
  border-top: 2px solid #C7C7CC;
  border-right: 2px solid #C7C7CC;
  transform: rotate(45deg);
  position: absolute;
  right: 20px;
  top: 50%;
  margin-top: -6px;
}

/* 使用指南 */
.usage-guide {
  background: #FFFFFF;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  padding: 20px;
  margin-bottom: 15px;
}

.guide-header {
  margin-bottom: 15px;
}

.guide-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  display: block;
  margin-bottom: 5px;
}

.guide-subtitle {
  font-size: 12px;
  color: #666;
}

.steps {
  padding: 5px 0;
}

.step-item {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}

.step-number {
  width: 24px;
  height: 24px;
  background: #4A6CF7;
  border-radius: 12px;
  color: white;
  font-size: 12px;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
}

.step-text {
  font-size: 14px;
  color: #333;
}

/* 成功案例 */
.success-cases {
  background: #FFFFFF;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  padding: 20px;
  margin-bottom: 15px;
}

.cases-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.cases-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.view-all {
  font-size: 14px;
  color: #4A6CF7;
}

.cases-scroll {
  white-space: nowrap;
}

.case-item {
  display: inline-block;
  width: 270px;
  padding: 15px;
  background: #F8FAFC;
  border-radius: 10px;
  margin-right: 10px;
}

.case-title {
  font-size: 14px;
  font-weight: 600;
  color: #333;
  display: block;
  margin-bottom: 8px;
}

.case-desc {
  font-size: 12px;
  color: #666;
  line-height: 1.4;
  display: block;
  margin-bottom: 10px;
  white-space: normal;
}

.case-tags {
  display: flex;
  flex-wrap: wrap;
}

.case-tag {
  font-size: 10px;
  color: #4A6CF7;
  background: rgba(74, 108, 247, 0.1);
  padding: 2px 8px;
  border-radius: 10px;
  margin-right: 6px;
  margin-bottom: 6px;
}
</style> 