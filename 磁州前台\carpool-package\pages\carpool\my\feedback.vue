<template>
  <view class="feedback-container">
    <!-- 自定义导航栏 -->
    <view class="custom-header" :style="{ paddingTop: statusBarHeight + 'px' }">
      <view class="header-content">
        <view class="left-action" @click="goBack">
          <image src="/static/images/tabbar/最新返回键.png" class="action-icon back-icon"></image>
        </view>
        <view class="title-area">
          <text class="page-title">意见反馈</text>
        </view>
        <view class="right-action">
          <!-- 右侧占位 -->
        </view>
      </view>
    </view>
    
    <!-- 内容区域 -->
    <view class="scrollable-content" :style="{ paddingTop: (statusBarHeight + 44) + 'px' }">
      <!-- 反馈表单 -->
      <view class="feedback-form">
        <!-- 反馈类型 -->
        <view class="form-section">
          <view class="section-title">反馈类型</view>
          <view class="feedback-types">
            <view 
              class="type-item" 
              v-for="(type, index) in feedbackTypes" 
              :key="index"
              :class="{ active: selectedType === index }"
              @click="selectType(index)"
            >
              <text class="type-text">{{type}}</text>
            </view>
          </view>
        </view>
        
        <!-- 反馈内容 -->
        <view class="form-section">
          <view class="section-title">反馈内容</view>
          <view class="content-textarea-wrapper">
            <textarea 
              class="content-textarea" 
              v-model="feedbackContent" 
              placeholder="请详细描述您遇到的问题或建议，以便我们更好地解决和改进..."
              maxlength="500"
              placeholder-class="textarea-placeholder"
            ></textarea>
            <view class="word-count">{{feedbackContent.length}}/500</view>
          </view>
        </view>
        
        <!-- 上传图片 -->
        <view class="form-section">
          <view class="section-title">上传图片(选填)</view>
          <view class="upload-section">
            <view class="image-grid">
              <view class="image-item" v-for="(image, index) in uploadedImages" :key="index">
                <image :src="image" mode="aspectFill" class="preview-image"></image>
                <view class="delete-icon" @click.stop="deleteImage(index)">×</view>
              </view>
              <view class="upload-item" @click="chooseImage" v-if="uploadedImages.length < 3">
                <image src="/static/images/icons/upload-image.png" mode="aspectFit" class="upload-icon"></image>
                <text class="upload-text">添加图片</text>
              </view>
            </view>
            <text class="upload-hint">最多上传3张图片，每张不超过5MB</text>
          </view>
        </view>
        
        <!-- 联系方式 -->
        <view class="form-section">
          <view class="section-title">联系方式(选填)</view>
          <view class="contact-input-wrapper">
            <input 
              class="contact-input" 
              v-model="contactInfo" 
              placeholder="请留下您的手机号或微信，方便我们联系您"
              placeholder-class="input-placeholder"
            />
          </view>
        </view>
      </view>
      
      <!-- 提交按钮 -->
      <button 
        class="submit-button" 
        :disabled="!isFormValid" 
        :class="{ disabled: !isFormValid }"
        @click="submitFeedback"
      >
        提交反馈
      </button>
      
      <!-- 历史反馈 -->
      <view class="history-section" v-if="feedbackHistory.length > 0">
        <view class="history-header">
          <view class="history-title">历史反馈</view>
        </view>
        
        <view class="history-list">
          <view class="history-item" v-for="(item, index) in feedbackHistory" :key="index" @click="viewHistoryDetail(item)">
            <view class="history-content">
              <view class="history-info">
                <text class="history-type">{{feedbackTypes[item.type]}}</text>
                <text class="history-time">{{item.time}}</text>
              </view>
              <text class="history-brief">{{item.content}}</text>
            </view>
            <view class="history-status" :class="getStatusClass(item.status)">
              <text class="status-text">{{getStatusText(item.status)}}</text>
              <image src="/static/images/icons/arrow-right-gray.png" mode="aspectFit" class="arrow-icon"></image>
            </view>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 历史反馈详情弹窗 -->
    <view class="popup-mask" v-if="showHistoryDetail" @click="closeHistoryDetail"></view>
    <view class="popup-container" v-if="showHistoryDetail">
      <view class="popup-header">
        <text class="popup-title">反馈详情</text>
        <view class="popup-close" @click="closeHistoryDetail">×</view>
      </view>
      <scroll-view class="popup-content" scroll-y>
        <view class="detail-section">
          <view class="detail-label">反馈类型</view>
          <view class="detail-value">{{feedbackTypes[currentHistory.type]}}</view>
        </view>
        
        <view class="detail-section">
          <view class="detail-label">提交时间</view>
          <view class="detail-value">{{currentHistory.time}}</view>
        </view>
        
        <view class="detail-section">
          <view class="detail-label">处理状态</view>
          <view class="detail-value" :class="getStatusClass(currentHistory.status)">
            {{getStatusText(currentHistory.status)}}
          </view>
        </view>
        
        <view class="detail-section">
          <view class="detail-label">反馈内容</view>
          <view class="detail-value content">{{currentHistory.content}}</view>
        </view>
        
        <view class="detail-section" v-if="currentHistory.images && currentHistory.images.length > 0">
          <view class="detail-label">附件图片</view>
          <view class="detail-images">
            <image 
              v-for="(img, index) in currentHistory.images" 
              :key="index" 
              :src="img" 
              mode="widthFix" 
              class="detail-image"
              @click="previewImage(img, currentHistory.images)"
            ></image>
          </view>
        </view>
        
        <view class="detail-section" v-if="currentHistory.reply">
          <view class="detail-label">官方回复</view>
          <view class="detail-value reply">
            <text class="reply-time">{{currentHistory.replyTime}}</text>
            <text class="reply-content">{{currentHistory.reply}}</text>
          </view>
        </view>
      </scroll-view>
    </view>
    
    <!-- 提交成功弹窗 -->
    <view class="popup-mask" v-if="showSuccessPopup"></view>
    <view class="success-popup" v-if="showSuccessPopup">
      <image src="/static/images/icons/success.png" mode="aspectFit" class="success-icon"></image>
      <text class="success-title">提交成功</text>
      <text class="success-message">感谢您的反馈，我们会尽快处理</text>
      <button class="success-button" @click="closeSuccessPopup">确定</button>
    </view>
  </view>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';

// 状态栏高度
const statusBarHeight = ref(20);

// 反馈类型
const feedbackTypes = ref(['功能建议', '信息错误', '拼车纠纷', '账号问题', '其他问题']);
const selectedType = ref(0);
const feedbackContent = ref('');
const uploadedImages = ref([]);
const contactInfo = ref('');
const showHistoryDetail = ref(false);
const currentHistory = ref({});
const showSuccessPopup = ref(false);
const feedbackHistory = ref([
  {
    id: '6001',
    type: 0,
    content: '建议在拼车页面增加路线规划功能，方便用户查看路线详情。',
    time: '2023-10-10 14:30',
    status: 'replied',
    reply: '感谢您的建议！我们正在开发路线规划功能，预计将在下个版本中上线。',
    replyTime: '2023-10-11 09:15'
  },
  {
    id: '6002',
    type: 3,
    content: '无法绑定手机号，提示"验证码错误"，但我确认验证码输入正确。',
    time: '2023-09-25 16:45',
    status: 'processing',
    images: ['/static/images/feedback/error.jpg']
  }
]);

// 计算表单是否有效
const isFormValid = computed(() => {
  return feedbackContent.value.trim().length >= 5;
});

// 返回上一页
const goBack = () => {
  uni.navigateBack();
};

// 选择反馈类型
const selectType = (index) => {
  selectedType.value = index;
};

// 选择图片
const chooseImage = () => {
  if (uploadedImages.value.length >= 3) {
    uni.showToast({
      title: '最多上传3张图片',
      icon: 'none'
    });
    return;
  }
  
  uni.chooseImage({
    count: 3 - uploadedImages.value.length,
    sizeType: ['compressed'],
    sourceType: ['album', 'camera'],
    success: (res) => {
      // 检查图片大小
      const tempFiles = res.tempFiles;
      let isValid = true;
      
      tempFiles.forEach(file => {
        if (file.size > 5 * 1024 * 1024) { // 5MB
          isValid = false;
          uni.showToast({
            title: '图片大小不能超过5MB',
            icon: 'none'
          });
          return;
        }
      });
      
      if (isValid) {
        const tempFilePaths = res.tempFilePaths;
        uploadedImages.value = [...uploadedImages.value, ...tempFilePaths];
        
        // 控制数量
        if (uploadedImages.value.length > 3) {
          uploadedImages.value = uploadedImages.value.slice(0, 3);
        }
      }
    }
  });
};

// 删除图片
const deleteImage = (index) => {
  uploadedImages.value.splice(index, 1);
};

// 提交反馈
const submitFeedback = () => {
  if (!isFormValid.value) return;
  
  // 显示加载状态
  uni.showLoading({
    title: '提交中...'
  });
  
  // 模拟提交
  setTimeout(() => {
    uni.hideLoading();
    
    // 模拟成功
    showSuccessPopup.value = true;
    
    // 清空表单
    feedbackContent.value = '';
    uploadedImages.value = [];
  }, 1500);
};

// 关闭成功弹窗
const closeSuccessPopup = () => {
  showSuccessPopup.value = false;
  
  // 添加到历史记录
  const newFeedback = {
    id: Date.now().toString(),
    type: selectedType.value,
    content: feedbackContent.value,
    time: formatTime(new Date()),
    status: 'pending',
    images: uploadedImages.value
  };
  
  feedbackHistory.value.unshift(newFeedback);
};

// 查看历史详情
const viewHistoryDetail = (item) => {
  currentHistory.value = item;
  showHistoryDetail.value = true;
};

// 关闭历史详情
const closeHistoryDetail = () => {
  showHistoryDetail.value = false;
};

// 预览图片
const previewImage = (current, urls) => {
  uni.previewImage({
    current: current,
    urls: urls
  });
};

// 获取状态文本
const getStatusText = (status) => {
  const statusMap = {
    pending: '待处理',
    processing: '处理中',
    replied: '已回复',
    closed: '已关闭'
  };
  return statusMap[status] || status;
};

// 获取状态样式类
const getStatusClass = (status) => {
  const classMap = {
    pending: 'status-pending',
    processing: 'status-processing',
    replied: 'status-replied',
    closed: 'status-closed'
  };
  return classMap[status] || '';
};

// 格式化时间
const formatTime = (date) => {
  const year = date.getFullYear();
  const month = (date.getMonth() + 1).toString().padStart(2, '0');
  const day = date.getDate().toString().padStart(2, '0');
  const hours = date.getHours().toString().padStart(2, '0');
  const minutes = date.getMinutes().toString().padStart(2, '0');
  
  return `${year}-${month}-${day} ${hours}:${minutes}`;
};

// 生命周期函数
onMounted(() => {
  // 获取状态栏高度
  const systemInfo = uni.getSystemInfoSync();
  statusBarHeight.value = systemInfo.statusBarHeight || 20;
});
</script>

<style lang="scss">
.feedback-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #F5F7FA;
}

/* 导航栏样式 */
.custom-header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  background-color: #1677FF;
}

.header-content {
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 12px;
}

.left-action, .right-action {
  width: 44px;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.action-icon {
  width: 24px;
  height: 24px;
}

.back-icon {
  width: 24px;
  height: 24px;
  /* 图标是黑色的，需要转为白色 */
  filter: brightness(0) invert(1);
}

.title-area {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

.page-title {
  font-size: 18px;
  font-weight: 600;
  color: #FFFFFF;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

/* 内容区域 */
.scrollable-content {
  padding-left: 16px;
  padding-right: 16px;
  padding-bottom: 16px;
}

/* 反馈表单 */
.feedback-form {
  background-color: #FFFFFF;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  margin-bottom: 16px;
}

.form-section {
  padding: 16px;
  border-bottom: 1px solid #F5F5F5;
}

.form-section:last-child {
  border-bottom: none;
}

.section-title {
  font-size: 16px;
  font-weight: 500;
  color: #333333;
  margin-bottom: 12px;
}

/* 反馈类型 */
.feedback-types {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
}

.type-item {
  padding: 8px 16px;
  border-radius: 20px;
  background-color: #F5F5F5;
}

.type-item.active {
  background-color: rgba(76, 175, 80, 0.1);
  border: 1px solid #4CAF50;
}

.type-text {
  font-size: 14px;
  color: #666666;
}

.type-item.active .type-text {
  color: #4CAF50;
}

/* 反馈内容 */
.content-textarea-wrapper {
  position: relative;
  border: 1px solid #EEEEEE;
  border-radius: 8px;
  background-color: #F9F9F9;
  padding: 12px;
}

.content-textarea {
  width: 100%;
  height: 120px;
  font-size: 14px;
  color: #333333;
  line-height: 1.5;
}

.textarea-placeholder {
  color: #999999;
}

.word-count {
  position: absolute;
  right: 12px;
  bottom: 12px;
  font-size: 12px;
  color: #999999;
}

/* 上传图片 */
.upload-section {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.image-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
}

.image-item, .upload-item {
  width: 80px;
  height: 80px;
  border-radius: 8px;
  overflow: hidden;
  position: relative;
}

.preview-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.delete-icon {
  position: absolute;
  top: 0;
  right: 0;
  width: 20px;
  height: 20px;
  background-color: rgba(0, 0, 0, 0.5);
  color: #FFFFFF;
  font-size: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-bottom-left-radius: 8px;
}

.upload-item {
  border: 1px dashed #DDDDDD;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 4px;
  background-color: #F9F9F9;
}

.upload-icon {
  width: 24px;
  height: 24px;
}

.upload-text {
  font-size: 12px;
  color: #999999;
}

.upload-hint {
  font-size: 12px;
  color: #999999;
}

/* 联系方式 */
.contact-input-wrapper {
  border: 1px solid #EEEEEE;
  border-radius: 8px;
  background-color: #F9F9F9;
  padding: 12px;
}

.contact-input {
  width: 100%;
  height: 24px;
  font-size: 14px;
  color: #333333;
}

.input-placeholder {
  color: #999999;
}

/* 提交按钮 */
.submit-button {
  width: 100%;
  height: 44px;
  line-height: 44px;
  text-align: center;
  background-color: #4CAF50;
  color: #FFFFFF;
  font-size: 16px;
  font-weight: 500;
  border-radius: 22px;
  margin: 24px 0;
}

.submit-button.disabled {
  background-color: #E0E0E0;
  color: #999999;
}

/* 历史反馈 */
.history-section {
  background-color: #FFFFFF;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  margin-bottom: 24px;
}

.history-header {
  padding: 16px;
  border-bottom: 1px solid #F5F5F5;
}

.history-title {
  font-size: 16px;
  font-weight: 500;
  color: #333333;
}

.history-list {
  display: flex;
  flex-direction: column;
}

.history-item {
  display: flex;
  justify-content: space-between;
  padding: 16px;
  border-bottom: 1px solid #F5F5F5;
}

.history-item:last-child {
  border-bottom: none;
}

.history-content {
  flex: 1;
}

.history-info {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.history-type {
  font-size: 14px;
  font-weight: 500;
  color: #333333;
}

.history-time {
  font-size: 12px;
  color: #999999;
}

.history-brief {
  font-size: 14px;
  color: #666666;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
  overflow: hidden;
}

.history-status {
  display: flex;
  align-items: center;
  gap: 4px;
}

.status-text {
  font-size: 14px;
}

.arrow-icon {
  width: 16px;
  height: 16px;
}

.status-pending .status-text {
  color: #FF9800;
}

.status-processing .status-text {
  color: #2196F3;
}

.status-replied .status-text {
  color: #4CAF50;
}

.status-closed .status-text {
  color: #9E9E9E;
}

/* 历史详情弹窗 */
.popup-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000;
}

.popup-container {
  position: fixed;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  width: 90%;
  max-height: 80vh;
  display: flex;
  flex-direction: column;
  background-color: #FFFFFF;
  border-radius: 12px;
  overflow: hidden;
  z-index: 1001;
}

.popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #EEEEEE;
}

.popup-title {
  font-size: 18px;
  font-weight: 500;
  color: #333333;
}

.popup-close {
  font-size: 24px;
  color: #999999;
  padding: 0 8px;
}

.popup-content {
  flex: 1;
  padding: 16px;
  overflow-y: auto;
}

.detail-section {
  margin-bottom: 16px;
}

.detail-label {
  font-size: 14px;
  color: #999999;
  margin-bottom: 8px;
}

.detail-value {
  font-size: 15px;
  color: #333333;
}

.detail-value.content {
  line-height: 1.6;
  white-space: pre-wrap;
}

.detail-images {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 8px;
}

.detail-image {
  width: 100%;
  border-radius: 8px;
}

.detail-value.reply {
  background-color: #F8F8F8;
  border-radius: 8px;
  padding: 12px;
}

.reply-time {
  font-size: 12px;
  color: #999999;
  display: block;
  margin-bottom: 8px;
}

.reply-content {
  font-size: 14px;
  color: #333333;
  line-height: 1.6;
}

/* 提交成功弹窗 */
.success-popup {
  position: fixed;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  width: 80%;
  background-color: #FFFFFF;
  border-radius: 12px;
  overflow: hidden;
  z-index: 1001;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 24px;
}

.success-icon {
  width: 60px;
  height: 60px;
  margin-bottom: 16px;
}

.success-title {
  font-size: 18px;
  font-weight: 500;
  color: #333333;
  margin-bottom: 8px;
}

.success-message {
  font-size: 14px;
  color: #666666;
  margin-bottom: 24px;
  text-align: center;
}

.success-button {
  width: 80%;
  height: 44px;
  line-height: 44px;
  text-align: center;
  background-color: #4CAF50;
  color: #FFFFFF;
  font-size: 16px;
  border-radius: 22px;
}
</style> 