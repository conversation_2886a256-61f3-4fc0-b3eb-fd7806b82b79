"use strict";
const carDetail = {
  id: "car-001",
  title: "2020年丰田卡罗拉1.8L自动挡",
  price: 98e3,
  brand: "丰田",
  model: "卡罗拉",
  year: 2020,
  mileage: 3.5,
  // 行驶里程，单位万公里
  displacement: "1.8L",
  transmission: "自动挡",
  fuelType: "汽油",
  color: "珍珠白",
  licensePlate: "冀E",
  // 车牌归属地
  licensedTime: "2020-05",
  // 上牌时间
  annualInspection: "2024-05",
  // 年检到期时间
  insurance: "2024-05",
  // 保险到期时间
  transferTimes: 0,
  // 过户次数
  condition: "九成新",
  description: "个人一手车，无事故，定期4S店保养，车况极佳，内饰干净整洁，发动机变速箱工作正常，随时可以看车试驾。",
  contact: {
    name: "李先生",
    phone: "139****5678",
    wechat: "lixiansheng123"
  },
  features: ["一手车", "无事故", "4S店保养", "新车质保", "可按揭"],
  configuration: ["倒车影像", "定速巡航", "自动空调", "真皮座椅", "多功能方向盘", "蓝牙/车载电话"],
  images: [
    "/static/images/car/car-1.jpg",
    "/static/images/car/car-2.jpg",
    "/static/images/car/car-3.jpg",
    "/static/images/car/car-4.jpg"
  ],
  location: {
    latitude: 36.314736,
    longitude: 114.711234,
    address: "磁县城区幸福路456号"
  },
  publishTime: "2024-03-12 09:45:00",
  views: 278,
  likes: 18,
  collections: 9,
  status: "active"
  // active, sold, expired
};
const relatedCars = [
  {
    id: "car-002",
    title: "2019年大众朗逸1.5L手动挡",
    price: 75e3,
    brand: "大众",
    model: "朗逸",
    year: 2019,
    mileage: 4.2,
    displacement: "1.5L",
    transmission: "手动挡",
    condition: "八成新",
    image: "/static/images/car/car-5.jpg"
  },
  {
    id: "car-003",
    title: "2021年本田思域1.5T自动挡",
    price: 128e3,
    brand: "本田",
    model: "思域",
    year: 2021,
    mileage: 2.8,
    displacement: "1.5T",
    transmission: "自动挡",
    condition: "九成新",
    image: "/static/images/car/car-6.jpg"
  },
  {
    id: "car-004",
    title: "2018年日产轩逸1.6L自动挡",
    price: 68e3,
    brand: "日产",
    model: "轩逸",
    year: 2018,
    mileage: 5.6,
    displacement: "1.6L",
    transmission: "自动挡",
    condition: "七成新",
    image: "/static/images/car/car-7.jpg"
  }
];
const fetchCarDetail = (id) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve(carDetail);
    }, 500);
  });
};
const fetchRelatedCars = () => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve(relatedCars);
    }, 500);
  });
};
exports.fetchCarDetail = fetchCarDetail;
exports.fetchRelatedCars = fetchRelatedCars;
//# sourceMappingURL=../../../.sourcemap/mp-weixin/mock/publish/carInfo.js.map
