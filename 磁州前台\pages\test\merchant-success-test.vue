<template>
  <view class="test-page">
    <view class="page-header">
      <text class="page-title">商家成功页面测试</text>
      <text class="page-desc">测试商家置顶和刷新功能</text>
    </view>
    
    <!-- 模拟商家成功页面的布局 -->
    <view class="success-container">
      <!-- 成功信息 -->
      <view class="success-info">
        <view class="success-icon">✅</view>
        <text class="success-title">恭喜！您已成功入驻</text>
        <text class="success-subtitle">审核通过，您的店铺已正式上线</text>
      </view>
      
      <!-- 店铺推广 -->
      <view class="promotion-section">
        <view class="section-title">店铺推广</view>
        <text class="section-desc">提升店铺曝光，获得更多客户</text>
        
        <!-- 选项卡 -->
        <view class="tab-container">
          <view 
            class="tab-item" 
            :class="{ active: activeTab === 'top' }"
            @click="activeTab = 'top'"
          >
            置顶店铺
          </view>
          <view 
            class="tab-item" 
            :class="{ active: activeTab === 'refresh' }"
            @click="activeTab = 'refresh'"
          >
            刷新店铺
          </view>
        </view>
        
        <!-- 置顶内容 -->
        <view v-if="activeTab === 'top'" class="tab-content">
          <ConfigurablePremiumActions
            showMode="direct"
            pageType="merchant_top"
            :itemData="topData"
            @action-completed="handleTopCompleted"
            @action-cancelled="handleTopCancelled"
          />
        </view>
        
        <!-- 刷新内容 -->
        <view v-if="activeTab === 'refresh'" class="tab-content">
          <ConfigurablePremiumActions
            showMode="direct"
            pageType="merchant_refresh"
            :itemData="refreshData"
            @action-completed="handleRefreshCompleted"
            @action-cancelled="handleRefreshCancelled"
          />
        </view>
      </view>
    </view>
    
    <!-- 测试结果 -->
    <view class="test-results" v-if="testResults.length > 0">
      <view class="results-title">操作记录</view>
      <view class="result-item" v-for="(result, index) in testResults" :key="index">
        <view class="result-time">{{ result.time }}</view>
        <view class="result-action">{{ result.action }}</view>
        <view class="result-type">{{ result.type }}</view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, reactive } from 'vue';
import ConfigurablePremiumActions from '@/components/premium/ConfigurablePremiumActions.vue';

// 当前选中的选项卡
const activeTab = ref('top');

// 测试数据
const topData = reactive({
  id: 'merchant_top_test',
  title: '商家置顶',
  description: '置顶您的店铺，获得更多曝光'
});

const refreshData = reactive({
  id: 'merchant_refresh_test',
  title: '商家刷新',
  description: '刷新您的店铺信息到最新'
});

// 测试结果
const testResults = ref([]);

// 处理置顶操作完成
const handleTopCompleted = (result) => {
  console.log('置顶操作完成:', result);
  
  testResults.value.unshift({
    time: new Date().toLocaleTimeString(),
    action: '置顶完成',
    type: result.type === 'ad' ? '看广告' : '付费'
  });
  
  uni.showToast({
    title: '置顶成功',
    icon: 'success'
  });
};

// 处理置顶操作取消
const handleTopCancelled = (result) => {
  console.log('置顶操作取消:', result);
  
  testResults.value.unshift({
    time: new Date().toLocaleTimeString(),
    action: '置顶取消',
    type: result.type === 'ad' ? '看广告' : '付费'
  });
};

// 处理刷新操作完成
const handleRefreshCompleted = (result) => {
  console.log('刷新操作完成:', result);
  
  testResults.value.unshift({
    time: new Date().toLocaleTimeString(),
    action: '刷新完成',
    type: result.type === 'ad' ? '看广告' : '付费'
  });
  
  uni.showToast({
    title: '刷新成功',
    icon: 'success'
  });
};

// 处理刷新操作取消
const handleRefreshCancelled = (result) => {
  console.log('刷新操作取消:', result);
  
  testResults.value.unshift({
    time: new Date().toLocaleTimeString(),
    action: '刷新取消',
    type: result.type === 'ad' ? '看广告' : '付费'
  });
};
</script>

<style lang="scss" scoped>
.test-page {
  padding: 20rpx;
  background: #f5f5f5;
  min-height: 100vh;
}

.page-header {
  text-align: center;
  margin-bottom: 30rpx;
  padding: 30rpx;
  background: white;
  border-radius: 20rpx;
}

.page-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  display: block;
  margin-bottom: 10rpx;
}

.page-desc {
  font-size: 28rpx;
  color: #666;
  display: block;
}

.success-container {
  background: white;
  border-radius: 20rpx;
  overflow: hidden;
}

.success-info {
  text-align: center;
  padding: 40rpx 30rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.success-icon {
  font-size: 80rpx;
  margin-bottom: 20rpx;
}

.success-title {
  font-size: 36rpx;
  font-weight: 600;
  display: block;
  margin-bottom: 10rpx;
}

.success-subtitle {
  font-size: 28rpx;
  opacity: 0.9;
  display: block;
}

.promotion-section {
  padding: 30rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 10rpx;
}

.section-desc {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 30rpx;
  display: block;
}

.tab-container {
  display: flex;
  background: #f8f9fa;
  border-radius: 10rpx;
  padding: 6rpx;
  margin-bottom: 30rpx;
}

.tab-item {
  flex: 1;
  text-align: center;
  padding: 20rpx;
  font-size: 28rpx;
  color: #666;
  border-radius: 8rpx;
  transition: all 0.3s ease;
}

.tab-item.active {
  background: white;
  color: #4f46e5;
  font-weight: 600;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.tab-content {
  margin-top: 20rpx;
}

.test-results {
  margin-top: 30rpx;
  padding: 30rpx;
  background: white;
  border-radius: 20rpx;
}

.results-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 20rpx;
}

.result-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx;
  margin-bottom: 10rpx;
  background: #f8f9fa;
  border-radius: 10rpx;
}

.result-time {
  font-size: 24rpx;
  color: #999;
}

.result-action {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
}

.result-type {
  font-size: 26rpx;
  color: #4f46e5;
}
</style>
