"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  __name: "MerchantRecommend",
  setup(__props) {
    const currentMerchantPage = common_vendor.ref(0);
    const recommendBusinessList = common_vendor.ref([
      {
        id: 1,
        logo: "/static/images/merchant/logo1.jpg",
        name: "品味小厨",
        description: "特色家常菜 | 人均¥38",
        category: "餐饮",
        isFollowed: false,
        url: "/pages/business/shop-detail?id=1",
        rating: "4.9",
        distance: "350m",
        isHot: true
      },
      {
        id: 2,
        logo: "/static/images/merchant/logo2.jpg",
        name: "鲜花花店",
        description: "鲜花礼品 | 同城配送",
        category: "礼品",
        isFollowed: true,
        url: "/pages/business/shop-detail?id=2",
        rating: "4.7",
        distance: "1.2km",
        isNew: true
      },
      {
        id: 3,
        logo: "/static/images/merchant/logo3.jpg",
        name: "快修先生",
        description: "专业维修 | 上门服务",
        category: "维修",
        isFollowed: false,
        url: "/pages/business/shop-detail?id=3",
        rating: "4.8",
        distance: "800m"
      },
      {
        id: 4,
        logo: "/static/images/merchant/logo4.jpg",
        name: "鲜果超市",
        description: "新鲜水果 | 当日配送",
        category: "生鲜",
        isFollowed: false,
        url: "/pages/business/shop-detail?id=4",
        rating: "4.6",
        distance: "650m",
        isHot: true
      },
      {
        id: 5,
        logo: "/static/images/merchant/logo1.jpg",
        name: "川湘小馆",
        description: "川湘菜 | 人均¥45",
        category: "餐饮",
        isFollowed: false,
        url: "/pages/business/shop-detail?id=5",
        rating: "4.5",
        distance: "1.5km"
      },
      {
        id: 6,
        logo: "/static/images/merchant/logo2.jpg",
        name: "优品超市",
        description: "日用百货 | 满减活动",
        category: "超市",
        isFollowed: false,
        url: "/pages/business/shop-detail?id=6",
        rating: "4.7",
        distance: "800m",
        isNew: true
      }
    ]);
    function getMerchantsForPage(pageIndex) {
      const startIndex = pageIndex * 2;
      return recommendBusinessList.value.slice(startIndex, startIndex + 2);
    }
    function onMerchantPageChange(e) {
      currentMerchantPage.value = e.detail.current;
    }
    function changeMerchantPage(index) {
      currentMerchantPage.value = index;
    }
    function navigateToMerchant(item) {
      if (!item.url)
        return;
      common_vendor.index.navigateTo({
        url: item.url,
        fail: () => {
          common_vendor.index.showToast({
            title: "页面跳转失败",
            icon: "none"
          });
        }
      });
    }
    function navigateToMore() {
      common_vendor.index.navigateTo({
        url: "/pages/business/list",
        fail: () => {
          common_vendor.index.showToast({
            title: "页面跳转失败",
            icon: "none"
          });
        }
      });
    }
    return (_ctx, _cache) => {
      return {
        a: common_vendor.o(navigateToMore),
        b: common_vendor.f(Math.ceil(recommendBusinessList.value.length / 2), (pageIndex, k0, i0) => {
          return {
            a: common_vendor.f(getMerchantsForPage(pageIndex - 1), (merchant, k1, i1) => {
              return common_vendor.e({
                a: merchant.logo,
                b: merchant.isNew
              }, merchant.isNew ? {} : {}, {
                c: merchant.isHot
              }, merchant.isHot ? {} : {}, {
                d: common_vendor.t(merchant.name),
                e: common_vendor.t(merchant.rating || "4.8"),
                f: common_vendor.t(merchant.description),
                g: common_vendor.t(merchant.category),
                h: common_vendor.t(merchant.distance || "500m"),
                i: merchant.id,
                j: common_vendor.o(($event) => navigateToMerchant(merchant), merchant.id)
              });
            }),
            b: "page-" + pageIndex
          };
        }),
        c: currentMerchantPage.value,
        d: common_vendor.o(onMerchantPageChange),
        e: common_vendor.f(Math.ceil(recommendBusinessList.value.length / 2), (dot, i, i0) => {
          return {
            a: i,
            b: currentMerchantPage.value === i ? 1 : "",
            c: common_vendor.o(($event) => changeMerchantPage(i), i)
          };
        })
      };
    };
  }
};
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-f925b123"]]);
wx.createComponent(Component);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/components/index/MerchantRecommend.js.map
