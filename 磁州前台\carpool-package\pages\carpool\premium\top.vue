<template>
  <view class="top-container">
    <!-- 导航栏 -->
    <carpool-nav title="置顶信息"></carpool-nav>
    
    <!-- 页面主体 -->
    <view class="top-content">
      <!-- 信息卡片 -->
      <view class="info-card">
        <view class="card-header">
          <text class="card-title">您的拼车信息</text>
          <view class="info-tag" :class="typeClass">{{typeText}}</view>
        </view>
        
        <view class="route-section">
          <view class="route-points">
            <view class="route-point">
              <view class="point-dot start"></view>
              <text class="point-text">{{infoData.startPoint}}</text>
            </view>
            <view class="route-divider">
              <view class="divider-line"></view>
              <view class="divider-arrow">
                <image src="/static/images/tabbar/arrow-right.png" mode="aspectFit" class="arrow-icon"></image>
              </view>
            </view>
            <view class="route-point">
              <view class="point-dot end"></view>
              <text class="point-text">{{infoData.endPoint}}</text>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 拼车置顶推广操作 -->
      <view class="premium-card">
        <view class="premium-header">
          <image src="/static/images/tabbar/crown.png" mode="aspectFit" class="premium-icon"></image>
          <text class="premium-title">置顶服务</text>
        </view>

        <view class="premium-desc">
          <text>置顶后您的信息将会展示在列表顶部，获得更多曝光</text>
        </view>

        <ConfigurablePremiumActions
          pageType="carpool_top"
          showMode="direct"
          :itemData="carpoolData"
          @action-completed="handleTopCompleted"
          @action-cancelled="handleTopCancelled"
        />
      </view>
      
      <!-- 温馨提示 -->
      <view class="tips-card">
        <view class="tips-header">
          <image src="/static/images/tabbar/info.png" mode="aspectFit" class="tips-icon"></image>
          <text class="tips-title">温馨提示</text>
        </view>
        <view class="tips-content">
          <text class="tips-text">· 置顶服务在您支付成功后立即生效</text>
          <text class="tips-text">· 置顶期间，您的信息将始终保持在列表顶部</text>
          <text class="tips-text">· 如有疑问，请联系客服：************</text>
        </view>
      </view>
    </view>
    

  </view>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import ConfigurablePremiumActions from '@/components/premium/ConfigurablePremiumActions.vue';

// 信息ID
const infoId = ref('');

// 信息数据
const infoData = ref({
  startPoint: '磁州城区',
  endPoint: '邯郸站',
  type: 'people-to-car'
});

// 拼车置顶数据
const carpoolData = ref({
  id: infoId.value || 'carpool_123',
  title: '拼车置顶',
  description: `${infoData.value.startPoint} → ${infoData.value.endPoint}`,
  type: infoData.value.type
});

// 置顶选项
const topOptions = ref([
  {
    title: '置顶1天',
    desc: '24小时置顶显示',
    price: '2.00',
    days: 1
  },
  {
    title: '置顶3天',
    desc: '72小时置顶显示',
    price: '5.00',
    days: 3
  },
  {
    title: '置顶7天',
    desc: '7天置顶显示',
    price: '10.00',
    days: 7
  }
]);

// 支付方式
const paymentMethods = ref([
  {
    name: '微信支付',
    icon: '/static/images/tabbar/wechat-pay.png',
    id: 'wxpay'
  },
  {
    name: '余额支付',
    icon: '/static/images/tabbar/wallet.png',
    id: 'balance'
  }
]);

// 选中的选项
const selectedOption = ref(1); // 默认选中3天
const selectedPayment = ref(0); // 默认选中微信支付

// 计算属性
const typeText = computed(() => {
  const typeMap = {
    'people-to-car': '人找车',
    'car-to-people': '车找人',
    'goods-to-car': '货找车',
    'car-to-goods': '车找货'
  };
  return typeMap[infoData.value.type] || '人找车';
});

const typeClass = computed(() => {
  return infoData.value.type;
});

const selectedOptionPrice = computed(() => {
  return topOptions.value[selectedOption.value].price;
});

// 页面加载
onMounted(() => {
  const pages = getCurrentPages();
  const currentPage = pages[pages.length - 1];
  const options = currentPage.options || {};
  
  // 获取信息ID
  if (options && options.id) {
    infoId.value = options.id;
    getInfoDetail();
  }
});

// 获取信息详情
const getInfoDetail = () => {
  // 这里应该是真实的API调用
  // 目前使用模拟数据
  console.log('获取信息ID:', infoId.value);
  
  // 模拟获取数据
  setTimeout(() => {
    infoData.value = {
      startPoint: '磁州城区',
      endPoint: '邯郸站',
      type: 'car-to-people'
    };
  }, 500);
};

// 选择置顶选项
const selectOption = (index) => {
  selectedOption.value = index;
};

// 选择支付方式
const selectPayment = (index) => {
  selectedPayment.value = index;
};

// 处理置顶完成
const handleTopCompleted = (result) => {
  console.log('置顶完成:', result);

  if (result.type === 'ad') {
    // 看广告置顶成功
    uni.showToast({
      title: '置顶成功',
      icon: 'success',
      duration: 2000
    });
  } else if (result.type === 'payment') {
    // 付费置顶成功
    uni.showToast({
      title: '支付成功，置顶生效',
      icon: 'success',
      duration: 2000
    });
  }

  // 延迟跳转回详情页
  setTimeout(() => {
    uni.navigateBack();
  }, 2000);
};

// 处理置顶取消
const handleTopCancelled = (result) => {
  console.log('置顶取消:', result);
  if (result.type === 'ad') {
    uni.showToast({
      title: '已取消观看广告',
      icon: 'none'
    });
  } else if (result.type === 'payment') {
    uni.showToast({
      title: '已取消支付',
      icon: 'none'
    });
  }
};
</script>

<style lang="scss">
.top-container {
  min-height: 100vh;
  background-color: #f5f7fa;
  padding-bottom: 120rpx; /* 为底部支付栏留出空间 */
}

.top-content {
  padding: 30rpx;
  display: flex;
  flex-direction: column;
  gap: 30rpx;
}

/* 信息卡片 */
.info-card {
  background-color: #ffffff;
  border-radius: 24rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.04);
}

.card-header {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.card-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
}

.info-tag {
  margin-left: 15rpx;
  font-size: 22rpx;
  color: #ffffff;
  padding: 4rpx 12rpx;
  border-radius: 6rpx;
}

.people-to-car {
  background-color: #0A84FF;
}

.car-to-people {
  background-color: #FF453A;
}

.goods-to-car {
  background-color: #30D158;
}

.car-to-goods {
  background-color: #FF9F0A;
}

/* 路线信息 */
.route-section {
  margin-bottom: 10rpx;
}

.route-points {
  margin-bottom: 10rpx;
}

.route-point {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.point-dot {
  width: 16rpx;
  height: 16rpx;
  border-radius: 50%;
  margin-right: 15rpx;
  flex-shrink: 0;
}

.start {
  background-color: #0A84FF;
}

.end {
  background-color: #FF453A;
}

.point-text {
  font-size: 32rpx;
  color: #333333;
  font-weight: 500;
}

.route-divider {
  padding-left: 7rpx;
  margin: 10rpx 0;
  display: flex;
}

.divider-line {
  width: 2rpx;
  height: 30rpx;
  background-color: #dddddd;
}

.divider-arrow {
  margin-left: -7rpx;
  margin-top: 30rpx;
}

.arrow-icon {
  width: 24rpx;
  height: 24rpx;
  opacity: 0.6;
}

/* 置顶选项卡片 */
.premium-card {
  background-color: #ffffff;
  border-radius: 24rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.04);
}

.premium-header {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.premium-icon {
  width: 36rpx;
  height: 36rpx;
  margin-right: 15rpx;
}

.premium-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
}

.premium-desc {
  font-size: 28rpx;
  color: #666666;
  margin-bottom: 30rpx;
  line-height: 1.5;
}

.options-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.option-item {
  background-color: #f8f8f8;
  border-radius: 16rpx;
  padding: 24rpx;
  position: relative;
  transition: all 0.3s;
}

.option-selected {
  background-color: rgba(10, 132, 255, 0.05);
  border: 1rpx solid #0A84FF;
}

.option-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.option-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 8rpx;
}

.option-desc {
  font-size: 24rpx;
  color: #999999;
}

.option-price {
  font-size: 36rpx;
  font-weight: 700;
  color: #FF3B30;
}

.option-price::before {
  content: '¥';
  font-size: 24rpx;
  font-weight: 400;
}

.option-check {
  position: absolute;
  right: 24rpx;
  top: 50%;
  transform: translateY(-50%);
  width: 36rpx;
  height: 36rpx;
  border-radius: 50%;
  background-color: #0A84FF;
  display: flex;
  align-items: center;
  justify-content: center;
}

.check-icon {
  width: 20rpx;
  height: 20rpx;
}

/* 支付方式卡片 */
.payment-card {
  background-color: #ffffff;
  border-radius: 24rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.04);
}

.payment-header {
  margin-bottom: 20rpx;
}

.payment-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
}

.payment-options {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.payment-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx;
  background-color: #f8f8f8;
  border-radius: 16rpx;
  transition: all 0.3s;
}

.payment-selected {
  background-color: rgba(10, 132, 255, 0.05);
  border: 1rpx solid #0A84FF;
}

.payment-left {
  display: flex;
  align-items: center;
}

.payment-icon {
  width: 40rpx;
  height: 40rpx;
  margin-right: 20rpx;
}

.payment-name {
  font-size: 28rpx;
  color: #333333;
}

.payment-check {
  width: 36rpx;
  height: 36rpx;
  border-radius: 50%;
  background-color: #0A84FF;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 温馨提示 */
.tips-card {
  background-color: rgba(10, 132, 255, 0.05);
  border-radius: 24rpx;
  padding: 20rpx 30rpx;
}

.tips-header {
  display: flex;
  align-items: center;
  margin-bottom: 15rpx;
}

.tips-icon {
  width: 28rpx;
  height: 28rpx;
  margin-right: 10rpx;
  opacity: 0.7;
}

.tips-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333333;
}

.tips-content {
  display: flex;
  flex-direction: column;
  gap: 10rpx;
}

.tips-text {
  font-size: 24rpx;
  color: #666666;
  line-height: 1.5;
}

/* 底部支付栏 */
.bottom-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 120rpx;
  background-color: #ffffff;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 30rpx;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  z-index: 100;
}

.price-info {
  display: flex;
  align-items: center;
}

.price-label {
  font-size: 28rpx;
  color: #666666;
}

.price-value {
  font-size: 36rpx;
  font-weight: 700;
  color: #FF3B30;
}

.pay-button {
  height: 80rpx;
  border-radius: 40rpx;
  background: linear-gradient(135deg, #0A84FF, #0066CC);
  color: #ffffff;
  font-size: 30rpx;
  font-weight: 600;
  padding: 0 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 10rpx rgba(10, 132, 255, 0.3);
}
</style> 