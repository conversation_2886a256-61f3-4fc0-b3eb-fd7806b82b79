const set = require('regenerate')(0x3005, 0x3007);
set.addRange(0x2E80, 0x2E99).addRange(0x2E9B, 0x2EF3).addRange(0x2F00, 0x2FD5).addRange(0x3021, 0x3029).addRange(0x3038, 0x303B).addRange(0x3400, 0x4DBF).addRange(0x4E00, 0x9FFF).addRange(0xF900, 0xFA6D).addRange(0xFA70, 0xFAD9).addRange(0x16FE2, 0x16FE3).addRange(0x16FF0, 0x16FF1).addRange(0x20000, 0x2A6DF).addRange(0x2A700, 0x2B739).addRange(0x2B740, 0x2B81D).addRange(0x2B820, 0x2CEA1).addRange(0x2CEB0, 0x2EBE0).addRange(0x2EBF0, 0x2EE5D).addRange(0x2F800, 0x2FA1D).addRange(0x30000, 0x3134A).addRange(0x31350, 0x323AF);
exports.characters = set;
