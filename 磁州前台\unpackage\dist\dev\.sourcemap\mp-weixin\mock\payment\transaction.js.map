{"version": 3, "file": "transaction.js", "sources": ["mock/payment/transaction.js"], "sourcesContent": ["// 交易记录模拟数据\r\nexport const transactionList = [\r\n  {\r\n    id: 'trans-001',\r\n    type: 'expense', // income, expense, refund, withdraw, recharge\r\n    amount: 68.00,\r\n    balance: 1280.50,\r\n    title: '商家消费',\r\n    description: '磁县老味道餐厅',\r\n    time: '2024-03-15 12:30',\r\n    status: 'success', // success, pending, failed\r\n    paymentMethod: '钱包余额',\r\n    category: '餐饮美食',\r\n    orderNo: 'DD20240315123001',\r\n    icon: '/static/images/payment/restaurant.png'\r\n  },\r\n  {\r\n    id: 'trans-002',\r\n    type: 'recharge',\r\n    amount: 200.00,\r\n    balance: 1348.50,\r\n    title: '钱包充值',\r\n    description: '微信支付充值',\r\n    time: '2024-03-14 18:45',\r\n    status: 'success',\r\n    paymentMethod: '微信支付',\r\n    category: '充值',\r\n    orderNo: 'CZ20240314184501',\r\n    icon: '/static/images/payment/recharge.png'\r\n  },\r\n  {\r\n    id: 'trans-003',\r\n    type: 'expense',\r\n    amount: 45.50,\r\n    balance: 1148.50,\r\n    title: '商家消费',\r\n    description: '磁县优品生活超市',\r\n    time: '2024-03-13 10:20',\r\n    status: 'success',\r\n    paymentMethod: '钱包余额',\r\n    category: '日用百货',\r\n    orderNo: 'DD20240313102001',\r\n    icon: '/static/images/payment/shopping.png'\r\n  },\r\n  {\r\n    id: 'trans-004',\r\n    type: 'withdraw',\r\n    amount: 500.00,\r\n    balance: 1194.00,\r\n    title: '提现',\r\n    description: '提现至建设银行(3456)',\r\n    time: '2024-03-10 16:30',\r\n    status: 'success',\r\n    paymentMethod: '银行卡',\r\n    category: '提现',\r\n    orderNo: 'TX20240310163001',\r\n    icon: '/static/images/payment/withdraw.png'\r\n  },\r\n  {\r\n    id: 'trans-005',\r\n    type: 'income',\r\n    amount: 120.00,\r\n    balance: 1694.00,\r\n    title: '收入',\r\n    description: '二手商品出售',\r\n    time: '2024-03-08 14:15',\r\n    status: 'success',\r\n    paymentMethod: '钱包余额',\r\n    category: '二手交易',\r\n    orderNo: 'SR20240308141501',\r\n    icon: '/static/images/payment/income.png'\r\n  },\r\n  {\r\n    id: 'trans-006',\r\n    type: 'refund',\r\n    amount: 25.50,\r\n    balance: 1574.00,\r\n    title: '退款',\r\n    description: '商品退款-磁县优品生活超市',\r\n    time: '2024-03-07 09:40',\r\n    status: 'success',\r\n    paymentMethod: '钱包余额',\r\n    category: '退款',\r\n    orderNo: 'TK20240307094001',\r\n    icon: '/static/images/payment/refund.png'\r\n  }\r\n];\r\n\r\n// 交易分类\r\nexport const transactionCategories = [\r\n  { id: 'all', name: '全部' },\r\n  { id: 'expense', name: '支出' },\r\n  { id: 'income', name: '收入' },\r\n  { id: 'refund', name: '退款' },\r\n  { id: 'withdraw', name: '提现' },\r\n  { id: 'recharge', name: '充值' }\r\n];\r\n\r\n// 获取交易记录的API函数\r\nexport const fetchTransactionList = (type = 'all', page = 1, pageSize = 10) => {\r\n  return new Promise((resolve) => {\r\n    setTimeout(() => {\r\n      let result = [...transactionList];\r\n      \r\n      // 按类型筛选\r\n      if (type !== 'all') {\r\n        result = result.filter(item => item.type === type);\r\n      }\r\n      \r\n      // 分页处理\r\n      const start = (page - 1) * pageSize;\r\n      const end = start + pageSize;\r\n      const data = result.slice(start, end);\r\n      \r\n      // 返回数据和分页信息\r\n      resolve({\r\n        list: data,\r\n        total: result.length,\r\n        hasMore: end < result.length\r\n      });\r\n    }, 500);\r\n  });\r\n};\r\n\r\n// 获取交易详情的API函数\r\nexport const fetchTransactionDetail = (id) => {\r\n  return new Promise((resolve) => {\r\n    setTimeout(() => {\r\n      const transaction = transactionList.find(item => item.id === id);\r\n      \r\n      if (transaction) {\r\n        // 添加更多详细信息\r\n        const detailedTransaction = {\r\n          ...transaction,\r\n          merchant: {\r\n            name: transaction.description,\r\n            address: '磁县城区幸福路123号',\r\n            phone: '0310-12345678'\r\n          },\r\n          items: [\r\n            {\r\n              name: '商品1',\r\n              price: transaction.amount * 0.6,\r\n              quantity: 1\r\n            },\r\n            {\r\n              name: '商品2',\r\n              price: transaction.amount * 0.4,\r\n              quantity: 2\r\n            }\r\n          ],\r\n          paymentTime: transaction.time,\r\n          completeTime: transaction.status === 'success' ? transaction.time : null,\r\n          remark: '无'\r\n        };\r\n        \r\n        resolve({\r\n          success: true,\r\n          data: detailedTransaction\r\n        });\r\n      } else {\r\n        resolve({\r\n          success: false,\r\n          message: '交易记录不存在'\r\n        });\r\n      }\r\n    }, 500);\r\n  });\r\n};\r\n\r\n// 获取交易统计的API函数\r\nexport const fetchTransactionStats = (month) => {\r\n  return new Promise((resolve) => {\r\n    setTimeout(() => {\r\n      // 模拟统计数据\r\n      const stats = {\r\n        totalExpense: 139.00,\r\n        totalIncome: 120.00,\r\n        totalRefund: 25.50,\r\n        totalWithdraw: 500.00,\r\n        totalRecharge: 200.00,\r\n        categoryStats: [\r\n          { category: '餐饮美食', amount: 68.00, percentage: 48.9 },\r\n          { category: '日用百货', amount: 45.50, percentage: 32.7 },\r\n          { category: '其他', amount: 25.50, percentage: 18.4 }\r\n        ],\r\n        dailyStats: [\r\n          { date: '03-07', expense: 0, income: 0, refund: 25.50 },\r\n          { date: '03-08', expense: 0, income: 120.00, refund: 0 },\r\n          { date: '03-09', expense: 0, income: 0, refund: 0 },\r\n          { date: '03-10', expense: 0, income: 0, refund: 0, withdraw: 500.00 },\r\n          { date: '03-11', expense: 0, income: 0, refund: 0 },\r\n          { date: '03-12', expense: 0, income: 0, refund: 0 },\r\n          { date: '03-13', expense: 45.50, income: 0, refund: 0 },\r\n          { date: '03-14', expense: 0, income: 0, refund: 0, recharge: 200.00 },\r\n          { date: '03-15', expense: 68.00, income: 0, refund: 0 }\r\n        ]\r\n      };\r\n      \r\n      resolve(stats);\r\n    }, 800);\r\n  });\r\n}; "], "names": [], "mappings": ";AACO,MAAM,kBAAkB;AAAA,EAC7B;AAAA,IACE,IAAI;AAAA,IACJ,MAAM;AAAA;AAAA,IACN,QAAQ;AAAA,IACR,SAAS;AAAA,IACT,OAAO;AAAA,IACP,aAAa;AAAA,IACb,MAAM;AAAA,IACN,QAAQ;AAAA;AAAA,IACR,eAAe;AAAA,IACf,UAAU;AAAA,IACV,SAAS;AAAA,IACT,MAAM;AAAA,EACP;AAAA,EACD;AAAA,IACE,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,SAAS;AAAA,IACT,OAAO;AAAA,IACP,aAAa;AAAA,IACb,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,eAAe;AAAA,IACf,UAAU;AAAA,IACV,SAAS;AAAA,IACT,MAAM;AAAA,EACP;AAAA,EACD;AAAA,IACE,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,SAAS;AAAA,IACT,OAAO;AAAA,IACP,aAAa;AAAA,IACb,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,eAAe;AAAA,IACf,UAAU;AAAA,IACV,SAAS;AAAA,IACT,MAAM;AAAA,EACP;AAAA,EACD;AAAA,IACE,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,SAAS;AAAA,IACT,OAAO;AAAA,IACP,aAAa;AAAA,IACb,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,eAAe;AAAA,IACf,UAAU;AAAA,IACV,SAAS;AAAA,IACT,MAAM;AAAA,EACP;AAAA,EACD;AAAA,IACE,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,SAAS;AAAA,IACT,OAAO;AAAA,IACP,aAAa;AAAA,IACb,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,eAAe;AAAA,IACf,UAAU;AAAA,IACV,SAAS;AAAA,IACT,MAAM;AAAA,EACP;AAAA,EACD;AAAA,IACE,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,SAAS;AAAA,IACT,OAAO;AAAA,IACP,aAAa;AAAA,IACb,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,eAAe;AAAA,IACf,UAAU;AAAA,IACV,SAAS;AAAA,IACT,MAAM;AAAA,EACP;AACH;AAaY,MAAC,uBAAuB,CAAC,OAAO,OAAO,OAAO,GAAG,WAAW,OAAO;AAC7E,SAAO,IAAI,QAAQ,CAAC,YAAY;AAC9B,eAAW,MAAM;AACf,UAAI,SAAS,CAAC,GAAG,eAAe;AAGhC,UAAI,SAAS,OAAO;AAClB,iBAAS,OAAO,OAAO,UAAQ,KAAK,SAAS,IAAI;AAAA,MAClD;AAGD,YAAM,SAAS,OAAO,KAAK;AAC3B,YAAM,MAAM,QAAQ;AACpB,YAAM,OAAO,OAAO,MAAM,OAAO,GAAG;AAGpC,cAAQ;AAAA,QACN,MAAM;AAAA,QACN,OAAO,OAAO;AAAA,QACd,SAAS,MAAM,OAAO;AAAA,MAC9B,CAAO;AAAA,IACF,GAAE,GAAG;AAAA,EACV,CAAG;AACH;AAGY,MAAC,yBAAyB,CAAC,OAAO;AAC5C,SAAO,IAAI,QAAQ,CAAC,YAAY;AAC9B,eAAW,MAAM;AACf,YAAM,cAAc,gBAAgB,KAAK,UAAQ,KAAK,OAAO,EAAE;AAE/D,UAAI,aAAa;AAEf,cAAM,sBAAsB;AAAA,UAC1B,GAAG;AAAA,UACH,UAAU;AAAA,YACR,MAAM,YAAY;AAAA,YAClB,SAAS;AAAA,YACT,OAAO;AAAA,UACR;AAAA,UACD,OAAO;AAAA,YACL;AAAA,cACE,MAAM;AAAA,cACN,OAAO,YAAY,SAAS;AAAA,cAC5B,UAAU;AAAA,YACX;AAAA,YACD;AAAA,cACE,MAAM;AAAA,cACN,OAAO,YAAY,SAAS;AAAA,cAC5B,UAAU;AAAA,YACX;AAAA,UACF;AAAA,UACD,aAAa,YAAY;AAAA,UACzB,cAAc,YAAY,WAAW,YAAY,YAAY,OAAO;AAAA,UACpE,QAAQ;AAAA,QAClB;AAEQ,gBAAQ;AAAA,UACN,SAAS;AAAA,UACT,MAAM;AAAA,QAChB,CAAS;AAAA,MACT,OAAa;AACL,gBAAQ;AAAA,UACN,SAAS;AAAA,UACT,SAAS;AAAA,QACnB,CAAS;AAAA,MACF;AAAA,IACF,GAAE,GAAG;AAAA,EACV,CAAG;AACH;AAGY,MAAC,wBAAwB,CAAC,UAAU;AAC9C,SAAO,IAAI,QAAQ,CAAC,YAAY;AAC9B,eAAW,MAAM;AAEf,YAAM,QAAQ;AAAA,QACZ,cAAc;AAAA,QACd,aAAa;AAAA,QACb,aAAa;AAAA,QACb,eAAe;AAAA,QACf,eAAe;AAAA,QACf,eAAe;AAAA,UACb,EAAE,UAAU,QAAQ,QAAQ,IAAO,YAAY,KAAM;AAAA,UACrD,EAAE,UAAU,QAAQ,QAAQ,MAAO,YAAY,KAAM;AAAA,UACrD,EAAE,UAAU,MAAM,QAAQ,MAAO,YAAY,KAAM;AAAA,QACpD;AAAA,QACD,YAAY;AAAA,UACV,EAAE,MAAM,SAAS,SAAS,GAAG,QAAQ,GAAG,QAAQ,KAAO;AAAA,UACvD,EAAE,MAAM,SAAS,SAAS,GAAG,QAAQ,KAAQ,QAAQ,EAAG;AAAA,UACxD,EAAE,MAAM,SAAS,SAAS,GAAG,QAAQ,GAAG,QAAQ,EAAG;AAAA,UACnD,EAAE,MAAM,SAAS,SAAS,GAAG,QAAQ,GAAG,QAAQ,GAAG,UAAU,IAAQ;AAAA,UACrE,EAAE,MAAM,SAAS,SAAS,GAAG,QAAQ,GAAG,QAAQ,EAAG;AAAA,UACnD,EAAE,MAAM,SAAS,SAAS,GAAG,QAAQ,GAAG,QAAQ,EAAG;AAAA,UACnD,EAAE,MAAM,SAAS,SAAS,MAAO,QAAQ,GAAG,QAAQ,EAAG;AAAA,UACvD,EAAE,MAAM,SAAS,SAAS,GAAG,QAAQ,GAAG,QAAQ,GAAG,UAAU,IAAQ;AAAA,UACrE,EAAE,MAAM,SAAS,SAAS,IAAO,QAAQ,GAAG,QAAQ,EAAG;AAAA,QACxD;AAAA,MACT;AAEM,cAAQ,KAAK;AAAA,IACd,GAAE,GAAG;AAAA,EACV,CAAG;AACH;;;;"}