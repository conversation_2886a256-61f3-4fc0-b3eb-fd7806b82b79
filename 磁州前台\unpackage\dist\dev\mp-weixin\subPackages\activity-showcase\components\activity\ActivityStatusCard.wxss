/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 移除阿里妈妈字体引用，改用系统默认字体 */
/* 移除原有阿里妈妈字体定义
$uni-font-family: 'AlimamaShuHeiTi';
$uni-font-family-text: 'AlimamaShuHeiTi', -apple-system, BlinkMacSystemFont, 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;
*/
body.data-v-abc6c209, html.data-v-abc6c209, #app.data-v-abc6c209, .index-container.data-v-abc6c209 {
  font-family: "AlimamaShuHeiTi", -apple-system, BlinkMacSystemFont, "Helvetica Neue", "PingFang SC", "Microsoft YaHei", sans-serif;
}
.activity-status-card.data-v-abc6c209 {
  width: 100%;
  border-radius: 24px;
  background-color: #FFFFFF;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08), 0 0 0 1px rgba(0, 0, 0, 0.02);
  margin-bottom: 30rpx;
  overflow: hidden;
  position: relative;
  transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}
.activity-status-card.data-v-abc6c209:active {
  transform: scale(0.98) !important;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05), 0 0 0 1px rgba(0, 0, 0, 0.03);
}
.card-image-container.data-v-abc6c209 {
  width: 100%;
  height: 360rpx;
  position: relative;
  overflow: hidden;
}
.card-image-container .card-image.data-v-abc6c209 {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.5s ease;
}
.card-image-container .card-image.data-v-abc6c209:hover {
  transform: scale(1.05);
}
.card-image-container .card-tag.data-v-abc6c209 {
  position: absolute;
  top: 20rpx;
  left: 20rpx;
  padding: 8rpx 20rpx;
  font-size: 22rpx;
  font-weight: 600;
  color: #FFFFFF;
  border-radius: 20rpx;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.15);
  z-index: 10;
}
.card-image-container .favorite-btn.data-v-abc6c209 {
  position: absolute;
  top: 20rpx;
  right: 20rpx;
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
  z-index: 10;
  color: #FF3B69;
}
.card-image-container .favorite-btn.data-v-abc6c209:active {
  transform: scale(0.9);
}
.card-image-container .countdown-container.data-v-abc6c209 {
  position: absolute;
  bottom: 20rpx;
  right: 20rpx;
  background: rgba(0, 0, 0, 0.7);
  padding: 10rpx 20rpx;
  border-radius: 20rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
  z-index: 10;
}
.card-image-container .countdown-container .countdown-label.data-v-abc6c209 {
  font-size: 20rpx;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 4rpx;
}
.card-image-container .countdown-container .countdown-timer.data-v-abc6c209 {
  display: flex;
  align-items: center;
}
.card-image-container .countdown-container .countdown-timer .time-block.data-v-abc6c209 {
  width: 40rpx;
  height: 40rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 6rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  font-weight: 600;
  color: #FFFFFF;
}
.card-image-container .countdown-container .countdown-timer .time-separator.data-v-abc6c209 {
  margin: 0 4rpx;
  color: #FFFFFF;
  font-weight: 600;
}
.card-image-container .status-indicator.data-v-abc6c209 {
  position: absolute;
  top: 20rpx;
  right: 100rpx;
  padding: 8rpx 20rpx;
  border-radius: 20rpx;
  font-size: 22rpx;
  font-weight: 600;
  display: flex;
  align-items: center;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
  z-index: 10;
}
.card-image-container .status-indicator .status-dot.data-v-abc6c209 {
  width: 8rpx;
  height: 8rpx;
  border-radius: 50%;
  margin-right: 8rpx;
}
.card-info.data-v-abc6c209 {
  padding: 30rpx;
  position: relative;
}
.card-info .card-header.data-v-abc6c209 {
  margin-bottom: 20rpx;
}
.card-info .card-header .card-title-container .card-title.data-v-abc6c209 {
  font-size: 32rpx;
  font-weight: 700;
  color: #333333;
  margin-bottom: 10rpx;
  line-height: 1.4;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}
.card-info .card-header .card-title-container .card-shop.data-v-abc6c209 {
  display: flex;
  align-items: center;
  font-size: 24rpx;
  color: #8E8E93;
}
.card-info .card-header .card-title-container .card-shop .shop-icon.data-v-abc6c209 {
  margin-right: 6rpx;
  color: #8E8E93;
}
.card-info .card-details.data-v-abc6c209 {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 20rpx;
}
.card-info .card-details .price-section.data-v-abc6c209 {
  flex: 1;
  margin-right: 20rpx;
  margin-bottom: 16rpx;
}
.card-info .card-details .price-section .current-price.data-v-abc6c209 {
  display: flex;
  align-items: baseline;
}
.card-info .card-details .price-section .current-price .price-symbol.data-v-abc6c209 {
  font-size: 24rpx;
  color: #FF3B69;
  margin-right: 4rpx;
}
.card-info .card-details .price-section .current-price .price-value.data-v-abc6c209 {
  font-size: 40rpx;
  font-weight: 700;
  color: #FF3B69;
  line-height: 1;
}
.card-info .card-details .price-section .price-info.data-v-abc6c209 {
  display: flex;
  align-items: center;
  margin-top: 8rpx;
}
.card-info .card-details .price-section .price-info .price-original.data-v-abc6c209 {
  font-size: 24rpx;
  color: #8E8E93;
  text-decoration: line-through;
  margin-right: 10rpx;
}
.card-info .card-details .price-section .price-info .discount-tag.data-v-abc6c209 {
  padding: 4rpx 10rpx;
  background-color: rgba(255, 59, 105, 0.1);
  border-radius: 10rpx;
  font-size: 20rpx;
  color: #FF3B69;
  font-weight: 600;
}
.card-info .card-details .activity-time.data-v-abc6c209 {
  display: flex;
  align-items: center;
  font-size: 24rpx;
  color: #8E8E93;
  margin-bottom: 16rpx;
}
.card-info .card-details .activity-time .time-icon.data-v-abc6c209 {
  margin-right: 6rpx;
  color: #8E8E93;
}
.card-info .card-details .participants-info.data-v-abc6c209 {
  display: flex;
  align-items: center;
  margin-top: 16rpx;
}
.card-info .card-details .participants-info .avatar-group.data-v-abc6c209 {
  position: relative;
  height: 50rpx;
  margin-right: 20rpx;
}
.card-info .card-details .participants-info .avatar-group .participant-avatar.data-v-abc6c209 {
  width: 50rpx;
  height: 50rpx;
  border-radius: 50%;
  border: 2rpx solid #FFFFFF;
  position: absolute;
  top: 0;
}
.card-info .card-details .participants-info .participant-count.data-v-abc6c209 {
  font-size: 24rpx;
  color: #8E8E93;
}
.card-info .card-actions.data-v-abc6c209 {
  display: flex;
  margin-top: 20rpx;
}
.card-info .card-actions .action-btn.data-v-abc6c209 {
  height: 70rpx;
  border-radius: 35rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 30rpx;
  margin-right: 20rpx;
  font-size: 26rpx;
  font-weight: 600;
  transition: all 0.2s ease;
}
.card-info .card-actions .action-btn .icon.data-v-abc6c209 {
  margin-right: 8rpx;
}
.card-info .card-actions .action-btn.data-v-abc6c209:active {
  transform: scale(0.95);
}
.card-info .card-actions .primary-btn.data-v-abc6c209 {
  background: linear-gradient(135deg, #FF3B69 0%, #FF7A9E 100%);
  color: #FFFFFF;
  box-shadow: 0 4px 10px rgba(255, 59, 105, 0.3);
  flex: 1;
}
.card-info .card-actions .share-btn.data-v-abc6c209 {
  background-color: rgba(255, 59, 105, 0.1);
  color: #FF3B69;
}
.card-info .card-actions .cancel-btn.data-v-abc6c209 {
  background-color: rgba(142, 142, 147, 0.1);
  color: #8E8E93;
}
.card-info .progress-container.data-v-abc6c209 {
  margin-top: 20rpx;
}
.card-info .progress-container .progress-bar.data-v-abc6c209 {
  width: 100%;
  height: 10rpx;
  background-color: rgba(0, 0, 0, 0.05);
  border-radius: 5rpx;
  overflow: hidden;
}
.card-info .progress-container .progress-bar .progress-fill.data-v-abc6c209 {
  height: 100%;
  background: linear-gradient(90deg, #FF3B69 0%, #FF7A9E 100%);
  border-radius: 5rpx;
  transition: width 0.5s ease;
}
.card-info .progress-container .progress-text.data-v-abc6c209 {
  display: flex;
  justify-content: space-between;
  margin-top: 8rpx;
  font-size: 22rpx;
  color: #8E8E93;
}

/* 标签样式 */
.tag-flash.data-v-abc6c209 {
  background: linear-gradient(135deg, #FF3B30 0%, #FF5E3A 100%);
}
.tag-group.data-v-abc6c209 {
  background: linear-gradient(135deg, #34C759 0%, #30D158 100%);
}
.tag-discount.data-v-abc6c209 {
  background: linear-gradient(135deg, #5856D6 0%, #5E5CE6 100%);
}
.tag-coupon.data-v-abc6c209 {
  background: linear-gradient(135deg, #FF9500 0%, #FFCC00 100%);
}

/* 状态样式 */
.status-ongoing.data-v-abc6c209 {
  color: #34C759;
  background-color: rgba(52, 199, 89, 0.9);
  color: #FFFFFF;
}
.status-ongoing .status-dot.data-v-abc6c209 {
  background-color: #FFFFFF;
  box-shadow: 0 0 0 2rpx rgba(255, 255, 255, 0.5);
  animation: pulse-abc6c209 2s infinite;
}
.status-registered.data-v-abc6c209 {
  background-color: rgba(255, 59, 105, 0.9);
  color: #FFFFFF;
}
.status-registered .status-dot.data-v-abc6c209 {
  background-color: #FFFFFF;
}
.status-completed.data-v-abc6c209 {
  background-color: rgba(142, 142, 147, 0.9);
  color: #FFFFFF;
}
.status-completed .status-dot.data-v-abc6c209 {
  background-color: #FFFFFF;
}
.status-favorite.data-v-abc6c209 {
  background-color: rgba(255, 149, 0, 0.9);
  color: #FFFFFF;
}
.status-favorite .status-dot.data-v-abc6c209 {
  background-color: #FFFFFF;
}
.status-cancelled.data-v-abc6c209 {
  background-color: rgba(255, 59, 48, 0.9);
  color: #FFFFFF;
}
.status-cancelled .status-dot.data-v-abc6c209 {
  background-color: #FFFFFF;
}
@keyframes pulse-abc6c209 {
0% {
    transform: scale(1);
    opacity: 1;
}
50% {
    transform: scale(1.5);
    opacity: 0.5;
}
100% {
    transform: scale(1);
    opacity: 1;
}
}