// 海报模板
const posterTemplates = ref([
  {
    name: '商品推广',
    thumb: '/static/images/distribution/poster-thumb-1.png',
    url: '/static/images/distribution/poster-1.png',
    type: 'product'
  },
  {
    name: '店铺推广',
    thumb: '/static/images/distribution/poster-thumb-2.png',
    url: '/static/images/distribution/poster-2.png',
    type: 'store'
  },
  {
    name: '活动推广',
    thumb: '/static/images/distribution/poster-thumb-3.png',
    url: '/static/images/distribution/poster-3.png',
    type: 'activity'
  },
  {
    name: '会员推广',
    thumb: '/static/images/distribution/poster-thumb-4.png',
    url: '/static/images/distribution/poster-4.png',
    type: 'member'
  },
  {
    name: '节日主题',
    thumb: '/static/images/distribution/poster-thumb-5.png',
    url: '/static/images/distribution/poster-5.png',
    type: 'general'
  }
]);

// 特定类型的模板
const typeTemplates = {
  'carpool': [
    {
      name: '拼车专用',
      thumb: '/static/images/distribution/carpool-thumb-1.png',
      url: '/static/images/distribution/carpool-1.png',
      type: 'carpool'
    }
  ],
  'secondhand': [
    {
      name: '二手商品专用',
      thumb: '/static/images/distribution/secondhand-thumb-1.png',
      url: '/static/images/distribution/secondhand-1.png',
      type: 'secondhand'
    }
  ],
  'house': [
    {
      name: '房屋租售专用',
      thumb: '/static/images/distribution/house-thumb-1.png',
      url: '/static/images/distribution/house-1.png',
      type: 'house'
    }
  ]
};

// 主题颜色
const themeColors = [
  { bgColor: '#FFFFFF', textColor: '#333333' },
  { bgColor: '#3846CD', textColor: '#FFFFFF' },
  { bgColor: '#2C3AA0', textColor: '#FFFFFF' },
  { bgColor: '#F5F7FA', textColor: '#333333' },
  { bgColor: '#FFE8F0', textColor: '#FF3B30' },
  { bgColor: '#E8F8FF', textColor: '#007AFF' }
];

// 状态变量
const currentPosterIndex = ref(0);
const selectedTheme = ref(themeColors[0]);
const showId = ref(true);
const showPromoText = ref(false);
const customText = ref('');

// 当前海报
const currentPoster = computed(() => {
  if (posterTemplates.value.length === 0) return '';
  return posterTemplates.value[currentPosterIndex.value].url;
});

// 加载海报模板
const loadPosterTemplates = () => {
  // 获取特定类型的模板
  const typeSpecificTemplates = typeTemplates[contentType.value] || [];
  
  // 合并模板
  posterTemplates.value = [
    ...typeSpecificTemplates,
    ...posterTemplates.value.filter(template => 
      template.type === 'general' || template.type === contentType.value
    )
  ];
};

// 选择海报模板
const selectPoster = (index) => {
  currentPosterIndex.value = index;
  generatePoster();
};

// 选择主题颜色
const selectTheme = (theme) => {
  selectedTheme.value = theme;
  generatePoster();
};

// 切换显示推广员ID
const toggleId = (e) => {
  showId.value = e.detail.value;
  generatePoster();
};

// 切换显示推广文案
const togglePromoText = (e) => {
  showPromoText.value = e.detail.value;
  if (showPromoText.value && !customText.value) {
    generateDefaultText();
  }
  generatePoster();
};

// 生成默认文案
const generateDefaultText = () => {
  switch(contentType.value) {
    case 'carpool':
      customText.value = `【${previewData.title}】${previewData.departure}→${previewData.destination}，出发时间：${previewData.departureTime}`;
      break;
    case 'product':
      customText.value = `【${previewData.title}】价格：¥${previewData.price}`;
      break;
    case 'house':
      customText.value = `【${previewData.title}】${previewData.roomType}，${previewData.area}㎡，${previewData.price}${previewData.priceUnit}`;
      break;
    default:
      customText.value = `【${previewData.title}】`;
  }
};

// 显示帮助
const showHelp = () => {
  uni.showModal({
    title: '推广海报使用帮助',
    content: '选择喜欢的海报模板，自定义颜色和文案，生成精美海报进行分享推广。',
    showCancel: false
  });
}; 