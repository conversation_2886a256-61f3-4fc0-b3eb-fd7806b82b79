{"version": 3, "file": "channels.js", "sources": ["subPackages/merchant-admin-marketing/pages/marketing/distribution/channels.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/c3ViUGFja2FnZXNcbWVyY2hhbnQtYWRtaW4tbWFya2V0aW5nXHBhZ2VzXG1hcmtldGluZ1xkaXN0cmlidXRpb25cY2hhbm5lbHMudnVl"], "sourcesContent": ["<template>\r\n  <view class=\"channels-container\">\r\n    <!-- 自定义导航栏 -->\r\n    <view class=\"navbar\">\r\n      <view class=\"navbar-back\" @click=\"goBack\">\r\n        <view class=\"back-icon\"></view>\r\n      </view>\r\n      <text class=\"navbar-title\">分销员渠道</text>\r\n      <view class=\"navbar-right\">\r\n        <view class=\"add-icon\" @click=\"addChannel\">\r\n          <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n            <path d=\"M12 5V19M5 12H19\" stroke=\"white\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\r\n          </svg>\r\n        </view>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 渠道数据概览 -->\r\n    <view class=\"overview-section\">\r\n      <view class=\"overview-header\">\r\n        <text class=\"overview-title\">渠道数据概览</text>\r\n        <view class=\"date-filter\" @click=\"showDatePicker\">\r\n          <text>{{dateRange}}</text>\r\n          <view class=\"filter-arrow\">\r\n            <svg width=\"12\" height=\"12\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n              <path d=\"M6 9L12 15L18 9\" stroke=\"#6B0FBE\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\r\n            </svg>\r\n          </view>\r\n        </view>\r\n      </view>\r\n      \r\n      <!-- 数据卡片 -->\r\n      <view class=\"data-cards\">\r\n        <view class=\"data-card\">\r\n          <view class=\"card-value\">{{channelStats.totalChannels}}</view>\r\n          <view class=\"card-label\">分销员总数</view>\r\n          <view class=\"card-trend\" :class=\"channelStats.channelsTrend\">\r\n            <view class=\"trend-icon\"></view>\r\n            <text>{{channelStats.channelsGrowth}}</text>\r\n          </view>\r\n          <view class=\"card-icon distributor-icon\">\r\n            <svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n              <path d=\"M17 21V19C17 17.9391 16.5786 16.9217 15.8284 16.1716C15.0783 15.4214 14.0609 15 13 15H5C3.93913 15 2.92172 15.4214 2.17157 16.1716C1.42143 16.9217 1 17.9391 1 19V21\" stroke=\"#6B0FBE\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\r\n              <path d=\"M9 11C11.2091 11 13 9.20914 13 7C13 4.79086 11.2091 3 9 3C6.79086 3 5 4.79086 5 7C5 9.20914 6.79086 11 9 11Z\" stroke=\"#6B0FBE\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\r\n              <path d=\"M23 21V19C22.9993 18.1137 22.7044 17.2528 22.1614 16.5523C21.6184 15.8519 20.8581 15.3516 20 15.13\" stroke=\"#6B0FBE\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\r\n              <path d=\"M16 3.13C16.8604 3.35031 17.623 3.85071 18.1676 4.55232C18.7122 5.25392 19.0078 6.11683 19.0078 7.005C19.0078 7.89318 18.7122 8.75608 18.1676 9.45769C17.623 10.1593 16.8604 10.6597 16 10.88\" stroke=\"#6B0FBE\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\r\n            </svg>\r\n          </view>\r\n        </view>\r\n        \r\n        <view class=\"data-card\">\r\n          <view class=\"card-value\">{{channelStats.activeChannels}}</view>\r\n          <view class=\"card-label\">活跃分销员</view>\r\n          <view class=\"card-trend\" :class=\"channelStats.activeTrend\">\r\n            <view class=\"trend-icon\"></view>\r\n            <text>{{channelStats.activeGrowth}}</text>\r\n          </view>\r\n          <view class=\"card-icon active-icon\">\r\n            <svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n              <path d=\"M16 21V19C16 17.9391 15.5786 16.9217 14.8284 16.1716C14.0783 15.4214 13.0609 15 12 15H5C3.93913 15 2.92172 15.4214 2.17157 16.1716C1.42143 16.9217 1 17.9391 1 19V21\" stroke=\"#34C759\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\r\n              <path d=\"M8.5 11C10.7091 11 12.5 9.20914 12.5 7C12.5 4.79086 10.7091 3 8.5 3C6.29086 3 4.5 4.79086 4.5 7C4.5 9.20914 6.29086 11 8.5 11Z\" stroke=\"#34C759\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\r\n              <path d=\"M20 8V14\" stroke=\"#34C759\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\r\n              <path d=\"M23 11H17\" stroke=\"#34C759\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\r\n            </svg>\r\n          </view>\r\n        </view>\r\n        \r\n        <view class=\"data-card\">\r\n          <view class=\"card-value\">¥{{formatNumber(channelStats.totalCommission)}}</view>\r\n          <view class=\"card-label\">佣金总额</view>\r\n          <view class=\"card-trend\" :class=\"channelStats.commissionTrend\">\r\n            <view class=\"trend-icon\"></view>\r\n            <text>{{channelStats.commissionGrowth}}</text>\r\n          </view>\r\n          <view class=\"card-icon commission-icon\">\r\n            <svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n              <path d=\"M12 1V23M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6\" stroke=\"#FF9500\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\r\n            </svg>\r\n          </view>\r\n        </view>\r\n        \r\n        <view class=\"data-card\">\r\n          <view class=\"card-value\">{{channelStats.totalOrders}}</view>\r\n          <view class=\"card-label\">订单数量</view>\r\n          <view class=\"card-trend\" :class=\"channelStats.ordersTrend\">\r\n            <view class=\"trend-icon\"></view>\r\n            <text>{{channelStats.ordersGrowth}}</text>\r\n          </view>\r\n          <view class=\"card-icon orders-icon\">\r\n            <svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n              <path d=\"M9 17H15M9 13H15M9 9H15M5 21H19C20.1046 21 21 20.1046 21 19V5C21 3.89543 20.1046 3 19 3H5C3.89543 3 3 3.89543 3 5V19C3 20.1046 3.89543 21 5 21Z\" stroke=\"#007AFF\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\r\n            </svg>\r\n          </view>\r\n        </view>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 渠道筛选和搜索 -->\r\n    <view class=\"filter-section\">\r\n      <view class=\"search-bar\">\r\n        <view class=\"search-icon\">\r\n          <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n            <path d=\"M11 19C15.4183 19 19 15.4183 19 11C19 6.58172 15.4183 3 11 3C6.58172 3 3 6.58172 3 11C3 15.4183 6.58172 19 11 19Z\" stroke=\"#999\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\r\n            <path d=\"M21 21L16.65 16.65\" stroke=\"#999\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\r\n          </svg>\r\n        </view>\r\n        <input type=\"text\" class=\"search-input\" placeholder=\"搜索分销员\" v-model=\"searchKeyword\" @input=\"handleSearch\" />\r\n      </view>\r\n      \r\n      <view class=\"filter-tabs\">\r\n        <view \r\n          v-for=\"(tab, index) in filterTabs\" \r\n          :key=\"index\" \r\n          class=\"filter-tab\" \r\n          :class=\"{ active: currentTab === tab.value }\"\r\n          @click=\"switchTab(tab.value)\"\r\n        >\r\n          <text>{{tab.name}}</text>\r\n        </view>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 分销员列表 -->\r\n    <view class=\"channels-list-section\">\r\n      <view \r\n        v-for=\"(channel, index) in filteredChannels\" \r\n        :key=\"index\" \r\n        class=\"channel-item\"\r\n        @click=\"viewChannelDetail(channel)\"\r\n      >\r\n        <view class=\"channel-avatar-container\">\r\n          <image class=\"channel-avatar\" :src=\"channel.avatar\" mode=\"aspectFill\"></image>\r\n          <view class=\"channel-status\" :class=\"channel.status\"></view>\r\n        </view>\r\n        \r\n        <view class=\"channel-info\">\r\n          <view class=\"channel-header\">\r\n            <text class=\"channel-name\">{{channel.name}}</text>\r\n            <view class=\"channel-level\" :class=\"'level-' + channel.level\">{{channel.levelName}}</view>\r\n          </view>\r\n          \r\n          <view class=\"channel-stats\">\r\n            <view class=\"stat-item\">\r\n              <text class=\"stat-value\">{{channel.orders}}</text>\r\n              <text class=\"stat-label\">推广订单</text>\r\n            </view>\r\n            <view class=\"stat-item\">\r\n              <text class=\"stat-value\">¥{{channel.commission}}</text>\r\n              <text class=\"stat-label\">佣金收益</text>\r\n            </view>\r\n            <view class=\"stat-item\">\r\n              <text class=\"stat-value\">{{channel.fans}}</text>\r\n              <text class=\"stat-label\">粉丝数量</text>\r\n            </view>\r\n          </view>\r\n          \r\n          <view class=\"channel-actions\">\r\n            <view class=\"action-btn view-qrcode\" @click.stop=\"viewQrcode(channel)\">\r\n              <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n                <path d=\"M3 11V3H11V11H3ZM3 21V13H11V21H3ZM13 11V3H21V11H13ZM13 21V13H21V21H13ZM5 9H9V5H5V9ZM15 9H19V5H15V9ZM5 19H9V15H5V19ZM15 19H19V15H15V19Z\" stroke=\"#6B0FBE\" stroke-width=\"1.5\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\r\n              </svg>\r\n              <text>查看二维码</text>\r\n            </view>\r\n            <view class=\"action-btn edit-channel\" @click.stop=\"editChannel(channel)\">\r\n              <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n                <path d=\"M11 4H4C3.46957 4 2.96086 4.21071 2.58579 4.58579C2.21071 4.96086 2 5.46957 2 6V20C2 20.5304 2.21071 21.0391 2.58579 21.4142C2.96086 21.7893 3.46957 22 4 22H18C18.5304 22 19.0391 21.7893 19.4142 21.4142C19.7893 21.0391 20 20.5304 20 20V13\" stroke=\"#6B0FBE\" stroke-width=\"1.5\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\r\n                <path d=\"M18.5 2.5C18.8978 2.10217 19.4374 1.87868 20 1.87868C20.5626 1.87868 21.1022 2.10217 21.5 2.5C21.8978 2.89782 22.1213 3.43739 22.1213 4C22.1213 4.56261 21.8978 5.10217 21.5 5.5L12 15L8 16L9 12L18.5 2.5Z\" stroke=\"#6B0FBE\" stroke-width=\"1.5\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\r\n              </svg>\r\n              <text>编辑</text>\r\n            </view>\r\n          </view>\r\n        </view>\r\n      </view>\r\n      \r\n      <!-- 空状态展示 -->\r\n      <view class=\"empty-state\" v-if=\"filteredChannels.length === 0\">\r\n        <image class=\"empty-image\" src=\"/static/images/empty-state.png\" mode=\"aspectFit\"></image>\r\n        <text class=\"empty-text\">暂无符合条件的分销员</text>\r\n        <button class=\"empty-btn\" @click=\"addChannel\">添加分销员</button>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 新建渠道浮动按钮 -->\r\n    <view class=\"floating-btn\" @click=\"addChannel\">\r\n      <svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n        <path d=\"M12 5V19M5 12H19\" stroke=\"white\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\r\n      </svg>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script setup>\r\nimport { ref, reactive, onMounted, computed } from 'vue';\r\n\r\n// 日期范围\r\nconst dateRange = ref('2023-04-01 ~ 2023-04-30');\r\n\r\n// 渠道统计数据\r\nconst channelStats = reactive({\r\n  totalChannels: 248,\r\n  channelsTrend: 'up',\r\n  channelsGrowth: '12%',\r\n  \r\n  activeChannels: 186,\r\n  activeTrend: 'up',\r\n  activeGrowth: '8%',\r\n  \r\n  totalCommission: 15820.50,\r\n  commissionTrend: 'up',\r\n  commissionGrowth: '15%',\r\n  \r\n  totalOrders: 1245,\r\n  ordersTrend: 'down',\r\n  ordersGrowth: '3%'\r\n});\r\n\r\n// 格式化数字\r\nconst formatNumber = (num) => {\r\n  return num.toLocaleString('zh-CN', { minimumFractionDigits: 2, maximumFractionDigits: 2 });\r\n};\r\n\r\n// 显示日期选择器\r\nconst showDatePicker = () => {\r\n  // 日期选择逻辑\r\n  uni.showToast({\r\n    title: '日期筛选功能开发中',\r\n    icon: 'none'\r\n  });\r\n};\r\n\r\n// 基础方法\r\nconst goBack = () => {\r\n  uni.navigateBack();\r\n};\r\n\r\nconst addChannel = () => {\r\n  uni.navigateTo({\r\n    url: '/subPackages/merchant-admin-marketing/pages/distribution/channels/add'\r\n  });\r\n};\r\n\r\n// 筛选标签\r\nconst filterTabs = ref([\r\n  { name: '全部', value: 'all' },\r\n  { name: '活跃', value: 'active' },\r\n  { name: '新增', value: 'new' },\r\n  { name: '休眠', value: 'inactive' }\r\n]);\r\n\r\nconst currentTab = ref('all');\r\nconst searchKeyword = ref('');\r\n\r\n// 渠道列表数据\r\nconst channels = reactive([\r\n  {\r\n    id: '1001',\r\n    name: '张小明',\r\n    avatar: '/static/images/avatars/avatar1.png',\r\n    status: 'online',\r\n    level: 3,\r\n    levelName: '钻石会员',\r\n    orders: 89,\r\n    commission: '4,526.50',\r\n    fans: 245,\r\n    joinTime: '2023-03-15'\r\n  },\r\n  {\r\n    id: '1002',\r\n    name: '王丽丽',\r\n    avatar: '/static/images/avatars/avatar2.png',\r\n    status: 'online',\r\n    level: 2,\r\n    levelName: '黄金会员',\r\n    orders: 56,\r\n    commission: '2,830.80',\r\n    fans: 128,\r\n    joinTime: '2023-03-18'\r\n  },\r\n  {\r\n    id: '1003',\r\n    name: '李大壮',\r\n    avatar: '/static/images/avatars/avatar3.png',\r\n    status: 'offline',\r\n    level: 1,\r\n    levelName: '白银会员',\r\n    orders: 34,\r\n    commission: '1,254.30',\r\n    fans: 76,\r\n    joinTime: '2023-03-22'\r\n  },\r\n  {\r\n    id: '1004',\r\n    name: '赵小红',\r\n    avatar: '/static/images/avatars/avatar4.png',\r\n    status: 'online',\r\n    level: 2,\r\n    levelName: '黄金会员',\r\n    orders: 42,\r\n    commission: '2,102.60',\r\n    fans: 94,\r\n    joinTime: '2023-03-25'\r\n  },\r\n  {\r\n    id: '1005',\r\n    name: '刘伟',\r\n    avatar: '/static/images/avatars/avatar5.png',\r\n    status: 'offline',\r\n    level: 1,\r\n    levelName: '白银会员',\r\n    orders: 28,\r\n    commission: '986.40',\r\n    fans: 52,\r\n    joinTime: '2023-04-01'\r\n  }\r\n]);\r\n\r\n// 根据筛选条件过滤渠道\r\nconst filteredChannels = computed(() => {\r\n  let result = [...channels];\r\n  \r\n  // 根据标签筛选\r\n  if (currentTab.value !== 'all') {\r\n    if (currentTab.value === 'active') {\r\n      result = result.filter(item => item.status === 'online');\r\n    } else if (currentTab.value === 'inactive') {\r\n      result = result.filter(item => item.status === 'offline');\r\n    } else if (currentTab.value === 'new') {\r\n      // 假设14天内为新增\r\n      const twoWeeksAgo = new Date();\r\n      twoWeeksAgo.setDate(twoWeeksAgo.getDate() - 14);\r\n      result = result.filter(item => {\r\n        const joinDate = new Date(item.joinTime);\r\n        return joinDate >= twoWeeksAgo;\r\n      });\r\n    }\r\n  }\r\n  \r\n  // 根据关键词搜索\r\n  if (searchKeyword.value) {\r\n    const keyword = searchKeyword.value.toLowerCase();\r\n    result = result.filter(item => \r\n      item.name.toLowerCase().includes(keyword) || \r\n      item.id.includes(keyword)\r\n    );\r\n  }\r\n  \r\n  return result;\r\n});\r\n\r\n// 切换筛选标签\r\nconst switchTab = (tab) => {\r\n  currentTab.value = tab;\r\n};\r\n\r\n// 搜索处理\r\nconst handleSearch = () => {\r\n  // 实时搜索，无需额外处理，computed会自动更新\r\n};\r\n\r\n// 查看渠道详情\r\nconst viewChannelDetail = (channel) => {\r\n  uni.navigateTo({\r\n    url: `/subPackages/merchant-admin-marketing/pages/distribution/channels/detail?id=${channel.id}`\r\n  });\r\n};\r\n\r\n// 查看二维码\r\nconst viewQrcode = (channel) => {\r\n  uni.navigateTo({\r\n    url: `/subPackages/merchant-admin-marketing/pages/distribution/qrcode/index?channelId=${channel.id}&channelName=${encodeURIComponent(channel.name)}`\r\n  });\r\n};\r\n\r\n// 编辑渠道\r\nconst editChannel = (channel) => {\r\n  uni.navigateTo({\r\n    url: `/subPackages/merchant-admin-marketing/pages/distribution/channels/edit?id=${channel.id}`\r\n  });\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.channels-container {\r\n  min-height: 100vh;\r\n  background-color: #F5F7FA;\r\n  padding-bottom: env(safe-area-inset-bottom);\r\n}\r\n\r\n/* 导航栏样式 */\r\n.navbar {\r\n  position: sticky;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  background: linear-gradient(135deg, #A764CA, #6B0FBE);\r\n  color: #fff;\r\n  padding: 44px 16px 15px;\r\n  display: flex;\r\n  align-items: center;\r\n  z-index: 100;\r\n  box-shadow: 0 2px 10px rgba(107, 15, 190, 0.15);\r\n}\r\n\r\n.navbar-back {\r\n  width: 36px;\r\n  height: 36px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.back-icon {\r\n  width: 12px;\r\n  height: 12px;\r\n  border-left: 2px solid #fff;\r\n  border-bottom: 2px solid #fff;\r\n  transform: rotate(45deg);\r\n}\r\n\r\n.navbar-title {\r\n  flex: 1;\r\n  text-align: center;\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n  letter-spacing: 0.5px;\r\n}\r\n\r\n.navbar-right {\r\n  width: 36px;\r\n  height: 36px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.add-icon {\r\n  width: 36px;\r\n  height: 36px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n/* 概览部分样式 */\r\n.overview-section {\r\n  margin: 16px;\r\n  background-color: #FFFFFF;\r\n  border-radius: 20px;\r\n  padding: 20px;\r\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.04);\r\n}\r\n\r\n.overview-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 16px;\r\n}\r\n\r\n.overview-title {\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  color: #333;\r\n}\r\n\r\n.date-filter {\r\n  display: flex;\r\n  align-items: center;\r\n  background-color: #F5F7FA;\r\n  padding: 6px 10px;\r\n  border-radius: 15px;\r\n  font-size: 12px;\r\n  color: #6B0FBE;\r\n}\r\n\r\n.filter-arrow {\r\n  margin-left: 4px;\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n/* 数据卡片样式 */\r\n.data-cards {\r\n  display: grid;\r\n  grid-template-columns: 1fr 1fr;\r\n  gap: 12px;\r\n}\r\n\r\n.data-card {\r\n  position: relative;\r\n  background-color: #FFFFFF;\r\n  border-radius: 16px;\r\n  padding: 16px;\r\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.03);\r\n  border: 1px solid rgba(0, 0, 0, 0.04);\r\n  overflow: hidden;\r\n}\r\n\r\n.card-value {\r\n  font-size: 22px;\r\n  font-weight: 700;\r\n  color: #333;\r\n  margin-bottom: 4px;\r\n}\r\n\r\n.card-label {\r\n  font-size: 12px;\r\n  color: #999;\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.card-trend {\r\n  display: flex;\r\n  align-items: center;\r\n  font-size: 12px;\r\n  \r\n  &.up {\r\n    color: #34C759;\r\n    \r\n    .trend-icon {\r\n      width: 0;\r\n      height: 0;\r\n      border-left: 4px solid transparent;\r\n      border-right: 4px solid transparent;\r\n      border-bottom: 6px solid #34C759;\r\n      margin-right: 4px;\r\n    }\r\n  }\r\n  \r\n  &.down {\r\n    color: #FF3B30;\r\n    \r\n    .trend-icon {\r\n      width: 0;\r\n      height: 0;\r\n      border-left: 4px solid transparent;\r\n      border-right: 4px solid transparent;\r\n      border-top: 6px solid #FF3B30;\r\n      margin-right: 4px;\r\n    }\r\n  }\r\n}\r\n\r\n.card-icon {\r\n  position: absolute;\r\n  top: 14px;\r\n  right: 14px;\r\n  opacity: 0.1;\r\n  transform: scale(1.5);\r\n  transform-origin: top right;\r\n}\r\n\r\n/* 筛选部分样式 */\r\n.filter-section {\r\n  margin: 16px;\r\n  background-color: #FFFFFF;\r\n  border-radius: 20px;\r\n  padding: 16px;\r\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.04);\r\n}\r\n\r\n.search-bar {\r\n  display: flex;\r\n  align-items: center;\r\n  background-color: #F5F7FA;\r\n  border-radius: 12px;\r\n  padding: 0 12px;\r\n  margin-bottom: 16px;\r\n}\r\n\r\n.search-icon {\r\n  margin-right: 8px;\r\n}\r\n\r\n.search-input {\r\n  flex: 1;\r\n  height: 40px;\r\n  font-size: 14px;\r\n  background-color: transparent;\r\n  border: none;\r\n  color: #333;\r\n}\r\n\r\n.filter-tabs {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  border-bottom: 1px solid #F0F0F0;\r\n}\r\n\r\n.filter-tab {\r\n  flex: 1;\r\n  text-align: center;\r\n  padding: 12px 0;\r\n  font-size: 14px;\r\n  color: #999;\r\n  position: relative;\r\n  \r\n  &.active {\r\n    color: #6B0FBE;\r\n    font-weight: 500;\r\n    \r\n    &::after {\r\n      content: '';\r\n      position: absolute;\r\n      bottom: -1px;\r\n      left: 25%;\r\n      width: 50%;\r\n      height: 3px;\r\n      background-color: #6B0FBE;\r\n      border-radius: 3px;\r\n    }\r\n  }\r\n}\r\n\r\n/* 渠道列表样式 */\r\n.channels-list-section {\r\n  margin: 16px;\r\n}\r\n\r\n.channel-item {\r\n  background-color: #FFFFFF;\r\n  border-radius: 20px;\r\n  margin-bottom: 16px;\r\n  padding: 16px;\r\n  display: flex;\r\n  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.03);\r\n}\r\n\r\n.channel-avatar-container {\r\n  position: relative;\r\n  margin-right: 16px;\r\n}\r\n\r\n.channel-avatar {\r\n  width: 60px;\r\n  height: 60px;\r\n  border-radius: 50%;\r\n  border: 2px solid #FFFFFF;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.channel-status {\r\n  position: absolute;\r\n  bottom: 0;\r\n  right: 0;\r\n  width: 14px;\r\n  height: 14px;\r\n  border-radius: 50%;\r\n  border: 2px solid #FFFFFF;\r\n  \r\n  &.online {\r\n    background-color: #34C759;\r\n  }\r\n  \r\n  &.offline {\r\n    background-color: #999999;\r\n  }\r\n}\r\n\r\n.channel-info {\r\n  flex: 1;\r\n}\r\n\r\n.channel-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.channel-name {\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  color: #333;\r\n}\r\n\r\n.channel-level {\r\n  font-size: 12px;\r\n  padding: 2px 8px;\r\n  border-radius: 10px;\r\n  \r\n  &.level-1 {\r\n    background-color: #E5E5EA;\r\n    color: #8E8E93;\r\n  }\r\n  \r\n  &.level-2 {\r\n    background-color: #FFD700;\r\n    color: #8B6914;\r\n  }\r\n  \r\n  &.level-3 {\r\n    background-color: #B9F2FF;\r\n    color: #007AFF;\r\n  }\r\n}\r\n\r\n.channel-stats {\r\n  display: flex;\r\n  margin: 12px 0;\r\n}\r\n\r\n.stat-item {\r\n  flex: 1;\r\n  text-align: center;\r\n  border-right: 1px solid #EEEEEE;\r\n  \r\n  &:last-child {\r\n    border-right: none;\r\n  }\r\n}\r\n\r\n.stat-value {\r\n  display: block;\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  color: #333;\r\n}\r\n\r\n.stat-label {\r\n  display: block;\r\n  font-size: 12px;\r\n  color: #999;\r\n  margin-top: 4px;\r\n}\r\n\r\n.channel-actions {\r\n  display: flex;\r\n  margin-top: 12px;\r\n  border-top: 1px solid #EEEEEE;\r\n  padding-top: 12px;\r\n}\r\n\r\n.action-btn {\r\n  flex: 1;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  font-size: 13px;\r\n  \r\n  svg {\r\n    margin-right: 4px;\r\n  }\r\n  \r\n  &.view-qrcode {\r\n    color: #6B0FBE;\r\n  }\r\n  \r\n  &.edit-channel {\r\n    color: #6B0FBE;\r\n    border-left: 1px solid #EEEEEE;\r\n  }\r\n}\r\n\r\n/* 空状态样式 */\r\n.empty-state {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  padding: 40px 0;\r\n}\r\n\r\n.empty-image {\r\n  width: 120px;\r\n  height: 120px;\r\n  margin-bottom: 16px;\r\n}\r\n\r\n.empty-text {\r\n  font-size: 14px;\r\n  color: #999;\r\n  margin-bottom: 16px;\r\n}\r\n\r\n.empty-btn {\r\n  background: linear-gradient(135deg, #A764CA, #6B0FBE);\r\n  color: #FFFFFF;\r\n  border: none;\r\n  border-radius: 20px;\r\n  padding: 8px 20px;\r\n  font-size: 14px;\r\n}\r\n\r\n/* 浮动按钮 */\r\n.floating-btn {\r\n  position: fixed;\r\n  right: 24px;\r\n  bottom: 40px;\r\n  width: 56px;\r\n  height: 56px;\r\n  background: linear-gradient(135deg, #A764CA, #6B0FBE);\r\n  border-radius: 50%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  box-shadow: 0 4px 16px rgba(107, 15, 190, 0.3);\r\n  z-index: 90;\r\n}\r\n</style> ", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/subPackages/merchant-admin-marketing/pages/marketing/distribution/channels.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "reactive", "uni", "computed", "MiniProgramPage"], "mappings": ";;;;;;;;;;;AAmMA,UAAM,YAAYA,cAAAA,IAAI,yBAAyB;AAG/C,UAAM,eAAeC,cAAAA,SAAS;AAAA,MAC5B,eAAe;AAAA,MACf,eAAe;AAAA,MACf,gBAAgB;AAAA,MAEhB,gBAAgB;AAAA,MAChB,aAAa;AAAA,MACb,cAAc;AAAA,MAEd,iBAAiB;AAAA,MACjB,iBAAiB;AAAA,MACjB,kBAAkB;AAAA,MAElB,aAAa;AAAA,MACb,aAAa;AAAA,MACb,cAAc;AAAA,IAChB,CAAC;AAGD,UAAM,eAAe,CAAC,QAAQ;AAC5B,aAAO,IAAI,eAAe,SAAS,EAAE,uBAAuB,GAAG,uBAAuB,EAAC,CAAE;AAAA,IAC3F;AAGA,UAAM,iBAAiB,MAAM;AAE3BC,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,MACV,CAAG;AAAA,IACH;AAGA,UAAM,SAAS,MAAM;AACnBA,oBAAG,MAAC,aAAY;AAAA,IAClB;AAEA,UAAM,aAAa,MAAM;AACvBA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK;AAAA,MACT,CAAG;AAAA,IACH;AAGA,UAAM,aAAaF,cAAAA,IAAI;AAAA,MACrB,EAAE,MAAM,MAAM,OAAO,MAAO;AAAA,MAC5B,EAAE,MAAM,MAAM,OAAO,SAAU;AAAA,MAC/B,EAAE,MAAM,MAAM,OAAO,MAAO;AAAA,MAC5B,EAAE,MAAM,MAAM,OAAO,WAAY;AAAA,IACnC,CAAC;AAED,UAAM,aAAaA,cAAAA,IAAI,KAAK;AAC5B,UAAM,gBAAgBA,cAAAA,IAAI,EAAE;AAG5B,UAAM,WAAWC,cAAAA,SAAS;AAAA,MACxB;AAAA,QACE,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,QAAQ;AAAA,QACR,OAAO;AAAA,QACP,WAAW;AAAA,QACX,QAAQ;AAAA,QACR,YAAY;AAAA,QACZ,MAAM;AAAA,QACN,UAAU;AAAA,MACX;AAAA,MACD;AAAA,QACE,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,QAAQ;AAAA,QACR,OAAO;AAAA,QACP,WAAW;AAAA,QACX,QAAQ;AAAA,QACR,YAAY;AAAA,QACZ,MAAM;AAAA,QACN,UAAU;AAAA,MACX;AAAA,MACD;AAAA,QACE,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,QAAQ;AAAA,QACR,OAAO;AAAA,QACP,WAAW;AAAA,QACX,QAAQ;AAAA,QACR,YAAY;AAAA,QACZ,MAAM;AAAA,QACN,UAAU;AAAA,MACX;AAAA,MACD;AAAA,QACE,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,QAAQ;AAAA,QACR,OAAO;AAAA,QACP,WAAW;AAAA,QACX,QAAQ;AAAA,QACR,YAAY;AAAA,QACZ,MAAM;AAAA,QACN,UAAU;AAAA,MACX;AAAA,MACD;AAAA,QACE,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,QAAQ;AAAA,QACR,OAAO;AAAA,QACP,WAAW;AAAA,QACX,QAAQ;AAAA,QACR,YAAY;AAAA,QACZ,MAAM;AAAA,QACN,UAAU;AAAA,MACX;AAAA,IACH,CAAC;AAGD,UAAM,mBAAmBE,cAAQ,SAAC,MAAM;AACtC,UAAI,SAAS,CAAC,GAAG,QAAQ;AAGzB,UAAI,WAAW,UAAU,OAAO;AAC9B,YAAI,WAAW,UAAU,UAAU;AACjC,mBAAS,OAAO,OAAO,UAAQ,KAAK,WAAW,QAAQ;AAAA,QAC7D,WAAe,WAAW,UAAU,YAAY;AAC1C,mBAAS,OAAO,OAAO,UAAQ,KAAK,WAAW,SAAS;AAAA,QAC9D,WAAe,WAAW,UAAU,OAAO;AAErC,gBAAM,cAAc,oBAAI;AACxB,sBAAY,QAAQ,YAAY,QAAS,IAAG,EAAE;AAC9C,mBAAS,OAAO,OAAO,UAAQ;AAC7B,kBAAM,WAAW,IAAI,KAAK,KAAK,QAAQ;AACvC,mBAAO,YAAY;AAAA,UAC3B,CAAO;AAAA,QACF;AAAA,MACF;AAGD,UAAI,cAAc,OAAO;AACvB,cAAM,UAAU,cAAc,MAAM,YAAW;AAC/C,iBAAS,OAAO;AAAA,UAAO,UACrB,KAAK,KAAK,cAAc,SAAS,OAAO,KACxC,KAAK,GAAG,SAAS,OAAO;AAAA,QAC9B;AAAA,MACG;AAED,aAAO;AAAA,IACT,CAAC;AAGD,UAAM,YAAY,CAAC,QAAQ;AACzB,iBAAW,QAAQ;AAAA,IACrB;AAGA,UAAM,eAAe,MAAM;AAAA,IAE3B;AAGA,UAAM,oBAAoB,CAAC,YAAY;AACrCD,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,+EAA+E,QAAQ,EAAE;AAAA,MAClG,CAAG;AAAA,IACH;AAGA,UAAM,aAAa,CAAC,YAAY;AAC9BA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,mFAAmF,QAAQ,EAAE,gBAAgB,mBAAmB,QAAQ,IAAI,CAAC;AAAA,MACtJ,CAAG;AAAA,IACH;AAGA,UAAM,cAAc,CAAC,YAAY;AAC/BA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,6EAA6E,QAAQ,EAAE;AAAA,MAChG,CAAG;AAAA,IACH;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACzXA,GAAG,WAAWE,SAAe;"}