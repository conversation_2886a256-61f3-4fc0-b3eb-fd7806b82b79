<template>
  <view class="test-page">
    <view class="page-header">
      <text class="page-title">付费模态框测试</text>
      <text class="page-desc">测试付费选项的下拉选择框功能</text>
    </view>
    
    <!-- 测试不同类型的付费功能 -->
    <view class="test-section">
      <view class="section-title">发布付费测试</view>
      <ConfigurablePremiumActions
        pageType="publish"
        showMode="direct"
        :itemData="publishData"
        @action-completed="handleActionCompleted"
        @action-cancelled="handleActionCancelled"
      />
    </view>
    
    <view class="test-section">
      <view class="section-title">置顶付费测试</view>
      <ConfigurablePremiumActions
        pageType="merchant_top"
        showMode="direct"
        :itemData="topData"
        @action-completed="handleActionCompleted"
        @action-cancelled="handleActionCancelled"
      />
    </view>
    
    <view class="test-section">
      <view class="section-title">刷新付费测试</view>
      <ConfigurablePremiumActions
        pageType="merchant_refresh"
        showMode="direct"
        :itemData="refreshData"
        @action-completed="handleActionCompleted"
        @action-cancelled="handleActionCancelled"
      />
    </view>
    
    <view class="test-section">
      <view class="section-title">商家入驻测试</view>
      <ConfigurablePremiumActions
        pageType="merchant_join"
        showMode="direct"
        :itemData="joinData"
        @action-completed="handleActionCompleted"
        @action-cancelled="handleActionCancelled"
      />
    </view>
    
    <!-- 测试结果显示 -->
    <view class="test-results" v-if="testResults.length > 0">
      <view class="results-title">操作记录</view>
      <view class="result-item" v-for="(result, index) in testResults" :key="index">
        <view class="result-time">{{ result.time }}</view>
        <view class="result-action">{{ result.action }}</view>
        <view class="result-type">{{ result.type }}</view>
        <view class="result-data" v-if="result.data">
          <text v-if="result.data.duration">时长: {{ result.data.duration }}</text>
          <text v-if="result.data.price">价格: ¥{{ result.data.price }}</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, reactive } from 'vue';
import ConfigurablePremiumActions from '@/components/premium/ConfigurablePremiumActions.vue';

// 测试数据
const publishData = reactive({
  id: 'publish_test',
  title: '发布测试',
  description: '测试发布付费功能'
});

const topData = reactive({
  id: 'top_test',
  title: '置顶测试',
  description: '测试置顶付费功能'
});

const refreshData = reactive({
  id: 'refresh_test',
  title: '刷新测试',
  description: '测试刷新付费功能'
});

const joinData = reactive({
  id: 'join_test',
  title: '入驻测试',
  description: '测试商家入驻付费功能'
});

// 测试结果
const testResults = ref([]);

// 处理操作完成
const handleActionCompleted = (result) => {
  console.log('操作完成:', result);
  
  testResults.value.unshift({
    time: new Date().toLocaleTimeString(),
    action: `${result.action}完成`,
    type: result.type === 'ad' ? '看广告' : '付费',
    data: result.data
  });
  
  let message = '操作完成';
  if (result.type === 'payment' && result.data) {
    message = `付费成功 - ${result.data.duration} ¥${result.data.price}`;
  } else if (result.type === 'ad') {
    message = '广告观看完成';
  }
  
  uni.showToast({
    title: message,
    icon: 'success',
    duration: 2000
  });
};

// 处理操作取消
const handleActionCancelled = (result) => {
  console.log('操作取消:', result);
  
  testResults.value.unshift({
    time: new Date().toLocaleTimeString(),
    action: `${result.action}取消`,
    type: result.type === 'ad' ? '看广告' : '付费',
    data: null
  });
  
  uni.showToast({
    title: '操作已取消',
    icon: 'none'
  });
};
</script>

<style lang="scss" scoped>
.test-page {
  padding: 20rpx;
  background: #f5f5f5;
  min-height: 100vh;
}

.page-header {
  text-align: center;
  margin-bottom: 30rpx;
  padding: 30rpx;
  background: white;
  border-radius: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.page-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  display: block;
  margin-bottom: 10rpx;
}

.page-desc {
  font-size: 28rpx;
  color: #666;
  display: block;
}

.test-section {
  margin-bottom: 30rpx;
  padding: 30rpx;
  background: white;
  border-radius: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 20rpx;
  padding-bottom: 10rpx;
  border-bottom: 2rpx solid #f0f0f0;
}

.test-results {
  margin-top: 30rpx;
  padding: 30rpx;
  background: white;
  border-radius: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.results-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 20rpx;
}

.result-item {
  padding: 20rpx;
  margin-bottom: 15rpx;
  background: #f8f9fa;
  border-radius: 10rpx;
  border-left: 4rpx solid #4f46e5;
}

.result-time {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 5rpx;
}

.result-action {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 5rpx;
}

.result-type {
  font-size: 26rpx;
  color: #4f46e5;
  margin-bottom: 10rpx;
}

.result-data {
  display: flex;
  gap: 20rpx;
}

.result-data text {
  font-size: 24rpx;
  color: #666;
  background: white;
  padding: 5rpx 10rpx;
  border-radius: 5rpx;
}
</style>
