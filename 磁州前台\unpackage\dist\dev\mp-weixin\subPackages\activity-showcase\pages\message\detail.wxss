/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 移除阿里妈妈字体引用，改用系统默认字体 */
/* 移除原有阿里妈妈字体定义
$uni-font-family: 'AlimamaShuHeiTi';
$uni-font-family-text: 'AlimamaShuHeiTi', -apple-system, BlinkMacSystemFont, 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;
*/
body.data-v-e79a3555, html.data-v-e79a3555, #app.data-v-e79a3555, .index-container.data-v-e79a3555 {
  font-family: "AlimamaShuHeiTi", -apple-system, BlinkMacSystemFont, "Helvetica Neue", "PingFang SC", "Microsoft YaHei", sans-serif;
}
.message-detail .message-header .title.data-v-e79a3555 {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 16rpx;
}
.message-detail .message-header .meta.data-v-e79a3555 {
  display: flex;
  align-items: center;
  font-size: 24rpx;
  color: #999;
}
.message-detail .message-header .meta .time.data-v-e79a3555 {
  margin-right: 16rpx;
}
.message-detail .message-header .meta .type.data-v-e79a3555 {
  background: #f0f0f0;
  padding: 4rpx 12rpx;
  border-radius: 4rpx;
}
.message-detail .message-content.data-v-e79a3555 {
  margin-top: 24rpx;
}
.message-detail .message-content.data-v-e79a3555 rich-text {
  font-size: 28rpx;
  line-height: 1.6;
  color: #666;
}
.message-detail .message-content .image-list.data-v-e79a3555 {
  margin-top: 24rpx;
}
.message-detail .message-content .image-list image.data-v-e79a3555 {
  width: 100%;
  border-radius: 8rpx;
  margin-bottom: 16rpx;
}
.message-detail .message-actions.data-v-e79a3555 {
  margin-top: 24rpx;
  display: flex;
  justify-content: center;
}