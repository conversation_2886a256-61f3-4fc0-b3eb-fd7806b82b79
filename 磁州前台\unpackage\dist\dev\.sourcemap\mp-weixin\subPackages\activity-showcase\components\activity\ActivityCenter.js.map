{"version": 3, "file": "ActivityCenter.js", "sources": ["subPackages/activity-showcase/components/activity/ActivityCenter.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniComponent:/QzovVXNlcnMvQWRtaW5pc3RyYXRvci9EZXNrdG9wL-aWsOW7uuaWh-S7tuWkuS_no4Hlt57liY3lj7Avc3ViUGFja2FnZXMvYWN0aXZpdHktc2hvd2Nhc2UvY29tcG9uZW50cy9hY3Rpdml0eS9BY3Rpdml0eUNlbnRlci52dWU"], "sourcesContent": ["<template>\n  <!-- 同城活动中心模块 - 苹果风格设计 -->\n  <view class=\"activity-center\">\n    <!-- 模块标题区 -->\n    <view class=\"activity-header\">\n      <view class=\"title-container\">\n        <view class=\"title-bar\"></view>\n        <text class=\"activity-title\">同城活动中心</text>\n        <view class=\"activity-badge\">\n          <text class=\"badge-text\">精选推荐</text>\n        </view>\n      </view>\n      <view class=\"more-btn\" @click=\"navigateTo('/subPackages/activity-showcase/pages/list')\">\n        <text class=\"more-text\">更多</text>\n        <view class=\"more-icon\">\n          <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\">\n            <polyline points=\"9 18 15 12 9 6\"></polyline>\n          </svg>\n        </view>\n      </view>\n    </view>\n    \n    <!-- 标签导航栏 -->\n    <view class=\"activity-tabs-container\">\n      <scroll-view \n        class=\"activity-tabs\" \n        scroll-x \n        show-scrollbar=\"false\"\n        :scroll-with-animation=\"true\"\n        :enhanced=\"true\"\n        :bounces=\"true\"\n      >\n        <view \n          v-for=\"(tab, index) in activityTabs\" \n          :key=\"index\"\n          class=\"activity-tab\" \n          :class=\"{active: currentTab === index}\" \n          @click=\"switchTab(index)\"\n        >\n          <view class=\"tab-content\">\n            <view class=\"tab-icon\" v-if=\"tab.icon\">\n              <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"18\" height=\"18\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\">\n                <path v-if=\"tab.name === '全部活动'\" d=\"M3 3h18v18H3zM12 8v8M8 12h8\"></path>\n                <path v-else-if=\"tab.name === '拼团活动'\" d=\"M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2M9 7a4 4 0 1 0 0-8 4 4 0 0 0 0 8zM23 21v-2a4 4 0 0 0-3-3.87M16 3.13a4 4 0 0 1 0 7.75\"></path>\n                <path v-else-if=\"tab.name === '秒杀活动'\" d=\"M12 2L2 7l10 5 10-5-10-5zM2 17l10 5 10-5M2 12l10 5 10-5\"></path>\n                <path v-else-if=\"tab.name === '优惠券'\" d=\"M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z\"></path>\n                <path v-else-if=\"tab.name === '满减活动'\" d=\"M12 2v20M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6\"></path>\n                <circle v-else cx=\"12\" cy=\"12\" r=\"10\"></circle>\n              </svg>\n            </view>\n            <text class=\"tab-text\">{{tab.name}}</text>\n          </view>\n          <view class=\"tab-line\" v-if=\"currentTab === index\"></view>\n          <view class=\"tab-glow\" v-if=\"currentTab === index\"></view>\n        </view>\n      </scroll-view>\n    </view>\n    \n    <!-- 活动列表 - 水平滚动 -->\n    <view class=\"activity-scroll-container\">\n      <view class=\"carousel-controls\" v-if=\"filteredActivities.length > 1\">\n        <view class=\"control-btn prev-btn\" @click=\"scrollToPrev\" :class=\"{ disabled: currentScrollIndex === 0 && !isLooping }\">\n          <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\">\n            <polyline points=\"15 18 9 12 15 6\"></polyline>\n          </svg>\n        </view>\n        <view class=\"control-btn next-btn\" @click=\"scrollToNext\">\n          <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\">\n            <polyline points=\"9 18 15 12 9 6\"></polyline>\n          </svg>\n        </view>\n      </view>\n      \n      <scroll-view \n        class=\"activity-scroll\" \n        scroll-x \n        show-scrollbar=\"false\"\n        :scroll-with-animation=\"true\"\n        :enhanced=\"true\"\n        :bounces=\"true\"\n        :scroll-left=\"scrollLeft\"\n        ref=\"activityScrollRef\"\n        @touchstart=\"handleTouchStart\"\n        @touchend=\"handleTouchEnd\"\n        @scroll=\"handleScroll\"\n      >\n        <view class=\"activity-list\" :style=\"{ paddingLeft: `${centerOffsetRpx}rpx`, paddingRight: `${centerOffsetRpx}rpx` }\">\n          <view \n            class=\"activity-item\" \n            v-for=\"(item, index) in filteredActivities\" \n            :key=\"item.id\"\n            :style=\"{ \n              transform: `scale(${currentScrollIndex === index ? 1 : 0.95})`, \n              opacity: currentScrollIndex === index ? 1 : 0.8 \n            }\"\n          >\n            <activity-card-factory \n              :item=\"item\"\n              @navigate=\"navigateToDetail\"\n              @favorite=\"toggleFavorite\"\n              @action=\"handleAction\"\n            />\n          </view>\n        </view>\n      </scroll-view>\n      \n      <!-- 轮播指示器 -->\n      <view class=\"carousel-indicators\" v-if=\"filteredActivities.length > 1\">\n        <view \n          v-for=\"(_, index) in filteredActivities\" \n          :key=\"index\"\n          class=\"indicator-dot\"\n          :class=\"{ active: currentScrollIndex === index }\"\n          @click=\"scrollToIndex(index)\"\n        ></view>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script setup>\nimport { ref, computed, onMounted, onBeforeUnmount } from 'vue';\nimport ActivityCardFactory from './ActivityCardFactory.vue';\n\n// 活动标签数据\nconst activityTabs = ref([\n  { name: '全部活动', icon: true },\n  { name: '拼团活动', icon: true },\n  { name: '秒杀活动', icon: true },\n  { name: '优惠券', icon: true },\n  { name: '满减活动', icon: true }\n]);\n\n// 当前选中的标签\nconst currentTab = ref(0);\n\n// 模拟活动数据\nconst activities = ref([\n  {\n    id: '1',\n    type: 'groupBuy',\n    title: '5人拼团 | 磁县特产礼盒',\n    status: 'ongoing',\n    startTime: '2024-07-10',\n    endTime: '2024-07-20',\n    location: '磁县特产展销中心',\n    coverImage: '/static/images/activity/group-buy-1.jpg',\n    isFavorite: false,\n    hot: true,\n    isPaidTop: true, // 付费置顶\n    groupPrice: 99,\n    originalPrice: 199,\n    groupSize: 5,\n    currentGroupMembers: 3,\n    participants: [\n      { avatar: '/static/images/avatar/avatar-1.jpg' },\n      { avatar: '/static/images/avatar/avatar-2.jpg' },\n      { avatar: '/static/images/avatar/avatar-3.jpg' },\n      { avatar: '/static/images/avatar/avatar-4.jpg' }\n    ]\n  },\n  {\n    id: '2',\n    type: 'flashSale',\n    title: '限时秒杀 | 本地农产品特惠',\n    status: 'ongoing',\n    startTime: '2024-07-12',\n    endTime: '2024-07-13',\n    location: '磁县农贸市场',\n    coverImage: '/static/images/activity/flash-sale-1.jpg',\n    isFavorite: true,\n    isAdTop: true, // 广告置顶\n    salePrice: 29.9,\n    originalPrice: 59.9,\n    totalStock: 100,\n    soldCount: 78,\n    participants: [\n      { avatar: '/static/images/avatar/avatar-5.jpg' },\n      { avatar: '/static/images/avatar/avatar-6.jpg' },\n      { avatar: '/static/images/avatar/avatar-7.jpg' },\n      { avatar: '/static/images/avatar/avatar-8.jpg' },\n      { avatar: '/static/images/avatar/avatar-9.jpg' }\n    ]\n  },\n  {\n    id: '3',\n    type: 'coupon',\n    title: '周末专享 | 餐饮优惠券',\n    status: 'upcoming',\n    startTime: '2024-07-15',\n    endTime: '2024-07-17',\n    location: '磁县商业街',\n    coverImage: '/static/images/activity/coupon-1.jpg',\n    isFavorite: false,\n    couponType: 'cash',\n    couponValue: 50,\n    couponCondition: '满200元可用',\n    couponValidity: '2024-07-31',\n    participants: [\n      { avatar: '/static/images/avatar/avatar-10.jpg' },\n      { avatar: '/static/images/avatar/avatar-11.jpg' }\n    ]\n  },\n  {\n    id: '4',\n    type: 'discount',\n    title: '商圈联合满减 | 暑期狂欢',\n    status: 'ongoing',\n    startTime: '2024-07-01',\n    endTime: '2024-07-31',\n    location: '磁县中心商场',\n    coverImage: '/static/images/activity/discount-1.jpg',\n    isFavorite: false,\n    hot: true,\n    isPaidTop: true, // 付费置顶\n    discountRules: [\n      { threshold: 100, discount: 20 },\n      { threshold: 200, discount: 50 },\n      { threshold: 300, discount: 100 }\n    ],\n    merchantCount: 28,\n    participants: [\n      { avatar: '/static/images/avatar/avatar-12.jpg' },\n      { avatar: '/static/images/avatar/avatar-13.jpg' },\n      { avatar: '/static/images/avatar/avatar-14.jpg' },\n      { avatar: '/static/images/avatar/avatar-15.jpg' },\n      { avatar: '/static/images/avatar/avatar-16.jpg' },\n      { avatar: '/static/images/avatar/avatar-17.jpg' }\n    ]\n  },\n  {\n    id: '5',\n    type: 'groupBuy',\n    title: '亲子团购 | 磁县水上乐园门票',\n    status: 'ongoing',\n    startTime: '2024-07-05',\n    endTime: '2024-08-05',\n    location: '磁县水上乐园',\n    coverImage: '/static/images/activity/group-buy-2.jpg',\n    isFavorite: false,\n    isAdTop: true, // 广告置顶\n    groupPrice: 79,\n    originalPrice: 158,\n    groupSize: 3,\n    currentGroupMembers: 1,\n    participants: [\n      { avatar: '/static/images/avatar/avatar-18.jpg' }\n    ]\n  }\n]);\n\n// 根据当前标签过滤活动\nconst filteredActivities = computed(() => {\n  if (currentTab.value === 0) {\n    return activities.value;\n  }\n  \n  const tabTypeMap = {\n    1: 'groupBuy',\n    2: 'flashSale',\n    3: 'coupon',\n    4: 'discount'\n  };\n  \n  const selectedType = tabTypeMap[currentTab.value];\n  return activities.value.filter(activity => activity.type === selectedType);\n});\n\n// 自动轮播相关变量\nconst scrollLeft = ref(0);\nconst cardWidth = ref(620); // 卡片宽度 + 右边距 (rpx)\nconst cardWidthPx = ref(310); // 卡片宽度像素值\nconst scrollInterval = ref(null);\nconst activityScrollRef = ref(null);\nconst scrollSpeed = 3000; // 轮播间隔时间，单位毫秒\nconst scrollAnimationDuration = 500; // 滚动动画持续时间，单位毫秒\nconst currentScrollIndex = ref(0);\nconst lastScrollLeft = ref(0); // 记录上次滚动位置\nconst isScrolling = ref(false); // 是否正在滚动中\nconst isLooping = ref(true); // 是否循环轮播\nconst centerOffset = ref(0); // 居中偏移量（像素）\nconst centerOffsetRpx = ref(0); // 居中偏移量（rpx）\nconst screenWidth = ref(375); // 屏幕宽度（像素）\n\n// 开始自动轮播\nfunction startAutoScroll() {\n  if (scrollInterval.value || filteredActivities.value.length <= 1) return;\n  \n  scrollInterval.value = setInterval(() => {\n    if (isUserInteracting.value || isScrolling.value) return;\n    \n    // 如果已经到最后一个，则回到第一个\n    if (currentScrollIndex.value >= filteredActivities.value.length - 1) {\n      // 无动画快速回到第一个\n      isScrolling.value = true;\n      currentScrollIndex.value = 0;\n      scrollToIndex(0, true);\n      \n      setTimeout(() => {\n        isScrolling.value = false;\n      }, 50);\n    } else {\n      // 正常向左滚动到下一个\n      currentScrollIndex.value++;\n      isScrolling.value = true;\n      scrollToIndex(currentScrollIndex.value, true);\n      \n      setTimeout(() => {\n        isScrolling.value = false;\n      }, scrollAnimationDuration);\n    }\n  }, scrollSpeed);\n}\n\n// 停止自动轮播\nfunction stopAutoScroll() {\n  if (scrollInterval.value) {\n    clearInterval(scrollInterval.value);\n    scrollInterval.value = null;\n  }\n}\n\n// 处理滚动事件\nfunction handleScroll(e) {\n  if (isUserInteracting.value) {\n    // 用户正在交互，记录当前滚动位置\n    lastScrollLeft.value = e.detail.scrollLeft;\n  }\n}\n\n// 组件挂载时启动自动轮播\nonMounted(() => {\n  // 获取设备信息，根据屏幕宽度调整卡片宽度和居中偏移量\n  uni.getSystemInfo({\n    success: (res) => {\n      // 保存屏幕宽度\n      screenWidth.value = res.windowWidth;\n      \n      // 根据屏幕宽度动态调整卡片宽度\n      let actualCardWidthRpx = 600; // 默认卡片宽度 (rpx)\n      \n      if (res.windowWidth <= 375) {\n        actualCardWidthRpx = 550;\n        cardWidth.value = 570; // 550 + 20 (卡片宽度 + 右边距)\n      } else if (res.windowWidth >= 768) {\n        actualCardWidthRpx = 650;\n        cardWidth.value = 670; // 650 + 20 (卡片宽度 + 右边距)\n      } else {\n        actualCardWidthRpx = 600;\n        cardWidth.value = 620; // 600 + 20 (卡片宽度 + 右边距)\n      }\n      \n      // 计算rpx到px的转换比例\n      const rpxRatio = res.windowWidth / 750;\n      \n      // 计算卡片宽度的像素值\n      cardWidthPx.value = cardWidth.value * rpxRatio;\n      \n      // 计算居中偏移量（像素）= (屏幕宽度 - 卡片宽度) / 2\n      centerOffset.value = (res.windowWidth - (actualCardWidthRpx * rpxRatio)) / 2;\n      \n      // 计算居中偏移量（rpx）\n      centerOffsetRpx.value = centerOffset.value / rpxRatio;\n      \n      console.log('屏幕宽度:', res.windowWidth, 'px');\n      console.log('卡片宽度:', actualCardWidthRpx, 'rpx =', actualCardWidthRpx * rpxRatio, 'px');\n      console.log('居中偏移量:', centerOffset.value, 'px =', centerOffsetRpx.value, 'rpx');\n      \n      // 初始化滚动位置，使第一个卡片居中\n      setTimeout(() => {\n        scrollToIndex(0, false);\n      }, 100);\n    }\n  });\n  \n  // 添加监听器，检测页面显示/隐藏状态\n  uni.onAppShow(() => {\n    if (!isUserInteracting.value) {\n      startAutoScroll();\n    }\n  });\n  \n  uni.onAppHide(() => {\n    stopAutoScroll();\n  });\n  \n  // 延迟启动轮播，确保组件完全渲染\n  setTimeout(() => {\n    startAutoScroll();\n  }, 1000);\n});\n\n// 组件销毁前停止自动轮播\nonBeforeUnmount(() => {\n  stopAutoScroll();\n});\n\n// 切换标签时重置轮播\nfunction switchTab(index) {\n  currentTab.value = index;\n  currentScrollIndex.value = 0;\n  \n  // 重置滚动位置\n  setTimeout(() => {\n    scrollToIndex(0, false);\n  }, 100);\n  \n  // 重新启动轮播\n  stopAutoScroll();\n  setTimeout(() => {\n    startAutoScroll();\n  }, 500);\n}\n\n// 导航到详情页\nfunction navigateToDetail(data) {\n  uni.navigateTo({\n    url: `/subPackages/activity-showcase/pages/detail/index?id=${data.id}&type=${data.type}`,\n    fail: (err) => {\n      console.error('导航失败:', err);\n      uni.showToast({\n        title: '页面跳转失败',\n        icon: 'none'\n      });\n    }\n  });\n}\n\n// 切换收藏状态\nfunction toggleFavorite(id) {\n  const activity = activities.value.find(item => item.id === id);\n  if (activity) {\n    activity.isFavorite = !activity.isFavorite;\n    \n    // 显示提示\n    uni.showToast({\n      title: activity.isFavorite ? '已收藏' : '已取消收藏',\n      icon: 'none'\n    });\n  }\n}\n\n// 处理操作按钮点击\nfunction handleAction(data) {\n  console.log('操作按钮点击:', data);\n  \n  switch(data.type) {\n    case 'groupBuy':\n      if (data.status === 'ongoing') {\n        uni.navigateTo({\n          url: `/subPackages/activity-showcase/pages/group-buy?id=${data.id}`\n        });\n      } else if (data.status === 'upcoming') {\n        uni.showToast({\n          title: '已预约拼团提醒',\n          icon: 'success'\n        });\n      }\n      break;\n      \n    case 'flashSale':\n      if (data.status === 'ongoing') {\n        uni.navigateTo({\n          url: `/subPackages/activity-showcase/pages/flash-sale?id=${data.id}`\n        });\n      } else if (data.status === 'upcoming') {\n        uni.showToast({\n          title: '已设置开始提醒',\n          icon: 'success'\n        });\n      }\n      break;\n      \n    case 'coupon':\n      uni.showToast({\n        title: '优惠券已领取',\n        icon: 'success'\n      });\n      break;\n      \n    case 'discount':\n      uni.navigateTo({\n        url: `/subPackages/activity-showcase/pages/discount?id=${data.id}`\n      });\n      break;\n      \n    default:\n      navigateToDetail(data);\n  }\n}\n\n// 导航到指定页面\nfunction navigateTo(url) {\n  uni.navigateTo({\n    url: url,\n    fail: (err) => {\n      console.error('导航失败:', err);\n      uni.showToast({\n        title: '页面跳转失败',\n        icon: 'none'\n      });\n    }\n  });\n}\n\n// 用户交互相关\nconst isUserInteracting = ref(false);\n\n// 处理触摸开始事件\nfunction handleTouchStart() {\n  isUserInteracting.value = true;\n  stopAutoScroll();\n}\n\n// 滚动到上一个\nfunction scrollToPrev() {\n  if (currentScrollIndex.value <= 0) {\n    if (isLooping.value) {\n      // 循环到最后一个\n      currentScrollIndex.value = filteredActivities.value.length - 1;\n    } else {\n      return;\n    }\n  } else {\n    currentScrollIndex.value--;\n  }\n  \n  // 临时停止自动轮播\n  stopAutoScroll();\n  \n  // 滚动到目标位置\n  scrollToIndex(currentScrollIndex.value);\n  \n  // 延迟恢复自动轮播\n  setTimeout(() => {\n    if (!isUserInteracting.value) {\n      startAutoScroll();\n    }\n  }, 1500);\n}\n\n// 滚动到下一个\nfunction scrollToNext() {\n  if (currentScrollIndex.value >= filteredActivities.value.length - 1) {\n    if (isLooping.value) {\n      // 循环到第一个\n      currentScrollIndex.value = 0;\n    } else {\n      return;\n    }\n  } else {\n    currentScrollIndex.value++;\n  }\n  \n  // 临时停止自动轮播\n  stopAutoScroll();\n  \n  // 滚动到目标位置\n  scrollToIndex(currentScrollIndex.value);\n  \n  // 延迟恢复自动轮播\n  setTimeout(() => {\n    if (!isUserInteracting.value) {\n      startAutoScroll();\n    }\n  }, 1500);\n}\n\n// 滚动到指定索引\nfunction scrollToIndex(index, animate = true) {\n  currentScrollIndex.value = index;\n  \n  // 计算滚动位置：卡片宽度（像素）* 索引\n  const targetScrollLeft = index * cardWidthPx.value;\n  \n  if (!animate) {\n    // 无动画直接设置\n    scrollLeft.value = targetScrollLeft;\n  } else {\n    // 有动画过渡\n    isScrolling.value = true;\n    scrollLeft.value = targetScrollLeft;\n    \n    setTimeout(() => {\n      isScrolling.value = false;\n    }, scrollAnimationDuration);\n  }\n  \n  return targetScrollLeft;\n}\n\n// 处理触摸结束事件\nfunction handleTouchEnd() {\n  isUserInteracting.value = false;\n  \n  // 根据滚动位置确定当前应该显示的卡片索引\n  const estimatedIndex = Math.round(lastScrollLeft.value / cardWidthPx.value);\n  const maxIndex = filteredActivities.value.length - 1;\n  \n  // 边界检查\n  currentScrollIndex.value = Math.max(0, Math.min(estimatedIndex, maxIndex));\n  \n  // 平滑滚动到正确位置\n  scrollToIndex(currentScrollIndex.value);\n  \n  // 延迟恢复自动轮播\n  setTimeout(() => {\n    if (!isUserInteracting.value) {\n      startAutoScroll();\n    }\n  }, 1500);\n}\n</script>\n\n<style scoped>\n/* 活动中心模块基础样式 */\n.activity-center {\n  margin: 30rpx 0;\n  padding-bottom: 20rpx;\n}\n\n/* 模块标题区 */\n.activity-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 0 30rpx;\n  margin-bottom: 20rpx;\n}\n\n.title-container {\n  display: flex;\n  align-items: center;\n}\n\n.title-bar {\n  width: 8rpx;\n  height: 36rpx;\n  background: linear-gradient(to bottom, #007aff, #5ac8fa);\n  border-radius: 4rpx;\n  margin-right: 16rpx;\n}\n\n.activity-title {\n  font-size: 36rpx;\n  font-weight: 600;\n  color: #000000;\n}\n\n.activity-badge {\n  margin-left: 16rpx;\n  padding: 4rpx 12rpx;\n  background-color: rgba(0, 122, 255, 0.1);\n  border-radius: 20rpx;\n}\n\n.badge-text {\n  font-size: 22rpx;\n  color: #007aff;\n}\n\n.more-btn {\n  display: flex;\n  align-items: center;\n  padding: 10rpx;\n}\n\n.more-text {\n  font-size: 28rpx;\n  color: #8e8e93;\n}\n\n.more-icon {\n  margin-left: 4rpx;\n  color: #8e8e93;\n}\n\n/* 标签导航栏 */\n.activity-tabs-container {\n  margin-bottom: 20rpx;\n}\n\n.activity-tabs {\n  white-space: nowrap;\n  padding: 0 20rpx;\n}\n\n.activity-tab {\n  display: inline-flex;\n  flex-direction: column;\n  align-items: center;\n  padding: 16rpx 24rpx;\n  position: relative;\n  transition: all 0.3s cubic-bezier(0.25, 1, 0.5, 1);\n  border-radius: 16rpx;\n  overflow: hidden;\n  margin: 0 6rpx;\n}\n\n.activity-tab::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: transparent;\n  z-index: -1;\n  transition: all 0.3s ease;\n  opacity: 0;\n  transform: scale(0.9);\n}\n\n.activity-tab.active::before {\n  background: linear-gradient(35deg, rgba(0, 122, 255, 0.12), rgba(90, 200, 250, 0.08));\n  opacity: 1;\n  transform: scale(1);\n}\n\n.tab-content {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  position: relative;\n  z-index: 2;\n}\n\n.tab-icon {\n  margin-bottom: 8rpx;\n  color: #8e8e93;\n  transition: all 0.3s ease;\n  transform: translateY(0);\n}\n\n.activity-tab.active .tab-icon {\n  color: #007aff;\n  transform: translateY(-2rpx);\n  filter: drop-shadow(0 1rpx 2rpx rgba(0, 122, 255, 0.3));\n}\n\n.tab-text {\n  font-size: 28rpx;\n  color: #8e8e93;\n  transition: all 0.3s ease;\n  position: relative;\n}\n\n.activity-tab.active .tab-text {\n  color: #007aff;\n  font-weight: 600;\n  text-shadow: 0 0 0.5px rgba(0, 122, 255, 0.3);\n}\n\n.tab-line {\n  position: absolute;\n  bottom: 6rpx;\n  left: 50%;\n  transform: translateX(-50%);\n  width: 40rpx;\n  height: 4rpx;\n  background: linear-gradient(90deg, #007aff, #5ac8fa);\n  border-radius: 4rpx;\n  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);\n  box-shadow: 0 1rpx 3rpx rgba(0, 122, 255, 0.3);\n}\n\n.activity-tab.active .tab-line {\n  width: 60rpx;\n}\n\n.tab-glow {\n  position: absolute;\n  bottom: -10rpx;\n  left: 50%;\n  transform: translateX(-50%);\n  width: 80rpx;\n  height: 40rpx;\n  background: radial-gradient(ellipse at center, rgba(0, 122, 255, 0.2) 0%, rgba(0, 122, 255, 0) 70%);\n  border-radius: 50%;\n  z-index: 1;\n  opacity: 0.7;\n  filter: blur(4rpx);\n}\n\n/* 活动列表 */\n.activity-scroll-container {\n  position: relative;\n  width: 100%;\n  padding-bottom: 30rpx;\n}\n\n.activity-scroll {\n  width: 100%;\n  overflow: hidden;\n}\n\n.activity-list {\n  display: flex;\n  padding: 10rpx 0;\n  box-sizing: border-box;\n}\n\n.activity-item-placeholder {\n  flex-shrink: 0;\n  height: 1px;\n}\n\n.activity-item {\n  width: 600rpx;\n  flex-shrink: 0;\n  margin-right: 20rpx;\n  transition: all 0.3s ease;\n  will-change: transform, opacity;\n}\n\n.activity-item:last-child {\n  margin-right: 20rpx; /* 确保最后一个卡片也有右边距 */\n}\n\n/* 轮播控制按钮 */\n.carousel-controls {\n  position: absolute;\n  top: 50%;\n  left: 0;\n  right: 0;\n  display: flex;\n  justify-content: space-between;\n  padding: 0 10rpx;\n  transform: translateY(-50%);\n  z-index: 10;\n  pointer-events: none;\n}\n\n.control-btn {\n  width: 60rpx;\n  height: 60rpx;\n  border-radius: 50%;\n  background-color: rgba(255, 255, 255, 0.8);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);\n  color: #333333;\n  transition: all 0.3s ease;\n  pointer-events: auto;\n  opacity: 0.7;\n}\n\n.control-btn:active {\n  transform: scale(0.9);\n  background-color: rgba(255, 255, 255, 0.9);\n}\n\n.control-btn.disabled {\n  opacity: 0.3;\n  pointer-events: none;\n}\n\n.control-btn:hover {\n  opacity: 1;\n}\n\n/* 轮播指示器 */\n.carousel-indicators {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  margin-top: 20rpx;\n}\n\n.indicator-dot {\n  width: 16rpx;\n  height: 16rpx;\n  border-radius: 8rpx;\n  background-color: rgba(142, 142, 147, 0.3);\n  margin: 0 8rpx;\n  transition: all 0.3s ease;\n  cursor: pointer;\n}\n\n.indicator-dot.active {\n  width: 24rpx;\n  background-color: #007aff;\n}\n\n/* 媒体查询 - 适配不同屏幕尺寸 */\n@media screen and (max-width: 375px) {\n  .activity-item {\n    width: 550rpx;\n  }\n}\n\n@media screen and (min-width: 768px) {\n  .activity-item {\n    width: 650rpx;\n  }\n}\n</style> ", "import Component from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/subPackages/activity-showcase/components/activity/ActivityCenter.vue'\nwx.createComponent(Component)"], "names": ["ref", "computed", "onMounted", "uni", "onBeforeUnmount"], "mappings": ";;;;;;;;;;;;AA0HA,MAAM,sBAAsB,MAAW;AAwJvC,MAAM,cAAc;AACpB,MAAM,0BAA0B;;;;AAtJhC,UAAM,eAAeA,cAAAA,IAAI;AAAA,MACvB,EAAE,MAAM,QAAQ,MAAM,KAAM;AAAA,MAC5B,EAAE,MAAM,QAAQ,MAAM,KAAM;AAAA,MAC5B,EAAE,MAAM,QAAQ,MAAM,KAAM;AAAA,MAC5B,EAAE,MAAM,OAAO,MAAM,KAAM;AAAA,MAC3B,EAAE,MAAM,QAAQ,MAAM,KAAM;AAAA,IAC9B,CAAC;AAGD,UAAM,aAAaA,cAAAA,IAAI,CAAC;AAGxB,UAAM,aAAaA,cAAAA,IAAI;AAAA,MACrB;AAAA,QACE,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,OAAO;AAAA,QACP,QAAQ;AAAA,QACR,WAAW;AAAA,QACX,SAAS;AAAA,QACT,UAAU;AAAA,QACV,YAAY;AAAA,QACZ,YAAY;AAAA,QACZ,KAAK;AAAA,QACL,WAAW;AAAA;AAAA,QACX,YAAY;AAAA,QACZ,eAAe;AAAA,QACf,WAAW;AAAA,QACX,qBAAqB;AAAA,QACrB,cAAc;AAAA,UACZ,EAAE,QAAQ,qCAAsC;AAAA,UAChD,EAAE,QAAQ,qCAAsC;AAAA,UAChD,EAAE,QAAQ,qCAAsC;AAAA,UAChD,EAAE,QAAQ,qCAAsC;AAAA,QACjD;AAAA,MACF;AAAA,MACD;AAAA,QACE,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,OAAO;AAAA,QACP,QAAQ;AAAA,QACR,WAAW;AAAA,QACX,SAAS;AAAA,QACT,UAAU;AAAA,QACV,YAAY;AAAA,QACZ,YAAY;AAAA,QACZ,SAAS;AAAA;AAAA,QACT,WAAW;AAAA,QACX,eAAe;AAAA,QACf,YAAY;AAAA,QACZ,WAAW;AAAA,QACX,cAAc;AAAA,UACZ,EAAE,QAAQ,qCAAsC;AAAA,UAChD,EAAE,QAAQ,qCAAsC;AAAA,UAChD,EAAE,QAAQ,qCAAsC;AAAA,UAChD,EAAE,QAAQ,qCAAsC;AAAA,UAChD,EAAE,QAAQ,qCAAsC;AAAA,QACjD;AAAA,MACF;AAAA,MACD;AAAA,QACE,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,OAAO;AAAA,QACP,QAAQ;AAAA,QACR,WAAW;AAAA,QACX,SAAS;AAAA,QACT,UAAU;AAAA,QACV,YAAY;AAAA,QACZ,YAAY;AAAA,QACZ,YAAY;AAAA,QACZ,aAAa;AAAA,QACb,iBAAiB;AAAA,QACjB,gBAAgB;AAAA,QAChB,cAAc;AAAA,UACZ,EAAE,QAAQ,sCAAuC;AAAA,UACjD,EAAE,QAAQ,sCAAuC;AAAA,QAClD;AAAA,MACF;AAAA,MACD;AAAA,QACE,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,OAAO;AAAA,QACP,QAAQ;AAAA,QACR,WAAW;AAAA,QACX,SAAS;AAAA,QACT,UAAU;AAAA,QACV,YAAY;AAAA,QACZ,YAAY;AAAA,QACZ,KAAK;AAAA,QACL,WAAW;AAAA;AAAA,QACX,eAAe;AAAA,UACb,EAAE,WAAW,KAAK,UAAU,GAAI;AAAA,UAChC,EAAE,WAAW,KAAK,UAAU,GAAI;AAAA,UAChC,EAAE,WAAW,KAAK,UAAU,IAAK;AAAA,QAClC;AAAA,QACD,eAAe;AAAA,QACf,cAAc;AAAA,UACZ,EAAE,QAAQ,sCAAuC;AAAA,UACjD,EAAE,QAAQ,sCAAuC;AAAA,UACjD,EAAE,QAAQ,sCAAuC;AAAA,UACjD,EAAE,QAAQ,sCAAuC;AAAA,UACjD,EAAE,QAAQ,sCAAuC;AAAA,UACjD,EAAE,QAAQ,sCAAuC;AAAA,QAClD;AAAA,MACF;AAAA,MACD;AAAA,QACE,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,OAAO;AAAA,QACP,QAAQ;AAAA,QACR,WAAW;AAAA,QACX,SAAS;AAAA,QACT,UAAU;AAAA,QACV,YAAY;AAAA,QACZ,YAAY;AAAA,QACZ,SAAS;AAAA;AAAA,QACT,YAAY;AAAA,QACZ,eAAe;AAAA,QACf,WAAW;AAAA,QACX,qBAAqB;AAAA,QACrB,cAAc;AAAA,UACZ,EAAE,QAAQ,sCAAuC;AAAA,QAClD;AAAA,MACF;AAAA,IACH,CAAC;AAGD,UAAM,qBAAqBC,cAAQ,SAAC,MAAM;AACxC,UAAI,WAAW,UAAU,GAAG;AAC1B,eAAO,WAAW;AAAA,MACnB;AAED,YAAM,aAAa;AAAA,QACjB,GAAG;AAAA,QACH,GAAG;AAAA,QACH,GAAG;AAAA,QACH,GAAG;AAAA,MACP;AAEE,YAAM,eAAe,WAAW,WAAW,KAAK;AAChD,aAAO,WAAW,MAAM,OAAO,cAAY,SAAS,SAAS,YAAY;AAAA,IAC3E,CAAC;AAGD,UAAM,aAAaD,cAAAA,IAAI,CAAC;AACxB,UAAM,YAAYA,cAAAA,IAAI,GAAG;AACzB,UAAM,cAAcA,cAAAA,IAAI,GAAG;AAC3B,UAAM,iBAAiBA,cAAAA,IAAI,IAAI;AACLA,kBAAG,IAAC,IAAI;AAGlC,UAAM,qBAAqBA,cAAAA,IAAI,CAAC;AAChC,UAAM,iBAAiBA,cAAAA,IAAI,CAAC;AAC5B,UAAM,cAAcA,cAAAA,IAAI,KAAK;AAC7B,UAAM,YAAYA,cAAAA,IAAI,IAAI;AAC1B,UAAM,eAAeA,cAAAA,IAAI,CAAC;AAC1B,UAAM,kBAAkBA,cAAAA,IAAI,CAAC;AAC7B,UAAM,cAAcA,cAAAA,IAAI,GAAG;AAG3B,aAAS,kBAAkB;AACzB,UAAI,eAAe,SAAS,mBAAmB,MAAM,UAAU;AAAG;AAElE,qBAAe,QAAQ,YAAY,MAAM;AACvC,YAAI,kBAAkB,SAAS,YAAY;AAAO;AAGlD,YAAI,mBAAmB,SAAS,mBAAmB,MAAM,SAAS,GAAG;AAEnE,sBAAY,QAAQ;AACpB,6BAAmB,QAAQ;AAC3B,wBAAc,GAAG,IAAI;AAErB,qBAAW,MAAM;AACf,wBAAY,QAAQ;AAAA,UACrB,GAAE,EAAE;AAAA,QACX,OAAW;AAEL,6BAAmB;AACnB,sBAAY,QAAQ;AACpB,wBAAc,mBAAmB,OAAO,IAAI;AAE5C,qBAAW,MAAM;AACf,wBAAY,QAAQ;AAAA,UACrB,GAAE,uBAAuB;AAAA,QAC3B;AAAA,MACF,GAAE,WAAW;AAAA,IAChB;AAGA,aAAS,iBAAiB;AACxB,UAAI,eAAe,OAAO;AACxB,sBAAc,eAAe,KAAK;AAClC,uBAAe,QAAQ;AAAA,MACxB;AAAA,IACH;AAGA,aAAS,aAAa,GAAG;AACvB,UAAI,kBAAkB,OAAO;AAE3B,uBAAe,QAAQ,EAAE,OAAO;AAAA,MACjC;AAAA,IACH;AAGAE,kBAAAA,UAAU,MAAM;AAEdC,oBAAAA,MAAI,cAAc;AAAA,QAChB,SAAS,CAAC,QAAQ;AAEhB,sBAAY,QAAQ,IAAI;AAGxB,cAAI,qBAAqB;AAEzB,cAAI,IAAI,eAAe,KAAK;AAC1B,iCAAqB;AACrB,sBAAU,QAAQ;AAAA,UAC1B,WAAiB,IAAI,eAAe,KAAK;AACjC,iCAAqB;AACrB,sBAAU,QAAQ;AAAA,UAC1B,OAAa;AACL,iCAAqB;AACrB,sBAAU,QAAQ;AAAA,UACnB;AAGD,gBAAM,WAAW,IAAI,cAAc;AAGnC,sBAAY,QAAQ,UAAU,QAAQ;AAGtC,uBAAa,SAAS,IAAI,cAAe,qBAAqB,YAAa;AAG3E,0BAAgB,QAAQ,aAAa,QAAQ;AAE7CA,0HAAY,SAAS,IAAI,aAAa,IAAI;AAC1CA,wBAAAA,kGAAY,SAAS,oBAAoB,SAAS,qBAAqB,UAAU,IAAI;AACrFA,wBAAAA,MAAY,MAAA,OAAA,+EAAA,UAAU,aAAa,OAAO,QAAQ,gBAAgB,OAAO,KAAK;AAG9E,qBAAW,MAAM;AACf,0BAAc,GAAG,KAAK;AAAA,UACvB,GAAE,GAAG;AAAA,QACP;AAAA,MACL,CAAG;AAGDA,oBAAG,MAAC,UAAU,MAAM;AAClB,YAAI,CAAC,kBAAkB,OAAO;AAC5B;QACD;AAAA,MACL,CAAG;AAEDA,oBAAG,MAAC,UAAU,MAAM;AAClB;MACJ,CAAG;AAGD,iBAAW,MAAM;AACf;MACD,GAAE,GAAI;AAAA,IACT,CAAC;AAGDC,kBAAAA,gBAAgB,MAAM;AACpB;IACF,CAAC;AAGD,aAAS,UAAU,OAAO;AACxB,iBAAW,QAAQ;AACnB,yBAAmB,QAAQ;AAG3B,iBAAW,MAAM;AACf,sBAAc,GAAG,KAAK;AAAA,MACvB,GAAE,GAAG;AAGN;AACA,iBAAW,MAAM;AACf;MACD,GAAE,GAAG;AAAA,IACR;AAGA,aAAS,iBAAiB,MAAM;AAC9BD,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,wDAAwD,KAAK,EAAE,SAAS,KAAK,IAAI;AAAA,QACtF,MAAM,CAAC,QAAQ;AACbA,wBAAA,MAAA,MAAA,SAAA,+EAAc,SAAS,GAAG;AAC1BA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,UACd,CAAO;AAAA,QACF;AAAA,MACL,CAAG;AAAA,IACH;AAGA,aAAS,eAAe,IAAI;AAC1B,YAAM,WAAW,WAAW,MAAM,KAAK,UAAQ,KAAK,OAAO,EAAE;AAC7D,UAAI,UAAU;AACZ,iBAAS,aAAa,CAAC,SAAS;AAGhCA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO,SAAS,aAAa,QAAQ;AAAA,UACrC,MAAM;AAAA,QACZ,CAAK;AAAA,MACF;AAAA,IACH;AAGA,aAAS,aAAa,MAAM;AAC1BA,oBAAY,MAAA,MAAA,OAAA,+EAAA,WAAW,IAAI;AAE3B,cAAO,KAAK,MAAI;AAAA,QACd,KAAK;AACH,cAAI,KAAK,WAAW,WAAW;AAC7BA,0BAAAA,MAAI,WAAW;AAAA,cACb,KAAK,qDAAqD,KAAK,EAAE;AAAA,YAC3E,CAAS;AAAA,UACT,WAAiB,KAAK,WAAW,YAAY;AACrCA,0BAAAA,MAAI,UAAU;AAAA,cACZ,OAAO;AAAA,cACP,MAAM;AAAA,YAChB,CAAS;AAAA,UACF;AACD;AAAA,QAEF,KAAK;AACH,cAAI,KAAK,WAAW,WAAW;AAC7BA,0BAAAA,MAAI,WAAW;AAAA,cACb,KAAK,sDAAsD,KAAK,EAAE;AAAA,YAC5E,CAAS;AAAA,UACT,WAAiB,KAAK,WAAW,YAAY;AACrCA,0BAAAA,MAAI,UAAU;AAAA,cACZ,OAAO;AAAA,cACP,MAAM;AAAA,YAChB,CAAS;AAAA,UACF;AACD;AAAA,QAEF,KAAK;AACHA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,UACd,CAAO;AACD;AAAA,QAEF,KAAK;AACHA,wBAAAA,MAAI,WAAW;AAAA,YACb,KAAK,oDAAoD,KAAK,EAAE;AAAA,UACxE,CAAO;AACD;AAAA,QAEF;AACE,2BAAiB,IAAI;AAAA,MACxB;AAAA,IACH;AAGA,aAAS,WAAW,KAAK;AACvBA,oBAAAA,MAAI,WAAW;AAAA,QACb;AAAA,QACA,MAAM,CAAC,QAAQ;AACbA,wBAAA,MAAA,MAAA,SAAA,+EAAc,SAAS,GAAG;AAC1BA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,UACd,CAAO;AAAA,QACF;AAAA,MACL,CAAG;AAAA,IACH;AAGA,UAAM,oBAAoBH,cAAAA,IAAI,KAAK;AAGnC,aAAS,mBAAmB;AAC1B,wBAAkB,QAAQ;AAC1B;IACF;AAGA,aAAS,eAAe;AACtB,UAAI,mBAAmB,SAAS,GAAG;AACjC,YAAI,UAAU,OAAO;AAEnB,6BAAmB,QAAQ,mBAAmB,MAAM,SAAS;AAAA,QACnE,OAAW;AACL;AAAA,QACD;AAAA,MACL,OAAS;AACL,2BAAmB;AAAA,MACpB;AAGD;AAGA,oBAAc,mBAAmB,KAAK;AAGtC,iBAAW,MAAM;AACf,YAAI,CAAC,kBAAkB,OAAO;AAC5B;QACD;AAAA,MACF,GAAE,IAAI;AAAA,IACT;AAGA,aAAS,eAAe;AACtB,UAAI,mBAAmB,SAAS,mBAAmB,MAAM,SAAS,GAAG;AACnE,YAAI,UAAU,OAAO;AAEnB,6BAAmB,QAAQ;AAAA,QACjC,OAAW;AACL;AAAA,QACD;AAAA,MACL,OAAS;AACL,2BAAmB;AAAA,MACpB;AAGD;AAGA,oBAAc,mBAAmB,KAAK;AAGtC,iBAAW,MAAM;AACf,YAAI,CAAC,kBAAkB,OAAO;AAC5B;QACD;AAAA,MACF,GAAE,IAAI;AAAA,IACT;AAGA,aAAS,cAAc,OAAO,UAAU,MAAM;AAC5C,yBAAmB,QAAQ;AAG3B,YAAM,mBAAmB,QAAQ,YAAY;AAE7C,UAAI,CAAC,SAAS;AAEZ,mBAAW,QAAQ;AAAA,MACvB,OAAS;AAEL,oBAAY,QAAQ;AACpB,mBAAW,QAAQ;AAEnB,mBAAW,MAAM;AACf,sBAAY,QAAQ;AAAA,QACrB,GAAE,uBAAuB;AAAA,MAC3B;AAED,aAAO;AAAA,IACT;AAGA,aAAS,iBAAiB;AACxB,wBAAkB,QAAQ;AAG1B,YAAM,iBAAiB,KAAK,MAAM,eAAe,QAAQ,YAAY,KAAK;AAC1E,YAAM,WAAW,mBAAmB,MAAM,SAAS;AAGnD,yBAAmB,QAAQ,KAAK,IAAI,GAAG,KAAK,IAAI,gBAAgB,QAAQ,CAAC;AAGzE,oBAAc,mBAAmB,KAAK;AAGtC,iBAAW,MAAM;AACf,YAAI,CAAC,kBAAkB,OAAO;AAC5B;QACD;AAAA,MACF,GAAE,IAAI;AAAA,IACT;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AClmBA,GAAG,gBAAgB,SAAS;"}