/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 移除阿里妈妈字体引用，改用系统默认字体 */
/* 移除原有阿里妈妈字体定义
$uni-font-family: 'AlimamaShuHeiTi';
$uni-font-family-text: 'AlimamaShuHeiTi', -apple-system, BlinkMacSystemFont, 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;
*/
body, html, #app, .index-container {
  font-family: "AlimamaShuHeiTi", -apple-system, BlinkMacSystemFont, "Helvetica Neue", "PingFang SC", "Microsoft YaHei", sans-serif;
}
.levels-container {
  min-height: 100vh;
  background-color: #F5F7FA;
  padding-bottom: 30rpx;
}

/* 导航栏样式 */
.navbar {
  position: -webkit-sticky;
  position: sticky;
  top: 0;
  left: 0;
  right: 0;
  background: linear-gradient(135deg, #A764CA, #6B0FBE);
  color: #fff;
  padding: 88rpx 32rpx 30rpx;
  display: flex;
  align-items: center;
  z-index: 100;
  box-shadow: 0 4rpx 20rpx rgba(107, 15, 190, 0.15);
}
.navbar-back {
  width: 72rpx;
  height: 72rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.back-icon {
  width: 24rpx;
  height: 24rpx;
  border-left: 4rpx solid #fff;
  border-bottom: 4rpx solid #fff;
  transform: rotate(45deg);
}
.navbar-title {
  flex: 1;
  text-align: center;
  font-size: 36rpx;
  font-weight: 600;
  letter-spacing: 1rpx;
}
.navbar-right {
  width: 72rpx;
  height: 72rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.help-icon {
  width: 48rpx;
  height: 48rpx;
  border-radius: 24rpx;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  font-weight: bold;
}

/* 开关卡片 */
.switch-card {
  margin: 30rpx;
  background: #FFFFFF;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}
.switch-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.switch-label {
  flex: 1;
}
.label-text {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
  display: block;
}
.label-desc {
  font-size: 24rpx;
  color: #999;
}

/* 等级列表 */
.levels-list {
  margin: 30rpx;
}
.level-card {
  background: #FFFFFF;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}
.level-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}
.level-title {
  display: flex;
  align-items: center;
}
.level-name {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-right: 16rpx;
}
.level-tag {
  font-size: 24rpx;
  color: #fff;
  padding: 4rpx 12rpx;
  border-radius: 20rpx;
}
.level-actions {
  display: flex;
  align-items: center;
}
.edit-icon,
.delete-icon {
  width: 40rpx;
  height: 40rpx;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  margin-left: 20rpx;
}
.edit-icon {
  background-color: #409EFF;
  border-radius: 50%;
  position: relative;
}
.edit-icon::before,
.edit-icon::after {
  content: "";
  position: absolute;
  background-color: white;
}
.edit-icon::before {
  width: 16rpx;
  height: 2rpx;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}
.edit-icon::after {
  width: 2rpx;
  height: 16rpx;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}
.delete-icon {
  background-color: #F56C6C;
  border-radius: 50%;
  position: relative;
}
.delete-icon::before {
  content: "";
  position: absolute;
  width: 16rpx;
  height: 2rpx;
  background-color: white;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) rotate(45deg);
}
.delete-icon::after {
  content: "";
  position: absolute;
  width: 16rpx;
  height: 2rpx;
  background-color: white;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) rotate(-45deg);
}
.level-content {
  padding-top: 20rpx;
  border-top: 1rpx solid #f0f0f0;
}
.level-item {
  margin-bottom: 16rpx;
}
.level-item:last-child {
  margin-bottom: 0;
}
.item-label {
  font-size: 26rpx;
  color: #999;
  margin-bottom: 8rpx;
  display: block;
}
.item-value {
  font-size: 26rpx;
  color: #333;
}

/* 添加等级 */
.add-level {
  background: #FFFFFF;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2rpx dashed #ddd;
}
.add-icon {
  width: 40rpx;
  height: 40rpx;
  background-color: #6B0FBE;
  border-radius: 50%;
  position: relative;
  margin-right: 16rpx;
}
.add-icon::before,
.add-icon::after {
  content: "";
  position: absolute;
  background-color: white;
}
.add-icon::before {
  width: 20rpx;
  height: 2rpx;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}
.add-icon::after {
  width: 2rpx;
  height: 20rpx;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}
.add-text {
  font-size: 28rpx;
  color: #6B0FBE;
}

/* 等级编辑弹窗 */
.level-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
}
.modal-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
}
.modal-content {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 80%;
  max-height: 90%;
  background-color: #FFFFFF;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
  overflow-y: auto;
}
.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}
.modal-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}
.close-icon {
  width: 40rpx;
  height: 40rpx;
  position: relative;
}
.close-icon::before,
.close-icon::after {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  width: 32rpx;
  height: 2rpx;
  background-color: #999;
}
.close-icon::before {
  transform: translate(-50%, -50%) rotate(45deg);
}
.close-icon::after {
  transform: translate(-50%, -50%) rotate(-45deg);
}
.form-content {
  margin-bottom: 30rpx;
}
.form-item {
  margin-bottom: 20rpx;
}
.form-label {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 12rpx;
  display: block;
}
.form-input {
  width: 100%;
  height: 80rpx;
  background: #F5F7FA;
  border-radius: 10rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  color: #333;
  box-sizing: border-box;
}
.form-textarea {
  width: 100%;
  height: 160rpx;
  background: #F5F7FA;
  border-radius: 10rpx;
  padding: 20rpx;
  font-size: 28rpx;
  color: #333;
  box-sizing: border-box;
}
.color-picker {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -10rpx;
}
.color-option {
  width: 60rpx;
  height: 60rpx;
  border-radius: 30rpx;
  margin: 10rpx;
  border: 2rpx solid transparent;
}
.color-option.active {
  border-color: #333;
  box-shadow: 0 0 0 4rpx rgba(0, 0, 0, 0.1);
}
.condition-type {
  display: flex;
  margin-bottom: 16rpx;
}
.type-option {
  flex: 1;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  color: #666;
  background: #F5F7FA;
  border-radius: 10rpx;
  margin-right: 20rpx;
}
.type-option:last-child {
  margin-right: 0;
}
.type-option.active {
  background: #6B0FBE;
  color: #fff;
}
.modal-footer {
  display: flex;
  justify-content: space-between;
}
.cancel-btn,
.submit-btn {
  width: 48%;
  height: 80rpx;
  border-radius: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
}
.cancel-btn {
  background: #F5F7FA;
  color: #666;
}
.submit-btn {
  background: linear-gradient(135deg, #A764CA, #6B0FBE);
  color: #fff;
}
.submit-btn.disabled {
  background: #cccccc;
  color: #ffffff;
}

/* 保存按钮 */
.save-section {
  margin: 30rpx;
}
.save-btn {
  background: linear-gradient(135deg, #A764CA, #6B0FBE);
  color: #fff;
  border: none;
  border-radius: 40rpx;
  font-size: 32rpx;
  padding: 20rpx 0;
  line-height: 1.5;
  width: 100%;
}