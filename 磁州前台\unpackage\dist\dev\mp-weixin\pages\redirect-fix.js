"use strict";
const common_vendor = require("../common/vendor.js");
const _sfc_main = {
  onLoad(options) {
    common_vendor.index.__f__("log", "at pages/redirect-fix.vue:11", "接收到的跳转参数:", JSON.stringify(options));
    const params = Object.keys(options).map((key) => `${key}=${encodeURIComponent(options[key])}`).join("&");
    const pageType = options.pageType || "info-detail";
    let url = `/pages/publish/${pageType}?${params}`;
    common_vendor.index.__f__("log", "at pages/redirect-fix.vue:23", "尝试跳转到原始页面:", url);
    common_vendor.index.navigateTo({
      url,
      success: () => {
        common_vendor.index.__f__("log", "at pages/redirect-fix.vue:28", "跳转成功");
      },
      fail: (err) => {
        common_vendor.index.__f__("error", "at pages/redirect-fix.vue:31", "跳转原始页面失败", err);
        common_vendor.index.showToast({
          title: "页面跳转失败，请稍后重试",
          icon: "none",
          duration: 1e3
        });
      }
    });
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {};
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../.sourcemap/mp-weixin/pages/redirect-fix.js.map
