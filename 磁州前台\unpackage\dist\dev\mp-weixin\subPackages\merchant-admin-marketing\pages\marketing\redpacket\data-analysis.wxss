/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 移除阿里妈妈字体引用，改用系统默认字体 */
/* 移除原有阿里妈妈字体定义
$uni-font-family: 'AlimamaShuHeiTi';
$uni-font-family-text: 'AlimamaShuHeiTi', -apple-system, BlinkMacSystemFont, 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;
*/
body, html, #app, .index-container {
  font-family: "AlimamaShuHeiTi", -apple-system, BlinkMacSystemFont, "Helvetica Neue", "PingFang SC", "Microsoft YaHei", sans-serif;
}
.page-container {
  min-height: 100vh;
  background-color: #F5F7FA;
  padding-bottom: env(safe-area-inset-bottom);
}

/* 导航栏样式 */
.navbar {
  position: -webkit-sticky;
  position: sticky;
  top: 0;
  left: 0;
  right: 0;
  background: linear-gradient(135deg, #FF5858, #FF0000);
  color: #fff;
  padding: 44px 16px 15px;
  display: flex;
  align-items: center;
  z-index: 100;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.15);
}
.navbar-back {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.back-icon {
  width: 12px;
  height: 12px;
  border-left: 2px solid #fff;
  border-bottom: 2px solid #fff;
  transform: rotate(45deg);
}
.navbar-title {
  flex: 1;
  text-align: center;
  font-size: 18px;
  font-weight: 600;
  letter-spacing: 0.5px;
}
.navbar-right {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.help-icon {
  width: 24px;
  height: 24px;
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: bold;
}

/* 筛选样式 */
.filter-section {
  background-color: #fff;
  padding: 15px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}
.filter-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.date-picker,
.type-filter {
  display: flex;
  align-items: center;
  background-color: #F5F7FA;
  padding: 8px 12px;
  border-radius: 15px;
}
.date-text,
.filter-text {
  font-size: 12px;
  color: #666;
  margin-right: 5px;
}
.date-icon,
.filter-icon {
  width: 10px;
  height: 10px;
  border-left: 1px solid #666;
  border-bottom: 1px solid #666;
  transform: rotate(-45deg);
}

/* 数据概览样式 */
.overview-section {
  background-color: #fff;
  margin: 15px;
  border-radius: 12px;
  padding: 15px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}
.overview-header {
  margin-bottom: 15px;
}
.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}
.stats-cards {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}
.stats-card {
  width: 48%;
  background: linear-gradient(135deg, #FFF, #F5F7FA);
  border-radius: 10px;
  padding: 15px;
  margin-bottom: 10px;
  position: relative;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.03);
}
.card-value {
  font-size: 22px;
  font-weight: bold;
  color: #333;
  margin-bottom: 5px;
}
.card-label {
  font-size: 12px;
  color: #666;
}
.card-trend {
  position: absolute;
  top: 15px;
  right: 15px;
  display: flex;
  align-items: center;
  font-size: 12px;
}
.card-trend.up {
  color: #FF5858;
}
.card-trend.down {
  color: #2ED573;
}
.trend-arrow {
  width: 8px;
  height: 8px;
  border-left: 1px solid currentColor;
  border-top: 1px solid currentColor;
  margin-right: 2px;
}
.up .trend-arrow {
  transform: rotate(45deg);
}
.down .trend-arrow {
  transform: rotate(-135deg);
}

/* 图表样式 */
.chart-section {
  background-color: #fff;
  margin: 15px;
  border-radius: 12px;
  padding: 15px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}
.chart-tabs {
  display: flex;
}
.chart-tab {
  padding: 5px 10px;
  margin-left: 5px;
  border-radius: 15px;
  background-color: #F5F7FA;
}
.chart-tab.active {
  background-color: #FF5858;
  color: #fff;
}
.tab-text {
  font-size: 12px;
}
.chart-container {
  height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.chart-placeholder {
  width: 100%;
  height: auto;
}

/* 用户分析样式 */
.user-analysis-section {
  background-color: #fff;
  margin: 15px;
  border-radius: 12px;
  padding: 15px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}
.user-stats {
  display: flex;
  justify-content: space-around;
  margin-top: 15px;
}
.user-stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}
.stat-circle {
  width: 80px;
  height: 80px;
  border-radius: 40px;
  position: relative;
  margin-bottom: 10px;
}
.inner-circle {
  position: absolute;
  top: 10px;
  left: 10px;
  width: 60px;
  height: 60px;
  border-radius: 30px;
  background-color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
}
.circle-value {
  font-size: 16px;
  font-weight: bold;
  color: #333;
}
.stat-label {
  font-size: 12px;
  color: #666;
}

/* 类型分析样式 */
.type-analysis-section {
  background-color: #fff;
  margin: 15px;
  border-radius: 12px;
  padding: 15px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}
.type-stats {
  margin-top: 15px;
}
.type-stat-item {
  margin-bottom: 15px;
}
.type-bar-container {
  height: 10px;
  background-color: #F5F7FA;
  border-radius: 5px;
  overflow: hidden;
  margin-bottom: 5px;
}
.type-bar {
  height: 100%;
  border-radius: 5px;
}
.type-info {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
}
.type-name {
  color: #666;
}
.type-value {
  color: #333;
  font-weight: 500;
}

/* 排行榜样式 */
.ranking-section {
  background-color: #fff;
  margin: 15px;
  border-radius: 12px;
  padding: 15px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}
.ranking-tabs {
  display: flex;
  margin-bottom: 15px;
}
.ranking-tab {
  padding: 5px 15px;
  margin-right: 10px;
  border-radius: 15px;
  background-color: #F5F7FA;
}
.ranking-tab.active {
  background-color: #FF5858;
  color: #fff;
}
.ranking-item {
  display: flex;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #eee;
}
.ranking-item:last-child {
  border-bottom: none;
}
.ranking-number {
  width: 24px;
  height: 24px;
  border-radius: 12px;
  background-color: #F5F7FA;
  color: #666;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  margin-right: 10px;
}
.ranking-number.top-three {
  background-color: #FF5858;
  color: #fff;
}
.ranking-content {
  flex: 1;
}
.ranking-title {
  font-size: 14px;
  color: #333;
  margin-bottom: 5px;
  display: block;
}
.ranking-time {
  font-size: 12px;
  color: #999;
}
.ranking-value {
  text-align: right;
}
.value-text {
  font-size: 16px;
  font-weight: bold;
  color: #FF5858;
}
.value-unit {
  font-size: 12px;
  color: #666;
  margin-left: 2px;
}

/* 导出按钮 */
.export-button {
  margin: 15px;
  padding: 12px 0;
  background-color: #fff;
  border-radius: 25px;
  text-align: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  margin-bottom: 30px;
}
.export-text {
  font-size: 14px;
  color: #FF5858;
  font-weight: 500;
}