"use strict";
const common_vendor = require("../../../common/vendor.js");
const common_assets = require("../../../common/assets.js");
const _sfc_main = {
  data() {
    return {
      statusBarHeight: 20,
      navbarHeight: 64,
      activityId: "",
      activityInfo: {
        id: "1",
        title: "夏日狂欢购物节",
        coverImage: "/static/images/activity/shopping.jpg",
        time: "2023-05-20 至 2023-05-30",
        location: "磁州商业广场",
        participants: 328,
        organizer: "磁州商业联盟",
        status: "ongoing",
        // upcoming, ongoing, ended
        content: '<p style="text-indent:2em;">夏日狂欢购物节盛大开启！活动期间，参与商家推出多重优惠，满100减50，多买多送。</p><p style="text-indent:2em;">同时，现场设有抽奖环节，有机会赢取iPhone、平板电脑等大奖。</p><p style="text-indent:2em;">此外，消费满额即可获得精美礼品一份，数量有限，先到先得。</p><p style="text-indent:2em;">活动详情请咨询各参与商家。</p>',
        rules: [
          "活动时间：2023年5月20日至5月30日",
          "参与方式：在活动期间到店消费即可参与",
          '奖励发放：系统自动发放至"我的福利"中',
          "活动规则最终解释权归商家所有"
        ]
      },
      userRewards: [
        {
          id: "1",
          type: "coupon",
          name: "满100减20优惠券",
          description: "适用于全部商家",
          amount: 20,
          time: "2023-05-20 获得",
          status: "available"
        },
        {
          id: "2",
          type: "redPacket",
          name: "5元现金红包",
          description: "可直接提现至微信钱包",
          amount: 5,
          time: "2023-05-20 获得",
          status: "available"
        }
      ],
      relatedActivities: [
        {
          id: "2",
          title: "美食品鉴会",
          time: "05-25 19:00",
          image: "/static/images/activity/food.jpg"
        },
        {
          id: "3",
          title: "亲子嘉年华",
          time: "05-28 全天",
          image: "/static/images/activity/family.jpg"
        },
        {
          id: "4",
          title: "文化艺术节",
          time: "06-01 至 06-05",
          image: "/static/images/activity/art.jpg"
        }
      ],
      showSuccessPopup: false,
      newRewards: []
    };
  },
  onLoad(options) {
    const sysInfo = common_vendor.index.getSystemInfoSync();
    this.statusBarHeight = sysInfo.statusBarHeight;
    this.navbarHeight = this.statusBarHeight + 44;
    if (options.id) {
      this.activityId = options.id;
      this.loadActivityDetail();
    }
  },
  methods: {
    // 返回上一页
    goBack() {
      common_vendor.index.navigateBack();
    },
    // 页面跳转
    navigateTo(url) {
      common_vendor.index.navigateTo({
        url
      });
    },
    // 加载活动详情
    loadActivityDetail() {
      common_vendor.index.__f__("log", "at subPackages/activity/pages/detail-with-rewards.vue:266", "加载活动ID:", this.activityId);
    },
    // 获取活动状态样式类
    getStatusClass(status) {
      switch (status) {
        case "upcoming":
          return "status-upcoming";
        case "ongoing":
          return "status-ongoing";
        case "ended":
          return "status-ended";
        default:
          return "";
      }
    },
    // 获取活动状态文本
    getStatusText(status) {
      switch (status) {
        case "upcoming":
          return "即将开始";
        case "ongoing":
          return "进行中";
        case "ended":
          return "已结束";
        default:
          return "未知";
      }
    },
    // 获取奖励类型图标
    getRewardTypeIcon(type) {
      switch (type) {
        case "coupon":
          return "/static/images/tabbar/卡券.png";
        case "redPacket":
          return "/static/images/tabbar/我的红包.png";
        case "points":
          return "/static/images/tabbar/每日签到.png";
        case "gift":
          return "/static/images/tabbar/礼品.png";
        default:
          return "/static/images/tabbar/活动.png";
      }
    },
    // 获取奖励状态文本
    getRewardStatusText(status) {
      switch (status) {
        case "available":
          return "可使用";
        case "used":
          return "已使用";
        case "expired":
          return "已过期";
        default:
          return "未知";
      }
    },
    // 查看奖励详情
    viewRewardDetail(item) {
      common_vendor.index.showToast({
        title: "查看详情: " + item.name,
        icon: "none"
      });
    },
    // 参与活动
    joinActivity() {
      setTimeout(() => {
        this.newRewards = [
          {
            id: "3",
            type: "coupon",
            name: "满200减50优惠券",
            description: "限时特惠",
            amount: 50
          },
          {
            id: "4",
            type: "points",
            name: "活动积分",
            description: "可在积分商城兑换礼品",
            amount: 100
          }
        ];
        this.showSuccessPopup = true;
        this.userRewards = [...this.newRewards.map((item) => ({
          ...item,
          time: "刚刚获得",
          status: "available"
        })), ...this.userRewards];
      }, 1e3);
    },
    // 关闭成功弹窗
    closeSuccessPopup() {
      this.showSuccessPopup = false;
    }
  },
  // 分享功能
  onShareAppMessage() {
    return {
      title: this.activityInfo.title,
      path: "/subPackages/activity/pages/detail-with-rewards?id=" + this.activityId,
      imageUrl: this.activityInfo.coverImage
    };
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_assets._imports_0$5,
    b: common_vendor.o((...args) => $options.goBack && $options.goBack(...args)),
    c: $data.statusBarHeight + "px",
    d: $data.activityInfo.coverImage,
    e: common_vendor.t($options.getStatusText($data.activityInfo.status)),
    f: common_vendor.n($options.getStatusClass($data.activityInfo.status)),
    g: $data.navbarHeight + "px",
    h: common_vendor.t($data.activityInfo.title),
    i: common_assets._imports_1$28,
    j: common_vendor.t($data.activityInfo.time),
    k: common_assets._imports_2$23,
    l: common_vendor.t($data.activityInfo.location),
    m: common_assets._imports_3$19,
    n: common_vendor.t($data.activityInfo.participants),
    o: common_assets._imports_4$15,
    p: common_vendor.t($data.activityInfo.organizer),
    q: $data.userRewards.length > 0
  }, $data.userRewards.length > 0 ? {
    r: common_vendor.o(($event) => $options.navigateTo("/pages/user-center/activity-rewards")),
    s: common_vendor.f($data.userRewards, (item, index, i0) => {
      return common_vendor.e({
        a: $options.getRewardTypeIcon(item.type),
        b: common_vendor.t(item.name),
        c: common_vendor.t(item.description),
        d: common_vendor.t(item.time),
        e: item.type === "coupon" || item.type === "redPacket"
      }, item.type === "coupon" || item.type === "redPacket" ? {
        f: common_vendor.t(item.amount)
      } : item.type === "points" ? {
        h: common_vendor.t(item.amount)
      } : {}, {
        g: item.type === "points",
        i: common_vendor.t($options.getRewardStatusText(item.status)),
        j: item.status === "used" ? 1 : "",
        k: item.status === "expired" ? 1 : "",
        l: index,
        m: common_vendor.o(($event) => $options.viewRewardDetail(item), index)
      });
    })
  } : {}, {
    t: $data.activityInfo.content,
    v: common_vendor.f($data.activityInfo.rules, (rule, index, i0) => {
      return {
        a: common_vendor.t(index + 1),
        b: common_vendor.t(rule),
        c: index
      };
    }),
    w: common_vendor.o(($event) => $options.navigateTo("/subPackages/activity/pages/list")),
    x: common_vendor.f($data.relatedActivities, (item, index, i0) => {
      return {
        a: item.image,
        b: common_vendor.t(item.title),
        c: common_vendor.t(item.time),
        d: index,
        e: common_vendor.o(($event) => $options.navigateTo("/subPackages/activity/pages/detail?id=" + item.id), index)
      };
    }),
    y: common_assets._imports_2$1,
    z: $data.activityInfo.status === "ongoing"
  }, $data.activityInfo.status === "ongoing" ? {
    A: common_vendor.o((...args) => $options.joinActivity && $options.joinActivity(...args))
  } : $data.activityInfo.status === "upcoming" ? {} : $data.activityInfo.status === "ended" ? {} : {}, {
    B: $data.activityInfo.status === "upcoming",
    C: $data.activityInfo.status === "ended",
    D: common_vendor.o(($event) => $options.navigateTo("/pages/merchant-center/merchant")),
    E: $data.showSuccessPopup
  }, $data.showSuccessPopup ? {
    F: common_vendor.o((...args) => $options.closeSuccessPopup && $options.closeSuccessPopup(...args)),
    G: common_assets._imports_6$11,
    H: common_vendor.f($data.newRewards, (item, index, i0) => {
      return common_vendor.e({
        a: $options.getRewardTypeIcon(item.type),
        b: common_vendor.t(item.name),
        c: common_vendor.t(item.description),
        d: item.type === "coupon" || item.type === "redPacket"
      }, item.type === "coupon" || item.type === "redPacket" ? {
        e: common_vendor.t(item.amount)
      } : item.type === "points" ? {
        g: common_vendor.t(item.amount)
      } : {}, {
        f: item.type === "points",
        h: index
      });
    }),
    I: common_vendor.o(($event) => $options.navigateTo("/pages/user-center/my-benefits")),
    J: common_vendor.o(() => {
    }),
    K: common_vendor.o((...args) => $options.closeSuccessPopup && $options.closeSuccessPopup(...args))
  } : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-7df6a6fd"]]);
_sfc_main.__runtimeHooks = 2;
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/subPackages/activity/pages/detail-with-rewards.js.map
