
.news-container {
		min-height: 100vh;
		background-color: #f5f5f5;
}
.category-scroll {
		background-color: #fff;
		padding: 20rpx 0;
		position: -webkit-sticky;
		position: sticky;
		top: 0;
		z-index: 10;
}
.category-list {
		display: flex;
		padding: 0 20rpx;
		white-space: nowrap;
}
.category-item {
		display: inline-block;
		padding: 12rpx 30rpx;
		margin-right: 20rpx;
		font-size: 28rpx;
		color: #666;
		background-color: #f5f5f5;
		border-radius: 30rpx;
		transition: all 0.3s;
}
.category-item.active {
		color: #fff;
		background: linear-gradient(to right, #0052CC, #2196F3);
}
.news-list {
		padding: 20rpx;
}
.news-item {
		background-color: #fff;
		border-radius: 12rpx;
		padding: 20rpx;
		margin-bottom: 20rpx;
}
.news-content {
		display: flex;
		margin-bottom: 16rpx;
}
.news-info {
		flex: 1;
		margin-right: 20rpx;
}
.news-title {
		font-size: 32rpx;
		font-weight: bold;
		color: #333;
		margin-bottom: 12rpx;
		display: -webkit-box;
		-webkit-box-orient: vertical;
		-webkit-line-clamp: 2;
		overflow: hidden;
}
.news-desc {
		font-size: 26rpx;
		color: #666;
		margin-bottom: 12rpx;
		display: -webkit-box;
		-webkit-box-orient: vertical;
		-webkit-line-clamp: 2;
		overflow: hidden;
}
.news-image {
		width: 200rpx;
		height: 150rpx;
		border-radius: 8rpx;
}
.news-meta {
		display: flex;
		align-items: center;
		font-size: 24rpx;
		color: #999;
}
.news-time {
		margin-right: 20rpx;
}
.news-category {
		color: #0052CC;
}
.news-stats {
		display: flex;
		border-top: 1rpx solid #eee;
		padding-top: 16rpx;
}
.stat-item {
		display: flex;
		align-items: center;
		margin-right: 40rpx;
}
.stat-icon {
		width: 32rpx;
		height: 32rpx;
		margin-right: 8rpx;
}
.stat-text {
		font-size: 24rpx;
		color: #999;
}
	
	/* 继承首页的卡片和动画样式 */
.card-section {
		background-color: #ffffff;
		margin-bottom: 30rpx;
		padding: 26rpx 22rpx;
		border-radius: 16rpx;
		box-shadow: 0 6rpx 15rpx rgba(0, 0, 0, 0.05);
}
.fade-in {
		animation: fadeIn 0.5s ease;
}
@keyframes fadeIn {
from {
			opacity: 0;
			transform: translateY(20rpx);
}
to {
			opacity: 1;
			transform: translateY(0);
}
}
	
	/* 加载更多样式 */
.load-more, .no-more, .loading, .empty-list {
		text-align: center;
		padding: 30rpx 0;
		font-size: 28rpx;
		color: #999;
}
.load-more {
		color: #0052CC;
}
