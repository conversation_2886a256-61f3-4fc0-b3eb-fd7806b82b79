<!-- 搜索页面开始 -->
<template>
  <view class="search-page page-container">
    <!-- 搜索框 -->
    <view class="search-header card">
      <view class="search-input-wrap">
        <view class="search-icon">
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path d="M909.6 854.5L649.9 594.8C690.2 542.7 712 479 712 412c0-80.2-31.3-155.4-87.9-212.1-56.6-56.7-132-87.9-212.1-87.9s-155.5 31.3-212.1 87.9C143.2 256.5 112 331.8 112 412c0 80.1 31.3 155.5 87.9 212.1C256.5 680.8 331.8 712 412 712c67 0 130.6-21.8 182.7-62l259.7 259.6c3.2 3.2 8.4 3.2 11.6 0l43.6-43.5c3.2-3.2 3.2-8.4 0-11.6zM570.4 570.4C528 612.7 471.8 636 412 636s-116-23.3-158.4-65.6C211.3 528 188 471.8 188 412s23.3-116.1 65.6-158.4C296 211.3 352.2 188 412 188s116.1 23.2 158.4 65.6S636 352.2 636 412s-23.3 116.1-65.6 158.4z" fill="#999"/>
          </svg>
        </view>
        <input 
          type="text"
          class="search-input"
          v-model="keyword"
          placeholder="搜索活动、商品、文章"
          confirm-type="search"
          @confirm="handleSearch"
          focus
        />
        <view v-if="keyword" class="clear-icon" @click="clearKeyword">
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path d="M512 421.490332L871.696581 61.793751l90.509668 90.509668L602.509668 512l359.696581 359.696581-90.509668 90.509668L512 602.509668 152.303419 962.206249l-90.509668-90.509668L421.490332 512 61.793751 152.303419l90.509668-90.509668z" fill="#999"/>
          </svg>
        </view>
      </view>
      <text class="cancel-btn" @click="goBack">取消</text>
    </view>
    
    <!-- 搜索历史 -->
    <view v-if="!keyword && searchHistory.length > 0 && !hasSearched" class="search-history card">
      <view class="section-header">
        <text class="section-title">搜索历史</text>
        <view class="clear-btn" @click="clearHistory">
          <svg class="icon" viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path d="M899.1 869.6l-53-305.6H864c14.4 0 26-11.6 26-26V346c0-14.4-11.6-26-26-26H618V138c0-14.4-11.6-26-26-26H432c-14.4 0-26 11.6-26 26v182H160c-14.4 0-26 11.6-26 26v192c0 14.4 11.6 26 26 26h17.9l-53 305.6c-0.3 1.5-0.4 3-0.4 4.4 0 14.4 11.6 26 26 26h723c1.5 0 3-0.1 4.4-0.4 14.2-2.4 23.7-15.9 21.2-30zM204 390h272V182h72v208h272v104H204V390z m468 440V674c0-4.4-3.6-8-8-8h-48c-4.4 0-8 3.6-8 8v156H416V674c0-4.4-3.6-8-8-8h-48c-4.4 0-8 3.6-8 8v156H202.8l45.1-260H776l45.1 260H672z" fill="#999"/>
          </svg>
          <text>清空</text>
        </view>
      </view>
      
      <view class="tag-list">
        <view 
          v-for="(item, index) in searchHistory" 
          :key="index"
          class="tag-item"
          @click="useHistoryItem(item)"
        >
          {{ item }}
        </view>
      </view>
    </view>
    
    <!-- 热门搜索 -->
    <view v-if="!keyword && !hasSearched" class="hot-search card">
      <view class="section-header">
        <text class="section-title">热门搜索</text>
      </view>
      
      <view class="tag-list">
        <view 
          v-for="(item, index) in hotSearches" 
          :key="index"
          class="tag-item"
          :class="{ 'hot': index < 3 }"
          @click="useHistoryItem(item)"
        >
          <text v-if="index < 3" class="hot-rank">{{ index + 1 }}</text>
          {{ item }}
        </view>
      </view>
    </view>
    
    <!-- 搜索结果 -->
    <view v-if="hasSearched" class="search-results">
      <!-- 结果筛选 -->
      <view class="filter-tabs card">
        <view 
          v-for="(tab, index) in filterTabs" 
          :key="index"
          class="filter-tab"
          :class="{ active: currentTab === index }"
          @click="switchTab(index)"
        >
          {{ tab.name }}
        </view>
      </view>
      
      <!-- 活动结果 -->
      <view v-if="currentTab === 0" class="result-list">
        <view 
          v-for="(item, index) in activityResults"
          :key="index"
          class="activity-item card"
          @click="goToDetail(item.id, 'activity')"
        >
          <image :src="item.image" mode="aspectFill" class="item-image" />
          <view class="item-info">
            <view class="item-title">{{ item.title }}</view>
            <view class="item-desc">{{ item.description }}</view>
            <view class="item-meta">
              <text class="time">{{ item.time }}</text>
              <text class="price">¥{{ item.price }}起</text>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 商品结果 -->
      <view v-if="currentTab === 1" class="result-list">
        <view 
          v-for="(item, index) in productResults"
          :key="index"
          class="product-item card"
          @click="goToDetail(item.id, 'product')"
        >
          <image :src="item.image" mode="aspectFill" class="item-image" />
          <view class="item-info">
            <view class="item-title">{{ item.title }}</view>
            <view class="item-desc">{{ item.description }}</view>
            <view class="item-price">
              <text class="current">¥{{ item.price }}</text>
              <text class="original">¥{{ item.originalPrice }}</text>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 文章结果 -->
      <view v-if="currentTab === 2" class="result-list">
        <view 
          v-for="(item, index) in articleResults"
          :key="index"
          class="article-item card"
          @click="goToDetail(item.id, 'article')"
        >
          <view class="item-info">
            <view class="item-title">{{ item.title }}</view>
            <view class="item-desc">{{ item.summary }}</view>
            <view class="item-meta">
              <text class="time">{{ item.publishTime }}</text>
              <text class="views">{{ item.views }}阅读</text>
            </view>
          </view>
          <image v-if="item.image" :src="item.image" mode="aspectFill" class="item-image" />
        </view>
      </view>
      
      <!-- 加载状态 -->
      <view v-if="isLoading" class="loading-state">
        <view class="loading-indicator"></view>
        <text>加载中...</text>
      </view>
      
      <!-- 加载更多 -->
      <view v-if="hasMore && !isLoading" class="load-more" @click="loadMore">
        <text>点击加载更多</text>
      </view>
      
      <!-- 没有更多 -->
      <view v-if="!hasMore && getCurrentResults.length > 0" class="no-more">
        <text>没有更多内容了</text>
      </view>
      
      <!-- 空状态 -->
      <view v-if="hasSearched && !isLoading && getCurrentResults.length === 0" class="empty-state">
        <image class="empty-icon" src="/static/images/empty-search.svg" />
        <text class="empty-text">没有找到相关内容</text>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, watch } from 'vue'

// 搜索关键词
const keyword = ref('')

// 是否已搜索
const hasSearched = ref(false)

// 是否加载中
const isLoading = ref(false)

// 是否有更多
const hasMore = ref(true)

// 当前页码
const pageNum = ref(1)

// 每页数量
const pageSize = ref(10)

// 搜索历史
const searchHistory = ref<string[]>([])

// 热门搜索
const hotSearches = ref([
  '新年音乐节',
  '美食品鉴会',
  '亲子活动',
  '户外露营',
  '瑜伽课程',
  '电影首映',
  '艺术展览',
  '读书会'
])

// 筛选标签
const filterTabs = [
  { name: '活动', type: 'activity' },
  { name: '商品', type: 'product' },
  { name: '文章', type: 'article' }
]

// 当前选中的标签
const currentTab = ref(0)

// 活动结果
const activityResults = ref([
  {
    id: '1',
    title: '2024新年音乐节',
    description: '迎接新年的音乐盛宴，多位知名歌手现场献唱',
    image: '/static/images/activity1.jpg',
    time: '2024-01-01 19:30',
    price: 199
  },
  {
    id: '2',
    title: '美食品鉴会',
    description: '汇聚全球美食，享受舌尖上的盛宴',
    image: '/static/images/activity2.jpg',
    time: '2024-01-15 14:00',
    price: 299
  }
])

// 商品结果
const productResults = ref([
  {
    id: '1',
    title: '限量版纪念T恤',
    description: '2024新年音乐节限定款',
    image: '/static/images/product1.jpg',
    price: 129,
    originalPrice: 199
  },
  {
    id: '2',
    title: '美食礼盒',
    description: '精选多国特色美食',
    image: '/static/images/product2.jpg',
    price: 299,
    originalPrice: 399
  }
])

// 文章结果
const articleResults = ref([
  {
    id: '1',
    title: '如何准备一场完美的音乐节',
    summary: '从选址到表演者邀请，一文了解音乐节筹备全过程',
    image: '/static/images/article1.jpg',
    publishTime: '2023-12-20',
    views: 1234
  },
  {
    id: '2',
    title: '2024年最值得期待的十大活动',
    summary: '新的一年，哪些活动最值得参与？本文为你一一盘点',
    image: '/static/images/article2.jpg',
    publishTime: '2023-12-25',
    views: 2345
  }
])

// 获取当前标签的结果
const getCurrentResults = computed(() => {
  switch (currentTab.value) {
    case 0:
      return activityResults.value
    case 1:
      return productResults.value
    case 2:
      return articleResults.value
    default:
      return []
  }
})

// 切换标签
const switchTab = (index: number) => {
  if (currentTab.value === index) return
  
  currentTab.value = index
  pageNum.value = 1
  hasMore.value = true
  
  // 如果当前标签没有数据，则重新搜索
  if (getCurrentResults.value.length === 0) {
    search()
  }
}

// 搜索方法
const search = () => {
  isLoading.value = true
  
  // 模拟搜索请求
  setTimeout(() => {
    // 这里应该是实际的搜索请求
    // 根据关键词和当前标签类型获取搜索结果
    
    // 模拟搜索完成
    isLoading.value = false
    
    // 模拟判断是否有更多数据
    if (pageNum.value >= 3) {
      hasMore.value = false
    }
  }, 1000)
}

// 处理搜索
const handleSearch = () => {
  if (!keyword.value.trim()) return
  
  // 保存搜索历史
  saveSearchHistory(keyword.value)
  
  // 设置已搜索状态
  hasSearched.value = true
  
  // 重置页码和更多状态
  pageNum.value = 1
  hasMore.value = true
  
  // 清空之前的搜索结果
  if (currentTab.value === 0) {
    activityResults.value = []
  } else if (currentTab.value === 1) {
    productResults.value = []
  } else if (currentTab.value === 2) {
    articleResults.value = []
  }
  
  // 执行搜索
  search()
}

// 加载更多
const loadMore = () => {
  if (isLoading.value || !hasMore.value) return
  
  pageNum.value++
  search()
}

// 清除关键词
const clearKeyword = () => {
  keyword.value = ''
  hasSearched.value = false
}

// 返回上一页
const goBack = () => {
  uni.navigateBack()
}

// 保存搜索历史
const saveSearchHistory = (key: string) => {
  // 如果已存在，则先移除
  const index = searchHistory.value.indexOf(key)
  if (index > -1) {
    searchHistory.value.splice(index, 1)
  }
  
  // 添加到开头
  searchHistory.value.unshift(key)
  
  // 最多保存10条
  if (searchHistory.value.length > 10) {
    searchHistory.value.pop()
  }
  
  // 保存到本地存储
  uni.setStorageSync('searchHistory', JSON.stringify(searchHistory.value))
}

// 使用历史记录项
const useHistoryItem = (item: string) => {
  keyword.value = item
  handleSearch()
}

// 清空历史记录
const clearHistory = () => {
  uni.showModal({
    title: '提示',
    content: '确定要清空搜索历史吗？',
    success: (res) => {
      if (res.confirm) {
        searchHistory.value = []
        uni.removeStorageSync('searchHistory')
      }
    }
  })
}

// 跳转到详情页
const goToDetail = (id: string, type: string) => {
  let url = ''
  
  switch (type) {
    case 'activity':
      url = `/pages/activity/detail?id=${id}`
      break
    case 'product':
      url = `/pages/product/detail?id=${id}`
      break
    case 'article':
      url = `/pages/article/detail?id=${id}`
      break
  }
  
  if (url) {
    uni.navigateTo({ url })
  }
}

// 获取历史记录
const getSearchHistory = () => {
  try {
    const history = uni.getStorageSync('searchHistory')
    if (history) {
      searchHistory.value = JSON.parse(history)
    }
  } catch (e) {
    console.error('获取搜索历史失败', e)
  }
}

// 监听关键词变化
watch(keyword, (newVal) => {
  if (!newVal) {
    hasSearched.value = false
  }
})

onMounted(() => {
  getSearchHistory()
})
</script>

<style lang="scss" scoped>
.search-page {
  padding: 0;
  background: #fff;
  min-height: 100vh;
  
  .search-header {
    display: flex;
    align-items: center;
    padding: 24rpx;
    background: #fff;
    box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
    border-radius: 0;
    position: sticky;
    top: 0;
    z-index: 100;
    
    .search-input-wrap {
      flex: 1;
      height: 72rpx;
      background: #f5f5f5;
      border-radius: 36rpx;
      display: flex;
      align-items: center;
      padding: 0 24rpx;
      
      .search-icon {
        width: 40rpx;
        height: 40rpx;
        margin-right: 16rpx;
        
        svg {
          width: 100%;
          height: 100%;
        }
      }
      
      .search-input {
        flex: 1;
        height: 100%;
        font-size: 28rpx;
      }
      
      .clear-icon {
        width: 40rpx;
        height: 40rpx;
        
        svg {
          width: 100%;
          height: 100%;
        }
      }
    }
    
    .cancel-btn {
      padding-left: 24rpx;
      font-size: 28rpx;
      color: #007AFF;
    }
  }
  
  .search-history,
  .hot-search {
    margin-top: 24rpx;
    
    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 24rpx;
      
      .section-title {
        font-size: 32rpx;
        font-weight: 500;
        color: #333;
      }
      
      .clear-btn {
        display: flex;
        align-items: center;
        font-size: 24rpx;
        color: #999;
        
        .icon {
          width: 32rpx;
          height: 32rpx;
          margin-right: 8rpx;
        }
      }
    }
    
    .tag-list {
      display: flex;
      flex-wrap: wrap;
      
      .tag-item {
        padding: 12rpx 24rpx;
        background: #f5f5f5;
        border-radius: 8rpx;
        font-size: 24rpx;
        color: #666;
        margin-right: 16rpx;
        margin-bottom: 16rpx;
        position: relative;
        
        &.hot {
          padding-left: 48rpx;
          color: #FF6B6B;
          background: rgba(255, 107, 107, 0.1);
        }
        
        .hot-rank {
          position: absolute;
          left: 16rpx;
          top: 50%;
          transform: translateY(-50%);
          font-weight: 600;
        }
      }
    }
  }
  
  .search-results {
    padding: 24rpx;
    
    .filter-tabs {
      display: flex;
      padding: 0;
      margin-bottom: 24rpx;
      position: sticky;
      top: 0;
      z-index: 10;
      background: #fff;
      
      .filter-tab {
        flex: 1;
        text-align: center;
        padding: 24rpx 0;
        font-size: 28rpx;
        color: #666;
        position: relative;
        
        &.active {
          color: #007AFF;
          font-weight: 500;
          
          &::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 48rpx;
            height: 4rpx;
            background: #007AFF;
            border-radius: 2rpx;
          }
        }
      }
    }
    
    .result-list {
      .activity-item,
      .product-item {
        display: flex;
        padding: 24rpx;
        margin-bottom: 24rpx;
        
        .item-image {
          width: 200rpx;
          height: 200rpx;
          border-radius: 12rpx;
          margin-right: 24rpx;
        }
        
        .item-info {
          flex: 1;
          display: flex;
          flex-direction: column;
          
          .item-title {
            font-size: 32rpx;
            font-weight: 500;
            color: #333;
            margin-bottom: 16rpx;
          }
          
          .item-desc {
            font-size: 26rpx;
            color: #666;
            margin-bottom: 16rpx;
            flex: 1;
          }
          
          .item-meta {
            display: flex;
            justify-content: space-between;
            font-size: 24rpx;
            
            .time {
              color: #999;
            }
            
            .price {
              color: #FF6B6B;
              font-weight: 500;
            }
          }
          
          .item-price {
            .current {
              font-size: 32rpx;
              color: #FF6B6B;
              font-weight: 500;
              margin-right: 16rpx;
            }
            
            .original {
              font-size: 24rpx;
              color: #999;
              text-decoration: line-through;
            }
          }
        }
      }
      
      .article-item {
        display: flex;
        padding: 24rpx;
        margin-bottom: 24rpx;
        
        .item-info {
          flex: 1;
          margin-right: 24rpx;
          
          .item-title {
            font-size: 32rpx;
            font-weight: 500;
            color: #333;
            margin-bottom: 16rpx;
          }
          
          .item-desc {
            font-size: 26rpx;
            color: #666;
            margin-bottom: 16rpx;
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 2;
            overflow: hidden;
          }
          
          .item-meta {
            display: flex;
            justify-content: space-between;
            font-size: 24rpx;
            color: #999;
          }
        }
        
        .item-image {
          width: 200rpx;
          height: 150rpx;
          border-radius: 8rpx;
        }
      }
    }
    
    .loading-state,
    .load-more,
    .no-more {
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 48rpx 0;
      
      text {
        font-size: 28rpx;
        color: #999;
      }
    }
    
    .loading-state {
      .loading-indicator {
        width: 48rpx;
        height: 48rpx;
        border: 4rpx solid #f3f3f3;
        border-top: 4rpx solid #007AFF;
        border-radius: 50%;
        margin-bottom: 16rpx;
        /* 使用静态样式代替动画 */
      }
    }
    
    .load-more {
      text {
        color: #007AFF;
      }
    }
  }
}
</style>
<!-- 搜索页面结束 --> 