{"version": 3, "file": "test-navigation.js", "sources": ["pages/test-navigation.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvdGVzdC1uYXZpZ2F0aW9uLnZ1ZQ"], "sourcesContent": ["<template>\n  <view class=\"test-container\">\n    <view class=\"header\">\n      <text class=\"title\">跳转测试页面</text>\n    </view>\n    \n    <view class=\"button-group\">\n      <button class=\"test-button\" @click=\"testNavigate('/pages/publish/info-detail')\">\n        跳转到信息详情页\n      </button>\n      <button class=\"test-button\" @click=\"testNavigate('/pages/publish/publish')\">\n        跳转到发布页\n      </button>\n      <button class=\"test-button\" @click=\"testNavigate('/pages/publish/detail')\">\n        跳转到发布详情页\n      </button>\n    </view>\n    \n    <view class=\"result-panel\">\n      <text class=\"result-title\">测试结果:</text>\n      <text class=\"result-content\">{{result}}</text>\n    </view>\n  </view>\n</template>\n\n<script>\nexport default {\n  data() {\n    return {\n      result: '未执行测试'\n    }\n  },\n  methods: {\n    testNavigate(url) {\n      this.result = `正在跳转到: ${url}`;\n      \n      uni.navigateTo({\n        url,\n        success: () => {\n          this.result = `跳转成功: ${url}`;\n        },\n        fail: (err) => {\n          this.result = `跳转失败: ${url}, 错误: ${JSON.stringify(err)}`;\n        }\n      });\n    }\n  }\n}\n</script>\n\n<style>\n.test-container {\n  padding: 40rpx;\n}\n\n.header {\n  margin-bottom: 40rpx;\n}\n\n.title {\n  font-size: 40rpx;\n  font-weight: bold;\n}\n\n.button-group {\n  display: flex;\n  flex-direction: column;\n  gap: 20rpx;\n  margin-bottom: 40rpx;\n}\n\n.test-button {\n  background-color: #1677FF;\n  color: white;\n  padding: 20rpx;\n  border-radius: 8rpx;\n}\n\n.result-panel {\n  padding: 20rpx;\n  border: 1px solid #ddd;\n  border-radius: 8rpx;\n}\n\n.result-title {\n  font-weight: bold;\n  margin-bottom: 10rpx;\n  display: block;\n}\n\n.result-content {\n  font-family: monospace;\n}\n</style> ", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/pages/test-navigation.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni"], "mappings": ";;AA0BA,MAAK,YAAU;AAAA,EACb,OAAO;AACL,WAAO;AAAA,MACL,QAAQ;AAAA,IACV;AAAA,EACD;AAAA,EACD,SAAS;AAAA,IACP,aAAa,KAAK;AAChB,WAAK,SAAS,UAAU,GAAG;AAE3BA,oBAAAA,MAAI,WAAW;AAAA,QACb;AAAA,QACA,SAAS,MAAM;AACb,eAAK,SAAS,SAAS,GAAG;AAAA,QAC3B;AAAA,QACD,MAAM,CAAC,QAAQ;AACb,eAAK,SAAS,SAAS,GAAG,SAAS,KAAK,UAAU,GAAG,CAAC;AAAA,QACxD;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AACF;;;;;;;;;;AC9CA,GAAG,WAAW,eAAe;"}