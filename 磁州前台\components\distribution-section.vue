<!-- 分销推广组件 -->
<template>
  <view class="distribution-section" @click="handleClick">
    <view class="distribution-card">
      <view class="distribution-left">
        <view class="distribution-icon" :class="itemTypeClass">
          <image src="/static/images/tabbar/分销.png" mode="aspectFit" class="icon-image"></image>
        </view>
        <view class="distribution-info">
          <text class="distribution-title">{{ title }}</text>
          <text class="distribution-desc">最高可赚¥{{ commissionAmount }}</text>
        </view>
      </view>
      <view class="distribution-right" :class="itemTypeClass">
        <text class="distribution-btn">去推广</text>
        <view class="arrow-icon">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M9 6L15 12L9 18" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import distributionService from '@/utils/distributionService';

export default {
  name: 'DistributionSection',
  props: {
    // 商品/活动ID
    itemId: {
      type: [String, Number],
      required: true
    },
    // 商品/活动类型: product, coupon, discount, flash, group
    itemType: {
      type: String,
      default: 'product'
    },
    // 商品/活动标题
    itemTitle: {
      type: String,
      default: ''
    },
    // 商品/活动价格
    itemPrice: {
      type: [String, Number],
      default: 0
    },
    // 佣金比例
    commissionRate: {
      type: [String, Number],
      default: 20
    }
  },
  computed: {
    // 计算佣金金额
    commissionAmount() {
      const price = parseFloat(this.itemPrice);
      const rate = parseFloat(this.commissionRate);
      if (isNaN(price) || isNaN(rate)) return '0';
      return (price * rate / 100).toFixed(2);
    },
    // 分销标题
    title() {
      const typeMap = {
        'product': '分销此商品',
        'coupon': '分销此优惠券',
        'discount': '分销此活动',
        'flash': '分销此秒杀',
        'group': '分销此拼团'
      };
      return typeMap[this.itemType] || '分销此商品';
    },
    // 根据活动类型设置对应的样式类名
    itemTypeClass() {
      return `type-${this.itemType}`;
    }
  },
  methods: {
    // 处理点击事件
    async handleClick() {
      // 检查用户是否是分销员
      const isDistributor = uni.getStorageSync('isDistributor') || false;
      
      if (isDistributor) {
        // 如果是分销员，生成分销链接或海报
        this.generateDistributionContent();
      } else {
        // 如果不是分销员，显示分销申请弹窗
        this.showDistributionPopup();
      }
    },
    
    // 生成分销内容
    async generateDistributionContent() {
      uni.showActionSheet({
        itemList: ['生成分销海报', '复制分销链接', '生成小程序码'],
        success: (res) => {
          switch (res.tapIndex) {
            case 0:
              this.generatePoster();
              break;
            case 1:
              this.copyLink();
              break;
            case 2:
              this.generateQrCode();
              break;
          }
        }
      });
    },
    
    // 生成分销海报
    async generatePoster() {
      uni.showLoading({ title: '生成中...' });
      try {
        const result = await distributionService.getDistributionPoster({
          type: this.itemType,
          id: this.itemId,
          title: this.itemTitle,
          price: this.itemPrice
        });
        
        uni.hideLoading();
        if (result && result.posterUrl) {
          uni.previewImage({
            urls: [result.posterUrl],
            current: result.posterUrl,
            success: () => {
              uni.showToast({
                title: '长按图片保存',
                icon: 'none'
              });
            }
          });
        } else {
          uni.showToast({
            title: '生成海报失败',
            icon: 'none'
          });
        }
      } catch (error) {
        uni.hideLoading();
        uni.showToast({
          title: '生成海报失败',
          icon: 'none'
        });
      }
    },
    
    // 复制分销链接
    async copyLink() {
      uni.showLoading({ title: '生成中...' });
      try {
        const result = await distributionService.generatePromotionLink({
          type: this.itemType,
          id: this.itemId,
          distributorId: uni.getStorageSync('userId') || '0'
        });
        
        uni.hideLoading();
        if (result && result.url) {
          uni.setClipboardData({
            data: result.url,
            success: () => {
              uni.showToast({
                title: '链接已复制',
                icon: 'success'
              });
            }
          });
        } else {
          uni.showToast({
            title: '生成链接失败',
            icon: 'none'
          });
        }
      } catch (error) {
        uni.hideLoading();
        uni.showToast({
          title: '生成链接失败',
          icon: 'none'
        });
      }
    },
    
    // 生成小程序码
    async generateQrCode() {
      uni.showLoading({ title: '生成中...' });
      try {
        const result = await distributionService.generateProductCode({
          type: this.itemType,
          id: this.itemId
        });
        
        uni.hideLoading();
        if (result && result.qrCodeUrl) {
          uni.previewImage({
            urls: [result.qrCodeUrl],
            current: result.qrCodeUrl,
            success: () => {
              uni.showToast({
                title: '长按图片保存',
                icon: 'none'
              });
            }
          });
        } else {
          uni.showToast({
            title: '生成小程序码失败',
            icon: 'none'
          });
        }
      } catch (error) {
        uni.hideLoading();
        uni.showToast({
          title: '生成小程序码失败',
          icon: 'none'
        });
      }
    },
    
    // 显示分销申请弹窗
    showDistributionPopup() {
      uni.navigateTo({
        url: '/subPackages/distribution/pages/apply'
      });
    }
  }
}
</script>

<style>
.distribution-section {
  padding: 0 24rpx;
  margin: 30rpx 0;
  position: relative;
  z-index: 10;
}

.distribution-card {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: linear-gradient(135deg, rgba(255,255,255,0.95) 0%, rgba(245,245,247,0.9) 100%);
  padding: 28rpx 30rpx;
  border-radius: 35rpx;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.08), 
              0 2rpx 4rpx rgba(0, 0, 0, 0.05),
              0 0 1rpx rgba(0, 0, 0, 0.15);
  backdrop-filter: blur(10px);
  border: 1rpx solid rgba(255, 255, 255, 0.3);
  position: relative;
  overflow: hidden;
}

.distribution-card::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(255,255,255,0.2) 0%, rgba(255,255,255,0) 70%);
  opacity: 0.5;
}

.distribution-left {
  display: flex;
  align-items: center;
}

.distribution-icon {
  width: 70rpx;
  height: 70rpx;
  border-radius: 22rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.2);
}

/* 默认样式 - 紫色 */
.distribution-icon {
  background: linear-gradient(135deg, #6B0FBE 0%, #9013FE 100%);
}

/* 拼团活动 - 红色 */
.distribution-icon.type-group {
  background: linear-gradient(135deg, #FF2C54 0%, #FF4F64 100%);
  box-shadow: 0 4rpx 12rpx rgba(255, 44, 84, 0.2);
}

/* 秒杀活动 - 橙色 */
.distribution-icon.type-flash {
  background: linear-gradient(135deg, #FF6B00 0%, #FF9500 100%);
  box-shadow: 0 4rpx 12rpx rgba(255, 107, 0, 0.2);
}

/* 优惠券 - 蓝色 */
.distribution-icon.type-coupon {
  background: linear-gradient(135deg, #007AFF 0%, #5AC8FA 100%);
  box-shadow: 0 4rpx 12rpx rgba(0, 122, 255, 0.2);
}

/* 满减活动 - 绿色 */
.distribution-icon.type-discount {
  background: linear-gradient(135deg, #34C759 0%, #30DB5B 100%);
  box-shadow: 0 4rpx 12rpx rgba(52, 199, 89, 0.2);
}

.icon-image {
  width: 40rpx;
  height: 40rpx;
  filter: brightness(0) invert(1);
}

.distribution-info {
  display: flex;
  flex-direction: column;
}

.distribution-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #1D1D1F;
  margin-bottom: 8rpx;
  letter-spacing: 0.5rpx;
}

.distribution-desc {
  font-size: 26rpx;
  color: #FF6B00;
  font-weight: 500;
}

.distribution-right {
  display: flex;
  align-items: center;
  padding: 16rpx 24rpx;
  border-radius: 30rpx;
}

/* 默认样式 - 紫色 */
.distribution-right {
  background: linear-gradient(135deg, #6B0FBE 0%, #9013FE 100%);
  box-shadow: 0 4rpx 12rpx rgba(107, 15, 190, 0.2);
}

/* 拼团活动 - 红色 */
.distribution-right.type-group {
  background: linear-gradient(135deg, #FF2C54 0%, #FF4F64 100%);
  box-shadow: 0 4rpx 12rpx rgba(255, 44, 84, 0.2);
}

/* 秒杀活动 - 橙色 */
.distribution-right.type-flash {
  background: linear-gradient(135deg, #FF6B00 0%, #FF9500 100%);
  box-shadow: 0 4rpx 12rpx rgba(255, 107, 0, 0.2);
}

/* 优惠券 - 蓝色 */
.distribution-right.type-coupon {
  background: linear-gradient(135deg, #007AFF 0%, #5AC8FA 100%);
  box-shadow: 0 4rpx 12rpx rgba(0, 122, 255, 0.2);
}

/* 满减活动 - 绿色 */
.distribution-right.type-discount {
  background: linear-gradient(135deg, #34C759 0%, #30DB5B 100%);
  box-shadow: 0 4rpx 12rpx rgba(52, 199, 89, 0.2);
}

.distribution-btn {
  font-size: 28rpx;
  color: #FFFFFF;
  font-weight: 500;
}

.arrow-icon {
  display: flex;
  align-items: center;
  margin-left: 8rpx;
  color: #FFFFFF;
}
</style> 