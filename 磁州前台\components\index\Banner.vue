<template>
  <!-- 轮播图 -->
  <view class="banner-outer">
    <swiper class="banner-swiper" 
      indicator-dots 
      indicator-color="rgba(255,255,255,0.5)" 
      indicator-active-color="#ffffff"
      autoplay 
      circular 
      interval="3000" 
      duration="500">
      <swiper-item v-for="(item, index) in bannerList" :key="index">
        <view class="banner-item">
          <image :src="item.image" class="banner-image" mode="scaleToFill" @click="navigateTo(item.url)"></image>
        </view>
      </swiper-item>
    </swiper>
  </view>
</template>

<script>
export default {
  name: 'IndexBanner',
  props: {
    bannerList: {
      type: Array,
      default: () => []
    }
  },
  methods: {
    navigateTo(url) {
      if (!url) return;
      
      try {
        uni.navigateTo({
          url: url,
          fail: (err) => {
            console.error('页面跳转失败:', err);
            // 尝试使用switchTab（如果是tabBar页面）
            uni.switchTab({
              url: url,
              fail: (err2) => {
                console.error('switchTab跳转也失败:', err2);
                uni.showToast({
                  title: '页面跳转失败',
                  icon: 'none'
                });
              }
            });
          }
        });
      } catch (error) {
        console.error('导航出错:', error);
      }
    }
  }
}
</script>

<style lang="scss" scoped>
/* 轮播图 */
.banner-outer {
  position: relative;
  margin: 55rpx 30rpx 50rpx; /* 再次增加顶部间距，使轮播图位置更低 */
  border-radius: 30rpx;
  overflow: hidden;
  box-shadow: 0 25rpx 45rpx rgba(0, 0, 0, 0.35), 0 15rpx 25rpx rgba(0, 0, 0, 0.2);
  border: 12rpx solid #ffffff;
  transform: translateZ(0);
  z-index: 3;
  animation: float 6s ease-in-out infinite;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  padding: 2rpx;
  background-color: rgba(255, 255, 255, 0.8);
}

@keyframes float {
  0% { transform: translateY(0) translateZ(0); }
  50% { transform: translateY(-10rpx) translateZ(0); }
  100% { transform: translateY(0) translateZ(0); }
}

.banner-swiper {
  width: 100%;
  height: 300rpx;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 0 10rpx rgba(0, 0, 0, 0.1);
}

.banner-item {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #FFFFFF;
}

.banner-image {
  width: 100%;
  height: 100%;
  border-radius: 16rpx;
  box-shadow: inset 0 0 15rpx rgba(0, 0, 0, 0.15);
  transition: transform 0.3s ease;
}

/* 轮播图指示器样式增强 */
:deep(.wx-swiper-dots) {
  position: relative;
  bottom: 20rpx !important;
}

:deep(.wx-swiper-dot) {
  width: 16rpx !important;
  height: 16rpx !important;
  border-radius: 8rpx !important;
  margin-left: 10rpx !important;
  margin-right: 10rpx !important;
  background: rgba(255, 255, 255, 0.6) !important;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.25) !important;
  transition: width 0.3s ease !important;
}

:deep(.wx-swiper-dot.wx-swiper-dot-active) {
  width: 32rpx !important;
  background: #ffffff !important;
}
</style> 