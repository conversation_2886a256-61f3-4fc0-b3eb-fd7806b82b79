<template>
  <view class="countdown-container" :class="[`countdown-${type}`]">
    <view class="countdown-label">{{ label }}</view>
    <view class="countdown-timer">
      <view class="time-block">
        <view class="time-value">{{ days }}</view>
        <view class="time-unit">天</view>
      </view>
      <view class="time-separator">:</view>
      <view class="time-block">
        <view class="time-value">{{ hours }}</view>
        <view class="time-unit">时</view>
      </view>
      <view class="time-separator">:</view>
      <view class="time-block">
        <view class="time-value">{{ minutes }}</view>
        <view class="time-unit">分</view>
      </view>
      <view class="time-separator">:</view>
      <view class="time-block">
        <view class="time-value">{{ seconds }}</view>
        <view class="time-unit">秒</view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'CountdownTimer',
  props: {
    endTime: {
      type: [String, Number, Date],
      required: true
    },
    type: {
      type: String,
      default: 'default',
      validator: (value) => {
        return ['flash', 'group', 'discount', 'coupon', 'default'].includes(value)
      }
    }
  },
  data() {
    return {
      days: '00',
      hours: '00',
      minutes: '00',
      seconds: '00',
      timer: null,
      isEnded: false
    }
  },
  computed: {
    label() {
      if (this.isEnded) {
        return '活动已结束'
      }
      
      const labelMap = {
        flash: '距结束还剩',
        group: '拼团倒计时',
        discount: '优惠倒计时',
        coupon: '领取倒计时',
        default: '距结束还剩'
      }
      
      return labelMap[this.type] || '距结束还剩'
    }
  },
  mounted() {
    this.startCountdown()
  },
  beforeUnmount() {
    this.clearCountdown()
  },
  methods: {
    startCountdown() {
      this.calculateTime()
      this.timer = setInterval(() => {
        this.calculateTime()
      }, 1000)
    },
    clearCountdown() {
      if (this.timer) {
        clearInterval(this.timer)
        this.timer = null
      }
    },
    calculateTime() {
      const endTime = new Date(this.endTime).getTime()
      const now = new Date().getTime()
      const diff = endTime - now
      
      if (diff <= 0) {
        this.days = '00'
        this.hours = '00'
        this.minutes = '00'
        this.seconds = '00'
        this.isEnded = true
        this.clearCountdown()
        this.$emit('countdown-end')
        return
      }
      
      // 计算天、时、分、秒
      const days = Math.floor(diff / (1000 * 60 * 60 * 24))
      const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))
      const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60))
      const seconds = Math.floor((diff % (1000 * 60)) / 1000)
      
      // 格式化为两位数
      this.days = days < 10 ? `0${days}` : `${days}`
      this.hours = hours < 10 ? `0${hours}` : `${hours}`
      this.minutes = minutes < 10 ? `0${minutes}` : `${minutes}`
      this.seconds = seconds < 10 ? `0${seconds}` : `${seconds}`
      
      // 添加动画效果，当秒数变化时
      const secondsBlock = document.querySelector('.time-block:last-child .time-value')
      if (secondsBlock) {
        secondsBlock.classList.add('time-pulse')
        setTimeout(() => {
          secondsBlock.classList.remove('time-pulse')
        }, 500)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.countdown-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 16rpx;
  
  .countdown-label {
    font-size: 24rpx;
    margin-bottom: 8rpx;
  }
  
  .countdown-timer {
    display: flex;
    align-items: center;
    
    .time-block {
      display: flex;
      flex-direction: column;
      align-items: center;
      
      .time-value {
        min-width: 50rpx;
        height: 50rpx;
        line-height: 50rpx;
        text-align: center;
        font-size: 28rpx;
        font-weight: 600;
        background-color: rgba(0, 0, 0, 0.6);
        color: #fff;
        border-radius: 35rpx;
        padding: 0 8rpx;
        transition: transform 0.3s ease;
        
        &.time-pulse {
          animation: pulse 0.5s ease-in-out;
        }
      }
      
      .time-unit {
        font-size: 20rpx;
        color: #666;
        margin-top: 4rpx;
      }
    }
    
    .time-separator {
      margin: 0 4rpx;
      color: #666;
      font-weight: 600;
      font-size: 28rpx;
      padding-bottom: 24rpx;
    }
  }
}

.countdown-flash {
  .countdown-label {
    color: #FF3B30;
  }
  
  .time-value {
    background: linear-gradient(135deg, #FF5A5F 0%, #FF3B30 100%) !important;
  }
}

.countdown-group {
  .countdown-label {
    color: #34C759;
  }
  
  .time-value {
    background: linear-gradient(135deg, #4CD964 0%, #34C759 100%) !important;
  }
}

.countdown-discount {
  .countdown-label {
    color: #5856D6;
  }
  
  .time-value {
    background: linear-gradient(135deg, #5E5CE6 0%, #5856D6 100%) !important;
  }
}

.countdown-coupon {
  .countdown-label {
    color: #FF9500;
  }
  
  .time-value {
    background: linear-gradient(135deg, #FFCC00 0%, #FF9500 100%) !important;
  }
}

.countdown-default {
  .countdown-label {
    color: #007AFF;
  }
  
  .time-value {
    background: linear-gradient(135deg, #007AFF 0%, #0A84FF 100%) !important;
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
  }
}
</style> 