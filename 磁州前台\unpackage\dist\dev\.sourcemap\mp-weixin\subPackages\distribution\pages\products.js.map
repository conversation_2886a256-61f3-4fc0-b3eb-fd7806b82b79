{"version": 3, "file": "products.js", "sources": ["subPackages/distribution/pages/products.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/c3ViUGFja2FnZXNcZGlzdHJpYnV0aW9uXHBhZ2VzXHByb2R1Y3RzLnZ1ZQ"], "sourcesContent": ["<template>\r\n  <view class=\"products-container\">\r\n    <!-- 自定义导航栏 -->\r\n    <view class=\"navbar\">\r\n      <view class=\"navbar-back\" @click=\"goBack\">\r\n        <view class=\"back-icon\"></view>\r\n      </view>\r\n      <text class=\"navbar-title\">可分销商品</text>\r\n      <view class=\"navbar-right\">\r\n        <view class=\"help-icon\" @click=\"showHelp\">?</view>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 搜索栏 -->\r\n    <view class=\"search-bar\">\r\n      <view class=\"search-input-wrap\">\r\n        <view class=\"search-icon\"></view>\r\n        <input \r\n          class=\"search-input\" \r\n          type=\"text\" \r\n          v-model=\"searchKeyword\" \r\n          placeholder=\"搜索商品\" \r\n          confirm-type=\"search\"\r\n          @confirm=\"searchProducts\"\r\n        />\r\n        <view class=\"clear-icon\" v-if=\"searchKeyword\" @click=\"clearSearch\"></view>\r\n      </view>\r\n      <view class=\"search-btn\" @click=\"searchProducts\">搜索</view>\r\n    </view>\r\n    \r\n    <!-- 筛选栏 -->\r\n    <view class=\"filter-bar\">\r\n      <view \r\n        v-for=\"(filter, index) in filters\" \r\n        :key=\"index\" \r\n        class=\"filter-item\" \r\n        :class=\"{ 'active': activeFilter === filter.value }\"\r\n        @click=\"switchFilter(filter.value)\"\r\n      >\r\n        <text>{{filter.name}}</text>\r\n        <view class=\"sort-icon\" v-if=\"filter.sortable\"></view>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 商品列表 -->\r\n    <view class=\"products-list\" v-if=\"products.length > 0\">\r\n      <view \r\n        v-for=\"(product, index) in products\" \r\n        :key=\"index\" \r\n        class=\"product-card\"\r\n        @click=\"navigateToProduct(product)\"\r\n      >\r\n        <image class=\"product-image\" :src=\"product.image\" mode=\"aspectFill\"></image>\r\n        <view class=\"product-info\">\r\n          <text class=\"product-name\">{{product.name}}</text>\r\n          <view class=\"product-meta\">\r\n            <text class=\"product-price\">¥{{product.price}}</text>\r\n            <text class=\"product-sales\">已售{{product.sales}}件</text>\r\n          </view>\r\n          <view class=\"product-commission\">\r\n            <text class=\"commission-rate\">佣金比例 {{product.commissionRate}}%</text>\r\n            <text class=\"commission-value\">预计佣金 ¥{{product.commission}}</text>\r\n          </view>\r\n          <button class=\"promote-btn\" @click.stop=\"promoteProduct(product)\">立即推广</button>\r\n        </view>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 空状态 -->\r\n    <view class=\"empty-state\" v-else>\r\n      <image class=\"empty-image\" src=\"/static/images/empty-products.png\" mode=\"aspectFit\"></image>\r\n      <text class=\"empty-text\">暂无可分销商品</text>\r\n    </view>\r\n    \r\n    <!-- 加载更多 -->\r\n    <view class=\"load-more\" v-if=\"hasMoreData && products.length > 0\">\r\n      <text v-if=\"loading\">加载中...</text>\r\n      <text v-else @click=\"loadMore\">点击加载更多</text>\r\n    </view>\r\n    \r\n    <!-- 推广弹窗 -->\r\n    <view class=\"promotion-modal\" v-if=\"showPromotionModal\">\r\n      <view class=\"modal-mask\" @click=\"closePromotionModal\"></view>\r\n      <view class=\"modal-content\">\r\n        <view class=\"modal-header\">\r\n          <text class=\"modal-title\">选择推广方式</text>\r\n          <view class=\"close-icon\" @click=\"closePromotionModal\"></view>\r\n        </view>\r\n        \r\n        <view class=\"promotion-options\">\r\n          <view class=\"promotion-option\" @click=\"navigateTo('/subPackages/promotion/pages/promotion-tool', selectedProduct)\">\r\n            <view class=\"option-icon poster\"></view>\r\n            <text class=\"option-name\">生成海报</text>\r\n          </view>\r\n          \r\n          <view class=\"promotion-option\" @click=\"navigateTo('/subPackages/promotion/pages/qrcode', selectedProduct)\">\r\n            <view class=\"option-icon qrcode\"></view>\r\n            <text class=\"option-name\">生成二维码</text>\r\n          </view>\r\n          \r\n          <view class=\"promotion-option\" @click=\"copyPromotionLink\">\r\n            <view class=\"option-icon link\"></view>\r\n            <text class=\"option-name\">复制链接</text>\r\n          </view>\r\n          \r\n          <view class=\"promotion-option\" @click=\"shareToFriends\">\r\n            <view class=\"option-icon share\"></view>\r\n            <text class=\"option-name\">分享好友</text>\r\n          </view>\r\n        </view>\r\n      </view>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script setup>\r\nimport { ref, reactive, onMounted } from 'vue';\r\nimport distributionService from '@/utils/distributionService';\r\n\r\n// 搜索关键词\r\nconst searchKeyword = ref('');\r\n\r\n// 筛选选项\r\nconst filters = [\r\n  { name: '综合排序', value: 'default' },\r\n  { name: '佣金优先', value: 'commission', sortable: true },\r\n  { name: '销量优先', value: 'sales', sortable: true },\r\n  { name: '价格优先', value: 'price', sortable: true }\r\n];\r\n\r\n// 当前选中的筛选\r\nconst activeFilter = ref('default');\r\n\r\n// 商品列表\r\nconst products = ref([]);\r\n\r\n// 分页信息\r\nconst pagination = reactive({\r\n  page: 1,\r\n  pageSize: 10,\r\n  total: 0,\r\n  totalPages: 0\r\n});\r\n\r\n// 是否有更多数据\r\nconst hasMoreData = ref(false);\r\n\r\n// 是否正在加载\r\nconst loading = ref(false);\r\n\r\n// 推广弹窗\r\nconst showPromotionModal = ref(false);\r\n\r\n// 选中的商品\r\nconst selectedProduct = ref(null);\r\n\r\n// 页面加载\r\nonMounted(async () => {\r\n  // 获取商品列表\r\n  await getProducts();\r\n});\r\n\r\n// 获取商品列表\r\nconst getProducts = async (loadMore = false) => {\r\n  if (loading.value) return;\r\n  \r\n  try {\r\n    loading.value = true;\r\n    \r\n    const page = loadMore ? pagination.page + 1 : 1;\r\n    \r\n    const result = await distributionService.getDistributableProducts({\r\n      page,\r\n      pageSize: pagination.pageSize,\r\n      sortBy: activeFilter.value === 'default' ? 'commission' : activeFilter.value,\r\n      keyword: searchKeyword.value\r\n    });\r\n    \r\n    if (result) {\r\n      // 更新商品列表\r\n      if (loadMore) {\r\n        products.value = [...products.value, ...result.list];\r\n      } else {\r\n        products.value = result.list;\r\n      }\r\n      \r\n      // 更新分页信息\r\n      pagination.page = page;\r\n      pagination.total = result.pagination.total;\r\n      pagination.totalPages = result.pagination.totalPages;\r\n      \r\n      // 更新是否有更多数据\r\n      hasMoreData.value = pagination.page < pagination.totalPages;\r\n    }\r\n  } catch (error) {\r\n    console.error('获取商品列表失败', error);\r\n    uni.showToast({\r\n      title: '获取商品列表失败',\r\n      icon: 'none'\r\n    });\r\n  } finally {\r\n    loading.value = false;\r\n  }\r\n};\r\n\r\n// 搜索商品\r\nconst searchProducts = () => {\r\n  getProducts();\r\n};\r\n\r\n// 清除搜索\r\nconst clearSearch = () => {\r\n  searchKeyword.value = '';\r\n  getProducts();\r\n};\r\n\r\n// 切换筛选\r\nconst switchFilter = (filter) => {\r\n  if (activeFilter.value === filter) return;\r\n  \r\n  activeFilter.value = filter;\r\n  getProducts();\r\n};\r\n\r\n// 加载更多\r\nconst loadMore = () => {\r\n  if (hasMoreData.value && !loading.value) {\r\n    getProducts(true);\r\n  }\r\n};\r\n\r\n// 推广商品\r\nconst promoteProduct = (product) => {\r\n  selectedProduct.value = product;\r\n  showPromotionModal.value = true;\r\n};\r\n\r\n// 关闭推广弹窗\r\nconst closePromotionModal = () => {\r\n  showPromotionModal.value = false;\r\n};\r\n\r\n// 复制推广链接\r\nconst copyPromotionLink = async () => {\r\n  if (!selectedProduct.value) return;\r\n  \r\n  try {\r\n    const result = await distributionService.generatePromotionLink({\r\n      type: 'product',\r\n      id: selectedProduct.value.id,\r\n      distributorId: 'DIS1001' // 模拟数据，实际应从用户系统获取\r\n    });\r\n    \r\n    if (result && result.url) {\r\n      uni.setClipboardData({\r\n        data: result.url,\r\n        success: () => {\r\n          uni.showToast({\r\n            title: '链接已复制',\r\n            icon: 'success'\r\n          });\r\n          closePromotionModal();\r\n        }\r\n      });\r\n    }\r\n  } catch (error) {\r\n    console.error('生成推广链接失败', error);\r\n    uni.showToast({\r\n      title: '生成推广链接失败',\r\n      icon: 'none'\r\n    });\r\n  }\r\n};\r\n\r\n// 分享给好友\r\nconst shareToFriends = () => {\r\n  if (!selectedProduct.value) return;\r\n  \r\n  // 调用分享API\r\n  uni.showShareMenu({\r\n    withShareTicket: true,\r\n    menus: ['shareAppMessage', 'shareTimeline']\r\n  });\r\n  \r\n  // 模拟分享成功\r\n  setTimeout(() => {\r\n    closePromotionModal();\r\n  }, 500);\r\n};\r\n\r\n// 导航到商品详情\r\nconst navigateToProduct = (product) => {\r\n  uni.navigateTo({\r\n    url: `/pages/product/detail?id=${product.id}&isDistribution=true`\r\n  });\r\n};\r\n\r\n// 页面导航\r\nconst navigateTo = (url, product) => {\r\n  if (!product) {\r\n    uni.navigateTo({ url });\r\n    return;\r\n  }\r\n  \r\n  // 携带商品信息\r\n  uni.navigateTo({\r\n    url: `${url}?type=product&id=${product.id}&title=${encodeURIComponent(product.name)}&image=${encodeURIComponent(product.image)}`\r\n  });\r\n  \r\n  // 关闭弹窗\r\n  closePromotionModal();\r\n};\r\n\r\n// 返回上一页\r\nconst goBack = () => {\r\n  uni.navigateBack();\r\n};\r\n\r\n// 显示帮助\r\nconst showHelp = () => {\r\n  uni.showModal({\r\n    title: '可分销商品帮助',\r\n    content: '可分销商品是平台允许分销员推广并获得佣金的商品。您可以选择商品进行推广，通过分享获得佣金。',\r\n    showCancel: false\r\n  });\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.products-container {\r\n  min-height: 100vh;\r\n  background-color: #F5F7FA;\r\n  padding-bottom: 30rpx;\r\n}\r\n\r\n/* 导航栏样式 */\r\n.navbar {\r\n  position: sticky;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  background: linear-gradient(135deg, #A764CA, #6B0FBE);\r\n  color: #fff;\r\n  padding: 88rpx 32rpx 30rpx;\r\n  display: flex;\r\n  align-items: center;\r\n  z-index: 100;\r\n  box-shadow: 0 4rpx 20rpx rgba(107, 15, 190, 0.15);\r\n}\r\n\r\n.navbar-back {\r\n  width: 72rpx;\r\n  height: 72rpx;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.back-icon {\r\n  width: 24rpx;\r\n  height: 24rpx;\r\n  border-left: 4rpx solid #fff;\r\n  border-bottom: 4rpx solid #fff;\r\n  transform: rotate(45deg);\r\n}\r\n\r\n.navbar-title {\r\n  flex: 1;\r\n  text-align: center;\r\n  font-size: 36rpx;\r\n  font-weight: 600;\r\n  letter-spacing: 1rpx;\r\n}\r\n\r\n.navbar-right {\r\n  width: 72rpx;\r\n  height: 72rpx;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.help-icon {\r\n  width: 48rpx;\r\n  height: 48rpx;\r\n  border-radius: 24rpx;\r\n  background: rgba(255, 255, 255, 0.2);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  font-size: 28rpx;\r\n  font-weight: bold;\r\n}\r\n\r\n/* 搜索栏 */\r\n.search-bar {\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 20rpx 30rpx;\r\n  background: #FFFFFF;\r\n}\r\n\r\n.search-input-wrap {\r\n  flex: 1;\r\n  height: 72rpx;\r\n  background: #F5F7FA;\r\n  border-radius: 36rpx;\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 0 20rpx;\r\n  margin-right: 20rpx;\r\n}\r\n\r\n.search-icon {\r\n  width: 32rpx;\r\n  height: 32rpx;\r\n  background-color: #6B0FBE;\r\n  border-radius: 40rpx;\r\n  margin-right: 10rpx;\r\n}\r\n\r\n.search-input {\r\n  flex: 1;\r\n  height: 100%;\r\n  font-size: 28rpx;\r\n  color: #333;\r\n}\r\n\r\n.clear-icon {\r\n  width: 32rpx;\r\n  height: 32rpx;\r\n  background-color: #cccccc;\r\n  border-radius: 40rpx;\r\n}\r\n\r\n.search-btn {\r\n  font-size: 28rpx;\r\n  color: #6B0FBE;\r\n}\r\n\r\n/* 筛选栏 */\r\n.filter-bar {\r\n  display: flex;\r\n  background: #FFFFFF;\r\n  padding: 0 30rpx;\r\n  margin-bottom: 20rpx;\r\n  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n.filter-item {\r\n  flex: 1;\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  height: 80rpx;\r\n  font-size: 28rpx;\r\n  color: #666;\r\n  position: relative;\r\n}\r\n\r\n.filter-item.active {\r\n  color: #6B0FBE;\r\n  font-weight: 600;\r\n}\r\n\r\n.sort-icon {\r\n  width: 16rpx;\r\n  height: 24rpx;\r\n  position: relative;\r\n  margin-left: 8rpx;\r\n}\r\n\r\n.sort-icon::before,\r\n.sort-icon::after {\r\n  content: '';\r\n  position: absolute;\r\n  left: 0;\r\n  width: 0;\r\n  height: 0;\r\n}\r\n\r\n.sort-icon::before {\r\n  top: 4rpx;\r\n  border-left: 8rpx solid transparent;\r\n  border-right: 8rpx solid transparent;\r\n  border-bottom: 8rpx solid #999;\r\n}\r\n\r\n.sort-icon::after {\r\n  bottom: 4rpx;\r\n  border-left: 8rpx solid transparent;\r\n  border-right: 8rpx solid transparent;\r\n  border-top: 8rpx solid #999;\r\n}\r\n\r\n/* 商品列表 */\r\n.products-list {\r\n  margin: 0 30rpx;\r\n}\r\n\r\n.product-card {\r\n  background: #FFFFFF;\r\n  border-radius: 20rpx;\r\n  margin-bottom: 20rpx;\r\n  overflow: hidden;\r\n  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n.product-image {\r\n  width: 100%;\r\n  height: 400rpx;\r\n  background-color: #f5f5f5;\r\n}\r\n\r\n.product-info {\r\n  padding: 20rpx;\r\n}\r\n\r\n.product-name {\r\n  font-size: 28rpx;\r\n  font-weight: 600;\r\n  color: #333;\r\n  margin-bottom: 16rpx;\r\n  display: -webkit-box;\r\n  -webkit-box-orient: vertical;\r\n  -webkit-line-clamp: 2;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  line-height: 1.4;\r\n}\r\n\r\n.product-meta {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 16rpx;\r\n}\r\n\r\n.product-price {\r\n  font-size: 32rpx;\r\n  font-weight: 600;\r\n  color: #FF5722;\r\n}\r\n\r\n.product-sales {\r\n  font-size: 24rpx;\r\n  color: #999;\r\n}\r\n\r\n.product-commission {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 20rpx;\r\n}\r\n\r\n.commission-rate {\r\n  font-size: 24rpx;\r\n  color: #666;\r\n}\r\n\r\n.commission-value {\r\n  font-size: 24rpx;\r\n  color: #6B0FBE;\r\n  font-weight: 600;\r\n}\r\n\r\n.promote-btn {\r\n  background: linear-gradient(135deg, #A764CA, #6B0FBE);\r\n  color: #fff;\r\n  border: none;\r\n  border-radius: 40rpx;\r\n  font-size: 28rpx;\r\n  padding: 12rpx 0;\r\n  line-height: 1.5;\r\n  width: 100%;\r\n}\r\n\r\n/* 空状态 */\r\n.empty-state {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  padding: 100rpx 0;\r\n}\r\n\r\n.empty-image {\r\n  width: 200rpx;\r\n  height: 200rpx;\r\n  margin-bottom: 30rpx;\r\n}\r\n\r\n.empty-text {\r\n  font-size: 28rpx;\r\n  color: #999;\r\n}\r\n\r\n/* 加载更多 */\r\n.load-more {\r\n  text-align: center;\r\n  padding: 30rpx 0;\r\n  font-size: 26rpx;\r\n  color: #666;\r\n}\r\n\r\n/* 推广弹窗 */\r\n.promotion-modal {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  z-index: 1000;\r\n}\r\n\r\n.modal-mask {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background-color: rgba(0, 0, 0, 0.5);\r\n}\r\n\r\n.modal-content {\r\n  position: absolute;\r\n  bottom: 0;\r\n  left: 0;\r\n  right: 0;\r\n  background-color: #FFFFFF;\r\n  border-radius: 40rpx 40rpx 0 0;\r\n  padding: 30rpx;\r\n  animation: slideUp 0.3s ease-out;\r\n}\r\n\r\n@keyframes slideUp {\r\n  from {\r\n    transform: translateY(100%);\r\n  }\r\n  to {\r\n    transform: translateY(0);\r\n  }\r\n}\r\n\r\n.modal-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 30rpx;\r\n}\r\n\r\n.modal-title {\r\n  font-size: 32rpx;\r\n  font-weight: 600;\r\n  color: #333;\r\n}\r\n\r\n.close-icon {\r\n  width: 40rpx;\r\n  height: 40rpx;\r\n  background-color: #999;\r\n  border-radius: 40rpx;\r\n}\r\n\r\n.promotion-options {\r\n  display: flex;\r\n  justify-content: space-around;\r\n  padding: 30rpx 0;\r\n}\r\n\r\n.promotion-option {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n}\r\n\r\n.option-icon {\r\n  width: 80rpx;\r\n  height: 80rpx;\r\n  margin-bottom: 16rpx;\r\n  border-radius: 40rpx;\r\n}\r\n\r\n.option-icon.poster {\r\n  background-color: #FF9500;\r\n}\r\n\r\n.option-icon.qrcode {\r\n  background-color: #34C759;\r\n}\r\n\r\n.option-icon.link {\r\n  background-color: #1677FF;\r\n}\r\n\r\n.option-icon.share {\r\n  background-color: #6B0FBE;\r\n}\r\n\r\n.option-name {\r\n  font-size: 26rpx;\r\n  color: #333;\r\n}\r\n</style> ", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/subPackages/distribution/pages/products.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "reactive", "onMounted", "loadMore", "distributionService", "uni", "MiniProgramPage"], "mappings": ";;;;;;;AAwHA,UAAM,gBAAgBA,cAAAA,IAAI,EAAE;AAG5B,UAAM,UAAU;AAAA,MACd,EAAE,MAAM,QAAQ,OAAO,UAAW;AAAA,MAClC,EAAE,MAAM,QAAQ,OAAO,cAAc,UAAU,KAAM;AAAA,MACrD,EAAE,MAAM,QAAQ,OAAO,SAAS,UAAU,KAAM;AAAA,MAChD,EAAE,MAAM,QAAQ,OAAO,SAAS,UAAU,KAAM;AAAA,IAClD;AAGA,UAAM,eAAeA,cAAAA,IAAI,SAAS;AAGlC,UAAM,WAAWA,cAAAA,IAAI,CAAA,CAAE;AAGvB,UAAM,aAAaC,cAAAA,SAAS;AAAA,MAC1B,MAAM;AAAA,MACN,UAAU;AAAA,MACV,OAAO;AAAA,MACP,YAAY;AAAA,IACd,CAAC;AAGD,UAAM,cAAcD,cAAAA,IAAI,KAAK;AAG7B,UAAM,UAAUA,cAAAA,IAAI,KAAK;AAGzB,UAAM,qBAAqBA,cAAAA,IAAI,KAAK;AAGpC,UAAM,kBAAkBA,cAAAA,IAAI,IAAI;AAGhCE,kBAAAA,UAAU,YAAY;AAEpB,YAAM,YAAW;AAAA,IACnB,CAAC;AAGD,UAAM,cAAc,OAAOC,YAAW,UAAU;AAC9C,UAAI,QAAQ;AAAO;AAEnB,UAAI;AACF,gBAAQ,QAAQ;AAEhB,cAAM,OAAOA,YAAW,WAAW,OAAO,IAAI;AAE9C,cAAM,SAAS,MAAMC,0BAAmB,oBAAC,yBAAyB;AAAA,UAChE;AAAA,UACA,UAAU,WAAW;AAAA,UACrB,QAAQ,aAAa,UAAU,YAAY,eAAe,aAAa;AAAA,UACvE,SAAS,cAAc;AAAA,QAC7B,CAAK;AAED,YAAI,QAAQ;AAEV,cAAID,WAAU;AACZ,qBAAS,QAAQ,CAAC,GAAG,SAAS,OAAO,GAAG,OAAO,IAAI;AAAA,UAC3D,OAAa;AACL,qBAAS,QAAQ,OAAO;AAAA,UACzB;AAGD,qBAAW,OAAO;AAClB,qBAAW,QAAQ,OAAO,WAAW;AACrC,qBAAW,aAAa,OAAO,WAAW;AAG1C,sBAAY,QAAQ,WAAW,OAAO,WAAW;AAAA,QAClD;AAAA,MACF,SAAQ,OAAO;AACdE,sBAAA,MAAA,MAAA,SAAA,sDAAc,YAAY,KAAK;AAC/BA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AAAA,MACL,UAAY;AACR,gBAAQ,QAAQ;AAAA,MACjB;AAAA,IACH;AAGA,UAAM,iBAAiB,MAAM;AAC3B;IACF;AAGA,UAAM,cAAc,MAAM;AACxB,oBAAc,QAAQ;AACtB;IACF;AAGA,UAAM,eAAe,CAAC,WAAW;AAC/B,UAAI,aAAa,UAAU;AAAQ;AAEnC,mBAAa,QAAQ;AACrB;IACF;AAGA,UAAM,WAAW,MAAM;AACrB,UAAI,YAAY,SAAS,CAAC,QAAQ,OAAO;AACvC,oBAAY,IAAI;AAAA,MACjB;AAAA,IACH;AAGA,UAAM,iBAAiB,CAAC,YAAY;AAClC,sBAAgB,QAAQ;AACxB,yBAAmB,QAAQ;AAAA,IAC7B;AAGA,UAAM,sBAAsB,MAAM;AAChC,yBAAmB,QAAQ;AAAA,IAC7B;AAGA,UAAM,oBAAoB,YAAY;AACpC,UAAI,CAAC,gBAAgB;AAAO;AAE5B,UAAI;AACF,cAAM,SAAS,MAAMD,0BAAmB,oBAAC,sBAAsB;AAAA,UAC7D,MAAM;AAAA,UACN,IAAI,gBAAgB,MAAM;AAAA,UAC1B,eAAe;AAAA;AAAA,QACrB,CAAK;AAED,YAAI,UAAU,OAAO,KAAK;AACxBC,wBAAAA,MAAI,iBAAiB;AAAA,YACnB,MAAM,OAAO;AAAA,YACb,SAAS,MAAM;AACbA,4BAAAA,MAAI,UAAU;AAAA,gBACZ,OAAO;AAAA,gBACP,MAAM;AAAA,cAClB,CAAW;AACD;YACD;AAAA,UACT,CAAO;AAAA,QACF;AAAA,MACF,SAAQ,OAAO;AACdA,sBAAA,MAAA,MAAA,SAAA,sDAAc,YAAY,KAAK;AAC/BA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AAAA,MACF;AAAA,IACH;AAGA,UAAM,iBAAiB,MAAM;AAC3B,UAAI,CAAC,gBAAgB;AAAO;AAG5BA,oBAAAA,MAAI,cAAc;AAAA,QAChB,iBAAiB;AAAA,QACjB,OAAO,CAAC,mBAAmB,eAAe;AAAA,MAC9C,CAAG;AAGD,iBAAW,MAAM;AACf;MACD,GAAE,GAAG;AAAA,IACR;AAGA,UAAM,oBAAoB,CAAC,YAAY;AACrCA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,4BAA4B,QAAQ,EAAE;AAAA,MAC/C,CAAG;AAAA,IACH;AAGA,UAAM,aAAa,CAAC,KAAK,YAAY;AACnC,UAAI,CAAC,SAAS;AACZA,sBAAAA,MAAI,WAAW,EAAE,IAAG,CAAE;AACtB;AAAA,MACD;AAGDA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,GAAG,GAAG,oBAAoB,QAAQ,EAAE,UAAU,mBAAmB,QAAQ,IAAI,CAAC,UAAU,mBAAmB,QAAQ,KAAK,CAAC;AAAA,MAClI,CAAG;AAGD;IACF;AAGA,UAAM,SAAS,MAAM;AACnBA,oBAAG,MAAC,aAAY;AAAA,IAClB;AAGA,UAAM,WAAW,MAAM;AACrBA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,SAAS;AAAA,QACT,YAAY;AAAA,MAChB,CAAG;AAAA,IACH;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACpUA,GAAG,WAAWC,SAAe;"}