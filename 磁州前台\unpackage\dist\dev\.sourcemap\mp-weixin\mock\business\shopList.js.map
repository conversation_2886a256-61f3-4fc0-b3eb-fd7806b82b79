{"version": 3, "file": "shopList.js", "sources": ["mock/business/shopList.js"], "sourcesContent": ["// 商店列表模拟数据\r\nexport const shopList = [\r\n  {\r\n    id: 'shop-1',\r\n    name: '老街烧烤',\r\n    category: '餐饮美食',\r\n    tags: ['烧烤', '啤酒', '夜宵'],\r\n    rating: 4.8,\r\n    sales: 2580,\r\n    address: '磁县滏阳路与建设路交叉口',\r\n    distance: 0.8,\r\n    businessHours: '16:00-02:00',\r\n    phone: '***********',\r\n    images: [\r\n      '/static/images/business/shop1-1.jpg',\r\n      '/static/images/business/shop1-2.jpg'\r\n    ],\r\n    promotion: '满100减20，满200减50',\r\n    isOfficial: true,\r\n    isFeatured: true,\r\n    description: '正宗东北烧烤，食材新鲜，调料独特，环境舒适，欢迎品尝！'\r\n  },\r\n  {\r\n    id: 'shop-2',\r\n    name: '聚福茶楼',\r\n    category: '餐饮美食',\r\n    tags: ['早茶', '粤菜', '家庭聚餐'],\r\n    rating: 4.6,\r\n    sales: 1860,\r\n    address: '磁县人民路120号',\r\n    distance: 1.2,\r\n    businessHours: '07:00-21:00',\r\n    phone: '***********',\r\n    images: [\r\n      '/static/images/business/shop2-1.jpg'\r\n    ],\r\n    promotion: '早市8折，寿星免费赠送长寿面',\r\n    isOfficial: false,\r\n    isFeatured: true,\r\n    description: '正宗粤式早茶，提供各类点心、粥品、茶水，环境优雅，服务周到。'\r\n  },\r\n  {\r\n    id: 'shop-3',\r\n    name: '都市丽人美发',\r\n    category: '美容美发',\r\n    tags: ['烫发', '染发', '护理'],\r\n    rating: 4.7,\r\n    sales: 980,\r\n    address: '磁县振兴路56号',\r\n    distance: 1.5,\r\n    businessHours: '09:00-20:00',\r\n    phone: '***********',\r\n    images: [\r\n      '/static/images/business/shop3-1.jpg'\r\n    ],\r\n    promotion: '新客首次消费8折，办卡会员专享特权',\r\n    isOfficial: true,\r\n    isFeatured: false,\r\n    description: '专业美发沙龙，提供剪发、烫发、染发、护理等全方位服务，技术精湛，服务周到。'\r\n  }\r\n];\r\n\r\n// 获取商店列表的API函数\r\nexport const fetchShopList = (category = '') => {\r\n  return new Promise((resolve) => {\r\n    setTimeout(() => {\r\n      if (!category || category === '全部') {\r\n        resolve(shopList);\r\n      } else {\r\n        const filteredList = shopList.filter(shop => shop.category === category);\r\n        resolve(filteredList);\r\n      }\r\n    }, 300);\r\n  });\r\n};\r\n\r\n// 获取商店详情的API函数\r\nexport const fetchShopDetail = (id) => {\r\n  return new Promise((resolve, reject) => {\r\n    setTimeout(() => {\r\n      const shop = shopList.find(item => item.id === id);\r\n      if (shop) {\r\n        resolve(shop);\r\n      } else {\r\n        reject(new Error('商店不存在'));\r\n      }\r\n    }, 300);\r\n  });\r\n}; "], "names": [], "mappings": ";AACO,MAAM,WAAW;AAAA,EACtB;AAAA,IACE,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,UAAU;AAAA,IACV,MAAM,CAAC,MAAM,MAAM,IAAI;AAAA,IACvB,QAAQ;AAAA,IACR,OAAO;AAAA,IACP,SAAS;AAAA,IACT,UAAU;AAAA,IACV,eAAe;AAAA,IACf,OAAO;AAAA,IACP,QAAQ;AAAA,MACN;AAAA,MACA;AAAA,IACD;AAAA,IACD,WAAW;AAAA,IACX,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,aAAa;AAAA,EACd;AAAA,EACD;AAAA,IACE,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,UAAU;AAAA,IACV,MAAM,CAAC,MAAM,MAAM,MAAM;AAAA,IACzB,QAAQ;AAAA,IACR,OAAO;AAAA,IACP,SAAS;AAAA,IACT,UAAU;AAAA,IACV,eAAe;AAAA,IACf,OAAO;AAAA,IACP,QAAQ;AAAA,MACN;AAAA,IACD;AAAA,IACD,WAAW;AAAA,IACX,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,aAAa;AAAA,EACd;AAAA,EACD;AAAA,IACE,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,UAAU;AAAA,IACV,MAAM,CAAC,MAAM,MAAM,IAAI;AAAA,IACvB,QAAQ;AAAA,IACR,OAAO;AAAA,IACP,SAAS;AAAA,IACT,UAAU;AAAA,IACV,eAAe;AAAA,IACf,OAAO;AAAA,IACP,QAAQ;AAAA,MACN;AAAA,IACD;AAAA,IACD,WAAW;AAAA,IACX,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,aAAa;AAAA,EACd;AACH;AAGY,MAAC,gBAAgB,CAAC,WAAW,OAAO;AAC9C,SAAO,IAAI,QAAQ,CAAC,YAAY;AAC9B,eAAW,MAAM;AACf,UAAI,CAAC,YAAY,aAAa,MAAM;AAClC,gBAAQ,QAAQ;AAAA,MACxB,OAAa;AACL,cAAM,eAAe,SAAS,OAAO,UAAQ,KAAK,aAAa,QAAQ;AACvE,gBAAQ,YAAY;AAAA,MACrB;AAAA,IACF,GAAE,GAAG;AAAA,EACV,CAAG;AACH;AAGY,MAAC,kBAAkB,CAAC,OAAO;AACrC,SAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtC,eAAW,MAAM;AACf,YAAM,OAAO,SAAS,KAAK,UAAQ,KAAK,OAAO,EAAE;AACjD,UAAI,MAAM;AACR,gBAAQ,IAAI;AAAA,MACpB,OAAa;AACL,eAAO,IAAI,MAAM,OAAO,CAAC;AAAA,MAC1B;AAAA,IACF,GAAE,GAAG;AAAA,EACV,CAAG;AACH;;;"}