"use strict";
const common_vendor = require("../../../common/vendor.js");
const utils_promotionService = require("../../../utils/promotionService.js");
const _sfc_main = {
  __name: "qrcode",
  setup(__props) {
    const contentType = common_vendor.ref("");
    const contentId = common_vendor.ref("");
    const title = common_vendor.ref("磁州生活网");
    const qrcodePath = common_vendor.ref("");
    const userId = common_vendor.ref("");
    common_vendor.onMounted(() => {
      const pages = getCurrentPages();
      const currentPage = pages[pages.length - 1];
      const options = currentPage.options || {};
      contentType.value = options.type || "";
      contentId.value = options.id || "";
      title.value = options.title || "磁州生活网";
      const userInfo = common_vendor.index.getStorageSync("userInfo");
      if (userInfo && userInfo.userId) {
        userId.value = userInfo.userId;
      }
      generateQrcode();
    });
    const generateQrcode = async () => {
      if (!contentType.value || !contentId.value) {
        common_vendor.index.showToast({
          title: "参数错误",
          icon: "none"
        });
        setTimeout(() => {
          goBack();
        }, 1500);
        return;
      }
      try {
        qrcodePath.value = await utils_promotionService.promotionService.generateQrcode(
          contentType.value,
          contentId.value,
          userId.value
        );
        utils_promotionService.promotionService.recordPromotion(contentType.value, contentId.value, "qrcode");
      } catch (err) {
        common_vendor.index.__f__("error", "at subPackages/promotion/pages/qrcode.vue:110", "生成二维码失败", err);
        common_vendor.index.showToast({
          title: "生成二维码失败",
          icon: "none"
        });
      }
    };
    const saveQrcode = async () => {
      if (!qrcodePath.value) {
        common_vendor.index.showToast({
          title: "二维码未生成",
          icon: "none"
        });
        return;
      }
      try {
        await utils_promotionService.promotionService.savePosterToAlbum(qrcodePath.value);
      } catch (err) {
        common_vendor.index.__f__("error", "at subPackages/promotion/pages/qrcode.vue:131", "保存二维码失败", err);
      }
    };
    const shareQrcode = () => {
      if (!qrcodePath.value) {
        common_vendor.index.showToast({
          title: "二维码未生成",
          icon: "none"
        });
        return;
      }
      common_vendor.index.showShareMenu({
        withShareTicket: true,
        menus: ["shareAppMessage", "shareTimeline"]
      });
    };
    const goBack = () => {
      common_vendor.index.navigateBack();
    };
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_vendor.o(goBack),
        b: common_vendor.t(title.value),
        c: qrcodePath.value
      }, qrcodePath.value ? {
        d: qrcodePath.value
      } : {}, {
        e: common_vendor.o(saveQrcode),
        f: common_vendor.o(shareQrcode)
      });
    };
  }
};
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-172fcc21"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/subPackages/promotion/pages/qrcode.js.map
