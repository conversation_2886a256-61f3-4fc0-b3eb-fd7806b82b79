<!-- 创建中 -->
<template>
  <view class="create-package-info-container">
    <!-- 自定义导航栏 -->
    <view class="navbar">
      <view class="navbar-back" @click="goBack">
        <view class="back-icon"></view>
      </view>
      <text class="navbar-title">拼团活动</text>
      <view class="navbar-right">
        <view class="help-icon" @click="showHelp">?</view>
      </view>
    </view>
    
    <!-- 步骤指示器 -->
    <view class="step-indicator">
      <view class="step-progress">
        <view class="step-progress-bar" style="width: 40%"></view>
      </view>
      <view class="step-text">步骤 2/5</view>
    </view>
    
    <!-- 页面内容 -->
    <scroll-view scroll-y class="page-content">
      <view class="page-title">填写套餐基本信息</view>
      <view class="page-subtitle">请填写团购套餐的基本信息</view>
      
      <view class="form-section">
        <view class="form-item">
          <text class="form-label">套餐名称 <text class="required">*</text></text>
          <input class="form-input" type="text" v-model="packageInfo.name" placeholder="请输入套餐名称，如：四菜一汤家庭套餐" maxlength="30" />
          <text class="input-counter">{{packageInfo.name.length}}/30</text>
        </view>
        
        <view class="form-item">
          <text class="form-label">套餐描述</text>
          <textarea class="form-textarea" v-model="packageInfo.description" placeholder="请输入套餐描述信息，如：适合3-4人用餐，荤素搭配，营养均衡" maxlength="100"></textarea>
          <text class="input-counter">{{packageInfo.description.length}}/100</text>
        </view>
        
        <view class="form-item">
          <text class="form-label">套餐分类 <text class="required">*</text></text>
          <picker mode="selector" :range="categoryOptions" @change="onCategoryChange" class="form-picker">
            <view class="picker-value">
              <text v-if="packageInfo.category">{{categoryOptions[packageInfo.categoryIndex]}}</text>
              <text v-else class="placeholder">请选择套餐分类</text>
              <view class="picker-arrow"></view>
            </view>
          </picker>
        </view>
        
        <view class="form-item">
          <text class="form-label">成团人数 <text class="required">*</text></text>
          <view class="number-picker">
            <view class="number-btn minus" @tap="decrementGroupSize">-</view>
            <input class="number-input" type="number" v-model="packageInfo.groupSize" />
            <view class="number-btn plus" @tap="incrementGroupSize">+</view>
            <text class="unit-text">人</text>
          </view>
        </view>
        
        <view class="form-item">
          <text class="form-label">活动有效期 <text class="required">*</text></text>
          <view class="date-range-picker">
            <picker mode="date" :value="packageInfo.startDate" @change="onStartDateChange" class="date-picker">
              <view class="date-picker-value">
                <text v-if="packageInfo.startDate">{{packageInfo.startDate}}</text>
                <text v-else class="placeholder">开始日期</text>
              </view>
            </picker>
            <text class="date-separator">至</text>
            <picker mode="date" :value="packageInfo.endDate" @change="onEndDateChange" class="date-picker">
              <view class="date-picker-value">
                <text v-if="packageInfo.endDate">{{packageInfo.endDate}}</text>
                <text v-else class="placeholder">结束日期</text>
              </view>
            </picker>
          </view>
        </view>
      </view>
    </scroll-view>
    
    <!-- 底部按钮 -->
    <view class="footer-buttons">
      <button class="btn btn-secondary" @click="goBack">上一步</button>
      <button class="btn btn-primary" @click="nextStep">下一步</button>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      packageInfo: {
        name: '',
        description: '',
        category: '',
        categoryIndex: 0,
        groupSize: 2,
        startDate: '',
        endDate: ''
      },
      categoryOptions: ['餐饮美食', '休闲娱乐', '美容美发', '生活服务', '其他']
    }
  },
  onLoad() {
    // 尝试从本地存储获取之前保存的数据
    try {
      const packageType = uni.getStorageSync('packageType');
      const savedInfo = uni.getStorageSync('packageInfo');
      
      if (packageType) {
        console.log('获取到套餐类型:', packageType);
      }
      
      if (savedInfo) {
        this.packageInfo = JSON.parse(savedInfo);
      }
    } catch (e) {
      console.error('读取本地存储失败:', e);
    }
  },
  methods: {
    goBack() {
      // 保存当前页面数据
      this.saveData();
      uni.navigateBack();
    },
    nextStep() {
      // 表单验证
      if (!this.packageInfo.name) {
        uni.showToast({
          title: '请输入套餐名称',
          icon: 'none'
        });
        return;
      }
      
      if (!this.packageInfo.category) {
        uni.showToast({
          title: '请选择套餐分类',
          icon: 'none'
        });
        return;
      }
      
      if (!this.packageInfo.startDate || !this.packageInfo.endDate) {
        uni.showToast({
          title: '请选择活动有效期',
          icon: 'none'
        });
        return;
      }
      
      // 保存数据并跳转到下一步
      this.saveData();
      uni.navigateTo({
        url: '/subPackages/merchant-admin-marketing/pages/marketing/group/create-package-price'
      });
    },
    saveData() {
      // 保存当前页面数据到本地存储
      try {
        uni.setStorageSync('packageInfo', JSON.stringify(this.packageInfo));
      } catch (e) {
        console.error('保存数据失败:', e);
      }
    },
    onCategoryChange(e) {
      const index = e.detail.value;
      this.packageInfo.categoryIndex = index;
      this.packageInfo.category = this.categoryOptions[index];
    },
    onStartDateChange(e) {
      this.packageInfo.startDate = e.detail.value;
    },
    onEndDateChange(e) {
      this.packageInfo.endDate = e.detail.value;
    },
    decrementGroupSize() {
      if (this.packageInfo.groupSize > 2) {
        this.packageInfo.groupSize--;
      }
    },
    incrementGroupSize() {
      if (this.packageInfo.groupSize < 100) {
        this.packageInfo.groupSize++;
      }
    },
    showHelp() {
      uni.showToast({
        title: '帮助信息',
        icon: 'none'
      });
    }
  }
}
</script>

<style lang="scss">
.create-package-info-container {
  min-height: 100vh;
  background-color: #F5F7FA;
  display: flex;
  flex-direction: column;
}

/* 导航栏样式 */
.navbar {
  position: sticky;
  top: 0;
  left: 0;
  right: 0;
  background: linear-gradient(135deg, #9040FF, #5E35B1);
  color: #fff;
  padding: 44px 16px 15px;
  display: flex;
  align-items: center;
  z-index: 100;
  box-shadow: 0 2px 10px rgba(126, 48, 225, 0.15);
}

.navbar-back {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-icon {
  width: 12px;
  height: 12px;
  border-left: 2px solid #fff;
  border-bottom: 2px solid #fff;
  transform: rotate(45deg);
}

.navbar-title {
  flex: 1;
  text-align: center;
  font-size: 18px;
  font-weight: 600;
  letter-spacing: 0.5px;
}

.navbar-right {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.help-icon {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  border: 1px solid #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  color: #fff;
}

/* 步骤指示器 */
.step-indicator {
  padding: 15px;
  background: #FFFFFF;
}

.step-progress {
  height: 4px;
  background-color: #EBEDF5;
  border-radius: 2px;
  margin-bottom: 5px;
  position: relative;
}

.step-progress-bar {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  background: linear-gradient(90deg, #9040FF, #5E35B1);
  border-radius: 2px;
}

.step-text {
  font-size: 12px;
  color: #999;
  text-align: right;
}

/* 页面内容 */
.page-content {
  flex: 1;
  padding: 20px 15px;
}

.page-title {
  font-size: 20px;
  font-weight: 600;
  color: #333;
  margin-bottom: 5px;
}

.page-subtitle {
  font-size: 14px;
  color: #999;
  margin-bottom: 20px;
}

/* 表单样式 */
.form-section {
  background: #FFFFFF;
  border-radius: 12px;
  padding: 15px;
  margin-bottom: 20px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.form-item {
  margin-bottom: 20px;
}

.form-item:last-child {
  margin-bottom: 0;
}

.form-label {
  font-size: 14px;
  color: #333;
  margin-bottom: 8px;
  display: block;
}

.required {
  color: #FF3B30;
}

.form-input {
  width: 100%;
  height: 45px;
  background: #F5F7FA;
  border-radius: 8px;
  padding: 0 12px;
  font-size: 14px;
  color: #333;
  border: 1px solid #EBEDF5;
}

.form-textarea {
  width: 100%;
  height: 100px;
  background: #F5F7FA;
  border-radius: 8px;
  padding: 12px;
  font-size: 14px;
  color: #333;
  border: 1px solid #EBEDF5;
}

.input-counter {
  font-size: 12px;
  color: #999;
  text-align: right;
  margin-top: 5px;
}

.form-picker {
  width: 100%;
}

.picker-value {
  width: 100%;
  height: 45px;
  background: #F5F7FA;
  border-radius: 8px;
  padding: 0 12px;
  font-size: 14px;
  color: #333;
  border: 1px solid #EBEDF5;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.placeholder {
  color: #999;
}

.picker-arrow {
  width: 0;
  height: 0;
  border-left: 5px solid transparent;
  border-right: 5px solid transparent;
  border-top: 6px solid #999;
}

.number-picker {
  display: flex;
  align-items: center;
  height: 45px;
}

.number-btn {
  width: 45px;
  height: 45px;
  background: #F5F7FA;
  border: 1px solid #EBEDF5;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  color: #333;
}

.number-btn.minus {
  border-radius: 8px 0 0 8px;
}

.number-btn.plus {
  border-radius: 0 8px 8px 0;
}

.number-input {
  width: 60px;
  height: 45px;
  background: #F5F7FA;
  border-top: 1px solid #EBEDF5;
  border-bottom: 1px solid #EBEDF5;
  text-align: center;
  font-size: 14px;
  color: #333;
}

.unit-text {
  margin-left: 10px;
  font-size: 14px;
  color: #666;
}

.date-range-picker {
  display: flex;
  align-items: center;
}

.date-picker {
  flex: 1;
}

.date-picker-value {
  height: 45px;
  background: #F5F7FA;
  border-radius: 8px;
  padding: 0 12px;
  font-size: 14px;
  color: #333;
  border: 1px solid #EBEDF5;
  display: flex;
  align-items: center;
}

.date-separator {
  margin: 0 10px;
  font-size: 14px;
  color: #666;
}

/* 底部按钮 */
.footer-buttons {
  padding: 15px;
  background: #FFFFFF;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);
  display: flex;
  justify-content: space-between;
  gap: 15px;
}

.btn {
  flex: 1;
  height: 50px;
  border-radius: 25px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  font-weight: 600;
  border: none;
}

.btn-primary {
  background: linear-gradient(135deg, #9040FF, #5E35B1);
  color: #FFFFFF;
}

.btn-secondary {
  background: #F5F7FA;
  color: #666;
  border: 1px solid #EBEDF5;
}
</style>
