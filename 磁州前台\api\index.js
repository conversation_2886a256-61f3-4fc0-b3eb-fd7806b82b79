/**
 * API统一入口
 * 整合所有真实API模块，替换Mock数据
 */

import userApi from './userApi';
import contentApi from './contentApi';
import carpoolApi from './carpoolApi';
import homeApi from './homeApi';
import publishApi from './publishApi';

// 统一导出所有API - 使用真实API替换Mock数据
export default {
  // 用户相关API
  user: {
    login: userApi.login.bind(userApi),
    wxLogin: userApi.wxLogin.bind(userApi),
    register: userApi.register.bind(userApi),
    sendSmsCode: userApi.sendSmsCode.bind(userApi),
    getUserInfo: userApi.getUserInfo.bind(userApi),
    updateUserInfo: userApi.updateUserInfo.bind(userApi),
    changePassword: userApi.changePassword.bind(userApi),
    logout: userApi.logout.bind(userApi),
    isLoggedIn: userApi.isLoggedIn.bind(userApi),
    getCurrentUser: userApi.getCurrentUser.bind(userApi),
    getToken: userApi.getToken.bind(userApi)
  },
  
  // 新闻相关API
  news: {
    getCategories: contentApi.getNewsCategories.bind(contentApi),
    getList: contentApi.getNewsList.bind(contentApi),
    getDetail: contentApi.getNewsDetail.bind(contentApi)
  },
  
  // 信息相关API
  info: {
    getCategories: contentApi.getInfoCategories.bind(contentApi),
    getTopped: contentApi.getToppedInfo.bind(contentApi),
    getAll: contentApi.getInfoList.bind(contentApi),
    getDetail: contentApi.getInfoDetail.bind(contentApi),
    publish: contentApi.publishInfo.bind(contentApi)
  },
  
  // 商家相关API
  business: {
    getCategories: contentApi.getBusinessCategories.bind(contentApi),
    getList: contentApi.getBusinessList.bind(contentApi),
    getDetail: contentApi.getBusinessDetail.bind(contentApi)
  },
  
  // 拼车相关API
  carpool: {
    getList: carpoolApi.getCarpoolList.bind(carpoolApi),
    getDetail: carpoolApi.getCarpoolDetail.bind(carpoolApi),
    publish: carpoolApi.publishCarpool.bind(carpoolApi),
    update: carpoolApi.updateCarpool.bind(carpoolApi),
    delete: carpoolApi.deleteCarpool.bind(carpoolApi),
    getMy: carpoolApi.getMyCarpoolList.bind(carpoolApi),
    apply: carpoolApi.applyCarpool.bind(carpoolApi),
    handleApplication: carpoolApi.handleApplication.bind(carpoolApi),
    getApplications: carpoolApi.getApplications.bind(carpoolApi),
    top: carpoolApi.topCarpool.bind(carpoolApi),
    refresh: carpoolApi.refreshCarpool.bind(carpoolApi),
    getHotRoutes: carpoolApi.getHotRoutes.bind(carpoolApi),
    search: carpoolApi.searchCarpool.bind(carpoolApi),
    getStats: carpoolApi.getCarpoolStats.bind(carpoolApi),
    report: carpoolApi.reportCarpool.bind(carpoolApi)
  },
  
  // 通用内容API
  content: {
    uploadImage: contentApi.uploadImage.bind(contentApi),
    search: contentApi.searchContent.bind(contentApi)
  },
  
  // 首页相关API - 与后台管理系统互通
  home: {
    getBanners: homeApi.getBanners.bind(homeApi),
    getHomeConfig: homeApi.getHomeConfig.bind(homeApi),
    getHomeStats: homeApi.getHomeStats.bind(homeApi),
    getServiceCategories: homeApi.getServiceCategories.bind(homeApi),
    getMerchantRecommend: homeApi.getMerchantRecommend.bind(homeApi),
    getCityNews: homeApi.getCityNews.bind(homeApi),
    getFeatureConfig: homeApi.getFeatureConfig.bind(homeApi),
    recordPageView: homeApi.recordPageView.bind(homeApi),
    getAdBanner: homeApi.getAdBanner.bind(homeApi)
  },
  
  // 发布相关API - 与后台管理系统互通
  publish: {
    publishPost: publishApi.publishPost.bind(publishApi),
    getPosts: publishApi.getPosts.bind(publishApi),
    getPost: publishApi.getPost.bind(publishApi),
    updatePost: publishApi.updatePost.bind(publishApi),
    deletePost: publishApi.deletePost.bind(publishApi),
    topPost: publishApi.topPost.bind(publishApi),
    refreshPost: publishApi.refreshPost.bind(publishApi),
    addRedPacket: publishApi.addRedPacket.bind(publishApi),
    claimRedPacket: publishApi.claimRedPacket.bind(publishApi),
    getConfigs: publishApi.getConfigs.bind(publishApi),
    getPublishConfigs: publishApi.getPublishConfigs.bind(publishApi),
    uploadImage: publishApi.uploadImage.bind(publishApi),
    getCategories: publishApi.getCategories.bind(publishApi),
    getHomePosts: publishApi.getHomePosts.bind(publishApi),
    getCategoryPosts: publishApi.getCategoryPosts.bind(publishApi),
    getTopPosts: publishApi.getTopPosts.bind(publishApi),
    searchPosts: publishApi.searchPosts.bind(publishApi),
    getMyPosts: publishApi.getMyPosts.bind(publishApi),
    getPostStats: publishApi.getPostStats.bind(publishApi)
  }
};

// 兼容性导出 - 保持与原有代码的兼容性
export {
  userApi,
  contentApi,
  carpoolApi,
  homeApi,
  publishApi
};
