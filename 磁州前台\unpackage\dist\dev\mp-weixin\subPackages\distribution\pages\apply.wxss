/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 移除阿里妈妈字体引用，改用系统默认字体 */
/* 移除原有阿里妈妈字体定义
$uni-font-family: 'AlimamaShuHeiTi';
$uni-font-family-text: 'AlimamaShuHeiTi', -apple-system, BlinkMacSystemFont, 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;
*/
body, html, #app, .index-container {
  font-family: "AlimamaShuHeiTi", -apple-system, BlinkMacSystemFont, "Helvetica Neue", "PingFang SC", "Microsoft YaHei", sans-serif;
}
.apply-container {
  min-height: 100vh;
  background-color: #F5F7FA;
  padding-bottom: 30rpx;
}

/* 导航栏样式 */
.navbar {
  position: -webkit-sticky;
  position: sticky;
  top: 0;
  left: 0;
  right: 0;
  background: linear-gradient(135deg, #A764CA, #6B0FBE);
  color: #fff;
  padding: 88rpx 32rpx 30rpx;
  display: flex;
  align-items: center;
  z-index: 100;
  box-shadow: 0 4rpx 20rpx rgba(107, 15, 190, 0.15);
}
.navbar-back {
  width: 72rpx;
  height: 72rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.back-icon {
  width: 24rpx;
  height: 24rpx;
  border-left: 4rpx solid #fff;
  border-bottom: 4rpx solid #fff;
  transform: rotate(45deg);
}
.navbar-title {
  flex: 1;
  text-align: center;
  font-size: 36rpx;
  font-weight: 600;
  letter-spacing: 1rpx;
}
.navbar-right {
  width: 72rpx;
  height: 72rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.help-icon {
  width: 48rpx;
  height: 48rpx;
  border-radius: 24rpx;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  font-weight: bold;
}

/* 卡片通用样式 */
.conditions-card,
.form-card,
.tips-section {
  margin: 30rpx;
  background: #FFFFFF;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}
.card-header {
  margin-bottom: 30rpx;
}
.card-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

/* 条件列表样式 */
.conditions-list {
  margin-bottom: 20rpx;
}
.condition-item {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}
.condition-item:last-child {
  border-bottom: none;
}
.condition-icon {
  width: 40rpx;
  height: 40rpx;
  border-radius: 20rpx;
  border: 2rpx solid #ddd;
  margin-right: 20rpx;
  position: relative;
}
.condition-icon.satisfied {
  border-color: #6B0FBE;
  background-color: #6B0FBE;
}
.condition-icon.satisfied::after {
  content: "";
  position: absolute;
  width: 20rpx;
  height: 10rpx;
  border-left: 4rpx solid #fff;
  border-bottom: 4rpx solid #fff;
  transform: rotate(-45deg);
  top: 10rpx;
  left: 8rpx;
}
.condition-content {
  flex: 1;
}
.condition-title {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 8rpx;
}
.condition-desc {
  font-size: 24rpx;
  color: #999;
}
.condition-status {
  font-size: 26rpx;
  color: #6B0FBE;
}
.action-btn {
  background: #6B0FBE;
  color: #fff;
  font-size: 24rpx;
  padding: 6rpx 20rpx;
  border-radius: 30rpx;
  line-height: 1.5;
  margin: 0;
}

/* 表单样式 */
.form-content {
  margin-bottom: 20rpx;
}
.form-item {
  margin-bottom: 30rpx;
  position: relative;
}
.form-label {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 16rpx;
  display: block;
}
.form-input {
  width: 100%;
  height: 80rpx;
  background: #F5F7FA;
  border-radius: 10rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  color: #333;
  box-sizing: border-box;
}
.form-textarea {
  width: 100%;
  height: 200rpx;
  background: #F5F7FA;
  border-radius: 10rpx;
  padding: 20rpx;
  font-size: 28rpx;
  color: #333;
  box-sizing: border-box;
}
.textarea-counter {
  position: absolute;
  right: 20rpx;
  bottom: 20rpx;
  font-size: 24rpx;
  color: #999;
}

/* 协议同意 */
.agreement-section {
  margin: 30rpx;
  display: flex;
  align-items: center;
}
.agreement-checkbox {
  display: flex;
  align-items: center;
}
.checkbox {
  width: 36rpx;
  height: 36rpx;
  border-radius: 6rpx;
  border: 2rpx solid #ddd;
  margin-right: 16rpx;
  position: relative;
}
.checkbox.checked {
  background: #6B0FBE;
  border-color: #6B0FBE;
}
.checkbox.checked::after {
  content: "";
  position: absolute;
  width: 20rpx;
  height: 10rpx;
  border-left: 4rpx solid #fff;
  border-bottom: 4rpx solid #fff;
  transform: rotate(-45deg);
  top: 8rpx;
  left: 6rpx;
}
.agreement-text {
  font-size: 26rpx;
  color: #666;
}
.agreement-link {
  font-size: 26rpx;
  color: #6B0FBE;
}

/* 提交按钮 */
.submit-section {
  margin: 40rpx 30rpx;
}
.submit-btn {
  background: linear-gradient(135deg, #A764CA, #6B0FBE);
  color: #fff;
  border: none;
  border-radius: 40rpx;
  font-size: 32rpx;
  padding: 20rpx 0;
  line-height: 1.5;
  width: 100%;
}
.submit-btn.disabled {
  background: #cccccc;
  color: #ffffff;
}

/* 申请说明 */
.tips-header {
  margin-bottom: 20rpx;
}
.tips-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}
.tips-content {
  margin-bottom: 20rpx;
}
.tips-text {
  font-size: 26rpx;
  color: #666;
  line-height: 1.8;
  display: block;
}