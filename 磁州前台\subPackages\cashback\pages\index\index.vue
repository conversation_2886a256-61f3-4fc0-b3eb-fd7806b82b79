<template>
  <view class="cashback-container">
    <!-- 自定义导航栏 -->
    <custom-navbar title="返利商城" :show-close="true"></custom-navbar>
    
    <!-- 内容区域 -->
    <view class="content-container">
      <!-- 搜索框 -->
      <view class="search-section">
        <view class="search-bar" @tap="navigateToSearch">
          <svg class="search-icon" viewBox="0 0 24 24" width="20" height="20">
            <path fill="#999999" d="M9.5,3A6.5,6.5 0 0,1 16,9.5C16,11.11 15.41,12.59 14.44,13.73L14.71,14H15.5L20.5,19L19,20.5L14,15.5V14.71L13.73,14.44C12.59,15.41 11.11,16 9.5,16A6.5,6.5 0 0,1 3,9.5A6.5,6.5 0 0,1 9.5,3M9.5,5C7,5 5,7 5,9.5C5,12 7,14 9.5,14C12,14 14,12 14,9.5C14,7 12,5 9.5,5Z" />
          </svg>
          <text class="search-placeholder">粘贴商品链接，立即查询返利</text>
      </view>
    </view>
    
      <!-- 商品链接粘贴模块 -->
      <view class="paste-link-section">
        <view class="paste-link-info">
          <text class="paste-link-title">复制商品链接返<text class="highlight">购物平均返5%</text></text>
          <text class="paste-link-tutorial" @tap="showTutorial">教程</text>
          </view>
        <view class="paste-link-input">
          <text class="input-placeholder">粘贴商品链接或口令</text>
          <view class="example-link">
            <text class="example-text">例：</text>
            <text class="example-url">https://m.tb.cn/h.5CEEn2LD3uJBsC0?tk=aUfiWotnuJv</text>
          </view>
        </view>
        <view class="platform-icons">
          <image class="platform-icon" src="/static/images/cashback/platform-taobao.png" mode="aspectFit"></image>
          <image class="platform-icon" src="/static/images/cashback/platform-jd.png" mode="aspectFit"></image>
          <image class="platform-icon" src="/static/images/cashback/platform-pdd.png" mode="aspectFit"></image>
          <image class="platform-icon" src="/static/images/cashback/platform-douyin.png" mode="aspectFit"></image>
        </view>
        <button class="paste-button" @tap="pasteLink">粘贴</button>
      </view>
      
      <!-- 平台导航 -->
      <view class="platforms-section">
        <view class="section-header">
          <text class="section-title">热门平台</text>
          <view class="section-more" @tap="navigateToPlatforms">
            <text>全部</text>
            <svg class="arrow-icon" viewBox="0 0 24 24" width="16" height="16">
              <path fill="#999999" d="M8.59,16.58L13.17,12L8.59,7.41L10,6L16,12L10,18L8.59,16.58Z" />
            </svg>
          </view>
        </view>
        <scroll-view scroll-x class="platforms-scroll" show-scrollbar="false">
          <view class="platforms-container">
            <view class="platform-item" v-for="(platform, index) in platforms" :key="index" @tap="navigateToPlatformDetail(platform)">
              <image class="platform-icon" :src="platform.icon" mode="aspectFill"></image>
              <text class="platform-name">{{ platform.name }}</text>
            </view>
          </view>
        </scroll-view>
      </view>
      
      <!-- 生活返利 -->
      <view class="life-cashback-section">
        <view class="section-header">
          <text class="section-title">生活返现</text>
          <view class="section-more" @tap="navigateToLifeCashback">
            <text>全部特权</text>
            <svg class="arrow-icon" viewBox="0 0 24 24" width="16" height="16">
              <path fill="#999999" d="M8.59,16.58L13.17,12L8.59,7.41L10,6L16,12L10,18L8.59,16.58Z" />
            </svg>
          </view>
        </view>
        <view class="life-cashback-grid">
          <!-- 领外卖红包 -->
          <view class="life-cashback-item" @tap="navigateToLifeService('takeout')">
            <view class="item-icon-container" style="background-color: #FFE8E0;">
              <svg class="item-icon" viewBox="0 0 24 24" width="24" height="24">
                <path fill="#FF6B6B" d="M15.5,21L14,8H16.23L15.1,3.46L16.84,3L18.09,8H22L20.5,21H15.5M5,11H10A3,3 0 0,1 13,14H2A3,3 0 0,1 5,11M13,18A3,3 0 0,1 10,21H5A3,3 0 0,1 2,18H13M3,15H8L9.5,16.5L11,15H12A1,1 0 0,1 13,16A1,1 0 0,1 12,17H3A1,1 0 0,1 2,16A1,1 0 0,1 3,15Z" />
              </svg>
              </view>
            <view class="item-content">
              <text class="item-title">领外卖红包</text>
              <text class="item-desc">最高返2%-5%</text>
                </view>
                </view>
          
          <!-- 领打车红包 -->
          <view class="life-cashback-item" @tap="navigateToLifeService('taxi')">
            <view class="item-icon-container" style="background-color: #FFF2D6;">
              <svg class="item-icon" viewBox="0 0 24 24" width="24" height="24">
                <path fill="#FFA726" d="M5,11L6.5,6.5H17.5L19,11M17.5,16A1.5,1.5 0 0,1 16,14.5A1.5,1.5 0 0,1 17.5,13A1.5,1.5 0 0,1 19,14.5A1.5,1.5 0 0,1 17.5,16M6.5,16A1.5,1.5 0 0,1 5,14.5A1.5,1.5 0 0,1 6.5,13A1.5,1.5 0 0,1 8,14.5A1.5,1.5 0 0,1 6.5,16M18.92,6C18.72,5.42 18.16,5 17.5,5H6.5C5.84,5 5.28,5.42 5.08,6L3,12V20A1,1 0 0,0 4,21H5A1,1 0 0,0 6,20V19H18V20A1,1 0 0,0 19,21H20A1,1 0 0,0 21,20V12L18.92,6Z" />
              </svg>
              </view>
            <view class="item-content">
              <text class="item-title">领打车红包</text>
              <text class="item-desc">最高返2.4%-5%</text>
        </view>
      </view>
      
          <!-- 淘宝 -->
          <view class="life-cashback-item" @tap="navigateToLifeService('movie')">
            <view class="item-icon-container" style="background-color: #FFE0EC;">
              <svg class="item-icon" viewBox="0 0 24 24" width="24" height="24">
                <path fill="#E91E63" d="M18,9H16V7H18M18,13H16V11H18M18,17H16V15H18M8,9H6V7H8M8,13H6V11H8M8,17H6V15H8M18,3V5H16V3H8V5H6V3H4V21H6V19H8V21H16V19H18V21H20V3H18Z" />
              </svg>
          </view>
            <view class="item-content">
              <text class="item-title">电影票8折起</text>
              <text class="item-desc">返10%</text>
        </view>
            </view>
          
          <!-- 京东 -->
          <view class="life-cashback-item" @tap="navigateToLifeService('express')">
            <view class="item-icon-container" style="background-color: #E3F1FF;">
              <svg class="item-icon" viewBox="0 0 24 24" width="24" height="24">
                <path fill="#2196F3" d="M3,14H5V20H19V14H21V21A1,1 0 0,1 20,22H4A1,1 0 0,1 3,21V14M17,4H7V2H17V4M17.5,5L12,10.5L6.5,5H17.5M20,6.4L17.9,8.5L15.5,6.1L16.9,4.7L20,7.8V6.4M5.93,4.7L7.33,6.1L4.93,8.5L2.83,6.4V7.8L5.93,4.7Z" />
              </svg>
          </view>
            <view class="item-content">
              <text class="item-title">寄快递返现</text>
              <text class="item-desc">返15%</text>
        </view>
      </view>
      
          <!-- 优惠券 -->
          <view class="life-cashback-item special-item" @tap="navigateToLifeService('coupon')">
            <view class="special-content">
              <text class="special-title">淘宝-搜了么</text>
              <text class="special-desc">免费奶茶喝到爽</text>
              <text class="special-subdesc">下单再返3元</text>
          </view>
            <view class="special-price">
              <text class="price-value">15元</text>
              <text class="price-tag">券</text>
        </view>
            <image class="special-image" src="/static/images/cashback/coupon-mascot.png" mode="aspectFit"></image>
                  </view>
          
          <!-- 会员充值 -->
          <view class="life-cashback-item" @tap="navigateToLifeService('vip')">
            <view class="item-icon-container" style="background-color: #E8F5E9;">
              <svg class="item-icon" viewBox="0 0 24 24" width="24" height="24">
                <path fill="#4CAF50" d="M12,8H4A2,2 0 0,0 2,10V14A2,2 0 0,0 4,16H5V20A1,1 0 0,0 6,21H8A1,1 0 0,0 9,20V16H12L17,20V4L12,8M21.5,12C21.5,13.71 20.54,15.26 19,16V8C20.53,8.75 21.5,10.3 21.5,12Z" />
              </svg>
                  </view>
            <view class="item-content">
              <text class="item-title">会员充值</text>
              <text class="item-desc">3.6元起</text>
                </view>
              </view>
            </view>
          </view>
      
      <!-- 红包区域 -->
      <view class="coupon-section">
        <image class="coupon-banner" src="/static/images/cashback/coupon-banner.png" mode="widthFix"></image>
      </view>
      
      <!-- 推荐商品 -->
      <view class="products-section">
        <view class="section-header">
          <text class="section-title">精选好物</text>
          <view class="section-more" @tap="navigateToCategory">
            <text>更多</text>
            <svg class="arrow-icon" viewBox="0 0 24 24" width="16" height="16">
              <path fill="#999999" d="M8.59,16.58L13.17,12L8.59,7.41L10,6L16,12L10,18L8.59,16.58Z" />
            </svg>
          </view>
        </view>
        <view class="products-grid">
          <product-card
            v-for="(product, index) in products"
            :key="index"
            :product="product"
            @tap="navigateToDetail(product)"
          ></product-card>
                </view>
      </view>
    </view>
  </view>
</template>

<script>
import CustomNavbar from '../../components/CustomNavbar.vue';
import ProductCard from '../../components/ProductCard.vue';

export default {
  components: {
    CustomNavbar,
    ProductCard
  },
  data() {
    return {
      platforms: [
        { id: 1, name: '淘宝', icon: '/static/images/cashback/platform-taobao.png' },
        { id: 2, name: '京东', icon: '/static/images/cashback/platform-jd.png' },
        { id: 3, name: '拼多多', icon: '/static/images/cashback/platform-pdd.png' },
        { id: 4, name: '唯品会', icon: '/static/images/cashback/platform-vip.png' },
        { id: 5, name: '抖音', icon: '/static/images/cashback/platform-douyin.png' },
        { id: 6, name: '天猫', icon: '/static/images/cashback/platform-tmall.png' },
        { id: 7, name: '苏宁', icon: '/static/images/cashback/platform-suning.png' },
        { id: 8, name: '小红书', icon: '/static/images/cashback/platform-xiaohongshu.png' }
      ],
      products: [
        {
          id: 1,
          title: 'Apple iPhone 15 Pro Max (A2850) 256GB 原色钛金属',
          image: '/static/images/cashback/product-1.png',
          price: 9999.00,
          cashback: 300.00,
          platform: '京东'
        },
        {
          id: 2,
          title: 'HUAWEI Mate 60 Pro 12+512GB 雅黑色',
          image: '/static/images/cashback/product-2.png',
          price: 6999.00,
          cashback: 200.00,
          platform: '华为商城'
        },
        {
          id: 3,
          title: '小米14 Ultra 16+1T 黑色 徕卡光学',
          image: '/static/images/cashback/product-3.png',
          price: 7999.00,
          cashback: 240.00,
          platform: '小米商城'
        },
        {
          id: 4,
          title: 'OPPO Find X7 Ultra 16+512GB 棕色',
          image: '/static/images/cashback/product-4.png',
          price: 6999.00,
          cashback: 210.00,
          platform: '京东'
        }
      ],
      clipboardContent: '',
      hasDetectedLink: false,
      detectedPlatform: ''
    };
  },
  onLoad() {
    // 设置页面不显示系统导航栏
    uni.setNavigationBarColor({
      frontColor: '#ffffff',
      backgroundColor: '#9C27B0'
    });
    
    // #ifdef APP-PLUS
    // 获取剪贴板权限并禁用提示
    this.requestClipboardPermission();
    // #endif
    
    // 检测剪贴板是否有商品链接
      setTimeout(() => {
      this.checkClipboard();
    }, 500);
  },
  onShow() {
    // 每次页面显示时检测剪贴板
    setTimeout(() => {
      this.checkClipboard();
    }, 500);
  },
  methods: {
    navigateToSearch() {
      uni.navigateTo({
        url: '/subPackages/cashback/pages/search/index'
      });
    },
    navigateToPlatforms() {
      uni.navigateTo({
        url: '/subPackages/cashback/pages/platforms/index'
      });
    },
    navigateToPlatformDetail(platform) {
      uni.navigateTo({
        url: `/subPackages/cashback/pages/platform-detail/index?id=${platform.id}&name=${platform.name}`
      });
    },
    navigateToCategory() {
      uni.navigateTo({
        url: '/subPackages/cashback/pages/category/index'
      });
    },
    navigateToDetail(product) {
      uni.navigateTo({
        url: `/subPackages/cashback/pages/detail/index?id=${product.id}`
      });
    },
    // 请求剪贴板权限并禁用提示
    requestClipboardPermission() {
      // #ifdef APP-PLUS
      try {
        // 尝试使用plus API禁用剪贴板提示
        if (plus && plus.pasteboard) {
          // 先尝试读取一次剪贴板内容，获取权限
          plus.pasteboard.getClipboard();
          
          // 尝试禁用提示
          if (plus.os.name.toLowerCase() === 'android') {
            // Android平台
            const context = plus.android.importClass('android.content.Context');
            const activity = plus.android.runtimeMainActivity();
            const clipboard = activity.getSystemService(context.CLIPBOARD_SERVICE);
            
            if (clipboard) {
              // 尝试清除剪贴板监听
              clipboard.removePrimaryClipChangedListener && 
              clipboard.removePrimaryClipChangedListener();
            }
          } else if (plus.os.name.toLowerCase() === 'ios') {
            // iOS平台
            // iOS可能需要特殊处理
          }
        }
      } catch (e) {
        console.error('禁用剪贴板提示失败:', e);
      }
      // #endif
    },
    // 检测剪贴板内容
    checkClipboard() {
      // #ifdef APP-PLUS
      try {
        if (plus && plus.pasteboard) {
          // 使用plus API直接获取剪贴板内容，避免系统提示
          const content = plus.pasteboard.getClipboard();
          if (content && this.isProductLink(content)) {
            this.clipboardContent = content;
            this.hasDetectedLink = true;
            this.detectedPlatform = this.detectPlatform(content);
            
            // 显示检测到链接的提示
            uni.showToast({
              title: `检测到${this.detectedPlatform}商品链接`,
              icon: 'none',
              duration: 2000
            });
          }
          return;
        }
      } catch (e) {
        console.error('获取剪贴板内容失败:', e);
      }
      // #endif
      
      // 兼容其他平台或fallback
      uni.getClipboardData({
        success: (res) => {
          if (res.data && this.isProductLink(res.data)) {
            this.clipboardContent = res.data;
            this.hasDetectedLink = true;
            this.detectedPlatform = this.detectPlatform(res.data);
            
            // 显示检测到链接的提示
            uni.showToast({
              title: `检测到${this.detectedPlatform}商品链接`,
              icon: 'none',
              duration: 2000
            });
          }
        }
      });
    },
    isProductLink(link) {
      // 简单判断是否包含常见电商平台域名或短链接
      const patterns = [
        /taobao\.com/i, /tmall\.com/i, /jd\.com/i, /pinduoduo\.com/i, 
        /yangkeduo\.com/i, /vip\.com/i, /suning\.com/i, /kaola\.com/i,
        /tb\.cn/i, /m\.tb\.cn/i, /t\.cn/i, /dwz\.cn/i, /douyin\.com/i
      ];
      
      return patterns.some(pattern => pattern.test(link));
    },
    detectPlatform(link) {
      if (/taobao\.com/i.test(link) || /tb\.cn/i.test(link) || /m\.tb\.cn/i.test(link)) {
        return '淘宝';
      } else if (/tmall\.com/i.test(link)) {
        return '天猫';
      } else if (/jd\.com/i.test(link)) {
        return '京东';
      } else if (/pinduoduo\.com/i.test(link) || /yangkeduo\.com/i.test(link)) {
        return '拼多多';
      } else if (/douyin\.com/i.test(link)) {
        return '抖音';
      } else if (/vip\.com/i.test(link)) {
        return '唯品会';
      } else if (/suning\.com/i.test(link)) {
        return '苏宁';
      } else if (/kaola\.com/i.test(link)) {
        return '考拉';
      } else {
        return '电商';
      }
    },
    pasteLink() {
      if (this.hasDetectedLink && this.clipboardContent) {
        // 如果已经检测到链接，直接处理
        this.processLink(this.clipboardContent);
      } else {
        // #ifdef APP-PLUS
        try {
          if (plus && plus.pasteboard) {
            // 使用plus API直接获取剪贴板内容
            const content = plus.pasteboard.getClipboard();
            if (content) {
              this.processLink(content);
            } else {
              uni.showToast({
                title: '剪贴板为空',
                icon: 'none'
              });
            }
            return;
          }
        } catch (e) {
          console.error('获取剪贴板内容失败:', e);
        }
        // #endif
        
        // 兼容其他平台或fallback
        uni.getClipboardData({
          success: (res) => {
            if (res.data) {
              // 处理粘贴的链接
              this.processLink(res.data);
            } else {
              uni.showToast({
                title: '剪贴板为空',
                icon: 'none'
              });
            }
          },
          fail: () => {
            uni.showToast({
              title: '获取剪贴板失败',
              icon: 'none'
            });
          }
        });
      }
    },
    processLink(link) {
      // 这里处理商品链接，实际项目中可能需要发送到后端进行处理
      uni.showLoading({
        title: '解析链接中...'
      });
      
      // 模拟解析过程
      setTimeout(() => {
        uni.hideLoading();
        
        // 处理完成后重置状态
        this.clipboardContent = '';
        this.hasDetectedLink = false;
        
        uni.navigateTo({
          url: `/subPackages/cashback/pages/product-detail/index?link=${encodeURIComponent(link)}`
        });
      }, 1000);
    },
    showTutorial() {
      uni.showToast({
        title: '使用教程功能正在开发中',
        icon: 'none',
        duration: 2000
      });
    },
    navigateToLifeCashback() {
      uni.navigateTo({
        url: '/subPackages/cashback/pages/life-cashback/index'
      });
    },
    
    navigateToLifeService(type) {
      uni.navigateTo({
        url: `/subPackages/cashback/pages/life-service/index?type=${type}`
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.cashback-container {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.content-container {
  padding-top: calc(var(--status-bar-height) + 44px);
  padding-bottom: 20px;
}

.search-section {
  padding: 16px;
  background: linear-gradient(135deg, #AB47BC 0%, #9C27B0 100%);
  border-bottom-left-radius: 20px;
  border-bottom-right-radius: 20px;
  padding-top: 24px;
}

.search-bar {
  display: flex;
  align-items: center;
  background-color: #FFFFFF;
  border-radius: 20px;
  padding: 10px 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  
  .search-icon {
    margin-right: 8px;
}

.search-placeholder {
  color: #999999;
    font-size: 14px;
  }
}

/* 商品链接粘贴模块 */
.paste-link-section {
  margin: 16px;
  padding: 16px;
  background-color: #FFFFFF;
  border-radius: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  position: relative;
  
  &::after {
    content: '';
    display: v-bind('hasDetectedLink ? "block" : "none"');
    position: absolute;
    top: 0;
    right: 0;
    width: 60px;
    height: 60px;
    background-color: #9C27B0;
    border-radius: 0 16px 0 60px;
    z-index: 1;
  }
  
  &::before {
    content: '已检测';
    display: v-bind('hasDetectedLink ? "block" : "none"');
    position: absolute;
    top: 12px;
    right: 8px;
    color: #FFFFFF;
    font-size: 12px;
    font-weight: 500;
    transform: rotate(45deg);
    z-index: 2;
  }
}

.paste-link-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;

  .paste-link-title {
    font-size: 14px;
  color: #333333;
    
    .highlight {
      color: #9C27B0;
      font-weight: 500;
    }
  }
  
  .paste-link-tutorial {
    font-size: 14px;
    color: #9C27B0;
    background-color: rgba(156, 39, 176, 0.1);
    padding: 2px 8px;
    border-radius: 12px;
  }
}

.paste-link-input {
  background-color: #F5F5F5;
  border-radius: 12px;
  padding: 12px;
  margin-bottom: 12px;
  
  .input-placeholder {
    font-size: 14px;
    color: #999999;
    display: block;
    margin-bottom: 4px;
  }
  
  .example-link {
  display: flex;
    
    .example-text {
      font-size: 12px;
      color: #999999;
      margin-right: 4px;
    }
    
    .example-url {
      font-size: 12px;
      color: #999999;
      flex: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
    }
  }
}

.platform-icons {
  display: flex;
  margin-bottom: 12px;
  margin-top: 12px;
  justify-content: center;
  
  .platform-icon {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    margin-right: 8px;
    margin-left: 8px;
  }
}

.paste-button {
  width: 100%;
  height: 44px;
  background-color: #9C27B0;
  color: #FFFFFF;
  font-size: 16px;
  font-weight: 500;
  border-radius: 22px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
  position: relative;
  overflow: hidden;
  
  &::after {
    content: '';
    display: v-bind('hasDetectedLink ? "block" : "none"');
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, rgba(255,255,255,0.1), rgba(255,255,255,0.2), rgba(255,255,255,0.1));
    animation: shimmer 1.5s infinite;
  }
  
  @keyframes shimmer {
    0% {
      transform: translateX(-100%);
    }
    100% {
      transform: translateX(100%);
    }
  }
}

.platforms-section {
  margin: 16px;
  background-color: #FFFFFF;
  border-radius: 16px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  
  .section-title {
    font-size: 18px;
    font-weight: 600;
    color: #333333;
  }
  
  .section-more {
  display: flex;
  align-items: center;
    color: #999999;
    font-size: 14px;
  }
}

.platforms-scroll {
  width: 100%;
  white-space: nowrap;
}

.platforms-container {
  display: inline-flex;
  padding-bottom: 8px;
}

.platform-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-right: 24px;
  
  &:last-child {
    margin-right: 0;
  }
  
  .platform-icon {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    margin-bottom: 8px;
  }
  
  .platform-name {
    font-size: 12px;
    color: #333333;
  }
}

.coupon-section {
  margin: 16px;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);

  .coupon-banner {
  width: 100%;
    border-radius: 16px;
  }
}

.products-section {
  margin: 16px;
  background-color: #FFFFFF;
  border-radius: 16px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.products-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
}

/* 生活返利模块 */
.life-cashback-section {
  margin: 16px;
  background-color: #FFFFFF;
  border-radius: 16px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.life-cashback-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  grid-template-rows: auto auto;
  gap: 12px;
}

.life-cashback-item {
  background-color: #FFFFFF;
  border-radius: 12px;
  padding: 12px;
  display: flex;
  flex-direction: column;
  position: relative;
  border: 1px solid #F0F0F0;
  
  .item-icon-container {
    width: 40px;
    height: 40px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 8px;
  }
  
  .item-content {
  display: flex;
    flex-direction: column;
    
    .item-title {
      font-size: 14px;
      color: #333333;
      margin-bottom: 4px;
    }
    
    .item-desc {
      font-size: 12px;
      color: #9C27B0;
    }
  }
  
  &.special-item {
    grid-column: span 2;
    background-color: #F5F0FF;
    padding: 12px 16px;
    flex-direction: row;
    align-items: center;
    position: relative;
    overflow: hidden;
    
    .special-content {
  flex: 1;
      
      .special-title {
        font-size: 14px;
        color: #333333;
        margin-bottom: 4px;
      }
      
      .special-desc {
        font-size: 14px;
        color: #9C27B0;
  font-weight: 500;
        margin-bottom: 2px;
      }
      
      .special-subdesc {
        font-size: 12px;
        color: #666666;
      }
    }
    
    .special-price {
      background-color: #FF5252;
      border-radius: 20px;
      padding: 4px 8px;
  display: flex;
  align-items: center;
      margin-right: 12px;
      
      .price-value {
        font-size: 16px;
        font-weight: 600;
        color: #FFFFFF;
      }
      
      .price-tag {
        font-size: 12px;
        color: #FFFFFF;
        background-color: rgba(255, 255, 255, 0.3);
        border-radius: 10px;
        padding: 0 4px;
        margin-left: 2px;
      }
    }
    
    .special-image {
      position: absolute;
      right: 0;
      bottom: 0;
      width: 60px;
      height: 60px;
    }
  }
}
</style> 