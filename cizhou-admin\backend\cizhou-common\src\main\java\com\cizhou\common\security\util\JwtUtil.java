package com.cizhou.common.security.util;

import io.jsonwebtoken.*;
import io.jsonwebtoken.security.Keys;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.crypto.SecretKey;
import java.util.Date;
import java.util.Map;

/**
 * JWT工具类
 *
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Slf4j
@Component
public class JwtUtil {

    /**
     * JWT密钥
     */
    @Value("${jwt.secret:cizhou-admin-secret-key-2024-very-long-secret}")
    private String secret;

    /**
     * JWT过期时间（毫秒）
     */
    @Value("${jwt.expiration:86400000}")
    private Long expiration;

    /**
     * 刷新Token过期时间（毫秒）
     */
    @Value("${jwt.refresh-expiration:604800000}")
    private Long refreshExpiration;

    /**
     * JWT签发者
     */
    @Value("${jwt.issuer:cizhou-admin}")
    private String issuer;

    /**
     * 获取密钥
     */
    private SecretKey getSecretKey() {
        return Keys.hmacShaKeyFor(secret.getBytes());
    }

    /**
     * 生成访问Token
     */
    public String generateAccessToken(String subject, Map<String, Object> claims) {
        Date now = new Date();
        Date expiryDate = new Date(now.getTime() + expiration);

        return Jwts.builder()
                .setSubject(subject)
                .setIssuer(issuer)
                .setIssuedAt(now)
                .setExpiration(expiryDate)
                .addClaims(claims)
                .signWith(getSecretKey(), SignatureAlgorithm.HS512)
                .compact();
    }

    /**
     * 生成刷新Token
     */
    public String generateRefreshToken(String subject) {
        Date now = new Date();
        Date expiryDate = new Date(now.getTime() + refreshExpiration);

        return Jwts.builder()
                .setSubject(subject)
                .setIssuer(issuer)
                .setIssuedAt(now)
                .setExpiration(expiryDate)
                .claim("type", "refresh")
                .signWith(getSecretKey(), SignatureAlgorithm.HS512)
                .compact();
    }

    /**
     * 从Token中获取用户名
     */
    public String getUsernameFromToken(String token) {
        try {
            Claims claims = getClaimsFromToken(token);
            return claims.getSubject();
        } catch (Exception e) {
            log.error("从Token中获取用户名失败: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 从Token中获取Claims
     */
    public Claims getClaimsFromToken(String token) {
        return Jwts.parserBuilder()
                .setSigningKey(getSecretKey())
                .build()
                .parseClaimsJws(token)
                .getBody();
    }

    /**
     * 从Token中获取过期时间
     */
    public Date getExpirationDateFromToken(String token) {
        try {
            Claims claims = getClaimsFromToken(token);
            return claims.getExpiration();
        } catch (Exception e) {
            log.error("从Token中获取过期时间失败: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 判断Token是否过期
     */
    public boolean isTokenExpired(String token) {
        try {
            Date expiration = getExpirationDateFromToken(token);
            return expiration != null && expiration.before(new Date());
        } catch (Exception e) {
            log.error("判断Token是否过期失败: {}", e.getMessage());
            return true;
        }
    }

    /**
     * 验证Token
     */
    public boolean validateToken(String token) {
        try {
            Jwts.parserBuilder()
                    .setSigningKey(getSecretKey())
                    .build()
                    .parseClaimsJws(token);
            return true;
        } catch (SecurityException e) {
            log.error("JWT签名无效: {}", e.getMessage());
        } catch (MalformedJwtException e) {
            log.error("JWT格式错误: {}", e.getMessage());
        } catch (ExpiredJwtException e) {
            log.error("JWT已过期: {}", e.getMessage());
        } catch (UnsupportedJwtException e) {
            log.error("不支持的JWT: {}", e.getMessage());
        } catch (IllegalArgumentException e) {
            log.error("JWT参数为空: {}", e.getMessage());
        } catch (Exception e) {
            log.error("JWT验证失败: {}", e.getMessage());
        }
        return false;
    }

    /**
     * 验证刷新Token
     */
    public boolean validateRefreshToken(String token) {
        try {
            Claims claims = getClaimsFromToken(token);
            String type = claims.get("type", String.class);
            return "refresh".equals(type) && !isTokenExpired(token);
        } catch (Exception e) {
            log.error("验证刷新Token失败: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 获取Token剩余有效时间（秒）
     */
    public long getTokenRemainingTime(String token) {
        try {
            Date expiration = getExpirationDateFromToken(token);
            if (expiration != null) {
                long remaining = expiration.getTime() - System.currentTimeMillis();
                return Math.max(0, remaining / 1000);
            }
        } catch (Exception e) {
            log.error("获取Token剩余时间失败: {}", e.getMessage());
        }
        return 0;
    }

    /**
     * 刷新Token
     */
    public String refreshToken(String refreshToken) {
        try {
            if (validateRefreshToken(refreshToken)) {
                String username = getUsernameFromToken(refreshToken);
                Claims claims = getClaimsFromToken(refreshToken);
                
                // 移除刷新Token特有的claim
                claims.remove("type");
                claims.remove("iat");
                claims.remove("exp");
                
                return generateAccessToken(username, claims);
            }
        } catch (Exception e) {
            log.error("刷新Token失败: {}", e.getMessage());
        }
        return null;
    }

    /**
     * 从Token中获取指定的claim
     */
    public <T> T getClaimFromToken(String token, String claimName, Class<T> clazz) {
        try {
            Claims claims = getClaimsFromToken(token);
            return claims.get(claimName, clazz);
        } catch (Exception e) {
            log.error("从Token中获取claim失败: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 获取Token类型
     */
    public String getTokenType(String token) {
        return getClaimFromToken(token, "type", String.class);
    }

    /**
     * 判断是否为访问Token
     */
    public boolean isAccessToken(String token) {
        String type = getTokenType(token);
        return type == null || !"refresh".equals(type);
    }

    /**
     * 判断是否为刷新Token
     */
    public boolean isRefreshToken(String token) {
        String type = getTokenType(token);
        return "refresh".equals(type);
    }
}
