# 磁州生活网推广功能集成指南

本文档提供了在磁州生活网各业务模块中集成推广功能的详细步骤和最佳实践。

## 目录

1. [概述](#概述)
2. [集成步骤](#集成步骤)
3. [推广混入文件](#推广混入文件)
4. [组件说明](#组件说明)
5. [业务场景示例](#业务场景示例)
6. [常见问题](#常见问题)

## 概述

磁州生活网推广功能是一套通用的内容分享与推广解决方案，可以轻松集成到平台的各个业务模块中，包括但不限于：商家店铺、商品、拼车、房产、服务、社区信息等。

推广功能主要包括：
- 内容分享：生成分享链接、小程序码、海报等
- 佣金分销：支持分销员推广赚取佣金
- 数据统计：记录分享、浏览、转化等数据

## 集成步骤

### 1. 引入相关组件和混入

在需要集成推广功能的页面中，引入以下组件和混入：

```js
// 推广按钮组件
import PromotionToolButton from '@/components/PromotionToolButton.vue';
// 悬浮推广按钮组件
import FloatPromotionButton from '@/components/FloatPromotionButton.vue';
// 根据业务类型引入对应的推广混入
import xxxPromotionMixin from '@/mixins/xxxPromotionMixin';
```

### 2. 初始化推广功能

在页面加载时，初始化推广功能：

```js
// 推广相关
const hasPromotionPermission = ref(false);
const promotionData = ref({});

// 初始化推广功能
const initPromotion = () => {
  // 检查推广权限
  hasPromotionPermission.value = xxxPromotionMixin.methods.isContentOwner.call({
    $store: {
      state: {
        user: {
          userId: 'xxx' // 当前用户ID
        }
      }
    },
    xxxInfo: xxxInfo // 业务数据
  }) || xxxPromotionMixin.methods.isCommissionContent.call({
    xxxInfo: xxxInfo // 业务数据
  });
  
  // 生成推广数据
  if (hasPromotionPermission.value) {
    promotionData.value = xxxPromotionMixin.methods.generatePromotionData.call({
      xxxInfo: xxxInfo,
      promotionData: {}
    }).promotionData;
  }
};

// 显示推广工具
const showXxxPromotion = () => {
  xxxPromotionMixin.methods.showXxxPromotion.call({
    hasPromotionPermission: hasPromotionPermission.value,
    openPromotionTools: () => {
      uni.navigateTo({
        url: '/subPackages/promotion/pages/promotion-tool?type=xxx&id=' + xxxInfo.id
      });
    }
  });
};
```

### 3. 添加推广按钮

在页面模板中添加推广按钮：

```html
<!-- 普通推广按钮 -->
<PromotionToolButton 
  v-if="hasPromotionPermission" 
  @click="showXxxPromotion"
  buttonText="推广"
/>

<!-- 悬浮推广按钮 -->
<FloatPromotionButton 
  v-if="hasPromotionPermission" 
  @click="showXxxPromotion" 
/>
```

## 推广混入文件

推广混入文件是推广功能的核心，提供了权限判断、数据生成等通用方法。系统提供了以下混入文件：

- `basePromotionMixin.js`: 基础推广混入，提供核心功能
- `merchantPromotionMixin.js`: 商家推广混入
- `productPromotionMixin.js`: 商品推广混入
- `carpoolPromotionMixin.js`: 拼车推广混入
- `housePromotionMixin.js`: 房产推广混入
- `servicePromotionMixin.js`: 服务推广混入
- `communityPromotionMixin.js`: 社区信息推广混入
- `contentPromotionMixin.js`: 内容推广混入
- `activityPromotionMixin.js`: 活动推广混入

每个混入文件都包含以下核心方法：

- `isContentOwner()`: 判断当前用户是否是内容所有者
- `isCommissionContent()`: 判断内容是否支持佣金分销
- `generatePromotionData()`: 生成推广数据
- `showXxxPromotion()`: 显示推广工具

## 组件说明

### PromotionToolButton 推广按钮组件

普通推广按钮组件，适合放在操作栏等位置。

**属性：**
- `buttonText`: 按钮文字，默认为"推广"
- `buttonType`: 按钮类型，可选值：primary, default, warn，默认为"primary"
- `buttonSize`: 按钮大小，可选值：normal, small, mini，默认为"normal"

**事件：**
- `click`: 点击按钮时触发

### FloatPromotionButton 悬浮推广按钮组件

悬浮在页面上的推广按钮，通常放在右下角。

**属性：**
- `position`: 按钮位置，默认为{right: '30rpx', bottom: '180rpx'}
- `size`: 按钮大小，默认为'80rpx'

**事件：**
- `click`: 点击按钮时触发

## 业务场景示例

### 商家店铺推广

```vue
<template>
  <view class="shop-detail">
    <!-- 页面内容 -->
    
    <!-- 添加悬浮推广按钮 -->
    <FloatPromotionButton v-if="hasPromotionPermission" @click="showMerchantPromotion" />
    
    <!-- 操作区域 -->
    <view class="action-buttons">
      <PromotionToolButton 
        v-if="hasPromotionPermission" 
        @click="showMerchantPromotion" 
        buttonText="推广店铺" 
      />
    </view>
  </view>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import PromotionToolButton from '@/components/PromotionToolButton.vue';
import FloatPromotionButton from '@/components/FloatPromotionButton.vue';
import merchantPromotionMixin from '@/mixins/merchantPromotionMixin';

// 商家数据
const shopData = reactive({
  id: 'shop123',
  shopName: '示例店铺',
  // 其他店铺数据...
});

// 推广相关
const hasPromotionPermission = ref(false);
const promotionData = ref({});

// 页面加载
onMounted(() => {
  // 初始化推广功能
  initPromotion();
});

// 初始化推广功能
const initPromotion = () => {
  // 检查推广权限
  hasPromotionPermission.value = merchantPromotionMixin.methods.isContentOwner.call({
    $store: {
      state: {
        user: {
          userId: 'user123' // 当前用户ID
        }
      }
    },
    shopData: shopData
  }) || merchantPromotionMixin.methods.isCommissionContent.call({
    shopData: shopData
  });
  
  // 生成推广数据
  if (hasPromotionPermission.value) {
    promotionData.value = merchantPromotionMixin.methods.generatePromotionData.call({
      shopData: shopData,
      promotionData: {}
    }).promotionData;
  }
};

// 显示商家推广
const showMerchantPromotion = () => {
  merchantPromotionMixin.methods.showMerchantPromotion.call({
    hasPromotionPermission: hasPromotionPermission.value,
    openPromotionTools: () => {
      uni.navigateTo({
        url: '/subPackages/promotion/pages/promotion-tool?type=merchant&id=' + shopData.id
      });
    }
  });
};
</script>
```

### 拼车推广

```vue
<template>
  <view class="carpool-detail">
    <!-- 页面内容 -->
    
    <!-- 添加悬浮推广按钮 -->
    <FloatPromotionButton v-if="hasPromotionPermission" @click="showCarpoolPromotion" />
    
    <!-- 操作区域 -->
    <view class="action-buttons">
      <PromotionToolButton 
        v-if="hasPromotionPermission" 
        @click="showCarpoolPromotion" 
        buttonText="推广拼车" 
      />
    </view>
  </view>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import PromotionToolButton from '@/components/PromotionToolButton.vue';
import FloatPromotionButton from '@/components/FloatPromotionButton.vue';
import carpoolPromotionMixin from '@/mixins/carpoolPromotionMixin';

// 拼车数据
const carpoolInfo = reactive({
  id: 'carpool123',
  departure: '磁县',
  destination: '邯郸',
  // 其他拼车数据...
});

// 推广相关
const hasPromotionPermission = ref(false);
const promotionData = ref({});

// 页面加载
onMounted(() => {
  // 初始化推广功能
  initPromotion();
});

// 初始化推广功能
const initPromotion = () => {
  // 检查推广权限
  hasPromotionPermission.value = carpoolPromotionMixin.methods.isContentOwner.call({
    $store: {
      state: {
        user: {
          userId: 'user123' // 当前用户ID
        }
      }
    },
    carpoolInfo: carpoolInfo
  }) || carpoolPromotionMixin.methods.isCommissionContent.call({
    carpoolInfo: carpoolInfo
  });
  
  // 生成推广数据
  if (hasPromotionPermission.value) {
    promotionData.value = carpoolPromotionMixin.methods.generatePromotionData.call({
      carpoolInfo: carpoolInfo,
      promotionData: {}
    }).promotionData;
  }
};

// 显示拼车推广
const showCarpoolPromotion = () => {
  carpoolPromotionMixin.methods.showCarpoolPromotion.call({
    hasPromotionPermission: hasPromotionPermission.value,
    openPromotionTools: () => {
      uni.navigateTo({
        url: '/subPackages/promotion/pages/promotion-tool?type=carpool&id=' + carpoolInfo.id
      });
    }
  });
};
</script>
```

## 常见问题

### 1. 推广按钮不显示

检查以下几点：
- `hasPromotionPermission` 是否为 true
- 是否正确引入了组件和混入
- 是否正确调用了 `initPromotion` 方法

### 2. 推广数据不正确

检查以下几点：
- 业务数据是否完整
- 是否正确调用了 `generatePromotionData` 方法
- 是否正确传递了业务数据

### 3. 跳转推广页面失败

检查以下几点：
- URL 参数是否正确
- 推广页面是否存在
- 是否有权限访问推广页面

### 4. 自定义推广混入

如果现有的推广混入不满足需求，可以创建自定义推广混入：

```js
/**
 * 自定义推广混入
 */
import basePromotionMixin from './basePromotionMixin';

export default {
  mixins: [basePromotionMixin],

  data() {
    return {
      // 设置页面类型
      pageType: 'custom'
    };
  },

  methods: {
    /**
     * 重写：判断当前用户是否是内容所有者
     */
    isContentOwner() {
      // 自定义逻辑
    },

    /**
     * 重写：判断当前内容是否支持佣金
     */
    isCommissionContent() {
      // 自定义逻辑
    },

    /**
     * 重写：生成推广数据
     */
    generatePromotionData() {
      // 自定义逻辑
    },

    /**
     * 显示推广浮层
     */
    showCustomPromotion() {
      // 自定义逻辑
    }
  }
};
```

## 测试推广功能

为了确保推广功能正常工作，您可以按照以下步骤进行测试：

### 1. 测试推广按钮集成

1. 在业务页面中添加推广按钮：

```html
<PromotionToolButton 
  v-if="hasPromotionPermission" 
  @click="showXxxPromotion" 
  buttonText="推广" 
/>
```

2. 确认按钮正确显示并且点击后能够正确跳转到推广工具页面。

### 2. 测试推广工具页面

1. 直接访问推广工具页面进行测试：

```js
// 测试拼车推广
uni.navigateTo({
  url: '/subPackages/promotion/pages/promotion-tool?type=carpool&id=123'
});

// 测试商品推广
uni.navigateTo({
  url: '/subPackages/promotion/pages/promotion-tool?type=product&id=456'
});

// 测试商家推广
uni.navigateTo({
  url: '/subPackages/promotion/pages/promotion-tool?type=merchant&id=789'
});
```

2. 确认页面能够正确加载并显示相应的内容。

### 3. 测试二维码生成

1. 在推广工具页面中点击"小程序码"按钮。
2. 确认能够正确生成二维码并显示。
3. 测试保存二维码到相册功能。

### 4. 测试海报生成

1. 在推广工具页面中点击"生成海报"按钮。
2. 确认能够正确生成海报并显示。
3. 测试保存海报到相册功能。

### 5. 测试分享功能

1. 测试"微信好友"和"朋友圈"分享功能。
2. 测试"复制链接"功能。

### 6. 测试权限控制

1. 使用不同角色的用户登录测试推广权限。
2. 确认只有有权限的用户才能看到推广按钮。

## 推广功能完整示例

以下是一个完整的推广功能集成示例：

```vue
<template>
  <view class="product-detail">
    <!-- 产品信息 -->
    <view class="product-info">
      <image class="product-image" :src="productInfo.image" mode="aspectFill"></image>
      <view class="product-title">{{ productInfo.title }}</view>
      <view class="product-price">¥{{ productInfo.price }}</view>
    </view>
    
    <!-- 操作按钮 -->
    <view class="action-buttons">
      <button class="action-btn buy" @click="buyProduct">立即购买</button>
      <PromotionToolButton 
        v-if="hasPromotionPermission" 
        @click="showProductPromotion" 
        buttonText="推广赚佣金" 
        :showCommission="true" 
      />
    </view>
    
    <!-- 悬浮推广按钮 -->
    <FloatPromotionButton 
      v-if="hasPromotionPermission" 
      @click="showProductPromotion" 
    />
  </view>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import PromotionToolButton from '@/components/PromotionToolButton.vue';
import FloatPromotionButton from '@/components/FloatPromotionButton.vue';
import productPromotionMixin from '@/mixins/productPromotionMixin';

// 产品数据
const productInfo = reactive({
  id: '123456',
  title: '有机新鲜蔬菜礼盒',
  price: 99.8,
  originalPrice: 128,
  image: '/static/images/product-default.jpg',
  description: '本地新鲜蔬菜，无农药，当天采摘'
});

// 推广相关
const hasPromotionPermission = ref(false);
const promotionData = ref({});

// 页面加载
onMounted(() => {
  // 初始化推广功能
  initPromotion();
});

// 初始化推广功能
const initPromotion = () => {
  // 检查推广权限
  hasPromotionPermission.value = productPromotionMixin.methods.isContentOwner.call({
    $store: {
      state: {
        user: {
          userId: 'user123' // 当前用户ID
        }
      }
    },
    productInfo: productInfo
  }) || productPromotionMixin.methods.isCommissionContent.call({
    productInfo: productInfo
  });
  
  // 生成推广数据
  if (hasPromotionPermission.value) {
    promotionData.value = productPromotionMixin.methods.generatePromotionData.call({
      productInfo: productInfo,
      promotionData: {}
    }).promotionData;
  }
};

// 显示产品推广
const showProductPromotion = () => {
  productPromotionMixin.methods.showProductPromotion.call({
    hasPromotionPermission: hasPromotionPermission.value,
    openPromotionTools: () => {
      uni.navigateTo({
        url: '/subPackages/promotion/pages/promotion-tool?type=product&id=' + productInfo.id
      });
    }
  });
};

// 购买产品
const buyProduct = () => {
  uni.showToast({
    title: '购买功能开发中',
    icon: 'none'
  });
};
</script>
```

---

如有其他问题，请联系技术支持团队。 