{"version": 3, "file": "index.js", "sources": ["carpool-package/pages/carpool/my/index.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/Y2FycG9vbC1wYWNrYWdlXHBhZ2VzXGNhcnBvb2xcbXlcaW5kZXgudnVl"], "sourcesContent": ["<template>\n  <view class=\"carpool-my-container\">\n    <!-- 自定义导航栏 -->\n    <view class=\"custom-header\" :style=\"{ paddingTop: statusBarHeight + 'px' }\">\n      <view class=\"header-content\">\n        <view class=\"left-action\" @click=\"goBack\">\n          <image src=\"/static/images/tabbar/最新返回键.png\" class=\"action-icon back-icon\"></image>\n        </view>\n        <view class=\"title-area\">\n          <text class=\"page-title\">我的拼车</text>\n        </view>\n        <view class=\"right-action\">\n          <image src=\"/static/images/tabbar/setting.png\" class=\"action-icon\" @click=\"goSettings\"></image>\n        </view>\n      </view>\n    </view>\n    \n    <!-- 用户信息卡片 - 简化版 -->\n    <view class=\"user-card\">\n      <view class=\"user-avatar-container\">\n        <image class=\"user-avatar\" :src=\"userInfo.avatar\" mode=\"aspectFill\"></image>\n      </view>\n      <view class=\"user-name\">{{userInfo.nickname}}</view>\n      <view class=\"user-id\">ID: {{userInfo.id}}</view>\n      <view class=\"user-credit\">信用分: <text class=\"credit-score\">{{userInfo.creditScore}}</text></view>\n      \n      <view class=\"user-actions\">\n        <view class=\"action-item\" @click=\"goVerification\">\n          <text class=\"action-text\">{{userInfo.isVerified ? '已认证' : '去认证'}}</text>\n        </view>\n        <view class=\"action-divider\"></view>\n        <view class=\"action-item\" @click=\"goVIP\">\n          <text class=\"action-text\">{{userInfo.isVIP ? 'VIP会员' : '开通VIP'}}</text>\n        </view>\n      </view>\n    </view>\n    \n    <!-- 测试按钮 -->\n    <view class=\"test-button\" @click=\"goToSuccessPage\">\n      <text class=\"test-button-text\">测试发布成功页面</text>\n    </view>\n    \n    <!-- 统计数据 -->\n    <view class=\"stats-card\">\n      <view class=\"stat-item\">\n        <text class=\"stat-value\">{{stats.publishCount}}</text>\n        <text class=\"stat-label\">已发布</text>\n      </view>\n      <view class=\"stat-item\">\n        <text class=\"stat-value\">{{stats.completedCount}}</text>\n        <text class=\"stat-label\">已完成</text>\n      </view>\n      <view class=\"stat-item\">\n        <text class=\"stat-value\">{{stats.favorCount}}</text>\n        <text class=\"stat-label\">收藏</text>\n      </view>\n      <view class=\"stat-item\">\n        <text class=\"stat-value\">{{stats.tripDistance}}km</text>\n        <text class=\"stat-label\">总里程</text>\n      </view>\n    </view>\n    \n    <!-- 功能列表 -->\n    <view class=\"feature-section\">\n      <view class=\"section-header\">\n        <text class=\"section-title\">我的发布</text>\n      </view>\n      \n      <view class=\"feature-grid\">\n        <view class=\"feature-item\" @click=\"navigateToList('published')\">\n          <view class=\"feature-icon-wrapper blue\">\n            <image src=\"/static/images/tabbar/publish-list.png\" mode=\"aspectFit\" class=\"feature-icon\"></image>\n          </view>\n          <text class=\"feature-text\">发布列表</text>\n        </view>\n        \n        <view class=\"feature-item\" @click=\"goToTripRecords\">\n          <view class=\"feature-icon-wrapper purple\">\n            <image src=\"/static/images/tabbar/history.png\" mode=\"aspectFit\" class=\"feature-icon\"></image>\n          </view>\n          <text class=\"feature-text\">行程记录</text>\n        </view>\n        \n        <view class=\"feature-item\" @click=\"goToDriverRatings\">\n          <view class=\"feature-icon-wrapper orange\">\n            <image src=\"/static/images/tabbar/star-rating.png\" mode=\"aspectFit\" class=\"feature-icon\"></image>\n          </view>\n          <text class=\"feature-text\">我的评价</text>\n          <view class=\"badge\" v-if=\"stats.newRatingCount > 0\">{{stats.newRatingCount}}</view>\n        </view>\n        \n        <view class=\"feature-item\" @click=\"goToDriverProfile\">\n          <view class=\"feature-icon-wrapper green\">\n            <image src=\"/static/images/tabbar/driver.png\" mode=\"aspectFit\" class=\"feature-icon\"></image>\n          </view>\n          <text class=\"feature-text\">个人中心</text>\n        </view>\n      </view>\n    </view>\n    \n    <view class=\"feature-section\">\n      <view class=\"section-header\">\n        <text class=\"section-title\">更多服务</text>\n      </view>\n      \n      <view class=\"feature-list\">\n        <view class=\"list-item\" @click=\"navigateToList('favorites')\">\n          <view class=\"item-left\">\n            <view class=\"item-icon-wrapper purple\">\n              <image src=\"/static/images/tabbar/star.png\" mode=\"aspectFit\" class=\"item-icon\"></image>\n            </view>\n            <text class=\"item-text\">我的收藏</text>\n          </view>\n          <view class=\"item-right\">\n            <image src=\"/static/images/tabbar/arrow-right.png\" mode=\"aspectFit\" class=\"arrow-icon\"></image>\n          </view>\n        </view>\n        \n        <view class=\"list-item\" @click=\"navigateToPage('contact-history')\">\n          <view class=\"item-left\">\n            <view class=\"item-icon-wrapper green\">\n              <image src=\"/static/images/tabbar/history.png\" mode=\"aspectFit\" class=\"item-icon\"></image>\n            </view>\n            <text class=\"item-text\">联系历史</text>\n          </view>\n          <view class=\"item-right\">\n            <view class=\"notification-badge\" v-if=\"stats.contactHistoryCount > 0\">{{stats.contactHistoryCount}}</view>\n            <image src=\"/static/images/tabbar/arrow-right.png\" mode=\"aspectFit\" class=\"arrow-icon\"></image>\n          </view>\n        </view>\n        \n        <view class=\"list-item\" @click=\"navigateToPage('message')\">\n          <view class=\"item-left\">\n            <view class=\"item-icon-wrapper blue\">\n              <image src=\"/static/images/tabbar/message.png\" mode=\"aspectFit\" class=\"item-icon\"></image>\n            </view>\n            <text class=\"item-text\">消息中心</text>\n          </view>\n          <view class=\"item-right\">\n            <view class=\"notification-badge\" v-if=\"stats.unreadMessageCount > 0\">{{stats.unreadMessageCount}}</view>\n            <image src=\"/static/images/tabbar/arrow-right.png\" mode=\"aspectFit\" class=\"arrow-icon\"></image>\n          </view>\n        </view>\n        \n        <view class=\"list-item\" @click=\"navigateToPage('wallet')\">\n          <view class=\"item-left\">\n            <view class=\"item-icon-wrapper orange\">\n              <image src=\"/static/images/tabbar/wallet.png\" mode=\"aspectFit\" class=\"item-icon\"></image>\n            </view>\n            <text class=\"item-text\">我的钱包</text>\n          </view>\n          <view class=\"item-right\">\n            <image src=\"/static/images/tabbar/arrow-right.png\" mode=\"aspectFit\" class=\"arrow-icon\"></image>\n          </view>\n        </view>\n        \n        <view class=\"list-item\" @click=\"navigateToPage('driver-verification')\">\n          <view class=\"item-left\">\n            <view class=\"item-icon-wrapper blue\">\n              <image src=\"/static/images/tabbar/verify.png\" mode=\"aspectFit\" class=\"item-icon\"></image>\n            </view>\n            <text class=\"item-text\">司机认证</text>\n          </view>\n          <view class=\"item-right\">\n            <view class=\"verification-badge\" v-if=\"userInfo.isDriverVerified\">已认证</view>\n            <image src=\"/static/images/tabbar/arrow-right.png\" mode=\"aspectFit\" class=\"arrow-icon\"></image>\n          </view>\n        </view>\n        \n        <view class=\"list-item\" @click=\"navigateToPage('feedback')\">\n          <view class=\"item-left\">\n            <view class=\"item-icon-wrapper green\">\n              <image src=\"/static/images/tabbar/feedback.png\" mode=\"aspectFit\" class=\"item-icon\"></image>\n            </view>\n            <text class=\"item-text\">意见反馈</text>\n          </view>\n          <view class=\"item-right\">\n            <image src=\"/static/images/tabbar/arrow-right.png\" mode=\"aspectFit\" class=\"arrow-icon\"></image>\n          </view>\n        </view>\n      </view>\n    </view>\n    \n    <!-- 底部导航栏 -->\n    <view class=\"tabbar\">\n      <view class=\"tabbar-item\" :class=\"{ active: activeTab === 'home' }\" @click=\"navigateToPage('home')\">\n        <image :src=\"activeTab === 'home' ? '/static/images/tabbar/p首页选中.png' : '/static/images/tabbar/p首页.png'\" mode=\"aspectFit\" class=\"tabbar-icon\"></image>\n        <text class=\"tabbar-text\" :class=\"{ 'active-text': activeTab === 'home' }\">同城</text>\n      </view>\n      <view class=\"tabbar-item\" :class=\"{ active: activeTab === 'carpool-main' }\" @click=\"navigateToPage('carpool-main')\">\n        <image :src=\"activeTab === 'carpool-main' ? '/static/images/tabbar/p拼车选中.png' : '/static/images/tabbar/p拼车.png'\" mode=\"aspectFit\" class=\"tabbar-icon\"></image>\n        <text class=\"tabbar-text\" :class=\"{ 'active-text': activeTab === 'carpool-main' }\">拼车</text>\n      </view>\n      <view class=\"tabbar-item\" :class=\"{ active: activeTab === 'publish' }\" @click=\"publishNew\">\n        <image :src=\"activeTab === 'publish' ? '/static/images/tabbar/p发布选中.png' : '/static/images/tabbar/p发布.png'\" mode=\"aspectFit\" class=\"tabbar-icon\"></image>\n        <text class=\"tabbar-text\" :class=\"{ 'active-text': activeTab === 'publish' }\">发布</text>\n      </view>\n      <view class=\"tabbar-item\" :class=\"{ active: activeTab === 'groups' }\" @click=\"navigateToPage('groups')\">\n        <image :src=\"activeTab === 'groups' ? '/static/images/tabbar/p拼车群选中.png' : '/static/images/tabbar/p拼车群.png'\" mode=\"aspectFit\" class=\"tabbar-icon\"></image>\n        <text class=\"tabbar-text\" :class=\"{ 'active-text': activeTab === 'groups' }\">拼车群</text>\n      </view>\n      <view class=\"tabbar-item active\">\n        <image src=\"/static/images/tabbar/p我的选中.png\" mode=\"aspectFit\" class=\"tabbar-icon\"></image>\n        <text class=\"tabbar-text active-text\">我的</text>\n      </view>\n    </view>\n    \n    <!-- 底部安全区域 -->\n    <view class=\"safe-area-bottom\"></view>\n  </view>\n</template>\n\n<script setup>\nimport { ref, computed, onMounted } from 'vue';\nimport { onLoad, onShow, onHide, onUnload, onBackPress } from '@dcloudio/uni-app';\n\n// 用户信息\nconst userInfo = ref({\n  id: '10086',\n  nickname: '磁州用户',\n  avatar: '/static/images/avatar/user1.png',\n  creditScore: 98,\n  isVerified: true,\n  isVIP: false,\n  isDriverVerified: true\n});\n\n// 统计数据\nconst stats = ref({\n  publishCount: 12,\n  completedCount: 8,\n  favorCount: 5,\n  tripDistance: 320,\n  newRatingCount: 2,\n  contactHistoryCount: 3,\n  unreadMessageCount: 5\n});\n\n// 当前激活的tab\nconst activeTab = ref('my');\n\n// 状态栏高度\nconst statusBarHeight = ref(20);\n\n// 生命周期钩子\nonMounted(() => {\n  // 隐藏原生tabBar\n  uni.hideTabBar();\n  \n  // 获取状态栏高度\n  const systemInfo = uni.getSystemInfoSync();\n  statusBarHeight.value = systemInfo.statusBarHeight;\n});\n\nonShow(() => {\n  // 确保每次页面显示时都隐藏原生tabBar\n  uni.hideTabBar();\n});\n\nonLoad(() => {\n  getUserInfo();\n  getStatistics();\n  uni.hideTabBar();\n});\n\nonHide(() => {\n  // 页面隐藏时，需要根据下一个页面决定是否显示tabBar\n});\n\nonUnload(() => {\n  // 如果不是返回到拼车主页，需要显示默认tabBar\n  const pages = getCurrentPages();\n  const prevPage = pages[pages.length - 2];\n  if (!prevPage || prevPage.route !== 'carpool-package/pages/carpool-main/index') {\n    uni.showTabBar();\n  }\n});\n\nonBackPress((event) => {\n  if (event.from === 'backbutton') {\n    uni.navigateTo({\n      url: '/carpool-package/pages/carpool-main/index'\n    });\n    return true;\n  }\n  return false;\n});\n\n// 获取用户信息\nconst getUserInfo = () => {\n  // 这里应该是真实的API调用\n  // 目前使用模拟数据\n  console.log('获取用户信息');\n};\n\n// 获取统计数据\nconst getStatistics = () => {\n  // 这里应该是真实的API调用\n  // 目前使用模拟数据\n  console.log('获取统计数据');\n};\n\n// 查看用户资料\nconst viewUserProfile = () => {\n  uni.navigateTo({\n    url: '/carpool-package/pages/carpool/my/profile'\n  });\n};\n\n// 前往设置页面\nconst goSettings = () => {\n  uni.navigateTo({\n    url: '/carpool-package/pages/carpool/my/settings'\n  });\n};\n\n// 返回上一页\nconst goBack = () => {\n  uni.navigateBack();\n};\n\n// 前往认证页面\nconst goVerification = () => {\n  if (userInfo.value.isVerified) {\n    uni.showToast({\n      title: '您已通过认证',\n      icon: 'success'\n    });\n    return;\n  }\n  \n  uni.navigateTo({\n    url: '/carpool-package/pages/carpool/my/verification'\n  });\n};\n\n// 前往VIP页面\nconst goVIP = () => {\n  uni.navigateTo({\n    url: '/carpool-package/pages/carpool/my/vip'\n  });\n};\n\n// 导航到列表页面\nconst navigateToList = (type) => {\n  uni.navigateTo({\n    url: `/carpool-package/pages/carpool/my/${type === 'published' ? 'published-list' : type === 'pending' ? 'pending-list' : type === 'expired' ? 'expired-list' : 'favorites'}`\n  });\n};\n\n// 导航到其他页面\nconst navigateToPage = (page) => {\n  let url = '';\n  \n  switch(page) {\n    case 'home':\n      uni.switchTab({\n        url: '/pages/index/index'\n      });\n      return;\n    case 'carpool-main':\n      uni.navigateTo({\n        url: '/carpool-package/pages/carpool-main/index'\n      });\n      return;\n    case 'groups':\n      uni.navigateTo({\n        url: '/carpool-package/pages/carpool/groups/index'\n      });\n      return;\n    case 'message':\n      uni.navigateTo({\n        url: '/carpool-package/pages/carpool/my/message-center'\n      });\n      return;\n    case 'wallet':\n      uni.navigateTo({\n        url: '/subPackages/payment/pages/wallet'\n      });\n      return;\n    case 'driver-verification':\n      uni.navigateTo({\n        url: '/carpool-package/pages/carpool/my/driver-verification'\n      });\n      return;\n    case 'feedback':\n      uni.navigateTo({\n        url: '/carpool-package/pages/carpool/my/feedback'\n      });\n      return;\n    case 'trip-records':\n      uni.navigateTo({\n        url: '/carpool-package/pages/carpool/my/trip-records'\n      });\n      return;\n    case 'driver-ratings':\n      uni.navigateTo({\n        url: '/carpool-package/pages/carpool/my/driver-ratings'\n      });\n      return;\n    case 'driver-profile':\n      uni.navigateTo({\n        url: '/carpool-package/pages/carpool/my/driver-profile'\n      });\n      return;\n    case 'contact-history':\n      uni.navigateTo({\n        url: '/carpool-package/pages/carpool/my/contact-history'\n      });\n      return;\n  }\n  \n  if (url) {\n    uni.navigateTo({ url });\n  }\n};\n\n// 发布新拼车信息\nconst publishNew = () => {\n  activeTab.value = 'publish'; // 设置发布按钮为激活状态\n  uni.navigateTo({\n    url: '/carpool-package/pages/carpool-main/index'\n  });\n  // 注意：由于switchTab不支持eventChannel，可能需要其他方式触发弹窗\n  // 一种可能的解决方案是使用全局状态或本地存储\n  uni.setStorageSync('showPublishPopup', true);\n};\n\n// 前往司机评价页面\nconst goToDriverRatings = () => {\n  uni.navigateTo({\n    url: '/carpool-package/pages/carpool/my/driver-ratings'\n  });\n};\n\n// 前往行程记录页面\nconst goToTripRecords = () => {\n  uni.navigateTo({\n    url: '/carpool-package/pages/carpool/my/trip-records'\n  });\n};\n\n// 前往个人中心页面\nconst goToDriverProfile = () => {\n  uni.navigateTo({\n    url: '/carpool-package/pages/carpool/my/driver-profile'\n  });\n};\n\n// 跳转到发布成功页面\nconst goToSuccessPage = () => {\n  // 生成一个模拟的发布ID\n  const publishId = Date.now().toString();\n  \n  // 跳转到成功页面\n  uni.navigateTo({\n    url: `/carpool-package/pages/carpool/publish/success?id=${publishId}&type=car-to-people&mode=ad`\n  });\n};\n</script>\n\n<style lang=\"scss\">\n.carpool-my-container {\n  min-height: 100vh;\n  background-color: #F5F8FC;\n  position: relative;\n  padding-top: calc(var(--status-bar-height) + 90rpx); /* 只考虑标题栏高度 */\n  padding-bottom: calc(110rpx + env(safe-area-inset-bottom, 0));\n}\n\n/* 自定义标题栏模块 */\n.custom-header {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  width: 100%;\n  background-color: #1677FF; /* 恢复为实色背景 */\n  z-index: 103;\n  box-shadow: none;\n}\n\n/* 标题栏内容 */\n.header-content {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  height: 90rpx;\n  padding: 0 30rpx;\n  position: relative;\n  z-index: 102;\n}\n\n.left-action {\n  width: 70rpx;\n  height: 70rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.title-area {\n  flex: 1;\n  text-align: center;\n}\n\n.page-title {\n  color: #ffffff;\n  font-size: 36rpx;\n  font-weight: 600;\n  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);\n}\n\n.right-action {\n  width: 70rpx;\n  height: 70rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.action-icon {\n  width: 44rpx;\n  height: 44rpx;\n  filter: brightness(0) invert(1);\n}\n\n/* 用户信息卡片 - 简化版 */\n.user-card {\n  margin: 60rpx 32rpx 20rpx;\n  border-radius: 20rpx;\n  background-color: #FFFFFF;\n  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  padding: 24rpx 24rpx;\n}\n\n.user-avatar-container {\n  width: 100rpx;\n  height: 100rpx;\n  margin-bottom: 12rpx;\n  border-radius: 50%;\n  overflow: hidden;\n  border: 2rpx solid #F2F2F7;\n}\n\n.user-avatar {\n  width: 100%;\n  height: 100%;\n  border-radius: 50% !important;\n  object-fit: cover;\n}\n\n.user-name {\n  font-size: 32rpx;\n  font-weight: 600;\n  color: #333333;\n  margin-bottom: 6rpx;\n}\n\n.user-id {\n  font-size: 24rpx;\n  color: #8E8E93;\n  margin-bottom: 6rpx;\n}\n\n.user-credit {\n  font-size: 24rpx;\n  color: #8E8E93;\n  margin-bottom: 20rpx;\n}\n\n.credit-score {\n  color: #FF9F0A;\n  font-weight: 500;\n}\n\n.user-actions {\n  display: flex;\n  width: 100%;\n  border-top: 1px solid #F2F2F7;\n  padding-top: 20rpx;\n  margin-top: 4rpx;\n}\n\n.action-item {\n  flex: 1;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n}\n\n.action-divider {\n  width: 1px;\n  height: 32rpx;\n  background-color: #F2F2F7;\n}\n\n.action-text {\n  font-size: 28rpx;\n  color: #666666;\n}\n\n/* 统计数据卡片 */\n.stats-card {\n  margin: 0 32rpx 32rpx;\n  background-color: #FFFFFF;\n  border-radius: 20rpx;\n  padding: 24rpx 0;\n  display: flex;\n  justify-content: space-around;\n  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);\n}\n\n.stat-item {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n}\n\n.stat-value {\n  font-size: 34rpx;\n  font-weight: 600;\n  color: #333333;\n  margin-bottom: 8rpx;\n}\n\n.stat-label {\n  font-size: 24rpx;\n  color: #8E8E93;\n}\n\n/* 功能区块 */\n.feature-section {\n  margin: 0 32rpx 32rpx;\n  background-color: #FFFFFF;\n  border-radius: 20rpx;\n  padding: 24rpx;\n  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);\n}\n\n.section-header {\n  margin-bottom: 24rpx;\n}\n\n.section-title {\n  font-size: 30rpx;\n  font-weight: 600;\n  color: #333333;\n  position: relative;\n  padding-left: 16rpx;\n}\n\n.section-title::before {\n  content: '';\n  position: absolute;\n  left: 0;\n  top: 6rpx;\n  bottom: 6rpx;\n  width: 4rpx;\n  background: linear-gradient(to bottom, #0A84FF, #5AC8FA);\n  border-radius: 2rpx;\n}\n\n.feature-grid {\n  display: flex;\n  flex-wrap: wrap;\n}\n\n.feature-item {\n  width: 25%;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  margin-bottom: 24rpx;\n  position: relative;\n}\n\n.feature-icon-wrapper {\n  width: 88rpx;\n  height: 88rpx;\n  border-radius: 22rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-bottom: 14rpx;\n  box-shadow: 0 6rpx 12rpx rgba(0, 0, 0, 0.1);\n}\n\n.blue {\n  background: linear-gradient(135deg, #0A84FF, #5AC8FA);\n}\n\n.orange {\n  background: linear-gradient(135deg, #FF9F0A, #FF2D55);\n}\n\n.green {\n  background: linear-gradient(135deg, #30D158, #34C759);\n}\n\n.gray {\n  background: linear-gradient(135deg, #8E8E93, #636366);\n}\n\n.purple {\n  background: linear-gradient(135deg, #BF5AF2, #A34FC9);\n}\n\n.red {\n  background: linear-gradient(135deg, #FF2D55, #FF2D55);\n}\n\n.feature-icon {\n  width: 44rpx;\n  height: 44rpx;\n  filter: brightness(0) invert(1);\n}\n\n.feature-text {\n  font-size: 26rpx;\n  color: #666666;\n}\n\n.badge {\n  position: absolute;\n  top: -8rpx;\n  right: 20rpx;\n  min-width: 32rpx;\n  height: 32rpx;\n  border-radius: 16rpx;\n  background-color: #FF2D55;\n  color: #ffffff;\n  font-size: 22rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  padding: 0 8rpx;\n  box-shadow: 0 2rpx 6rpx rgba(255, 45, 85, 0.3);\n}\n\n.hot-badge {\n  background-color: #FF2D55;\n  color: #ffffff;\n  padding: 0 8rpx;\n  border-radius: 16rpx;\n  font-size: 22rpx;\n}\n\n/* 列表样式 */\n.feature-list {\n  background-color: #FFFFFF;\n  border-radius: 16rpx;\n  overflow: hidden;\n}\n\n.list-item {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 32rpx 0;\n  position: relative;\n}\n\n.list-item:not(:last-child) {\n  border-bottom: 1px solid #F2F2F7;\n}\n\n.item-left {\n  display: flex;\n  align-items: center;\n}\n\n.item-icon-wrapper {\n  width: 64rpx;\n  height: 64rpx;\n  border-radius: 16rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-right: 24rpx;\n  box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.1);\n}\n\n.item-icon {\n  width: 36rpx;\n  height: 36rpx;\n  filter: brightness(0) invert(1);\n}\n\n.item-text {\n  font-size: 30rpx;\n  color: #333333;\n  font-weight: 500;\n}\n\n.item-right {\n  display: flex;\n  align-items: center;\n}\n\n.arrow-icon {\n  width: 32rpx;\n  height: 32rpx;\n  opacity: 0.5;\n}\n\n.notification-badge {\n  background-color: #FF3B30;\n  color: #FFFFFF;\n  font-size: 20rpx;\n  min-width: 32rpx;\n  height: 32rpx;\n  border-radius: 16rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  padding: 0 8rpx;\n  margin-right: 10rpx;\n}\n\n.verification-badge {\n  background-color: #34C759;\n  color: #FFFFFF;\n  font-size: 20rpx;\n  height: 32rpx;\n  border-radius: 16rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  padding: 0 12rpx;\n  margin-right: 10rpx;\n}\n\n/* 底部导航栏 */\n.tabbar {\n  position: fixed;\n  bottom: 0;\n  left: 0;\n  right: 0;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  height: 110rpx;\n  background-color: rgba(255, 255, 255, 0.95);\n  border-top: 1px solid rgba(0, 0, 0, 0.05);\n  z-index: 9999;\n  padding-bottom: env(safe-area-inset-bottom);\n  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);\n  backdrop-filter: blur(10px);\n  -webkit-backdrop-filter: blur(10px);\n}\n\n.tabbar-item {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: 10rpx 0;\n  position: relative;\n  transition: all 0.2s ease;\n}\n\n.tabbar-item:active {\n  opacity: 0.7;\n}\n\n.tabbar-icon {\n  width: 48rpx;\n  height: 48rpx;\n  margin-bottom: 6rpx;\n}\n\n.tabbar-text {\n  font-size: 22rpx;\n  color: #999999;\n  line-height: 1;\n}\n\n.active-text {\n  color: #0A84FF;\n  font-weight: 500;\n}\n\n.tabbar-item.active .tabbar-icon {\n  transform: scale(1.1);\n}\n\n.safe-area-bottom {\n  height: calc(env(safe-area-inset-bottom) + 30rpx);\n  width: 100%;\n}\n\n/* 测试按钮样式 */\n.test-button {\n  margin: 0 32rpx 20rpx;\n  background: linear-gradient(135deg, #0A84FF, #5AC8FA);\n  border-radius: 45rpx;\n  height: 90rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  box-shadow: 0 4rpx 16rpx rgba(10, 132, 255, 0.3);\n}\n\n.test-button-text {\n  color: #FFFFFF;\n  font-size: 32rpx;\n  font-weight: 600;\n}\n</style> ", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/carpool-package/pages/carpool/my/index.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "onMounted", "uni", "onShow", "onLoad", "onHide", "onUnload", "onBackPress", "MiniProgramPage"], "mappings": ";;;;;;AAyNA,UAAM,WAAWA,cAAAA,IAAI;AAAA,MACnB,IAAI;AAAA,MACJ,UAAU;AAAA,MACV,QAAQ;AAAA,MACR,aAAa;AAAA,MACb,YAAY;AAAA,MACZ,OAAO;AAAA,MACP,kBAAkB;AAAA,IACpB,CAAC;AAGD,UAAM,QAAQA,cAAAA,IAAI;AAAA,MAChB,cAAc;AAAA,MACd,gBAAgB;AAAA,MAChB,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,gBAAgB;AAAA,MAChB,qBAAqB;AAAA,MACrB,oBAAoB;AAAA,IACtB,CAAC;AAGD,UAAM,YAAYA,cAAAA,IAAI,IAAI;AAG1B,UAAM,kBAAkBA,cAAAA,IAAI,EAAE;AAG9BC,kBAAAA,UAAU,MAAM;AAEdC,oBAAG,MAAC,WAAU;AAGd,YAAM,aAAaA,oBAAI;AACvB,sBAAgB,QAAQ,WAAW;AAAA,IACrC,CAAC;AAEDC,kBAAAA,OAAO,MAAM;AAEXD,oBAAG,MAAC,WAAU;AAAA,IAChB,CAAC;AAEDE,kBAAAA,OAAO,MAAM;AACX;AACA;AACAF,oBAAG,MAAC,WAAU;AAAA,IAChB,CAAC;AAEDG,kBAAAA,OAAO,MAAM;AAAA,IAEb,CAAC;AAEDC,kBAAAA,SAAS,MAAM;AAEb,YAAM,QAAQ;AACd,YAAM,WAAW,MAAM,MAAM,SAAS,CAAC;AACvC,UAAI,CAAC,YAAY,SAAS,UAAU,4CAA4C;AAC9EJ,sBAAG,MAAC,WAAU;AAAA,MACf;AAAA,IACH,CAAC;AAEDK,kBAAW,YAAC,CAAC,UAAU;AACrB,UAAI,MAAM,SAAS,cAAc;AAC/BL,sBAAAA,MAAI,WAAW;AAAA,UACb,KAAK;AAAA,QACX,CAAK;AACD,eAAO;AAAA,MACR;AACD,aAAO;AAAA,IACT,CAAC;AAGD,UAAM,cAAc,MAAM;AAGxBA,oBAAAA,MAAY,MAAA,OAAA,qDAAA,QAAQ;AAAA,IACtB;AAGA,UAAM,gBAAgB,MAAM;AAG1BA,oBAAAA,MAAY,MAAA,OAAA,qDAAA,QAAQ;AAAA,IACtB;AAUA,UAAM,aAAa,MAAM;AACvBA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK;AAAA,MACT,CAAG;AAAA,IACH;AAGA,UAAM,SAAS,MAAM;AACnBA,oBAAG,MAAC,aAAY;AAAA,IAClB;AAGA,UAAM,iBAAiB,MAAM;AAC3B,UAAI,SAAS,MAAM,YAAY;AAC7BA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AACD;AAAA,MACD;AAEDA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK;AAAA,MACT,CAAG;AAAA,IACH;AAGA,UAAM,QAAQ,MAAM;AAClBA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK;AAAA,MACT,CAAG;AAAA,IACH;AAGA,UAAM,iBAAiB,CAAC,SAAS;AAC/BA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,qCAAqC,SAAS,cAAc,mBAAmB,SAAS,YAAY,iBAAiB,SAAS,YAAY,iBAAiB,WAAW;AAAA,MAC/K,CAAG;AAAA,IACH;AAGA,UAAM,iBAAiB,CAAC,SAAS;AAG/B,cAAO,MAAI;AAAA,QACT,KAAK;AACHA,wBAAAA,MAAI,UAAU;AAAA,YACZ,KAAK;AAAA,UACb,CAAO;AACD;AAAA,QACF,KAAK;AACHA,wBAAAA,MAAI,WAAW;AAAA,YACb,KAAK;AAAA,UACb,CAAO;AACD;AAAA,QACF,KAAK;AACHA,wBAAAA,MAAI,WAAW;AAAA,YACb,KAAK;AAAA,UACb,CAAO;AACD;AAAA,QACF,KAAK;AACHA,wBAAAA,MAAI,WAAW;AAAA,YACb,KAAK;AAAA,UACb,CAAO;AACD;AAAA,QACF,KAAK;AACHA,wBAAAA,MAAI,WAAW;AAAA,YACb,KAAK;AAAA,UACb,CAAO;AACD;AAAA,QACF,KAAK;AACHA,wBAAAA,MAAI,WAAW;AAAA,YACb,KAAK;AAAA,UACb,CAAO;AACD;AAAA,QACF,KAAK;AACHA,wBAAAA,MAAI,WAAW;AAAA,YACb,KAAK;AAAA,UACb,CAAO;AACD;AAAA,QACF,KAAK;AACHA,wBAAAA,MAAI,WAAW;AAAA,YACb,KAAK;AAAA,UACb,CAAO;AACD;AAAA,QACF,KAAK;AACHA,wBAAAA,MAAI,WAAW;AAAA,YACb,KAAK;AAAA,UACb,CAAO;AACD;AAAA,QACF,KAAK;AACHA,wBAAAA,MAAI,WAAW;AAAA,YACb,KAAK;AAAA,UACb,CAAO;AACD;AAAA,QACF,KAAK;AACHA,wBAAAA,MAAI,WAAW;AAAA,YACb,KAAK;AAAA,UACb,CAAO;AACD;AAAA,MACH;AAAA,IAKH;AAGA,UAAM,aAAa,MAAM;AACvB,gBAAU,QAAQ;AAClBA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK;AAAA,MACT,CAAG;AAGDA,oBAAAA,MAAI,eAAe,oBAAoB,IAAI;AAAA,IAC7C;AAGA,UAAM,oBAAoB,MAAM;AAC9BA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK;AAAA,MACT,CAAG;AAAA,IACH;AAGA,UAAM,kBAAkB,MAAM;AAC5BA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK;AAAA,MACT,CAAG;AAAA,IACH;AAGA,UAAM,oBAAoB,MAAM;AAC9BA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK;AAAA,MACT,CAAG;AAAA,IACH;AAGA,UAAM,kBAAkB,MAAM;AAE5B,YAAM,YAAY,KAAK,IAAK,EAAC,SAAQ;AAGrCA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,qDAAqD,SAAS;AAAA,MACvE,CAAG;AAAA,IACH;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACzcA,GAAG,WAAWM,SAAe;"}