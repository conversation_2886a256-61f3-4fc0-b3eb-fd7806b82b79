<template>
  <!-- 秒杀活动卡片 - 苹果风格设计 -->
  <view class="flash-sale-card">
    <!-- 使用基础活动卡片 -->
    <ActivityCard 
      :item="item" 
      @favorite="$emit('favorite', item.id)"
      @action="$emit('action', { id: item.id, type: item.type, status: item.status })"
    >
      <!-- 秒杀特有信息插槽 -->
      <template #special-info>
        <view class="flash-sale-special">
          <!-- 价格区域 -->
          <view class="price-section">
            <view class="current-price">
              <text class="price-symbol">¥</text>
              <text class="price-value">{{item.salePrice}}</text>
            </view>
            <view class="original-price">
              <text class="price-label">原价</text>
              <text class="price-through">¥{{item.originalPrice}}</text>
            </view>
            <view class="discount-tag">
              <text class="discount-value">{{discountPercent}}折</text>
            </view>
          </view>
          
          <!-- 倒计时 -->
          <view class="countdown" v-if="item.status === 'ongoing' || item.status === 'upcoming'">
            <view class="countdown-header">
              <text class="countdown-title">{{item.status === 'ongoing' ? '距结束' : '距开始'}}</text>
              <text class="countdown-status">{{timeStatus}}</text>
            </view>
            <view class="countdown-timer">
              <view class="time-block">{{countdownHours}}</view>
              <view class="time-separator">:</view>
              <view class="time-block">{{countdownMinutes}}</view>
              <view class="time-separator">:</view>
              <view class="time-block">{{countdownSeconds}}</view>
            </view>
          </view>
          
          <!-- 库存进度 -->
          <view class="stock-progress">
            <view class="progress-header">
              <text class="progress-title">抢购进度</text>
              <text class="progress-status">{{item.soldCount}}/{{item.totalStock}}件</text>
            </view>
            <view class="progress-bar">
              <view class="progress-inner" :style="{width: progressWidth + '%'}"></view>
            </view>
            <view class="progress-tip" v-if="item.status === 'ongoing' && remainStock > 0">
              <text class="tip-text">仅剩{{remainStock}}件，抓紧抢购！</text>
            </view>
            <view class="progress-tip" v-else-if="item.status === 'ongoing' && remainStock <= 0">
              <text class="tip-text">已售罄，下次早点来哦~</text>
            </view>
          </view>
        </view>
      </template>
    </ActivityCard>
  </view>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue';
import ActivityCard from '../ActivityCard.vue';

const props = defineProps({
  item: {
    type: Object,
    required: true
  }
});

// 计算折扣百分比
const discountPercent = computed(() => {
  if (!props.item.salePrice || !props.item.originalPrice) return '';
  return Math.floor((props.item.salePrice / props.item.originalPrice) * 10);
});

// 计算进度条宽度
const progressWidth = computed(() => {
  if (!props.item.soldCount || !props.item.totalStock) return 0;
  return (props.item.soldCount / props.item.totalStock) * 100;
});

// 计算剩余库存
const remainStock = computed(() => {
  if (!props.item.soldCount || !props.item.totalStock) return 0;
  return props.item.totalStock - props.item.soldCount;
});

// 倒计时相关
const countdownHours = ref('00');
const countdownMinutes = ref('00');
const countdownSeconds = ref('00');
const timeStatus = ref('火热抢购中');
let countdownTimer = null;

// 更新倒计时
const updateCountdown = () => {
  if (!props.item.endTime) return;
  
  const now = new Date();
  const targetTime = props.item.status === 'ongoing' ? 
    new Date(props.item.endTime) : new Date(props.item.startTime);
  
  const timeDiff = targetTime - now;
  
  if (timeDiff <= 0) {
    // 倒计时结束
    countdownHours.value = '00';
    countdownMinutes.value = '00';
    countdownSeconds.value = '00';
    
    if (props.item.status === 'ongoing') {
      timeStatus.value = '已结束';
    } else {
      timeStatus.value = '已开始';
    }
    
    if (countdownTimer) {
      clearInterval(countdownTimer);
    }
    return;
  }
  
  // 计算时分秒
  const hours = Math.floor(timeDiff / (1000 * 60 * 60));
  const minutes = Math.floor((timeDiff % (1000 * 60 * 60)) / (1000 * 60));
  const seconds = Math.floor((timeDiff % (1000 * 60)) / 1000);
  
  // 格式化显示
  countdownHours.value = hours.toString().padStart(2, '0');
  countdownMinutes.value = minutes.toString().padStart(2, '0');
  countdownSeconds.value = seconds.toString().padStart(2, '0');
  
  // 更新状态文本
  if (props.item.status === 'ongoing') {
    if (hours > 0) {
      timeStatus.value = '火热抢购中';
    } else if (minutes > 30) {
      timeStatus.value = '火热抢购中';
    } else if (minutes > 10) {
      timeStatus.value = '即将结束';
    } else {
      timeStatus.value = '最后机会';
    }
  } else {
    if (hours > 24) {
      timeStatus.value = '即将开始';
    } else if (hours > 0) {
      timeStatus.value = '即将开始';
    } else if (minutes > 30) {
      timeStatus.value = '即将开始';
    } else {
      timeStatus.value = '马上开始';
    }
  }
};

// 初始化倒计时
onMounted(() => {
  updateCountdown();
  countdownTimer = setInterval(updateCountdown, 1000);
});

// 组件卸载时清除定时器
onUnmounted(() => {
  if (countdownTimer) {
    clearInterval(countdownTimer);
  }
});
</script>

<style scoped>
/* 秒杀活动卡片特有样式 */
.flash-sale-card {
  /* 继承基础卡片样式 */
}

/* 秒杀特有信息区域 */
.flash-sale-special {
  padding: 20rpx;
  background-color: rgba(255, 59, 48, 0.05);
  border-radius: 20rpx;
  margin-bottom: 20rpx;
}

/* 价格区域 */
.price-section {
  display: flex;
  align-items: baseline;
  margin-bottom: 20rpx;
}

.current-price {
  display: flex;
  align-items: baseline;
  color: #ff3b30;
  margin-right: 16rpx;
}

.price-symbol {
  font-size: 24rpx;
  font-weight: 500;
}

.price-value {
  font-size: 40rpx;
  font-weight: 700;
}

.original-price {
  display: flex;
  align-items: baseline;
  margin-right: 16rpx;
}

.price-label {
  font-size: 22rpx;
  color: #8e8e93;
  margin-right: 4rpx;
}

.price-through {
  font-size: 24rpx;
  color: #8e8e93;
  text-decoration: line-through;
}

.discount-tag {
  padding: 4rpx 10rpx;
  background-color: rgba(255, 59, 48, 0.1);
  border-radius: 10rpx;
}

.discount-value {
  font-size: 22rpx;
  color: #ff3b30;
  font-weight: 500;
}

/* 倒计时 */
.countdown {
  margin-bottom: 20rpx;
  border-top: 1rpx dashed rgba(255, 59, 48, 0.2);
  padding-top: 16rpx;
}

.countdown-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10rpx;
}

.countdown-title {
  font-size: 24rpx;
  color: #000000;
  font-weight: 500;
}

.countdown-status {
  font-size: 24rpx;
  color: #ff3b30;
  font-weight: 500;
}

.countdown-timer {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 10rpx;
}

.time-block {
  width: 60rpx;
  height: 60rpx;
  background-color: #1c1c1e;
  color: #ffffff;
  font-size: 28rpx;
  font-weight: 600;
  border-radius: 10rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.2);
}

.time-separator {
  margin: 0 8rpx;
  color: #1c1c1e;
  font-size: 28rpx;
  font-weight: 600;
}

/* 库存进度 */
.stock-progress {
  margin-top: 16rpx;
  border-top: 1rpx dashed rgba(255, 59, 48, 0.2);
  padding-top: 16rpx;
}

.progress-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10rpx;
}

.progress-title {
  font-size: 24rpx;
  color: #000000;
  font-weight: 500;
}

.progress-status {
  font-size: 24rpx;
  color: #ff3b30;
  font-weight: 500;
}

.progress-bar {
  height: 10rpx;
  background-color: rgba(255, 59, 48, 0.1);
  border-radius: 5rpx;
  overflow: hidden;
  margin-bottom: 10rpx;
}

.progress-inner {
  height: 100%;
  background-color: #ff3b30;
  border-radius: 5rpx;
  transition: width 0.3s ease;
}

.progress-tip {
  margin-top: 8rpx;
}

.tip-text {
  font-size: 22rpx;
  color: #ff3b30;
}
</style> 