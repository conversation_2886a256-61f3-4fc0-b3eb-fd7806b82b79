{"version": 3, "file": "MerchantAdButtons.js", "sources": ["components/MerchantAdButtons.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniComponent:/QzovVXNlcnMvQWRtaW5pc3RyYXRvci9EZXNrdG9wL-aWsOW7uuaWh-S7tuWkuS_no4Hlt57liY3lj7AvY29tcG9uZW50cy9NZXJjaGFudEFkQnV0dG9ucy52dWU"], "sourcesContent": ["<template>\n  <view class=\"merchant-ad-buttons\">\n    <!-- 商家入驻看广告按钮 -->\n    <view \n      class=\"ad-button join-button\" \n      @click=\"watchAdToJoin\"\n      v-if=\"showJoinButton\"\n    >\n      <view class=\"button-content\">\n        <view class=\"button-icon-wrapper\">\n          <image class=\"button-icon\" src=\"/static/images/tabbar/广告.png\"></image>\n          <view class=\"free-badge\">免费</view>\n        </view>\n        <view class=\"button-text\">\n          <text class=\"button-title\">看广告免费入驻</text>\n          <text class=\"button-desc\">观看广告获得1个月特权</text>\n        </view>\n      </view>\n    </view>\n\n    <!-- 商家续费看广告按钮 -->\n    <view \n      class=\"ad-button renew-button\" \n      @click=\"watchAdToRenew\"\n      v-if=\"showRenewButton\"\n    >\n      <view class=\"button-content\">\n        <view class=\"button-icon-wrapper\">\n          <image class=\"button-icon\" src=\"/static/images/tabbar/广告.png\"></image>\n          <view class=\"free-badge\">免费</view>\n        </view>\n        <view class=\"button-text\">\n          <text class=\"button-title\">看广告续费</text>\n          <text class=\"button-desc\">观看广告延长7天</text>\n        </view>\n      </view>\n    </view>\n\n    <!-- 信息置顶看广告按钮 -->\n    <view \n      class=\"ad-button top-button\" \n      @click=\"watchAdToTop\"\n      v-if=\"showTopButton\"\n      :class=\"{ 'disabled': isTopDisabled }\"\n    >\n      <view class=\"button-content\">\n        <view class=\"button-icon-wrapper\">\n          <image class=\"button-icon\" src=\"/static/images/tabbar/广告.png\"></image>\n          <view class=\"free-badge\" v-if=\"!isTopDisabled\">免费</view>\n        </view>\n        <view class=\"button-text\">\n          <text class=\"button-title\">{{ isTopDisabled ? '已置顶' : '看广告置顶' }}</text>\n          <text class=\"button-desc\">{{ isTopDisabled ? '置顶中' : '观看广告置顶2小时' }}</text>\n        </view>\n      </view>\n    </view>\n\n    <!-- 信息刷新看广告按钮 -->\n    <view \n      class=\"ad-button refresh-button\" \n      @click=\"watchAdToRefresh\"\n      v-if=\"showRefreshButton\"\n    >\n      <view class=\"button-content\">\n        <view class=\"button-icon-wrapper\">\n          <image class=\"button-icon\" src=\"/static/images/tabbar/广告.png\"></image>\n          <view class=\"free-badge\">免费</view>\n        </view>\n        <view class=\"button-text\">\n          <text class=\"button-title\">看广告刷新</text>\n          <text class=\"button-desc\">观看广告刷新到最新</text>\n        </view>\n      </view>\n    </view>\n\n    <!-- 广告次数提示 -->\n    <view class=\"ad-tips\" v-if=\"showTips\">\n      <view class=\"tips-content\">\n        <image class=\"tips-icon\" src=\"/static/images/tabbar/提示.png\"></image>\n        <text class=\"tips-text\">{{ tipsText }}</text>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script setup>\nimport { computed } from 'vue';\n\n// Props\nconst props = defineProps({\n  // 按钮类型控制\n  showJoinButton: {\n    type: Boolean,\n    default: false\n  },\n  showRenewButton: {\n    type: Boolean,\n    default: false\n  },\n  showTopButton: {\n    type: Boolean,\n    default: false\n  },\n  showRefreshButton: {\n    type: Boolean,\n    default: false\n  },\n  \n  // 状态控制\n  isTopDisabled: {\n    type: Boolean,\n    default: false\n  },\n  \n  // 数据传递\n  merchantId: {\n    type: String,\n    default: ''\n  },\n  infoId: {\n    type: String,\n    default: ''\n  },\n  carpoolId: {\n    type: String,\n    default: ''\n  },\n  \n  // 剩余次数\n  remainingAds: {\n    type: Object,\n    default: () => ({\n      join: 1,\n      renew: 2,\n      top: 3,\n      refresh: 5\n    })\n  },\n  \n  // 是否显示提示\n  showTips: {\n    type: Boolean,\n    default: true\n  }\n});\n\n// Emits\nconst emit = defineEmits([\n  'adSuccess',\n  'adFailed',\n  'adCancelled'\n]);\n\n// 计算属性\nconst tipsText = computed(() => {\n  const tips = [];\n  if (props.showJoinButton && props.remainingAds.join > 0) {\n    tips.push(`入驻${props.remainingAds.join}次`);\n  }\n  if (props.showRenewButton && props.remainingAds.renew > 0) {\n    tips.push(`续费${props.remainingAds.renew}次`);\n  }\n  if (props.showTopButton && props.remainingAds.top > 0) {\n    tips.push(`置顶${props.remainingAds.top}次`);\n  }\n  if (props.showRefreshButton && props.remainingAds.refresh > 0) {\n    tips.push(`刷新${props.remainingAds.refresh}次`);\n  }\n  \n  return tips.length > 0 ? `今日剩余：${tips.join('，')}` : '今日广告次数已用完';\n});\n\n// 方法\nconst watchAdToJoin = async () => {\n  if (props.remainingAds.join <= 0) {\n    uni.showToast({\n      title: '今日入驻次数已用完',\n      icon: 'none'\n    });\n    return;\n  }\n\n  try {\n    const result = await uni.showModal({\n      title: '看广告免费入驻',\n      content: '观看完整广告可获得1个月免费入驻特权，确定要观看吗？',\n      confirmText: '观看广告',\n      cancelText: '取消'\n    });\n\n    if (!result.confirm) {\n      emit('adCancelled', 'join');\n      return;\n    }\n\n    const rewardedAdService = require('@/services/rewardedAdService.js').default;\n    const success = await rewardedAdService.showRewardedVideoAd(\n      'merchant_join', \n      null, \n      props.merchantId,\n      null\n    );\n\n    if (success) {\n      uni.$on('rewardGranted', (data) => {\n        if (data.type === 'merchant_join') {\n          emit('adSuccess', {\n            type: 'join',\n            data: data.data\n          });\n        }\n      });\n    } else {\n      emit('adFailed', 'join');\n    }\n\n  } catch (error) {\n    console.error('看广告入驻失败:', error);\n    emit('adFailed', 'join');\n  }\n};\n\nconst watchAdToRenew = async () => {\n  if (props.remainingAds.renew <= 0) {\n    uni.showToast({\n      title: '本月续费次数已用完',\n      icon: 'none'\n    });\n    return;\n  }\n\n  try {\n    const result = await uni.showModal({\n      title: '看广告续费',\n      content: '观看完整广告可获得7天免费续费，确定要观看吗？',\n      confirmText: '观看广告',\n      cancelText: '取消'\n    });\n\n    if (!result.confirm) {\n      emit('adCancelled', 'renew');\n      return;\n    }\n\n    const rewardedAdService = require('@/services/rewardedAdService.js').default;\n    const success = await rewardedAdService.showRewardedVideoAd(\n      'merchant_renew', \n      null, \n      props.merchantId,\n      null\n    );\n\n    if (success) {\n      uni.$on('rewardGranted', (data) => {\n        if (data.type === 'merchant_renew') {\n          emit('adSuccess', {\n            type: 'renew',\n            data: data.data\n          });\n        }\n      });\n    } else {\n      emit('adFailed', 'renew');\n    }\n\n  } catch (error) {\n    console.error('看广告续费失败:', error);\n    emit('adFailed', 'renew');\n  }\n};\n\nconst watchAdToTop = async () => {\n  if (props.isTopDisabled) return;\n  \n  if (props.remainingAds.top <= 0) {\n    uni.showToast({\n      title: '今日置顶次数已用完',\n      icon: 'none'\n    });\n    return;\n  }\n\n  try {\n    const result = await uni.showModal({\n      title: '看广告置顶',\n      content: '观看完整广告可免费置顶信息2小时，确定要观看吗？',\n      confirmText: '观看广告',\n      cancelText: '取消'\n    });\n\n    if (!result.confirm) {\n      emit('adCancelled', 'top');\n      return;\n    }\n\n    const rewardedAdService = require('@/services/rewardedAdService.js').default;\n    const success = await rewardedAdService.showRewardedVideoAd(\n      'merchant_top', \n      props.carpoolId, \n      props.merchantId,\n      props.infoId\n    );\n\n    if (success) {\n      uni.$on('rewardGranted', (data) => {\n        if (data.type === 'merchant_top') {\n          emit('adSuccess', {\n            type: 'top',\n            data: data.data\n          });\n        }\n      });\n    } else {\n      emit('adFailed', 'top');\n    }\n\n  } catch (error) {\n    console.error('看广告置顶失败:', error);\n    emit('adFailed', 'top');\n  }\n};\n\nconst watchAdToRefresh = async () => {\n  if (props.remainingAds.refresh <= 0) {\n    uni.showToast({\n      title: '今日刷新次数已用完',\n      icon: 'none'\n    });\n    return;\n  }\n\n  try {\n    const result = await uni.showModal({\n      title: '看广告刷新',\n      content: '观看完整广告可免费刷新信息到最新，确定要观看吗？',\n      confirmText: '观看广告',\n      cancelText: '取消'\n    });\n\n    if (!result.confirm) {\n      emit('adCancelled', 'refresh');\n      return;\n    }\n\n    const rewardedAdService = require('@/services/rewardedAdService.js').default;\n    const success = await rewardedAdService.showRewardedVideoAd(\n      'merchant_refresh', \n      props.carpoolId, \n      props.merchantId,\n      props.infoId\n    );\n\n    if (success) {\n      uni.$on('rewardGranted', (data) => {\n        if (data.type === 'merchant_refresh') {\n          emit('adSuccess', {\n            type: 'refresh',\n            data: data.data\n          });\n        }\n      });\n    } else {\n      emit('adFailed', 'refresh');\n    }\n\n  } catch (error) {\n    console.error('看广告刷新失败:', error);\n    emit('adFailed', 'refresh');\n  }\n};\n</script>\n\n<style>\n.merchant-ad-buttons {\n  display: flex;\n  flex-direction: column;\n  gap: 20rpx;\n}\n\n.ad-button {\n  background: #fff;\n  border-radius: 16rpx;\n  padding: 24rpx;\n  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);\n  transition: all 0.3s ease;\n  position: relative;\n  overflow: hidden;\n}\n\n.ad-button:active {\n  transform: scale(0.98);\n}\n\n.ad-button.disabled {\n  opacity: 0.6;\n  pointer-events: none;\n}\n\n.ad-button::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  opacity: 0.1;\n  border-radius: 16rpx;\n}\n\n.join-button::before {\n  background: linear-gradient(135deg, #667eea, #764ba2);\n}\n\n.renew-button::before {\n  background: linear-gradient(135deg, #667eea, #764ba2);\n}\n\n.top-button::before {\n  background: linear-gradient(135deg, #ff9a00, #ff6a00);\n}\n\n.refresh-button::before {\n  background: linear-gradient(135deg, #52c41a, #73d13d);\n}\n\n.button-content {\n  display: flex;\n  align-items: center;\n  gap: 20rpx;\n  position: relative;\n  z-index: 1;\n}\n\n.button-icon-wrapper {\n  position: relative;\n  width: 80rpx;\n  height: 80rpx;\n  border-radius: 16rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  background: rgba(255, 255, 255, 0.9);\n  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);\n}\n\n.button-icon {\n  width: 40rpx;\n  height: 40rpx;\n}\n\n.free-badge {\n  position: absolute;\n  top: -8rpx;\n  right: -8rpx;\n  background: linear-gradient(135deg, #ff4757, #ff3742);\n  color: #fff;\n  font-size: 18rpx;\n  padding: 2rpx 8rpx;\n  border-radius: 10rpx;\n  box-shadow: 0 2rpx 8rpx rgba(255, 71, 87, 0.3);\n  z-index: 1;\n}\n\n.button-text {\n  flex: 1;\n}\n\n.button-title {\n  display: block;\n  font-size: 28rpx;\n  font-weight: 600;\n  color: #333;\n  margin-bottom: 8rpx;\n}\n\n.button-desc {\n  font-size: 22rpx;\n  color: #666;\n  line-height: 1.4;\n}\n\n.ad-tips {\n  background: #f8f9fa;\n  border-radius: 12rpx;\n  padding: 20rpx;\n  margin-top: 10rpx;\n}\n\n.tips-content {\n  display: flex;\n  align-items: center;\n  gap: 12rpx;\n}\n\n.tips-icon {\n  width: 24rpx;\n  height: 24rpx;\n  opacity: 0.6;\n}\n\n.tips-text {\n  font-size: 22rpx;\n  color: #666;\n  flex: 1;\n}\n</style>\n", "import Component from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/components/MerchantAdButtons.vue'\nwx.createComponent(Component)"], "names": ["computed", "uni", "Component"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyFA,UAAM,QAAQ;AA0Dd,UAAM,OAAO;AAOb,UAAM,WAAWA,cAAQ,SAAC,MAAM;AAC9B,YAAM,OAAO,CAAA;AACb,UAAI,MAAM,kBAAkB,MAAM,aAAa,OAAO,GAAG;AACvD,aAAK,KAAK,KAAK,MAAM,aAAa,IAAI,GAAG;AAAA,MAC1C;AACD,UAAI,MAAM,mBAAmB,MAAM,aAAa,QAAQ,GAAG;AACzD,aAAK,KAAK,KAAK,MAAM,aAAa,KAAK,GAAG;AAAA,MAC3C;AACD,UAAI,MAAM,iBAAiB,MAAM,aAAa,MAAM,GAAG;AACrD,aAAK,KAAK,KAAK,MAAM,aAAa,GAAG,GAAG;AAAA,MACzC;AACD,UAAI,MAAM,qBAAqB,MAAM,aAAa,UAAU,GAAG;AAC7D,aAAK,KAAK,KAAK,MAAM,aAAa,OAAO,GAAG;AAAA,MAC7C;AAED,aAAO,KAAK,SAAS,IAAI,QAAQ,KAAK,KAAK,GAAG,CAAC,KAAK;AAAA,IACtD,CAAC;AAGD,UAAM,gBAAgB,YAAY;AAChC,UAAI,MAAM,aAAa,QAAQ,GAAG;AAChCC,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AACD;AAAA,MACD;AAED,UAAI;AACF,cAAM,SAAS,MAAMA,cAAG,MAAC,UAAU;AAAA,UACjC,OAAO;AAAA,UACP,SAAS;AAAA,UACT,aAAa;AAAA,UACb,YAAY;AAAA,QAClB,CAAK;AAED,YAAI,CAAC,OAAO,SAAS;AACnB,eAAK,eAAe,MAAM;AAC1B;AAAA,QACD;AAED,cAAM,oBAAoB,QAAQ,iCAAiC,EAAE;AACrE,cAAM,UAAU,MAAM,kBAAkB;AAAA,UACtC;AAAA,UACA;AAAA,UACA,MAAM;AAAA,UACN;AAAA,QACN;AAEI,YAAI,SAAS;AACXA,wBAAAA,MAAI,IAAI,iBAAiB,CAAC,SAAS;AACjC,gBAAI,KAAK,SAAS,iBAAiB;AACjC,mBAAK,aAAa;AAAA,gBAChB,MAAM;AAAA,gBACN,MAAM,KAAK;AAAA,cACvB,CAAW;AAAA,YACF;AAAA,UACT,CAAO;AAAA,QACP,OAAW;AACL,eAAK,YAAY,MAAM;AAAA,QACxB;AAAA,MAEF,SAAQ,OAAO;AACdA,sBAAA,MAAA,MAAA,SAAA,2CAAc,YAAY,KAAK;AAC/B,aAAK,YAAY,MAAM;AAAA,MACxB;AAAA,IACH;AAEA,UAAM,iBAAiB,YAAY;AACjC,UAAI,MAAM,aAAa,SAAS,GAAG;AACjCA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AACD;AAAA,MACD;AAED,UAAI;AACF,cAAM,SAAS,MAAMA,cAAG,MAAC,UAAU;AAAA,UACjC,OAAO;AAAA,UACP,SAAS;AAAA,UACT,aAAa;AAAA,UACb,YAAY;AAAA,QAClB,CAAK;AAED,YAAI,CAAC,OAAO,SAAS;AACnB,eAAK,eAAe,OAAO;AAC3B;AAAA,QACD;AAED,cAAM,oBAAoB,QAAQ,iCAAiC,EAAE;AACrE,cAAM,UAAU,MAAM,kBAAkB;AAAA,UACtC;AAAA,UACA;AAAA,UACA,MAAM;AAAA,UACN;AAAA,QACN;AAEI,YAAI,SAAS;AACXA,wBAAAA,MAAI,IAAI,iBAAiB,CAAC,SAAS;AACjC,gBAAI,KAAK,SAAS,kBAAkB;AAClC,mBAAK,aAAa;AAAA,gBAChB,MAAM;AAAA,gBACN,MAAM,KAAK;AAAA,cACvB,CAAW;AAAA,YACF;AAAA,UACT,CAAO;AAAA,QACP,OAAW;AACL,eAAK,YAAY,OAAO;AAAA,QACzB;AAAA,MAEF,SAAQ,OAAO;AACdA,sBAAA,MAAA,MAAA,SAAA,2CAAc,YAAY,KAAK;AAC/B,aAAK,YAAY,OAAO;AAAA,MACzB;AAAA,IACH;AAEA,UAAM,eAAe,YAAY;AAC/B,UAAI,MAAM;AAAe;AAEzB,UAAI,MAAM,aAAa,OAAO,GAAG;AAC/BA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AACD;AAAA,MACD;AAED,UAAI;AACF,cAAM,SAAS,MAAMA,cAAG,MAAC,UAAU;AAAA,UACjC,OAAO;AAAA,UACP,SAAS;AAAA,UACT,aAAa;AAAA,UACb,YAAY;AAAA,QAClB,CAAK;AAED,YAAI,CAAC,OAAO,SAAS;AACnB,eAAK,eAAe,KAAK;AACzB;AAAA,QACD;AAED,cAAM,oBAAoB,QAAQ,iCAAiC,EAAE;AACrE,cAAM,UAAU,MAAM,kBAAkB;AAAA,UACtC;AAAA,UACA,MAAM;AAAA,UACN,MAAM;AAAA,UACN,MAAM;AAAA,QACZ;AAEI,YAAI,SAAS;AACXA,wBAAAA,MAAI,IAAI,iBAAiB,CAAC,SAAS;AACjC,gBAAI,KAAK,SAAS,gBAAgB;AAChC,mBAAK,aAAa;AAAA,gBAChB,MAAM;AAAA,gBACN,MAAM,KAAK;AAAA,cACvB,CAAW;AAAA,YACF;AAAA,UACT,CAAO;AAAA,QACP,OAAW;AACL,eAAK,YAAY,KAAK;AAAA,QACvB;AAAA,MAEF,SAAQ,OAAO;AACdA,sBAAA,MAAA,MAAA,SAAA,2CAAc,YAAY,KAAK;AAC/B,aAAK,YAAY,KAAK;AAAA,MACvB;AAAA,IACH;AAEA,UAAM,mBAAmB,YAAY;AACnC,UAAI,MAAM,aAAa,WAAW,GAAG;AACnCA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AACD;AAAA,MACD;AAED,UAAI;AACF,cAAM,SAAS,MAAMA,cAAG,MAAC,UAAU;AAAA,UACjC,OAAO;AAAA,UACP,SAAS;AAAA,UACT,aAAa;AAAA,UACb,YAAY;AAAA,QAClB,CAAK;AAED,YAAI,CAAC,OAAO,SAAS;AACnB,eAAK,eAAe,SAAS;AAC7B;AAAA,QACD;AAED,cAAM,oBAAoB,QAAQ,iCAAiC,EAAE;AACrE,cAAM,UAAU,MAAM,kBAAkB;AAAA,UACtC;AAAA,UACA,MAAM;AAAA,UACN,MAAM;AAAA,UACN,MAAM;AAAA,QACZ;AAEI,YAAI,SAAS;AACXA,wBAAAA,MAAI,IAAI,iBAAiB,CAAC,SAAS;AACjC,gBAAI,KAAK,SAAS,oBAAoB;AACpC,mBAAK,aAAa;AAAA,gBAChB,MAAM;AAAA,gBACN,MAAM,KAAK;AAAA,cACvB,CAAW;AAAA,YACF;AAAA,UACT,CAAO;AAAA,QACP,OAAW;AACL,eAAK,YAAY,SAAS;AAAA,QAC3B;AAAA,MAEF,SAAQ,OAAO;AACdA,sBAAA,MAAA,MAAA,SAAA,2CAAc,YAAY,KAAK;AAC/B,aAAK,YAAY,SAAS;AAAA,MAC3B;AAAA,IACH;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AChXA,GAAG,gBAAgBC,SAAS;"}