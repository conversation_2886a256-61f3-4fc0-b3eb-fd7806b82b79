"use strict";
const common_vendor = require("../../../../../common/vendor.js");
const common_services_ActivityService = require("../../../../../common/services/ActivityService.js");
const MarketingPromotionActions = () => "../../../components/MarketingPromotionActions.js";
const _sfc_main = {
  components: {
    MarketingPromotionActions
  },
  data() {
    return {
      formData: {
        name: "",
        images: [],
        originalPrice: "",
        flashPrice: "",
        stockTotal: "",
        purchaseLimit: "0",
        startTime: "",
        endTime: "",
        rules: "",
        description: "",
        detailImages: [],
        distributionSettings: {
          enabled: false,
          commission: 5,
          secondLevel: false,
          secondCommission: 2
        },
        type: common_services_ActivityService.ActivityType.FLASH
        // 设置活动类型为秒杀
      },
      timeError: "",
      hasMerchantDistribution: false
    };
  },
  computed: {
    // 计算折扣值
    discountValue() {
      if (!this.formData.originalPrice || !this.formData.flashPrice) {
        return "-";
      }
      const originalPrice = parseFloat(this.formData.originalPrice);
      const flashPrice = parseFloat(this.formData.flashPrice);
      if (isNaN(originalPrice) || isNaN(flashPrice) || originalPrice <= 0) {
        return "-";
      }
      const discount = (flashPrice / originalPrice * 10).toFixed(1);
      return `${discount}折`;
    }
  },
  onLoad() {
    this.checkMerchantDistribution();
  },
  methods: {
    // 返回上一页
    goBack() {
      common_vendor.index.navigateBack();
    },
    // 添加商品图片
    addImage() {
      common_vendor.index.chooseImage({
        count: 5 - this.formData.images.length,
        sizeType: ["compressed"],
        sourceType: ["album", "camera"],
        success: (res) => {
          this.formData.images = [...this.formData.images, ...res.tempFilePaths];
        }
      });
    },
    // 删除商品图片
    removeImage(index) {
      this.formData.images.splice(index, 1);
    },
    // 添加详情图片
    addDetailImage() {
      common_vendor.index.chooseImage({
        count: 9,
        sizeType: ["compressed"],
        sourceType: ["album", "camera"],
        success: (res) => {
          this.formData.detailImages = [...this.formData.detailImages, ...res.tempFilePaths];
        }
      });
    },
    // 删除详情图片
    removeDetailImage(index) {
      this.formData.detailImages.splice(index, 1);
    },
    // 开始时间变化
    startTimeChange(e) {
      this.formData.startTime = e.detail.value;
      this.validateTime();
    },
    // 结束时间变化
    endTimeChange(e) {
      this.formData.endTime = e.detail.value;
      this.validateTime();
    },
    // 验证时间
    validateTime() {
      if (this.formData.startTime && this.formData.endTime) {
        const startTime = new Date(this.formData.startTime.replace(/-/g, "/"));
        const endTime = new Date(this.formData.endTime.replace(/-/g, "/"));
        if (endTime <= startTime) {
          this.timeError = "结束时间必须晚于开始时间";
          return false;
        } else {
          this.timeError = "";
          return true;
        }
      }
      return true;
    },
    // 检查表单是否有效
    validateForm() {
      if (!this.formData.name) {
        common_vendor.index.showToast({
          title: "请输入活动名称",
          icon: "none"
        });
        return false;
      }
      if (this.formData.images.length === 0) {
        common_vendor.index.showToast({
          title: "请上传至少一张商品图片",
          icon: "none"
        });
        return false;
      }
      if (!this.formData.originalPrice) {
        common_vendor.index.showToast({
          title: "请输入商品原价",
          icon: "none"
        });
        return false;
      }
      if (!this.formData.flashPrice) {
        common_vendor.index.showToast({
          title: "请输入秒杀价格",
          icon: "none"
        });
        return false;
      }
      if (!this.formData.stockTotal) {
        common_vendor.index.showToast({
          title: "请输入活动库存",
          icon: "none"
        });
        return false;
      }
      if (!this.formData.startTime) {
        common_vendor.index.showToast({
          title: "请选择开始时间",
          icon: "none"
        });
        return false;
      }
      if (!this.formData.endTime) {
        common_vendor.index.showToast({
          title: "请选择结束时间",
          icon: "none"
        });
        return false;
      }
      return this.validateTime();
    },
    // 保存秒杀活动
    saveFlashSale() {
      if (!this.validateForm()) {
        return;
      }
      common_vendor.index.showLoading({
        title: "保存中..."
      });
      const activityData = {
        ...this.formData,
        coverImage: this.formData.images[0],
        // 设置封面图为第一张图片
        soldCount: 0,
        // 初始销量为0
        tag: "新品",
        // 默认标签
        merchantId: this.getMerchantId()
        // 获取商家ID
      };
      try {
        const createdActivity = common_services_ActivityService.ActivityService.createActivity(activityData);
        common_vendor.index.hideLoading();
        common_vendor.index.showToast({
          title: "活动创建成功",
          icon: "success"
        });
        this.formData.id = createdActivity.id;
        setTimeout(() => {
          common_vendor.index.showModal({
            title: "活动已创建",
            content: "您可以选择发布方式将活动展示给用户",
            confirmText: "立即发布",
            cancelText: "稍后发布",
            success: (res) => {
              if (res.confirm) {
              } else {
                setTimeout(() => {
                  common_vendor.index.navigateBack();
                }, 500);
              }
            }
          });
        }, 1e3);
      } catch (error) {
        common_vendor.index.__f__("error", "at subPackages/merchant-admin-marketing/pages/marketing/flash/create.vue:505", "创建活动失败:", error);
        common_vendor.index.hideLoading();
        common_vendor.index.showToast({
          title: "创建失败，请重试",
          icon: "none"
        });
      }
    },
    // 获取商家ID
    getMerchantId() {
      return "merchant_" + Math.floor(Math.random() * 1e3);
    },
    // 检查商家是否开通了分销功能
    checkMerchantDistribution() {
      setTimeout(() => {
        this.hasMerchantDistribution = true;
      }, 500);
    },
    // 更新分销设置
    updateDistributionSettings(settings) {
      this.formData.distributionSettings = settings;
    },
    // 处理活动发布操作完成
    handleActionCompleted(eventType, data) {
      var _a;
      common_vendor.index.__f__("log", "at subPackages/merchant-admin-marketing/pages/marketing/flash/create.vue:536", "活动发布操作完成", eventType, data);
      if (eventType === "adPublish" || eventType === "paidPublish") {
        const publishOptions = {
          publishType: eventType === "adPublish" ? "ad" : "paid",
          publishDuration: ((_a = data.option) == null ? void 0 : _a.duration) || "1天",
          publishAmount: data.amount || "0"
        };
        try {
          common_services_ActivityService.ActivityService.publishActivity(this.formData.id, publishOptions);
          common_vendor.index.showToast({
            title: "发布设置已保存",
            icon: "success"
          });
          setTimeout(() => {
            common_vendor.index.navigateBack();
          }, 1500);
        } catch (error) {
          common_vendor.index.__f__("error", "at subPackages/merchant-admin-marketing/pages/marketing/flash/create.vue:560", "发布活动失败:", error);
          common_vendor.index.showToast({
            title: "发布失败，请重试",
            icon: "none"
          });
        }
      }
    }
  }
};
if (!Array) {
  const _component_path = common_vendor.resolveComponent("path");
  const _component_svg = common_vendor.resolveComponent("svg");
  const _component_circle = common_vendor.resolveComponent("circle");
  const _component_distribution_setting = common_vendor.resolveComponent("distribution-setting");
  const _component_MarketingPromotionActions = common_vendor.resolveComponent("MarketingPromotionActions");
  (_component_path + _component_svg + _component_circle + _component_distribution_setting + _component_MarketingPromotionActions)();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.p({
      d: "M15 18L9 12L15 6",
      stroke: "white",
      ["stroke-width"]: "2",
      ["stroke-linecap"]: "round",
      ["stroke-linejoin"]: "round"
    }),
    b: common_vendor.p({
      width: "24",
      height: "24",
      viewBox: "0 0 24 24",
      fill: "none",
      xmlns: "http://www.w3.org/2000/svg"
    }),
    c: common_vendor.o((...args) => $options.goBack && $options.goBack(...args)),
    d: common_vendor.o((...args) => $options.saveFlashSale && $options.saveFlashSale(...args)),
    e: $data.formData.name,
    f: common_vendor.o(($event) => $data.formData.name = $event.detail.value),
    g: common_vendor.t($data.formData.name.length),
    h: common_vendor.f($data.formData.images, (image, index, i0) => {
      return {
        a: image,
        b: "004fbe38-3-" + i0 + "," + ("004fbe38-2-" + i0),
        c: "004fbe38-4-" + i0 + "," + ("004fbe38-2-" + i0),
        d: "004fbe38-2-" + i0,
        e: common_vendor.o(($event) => $options.removeImage(index), index),
        f: index
      };
    }),
    i: common_vendor.p({
      cx: "12",
      cy: "12",
      r: "10",
      fill: "rgba(0,0,0,0.5)"
    }),
    j: common_vendor.p({
      d: "M15 9L9 15M9 9L15 15",
      stroke: "white",
      ["stroke-width"]: "2",
      ["stroke-linecap"]: "round"
    }),
    k: common_vendor.p({
      width: "16",
      height: "16",
      viewBox: "0 0 24 24",
      fill: "none",
      xmlns: "http://www.w3.org/2000/svg"
    }),
    l: $data.formData.images.length < 5
  }, $data.formData.images.length < 5 ? {
    m: common_vendor.p({
      d: "M12 5V19M5 12H19",
      stroke: "#999999",
      ["stroke-width"]: "2",
      ["stroke-linecap"]: "round"
    }),
    n: common_vendor.p({
      width: "24",
      height: "24",
      viewBox: "0 0 24 24",
      fill: "none",
      xmlns: "http://www.w3.org/2000/svg"
    }),
    o: common_vendor.o((...args) => $options.addImage && $options.addImage(...args))
  } : {}, {
    p: $data.formData.originalPrice,
    q: common_vendor.o(($event) => $data.formData.originalPrice = $event.detail.value),
    r: $data.formData.flashPrice,
    s: common_vendor.o(($event) => $data.formData.flashPrice = $event.detail.value),
    t: common_vendor.t($options.discountValue),
    v: $data.formData.stockTotal,
    w: common_vendor.o(($event) => $data.formData.stockTotal = $event.detail.value),
    x: $data.formData.purchaseLimit,
    y: common_vendor.o(($event) => $data.formData.purchaseLimit = $event.detail.value),
    z: common_vendor.t($data.formData.startTime || "请选择开始时间"),
    A: $data.formData.startTime,
    B: common_vendor.o((...args) => $options.startTimeChange && $options.startTimeChange(...args)),
    C: common_vendor.t($data.formData.endTime || "请选择结束时间"),
    D: $data.formData.endTime,
    E: common_vendor.o((...args) => $options.endTimeChange && $options.endTimeChange(...args)),
    F: $data.timeError
  }, $data.timeError ? {
    G: common_vendor.t($data.timeError)
  } : {}, {
    H: $data.formData.rules,
    I: common_vendor.o(($event) => $data.formData.rules = $event.detail.value),
    J: common_vendor.t($data.formData.rules.length),
    K: $data.hasMerchantDistribution
  }, $data.hasMerchantDistribution ? {
    L: common_vendor.o($options.updateDistributionSettings),
    M: common_vendor.p({
      ["initial-settings"]: $data.formData.distributionSettings
    })
  } : {}, {
    N: $data.formData.description,
    O: common_vendor.o(($event) => $data.formData.description = $event.detail.value),
    P: common_vendor.t($data.formData.description.length),
    Q: common_vendor.f($data.formData.detailImages, (image, index, i0) => {
      return {
        a: image,
        b: "004fbe38-9-" + i0 + "," + ("004fbe38-8-" + i0),
        c: "004fbe38-10-" + i0 + "," + ("004fbe38-8-" + i0),
        d: "004fbe38-8-" + i0,
        e: common_vendor.o(($event) => $options.removeDetailImage(index), index),
        f: index
      };
    }),
    R: common_vendor.p({
      cx: "12",
      cy: "12",
      r: "10",
      fill: "rgba(0,0,0,0.5)"
    }),
    S: common_vendor.p({
      d: "M15 9L9 15M9 9L15 15",
      stroke: "white",
      ["stroke-width"]: "2",
      ["stroke-linecap"]: "round"
    }),
    T: common_vendor.p({
      width: "16",
      height: "16",
      viewBox: "0 0 24 24",
      fill: "none",
      xmlns: "http://www.w3.org/2000/svg"
    }),
    U: common_vendor.p({
      d: "M12 5V19M5 12H19",
      stroke: "#999999",
      ["stroke-width"]: "2",
      ["stroke-linecap"]: "round"
    }),
    V: common_vendor.p({
      width: "24",
      height: "24",
      viewBox: "0 0 24 24",
      fill: "none",
      xmlns: "http://www.w3.org/2000/svg"
    }),
    W: common_vendor.o((...args) => $options.addDetailImage && $options.addDetailImage(...args)),
    X: common_vendor.p({
      d: "M12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22Z",
      stroke: "#FF7600",
      ["stroke-width"]: "2",
      ["stroke-linecap"]: "round",
      ["stroke-linejoin"]: "round"
    }),
    Y: common_vendor.p({
      d: "M12 16V12",
      stroke: "#FF7600",
      ["stroke-width"]: "2",
      ["stroke-linecap"]: "round",
      ["stroke-linejoin"]: "round"
    }),
    Z: common_vendor.p({
      d: "M12 8H12.01",
      stroke: "#FF7600",
      ["stroke-width"]: "2",
      ["stroke-linecap"]: "round",
      ["stroke-linejoin"]: "round"
    }),
    aa: common_vendor.p({
      width: "24",
      height: "24",
      viewBox: "0 0 24 24",
      fill: "none",
      xmlns: "http://www.w3.org/2000/svg"
    }),
    ab: common_vendor.p({
      d: "M12 2L15.09 8.26L22 9.27L17 14.14L18.18 21.02L12 17.77L5.82 21.02L7 14.14L2 9.27L8.91 8.26L12 2Z",
      stroke: "#FF7600",
      ["stroke-width"]: "2",
      ["stroke-linecap"]: "round",
      ["stroke-linejoin"]: "round"
    }),
    ac: common_vendor.p({
      width: "24",
      height: "24",
      viewBox: "0 0 24 24",
      fill: "none",
      xmlns: "http://www.w3.org/2000/svg"
    }),
    ad: common_vendor.p({
      d: "M8 7V3M16 7V3M7 11H17M5 21H19C20.1046 21 21 20.1046 21 19V7C21 5.89543 20.1046 5 19 5H5C3.89543 5 3 5.89543 3 7V19C3 20.1046 3.89543 21 5 21Z",
      stroke: "#FF7600",
      ["stroke-width"]: "2",
      ["stroke-linecap"]: "round",
      ["stroke-linejoin"]: "round"
    }),
    ae: common_vendor.p({
      width: "24",
      height: "24",
      viewBox: "0 0 24 24",
      fill: "none",
      xmlns: "http://www.w3.org/2000/svg"
    }),
    af: common_vendor.o($options.handleActionCompleted),
    ag: common_vendor.p({
      activityId: $data.formData.id || "",
      activityType: "flash",
      showActions: ["publish"],
      publishModeOnly: true
    })
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-004fbe38"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../../../.sourcemap/mp-weixin/subPackages/merchant-admin-marketing/pages/marketing/flash/create.js.map
