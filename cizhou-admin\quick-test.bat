@echo off
chcp 65001 >nul

echo 🧪 磁州生活网后台管理系统快速测试
echo =====================================

echo.
echo 📋 测试步骤：
echo    1. 环境检查
echo    2. 后端服务测试
echo    3. 前端服务测试
echo    4. 登录功能测试

echo.
echo 🔍 第一步：环境检查
echo ---------------------

echo 检查 Node.js...
node --version 2>nul
if errorlevel 1 (
    echo ❌ Node.js 未安装，请访问 https://nodejs.org/ 下载安装
    goto :end
) else (
    echo ✅ Node.js 已安装
)

echo 检查 Docker...
docker --version 2>nul
if errorlevel 1 (
    echo ❌ Docker 未安装，请访问 https://www.docker.com/products/docker-desktop/ 下载安装
    goto :end
) else (
    echo ✅ Docker 已安装
)

echo 检查 Docker 运行状态...
docker info >nul 2>&1
if errorlevel 1 (
    echo ❌ Docker 未运行，请启动 Docker Desktop
    goto :end
) else (
    echo ✅ Docker 运行正常
)

echo.
echo 🚀 第二步：启动后端服务
echo ----------------------

echo 正在启动后端基础服务...
cd backend
docker-compose -f docker-compose.dev.yml up -d mysql redis nacos

if errorlevel 1 (
    echo ❌ 后端服务启动失败
    goto :end
)

echo 等待服务启动...
timeout /t 30 /nobreak >nul

echo 初始化数据库...
docker exec -i cizhou-mysql-dev mysql -uroot -pcizhou123456 < sql\init.sql

echo ✅ 后端服务启动完成

echo.
echo 🎨 第三步：检查前端环境
echo ----------------------

cd ..\frontend

echo 检查前端依赖...
if not exist "node_modules" (
    echo 安装前端依赖...
    npm config set registry https://registry.npmmirror.com/
    npm install
    
    if errorlevel 1 (
        echo ❌ 前端依赖安装失败
        goto :end
    )
)

echo ✅ 前端环境准备完成

echo.
echo 📊 第四步：服务状态检查
echo ----------------------

echo 检查 MySQL 连接...
docker exec cizhou-mysql-dev mysqladmin ping -h"localhost" --silent
if errorlevel 1 (
    echo ❌ MySQL 连接失败
) else (
    echo ✅ MySQL 连接正常
)

echo 检查 Redis 连接...
docker exec cizhou-redis-dev redis-cli -a cizhou123456 ping >nul 2>&1
if errorlevel 1 (
    echo ❌ Redis 连接失败
) else (
    echo ✅ Redis 连接正常
)

echo 检查 Nacos 服务...
curl -s http://localhost:8848/nacos >nul 2>&1
if errorlevel 1 (
    echo ❌ Nacos 服务不可用
) else (
    echo ✅ Nacos 服务正常
)

echo.
echo 🎉 测试完成！
echo =============

echo.
echo 📋 测试结果总结：
echo    ✅ 环境检查通过
echo    ✅ 后端服务启动成功
echo    ✅ 前端环境准备完成
echo    ✅ 基础服务连接正常

echo.
echo 🚀 下一步操作：
echo    1. 启动认证服务：在 IDE 中运行 cizhou-auth
echo    2. 启动网关服务：在 IDE 中运行 cizhou-gateway
echo    3. 启动前端服务：运行 frontend\start-dev.bat
echo    4. 访问管理后台：http://localhost:3000
echo    5. 使用账号登录：admin / admin123

echo.
echo 📞 如需帮助，请查看 TESTING_GUIDE.md 文档

:end
echo.
pause
