"use strict";
const common_vendor = require("../../../../common/vendor.js");
const SvgIcon = () => "./SvgIcon.js";
const _sfc_main = {
  name: "IconButton",
  components: {
    SvgIcon
  },
  props: {
    icon: {
      type: String,
      required: true
    },
    text: {
      type: String,
      default: ""
    },
    type: {
      type: String,
      default: "default",
      // default, primary, success, warning, danger
      validator: (value) => ["default", "primary", "success", "warning", "danger"].includes(value)
    },
    size: {
      type: String,
      default: "medium",
      // small, medium, large
      validator: (value) => ["small", "medium", "large"].includes(value)
    }
  },
  computed: {
    iconSize() {
      const sizes = {
        small: 16,
        medium: 20,
        large: 24
      };
      return sizes[this.size] || 20;
    },
    iconColor() {
      if (this.type === "default")
        return "#666";
      return "white";
    }
  },
  methods: {
    onClick() {
      this.$emit("click");
    }
  }
};
if (!Array) {
  const _component_svg_icon = common_vendor.resolveComponent("svg-icon");
  _component_svg_icon();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.p({
      name: $props.icon,
      size: $options.iconSize,
      color: $options.iconColor
    }),
    b: $props.text
  }, $props.text ? {
    c: common_vendor.t($props.text)
  } : {}, {
    d: common_vendor.n($props.type),
    e: common_vendor.n($props.size),
    f: common_vendor.o((...args) => $options.onClick && $options.onClick(...args))
  });
}
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createComponent(Component);
//# sourceMappingURL=../../../../../.sourcemap/mp-weixin/subPackages/merchant-admin/components/ui/IconButton.js.map
