{"version": 3, "file": "merchant-apply.js", "sources": ["subPackages/distribution/pages/merchant-apply.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/c3ViUGFja2FnZXNcZGlzdHJpYnV0aW9uXHBhZ2VzXG1lcmNoYW50LWFwcGx5LnZ1ZQ"], "sourcesContent": ["<template>\r\n  <view class=\"merchant-apply-container\">\r\n    <!-- 自定义导航栏 -->\r\n    <view class=\"navbar\">\r\n      <view class=\"navbar-back\" @click=\"goBack\">\r\n        <view class=\"back-icon\"></view>\r\n      </view>\r\n      <text class=\"navbar-title\">申请商家专属分销</text>\r\n      <view class=\"navbar-right\">\r\n        <view class=\"help-icon\" @click=\"showHelp\">?</view>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 专属分销说明 -->\r\n    <view class=\"intro-card\">\r\n      <view class=\"intro-header\">\r\n        <text class=\"intro-title\">商家专属分销特权</text>\r\n      </view>\r\n      \r\n      <view class=\"benefits-list\">\r\n        <view class=\"benefit-item\">\r\n          <view class=\"benefit-icon higher\"></view>\r\n          <view class=\"benefit-info\">\r\n            <text class=\"benefit-title\">更高佣金</text>\r\n            <text class=\"benefit-desc\">享受比普通分销员更高的佣金比例</text>\r\n          </view>\r\n        </view>\r\n        \r\n        <view class=\"benefit-item\">\r\n          <view class=\"benefit-icon priority\"></view>\r\n          <view class=\"benefit-info\">\r\n            <text class=\"benefit-title\">优先推广</text>\r\n            <text class=\"benefit-desc\">获得商家新品优先推广权</text>\r\n          </view>\r\n        </view>\r\n        \r\n        <view class=\"benefit-item\">\r\n          <view class=\"benefit-icon exclusive\"></view>\r\n          <view class=\"benefit-info\">\r\n            <text class=\"benefit-title\">专属活动</text>\r\n            <text class=\"benefit-desc\">参与商家专属分销员活动</text>\r\n          </view>\r\n        </view>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 商家列表 -->\r\n    <view class=\"section-header\">\r\n      <text class=\"section-title\">选择商家申请</text>\r\n    </view>\r\n    \r\n    <!-- 搜索栏 -->\r\n    <view class=\"search-bar\">\r\n      <view class=\"search-input-wrap\">\r\n        <view class=\"search-icon\"></view>\r\n        <input \r\n          class=\"search-input\" \r\n          type=\"text\" \r\n          v-model=\"searchKeyword\" \r\n          placeholder=\"搜索商家名称\" \r\n          confirm-type=\"search\"\r\n          @confirm=\"searchMerchants\"\r\n        />\r\n        <view class=\"clear-icon\" v-if=\"searchKeyword\" @click=\"clearSearch\"></view>\r\n      </view>\r\n      <view class=\"search-btn\" @click=\"searchMerchants\">搜索</view>\r\n    </view>\r\n    \r\n    <!-- 商家列表 -->\r\n    <view class=\"merchants-list\" v-if=\"merchants.length > 0\">\r\n      <view \r\n        v-for=\"(merchant, index) in merchants\" \r\n        :key=\"index\" \r\n        class=\"merchant-card\"\r\n        @click=\"selectMerchant(merchant)\"\r\n      >\r\n        <view class=\"merchant-info\">\r\n          <image class=\"merchant-logo\" :src=\"merchant.logo\" mode=\"aspectFill\"></image>\r\n          <view class=\"merchant-details\">\r\n            <text class=\"merchant-name\">{{merchant.name}}</text>\r\n            <view class=\"merchant-meta\">\r\n              <text class=\"merchant-category\">{{merchant.category}}</text>\r\n              <text class=\"merchant-commission\">佣金比例 {{merchant.commissionRate}}%</text>\r\n            </view>\r\n          </view>\r\n          <view class=\"merchant-status\" :class=\"{ 'applied': merchant.applied }\">\r\n            <text>{{merchant.applied ? '已申请' : '申请'}}</text>\r\n          </view>\r\n        </view>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 空状态 -->\r\n    <view class=\"empty-state\" v-else>\r\n      <image class=\"empty-image\" src=\"/static/images/empty-merchants.png\" mode=\"aspectFit\"></image>\r\n      <text class=\"empty-text\">暂无可申请的商家</text>\r\n    </view>\r\n    \r\n    <!-- 加载更多 -->\r\n    <view class=\"load-more\" v-if=\"hasMoreData && merchants.length > 0\">\r\n      <text v-if=\"loading\">加载中...</text>\r\n      <text v-else @click=\"loadMore\">点击加载更多</text>\r\n    </view>\r\n    \r\n    <!-- 申请弹窗 -->\r\n    <view class=\"apply-modal\" v-if=\"showApplyModal\">\r\n      <view class=\"modal-mask\" @click=\"closeApplyModal\"></view>\r\n      <view class=\"modal-content\">\r\n        <view class=\"modal-header\">\r\n          <text class=\"modal-title\">申请成为专属分销员</text>\r\n          <view class=\"close-icon\" @click=\"closeApplyModal\"></view>\r\n        </view>\r\n        \r\n        <view class=\"modal-merchant\">\r\n          <image class=\"merchant-logo\" :src=\"selectedMerchant.logo\" mode=\"aspectFill\"></image>\r\n          <text class=\"merchant-name\">{{selectedMerchant.name}}</text>\r\n        </view>\r\n        \r\n        <view class=\"form-content\">\r\n          <view class=\"form-item\">\r\n            <text class=\"form-label\">申请理由</text>\r\n            <textarea class=\"form-textarea\" v-model=\"formData.reason\" placeholder=\"请简要描述您申请成为该商家专属分销员的理由\" maxlength=\"200\" />\r\n            <text class=\"textarea-counter\">{{formData.reason.length}}/200</text>\r\n          </view>\r\n          \r\n          <view class=\"form-item\">\r\n            <text class=\"form-label\">销售经验</text>\r\n            <textarea class=\"form-textarea\" v-model=\"formData.experience\" placeholder=\"请简要描述您的销售经验或相关背景\" maxlength=\"200\" />\r\n            <text class=\"textarea-counter\">{{formData.experience.length}}/200</text>\r\n          </view>\r\n          \r\n          <view class=\"form-item\">\r\n            <text class=\"form-label\">联系方式</text>\r\n            <input class=\"form-input\" type=\"number\" v-model=\"formData.contact\" placeholder=\"请输入您的手机号\" maxlength=\"11\" />\r\n          </view>\r\n        </view>\r\n        \r\n        <view class=\"modal-footer\">\r\n          <button class=\"cancel-btn\" @click=\"closeApplyModal\">取消</button>\r\n          <button class=\"submit-btn\" :disabled=\"!canSubmit\" :class=\"{ 'disabled': !canSubmit }\" @click=\"submitApplication\">提交申请</button>\r\n        </view>\r\n      </view>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script setup>\r\nimport { ref, reactive, computed, onMounted } from 'vue';\r\nimport distributionService from '@/utils/distributionService';\r\n\r\n// 搜索关键词\r\nconst searchKeyword = ref('');\r\n\r\n// 商家列表\r\nconst merchants = ref([]);\r\n\r\n// 分页信息\r\nconst pagination = reactive({\r\n  page: 1,\r\n  pageSize: 10,\r\n  total: 0,\r\n  totalPages: 0\r\n});\r\n\r\n// 是否有更多数据\r\nconst hasMoreData = ref(false);\r\n\r\n// 是否正在加载\r\nconst loading = ref(false);\r\n\r\n// 申请弹窗\r\nconst showApplyModal = ref(false);\r\n\r\n// 选中的商家\r\nconst selectedMerchant = ref({});\r\n\r\n// 表单数据\r\nconst formData = reactive({\r\n  reason: '',\r\n  experience: '',\r\n  contact: ''\r\n});\r\n\r\n// 是否可以提交\r\nconst canSubmit = computed(() => {\r\n  return formData.reason.trim() !== '' && \r\n         formData.experience.trim() !== '' && \r\n         formData.contact.trim() !== '' &&\r\n         formData.contact.length === 11;\r\n});\r\n\r\n// 页面加载\r\nonMounted(async () => {\r\n  // 获取商家列表\r\n  await getMerchants();\r\n});\r\n\r\n// 获取商家列表\r\nconst getMerchants = async (loadMore = false) => {\r\n  if (loading.value) return;\r\n  \r\n  try {\r\n    loading.value = true;\r\n    \r\n    const page = loadMore ? pagination.page + 1 : 1;\r\n    \r\n    const result = await distributionService.getMerchantsWithDistribution({\r\n      page,\r\n      pageSize: pagination.pageSize,\r\n      keyword: searchKeyword.value\r\n    });\r\n    \r\n    if (result) {\r\n      // 更新商家列表\r\n      if (loadMore) {\r\n        merchants.value = [...merchants.value, ...result.list];\r\n      } else {\r\n        merchants.value = result.list;\r\n      }\r\n      \r\n      // 更新分页信息\r\n      pagination.page = page;\r\n      pagination.total = result.pagination.total;\r\n      pagination.totalPages = result.pagination.totalPages;\r\n      \r\n      // 更新是否有更多数据\r\n      hasMoreData.value = pagination.page < pagination.totalPages;\r\n    }\r\n  } catch (error) {\r\n    console.error('获取商家列表失败', error);\r\n    uni.showToast({\r\n      title: '获取商家列表失败',\r\n      icon: 'none'\r\n    });\r\n  } finally {\r\n    loading.value = false;\r\n  }\r\n};\r\n\r\n// 搜索商家\r\nconst searchMerchants = () => {\r\n  getMerchants();\r\n};\r\n\r\n// 清除搜索\r\nconst clearSearch = () => {\r\n  searchKeyword.value = '';\r\n  getMerchants();\r\n};\r\n\r\n// 加载更多\r\nconst loadMore = () => {\r\n  if (hasMoreData.value && !loading.value) {\r\n    getMerchants(true);\r\n  }\r\n};\r\n\r\n// 选择商家\r\nconst selectMerchant = (merchant) => {\r\n  if (merchant.applied) {\r\n    uni.showToast({\r\n      title: '您已申请该商家',\r\n      icon: 'none'\r\n    });\r\n    return;\r\n  }\r\n  \r\n  selectedMerchant.value = merchant;\r\n  showApplyModal.value = true;\r\n};\r\n\r\n// 关闭申请弹窗\r\nconst closeApplyModal = () => {\r\n  showApplyModal.value = false;\r\n};\r\n\r\n// 提交申请\r\nconst submitApplication = async () => {\r\n  if (!canSubmit.value) return;\r\n  \r\n  try {\r\n    uni.showLoading({\r\n      title: '提交中...',\r\n      mask: true\r\n    });\r\n    \r\n    const result = await distributionService.applyMerchantDistributor({\r\n      merchantId: selectedMerchant.value.id,\r\n      reason: formData.reason,\r\n      experience: formData.experience,\r\n      contact: formData.contact\r\n    });\r\n    \r\n    uni.hideLoading();\r\n    \r\n    if (result.success) {\r\n      uni.showModal({\r\n        title: '申请提交成功',\r\n        content: '您的专属分销员申请已提交，请耐心等待商家审核。',\r\n        showCancel: false,\r\n        success: () => {\r\n          // 更新商家状态\r\n          const index = merchants.value.findIndex(m => m.id === selectedMerchant.value.id);\r\n          if (index !== -1) {\r\n            merchants.value[index].applied = true;\r\n          }\r\n          \r\n          // 关闭弹窗\r\n          closeApplyModal();\r\n          \r\n          // 清空表单\r\n          formData.reason = '';\r\n          formData.experience = '';\r\n          formData.contact = '';\r\n        }\r\n      });\r\n    } else {\r\n      uni.showModal({\r\n        title: '申请提交失败',\r\n        content: result.message || '请稍后再试',\r\n        showCancel: false\r\n      });\r\n    }\r\n  } catch (error) {\r\n    uni.hideLoading();\r\n    console.error('提交申请失败', error);\r\n    uni.showToast({\r\n      title: '提交申请失败',\r\n      icon: 'none'\r\n    });\r\n  }\r\n};\r\n\r\n// 返回上一页\r\nconst goBack = () => {\r\n  uni.navigateBack();\r\n};\r\n\r\n// 显示帮助\r\nconst showHelp = () => {\r\n  uni.showModal({\r\n    title: '商家专属分销帮助',\r\n    content: '成为商家专属分销员后，您可以获得更高的佣金比例和更多专属特权。选择您感兴趣的商家，提交申请后等待商家审核。',\r\n    showCancel: false\r\n  });\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.merchant-apply-container {\r\n  min-height: 100vh;\r\n  background-color: #F5F7FA;\r\n  padding-bottom: 30rpx;\r\n}\r\n\r\n/* 导航栏样式 */\r\n.navbar {\r\n  position: sticky;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  background: linear-gradient(135deg, #A764CA, #6B0FBE);\r\n  color: #fff;\r\n  padding: 88rpx 32rpx 30rpx;\r\n  display: flex;\r\n  align-items: center;\r\n  z-index: 100;\r\n  box-shadow: 0 4rpx 20rpx rgba(107, 15, 190, 0.15);\r\n}\r\n\r\n.navbar-back {\r\n  width: 72rpx;\r\n  height: 72rpx;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.back-icon {\r\n  width: 24rpx;\r\n  height: 24rpx;\r\n  border-left: 4rpx solid #fff;\r\n  border-bottom: 4rpx solid #fff;\r\n  transform: rotate(45deg);\r\n}\r\n\r\n.navbar-title {\r\n  flex: 1;\r\n  text-align: center;\r\n  font-size: 36rpx;\r\n  font-weight: 600;\r\n  letter-spacing: 1rpx;\r\n}\r\n\r\n.navbar-right {\r\n  width: 72rpx;\r\n  height: 72rpx;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.help-icon {\r\n  width: 48rpx;\r\n  height: 48rpx;\r\n  border-radius: 24rpx;\r\n  background: rgba(255, 255, 255, 0.2);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  font-size: 28rpx;\r\n  font-weight: bold;\r\n}\r\n\r\n/* 专属分销说明 */\r\n.intro-card {\r\n  margin: 30rpx;\r\n  background: #FFFFFF;\r\n  border-radius: 20rpx;\r\n  padding: 30rpx;\r\n  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n.intro-header {\r\n  margin-bottom: 30rpx;\r\n}\r\n\r\n.intro-title {\r\n  font-size: 32rpx;\r\n  font-weight: 600;\r\n  color: #333;\r\n  text-align: center;\r\n}\r\n\r\n.benefits-list {\r\n  margin-bottom: 20rpx;\r\n}\r\n\r\n.benefit-item {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-bottom: 30rpx;\r\n}\r\n\r\n.benefit-item:last-child {\r\n  margin-bottom: 0;\r\n}\r\n\r\n.benefit-icon {\r\n  width: 80rpx;\r\n  height: 80rpx;\r\n  margin-right: 20rpx;\r\n  background-size: contain;\r\n  background-repeat: no-repeat;\r\n  background-position: center;\r\n  border-radius: 10rpx;\r\n  position: relative;\r\n}\r\n\r\n.benefit-icon.higher {\r\n  background-color: #FF9500;\r\n}\r\n\r\n.benefit-icon.higher::before {\r\n  content: '';\r\n  position: absolute;\r\n  width: 50rpx;\r\n  height: 50rpx;\r\n  top: 15rpx;\r\n  left: 15rpx;\r\n  background-color: white;\r\n  mask-image: url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath d='M16,6L18.29,8.29L13.41,13.17L9.41,9.17L2,16.59L3.41,18L9.41,12L13.41,16L19.71,9.71L22,12V6H16Z' fill='white'/%3E%3C/svg%3E\");\r\n  mask-repeat: no-repeat;\r\n  mask-position: center;\r\n  mask-size: contain;\r\n  -webkit-mask-image: url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath d='M16,6L18.29,8.29L13.41,13.17L9.41,9.17L2,16.59L3.41,18L9.41,12L13.41,16L19.71,9.71L22,12V6H16Z' fill='white'/%3E%3C/svg%3E\");\r\n  -webkit-mask-repeat: no-repeat;\r\n  -webkit-mask-position: center;\r\n  -webkit-mask-size: contain;\r\n}\r\n\r\n.benefit-icon.priority {\r\n  background-color: #34C759;\r\n}\r\n\r\n.benefit-icon.priority::before {\r\n  content: '';\r\n  position: absolute;\r\n  width: 50rpx;\r\n  height: 50rpx;\r\n  top: 15rpx;\r\n  left: 15rpx;\r\n  background-color: white;\r\n  mask-image: url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath d='M13,19H14A1,1 0 0,1 15,20H22V22H15A1,1 0 0,1 14,23H10A1,1 0 0,1 9,22H2V20H9A1,1 0 0,1 10,19H11V17H4A1,1 0 0,1 3,16V12A1,1 0 0,1 4,11H20A1,1 0 0,1 21,12V16A1,1 0 0,1 20,17H13V19M4,3H20A1,1 0 0,1 21,4V8A1,1 0 0,1 20,9H4A1,1 0 0,1 3,8V4A1,1 0 0,1 4,3M9,7H10V5H9V7M9,15H10V13H9V15M5,5V7H7V5H5M5,13V15H7V13H5Z' fill='white'/%3E%3C/svg%3E\");\r\n  mask-repeat: no-repeat;\r\n  mask-position: center;\r\n  mask-size: contain;\r\n  -webkit-mask-image: url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath d='M13,19H14A1,1 0 0,1 15,20H22V22H15A1,1 0 0,1 14,23H10A1,1 0 0,1 9,22H2V20H9A1,1 0 0,1 10,19H11V17H4A1,1 0 0,1 3,16V12A1,1 0 0,1 4,11H20A1,1 0 0,1 21,12V16A1,1 0 0,1 20,17H13V19M4,3H20A1,1 0 0,1 21,4V8A1,1 0 0,1 20,9H4A1,1 0 0,1 3,8V4A1,1 0 0,1 4,3M9,7H10V5H9V7M9,15H10V13H9V15M5,5V7H7V5H5M5,13V15H7V13H5Z' fill='white'/%3E%3C/svg%3E\");\r\n  -webkit-mask-repeat: no-repeat;\r\n  -webkit-mask-position: center;\r\n  -webkit-mask-size: contain;\r\n}\r\n\r\n.benefit-icon.exclusive {\r\n  background-color: #6B0FBE;\r\n}\r\n\r\n.benefit-icon.exclusive::before {\r\n  content: '';\r\n  position: absolute;\r\n  width: 50rpx;\r\n  height: 50rpx;\r\n  top: 15rpx;\r\n  left: 15rpx;\r\n  background-color: white;\r\n  mask-image: url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath d='M12,8L10.67,8.09C9.81,7.07 7.4,4.5 5,4.5C5,4.5 3.03,7.46 4.96,11.41C4.41,12.24 4.07,12.67 4,13.66L2.07,13.95L2.28,14.93L4.04,14.67L4.18,15.38L2.61,16.32L3.08,17.21L4.53,16.32L5.5,17.94L4.8,19.15L5.74,19.85L6.75,18.22L8.07,18.22L8.07,20.39L9.05,20.39L9.16,18.22L10.41,18.22L11.3,19.85L12.23,19.15L11.45,17.86L12.47,16.32L13.93,17.21L14.39,16.32L12.83,15.38L12.96,14.67L14.73,14.93L14.92,13.95L13.03,13.66C12.96,12.67 12.59,12.24 12.04,11.41C13.97,7.46 12,4.5 12,4.5C9.6,4.5 7.18,7.07 6.32,8.09L5,8L5.01,9H6.34C6.63,9.32 7.3,10.13 7.46,10.13C7.61,10.13 8.12,9.44 8.46,9H12L12,8Z' fill='white'/%3E%3C/svg%3E\");\r\n  mask-repeat: no-repeat;\r\n  mask-position: center;\r\n  mask-size: contain;\r\n  -webkit-mask-image: url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath d='M12,8L10.67,8.09C9.81,7.07 7.4,4.5 5,4.5C5,4.5 3.03,7.46 4.96,11.41C4.41,12.24 4.07,12.67 4,13.66L2.07,13.95L2.28,14.93L4.04,14.67L4.18,15.38L2.61,16.32L3.08,17.21L4.53,16.32L5.5,17.94L4.8,19.15L5.74,19.85L6.75,18.22L8.07,18.22L8.07,20.39L9.05,20.39L9.16,18.22L10.41,18.22L11.3,19.85L12.23,19.15L11.45,17.86L12.47,16.32L13.93,17.21L14.39,16.32L12.83,15.38L12.96,14.67L14.73,14.93L14.92,13.95L13.03,13.66C12.96,12.67 12.59,12.24 12.04,11.41C13.97,7.46 12,4.5 12,4.5C9.6,4.5 7.18,7.07 6.32,8.09L5,8L5.01,9H6.34C6.63,9.32 7.3,10.13 7.46,10.13C7.61,10.13 8.12,9.44 8.46,9H12L12,8Z' fill='white'/%3E%3C/svg%3E\");\r\n  -webkit-mask-repeat: no-repeat;\r\n  -webkit-mask-position: center;\r\n  -webkit-mask-size: contain;\r\n}\r\n\r\n.benefit-info {\r\n  flex: 1;\r\n}\r\n\r\n.benefit-title {\r\n  font-size: 28rpx;\r\n  font-weight: 600;\r\n  color: #333;\r\n  margin-bottom: 8rpx;\r\n}\r\n\r\n.benefit-desc {\r\n  font-size: 24rpx;\r\n  color: #666;\r\n}\r\n\r\n/* 商家列表 */\r\n.section-header {\r\n  margin: 30rpx 30rpx 20rpx;\r\n}\r\n\r\n.section-title {\r\n  font-size: 32rpx;\r\n  font-weight: 600;\r\n  color: #333;\r\n}\r\n\r\n/* 搜索栏 */\r\n.search-bar {\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 20rpx 30rpx;\r\n  background: #FFFFFF;\r\n  margin-bottom: 20rpx;\r\n}\r\n\r\n.search-input-wrap {\r\n  flex: 1;\r\n  height: 72rpx;\r\n  background: #F5F7FA;\r\n  border-radius: 36rpx;\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 0 20rpx;\r\n  margin-right: 20rpx;\r\n}\r\n\r\n.search-icon {\r\n  width: 32rpx;\r\n  height: 32rpx;\r\n  background-color: #6B0FBE;\r\n  border-radius: 50%;\r\n  position: relative;\r\n  margin-right: 10rpx;\r\n}\r\n\r\n.search-icon::before {\r\n  content: '';\r\n  position: absolute;\r\n  width: 12rpx;\r\n  height: 12rpx;\r\n  border: 2rpx solid white;\r\n  border-radius: 50%;\r\n  top: 6rpx;\r\n  left: 6rpx;\r\n}\r\n\r\n.search-icon::after {\r\n  content: '';\r\n  position: absolute;\r\n  width: 8rpx;\r\n  height: 2rpx;\r\n  background-color: white;\r\n  transform: rotate(45deg);\r\n  bottom: 8rpx;\r\n  right: 6rpx;\r\n}\r\n\r\n.search-input {\r\n  flex: 1;\r\n  height: 100%;\r\n  font-size: 28rpx;\r\n  color: #333;\r\n}\r\n\r\n.clear-icon {\r\n  width: 32rpx;\r\n  height: 32rpx;\r\n  background-color: #cccccc;\r\n  border-radius: 50%;\r\n  position: relative;\r\n}\r\n\r\n.clear-icon::before,\r\n.clear-icon::after {\r\n  content: '';\r\n  position: absolute;\r\n  width: 16rpx;\r\n  height: 2rpx;\r\n  background-color: white;\r\n  top: 50%;\r\n  left: 50%;\r\n}\r\n\r\n.clear-icon::before {\r\n  transform: translate(-50%, -50%) rotate(45deg);\r\n}\r\n\r\n.clear-icon::after {\r\n  transform: translate(-50%, -50%) rotate(-45deg);\r\n}\r\n\r\n.search-btn {\r\n  font-size: 28rpx;\r\n  color: #6B0FBE;\r\n}\r\n\r\n/* 商家列表 */\r\n.merchants-list {\r\n  margin: 0 30rpx;\r\n}\r\n\r\n.merchant-card {\r\n  background: #FFFFFF;\r\n  border-radius: 20rpx;\r\n  margin-bottom: 20rpx;\r\n  overflow: hidden;\r\n  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n.merchant-info {\r\n  padding: 30rpx;\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.merchant-logo {\r\n  width: 100rpx;\r\n  height: 100rpx;\r\n  border-radius: 10rpx;\r\n  margin-right: 20rpx;\r\n  background-color: #f5f5f5;\r\n}\r\n\r\n.merchant-details {\r\n  flex: 1;\r\n}\r\n\r\n.merchant-name {\r\n  font-size: 28rpx;\r\n  font-weight: 600;\r\n  color: #333;\r\n  margin-bottom: 10rpx;\r\n}\r\n\r\n.merchant-meta {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.merchant-category {\r\n  font-size: 24rpx;\r\n  color: #999;\r\n  background-color: #f5f5f5;\r\n  padding: 4rpx 12rpx;\r\n  border-radius: 20rpx;\r\n  margin-right: 16rpx;\r\n}\r\n\r\n.merchant-commission {\r\n  font-size: 24rpx;\r\n  color: #6B0FBE;\r\n}\r\n\r\n.merchant-status {\r\n  width: 120rpx;\r\n  height: 60rpx;\r\n  background: #6B0FBE;\r\n  border-radius: 30rpx;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  font-size: 26rpx;\r\n  color: #fff;\r\n}\r\n\r\n.merchant-status.applied {\r\n  background: #999;\r\n}\r\n\r\n/* 空状态 */\r\n.empty-state {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  padding: 100rpx 0;\r\n}\r\n\r\n.empty-image {\r\n  width: 200rpx;\r\n  height: 200rpx;\r\n  margin-bottom: 30rpx;\r\n}\r\n\r\n.empty-text {\r\n  font-size: 28rpx;\r\n  color: #999;\r\n}\r\n\r\n/* 加载更多 */\r\n.load-more {\r\n  text-align: center;\r\n  padding: 30rpx 0;\r\n  font-size: 26rpx;\r\n  color: #666;\r\n}\r\n\r\n/* 申请弹窗 */\r\n.apply-modal {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  z-index: 1000;\r\n}\r\n\r\n.modal-mask {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background-color: rgba(0, 0, 0, 0.5);\r\n}\r\n\r\n.modal-content {\r\n  position: absolute;\r\n  bottom: 0;\r\n  left: 0;\r\n  right: 0;\r\n  background-color: #FFFFFF;\r\n  border-radius: 40rpx 40rpx 0 0;\r\n  padding: 30rpx;\r\n  animation: slideUp 0.3s ease-out;\r\n}\r\n\r\n@keyframes slideUp {\r\n  from {\r\n    transform: translateY(100%);\r\n  }\r\n  to {\r\n    transform: translateY(0);\r\n  }\r\n}\r\n\r\n.modal-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 30rpx;\r\n}\r\n\r\n.modal-title {\r\n  font-size: 32rpx;\r\n  font-weight: 600;\r\n  color: #333;\r\n}\r\n\r\n.close-icon {\r\n  width: 40rpx;\r\n  height: 40rpx;\r\n  position: relative;\r\n}\r\n\r\n.close-icon::before,\r\n.close-icon::after {\r\n  content: '';\r\n  position: absolute;\r\n  top: 50%;\r\n  left: 50%;\r\n  width: 32rpx;\r\n  height: 2rpx;\r\n  background-color: #999;\r\n}\r\n\r\n.close-icon::before {\r\n  transform: translate(-50%, -50%) rotate(45deg);\r\n}\r\n\r\n.close-icon::after {\r\n  transform: translate(-50%, -50%) rotate(-45deg);\r\n}\r\n\r\n.modal-merchant {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  margin-bottom: 30rpx;\r\n}\r\n\r\n.modal-merchant .merchant-logo {\r\n  width: 120rpx;\r\n  height: 120rpx;\r\n  margin-bottom: 16rpx;\r\n  margin-right: 0;\r\n}\r\n\r\n.modal-merchant .merchant-name {\r\n  margin-bottom: 0;\r\n}\r\n\r\n.form-content {\r\n  margin-bottom: 30rpx;\r\n}\r\n\r\n.form-item {\r\n  margin-bottom: 30rpx;\r\n  position: relative;\r\n}\r\n\r\n.form-label {\r\n  font-size: 28rpx;\r\n  color: #333;\r\n  margin-bottom: 16rpx;\r\n  display: block;\r\n}\r\n\r\n.form-textarea {\r\n  width: 100%;\r\n  height: 200rpx;\r\n  background: #F5F7FA;\r\n  border-radius: 10rpx;\r\n  padding: 20rpx;\r\n  font-size: 28rpx;\r\n  color: #333;\r\n  box-sizing: border-box;\r\n}\r\n\r\n.textarea-counter {\r\n  position: absolute;\r\n  right: 20rpx;\r\n  bottom: 20rpx;\r\n  font-size: 24rpx;\r\n  color: #999;\r\n}\r\n\r\n.form-input {\r\n  width: 100%;\r\n  height: 80rpx;\r\n  background: #F5F7FA;\r\n  border-radius: 10rpx;\r\n  padding: 0 20rpx;\r\n  font-size: 28rpx;\r\n  color: #333;\r\n  box-sizing: border-box;\r\n}\r\n\r\n.modal-footer {\r\n  display: flex;\r\n  justify-content: space-between;\r\n}\r\n\r\n.cancel-btn,\r\n.submit-btn {\r\n  width: 48%;\r\n  height: 80rpx;\r\n  border-radius: 40rpx;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  font-size: 28rpx;\r\n}\r\n\r\n.cancel-btn {\r\n  background: #F5F7FA;\r\n  color: #666;\r\n}\r\n\r\n.submit-btn {\r\n  background: linear-gradient(135deg, #A764CA, #6B0FBE);\r\n  color: #fff;\r\n}\r\n\r\n.submit-btn.disabled {\r\n  background: #cccccc;\r\n  color: #ffffff;\r\n}\r\n</style> ", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/subPackages/distribution/pages/merchant-apply.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "reactive", "computed", "onMounted", "loadMore", "distributionService", "uni", "MiniProgramPage"], "mappings": ";;;;;;;AAuJA,UAAM,gBAAgBA,cAAAA,IAAI,EAAE;AAG5B,UAAM,YAAYA,cAAAA,IAAI,CAAA,CAAE;AAGxB,UAAM,aAAaC,cAAAA,SAAS;AAAA,MAC1B,MAAM;AAAA,MACN,UAAU;AAAA,MACV,OAAO;AAAA,MACP,YAAY;AAAA,IACd,CAAC;AAGD,UAAM,cAAcD,cAAAA,IAAI,KAAK;AAG7B,UAAM,UAAUA,cAAAA,IAAI,KAAK;AAGzB,UAAM,iBAAiBA,cAAAA,IAAI,KAAK;AAGhC,UAAM,mBAAmBA,cAAAA,IAAI,CAAA,CAAE;AAG/B,UAAM,WAAWC,cAAAA,SAAS;AAAA,MACxB,QAAQ;AAAA,MACR,YAAY;AAAA,MACZ,SAAS;AAAA,IACX,CAAC;AAGD,UAAM,YAAYC,cAAQ,SAAC,MAAM;AAC/B,aAAO,SAAS,OAAO,KAAI,MAAO,MAC3B,SAAS,WAAW,KAAI,MAAO,MAC/B,SAAS,QAAQ,KAAI,MAAO,MAC5B,SAAS,QAAQ,WAAW;AAAA,IACrC,CAAC;AAGDC,kBAAAA,UAAU,YAAY;AAEpB,YAAM,aAAY;AAAA,IACpB,CAAC;AAGD,UAAM,eAAe,OAAOC,YAAW,UAAU;AAC/C,UAAI,QAAQ;AAAO;AAEnB,UAAI;AACF,gBAAQ,QAAQ;AAEhB,cAAM,OAAOA,YAAW,WAAW,OAAO,IAAI;AAE9C,cAAM,SAAS,MAAMC,0BAAmB,oBAAC,6BAA6B;AAAA,UACpE;AAAA,UACA,UAAU,WAAW;AAAA,UACrB,SAAS,cAAc;AAAA,QAC7B,CAAK;AAED,YAAI,QAAQ;AAEV,cAAID,WAAU;AACZ,sBAAU,QAAQ,CAAC,GAAG,UAAU,OAAO,GAAG,OAAO,IAAI;AAAA,UAC7D,OAAa;AACL,sBAAU,QAAQ,OAAO;AAAA,UAC1B;AAGD,qBAAW,OAAO;AAClB,qBAAW,QAAQ,OAAO,WAAW;AACrC,qBAAW,aAAa,OAAO,WAAW;AAG1C,sBAAY,QAAQ,WAAW,OAAO,WAAW;AAAA,QAClD;AAAA,MACF,SAAQ,OAAO;AACdE,sBAAA,MAAA,MAAA,SAAA,4DAAc,YAAY,KAAK;AAC/BA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AAAA,MACL,UAAY;AACR,gBAAQ,QAAQ;AAAA,MACjB;AAAA,IACH;AAGA,UAAM,kBAAkB,MAAM;AAC5B;IACF;AAGA,UAAM,cAAc,MAAM;AACxB,oBAAc,QAAQ;AACtB;IACF;AAGA,UAAM,WAAW,MAAM;AACrB,UAAI,YAAY,SAAS,CAAC,QAAQ,OAAO;AACvC,qBAAa,IAAI;AAAA,MAClB;AAAA,IACH;AAGA,UAAM,iBAAiB,CAAC,aAAa;AACnC,UAAI,SAAS,SAAS;AACpBA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AACD;AAAA,MACD;AAED,uBAAiB,QAAQ;AACzB,qBAAe,QAAQ;AAAA,IACzB;AAGA,UAAM,kBAAkB,MAAM;AAC5B,qBAAe,QAAQ;AAAA,IACzB;AAGA,UAAM,oBAAoB,YAAY;AACpC,UAAI,CAAC,UAAU;AAAO;AAEtB,UAAI;AACFA,sBAAAA,MAAI,YAAY;AAAA,UACd,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AAED,cAAM,SAAS,MAAMD,0BAAmB,oBAAC,yBAAyB;AAAA,UAChE,YAAY,iBAAiB,MAAM;AAAA,UACnC,QAAQ,SAAS;AAAA,UACjB,YAAY,SAAS;AAAA,UACrB,SAAS,SAAS;AAAA,QACxB,CAAK;AAEDC,sBAAG,MAAC,YAAW;AAEf,YAAI,OAAO,SAAS;AAClBA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,SAAS;AAAA,YACT,YAAY;AAAA,YACZ,SAAS,MAAM;AAEb,oBAAM,QAAQ,UAAU,MAAM,UAAU,OAAK,EAAE,OAAO,iBAAiB,MAAM,EAAE;AAC/E,kBAAI,UAAU,IAAI;AAChB,0BAAU,MAAM,KAAK,EAAE,UAAU;AAAA,cAClC;AAGD;AAGA,uBAAS,SAAS;AAClB,uBAAS,aAAa;AACtB,uBAAS,UAAU;AAAA,YACpB;AAAA,UACT,CAAO;AAAA,QACP,OAAW;AACLA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,SAAS,OAAO,WAAW;AAAA,YAC3B,YAAY;AAAA,UACpB,CAAO;AAAA,QACF;AAAA,MACF,SAAQ,OAAO;AACdA,sBAAG,MAAC,YAAW;AACfA,sBAAA,MAAA,MAAA,SAAA,4DAAc,UAAU,KAAK;AAC7BA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AAAA,MACF;AAAA,IACH;AAGA,UAAM,SAAS,MAAM;AACnBA,oBAAG,MAAC,aAAY;AAAA,IAClB;AAGA,UAAM,WAAW,MAAM;AACrBA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,SAAS;AAAA,QACT,YAAY;AAAA,MAChB,CAAG;AAAA,IACH;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACxVA,GAAG,WAAWC,SAAe;"}