// 推广营销配置工具
// 用于前台ConfigurablePremiumActions组件获取后台配置

// 配置缓存
let configCache = null
let lastUpdateTime = 0
const CACHE_DURATION = 5 * 60 * 1000 // 5分钟缓存

// 默认配置（后台服务不可用时的备用配置）
const defaultConfig = {
  buttons: {
    publish: {
      ad: {
        enabled: true,
        title: '看广告发布',
        description: '看一个广告免费发布一天',
        carpoolDescription: '看一个广告发布一条信息',
        icon: '/static/images/premium/ad-publish.png',
        tag: '免费'
      },
      paid: {
        enabled: true,
        title: '付费发布',
        description: '3天/1周/1个月任选',
        carpoolDescription: '付费1元发布一条信息',
        icon: '/static/images/premium/paid-publish.png',
        tag: '付费'
      }
    },
    top: {
      ad: {
        enabled: true,
        title: '广告置顶',
        description: '观看30秒广告获得2小时置顶',
        duration: '2小时',
        icon: '/static/images/premium/ad-top.png',
        tag: '免费'
      },
      paid: {
        enabled: true,
        title: '付费置顶',
        description: '3天/1周/1个月任选',
        icon: '/static/images/premium/paid-top.png',
        tag: '付费'
      }
    },
    refresh: {
      ad: {
        enabled: true,
        title: '广告刷新',
        description: '观看15秒广告刷新一次',
        icon: '/static/images/premium/ad-refresh.png',
        tag: '免费'
      },
      paid: {
        enabled: true,
        title: '付费刷新',
        description: '5次/10次/20次任选',
        icon: '/static/images/premium/paid-refresh.png',
        tag: '付费'
      }
    },
    join: {
      ad: {
        enabled: true,
        title: '看广告入驻',
        description: '观看30秒广告获得1个月免费特权',
        icon: '/static/images/premium/ad-join.png',
        tag: '免费'
      },
      paid: {
        enabled: true,
        title: '付费入驻',
        description: '1个月/3个月/1年任选',
        icon: '/static/images/premium/paid-join.png',
        tag: '付费'
      }
    },
    renew: {
      ad: {
        enabled: true,
        title: '看广告续费',
        description: '观看30秒广告延长7天会员',
        icon: '/static/images/premium/ad-renew.png',
        tag: '免费'
      },
      paid: {
        enabled: true,
        title: '付费续费',
        description: '1个月/3个月/6个月任选',
        icon: '/static/images/premium/paid-renew.png',
        tag: '付费'
      }
    }
  },
  pricing: {
    publish: [
      { duration: '3天', price: '2.8', recommended: false },
      { duration: '1周', price: '5.8', recommended: true },
      { duration: '1个月', price: '19.8', recommended: false }
    ],
    top: [
      { duration: '3天', price: '2.8', recommended: false },
      { duration: '1周', price: '5.8', recommended: true },
      { duration: '1个月', price: '19.8', recommended: false }
    ],
    refresh: [
      { duration: '5次', price: '1.8', recommended: false },
      { duration: '10次', price: '2.8', recommended: true },
      { duration: '20次', price: '4.8', recommended: false }
    ],
    join: [
      { duration: '1个月', price: '99', recommended: false },
      { duration: '3个月', price: '199', recommended: true },
      { duration: '1年', price: '599', recommended: false }
    ],
    renew: [
      { duration: '1个月', price: '99', recommended: false },
      { duration: '3个月', price: '199', recommended: true },
      { duration: '6个月', price: '359', recommended: false }
    ]
  },
  toggles: {
    global: {
      enabled: true,
      defaultShowMode: 'direct'
    },
    pages: {
      publish: { enabled: true, adEnabled: true, paidEnabled: true, carpoolSpecial: true, priority: 'equal' },
      top: { enabled: true, adEnabled: true, paidEnabled: true, supportedTypes: ['merchant_top', 'carpool_top', 'publish_top'] },
      refresh: { enabled: true, adEnabled: true, paidEnabled: true },
      join: { enabled: true, adEnabled: true, paidEnabled: true },
      renew: { enabled: true, adEnabled: true, paidEnabled: true }
    },
    advanced: {
      debugMode: false,
      logging: true,
      cacheConfig: true,
      updateInterval: 300
    }
  },
  adReward: {
    adUnitId: 'adunit-xxxxxxxxx',
    rewardRules: {
      publish: { type: 'duration', value: 1, dailyLimit: 5 },
      top: { type: 'duration', value: 2, dailyLimit: 3 },
      refresh: { type: 'count', value: 1, dailyLimit: 10 },
      join: { type: 'duration', value: 30, dailyLimit: 1 },
      renew: { type: 'duration', value: 7, dailyLimit: 1 }
    }
  }
}

// 从后台获取配置
const fetchConfigFromBackend = async () => {
  try {
    // 这里应该是实际的后台API地址
    const response = await uni.request({
      url: 'https://your-backend-api.com/api/promotion/complete-config',
      method: 'GET',
      timeout: 5000
    })
    
    if (response.statusCode === 200 && response.data) {
      return response.data
    } else {
      throw new Error('获取配置失败')
    }
  } catch (error) {
    console.warn('从后台获取推广配置失败，使用默认配置:', error)
    return defaultConfig
  }
}

// 获取推广配置
export const getPromotionConfig = async (forceRefresh = false) => {
  const now = Date.now()
  
  // 检查缓存是否有效
  if (!forceRefresh && configCache && (now - lastUpdateTime) < CACHE_DURATION) {
    return configCache
  }
  
  try {
    // 从后台获取最新配置
    const config = await fetchConfigFromBackend()
    
    // 更新缓存
    configCache = config
    lastUpdateTime = now
    
    // 保存到本地存储
    try {
      uni.setStorageSync('promotion_config', config)
      uni.setStorageSync('promotion_config_time', now)
    } catch (e) {
      console.warn('保存配置到本地存储失败:', e)
    }
    
    return config
  } catch (error) {
    console.error('获取推广配置失败:', error)
    
    // 尝试从本地存储获取
    try {
      const localConfig = uni.getStorageSync('promotion_config')
      const localTime = uni.getStorageSync('promotion_config_time')
      
      if (localConfig && localTime && (now - localTime) < 24 * 60 * 60 * 1000) { // 24小时内的本地缓存
        configCache = localConfig
        return localConfig
      }
    } catch (e) {
      console.warn('从本地存储获取配置失败:', e)
    }
    
    // 返回默认配置
    return defaultConfig
  }
}

// 获取按钮配置
export const getButtonConfig = async (pageType, optionType) => {
  const config = await getPromotionConfig()
  
  // 检查全局开关
  if (!config.toggles.global.enabled) {
    return null
  }
  
  // 解析页面类型
  let configKey = 'publish'
  if (pageType.includes('top')) {
    configKey = 'top'
  } else if (pageType.includes('refresh')) {
    configKey = 'refresh'
  } else if (pageType.includes('join')) {
    configKey = 'join'
  } else if (pageType.includes('renew')) {
    configKey = 'renew'
  }
  
  // 检查页面开关
  const pageToggle = config.toggles.pages[configKey]
  if (!pageToggle || !pageToggle.enabled) {
    return null
  }
  
  // 检查选项开关
  if (optionType === 'ad' && !pageToggle.adEnabled) {
    return null
  }
  if (optionType === 'paid' && !pageToggle.paidEnabled) {
    return null
  }
  
  // 返回按钮配置
  const buttonConfig = config.buttons[configKey]
  if (!buttonConfig) {
    return null
  }
  
  return buttonConfig[optionType]
}

// 获取价格配置
export const getPricingConfig = async (action) => {
  const config = await getPromotionConfig()
  
  // 映射操作到配置键
  let configKey = 'publish'
  if (action === 'top') {
    configKey = 'top'
  } else if (action === 'refresh') {
    configKey = 'refresh'
  } else if (action === 'join') {
    configKey = 'join'
  } else if (action === 'renew') {
    configKey = 'renew'
  }
  
  return config.pricing[configKey] || []
}

// 获取广告奖励配置
export const getAdRewardConfig = async (action) => {
  const config = await getPromotionConfig()
  
  const rewardRule = config.adReward.rewardRules[action]
  if (!rewardRule) {
    return null
  }
  
  return {
    adUnitId: config.adReward.adUnitId,
    rewardType: rewardRule.type,
    rewardValue: rewardRule.value,
    dailyLimit: rewardRule.dailyLimit
  }
}

// 检查功能是否启用
export const isFeatureEnabled = async (pageType, optionType = null) => {
  const config = await getPromotionConfig()
  
  // 检查全局开关
  if (!config.toggles.global.enabled) {
    return false
  }
  
  // 解析页面类型
  let configKey = 'publish'
  if (pageType.includes('top')) {
    configKey = 'top'
  } else if (pageType.includes('refresh')) {
    configKey = 'refresh'
  } else if (pageType.includes('join')) {
    configKey = 'join'
  } else if (pageType.includes('renew')) {
    configKey = 'renew'
  }
  
  const pageToggle = config.toggles.pages[configKey]
  if (!pageToggle || !pageToggle.enabled) {
    return false
  }
  
  // 如果指定了选项类型，检查选项开关
  if (optionType === 'ad') {
    return pageToggle.adEnabled
  }
  if (optionType === 'paid') {
    return pageToggle.paidEnabled
  }
  
  return true
}

// 记录用户操作日志
export const logUserAction = async (action, data) => {
  const config = await getPromotionConfig()
  
  if (!config.toggles.advanced.logging) {
    return
  }
  
  try {
    // 发送日志到后台
    uni.request({
      url: 'https://your-backend-api.com/api/promotion/log',
      method: 'POST',
      data: {
        action,
        data,
        timestamp: Date.now(),
        userAgent: navigator.userAgent || 'unknown'
      }
    })
  } catch (error) {
    console.warn('发送操作日志失败:', error)
  }
}

// 清除配置缓存
export const clearConfigCache = () => {
  configCache = null
  lastUpdateTime = 0
  
  try {
    uni.removeStorageSync('promotion_config')
    uni.removeStorageSync('promotion_config_time')
  } catch (e) {
    console.warn('清除本地配置缓存失败:', e)
  }
}

// 导出默认配置（用于调试）
export { defaultConfig }
