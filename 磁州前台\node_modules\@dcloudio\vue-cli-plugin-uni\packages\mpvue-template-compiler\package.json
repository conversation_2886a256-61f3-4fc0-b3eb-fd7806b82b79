{"name": "mpvue-template-compiler", "version": "1.0.13", "description": "mpvue template compiler for Vue", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/<PERSON><PERSON><PERSON>-<PERSON>/mpvue.git"}, "keywords": ["vue", "compiler", "mp", "mp", "antmp"], "author": "meituan.com", "license": "MIT", "bugs": {"url": "https://github.com/<PERSON><PERSON><PERSON>-<PERSON>/mpvue/issues"}, "homepage": "https://github.com/<PERSON><PERSON><PERSON>-<PERSON>/mpvue/", "dependencies": {"he": "^1.1.0", "de-indent": "^1.0.2"}}