"use strict";
const common_vendor = require("../../../common/vendor.js");
const common_assets = require("../../../common/assets.js");
const _sfc_main = {
  __name: "partner-income",
  setup(__props) {
    const currentMonth = common_vendor.ref("2023-11");
    const totalIncome = common_vendor.ref("3628.50");
    const firstLevelIncome = common_vendor.ref("2745.20");
    const secondLevelIncome = common_vendor.ref("883.30");
    const incomeOrders = common_vendor.ref(56);
    const currentTab = common_vendor.ref("all");
    const incomeList = common_vendor.ref([]);
    const page = common_vendor.ref(1);
    common_vendor.ref(10);
    const hasMore = common_vendor.ref(true);
    const isLoading = common_vendor.ref(false);
    const chartData = common_vendor.ref([
      { date: "11-01", level1: 80, level2: 20 },
      { date: "11-05", level1: 120, level2: 50 },
      { date: "11-10", level1: 60, level2: 30 },
      { date: "11-15", level1: 100, level2: 40 },
      { date: "11-20", level1: 150, level2: 60 },
      { date: "11-25", level1: 90, level2: 35 },
      { date: "11-30", level1: 110, level2: 45 }
    ]);
    const formattedMonth = common_vendor.computed(() => {
      const [year, month] = currentMonth.value.split("-");
      return `${year}年${parseInt(month)}月`;
    });
    const filteredIncome = common_vendor.computed(() => {
      if (currentTab.value === "all") {
        return incomeList.value;
      } else if (currentTab.value === "level1") {
        return incomeList.value.filter((item) => item.level === 1);
      } else {
        return incomeList.value.filter((item) => item.level === 2);
      }
    });
    const monthChange = (e) => {
      currentMonth.value = e.detail.value;
      page.value = 1;
      hasMore.value = true;
      incomeList.value = [];
      loadData();
      updateChartData();
    };
    const switchTab = (tab) => {
      currentTab.value = tab;
    };
    const loadData = () => {
      isLoading.value = true;
      setTimeout(() => {
        const mockData = generateMockIncome();
        if (page.value === 1) {
          incomeList.value = mockData;
        } else {
          incomeList.value = [...incomeList.value, ...mockData];
        }
        hasMore.value = page.value < 3;
        isLoading.value = false;
      }, 500);
    };
    const loadMore = () => {
      if (isLoading.value || !hasMore.value)
        return;
      page.value++;
      loadData();
    };
    const updateChartData = () => {
      const [year, month] = currentMonth.value.split("-");
      const daysInMonth = new Date(parseInt(year), parseInt(month), 0).getDate();
      chartData.value = [];
      const step = Math.floor(daysInMonth / 6);
      for (let i = 0; i < 7; i++) {
        const day = i * step + 1;
        chartData.value.push({
          date: `${month}-${day < 10 ? "0" + day : day}`,
          level1: Math.floor(Math.random() * 100) + 50,
          level2: Math.floor(Math.random() * 50) + 10
        });
      }
    };
    const navigateToPoster = () => {
      common_vendor.index.navigateTo({
        url: "/pages/my/partner-poster"
      });
    };
    const generateMockIncome = () => {
      const mockIncome = [];
      const titles = ["商品订单佣金", "会员充值佣金", "服务预约佣金", "活动报名佣金"];
      const names = ["张先生", "李女士", "王先生", "赵女士", "刘先生", "陈女士", "杨先生", "周女士"];
      for (let i = 0; i < 10; i++) {
        const level = Math.random() > 0.7 ? 2 : 1;
        const title = titles[Math.floor(Math.random() * titles.length)];
        const name = names[Math.floor(Math.random() * names.length)];
        const amount = level === 1 ? (Math.floor(Math.random() * 100) + 30).toFixed(2) : (Math.floor(Math.random() * 50) + 10).toFixed(2);
        const [year, month] = currentMonth.value.split("-");
        const day = Math.floor(Math.random() * 28) + 1;
        const hour = Math.floor(Math.random() * 24);
        const minute = Math.floor(Math.random() * 60);
        const time = `${year}-${month}-${day < 10 ? "0" + day : day} ${hour < 10 ? "0" + hour : hour}:${minute < 10 ? "0" + minute : minute}`;
        mockIncome.push({
          id: "income_" + Date.now() + i,
          title,
          desc: `来自${name}的订单`,
          time,
          amount,
          level
        });
      }
      mockIncome.sort((a, b) => new Date(b.time) - new Date(a.time));
      return mockIncome;
    };
    const goBack = () => {
      common_vendor.index.navigateBack();
    };
    common_vendor.onMounted(() => {
      loadData();
    });
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_assets._imports_0$7,
        b: common_vendor.o(goBack),
        c: common_vendor.t(formattedMonth.value),
        d: currentMonth.value,
        e: common_vendor.o(monthChange),
        f: common_vendor.t(totalIncome.value),
        g: common_vendor.t(firstLevelIncome.value),
        h: common_vendor.t(secondLevelIncome.value),
        i: common_vendor.t(incomeOrders.value),
        j: common_vendor.f(chartData.value, (item, index, i0) => {
          return {
            a: item.level2 + "rpx",
            b: item.level1 + "rpx",
            c: common_vendor.t(item.date),
            d: index
          };
        }),
        k: currentTab.value === "all" ? 1 : "",
        l: common_vendor.o(($event) => switchTab("all")),
        m: currentTab.value === "level1" ? 1 : "",
        n: common_vendor.o(($event) => switchTab("level1")),
        o: currentTab.value === "level2" ? 1 : "",
        p: common_vendor.o(($event) => switchTab("level2")),
        q: filteredIncome.value.length > 0
      }, filteredIncome.value.length > 0 ? {
        r: common_vendor.f(filteredIncome.value, (item, index, i0) => {
          return {
            a: common_vendor.t(item.title),
            b: common_vendor.t(item.desc),
            c: common_vendor.t(item.time),
            d: common_vendor.t(item.amount),
            e: item.level === 1 ? 1 : "",
            f: item.level === 2 ? 1 : "",
            g: common_vendor.t(item.level === 1 ? "一级收益" : "二级收益"),
            h: index
          };
        })
      } : {
        s: common_assets._imports_1$21,
        t: common_vendor.o(navigateToPoster)
      }, {
        v: filteredIncome.value.length > 0 && hasMore.value
      }, filteredIncome.value.length > 0 && hasMore.value ? common_vendor.e({
        w: !isLoading.value
      }, !isLoading.value ? {
        x: common_vendor.o(loadMore)
      } : {}) : {}, {
        y: filteredIncome.value.length > 0 && !hasMore.value
      }, filteredIncome.value.length > 0 && !hasMore.value ? {} : {});
    };
  }
};
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-63ebc8b5"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/subPackages/partner/pages/partner-income.js.map
