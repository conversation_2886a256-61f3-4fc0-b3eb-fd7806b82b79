"use strict";
const common_vendor = require("../../../../../common/vendor.js");
const common_assets = require("../../../../../common/assets.js");
if (!Array) {
  const _component_path = common_vendor.resolveComponent("path");
  const _component_svg = common_vendor.resolveComponent("svg");
  (_component_path + _component_svg)();
}
const defaultAgreement = `<h2 style="text-align: center; font-weight: bold; margin-bottom: 20px;">分销员协议</h2>
<p style="text-indent: 2em; margin-bottom: 10px;">尊敬的用户，欢迎您申请成为我们的分销员。在您申请成为分销员之前，请您仔细阅读本协议的全部内容。</p>

<h3 style="font-weight: bold; margin: 15px 0 10px 0;">一、分销员资格</h3>
<p style="text-indent: 2em; margin-bottom: 10px;">1.1 您确认，您在申请成为分销员时，已经年满18周岁，具有完全民事行为能力。</p>
<p style="text-indent: 2em; margin-bottom: 10px;">1.2 您承诺，您提供的所有注册资料均真实、完整、准确，并承诺及时更新相关资料。</p>

<h3 style="font-weight: bold; margin: 15px 0 10px 0;">二、分销规则</h3>
<p style="text-indent: 2em; margin-bottom: 10px;">2.1 分销员可以获得的佣金比例、结算方式等具体规则，以平台当时公示的规则为准。</p>
<p style="text-indent: 2em; margin-bottom: 10px;">2.2 平台有权根据业务需要调整分销规则，调整后的规则将在平台公示，并自公示之日起生效。</p>

<h3 style="font-weight: bold; margin: 15px 0 10px 0;">三、分销员义务</h3>
<p style="text-indent: 2em; margin-bottom: 10px;">3.1 分销员应当遵守国家法律法规和平台规则，不得从事任何违法违规活动。</p>
<p style="text-indent: 2em; margin-bottom: 10px;">3.2 分销员在推广过程中，不得夸大宣传、虚假宣传，不得侵犯他人合法权益。</p>

<h3 style="font-weight: bold; margin: 15px 0 10px 0;">四、违约责任</h3>
<p style="text-indent: 2em; margin-bottom: 10px;">4.1 如分销员违反本协议约定，平台有权视情节轻重，采取警告、暂停分销资格、永久取消分销资格等措施。</p>
<p style="text-indent: 2em; margin-bottom: 10px;">4.2 如分销员的行为给平台或第三方造成损失，分销员应当承担赔偿责任。</p>

<h3 style="font-weight: bold; margin: 15px 0 10px 0;">五、协议修改</h3>
<p style="text-indent: 2em; margin-bottom: 10px;">5.1 平台有权根据业务需要修改本协议，修改后的协议将在平台公示，并自公示之日起生效。</p>
<p style="text-indent: 2em; margin-bottom: 10px;">5.2 如分销员不同意修改后的协议，可以申请终止分销员资格；如分销员继续使用分销功能，则视为同意修改后的协议。</p>

<p style="text-align: right; margin-top: 20px;">平台运营方：XXXX有限公司</p>
<p style="text-align: right;">日期：2023年XX月XX日</p>`;
const _sfc_main = {
  __name: "agreement",
  setup(__props) {
    const agreementEnabled = common_vendor.ref(true);
    const editorCtx = common_vendor.ref(null);
    const formats = common_vendor.reactive({
      bold: false,
      italic: false,
      underline: false,
      align: "left"
    });
    common_vendor.onMounted(() => {
      getAgreementSettings();
    });
    const getAgreementSettings = () => {
    };
    const toggleAgreement = (e) => {
      agreementEnabled.value = e.detail.value;
    };
    const onEditorReady = () => {
      common_vendor.index.createSelectorQuery().select("#editor").context((res) => {
        editorCtx.value = res.context;
        editorCtx.value.setContents({
          html: defaultAgreement,
          success: () => {
            common_vendor.index.__f__("log", "at subPackages/merchant-admin-marketing/pages/marketing/distribution/agreement.vue:182", "设置内容成功");
          },
          fail: (err) => {
            common_vendor.index.__f__("error", "at subPackages/merchant-admin-marketing/pages/marketing/distribution/agreement.vue:185", "设置内容失败:", err);
          }
        });
      }).exec();
    };
    const onEditorInput = (e) => {
      common_vendor.index.__f__("log", "at subPackages/merchant-admin-marketing/pages/marketing/distribution/agreement.vue:195", "内容变化:", e.detail);
    };
    const formatText = (name, value) => {
      editorCtx.value.format(name, value);
      if (name === "align") {
        formats.align = value;
      } else {
        formats[name] = !formats[name];
      }
    };
    const insertTemplate = () => {
      common_vendor.index.showActionSheet({
        itemList: ["插入分销规则模板", "插入佣金说明模板", "插入提现规则模板"],
        success: (res) => {
          let template = "";
          switch (res.tapIndex) {
            case 0:
              template = '<h3 style="font-weight: bold; margin: 15px 0 10px 0;">分销规则</h3><p style="text-indent: 2em; margin-bottom: 10px;">1. 分销员可获得一级佣金和二级佣金</p><p style="text-indent: 2em; margin-bottom: 10px;">2. 佣金比例根据分销员等级不同而不同</p>';
              break;
            case 1:
              template = '<h3 style="font-weight: bold; margin: 15px 0 10px 0;">佣金说明</h3><p style="text-indent: 2em; margin-bottom: 10px;">1. 佣金在订单完成后自动结算</p><p style="text-indent: 2em; margin-bottom: 10px;">2. 佣金可在"我的佣金"页面查看</p>';
              break;
            case 2:
              template = '<h3 style="font-weight: bold; margin: 15px 0 10px 0;">提现规则</h3><p style="text-indent: 2em; margin-bottom: 10px;">1. 最低提现金额为50元</p><p style="text-indent: 2em; margin-bottom: 10px;">2. 提现申请将在1-3个工作日内处理</p>';
              break;
          }
          editorCtx.value.insertText({
            text: template,
            success: () => {
              common_vendor.index.__f__("log", "at subPackages/merchant-admin-marketing/pages/marketing/distribution/agreement.vue:232", "插入模板成功");
            }
          });
        }
      });
    };
    const previewAgreement = () => {
      editorCtx.value.getContents({
        success: (res) => {
          res.html;
          common_vendor.index.showToast({
            title: "预览功能开发中",
            icon: "none"
          });
        }
      });
    };
    const saveSettings = () => {
      if (agreementEnabled.value) {
        editorCtx.value.getContents({
          success: (res) => {
            res.html;
            saveAgreementContent();
          }
        });
      } else {
        saveAgreementSetting();
      }
    };
    const saveAgreementContent = (html) => {
      common_vendor.index.showLoading({
        title: "保存中..."
      });
      setTimeout(() => {
        common_vendor.index.hideLoading();
        common_vendor.index.showToast({
          title: "保存成功",
          icon: "success"
        });
        setTimeout(() => {
          common_vendor.index.navigateBack();
        }, 1500);
      }, 1e3);
    };
    const saveAgreementSetting = () => {
      common_vendor.index.showLoading({
        title: "保存中..."
      });
      setTimeout(() => {
        common_vendor.index.hideLoading();
        common_vendor.index.showToast({
          title: "保存成功",
          icon: "success"
        });
        setTimeout(() => {
          common_vendor.index.navigateBack();
        }, 1500);
      }, 1e3);
    };
    const goBack = () => {
      common_vendor.index.navigateBack();
    };
    const showHelp = () => {
      common_vendor.index.showModal({
        title: "分销协议帮助",
        content: "您可以设置分销员协议，用户申请成为分销员时需要同意该协议。您可以使用富文本编辑器编辑协议内容，也可以使用模板快速插入常用内容。",
        showCancel: false
      });
    };
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_vendor.o(goBack),
        b: common_vendor.o(showHelp),
        c: agreementEnabled.value,
        d: common_vendor.o(toggleAgreement),
        e: agreementEnabled.value
      }, agreementEnabled.value ? {} : {}, {
        f: agreementEnabled.value
      }, agreementEnabled.value ? {
        g: common_vendor.o(($event) => formatText("bold")),
        h: formats.bold ? 1 : "",
        i: common_vendor.o(($event) => formatText("italic")),
        j: formats.italic ? 1 : "",
        k: common_vendor.o(($event) => formatText("underline")),
        l: formats.underline ? 1 : "",
        m: common_vendor.p({
          d: "M3 6H21",
          stroke: "currentColor",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        n: common_vendor.p({
          d: "M3 12H15",
          stroke: "currentColor",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        o: common_vendor.p({
          d: "M3 18H21",
          stroke: "currentColor",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        p: common_vendor.p({
          width: "16",
          height: "16",
          viewBox: "0 0 24 24",
          fill: "none",
          xmlns: "http://www.w3.org/2000/svg"
        }),
        q: common_vendor.o(($event) => formatText("align", "left")),
        r: formats.align === "left" ? 1 : "",
        s: common_vendor.p({
          d: "M3 6H21",
          stroke: "currentColor",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        t: common_vendor.p({
          d: "M6 12H18",
          stroke: "currentColor",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        v: common_vendor.p({
          d: "M3 18H21",
          stroke: "currentColor",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        w: common_vendor.p({
          width: "16",
          height: "16",
          viewBox: "0 0 24 24",
          fill: "none",
          xmlns: "http://www.w3.org/2000/svg"
        }),
        x: common_vendor.o(($event) => formatText("align", "center")),
        y: formats.align === "center" ? 1 : "",
        z: common_vendor.p({
          d: "M3 6H21",
          stroke: "currentColor",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        A: common_vendor.p({
          d: "M9 12H21",
          stroke: "currentColor",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        B: common_vendor.p({
          d: "M3 18H21",
          stroke: "currentColor",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        C: common_vendor.p({
          width: "16",
          height: "16",
          viewBox: "0 0 24 24",
          fill: "none",
          xmlns: "http://www.w3.org/2000/svg"
        }),
        D: common_vendor.o(($event) => formatText("align", "right")),
        E: formats.align === "right" ? 1 : "",
        F: common_vendor.p({
          d: "M14 2H6C5.46957 2 4.96086 2.21071 4.58579 2.58579C4.21071 2.96086 4 3.46957 4 4V20C4 20.5304 4.21071 21.0391 4.58579 21.4142C4.96086 21.7893 5.46957 22 6 22H18C18.5304 22 19.0391 21.7893 19.4142 21.4142C19.7893 21.0391 20 20.5304 20 20V8L14 2Z",
          stroke: "currentColor",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        G: common_vendor.p({
          d: "M14 2V8H20",
          stroke: "currentColor",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        H: common_vendor.p({
          d: "M16 13H8",
          stroke: "currentColor",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        I: common_vendor.p({
          d: "M16 17H8",
          stroke: "currentColor",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        J: common_vendor.p({
          d: "M10 9H9H8",
          stroke: "currentColor",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        K: common_vendor.p({
          width: "16",
          height: "16",
          viewBox: "0 0 24 24",
          fill: "none",
          xmlns: "http://www.w3.org/2000/svg"
        }),
        L: common_vendor.o(insertTemplate),
        M: common_vendor.o(onEditorReady),
        N: common_vendor.o(onEditorInput)
      } : {}, {
        O: agreementEnabled.value
      }, agreementEnabled.value ? {
        P: common_vendor.o(previewAgreement),
        Q: common_assets._imports_0$42
      } : {}, {
        R: common_vendor.o(saveSettings)
      });
    };
  }
};
wx.createPage(_sfc_main);
//# sourceMappingURL=../../../../../../.sourcemap/mp-weixin/subPackages/merchant-admin-marketing/pages/marketing/distribution/agreement.js.map
