/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 移除阿里妈妈字体引用，改用系统默认字体 */
/* 移除原有阿里妈妈字体定义
$uni-font-family: 'AlimamaShuHeiTi';
$uni-font-family-text: 'AlimamaShuHeiTi', -apple-system, BlinkMacSystemFont, 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;
*/
body, html, #app, .index-container {
  font-family: "AlimamaShuHeiTi", -apple-system, BlinkMacSystemFont, "Helvetica Neue", "PingFang SC", "Microsoft YaHei", sans-serif;
}

/* 新导航栏样式 */
.nav-bar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  background-color: #1677FF;
  z-index: 999;
  /* 标题栏高度再减少5px */
  height: calc(var(--status-bar-height) + 82px);
  width: 100vw;
}
.status-bar {
  width: 100%;
}
.nav-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 49px;
  padding: 0 20px 8px;
  /* 左右内边距确保按钮与边缘有足够距离 */
  margin-top: 10px;
  /* 增加顶部间距，使内容整体下移 */
}
.back-btn {
  width: 44px;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  /* 左对齐 */
  position: relative;
  top: 29px;
  /* 再次下移3px */
}
.back-icon {
  width: 24px;
  height: 24px;
  filter: brightness(0) invert(1);
}
.nav-title {
  flex: 1;
  text-align: center;
  font-size: 18px;
  font-weight: 600;
  color: #ffffff;
  transform: translateX(8px) translateY(29px);
  /* 标题向右8px并下移29px */
  /* 确保文本可见 */
  display: block;
  position: relative;
  z-index: 1001;
  text-shadow: 0 1px 1px rgba(0, 0, 0, 0.2);
  /* 添加文字阴影提高可读性 */
}
.right-placeholder {
  width: 44px;
  height: 44px;
  display: flex;
  justify-content: flex-end;
  /* 右对齐 */
  position: relative;
  top: 29px;
  /* 与返回键保持一致的偏移 */
}

/* 主容器样式调整 */
.verification-container {
  min-height: 100vh;
  background-color: #F5F8FC;
  position: relative;
  padding-top: calc(var(--status-bar-height) + 82px);
  /* 再次调整后的导航栏高度 */
  padding-bottom: 40rpx;
}

/* 认证状态卡片 */
.status-card {
  margin: 30rpx;
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 40rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);
  display: flex;
  flex-direction: column;
  align-items: center;
}
.status-icon {
  width: 120rpx;
  height: 120rpx;
  border-radius: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 20rpx;
}
.status-icon.pending {
  background-color: #FF9F0A;
}
.status-icon.verified {
  background-color: #34C759;
}
.status-icon.rejected {
  background-color: #FF3B30;
}
.status-img {
  width: 60rpx;
  height: 60rpx;
}
.status-text {
  font-size: 36rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 16rpx;
}
.status-desc {
  font-size: 28rpx;
  color: #666666;
  text-align: center;
  line-height: 1.5;
}
.reapply-btn {
  margin-top: 30rpx;
  background-color: #0A84FF;
  color: #FFFFFF;
  font-size: 28rpx;
  padding: 12rpx 40rpx;
  border-radius: 30rpx;
  line-height: 1.5;
}

/* 认证表单 */
.verification-form {
  margin: 30rpx;
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);
}
.form-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 10rpx;
}
.form-subtitle {
  font-size: 24rpx;
  color: #8E8E93;
  margin-bottom: 30rpx;
}
.form-group {
  margin-bottom: 24rpx;
}
.form-label {
  font-size: 28rpx;
  color: #333333;
  margin-bottom: 10rpx;
}
.form-input {
  width: 100%;
  height: 80rpx;
  background-color: #F5F5F5;
  border-radius: 8rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  color: #333333;
}
.date-picker {
  width: 100%;
  height: 80rpx;
  background-color: #F5F5F5;
  border-radius: 8rpx;
  padding: 0 20rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.date-text {
  font-size: 28rpx;
  color: #333333;
}
.date-icon {
  width: 32rpx;
  height: 32rpx;
}

/* 上传部分 */
.upload-section {
  margin-top: 30rpx;
  margin-bottom: 30rpx;
}
.upload-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 20rpx;
}
.upload-item {
  margin-bottom: 24rpx;
}
.upload-label {
  font-size: 28rpx;
  color: #333333;
  margin-bottom: 10rpx;
}
.upload-box {
  width: 100%;
  height: 200rpx;
  background-color: #F5F5F5;
  border-radius: 8rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.upload-icon {
  width: 48rpx;
  height: 48rpx;
  margin-bottom: 10rpx;
}
.upload-text {
  font-size: 26rpx;
  color: #8E8E93;
}
.image-preview {
  width: 100%;
  height: 200rpx;
  border-radius: 8rpx;
  position: relative;
  overflow: hidden;
}
.preview-image {
  width: 100%;
  height: 100%;
}
.delete-btn {
  position: absolute;
  top: 10rpx;
  right: 10rpx;
  width: 40rpx;
  height: 40rpx;
  background-color: rgba(0, 0, 0, 0.5);
  border-radius: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.delete-icon {
  width: 24rpx;
  height: 24rpx;
}

/* 协议 */
.agreement {
  margin-bottom: 30rpx;
}
.checkbox-label {
  display: flex;
  align-items: center;
}
.agreement-text {
  font-size: 26rpx;
  color: #666666;
}
.agreement-link {
  font-size: 26rpx;
  color: #0A84FF;
}

/* 提交按钮 */
.submit-btn {
  width: 100%;
  height: 90rpx;
  background: linear-gradient(135deg, #0A84FF, #0040DD);
  color: #FFFFFF;
  font-size: 32rpx;
  font-weight: 500;
  border-radius: 45rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.submit-btn[disabled] {
  background: #CCCCCC;
  color: #FFFFFF;
}
.safe-area-bottom {
  height: env(safe-area-inset-bottom);
  width: 100%;
}