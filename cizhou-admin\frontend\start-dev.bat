@echo off
chcp 65001 >nul

echo 🎨 启动磁州生活网后台管理系统前端

REM 检查Node.js是否安装
node -v >nul 2>&1
if errorlevel 1 (
    echo ❌ Node.js未安装，请先安装Node.js 18+
    echo    下载地址: https://nodejs.org/
    pause
    exit /b 1
)

REM 显示Node.js版本
echo 📋 当前Node.js版本:
node -v

REM 检查npm是否安装
npm -v >nul 2>&1
if errorlevel 1 (
    echo ❌ npm未安装
    pause
    exit /b 1
)

REM 显示npm版本
echo 📋 当前npm版本:
npm -v

REM 检查是否存在node_modules
if not exist "node_modules" (
    echo 📦 首次运行，正在安装依赖...
    echo ⏳ 这可能需要几分钟时间，请耐心等待...
    
    REM 设置npm镜像源（可选，提高下载速度）
    echo 🔧 配置npm镜像源...
    npm config set registry https://registry.npmmirror.com/
    
    REM 安装依赖
    npm install
    
    if errorlevel 1 (
        echo ❌ 依赖安装失败
        echo 🔧 尝试清理缓存后重新安装...
        npm cache clean --force
        if exist node_modules rmdir /s /q node_modules
        if exist package-lock.json del package-lock.json
        npm install
        
        if errorlevel 1 (
            echo ❌ 依赖安装仍然失败，请检查网络连接或手动安装
            pause
            exit /b 1
        )
    )
    
    echo ✅ 依赖安装完成
) else (
    echo 📦 检查依赖更新...
    npm outdated
)

REM 检查后端服务是否启动
echo 🔍 检查后端服务状态...
curl -s -o nul -w "%%{http_code}" http://localhost:8080/actuator/health >temp_status.txt 2>nul
set /p BACKEND_STATUS=<temp_status.txt
del temp_status.txt >nul 2>&1

if not "%BACKEND_STATUS%"=="200" (
    echo ⚠️  后端服务未启动或不可访问
    echo 🔧 请确保后端服务已启动：
    echo    1. cd ../backend
    echo    2. start-dev.bat
    echo.
    echo 📋 或者手动启动后端服务：
    echo    - 网关服务: http://localhost:8080
    echo    - 认证服务: http://localhost:8081
    echo.
    set /p continue="是否继续启动前端服务？(y/N): "
    if /i not "%continue%"=="y" (
        echo ❌ 已取消启动
        pause
        exit /b 1
    )
) else (
    echo ✅ 后端服务运行正常
)

REM 启动开发服务器
echo 🚀 启动前端开发服务器...
echo 📱 前端地址: http://localhost:3000
echo 🔑 默认账号: admin / admin123
echo.
echo 💡 提示：
echo    - 按 Ctrl+C 停止服务
echo    - 修改代码会自动热重载
echo    - 如遇到问题，请检查控制台错误信息
echo.

REM 启动开发服务器
npm run dev

echo 👋 前端服务已停止
pause
