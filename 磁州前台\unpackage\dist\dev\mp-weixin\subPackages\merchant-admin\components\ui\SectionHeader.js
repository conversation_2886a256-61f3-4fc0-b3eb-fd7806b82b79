"use strict";
const common_vendor = require("../../../../common/vendor.js");
const _sfc_main = {
  name: "SectionHeader",
  props: {
    title: {
      type: String,
      default: "区块标题"
    },
    hasAction: {
      type: Boolean,
      default: false
    },
    actionText: {
      type: String,
      default: "更多"
    }
  },
  methods: {
    onAction() {
      this.$emit("action");
    }
  }
};
if (!Array) {
  const _component_path = common_vendor.resolveComponent("path");
  const _component_svg = common_vendor.resolveComponent("svg");
  (_component_path + _component_svg)();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.p({
      d: "M19 11H5C3.89543 11 3 11.8954 3 13V20C3 21.1046 3.89543 22 5 22H19C20.1046 22 21 21.1046 21 20V13C21 11.8954 20.1046 11 19 11Z",
      stroke: "#0A84FF",
      ["stroke-width"]: "2",
      ["stroke-linecap"]: "round",
      ["stroke-linejoin"]: "round"
    }),
    b: common_vendor.p({
      d: "M7 11V7C7 5.93913 7.42143 4.92172 8.17157 4.17157C8.92172 3.42143 9.93913 3 11 3H13C14.0609 3 15.0783 3.42143 15.8284 4.17157C16.5786 4.92172 17 5.93913 17 7V11",
      stroke: "#0A84FF",
      ["stroke-width"]: "2",
      ["stroke-linecap"]: "round",
      ["stroke-linejoin"]: "round"
    }),
    c: common_vendor.p({
      width: "20",
      height: "20",
      viewBox: "0 0 24 24",
      fill: "none",
      xmlns: "http://www.w3.org/2000/svg"
    }),
    d: common_vendor.t($props.title),
    e: $props.hasAction
  }, $props.hasAction ? {
    f: common_vendor.t($props.actionText),
    g: common_vendor.p({
      d: "M9 18L15 12L9 6",
      stroke: "#0A84FF",
      ["stroke-width"]: "2",
      ["stroke-linecap"]: "round",
      ["stroke-linejoin"]: "round"
    }),
    h: common_vendor.p({
      width: "16",
      height: "16",
      viewBox: "0 0 24 24",
      fill: "none",
      xmlns: "http://www.w3.org/2000/svg"
    }),
    i: common_vendor.o((...args) => $options.onAction && $options.onAction(...args))
  } : {});
}
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createComponent(Component);
//# sourceMappingURL=../../../../../.sourcemap/mp-weixin/subPackages/merchant-admin/components/ui/SectionHeader.js.map
