"use strict";
const common_vendor = require("../../../../../common/vendor.js");
const common_assets = require("../../../../../common/assets.js");
const _sfc_main = {
  data() {
    return {
      dateRange: "2023-04-01 ~ 2023-04-30",
      currentTab: 0,
      tabList: ["全部", "进行中", "未开始", "已结束", "草稿"],
      // 红包雨数据概览
      rainData: {
        totalCount: 18,
        countTrend: "up",
        countGrowth: "22.5%",
        totalUsers: 8752,
        usersTrend: "up",
        usersGrowth: "35.7%",
        totalAmount: 12680.5,
        amountTrend: "up",
        amountGrowth: "28.2%",
        conversionRate: 45.6,
        conversionTrend: "up",
        conversionGrowth: "7.8%"
      },
      // 红包雨活动列表
      rainList: [
        {
          id: 1,
          title: "周年庆红包雨",
          status: "active",
          statusText: "进行中",
          timeRange: "2023-04-15 ~ 2023-04-20",
          minAmount: 1,
          maxAmount: 50,
          duration: 60,
          participantCount: 3562,
          sentCount: 15820,
          receivedCount: 12453
        },
        {
          id: 2,
          title: "五一节日红包雨",
          status: "upcoming",
          statusText: "未开始",
          timeRange: "2023-05-01 ~ 2023-05-07",
          minAmount: 2,
          maxAmount: 88,
          duration: 90,
          participantCount: 0,
          sentCount: 0,
          receivedCount: 0
        },
        {
          id: 3,
          title: "新品发布红包雨",
          status: "draft",
          statusText: "草稿",
          timeRange: "未设置",
          minAmount: 1,
          maxAmount: 20,
          duration: 45,
          participantCount: 0,
          sentCount: 0,
          receivedCount: 0
        },
        {
          id: 4,
          title: "春节红包雨",
          status: "ended",
          statusText: "已结束",
          timeRange: "2023-01-20 ~ 2023-02-05",
          minAmount: 1,
          maxAmount: 100,
          duration: 120,
          participantCount: 5189,
          sentCount: 25680,
          receivedCount: 22547
        }
      ],
      // 红包雨设置指南
      guideSteps: [
        {
          title: "设置活动时间",
          description: "选择红包雨活动的开始和结束时间，可设置多场次"
        },
        {
          title: "设置红包参数",
          description: "配置红包金额范围、数量、持续时间等参数"
        },
        {
          title: "设置触发条件",
          description: "可选择自动触发或用户手动触发红包雨"
        },
        {
          title: "设置活动页面",
          description: "自定义红包雨活动页面的背景、文案等元素"
        },
        {
          title: "发布活动",
          description: "预览效果无误后发布活动，可通过多种渠道分享"
        }
      ]
    };
  },
  computed: {
    filteredRainList() {
      if (this.currentTab === 0) {
        return this.rainList;
      } else {
        const statusMap = {
          1: "active",
          2: "upcoming",
          3: "ended",
          4: "draft"
        };
        return this.rainList.filter((item) => item.status === statusMap[this.currentTab]);
      }
    }
  },
  methods: {
    goBack() {
      common_vendor.index.navigateBack();
    },
    showHelp() {
      common_vendor.index.showModal({
        title: "红包雨帮助",
        content: "红包雨是一种互动营销活动，在指定时间内向用户随机发放红包，提高用户活跃度和参与感。",
        showCancel: false
      });
    },
    showDatePicker() {
      common_vendor.index.showToast({
        title: "日期选择功能开发中",
        icon: "none"
      });
    },
    formatNumber(num) {
      return num.toFixed(2);
    },
    switchTab(index) {
      this.currentTab = index;
    },
    createRain() {
      common_vendor.index.showToast({
        title: "创建红包雨功能开发中",
        icon: "none"
      });
    },
    viewRainDetail(item) {
      common_vendor.index.showToast({
        title: "查看详情功能开发中",
        icon: "none"
      });
    },
    editRain(item) {
      common_vendor.index.showToast({
        title: "编辑功能开发中",
        icon: "none"
      });
    },
    shareRain(item) {
      common_vendor.index.showToast({
        title: "分享功能开发中",
        icon: "none"
      });
    },
    deleteRain(item) {
      common_vendor.index.showModal({
        title: "删除确认",
        content: `确定要删除"${item.title}"吗？`,
        success: (res) => {
          if (res.confirm) {
            common_vendor.index.showToast({
              title: "删除成功",
              icon: "success"
            });
          }
        }
      });
    },
    showPreview() {
      common_vendor.index.showToast({
        title: "预览功能开发中",
        icon: "none"
      });
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.o((...args) => $options.goBack && $options.goBack(...args)),
    b: common_vendor.o((...args) => $options.showHelp && $options.showHelp(...args)),
    c: common_vendor.t($data.dateRange),
    d: common_vendor.o((...args) => $options.showDatePicker && $options.showDatePicker(...args)),
    e: common_vendor.t($data.rainData.totalCount),
    f: common_vendor.t($data.rainData.countGrowth),
    g: common_vendor.n($data.rainData.countTrend),
    h: common_vendor.t($data.rainData.totalUsers),
    i: common_vendor.t($data.rainData.usersGrowth),
    j: common_vendor.n($data.rainData.usersTrend),
    k: common_vendor.t($options.formatNumber($data.rainData.totalAmount)),
    l: common_vendor.t($data.rainData.amountGrowth),
    m: common_vendor.n($data.rainData.amountTrend),
    n: common_vendor.t($data.rainData.conversionRate),
    o: common_vendor.t($data.rainData.conversionGrowth),
    p: common_vendor.n($data.rainData.conversionTrend),
    q: common_vendor.o((...args) => $options.createRain && $options.createRain(...args)),
    r: common_vendor.f($data.tabList, (tab, index, i0) => {
      return {
        a: common_vendor.t(tab),
        b: index,
        c: $data.currentTab === index ? 1 : "",
        d: common_vendor.o(($event) => $options.switchTab(index), index)
      };
    }),
    s: common_vendor.f($options.filteredRainList, (item, index, i0) => {
      return common_vendor.e({
        a: common_vendor.t(item.title),
        b: common_vendor.t(item.statusText),
        c: common_vendor.n("status-" + item.status),
        d: common_vendor.t(item.timeRange),
        e: common_vendor.t(item.minAmount),
        f: common_vendor.t(item.maxAmount),
        g: common_vendor.t(item.duration),
        h: common_vendor.t(item.participantCount),
        i: common_vendor.t(item.sentCount),
        j: common_vendor.t(item.receivedCount),
        k: common_vendor.o(($event) => $options.viewRainDetail(item), index),
        l: item.status === "draft"
      }, item.status === "draft" ? {
        m: common_vendor.o(($event) => $options.editRain(item), index)
      } : {}, {
        n: item.status === "active" || item.status === "upcoming"
      }, item.status === "active" || item.status === "upcoming" ? {
        o: common_vendor.o(($event) => $options.shareRain(item), index)
      } : {}, {
        p: item.status === "draft" || item.status === "ended"
      }, item.status === "draft" || item.status === "ended" ? {
        q: common_vendor.o(($event) => $options.deleteRain(item), index)
      } : {}, {
        r: index
      });
    }),
    t: $options.filteredRainList.length === 0
  }, $options.filteredRainList.length === 0 ? {
    v: common_vendor.t($data.tabList[$data.currentTab])
  } : {}, {
    w: common_vendor.f($data.guideSteps, (step, index, i0) => {
      return {
        a: common_vendor.t(index + 1),
        b: common_vendor.t(step.title),
        c: common_vendor.t(step.description),
        d: index
      };
    }),
    x: common_assets._imports_0$38,
    y: common_vendor.o((...args) => $options.showPreview && $options.showPreview(...args)),
    z: common_vendor.o((...args) => $options.createRain && $options.createRain(...args))
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../../../.sourcemap/mp-weixin/subPackages/merchant-admin-marketing/pages/marketing/redpacket/red-rain.js.map
