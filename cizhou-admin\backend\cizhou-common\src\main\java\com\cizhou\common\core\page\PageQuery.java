package com.cizhou.common.core.page;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import lombok.Data;

import java.io.Serializable;

/**
 * 分页查询参数
 *
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Data
@Schema(description = "分页查询参数")
public class PageQuery implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 页码
     */
    @Schema(description = "页码", example = "1")
    @Min(value = 1, message = "页码不能小于1")
    private Long page = 1L;

    /**
     * 每页大小
     */
    @Schema(description = "每页大小", example = "20")
    @Min(value = 1, message = "每页大小不能小于1")
    @Max(value = 1000, message = "每页大小不能大于1000")
    private Long size = 20L;

    /**
     * 排序字段
     */
    @Schema(description = "排序字段", example = "createTime")
    private String sortField;

    /**
     * 排序方向
     */
    @Schema(description = "排序方向", example = "desc", allowableValues = {"asc", "desc"})
    private String sortOrder = "desc";

    /**
     * 转换为MyBatis Plus的Page对象
     */
    public <T> Page<T> toPage() {
        return new Page<>(this.page, this.size);
    }

    /**
     * 转换为MyBatis Plus的Page对象（带排序）
     */
    public <T> Page<T> toPage(String defaultSortField) {
        Page<T> page = new Page<>(this.page, this.size);
        
        String field = this.sortField != null ? this.sortField : defaultSortField;
        if (field != null) {
            if ("asc".equalsIgnoreCase(this.sortOrder)) {
                page.addOrder(com.baomidou.mybatisplus.core.metadata.OrderItem.asc(field));
            } else {
                page.addOrder(com.baomidou.mybatisplus.core.metadata.OrderItem.desc(field));
            }
        }
        
        return page;
    }

    /**
     * 获取偏移量
     */
    public Long getOffset() {
        return (page - 1) * size;
    }

    /**
     * 获取限制数量
     */
    public Long getLimit() {
        return size;
    }

    /**
     * 是否升序
     */
    public boolean isAsc() {
        return "asc".equalsIgnoreCase(this.sortOrder);
    }

    /**
     * 是否降序
     */
    public boolean isDesc() {
        return "desc".equalsIgnoreCase(this.sortOrder);
    }
}
