"use strict";
const common_vendor = require("../common/vendor.js");
const basePromotionMixin = {
  data() {
    return {
      // 是否有推广权限
      hasPromotionPermission: false,
      // 是否可以获取佣金
      canEarnCommission: false,
      // 页面类型，用于确定推广内容类型
      pageType: "",
      // 推广数据
      promotionData: {},
      // 是否显示悬浮推广按钮
      showFloatPromotionButton: false
    };
  },
  created() {
    this.initPromotion();
  },
  mounted() {
    this.checkAndEnablePromotion();
  },
  methods: {
    /**
     * 初始化推广能力
     */
    async initPromotion() {
      this.detectPageType();
      if (!this.pageType) {
        return;
      }
      await this.checkPromotionPermission();
      this.generatePromotionData();
    },
    /**
     * 检查并启用推广功能
     * 自动检测当前页面内容，并决定是否显示推广按钮
     */
    checkAndEnablePromotion() {
      try {
        const pages = getCurrentPages();
        const currentPage = pages[pages.length - 1];
        const route = (currentPage == null ? void 0 : currentPage.route) || "";
        if (route.includes("detail")) {
          const pageData = currentPage.data || {};
          let contentId = "";
          let contentType = "";
          if (pageData.product || pageData.productInfo || pageData.goodsInfo) {
            const product = pageData.product || pageData.productInfo || pageData.goodsInfo;
            contentId = product.id || product._id;
            contentType = "product";
          } else if (pageData.carpool || pageData.carpoolInfo) {
            const carpool = pageData.carpool || pageData.carpoolInfo;
            contentId = carpool.id || carpool._id;
            contentType = "carpool";
          } else if (pageData.shop || pageData.shopData || pageData.merchant) {
            const shop = pageData.shop || pageData.shopData || pageData.merchant;
            contentId = shop.id || shop._id;
            contentType = "merchant";
          } else if (pageData.house || pageData.houseInfo) {
            const house = pageData.house || pageData.houseInfo;
            contentId = house.id || house._id;
            contentType = "house";
          } else if (pageData.service || pageData.serviceInfo) {
            const service = pageData.service || pageData.serviceInfo;
            contentId = service.id || service._id;
            contentType = "service";
          } else if (pageData.article || pageData.content || pageData.news) {
            const content = pageData.article || pageData.content || pageData.news;
            contentId = content.id || content._id;
            contentType = "content";
          }
          if (contentId && contentType) {
            this.pageType = contentType;
            this.promotionData = { id: contentId };
            this.hasPromotionPermission = true;
            this.addFloatPromotionButton();
          }
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at mixins/basePromotionMixin.js:120", "自动检测推广功能失败", error);
      }
    },
    /**
     * 添加悬浮推广按钮
     */
    addFloatPromotionButton() {
      this.showFloatPromotionButton = true;
      if (this.$nextTick) {
        this.$nextTick(() => {
          common_vendor.index.__f__("log", "at mixins/basePromotionMixin.js:135", "添加悬浮推广按钮成功");
        });
      }
    },
    /**
     * 检测页面类型
     */
    detectPageType() {
      var _a, _b;
      const pages = getCurrentPages();
      const currentPage = pages[pages.length - 1];
      const route = (currentPage == null ? void 0 : currentPage.route) || "";
      if (route.includes("carpool")) {
        this.pageType = "carpool";
      } else if (route.includes("secondhand")) {
        this.pageType = "secondhand";
      } else if (route.includes("house")) {
        this.pageType = "house";
      } else if (route.includes("service")) {
        this.pageType = "service";
      } else if (route.includes("community")) {
        this.pageType = "community";
      } else if (route.includes("product") || route.includes("goods")) {
        this.pageType = "product";
      } else if (route.includes("article") || route.includes("news")) {
        this.pageType = "content";
      } else if (route.includes("activity")) {
        this.pageType = "activity";
      }
      this.pageType = this.pageType || ((_b = (_a = this.$route) == null ? void 0 : _a.meta) == null ? void 0 : _b.pageType) || "";
    },
    /**
     * 检查用户推广权限
     */
    async checkPromotionPermission() {
      try {
        this.hasPromotionPermission = this.isContentOwner();
        this.canEarnCommission = this.isDistributor() && this.isCommissionContent();
      } catch (e) {
        common_vendor.index.__f__("error", "at mixins/basePromotionMixin.js:184", "获取推广权限失败", e);
        this.hasPromotionPermission = false;
        this.canEarnCommission = false;
      }
    },
    /**
     * 判断当前用户是否是内容所有者
     */
    isContentOwner() {
      return true;
    },
    /**
     * 判断当前用户是否是分销员
     */
    isDistributor() {
      return true;
    },
    /**
     * 判断当前内容是否支持佣金
     */
    isCommissionContent() {
      return true;
    },
    /**
     * 生成推广数据
     */
    generatePromotionData() {
      this.promotionData = {};
    },
    /**
     * 打开推广工具
     */
    openPromotionTools() {
      if (!this.hasPromotionPermission) {
        common_vendor.index.showToast({
          title: "暂无推广权限",
          icon: "none"
        });
        return;
      }
      const promotionService = require("@/utils/promotionService").default;
      promotionService.showPromotionTools(this.pageType, this.promotionData);
    },
    /**
     * 通用推广方法，可以在任何页面调用
     */
    showPromotion() {
      if (this.pageType && this.promotionData.id) {
        this.openPromotionTools();
      } else {
        common_vendor.index.showToast({
          title: "当前内容不支持推广",
          icon: "none"
        });
      }
    }
  }
};
exports.basePromotionMixin = basePromotionMixin;
//# sourceMappingURL=../../.sourcemap/mp-weixin/mixins/basePromotionMixin.js.map
