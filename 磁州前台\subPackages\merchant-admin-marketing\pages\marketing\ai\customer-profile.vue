<template>
  <view class="customer-profile-container">
    <!-- 自定义导航栏 -->
    <view class="navbar">
      <view class="navbar-back" @click="goBack">
        <view class="back-icon"></view>
      </view>
      <text class="navbar-title">客户画像分析</text>
      <view class="navbar-right">
        <view class="help-icon" @click="showHelp">?</view>
      </view>
    </view>
    
    <!-- 页面内容 -->
    <scroll-view scroll-y class="page-content">
      <!-- 客户概览卡片 -->
      <view class="overview-card">
        <view class="card-header">
          <text class="card-title">客户总览</text>
          <view class="date-filter">
            <text class="date-text">最近30天</text>
            <view class="filter-icon"></view>
          </view>
        </view>
        <view class="overview-metrics">
          <view class="metric-item">
            <text class="metric-value">8,965</text>
            <text class="metric-label">活跃客户</text>
            <text class="metric-growth positive">↑12.8%</text>
          </view>
          <view class="metric-item">
            <text class="metric-value">￥215</text>
            <text class="metric-label">平均客单价</text>
            <text class="metric-growth positive">↑3.5%</text>
          </view>
          <view class="metric-item">
            <text class="metric-value">72%</text>
            <text class="metric-label">复购率</text>
            <text class="metric-growth positive">↑5.2%</text>
          </view>
        </view>
      </view>
      
      <!-- 人口统计学特征 -->
      <view class="demographics-card">
        <view class="card-header">
          <text class="card-title">人口统计特征</text>
          <view class="view-all">查看详情</view>
        </view>
        
        <!-- 性别分布 -->
        <view class="demographic-section">
          <view class="section-title">性别分布</view>
          <view class="gender-chart">
            <!-- SVG实现的环形图 -->
            <svg class="pie-chart" viewBox="0 0 100 100">
              <!-- 女性占比 -->
              <circle
                cx="50"
                cy="50"
                r="40"
                fill="transparent"
                stroke="#EC4899"
                stroke-width="10"
                stroke-dasharray="188.5 251.3"
                stroke-dashoffset="0"
              />
              <!-- 男性占比 -->
              <circle
                cx="50"
                cy="50"
                r="40"
                fill="transparent"
                stroke="#6366F1"
                stroke-width="10"
                stroke-dasharray="62.8 251.3"
                stroke-dashoffset="-188.5"
              />
              <!-- 中心文字 -->
              <text x="50" y="45" text-anchor="middle" font-size="12" font-weight="bold">客户</text>
              <text x="50" y="60" text-anchor="middle" font-size="12" font-weight="bold">性别</text>
            </svg>
            <view class="chart-legend">
              <view class="legend-item">
                <view class="legend-color female"></view>
                <text class="legend-label">女性</text>
                <text class="legend-value">75%</text>
              </view>
              <view class="legend-item">
                <view class="legend-color male"></view>
                <text class="legend-label">男性</text>
                <text class="legend-value">25%</text>
              </view>
            </view>
          </view>
        </view>
        
        <!-- 年龄分布 -->
        <view class="demographic-section">
          <view class="section-title">年龄分布</view>
          <view class="age-chart">
            <!-- SVG实现的柱状图 -->
            <svg class="bar-chart" viewBox="0 0 200 120">
              <!-- 横坐标轴 -->
              <line x1="20" y1="100" x2="190" y2="100" stroke="#EAEAEA" stroke-width="1" />
              
              <!-- 柱状图 -->
              <rect x="30" y="85" width="15" height="15" fill="#C7D2FE" />
              <rect x="60" y="60" width="15" height="40" fill="#818CF8" />
              <rect x="90" y="30" width="15" height="70" fill="#6366F1" />
              <rect x="120" y="45" width="15" height="55" fill="#4F46E5" />
              <rect x="150" y="75" width="15" height="25" fill="#3730A3" />
              
              <!-- 坐标轴标签 -->
              <text x="37" y="115" text-anchor="middle" font-size="10">18-24</text>
              <text x="67" y="115" text-anchor="middle" font-size="10">25-34</text>
              <text x="97" y="115" text-anchor="middle" font-size="10">35-44</text>
              <text x="127" y="115" text-anchor="middle" font-size="10">45-54</text>
              <text x="157" y="115" text-anchor="middle" font-size="10">55+</text>
            </svg>
            <view class="chart-labels">
              <text class="chart-label">35-44岁客户占比最高 (38%)</text>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 消费行为分析 -->
      <view class="behavior-card">
        <view class="card-header">
          <text class="card-title">消费行为分析</text>
          <view class="ai-badge">AI</view>
        </view>
        
        <!-- 购物频率 -->
        <view class="behavior-section">
          <view class="section-title">购物频率分布</view>
          <view class="frequency-chart">
            <view class="frequency-bar" style="width: 70%;">
              <text class="frequency-label">高频客户 (每周)</text>
              <text class="frequency-value">22%</text>
            </view>
            <view class="frequency-bar" style="width: 100%;">
              <text class="frequency-label">中频客户 (每月)</text>
              <text class="frequency-value">48%</text>
            </view>
            <view class="frequency-bar" style="width: 60%;">
              <text class="frequency-label">低频客户 (每季度)</text>
              <text class="frequency-value">30%</text>
            </view>
          </view>
        </view>
        
        <!-- 客单价分布 -->
        <view class="behavior-section">
          <view class="section-title">客单价分布</view>
          <view class="price-chart">
            <!-- SVG曲线图 -->
            <svg class="curve-chart" viewBox="0 0 200 100">
              <!-- 网格线 -->
              <line x1="20" y1="20" x2="20" y2="80" stroke="#EAEAEA" stroke-width="1" />
              <line x1="20" y1="80" x2="180" y2="80" stroke="#EAEAEA" stroke-width="1" />
              <line x1="20" y1="60" x2="180" y2="60" stroke="#EAEAEA" stroke-width="0.5" />
              <line x1="20" y1="40" x2="180" y2="40" stroke="#EAEAEA" stroke-width="0.5" />
              <line x1="20" y1="20" x2="180" y2="20" stroke="#EAEAEA" stroke-width="0.5" />
              
              <!-- 曲线 -->
              <path
                d="M20,70 C40,75 60,50 80,45 S100,35 120,40 S140,50 160,30 S180,20 180,20"
                fill="transparent"
                stroke="#6366F1"
                stroke-width="2"
              />
              
              <!-- 坐标轴标签 -->
              <text x="20" y="95" text-anchor="middle" font-size="8">¥0</text>
              <text x="60" y="95" text-anchor="middle" font-size="8">¥200</text>
              <text x="100" y="95" text-anchor="middle" font-size="8">¥400</text>
              <text x="140" y="95" text-anchor="middle" font-size="8">¥600</text>
              <text x="180" y="95" text-anchor="middle" font-size="8">¥800+</text>
            </svg>
            <view class="chart-labels">
              <text class="chart-label">客单价集中在 ¥100-300 区间</text>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 客户分群 -->
      <view class="segments-card">
        <view class="card-header">
          <text class="card-title">客户分群</text>
          <view class="ai-badge">AI</view>
        </view>
        
        <view class="segment-list">
          <view class="segment-item" v-for="(segment, index) in segments" :key="index"
                @click="viewSegmentDetail(segment)">
            <view class="segment-icon" :class="segment.type"></view>
            <view class="segment-content">
              <text class="segment-name">{{segment.name}}</text>
              <text class="segment-count">{{segment.count}}位客户</text>
              <text class="segment-desc">{{segment.description}}</text>
              <view class="segment-tags">
                <text class="tag" v-for="(tag, tagIndex) in segment.tags" :key="tagIndex">{{tag}}</text>
              </view>
            </view>
            <view class="segment-percent">{{segment.percent}}%</view>
            <view class="segment-arrow"></view>
          </view>
        </view>
      </view>
      
      <!-- 价值区间 -->
      <view class="value-card">
        <view class="card-header">
          <text class="card-title">客户价值分布</text>
          <view class="ai-badge">AI</view>
        </view>
        
        <view class="value-pyramid">
          <view class="pyramid-level high">
            <text class="level-text">高价值客户</text>
            <text class="level-percent">18%</text>
            <text class="level-desc">贡献收入的62%</text>
          </view>
          <view class="pyramid-level medium">
            <text class="level-text">中价值客户</text>
            <text class="level-percent">32%</text>
            <text class="level-desc">贡献收入的28%</text>
          </view>
          <view class="pyramid-level low">
            <text class="level-text">低价值客户</text>
            <text class="level-percent">50%</text>
            <text class="level-desc">贡献收入的10%</text>
          </view>
        </view>
      </view>
      
      <!-- 营销建议 -->
      <view class="recommendation-card">
        <view class="card-header">
          <text class="card-title">基于客户画像的营销建议</text>
          <view class="ai-badge">AI</view>
        </view>
        
        <view class="recommendation-list">
          <view class="recommendation-item" v-for="(rec, index) in recommendations" :key="index">
            <view class="recommendation-icon" :class="rec.type"></view>
            <view class="recommendation-content">
              <text class="recommendation-title">{{rec.title}}</text>
              <text class="recommendation-desc">{{rec.description}}</text>
              <view class="recommendation-actions">
                <text class="action apply" @click="applyRecommendation(index)">应用</text>
                <text class="action ignore" @click="ignoreRecommendation(index)">忽略</text>
              </view>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 底部空间 -->
      <view style="height: 20px;"></view>
    </scroll-view>
    
    <!-- 底部导航栏 -->
    <tab-bar active-tab="marketing" @tab-change="handleTabChange"></tab-bar>
  </view>
</template>

<script>
import TabBar from '../../../../../components/TabBar.vue'

export default {
  components: {
    TabBar
  },
  data() {
    return {
      segments: [
        {
          name: '精致生活家',
          type: 'premium',
          count: 1283,
          percent: 28,
          description: '追求品质生活的高消费人群，对高端美妆和护肤产品有较高需求',
          tags: ['高客单价', '高复购', '追求品质']
        },
        {
          name: '时尚达人',
          type: 'fashion',
          count: 1752,
          percent: 35,
          description: '关注流行趋势，喜欢尝试新产品，消费频次高',
          tags: ['中客单价', '高频次', '新品尝鲜']
        },
        {
          name: '实用主义者',
          type: 'practical',
          count: 1463,
          percent: 26,
          description: '注重性价比，会在促销活动时集中购买',
          tags: ['中客单价', '促销敏感', '理性消费']
        },
        {
          name: '新兴客户',
          type: 'new',
          count: 467,
          percent: 11,
          description: '首次或近期开始购买的客户，消费行为尚未形成稳定模式',
          tags: ['低频次', '尝试阶段', '增长潜力']
        }
      ],
      recommendations: [
        {
          title: '高价值客户维系计划',
          type: 'vip',
          description: '为"精致生活家"群体创建专属VIP会员计划，提供个性化服务和优先购买权，预计可提升该群体复购率15%'
        },
        {
          title: '新品首发活动优化',
          type: 'new',
          description: '针对"时尚达人"群体推出新品首发邀请制活动，提供新品体验和社交分享机会'
        },
        {
          title: '促销活动个性化',
          type: 'promotion',
          description: '为"实用主义者"群体提供更有针对性的促销活动，注重性价比而非单纯折扣'
        },
        {
          title: '新客户培育计划',
          type: 'growth',
          description: '为"新兴客户"提供入门级产品组合和教育内容，帮助建立品牌忠诚度'
        }
      ]
    }
  },
  methods: {
    goBack() {
      uni.navigateBack();
    },
    showHelp() {
      uni.showToast({
        title: '客户画像分析帮助',
        icon: 'none'
      });
    },
    viewSegmentDetail(segment) {
      uni.showToast({
        title: `查看${segment.name}群体详情`,
        icon: 'none'
      });
    },
    applyRecommendation(index) {
      uni.showToast({
        title: `已应用建议：${this.recommendations[index].title}`,
        icon: 'success'
      });
    },
    ignoreRecommendation(index) {
      uni.showToast({
        title: `已忽略建议：${this.recommendations[index].title}`,
        icon: 'none'
      });
    },
    handleTabChange(tabId) {
      // 处理底部标签页切换事件
      console.log('切换到标签:', tabId);
    }
  }
}
</script>

<style lang="scss">
.customer-profile-container {
  min-height: 100vh;
  background-color: #F5F7FA;
}

/* 导航栏样式 */
.navbar {
  position: sticky;
  top: 0;
  left: 0;
  right: 0;
  background: linear-gradient(135deg, #EC4899, #D946EF);
  color: #fff;
  padding: 44px 16px 15px;
  display: flex;
  align-items: center;
  z-index: 100;
  box-shadow: 0 2px 10px rgba(236, 72, 153, 0.15);
}

.navbar-back {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-icon {
  width: 12px;
  height: 12px;
  border-left: 2px solid #fff;
  border-bottom: 2px solid #fff;
  transform: rotate(45deg);
}

.navbar-title {
  flex: 1;
  text-align: center;
  font-size: 18px;
  font-weight: 600;
  letter-spacing: 0.5px;
}

.navbar-right {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.help-icon {
  width: 24px;
  height: 24px;
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: bold;
}

.page-content {
  height: calc(100vh - 77px);
  box-sizing: border-box;
  padding: 15px;
}

/* 卡片通用样式 */
.overview-card, .demographics-card, .behavior-card, .segments-card, .value-card, .recommendation-card {
  background: #FFFFFF;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  padding: 20px;
  margin-bottom: 15px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.view-all {
  font-size: 14px;
  color: #EC4899;
}

.ai-badge {
  display: inline-block;
  background: linear-gradient(135deg, #EC4899, #D946EF);
  color: white;
  font-size: 10px;
  font-weight: 600;
  padding: 3px 6px;
  border-radius: 8px;
  margin-left: 8px;
}

.date-filter {
  display: flex;
  align-items: center;
  background: #F5F7FA;
  border-radius: 15px;
  padding: 5px 10px;
}

.date-text {
  font-size: 12px;
  color: #666;
  margin-right: 5px;
}

.filter-icon {
  width: 8px;
  height: 8px;
  border-top: 1.5px solid #666;
  border-right: 1.5px solid #666;
  transform: rotate(135deg);
}

/* 概览指标样式 */
.overview-metrics {
  display: flex;
  justify-content: space-between;
}

.metric-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 10px;
  position: relative;
}

.metric-item:not(:last-child)::after {
  content: '';
  position: absolute;
  right: 0;
  top: 10%;
  height: 80%;
  width: 1px;
  background-color: #F0F0F0;
}

.metric-value {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin-bottom: 5px;
}

.metric-label {
  font-size: 12px;
  color: #666;
  margin-bottom: 5px;
}

.metric-growth {
  font-size: 12px;
  font-weight: 500;
}

.metric-growth.positive {
  color: #34C759;
}

.metric-growth.negative {
  color: #FF3B30;
}

/* 人口统计学样式 */
.demographic-section {
  margin-bottom: 20px;
}

.section-title {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin-bottom: 15px;
}

.gender-chart {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.pie-chart {
  width: 120px;
  height: 120px;
}

.chart-legend {
  flex: 1;
  padding-left: 20px;
}

.legend-item {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.legend-color {
  width: 12px;
  height: 12px;
  border-radius: 2px;
  margin-right: 8px;
}

.legend-color.female {
  background: #EC4899;
}

.legend-color.male {
  background: #6366F1;
}

.legend-label {
  font-size: 14px;
  color: #333;
  margin-right: 10px;
}

.legend-value {
  font-size: 14px;
  font-weight: 600;
  color: #333;
}

.age-chart {
  margin-top: 10px;
}

.bar-chart {
  width: 100%;
  height: auto;
}

.chart-labels {
  margin-top: 5px;
  text-align: center;
}

.chart-label {
  font-size: 12px;
  color: #666;
}

/* 消费行为样式 */
.behavior-section {
  margin-bottom: 20px;
}

.frequency-chart {
  margin-top: 10px;
}

.frequency-bar {
  height: 30px;
  background: #F0E7FF;
  border-radius: 15px;
  margin-bottom: 10px;
  padding: 0 15px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;
  overflow: hidden;
}

.frequency-bar::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  background: linear-gradient(90deg, #EC4899, #D946EF);
  width: 100%;
  opacity: 0.2;
  border-radius: 15px;
}

.frequency-label {
  font-size: 12px;
  color: #333;
  z-index: 1;
}

.frequency-value {
  font-size: 12px;
  font-weight: 600;
  color: #EC4899;
  z-index: 1;
}

.price-chart {
  margin-top: 10px;
}

.curve-chart {
  width: 100%;
  height: auto;
}

/* 客户分群样式 */
.segment-item {
  display: flex;
  align-items: center;
  padding: 15px;
  background: #F8FAFC;
  border-radius: 10px;
  margin-bottom: 10px;
  cursor: pointer;
}

.segment-icon {
  width: 40px;
  height: 40px;
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  margin-right: 15px;
  flex-shrink: 0;
}

.segment-icon.premium {
  background: linear-gradient(135deg, #F59E0B, #FBBF24);
  color: white;
}

.segment-icon.premium::before {
  content: '💎';
}

.segment-icon.fashion {
  background: linear-gradient(135deg, #EC4899, #F472B6);
  color: white;
}

.segment-icon.fashion::before {
  content: '👗';
}

.segment-icon.practical {
  background: linear-gradient(135deg, #10B981, #34D399);
  color: white;
}

.segment-icon.practical::before {
  content: '🛒';
}

.segment-icon.new {
  background: linear-gradient(135deg, #3B82F6, #60A5FA);
  color: white;
}

.segment-icon.new::before {
  content: '🆕';
}

.segment-content {
  flex: 1;
}

.segment-name {
  font-size: 15px;
  font-weight: 600;
  color: #333;
  margin-bottom: 5px;
}

.segment-count {
  font-size: 12px;
  color: #666;
  margin-bottom: 5px;
}

.segment-desc {
  font-size: 13px;
  color: #666;
  margin-bottom: 8px;
  line-height: 1.4;
}

.segment-tags {
  display: flex;
  flex-wrap: wrap;
}

.tag {
  font-size: 10px;
  color: #666;
  background: #F0F0F0;
  padding: 2px 8px;
  border-radius: 10px;
  margin-right: 6px;
  margin-bottom: 6px;
}

.segment-percent {
  font-size: 16px;
  font-weight: 600;
  color: #EC4899;
  margin: 0 10px;
  flex-shrink: 0;
}

.segment-arrow {
  width: 6px;
  height: 6px;
  border-top: 1.5px solid #999;
  border-right: 1.5px solid #999;
  transform: rotate(45deg);
  flex-shrink: 0;
}

/* 价值分布样式 */
.value-pyramid {
  margin-top: 20px;
}

.pyramid-level {
  display: flex;
  align-items: center;
  height: 50px;
  margin-bottom: 5px;
  padding: 0 20px;
  color: white;
  position: relative;
  overflow: hidden;
}

.pyramid-level.high {
  width: 90%;
  margin-left: 5%;
  background: linear-gradient(90deg, #EC4899, #F472B6);
  border-radius: 8px 8px 0 0;
}

.pyramid-level.medium {
  width: 100%;
  background: linear-gradient(90deg, #8B5CF6, #A78BFA);
}

.pyramid-level.low {
  width: 100%;
  background: linear-gradient(90deg, #3B82F6, #60A5FA);
  border-radius: 0 0 8px 8px;
}

.level-text {
  font-size: 14px;
  font-weight: 600;
}

.level-percent {
  margin-left: auto;
  font-size: 14px;
  font-weight: 600;
}

.level-desc {
  position: absolute;
  right: 20px;
  font-size: 12px;
  opacity: 0.8;
}

/* 营销建议样式 */
.recommendation-item {
  display: flex;
  align-items: flex-start;
  padding: 15px;
  background: #F8FAFC;
  border-radius: 10px;
  margin-bottom: 12px;
}

.recommendation-icon {
  width: 40px;
  height: 40px;
  border-radius: 20px;
  margin-right: 15px;
  flex-shrink: 0;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  font-weight: bold;
  color: white;
}

.recommendation-icon.vip {
  background: linear-gradient(135deg, #F59E0B, #FBBF24);
}

.recommendation-icon.vip::before {
  content: '👑';
}

.recommendation-icon.new {
  background: linear-gradient(135deg, #EC4899, #F472B6);
}

.recommendation-icon.new::before {
  content: '🌟';
}

.recommendation-icon.promotion {
  background: linear-gradient(135deg, #10B981, #34D399);
}

.recommendation-icon.promotion::before {
  content: '🏷️';
}

.recommendation-icon.growth {
  background: linear-gradient(135deg, #3B82F6, #60A5FA);
}

.recommendation-icon.growth::before {
  content: '📈';
}

.recommendation-content {
  flex: 1;
}

.recommendation-title {
  font-size: 14px;
  font-weight: 600;
  color: #333;
  margin-bottom: 5px;
}

.recommendation-desc {
  font-size: 13px;
  color: #666;
  line-height: 1.5;
  margin-bottom: 10px;
}

.recommendation-actions {
  display: flex;
  justify-content: flex-end;
}

.action {
  padding: 5px 15px;
  border-radius: 15px;
  font-size: 12px;
  margin-left: 10px;
}

.action.apply {
  background: #EC4899;
  color: white;
}

.action.ignore {
  background: #F0F0F0;
  color: #666;
}
</style> 