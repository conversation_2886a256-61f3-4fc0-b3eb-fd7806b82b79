<template>
  <view class="marketing-analysis-container">
    <!-- 自定义导航栏 -->
    <view class="navbar">
      <view class="navbar-back" @click="goBack">
        <view class="back-icon"></view>
      </view>
      <text class="navbar-title">营销效果分析</text>
      <view class="navbar-right">
        <view class="export-icon" @click="exportData">📊</view>
      </view>
    </view>
    
    <!-- 日期选择器 -->
    <view class="date-selector-container">
      <view class="date-selector" @click="showDatePicker">
        <text class="date-text">{{currentDateRange}}</text>
        <text class="date-icon">📅</text>
      </view>
    </view>
    
    <!-- 营销概览 -->
    <view class="overview-section">
      <view class="overview-card">
        <view class="overview-item">
          <text class="overview-value">{{marketingData.totalCampaigns}}</text>
          <text class="overview-label">营销活动数</text>
        </view>
        <view class="overview-item">
          <text class="overview-value">¥{{marketingData.totalSpend}}</text>
          <text class="overview-label">营销总支出</text>
        </view>
        <view class="overview-item">
          <text class="overview-value">¥{{marketingData.totalRevenue}}</text>
          <text class="overview-label">营销总收入</text>
        </view>
        <view class="overview-item">
          <text class="overview-value">{{marketingData.avgROI}}%</text>
          <text class="overview-label">平均ROI</text>
          <view :class="['trend-icon', marketingData.avgROI >= 100 ? 'up' : 'down']"></view>
        </view>
      </view>
    </view>
    
    <!-- 各类营销活动ROI对比 -->
    <view class="roi-section">
      <view class="section-header">
        <text class="section-title">营销活动ROI对比</text>
        <text class="section-more" @click="navigateTo('./roi')">查看详情</text>
      </view>
      
      <view class="roi-card">
        <view class="roi-chart">
          <!-- 这里应该是图表组件，暂时用占位符 -->
          <view class="chart-placeholder">
            <text class="chart-text">营销活动ROI对比图</text>
          </view>
        </view>
        
        <view class="roi-list">
          <view 
            v-for="(campaign, index) in roiData.campaigns" 
            :key="index"
            class="roi-item"
            :class="{'high-roi': campaign.roi > 200, 'low-roi': campaign.roi < 100}">
            <view class="campaign-info">
              <text class="campaign-name">{{campaign.name}}</text>
              <text class="campaign-date">{{campaign.date}}</text>
            </view>
            <view class="roi-stats">
              <view class="stats-row">
                <text class="stats-label">投入</text>
                <text class="stats-value">¥{{campaign.spend}}</text>
              </view>
              <view class="stats-row">
                <text class="stats-label">收入</text>
                <text class="stats-value">¥{{campaign.revenue}}</text>
              </view>
              <view class="stats-row">
                <text class="stats-label">ROI</text>
                <text :class="['stats-value', 'roi-value', {'positive': campaign.roi >= 100, 'negative': campaign.roi < 100}]">{{campaign.roi}}%</text>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 引流转化漏斗分析 -->
    <view class="funnel-section">
      <view class="section-header">
        <text class="section-title">引流转化漏斗分析</text>
        <text class="section-more" @click="navigateTo('./funnel')">查看详情</text>
      </view>
      
      <view class="funnel-card">
        <view class="funnel-chart">
          <!-- 这里应该是图表组件，暂时用占位符 -->
          <view class="chart-placeholder">
            <text class="chart-text">转化漏斗图</text>
          </view>
        </view>
        
        <view class="funnel-stages">
          <view 
            v-for="(stage, index) in funnelData.stages" 
            :key="index"
            class="stage-item">
            <view class="stage-header">
              <view class="stage-icon" :style="{backgroundColor: stage.color}">{{stage.icon}}</view>
              <view class="stage-info">
                <text class="stage-name">{{stage.name}}</text>
                <text class="stage-count">{{stage.count}}人</text>
              </view>
            </view>
            <view class="stage-bar">
              <view class="bar-fill" :style="{width: stage.percentage + '%', backgroundColor: stage.color}"></view>
            </view>
            <view class="stage-footer">
              <text class="conversion-rate">转化率: {{stage.conversionRate}}%</text>
              <text class="dropout-rate">流失率: {{stage.dropoutRate}}%</text>
            </view>
          </view>
        </view>
        
        <view class="funnel-tips">
          <view class="tip-icon">💡</view>
          <view class="tip-content">
            <text class="tip-title">转化优化建议</text>
            <text class="tip-text">{{funnelData.optimizationTip}}</text>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 促销敏感度分析 -->
    <view class="sensitivity-section">
      <view class="section-header">
        <text class="section-title">促销敏感度分析</text>
        <text class="section-more" @click="navigateTo('./sensitivity')">查看详情</text>
      </view>
      
      <view class="sensitivity-card">
        <view class="sensitivity-chart">
          <!-- 这里应该是图表组件，暂时用占位符 -->
          <view class="chart-placeholder">
            <text class="chart-text">促销敏感度分析图</text>
          </view>
        </view>
        
        <view class="sensitivity-categories">
          <text class="sub-title">商品类别促销敏感度</text>
          <view class="category-list">
            <view 
              v-for="(category, index) in sensitivityData.categories" 
              :key="index"
              class="category-item">
              <text class="category-name">{{category.name}}</text>
              <view class="sensitivity-bar">
                <view class="bar-fill" :style="{width: category.sensitivity + '%', backgroundColor: getSensitivityColor(category.sensitivity)}"></view>
                <text class="sensitivity-value">{{category.sensitivity}}%</text>
              </view>
            </view>
          </view>
        </view>
        
        <view class="sensitivity-insights">
          <text class="sub-title">促销敏感度洞察</text>
          <view class="insights-list">
            <view 
              v-for="(insight, index) in sensitivityData.insights" 
              :key="index"
              class="insight-item">
              <view class="insight-icon">{{insight.icon}}</view>
              <view class="insight-content">
                <text class="insight-title">{{insight.title}}</text>
                <text class="insight-desc">{{insight.description}}</text>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      currentDateRange: '2023-05-01 至 2023-05-31',
      marketingData: {
        totalCampaigns: '12',
        totalSpend: '25,600.00',
        totalRevenue: '68,450.00',
        avgROI: '267.4'
      },
      roiData: {
        campaigns: [
          {
            name: '618促销活动',
            date: '2023-06-18',
            spend: '8,500.00',
            revenue: '28,650.00',
            roi: 337.1
          },
          {
            name: '会员专享日',
            date: '2023-05-20',
            spend: '5,200.00',
            revenue: '15,680.00',
            roi: 301.5
          },
          {
            name: '新品上市推广',
            date: '2023-05-10',
            spend: '3,800.00',
            revenue: '9,520.00',
            roi: 250.5
          },
          {
            name: '周年庆活动',
            date: '2023-04-15',
            spend: '6,800.00',
            revenue: '12,450.00',
            roi: 183.1
          },
          {
            name: '清仓特卖',
            date: '2023-04-01',
            spend: '1,300.00',
            revenue: '2,150.00',
            roi: 165.4
          }
        ]
      },
      funnelData: {
        stages: [
          {
            name: '浏览商品',
            count: 10000,
            percentage: 100,
            conversionRate: 100,
            dropoutRate: 0,
            icon: '👀',
            color: '#1677FF'
          },
          {
            name: '加入购物车',
            count: 3500,
            percentage: 35,
            conversionRate: 35,
            dropoutRate: 65,
            icon: '🛒',
            color: '#52c41a'
          },
          {
            name: '提交订单',
            count: 1800,
            percentage: 18,
            conversionRate: 51.4,
            dropoutRate: 48.6,
            icon: '📝',
            color: '#fa8c16'
          },
          {
            name: '支付成功',
            count: 1250,
            percentage: 12.5,
            conversionRate: 69.4,
            dropoutRate: 30.6,
            icon: '💰',
            color: '#722ed1'
          },
          {
            name: '复购',
            count: 380,
            percentage: 3.8,
            conversionRate: 30.4,
            dropoutRate: 69.6,
            icon: '🔄',
            color: '#eb2f96'
          }
        ],
        optimizationTip: '建议优化购物车到提交订单环节，简化下单流程，增加购物车商品推荐'
      },
      sensitivityData: {
        categories: [
          {
            name: '生鲜果蔬',
            sensitivity: 85
          },
          {
            name: '零食饮料',
            sensitivity: 72
          },
          {
            name: '美妆个护',
            sensitivity: 68
          },
          {
            name: '家居日用',
            sensitivity: 45
          },
          {
            name: '母婴用品',
            sensitivity: 38
          },
          {
            name: '电子产品',
            sensitivity: 25
          }
        ],
        insights: [
          {
            icon: '🔍',
            title: '高敏感度类别',
            description: '生鲜果蔬、零食饮料对促销反应最为敏感，建议在这些类别重点投放促销资源'
          },
          {
            icon: '📊',
            title: '最佳促销力度',
            description: '对于高敏感度类别，15%-25%的折扣力度性价比最高；低敏感度类别需要30%以上折扣才有明显效果'
          },
          {
            icon: '⏰',
            title: '最佳促销时间',
            description: '周五至周日是促销效果最好的时间段，尤其是周五晚上18:00-21:00效果最佳'
          }
        ]
      }
    }
  },
  methods: {
    goBack() {
      uni.navigateBack();
    },
    navigateTo(url) {
      uni.navigateTo({ url });
    },
    showDatePicker() {
      uni.showToast({
        title: '日期选择功能开发中',
        icon: 'none'
      });
    },
    exportData() {
      uni.showActionSheet({
        itemList: ['导出Excel', '导出PDF', '生成分析报告'],
        success: (res) => {
          uni.showToast({
            title: '导出功能开发中',
            icon: 'none'
          });
        }
      });
    },
    getSensitivityColor(sensitivity) {
      if (sensitivity >= 70) {
        return '#f5222d';
      } else if (sensitivity >= 50) {
        return '#fa8c16';
      } else if (sensitivity >= 30) {
        return '#52c41a';
      } else {
        return '#1677FF';
      }
    }
  }
}
</script>

<style>
.marketing-analysis-container {
  min-height: 100vh;
  background-color: #f5f7fa;
  padding-bottom: 20px;
}

.navbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 44px 16px 10px;
  background: linear-gradient(135deg, #1677FF, #065DD2);
  position: relative;
  z-index: 100;
}

.navbar-back {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
}

.back-icon {
  width: 12px;
  height: 12px;
  border-left: 2px solid #fff;
  border-bottom: 2px solid #fff;
  transform: rotate(45deg);
}

.navbar-title {
  color: #fff;
  font-size: 18px;
  font-weight: 600;
  flex: 1;
  text-align: center;
}

.navbar-right {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.export-icon {
  font-size: 20px;
  color: #fff;
}

.date-selector-container {
  padding: 16px;
  display: flex;
  justify-content: flex-end;
}

.date-selector {
  display: flex;
  align-items: center;
  background-color: #fff;
  padding: 8px 12px;
  border-radius: 16px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.date-text {
  font-size: 14px;
  color: #666;
  margin-right: 8px;
}

.date-icon {
  font-size: 16px;
}

.overview-section, .roi-section, .funnel-section, .sensitivity-section {
  padding: 0 16px;
  margin-bottom: 20px;
}

.overview-card, .roi-card, .funnel-card, .sensitivity-card {
  background-color: #fff;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.05);
}

.overview-card {
  display: flex;
  justify-content: space-between;
}

.overview-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
}

.overview-value {
  font-size: 20px;
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
}

.overview-label {
  font-size: 12px;
  color: #999;
}

.trend-icon {
  width: 0;
  height: 0;
  position: absolute;
  right: -15px;
  top: 8px;
}

.trend-icon.up {
  border-left: 6px solid transparent;
  border-right: 6px solid transparent;
  border-bottom: 8px solid #52c41a;
}

.trend-icon.down {
  border-left: 6px solid transparent;
  border-right: 6px solid transparent;
  border-top: 8px solid #f5222d;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.section-more {
  font-size: 12px;
  color: #1677FF;
}

.roi-chart, .funnel-chart, .sensitivity-chart {
  height: 180px;
  margin-bottom: 16px;
}

.chart-placeholder {
  height: 100%;
  background-color: #f9f9f9;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.chart-text {
  font-size: 14px;
  color: #999;
}

.roi-list {
  
}

.roi-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
}

.roi-item:last-child {
  border-bottom: none;
}

.roi-item.high-roi {
  background-color: rgba(82, 196, 26, 0.05);
}

.roi-item.low-roi {
  background-color: rgba(245, 34, 45, 0.05);
}

.campaign-info {
  flex: 1;
}

.campaign-name {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
  display: block;
}

.campaign-date {
  font-size: 12px;
  color: #999;
}

.roi-stats {
  min-width: 120px;
}

.stats-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 4px;
}

.stats-row:last-child {
  margin-bottom: 0;
}

.stats-label {
  font-size: 12px;
  color: #999;
}

.stats-value {
  font-size: 12px;
  color: #333;
  font-weight: 500;
}

.roi-value {
  font-weight: 600;
}

.roi-value.positive {
  color: #52c41a;
}

.roi-value.negative {
  color: #f5222d;
}

.funnel-stages {
  
}

.stage-item {
  margin-bottom: 16px;
}

.stage-item:last-child {
  margin-bottom: 0;
}

.stage-header {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.stage-icon {
  width: 28px;
  height: 28px;
  border-radius: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  margin-right: 8px;
}

.stage-info {
  flex: 1;
  display: flex;
  justify-content: space-between;
}

.stage-name {
  font-size: 14px;
  font-weight: 500;
  color: #333;
}

.stage-count {
  font-size: 14px;
  color: #666;
}

.stage-bar {
  height: 8px;
  background-color: #f0f0f0;
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 8px;
}

.bar-fill {
  height: 100%;
  border-radius: 4px;
}

.stage-footer {
  display: flex;
  justify-content: space-between;
}

.conversion-rate {
  font-size: 12px;
  color: #52c41a;
}

.dropout-rate {
  font-size: 12px;
  color: #f5222d;
}

.funnel-tips, .sensitivity-insights {
  margin-top: 16px;
}

.funnel-tips {
  display: flex;
  background-color: #f6ffed;
  padding: 12px;
  border-radius: 4px;
}

.tip-icon {
  font-size: 20px;
  margin-right: 12px;
}

.tip-content {
  flex: 1;
}

.tip-title {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
  display: block;
}

.tip-text {
  font-size: 12px;
  color: #666;
  line-height: 1.5;
}

.sub-title {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin-bottom: 12px;
  display: block;
}

.sensitivity-categories {
  margin-bottom: 16px;
}

.category-list {
  
}

.category-item {
  margin-bottom: 12px;
}

.category-item:last-child {
  margin-bottom: 0;
}

.category-name {
  font-size: 14px;
  color: #333;
  margin-bottom: 6px;
  display: block;
}

.sensitivity-bar {
  height: 8px;
  background-color: #f0f0f0;
  border-radius: 4px;
  overflow: hidden;
  position: relative;
}

.sensitivity-value {
  position: absolute;
  right: 0;
  top: -20px;
  font-size: 12px;
  color: #666;
}

.insights-list {
  
}

.insight-item {
  display: flex;
  margin-bottom: 12px;
  padding-bottom: 12px;
  border-bottom: 1px solid #f0f0f0;
}

.insight-item:last-child {
  margin-bottom: 0;
  padding-bottom: 0;
  border-bottom: none;
}

.insight-icon {
  font-size: 20px;
  margin-right: 12px;
}

.insight-content {
  flex: 1;
}

.insight-title {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
  display: block;
}

.insight-desc {
  font-size: 12px;
  color: #666;
  line-height: 1.5;
}
</style> 