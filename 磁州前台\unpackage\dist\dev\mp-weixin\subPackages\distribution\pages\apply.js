"use strict";
const common_vendor = require("../../../common/vendor.js");
const utils_distributionService = require("../../../utils/distributionService.js");
const _sfc_main = {
  __name: "apply",
  setup(__props) {
    const conditions = common_vendor.reactive({
      requirePurchase: true,
      minimumPurchase: 100,
      requireApproval: true,
      autoApprove: false,
      needRealName: true,
      needMobile: true,
      description: ""
    });
    const conditionStatus = common_vendor.reactive({
      purchase: false,
      realName: false,
      mobile: true
      // 假设已绑定手机号
    });
    const formData = common_vendor.reactive({
      name: "",
      mobile: "",
      wechat: "",
      reason: "",
      agreeAgreement: false
    });
    const canSubmit = common_vendor.computed(() => {
      const conditionsMet = (!conditions.requirePurchase || conditionStatus.purchase) && (!conditions.needRealName || conditionStatus.realName) && (!conditions.needMobile || conditionStatus.mobile);
      const formValid = formData.name.trim() !== "" && formData.mobile.trim() !== "" && formData.mobile.length === 11 && formData.wechat.trim() !== "" && formData.reason.trim() !== "" && formData.agreeAgreement;
      return conditionsMet && formValid;
    });
    common_vendor.onMounted(async () => {
      await getDistributionConditions();
      await checkConditionStatus();
    });
    const getDistributionConditions = async () => {
      try {
        const result = await utils_distributionService.distributionService.getDistributionConditions();
        if (result) {
          Object.assign(conditions, result);
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at subPackages/distribution/pages/apply.vue:186", "获取分销条件失败", error);
        common_vendor.index.showToast({
          title: "获取分销条件失败",
          icon: "none"
        });
      }
    };
    const checkConditionStatus = async () => {
      try {
        conditionStatus.purchase = true;
        conditionStatus.realName = false;
      } catch (error) {
        common_vendor.index.__f__("error", "at subPackages/distribution/pages/apply.vue:202", "检查条件状态失败", error);
      }
    };
    const toggleAgreement = () => {
      formData.agreeAgreement = !formData.agreeAgreement;
    };
    const showAgreement = async () => {
      try {
        const agreement = await utils_distributionService.distributionService.getDistributionAgreement();
        common_vendor.index.showModal({
          title: "分销员协议",
          content: agreement.substring(0, 500) + "...",
          confirmText: "查看全文",
          success: (res) => {
            if (res.confirm) {
              common_vendor.index.navigateTo({
                url: "/subPackages/merchant-admin-marketing/pages/marketing/distribution/agreement"
              });
            }
          }
        });
      } catch (error) {
        common_vendor.index.__f__("error", "at subPackages/distribution/pages/apply.vue:230", "获取分销协议失败", error);
      }
    };
    const submitApplication = async () => {
      if (!canSubmit.value) {
        return;
      }
      try {
        common_vendor.index.showLoading({
          title: "提交中...",
          mask: true
        });
        const result = await utils_distributionService.distributionService.applyDistributor({
          name: formData.name,
          mobile: formData.mobile,
          wechat: formData.wechat,
          reason: formData.reason
        });
        common_vendor.index.hideLoading();
        if (result.success) {
          common_vendor.index.showModal({
            title: "申请提交成功",
            content: conditions.autoApprove ? "您的分销员申请已自动通过，现在您可以开始分销推广了！" : "您的分销员申请已提交，请耐心等待审核。",
            showCancel: false,
            success: () => {
              common_vendor.index.navigateBack();
            }
          });
        } else {
          common_vendor.index.showModal({
            title: "申请提交失败",
            content: result.message || "请稍后再试",
            showCancel: false
          });
        }
      } catch (error) {
        common_vendor.index.hideLoading();
        common_vendor.index.__f__("error", "at subPackages/distribution/pages/apply.vue:274", "提交申请失败", error);
        common_vendor.index.showToast({
          title: "提交申请失败",
          icon: "none"
        });
      }
    };
    const navigateToShop = () => {
      common_vendor.index.switchTab({
        url: "/pages/business/business"
      });
    };
    const navigateToRealName = () => {
      common_vendor.index.navigateTo({
        url: "/pages/my/real-name"
      });
    };
    const navigateToBindMobile = () => {
      common_vendor.index.navigateTo({
        url: "/pages/my/bind-mobile"
      });
    };
    const goBack = () => {
      common_vendor.index.navigateBack();
    };
    const showHelp = () => {
      common_vendor.index.showModal({
        title: "申请帮助",
        content: "成为分销员需要满足平台设置的条件，并提交申请信息。审核通过后，您将成为平台分销员，可以通过分享商品获得佣金。",
        showCancel: false
      });
    };
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_vendor.o(goBack),
        b: common_vendor.o(showHelp),
        c: conditionStatus.purchase ? 1 : "",
        d: common_vendor.t(conditions.requirePurchase ? "购买任意商品" : "无需购买商品"),
        e: conditions.requirePurchase
      }, conditions.requirePurchase ? {
        f: common_vendor.t(conditions.minimumPurchase)
      } : {}, {
        g: conditionStatus.purchase
      }, conditionStatus.purchase ? {} : {
        h: common_vendor.o(navigateToShop)
      }, {
        i: conditionStatus.purchase ? 1 : "",
        j: conditionStatus.realName ? 1 : "",
        k: common_vendor.t(conditions.needRealName ? "实名认证" : "无需实名认证"),
        l: conditions.needRealName
      }, conditions.needRealName ? {} : {}, {
        m: conditionStatus.realName
      }, conditionStatus.realName ? {} : {
        n: common_vendor.o(navigateToRealName)
      }, {
        o: conditionStatus.realName ? 1 : "",
        p: conditionStatus.mobile ? 1 : "",
        q: common_vendor.t(conditions.needMobile ? "绑定手机号" : "无需绑定手机号"),
        r: conditions.needMobile
      }, conditions.needMobile ? {} : {}, {
        s: conditionStatus.mobile
      }, conditionStatus.mobile ? {} : {
        t: common_vendor.o(navigateToBindMobile)
      }, {
        v: conditionStatus.mobile ? 1 : "",
        w: formData.name,
        x: common_vendor.o(($event) => formData.name = $event.detail.value),
        y: formData.mobile,
        z: common_vendor.o(($event) => formData.mobile = $event.detail.value),
        A: formData.wechat,
        B: common_vendor.o(($event) => formData.wechat = $event.detail.value),
        C: formData.reason,
        D: common_vendor.o(($event) => formData.reason = $event.detail.value),
        E: common_vendor.t(formData.reason.length),
        F: formData.agreeAgreement ? 1 : "",
        G: common_vendor.o(toggleAgreement),
        H: common_vendor.o(showAgreement),
        I: !canSubmit.value,
        J: !canSubmit.value ? 1 : "",
        K: common_vendor.o(submitApplication),
        L: common_vendor.t(conditions.autoApprove ? "系统将自动审核" : "平台将在1-3个工作日内审核")
      });
    };
  }
};
wx.createPage(_sfc_main);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/subPackages/distribution/pages/apply.js.map
