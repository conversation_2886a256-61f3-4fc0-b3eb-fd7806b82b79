{"version": 3, "file": "index.js", "sources": ["subPackages/merchant-admin-home/pages/merchant-home/index.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/c3ViUGFja2FnZXNcbWVyY2hhbnQtYWRtaW4taG9tZVxwYWdlc1xtZXJjaGFudC1ob21lXGluZGV4LnZ1ZQ"], "sourcesContent": ["<template>\n  <view class=\"merchant-dashboard\">\n    <!-- 状态栏背景 -->\n    <view class=\"status-bar-bg\"></view>\n    \n    <!-- 顶部导航栏 -->\n    <view class=\"navbar\">\n      <view class=\"navbar-left\">\n        <view class=\"back-icon\" @tap=\"goBack\"></view>\n        </view>\n      <text class=\"navbar-title\">商家中心</text>\n      <view class=\"navbar-right\"></view>\n    </view>\n\n    <!-- 主内容区域 -->\n    <view class=\"content-container\">\n      <!-- 业绩概览卡片 - 更精致的设计 -->\n      <view class=\"performance-card\">\n        <view class=\"performance-header\">\n          <view class=\"performance-title\">\n            <view class=\"greeting-container\">\n            <text class=\"greeting\">您好，{{userInfo.name}}</text>\n              <svg class=\"greeting-icon\" width=\"18\" height=\"18\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n                <path d=\"M12 17V17.01M12 13.5C12 12.12 13.12 11 14.5 11C15.88 11 17 12.12 17 13.5C17 14.88 15.88 16 14.5 16H14.475C14.4558 15.9997 14.437 15.9948 14.4201 15.9858C14.4032 15.9768 14.3886 15.9639 14.3777 15.9482C14.3667 15.9325 14.3598 15.9144 14.3573 15.8955C14.3549 15.8766 14.357 15.8573 14.364 15.84L15 13.5H14.5C13.12 13.5 12 12.38 12 11C12 9.62 13.12 8.5 14.5 8.5C15.88 8.5 17 9.62 17 11V11.5\" stroke=\"#0A84FF\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n                <path d=\"M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z\" stroke=\"#0A84FF\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n              </svg>\n            </view>\n            <text class=\"date\">{{currentDate}}</text>\n          </view>\n          <view class=\"merchant-badge\">\n            <svg class=\"merchant-badge-icon\" width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n              <path d=\"M12 15C15.866 15 19 11.866 19 8C19 4.13401 15.866 1 12 1C8.13401 1 5 4.13401 5 8C5 11.866 8.13401 15 12 15Z\" fill=\"white\" fill-opacity=\"0.2\"/>\n              <path d=\"M12 15C15.866 15 19 11.866 19 8C19 4.13401 15.866 1 12 1C8.13401 1 5 4.13401 5 8C5 11.866 8.13401 15 12 15Z\" stroke=\"white\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n              <path d=\"M8.21 13.89L7 23L12 20L17 23L15.79 13.88\" stroke=\"white\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n            </svg>\n            <text>{{merchantInfo.level}}</text>\n      </view>\n    </view>\n\n        <view class=\"performance-metrics\">\n          <view class=\"metric-item\">\n            <text class=\"metric-value\">¥{{todayIncome}}</text>\n            <text class=\"metric-label\">今日收入</text>\n          </view>\n          <view class=\"metric-divider\"></view>\n          <view class=\"metric-item\">\n            <text class=\"metric-value\">{{todayOrders}}</text>\n            <text class=\"metric-label\">今日订单</text>\n          </view>\n          <view class=\"metric-divider\"></view>\n          <view class=\"metric-item\">\n            <text class=\"metric-value\">{{pendingOrders}}</text>\n            <text class=\"metric-label\">待处理</text>\n          </view>\n        </view>\n      </view>\n      \n      <!-- 快捷功能卡片 - 更现代的设计 -->\n      <view class=\"quick-actions-card\">\n        <view class=\"section-header\">\n          <view class=\"overview-title\">\n            <svg class=\"overview-icon\" width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n              <path d=\"M19 11H5C3.89543 11 3 11.8954 3 13V20C3 21.1046 3.89543 22 5 22H19C20.1046 22 21 21.1046 21 20V13C21 11.8954 20.1046 11 19 11Z\" stroke=\"#0A84FF\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n              <path d=\"M7 11V7C7 5.93913 7.42143 4.92172 8.17157 4.17157C8.92172 3.42143 9.93913 3 11 3H13C14.0609 3 15.0783 3.42143 15.8284 4.17157C16.5786 4.92172 17 5.93913 17 7V11\" stroke=\"#0A84FF\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n            </svg>\n          <text>快捷功能</text>\n        </view>\n          <view class=\"section-action\">\n            <text class=\"action-text\">自定义</text>\n            <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n              <path d=\"M9 18L15 12L9 6\" stroke=\"#0A84FF\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n            </svg>\n          </view>\n        </view>\n        \n        <view class=\"quick-actions\">\n          <view \n            class=\"action-item\" \n            v-for=\"(action, index) in quickActions\" \n            :key=\"index\" \n            @click.stop=\"handleQuickAction(action)\"\n          >\n            <view class=\"action-icon-container\" :class=\"action.icon\">\n              <svg v-if=\"action.icon === 'icon-product'\" class=\"action-svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n                <path d=\"M20 7L12 3L4 7M20 7V17L12 21M20 7L12 11M12 21L4 17V7M12 21V11M4 7L12 11\" stroke=\"white\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n              </svg>\n              <svg v-else-if=\"action.icon === 'icon-verification'\" class=\"action-svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n                <path d=\"M9 11L12 14L22 4M21 12V19C21 19.5304 20.7893 20.0391 20.4142 20.4142C20.0391 20.7893 19.5304 21 19 21H5C4.46957 21 3.96086 20.7893 3.58579 20.4142C3.21071 20.0391 3 19.5304 3 19V5C3 4.46957 3.21071 3.96086 3.58579 3.58579C3.96086 3.21071 4.46957 3 5 3H16\" stroke=\"white\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n              </svg>\n              <svg v-else-if=\"action.icon === 'icon-order'\" class=\"action-svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n                <path d=\"M9 17H15M9 13H15M9 9H15M5 21H19C20.1046 21 21 20.1046 21 19V5C21 3.89543 20.1046 3 19 3H5C3.89543 3 3 3.89543 3 5V19C3 20.1046 3.89543 21 5 21Z\" stroke=\"white\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n              </svg>\n              <svg v-else-if=\"action.icon === 'icon-analysis'\" class=\"action-svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n                <path d=\"M16 8V16M12 11V16M8 14V16M6 20H18C19.1046 20 20 19.1046 20 18V6C20 4.89543 19.1046 4 18 4H6C4.89543 4 4 4.89543 4 6V18C4 19.1046 4.89543 20 6 20Z\" stroke=\"white\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n              </svg>\n              <svg v-else-if=\"action.icon === 'icon-customer'\" class=\"action-svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n                <path d=\"M17 21V19C17 17.9391 16.5786 16.9217 15.8284 16.1716C15.0783 15.4214 14.0609 15 13 15H5C3.93913 15 2.92172 15.4214 2.17157 16.1716C1.42143 16.9217 1 17.9391 1 19V21M23 21V19C22.9993 18.1137 22.7044 17.2528 22.1614 16.5523C21.6184 15.8519 20.8581 15.3516 20 15.13M16 3.13C16.8604 3.35031 17.623 3.85071 18.1676 4.55232C18.7122 5.25392 19.0078 6.11683 19.0078 7.005C19.0078 7.89318 18.7122 8.75608 18.1676 9.45769C17.623 10.1593 16.8604 10.6597 16 10.88M13 7C13 9.20914 11.2091 11 9 11C6.79086 11 5 9.20914 5 7C5 4.79086 6.79086 3 9 3C11.2091 3 13 4.79086 13 7Z\" stroke=\"white\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n              </svg>\n              <svg v-else-if=\"action.icon === 'icon-campaign'\" class=\"action-svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n                <path d=\"M11 17.25C11 17.25 16 14 16 9.75C16 5.5 11 5.5 11 9.75M11 17.25C11 17.25 6 14 6 9.75C6 5.5 11 5.5 11 9.75M11 17.25V21M11 9.75V3\" stroke=\"white\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n              </svg>\n              <svg v-else-if=\"action.icon === 'icon-setting'\" class=\"action-svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n                <path d=\"M12 15C13.6569 15 15 13.6569 15 12C15 10.3431 13.6569 9 12 9C10.3431 9 9 10.3431 9 12C9 13.6569 10.3431 15 12 15Z\" stroke=\"white\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n                <path d=\"M19.4 15C19.2669 15.3016 19.2272 15.6362 19.286 15.9606C19.3448 16.285 19.4995 16.5843 19.73 16.82L19.79 16.88C19.976 17.0657 20.1235 17.2863 20.2241 17.5291C20.3248 17.7719 20.3766 18.0322 20.3766 18.295C20.3766 18.5578 20.3248 18.8181 20.2241 19.0609C20.1235 19.3037 19.976 19.5243 19.79 19.71C19.6043 19.896 19.3837 20.0435 19.1409 20.1441C18.8981 20.2448 18.6378 20.2966 18.375 20.2966C18.1122 20.2966 17.8519 20.2448 17.6091 20.1441C17.3663 20.0435 17.1457 19.896 16.96 19.71L16.9 19.65C16.6643 19.4195 16.365 19.2648 16.0406 19.206C15.7162 19.1472 15.3816 19.1869 15.08 19.32C14.7842 19.4468 14.532 19.6572 14.3543 19.9255C14.1766 20.1938 14.0813 20.5082 14.08 20.83V21C14.08 21.5304 13.8693 22.0391 13.4942 22.4142C13.1191 22.7893 12.6104 23 12.08 23C11.5496 23 11.0409 22.7893 10.6658 22.4142C10.2907 22.0391 10.08 21.5304 10.08 21V20.91C10.0723 20.579 9.96512 20.258 9.77251 19.9887C9.5799 19.7194 9.31074 19.5143 9 19.4C8.69838 19.2669 8.36381 19.2272 8.03941 19.286C7.71502 19.3448 7.41568 19.4995 7.18 19.73L7.12 19.79C6.93425 19.976 6.71368 20.1235 6.47088 20.2241C6.22808 20.3248 5.96783 20.3766 5.705 20.3766C5.44217 20.3766 5.18192 20.3248 4.93912 20.2241C4.69632 20.1235 4.47575 19.976 4.29 19.79C4.10405 19.6043 3.95653 19.3837 3.85588 19.1409C3.75523 18.8981 3.70343 18.6378 3.70343 18.375C3.70343 18.1122 3.75523 17.8519 3.85588 17.6091C3.95653 17.3663 4.10405 17.1457 4.29 16.96L4.35 16.9C4.58054 16.6643 4.73519 16.365 4.794 16.0406C4.85282 15.7162 4.81312 15.3816 4.68 15.08C4.55324 14.7842 4.34276 14.532 4.07447 14.3543C3.80618 14.1766 3.49179 14.0813 3.17 14.08H3C2.46957 14.08 1.96086 13.8693 1.58579 13.4942C1.21071 13.1191 1 12.6104 1 12.08C1 11.5496 1.21071 11.0409 1.58579 10.6658C1.96086 10.2907 2.46957 10.08 3 10.08H3.09C3.42099 10.0723 3.742 9.96512 4.0113 9.77251C4.28059 9.5799 4.48572 9.31074 4.6 9C4.73312 8.69838 4.77282 8.36381 4.714 8.03941C4.65519 7.71502 4.50054 7.41568 4.27 7.18L4.21 7.12C4.02405 6.93425 3.87653 6.71368 3.77588 6.47088C3.67523 6.22808 3.62343 5.96783 3.62343 5.705C3.62343 5.44217 3.67523 5.18192 3.77588 4.93912C3.87653 4.69632 4.02405 4.47575 4.21 4.29C4.39575 4.10405 4.61632 3.95653 4.85912 3.85588C5.10192 3.75523 5.36217 3.70343 5.625 3.70343C5.88783 3.70343 6.14808 3.75523 6.39088 3.85588C6.63368 3.95653 6.85425 4.10405 7.04 4.29L7.1 4.35C7.33568 4.58054 7.63502 4.73519 7.95941 4.794C8.28381 4.85282 8.61838 4.81312 8.92 4.68H9C9.29577 4.55324 9.54802 4.34276 9.72569 4.07447C9.90337 3.80618 9.99872 3.49179 10 3.17V3C10 2.46957 10.2107 1.96086 10.5858 1.58579C10.9609 1.21071 11.4696 1 12 1C12.5304 1 13.0391 1.21071 13.4142 1.58579C13.7893 1.96086 14 2.46957 14 3V3.09C14.0013 3.41179 14.0966 3.72618 14.2743 3.99447C14.452 4.26276 14.7042 4.47324 15 4.6C15.3016 4.73312 15.6362 4.77282 15.9606 4.714C16.285 4.65519 16.5843 4.50054 16.82 4.27L16.88 4.21C17.0657 4.02405 17.2863 3.87653 17.5291 3.77588C17.7719 3.67523 18.0322 3.62343 18.295 3.62343C18.5578 3.62343 18.8181 3.67523 19.0609 3.77588C19.3037 3.87653 19.5243 4.02405 19.71 4.21C19.896 4.39575 20.0435 4.61632 20.1441 4.85912C20.2448 5.10192 20.2966 5.36217 20.2966 5.625C20.2966 5.88783 20.2448 6.14808 20.1441 6.39088C20.0435 6.63368 19.896 6.85425 19.71 7.04L19.65 7.1C19.4195 7.33568 19.2648 7.63502 19.206 7.95941C19.1472 8.28381 19.1869 8.61838 19.32 8.92V9C19.4468 9.29577 19.6572 9.54802 19.9255 9.72569C20.1938 9.90337 20.5082 9.99872 20.83 10H21C21.5304 10 22.0391 10.2107 22.4142 10.5858C22.7893 10.9609 23 11.4696 23 12C23 12.5304 22.7893 13.0391 22.4142 13.4142C22.0391 13.7893 21.5304 14 21 14H20.91C20.5882 14.0013 20.2738 14.0966 20.0055 14.2743C19.7372 14.452 19.5268 14.7042 19.4 15Z\" stroke=\"white\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n              </svg>\n            </view>\n            <text class=\"action-text\">{{action.name}}</text>\n          </view>\n        </view>\n      </view>\n      \n      <!-- 数据概览 - 更精致的设计 -->\n      <view class=\"business-overview\">\n        <view class=\"overview-header\">\n          <view class=\"overview-title\">\n            <svg class=\"overview-icon\" width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n              <path d=\"M16 6L18.29 8.29L13.41 13.17L9.41 9.17L2 16.59L3.41 18L9.41 12L13.41 16L19.71 9.71L22 12V6H16Z\" stroke=\"#0A84FF\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n            </svg>\n            <text>业务概览</text>\n          </view>\n          <view class=\"tab-group\">\n            <view class=\"tab\" :class=\"{ active: currentStatsType === 'today' }\" @click=\"switchStatsType('today')\">今日</view>\n            <view class=\"tab\" :class=\"{ active: currentStatsType === 'week' }\" @click=\"switchStatsType('week')\">本周</view>\n            <view class=\"tab\" :class=\"{ active: currentStatsType === 'month' }\" @click=\"switchStatsType('month')\">本月</view>\n          </view>\n        </view>\n        \n        <view class=\"overview-cards\">\n          <view class=\"overview-card\" v-for=\"(stat, index) in currentStats\" :key=\"index\" @tap=\"viewStatDetail(stat)\">\n            <text class=\"card-label\">{{stat.title}}</text>\n            <text class=\"card-value\">{{stat.value}}</text>\n            <view class=\"card-trend\" :class=\"stat.trend\">\n              <svg v-if=\"stat.trend === 'up'\" width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n                <path d=\"M12 20L12 4M12 4L18 10M12 4L6 10\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n              </svg>\n              <svg v-else width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n                <path d=\"M12 4L12 20M12 20L6 14M12 20L18 14\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n              </svg>\n              <text>{{stat.trendValue}}</text>\n            </view>\n          </view>\n        </view>\n      </view>\n      \n      <!-- 智能助理 -->\n      <view class=\"ai-assistant\">\n        <view class=\"section-header\">\n          <view class=\"overview-title\">\n            <svg class=\"overview-icon\" width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n              <path d=\"M12 3C7.02944 3 3 7.02944 3 12C3 16.9706 7.02944 21 12 21C16.9706 21 21 16.9706 21 12C21 7.02944 16.9706 3 12 3Z\" stroke=\"#0A84FF\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n              <path d=\"M9 12C9 12.5523 9.44772 13 10 13C10.5523 13 11 12.5523 11 12C11 11.4477 10.5523 11 10 11C9.44772 11 9 11.4477 9 12Z\" fill=\"#0A84FF\"/>\n              <path d=\"M13 12C13 12.5523 13.4477 13 14 13C14.5523 13 15 12.5523 15 12C15 11.4477 14.5523 11 14 11C13.4477 11 13 11.4477 13 12Z\" fill=\"#0A84FF\"/>\n              <path d=\"M12 18C14.5 18 16.5 16.5 17 15H7C7.5 16.5 9.5 18 12 18Z\" stroke=\"#0A84FF\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n            </svg>\n            <text>智能经营助手</text>\n          </view>\n          <view class=\"section-action\" @tap=\"viewAssistantDetail\">\n            <text class=\"action-text\">更多</text>\n            <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n              <path d=\"M9 18L15 12L9 6\" stroke=\"#0A84FF\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n            </svg>\n          </view>\n        </view>\n        \n        <view class=\"assistant-insights\">\n          <view class=\"insight-card\" @tap=\"handleInsight('trend')\">\n            <view class=\"insight-icon-container\">\n              <svg class=\"insight-icon\" width=\"28\" height=\"28\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n                <path d=\"M16 6L18.29 8.29L13.41 13.17L9.41 9.17L2 16.59L3.41 18L9.41 12L13.41 16L19.71 9.71L22 12V6H16Z\" stroke=\"#0A84FF\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n              </svg>\n            </view>\n            <view class=\"insight-content\">\n              <text class=\"insight-title\">消费趋势分析</text>\n              <text class=\"insight-desc\">近期顾客偏好变化，建议调整商品结构</text>\n            </view>\n            <view class=\"insight-action\">\n              <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n                <path d=\"M9 18L15 12L9 6\" stroke=\"#8E8E93\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n              </svg>\n            </view>\n          </view>\n          \n          <view class=\"insight-card warning\" @tap=\"handleInsight('pricing')\">\n            <view class=\"insight-icon-container warning\">\n              <svg class=\"insight-icon\" width=\"28\" height=\"28\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n                <path d=\"M12 8V12M12 16H12.01M22 12C22 17.5228 17.5228 22 12 22C6.47715 22 2 17.5228 2 12C2 6.47715 6.47715 2 12 2C17.5228 2 22 6.47715 22 12Z\" stroke=\"#FF9F0A\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n              </svg>\n            </view>\n            <view class=\"insight-content\">\n              <text class=\"insight-title\">竞品价格监测</text>\n              <text class=\"insight-desc\">同类商品市场价格下降5%，建议调整策略</text>\n            </view>\n            <view class=\"insight-action\">\n              <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n                <path d=\"M9 18L15 12L9 6\" stroke=\"#8E8E93\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n              </svg>\n            </view>\n          </view>\n          \n          <view class=\"insight-card opportunity\" @tap=\"handleInsight('forecast')\">\n            <view class=\"insight-icon-container opportunity\">\n              <svg class=\"insight-icon\" width=\"28\" height=\"28\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n                <path d=\"M12 8V16M12 16L8 12M12 16L16 12M3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12Z\" stroke=\"#30D158\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n              </svg>\n            </view>\n            <view class=\"insight-content\">\n              <text class=\"insight-title\">销售预测模型</text>\n              <text class=\"insight-desc\">根据历史数据，下周销售额预计增长12%</text>\n            </view>\n            <view class=\"insight-action\">\n              <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n                <path d=\"M9 18L15 12L9 6\" stroke=\"#8E8E93\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n              </svg>\n            </view>\n          </view>\n        </view>\n      </view>\n      \n        </view>\n        \n    <!-- 底部导航栏 -->\n    <view class=\"tab-bar\">\n      <view v-for=\"(tab, index) in tabItems\" :key=\"index\" class=\"tab-item\" :class=\"{ active: currentTab === tab.id }\" @tap=\"switchTab(tab.id)\">\n        <view v-if=\"currentTab === tab.id\" class=\"active-indicator\"></view>\n        <view :class=\"['tab-icon', tab.icon]\"></view>\n        <text class=\"tab-text\">{{tab.text}}</text>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script setup>\nimport { ref, reactive, computed, onMounted } from 'vue';\n\n// 用户信息\nconst userInfo = reactive({\n        name: '张店长',\n        avatar: ''\n});\n\n// 商家信息\nconst merchantInfo = reactive({\n        location: '磁县',\n        level: '钻石商家',\n        rating: 4.9,\n        followers: 1280\n});\n\n// 页面状态\nconst currentDate = ref('2023年6月18日');\nconst hasNotification = ref(true);\nconst currentStatsType = ref('today'); // 新的状态类型变量\nconst todayIncome = ref('12,846');\nconst todayOrders = ref('128');\nconst pendingOrders = ref('3');\nconst currentTab = ref(0);\n      \n      // 智能助手项目\nconst assistantItems = reactive([\n        '基于AI分析的经营建议',\n        '潜在问题提醒',\n        '机会点识别与推荐'\n]);\n      \n      // 快捷操作\nconst quickActions = reactive([\n        {\n          name: '活动管理',\n          icon: 'icon-campaign',\n          url: '/subPackages/merchant-admin/pages/activity/index'\n        },\n        {\n          name: '发布商品',\n          icon: 'icon-product',\n          url: '/subPackages/merchant-admin/pages/products/create'\n        },\n        {\n          name: '核销中心',\n          icon: 'icon-verification',\n          url: '/subPackages/merchant-admin-marketing/pages/marketing/verification/index'\n        },\n        {\n          name: '数据分析',\n          icon: 'icon-analysis',\n          url: '/subPackages/merchant-admin/pages/analysis/index'\n        },\n        {\n          name: '客户管理',\n          icon: 'icon-customer',\n          url: '/subPackages/merchant-admin-customer/pages/customer/index'\n        },\n        {\n          name: '设置',\n          icon: 'icon-setting',\n          url: '/subPackages/merchant-admin/pages/settings/index'\n        }\n]);\n      \n      // 统计数据\nconst statsData = reactive({\n        today: [\n        {\n          title: '今日销售额',\n          value: '¥12,846',\n          trendValue: '+15.2%',\n            trend: 'up',\n            type: 'primary',\n            icon: 'sales'\n        },\n        {\n          title: '访客数量',\n          value: '1,286',\n          trendValue: '+8.5%',\n            trend: 'up',\n            type: 'secondary',\n            icon: 'visitor'\n        },\n        {\n          title: '订单数量',\n          value: '128',\n          trendValue: '+12.3%',\n            trend: 'up',\n            type: 'success',\n            icon: 'order'\n        },\n        {\n          title: '退款金额',\n          value: '¥1,234',\n          trendValue: '-5.2%',\n            trend: 'down',\n            type: 'warning',\n            icon: 'refund'\n          }\n        ],\n        week: [\n          {\n            title: '本周销售额',\n            value: '¥86,235',\n            trendValue: '+10.8%',\n            trend: 'up',\n            type: 'primary',\n            icon: 'sales'\n          },\n          {\n            title: '访客数量',\n            value: '8,562',\n            trendValue: '+6.2%',\n            trend: 'up',\n            type: 'secondary',\n            icon: 'visitor'\n          },\n          {\n            title: '订单数量',\n            value: '862',\n            trendValue: '+9.1%',\n            trend: 'up',\n            type: 'success',\n            icon: 'order'\n          },\n          {\n            title: '退款金额',\n            value: '¥7,823',\n            trendValue: '-2.8%',\n            trend: 'down',\n            type: 'warning',\n            icon: 'refund'\n          }\n        ],\n        month: [\n          {\n            title: '本月销售额',\n            value: '¥356,428',\n            trendValue: '+18.5%',\n            trend: 'up',\n            type: 'primary',\n            icon: 'sales'\n          },\n          {\n            title: '访客数量',\n            value: '35,621',\n            trendValue: '+12.4%',\n            trend: 'up',\n            type: 'secondary',\n            icon: 'visitor'\n          },\n          {\n            title: '订单数量',\n            value: '3,562',\n            trendValue: '+15.7%',\n            trend: 'up',\n            type: 'success',\n            icon: 'order'\n          },\n          {\n            title: '退款金额',\n            value: '¥28,456',\n            trendValue: '-8.3%',\n            trend: 'down',\n            type: 'warning',\n            icon: 'refund'\n          }\n        ]\n});\n      \n// 待办事项\nconst todos = reactive([\n        {\n          title: '待处理订单提醒',\n          time: '今天 14:00',\n          completed: false,\n          priority: 'high',\n          type: 'order'\n        },\n        {\n          title: '客户消息待回复',\n          time: '今天 16:00',\n          completed: true,\n          priority: 'medium',\n          type: 'message'\n        },\n        {\n          title: '\"夏季特惠\"活动即将到期',\n          time: '明天 10:00',\n          completed: false,\n          priority: 'high',\n          type: 'activity'\n        },\n        {\n          title: '商品置顶服务即将到期',\n          time: '后天 18:00',\n          completed: false,\n          priority: 'medium',\n          type: 'promotion'\n        },\n        {\n          title: '更新商品库存',\n          time: '周五 12:00',\n          completed: false,\n          priority: 'medium',\n          type: 'inventory'\n        },\n        {\n          title: '推广位即将到期',\n          time: '周六 10:00',\n          completed: false,\n          priority: 'high',\n          type: 'promotion'\n        }\n]);\n\n// 活动管理\nconst activities = reactive([\n        {\n          title: '618购物节特惠',\n          startDate: '2023-06-01',\n          endDate: '2023-06-18',\n          status: 'active',\n          views: '2.5k',\n          sales: '¥15,628',\n          progress: 85\n        },\n        {\n          title: '夏季新品上市',\n          startDate: '2023-05-15',\n          endDate: '2023-06-15',\n          status: 'ending',\n          views: '1.8k',\n          sales: '¥12,386',\n          progress: 95\n        },\n        {\n          title: '会员专享折扣',\n          startDate: '2023-06-10',\n          endDate: '2023-06-30',\n          status: 'upcoming',\n          views: '856',\n          sales: '¥6,245',\n          progress: 0\n        }\n]);\n\n// 导航项\nconst tabItems = reactive([\n        {\n          id: 0,\n          icon: 'dashboard',\n          text: '商家中心',\n    url: '/subPackages/merchant-admin-home/pages/merchant-home/index'\n        },\n        {\n          id: 1,\n          icon: 'store',\n          text: '店铺管理',\n          url: '/subPackages/merchant-admin/pages/store/index'\n        },\n        {\n          id: 2,\n          icon: 'marketing',\n          text: '营销中心',\n    url: '/subPackages/merchant-admin-marketing/pages/marketing/index'\n        },\n        {\n          id: 3,\n          icon: 'orders',\n          text: '订单管理',\n          url: '/subPackages/merchant-admin-order/pages/order/index'\n        },\n        {\n          id: 'more',\n          icon: 'more',\n          text: '更多',\n          url: ''\n  }\n]);\n\n// 计算属性\nconst currentStats = computed(() => {\n  return statsData[currentStatsType.value] || statsData.today;\n});\n\nconst pendingTodosCount = computed(() => {\n  return todos.filter(todo => !todo.completed).length;\n});\n    \n    // 底部导航栏显示的导航项（前4个核心功能 + 更多）\nconst visibleTabs = computed(() => {\n      // 前4个核心导航项\n  const mainTabs = tabItems.slice(0, 4);\n      \n      // 添加\"更多\"导航项\n      mainTabs.push({\n        id: 'more',\n        icon: 'more',\n        text: '更多',\n        url: ''\n      });\n      \n      // 不再自动替换路径，使用原始配置的路径\n      // 保留原始路径，确保正确跳转\n      \n      return mainTabs;\n});\n\n// 方法\nfunction getCurrentDate() {\n      const date = new Date();\n      const year = date.getFullYear();\n      const month = date.getMonth() + 1;\n      const day = date.getDate();\n  currentDate.value = `${year}年${month}月${day}日`;\n}\n\n// 移除onRefresh方法\n\nfunction switchStatsType(type) {\n  console.log('正在切换到:', type);\n  // 更新当前选中的统计类型\n  currentStatsType.value = type;\n  \n  // 根据不同类型显示不同的数据\n  let typeText = type === 'today' ? '今日' : type === 'week' ? '本周' : '本月';\n  \n  // 显示切换成功的提示\n  uni.showToast({\n    title: '切换到' + typeText,\n    icon: 'none',\n    duration: 1500\n  });\n}\n\nfunction getPriorityText(priority) {\n      const map = {\n        high: '紧急',\n        medium: '普通',\n        low: '低优'\n      };\n      return map[priority] || '普通';\n}\n\nfunction getStatusText(status) {\n      const map = {\n        active: '进行中',\n        ending: '即将结束',\n        upcoming: '未开始',\n        completed: '已结束'\n      };\n      return map[status] || '未知';\n}\n\nfunction getActivityProgress(activity) {\n      return activity.progress || 0;\n}\n\nfunction handleQuickAction(action) {\n  console.log('正在跳转到:', action.url);\n  \n  try {\n    // 检查URL是否有效\n    if (!action.url) {\n      throw new Error('URL不存在');\n    }\n    \n    // 特殊处理商家中心页面\n    if (action.name === '商家中心') {\n      uni.navigateTo({\n        url: '/subPackages/merchant-admin-home/pages/merchant-home/index',\n        fail: (err) => {\n          console.error('导航失败:', err);\n          uni.showToast({\n            title: '页面跳转失败，请稍后再试',\n            icon: 'none',\n            duration: 2000\n          });\n        }\n      });\n      return;\n    }\n    \n    // 核销中心特殊处理\n    if (action.name === '核销中心') {\n      console.log('跳转到核销中心:', action.url);\n      uni.navigateTo({\n        url: action.url,\n        success: () => {\n          console.log('成功跳转到核销中心');\n        },\n        fail: (err) => {\n          console.error('核销中心跳转失败:', err);\n          // 尝试使用switchTab\n          uni.switchTab({\n            url: action.url,\n            fail: (switchErr) => {\n              console.error('核销中心switchTab也失败:', switchErr);\n              uni.showToast({\n                title: '核销中心页面跳转失败，请稍后再试',\n                icon: 'none',\n                duration: 2000\n              });\n            }\n          });\n        }\n      });\n      return;\n    }\n    \n    // 使用uni.navigateTo导航到对应页面\n      uni.navigateTo({\n        url: action.url,\n        fail: (err) => {\n          console.error('导航失败:', err);\n        \n        // 尝试使用switchTab切换到Tab页面\n        uni.switchTab({\n          url: action.url,\n          fail: (switchErr) => {\n            console.error('switchTab也失败:', switchErr);\n            \n            // 显示错误提示\n          uni.showToast({\n              title: '页面跳转失败，请稍后再试',\n            icon: 'none',\n            duration: 2000\n          });\n        }\n      });\n      }\n    });\n  } catch (error) {\n    console.error('处理快捷操作时出错:', error);\n    \n    // 显示错误提示\n    uni.showToast({\n      title: '功能正在开发中',\n      icon: 'none',\n      duration: 2000\n    });\n  }\n}\n\nfunction toggleTodoStatus(index) {\n  todos[index].completed = !todos[index].completed;\n      \n      // 如果标记为完成，显示提示\n  if (todos[index].completed) {\n        uni.showToast({\n          title: '已标记为完成',\n          icon: 'success'\n        });\n      }\n}\n\nfunction viewAllTodos() {\n      uni.navigateTo({\n        url: '/subPackages/merchant-admin-home/pages/todos/index'\n      });\n}\n\nfunction handleTodoAction(todo) {\n      // 根据待办事项类型执行不同操作\n      const actions = {\n        order: {\n          title: '订单管理',\n          url: '/subPackages/merchant-admin-home/pages/orders/index'\n        },\n        message: {\n          title: '消息中心',\n          url: '/subPackages/merchant-admin-home/pages/messages/index'\n        },\n        activity: {\n          title: '活动管理',\n          url: '/subPackages/merchant-admin-home/pages/activity/list'\n        },\n        promotion: {\n          title: '推广管理',\n          url: '/subPackages/merchant-admin-home/pages/promotions/index'\n        },\n        inventory: {\n          title: '库存管理',\n          url: '/subPackages/merchant-admin-home/pages/inventory/index'\n        }\n      };\n      \n      const action = actions[todo.type];\n      if (action) {\n        uni.navigateTo({\n          url: action.url,\n          success: () => {\n            uni.showToast({\n              title: `正在进入${action.title}`,\n              icon: 'none'\n            });\n          }\n        });\n      }\n}\n\n\n\nfunction switchTab(tabId) {\n      // 处理\"更多\"选项\n      if (tabId === 'more') {\n    showMoreOptions();\n        return;\n      }\n      \n  if (tabId === currentTab.value) return;\n      \n  currentTab.value = tabId;\n      \n  // 特殊处理数据概览标签\n  if (tabId === 0) {\n      uni.navigateTo({\n      url: '/subPackages/merchant-admin-home/pages/merchant-home/index',\n      fail: (err) => {\n        console.error('导航到商家中心失败:', err);\n        uni.showToast({\n          title: '页面跳转失败，请稍后再试',\n          icon: 'none'\n        });\n      }\n    });\n    return;\n  }\n      \n  // 尝试使用switchTab切换到tabBar页面\n  uni.switchTab({\n    url: tabItems[tabId].url,\n    fail: (err) => {\n      console.error('switchTab失败:', err);\n      // 如果switchTab失败（可能是因为页面不在tabBar中），则使用navigateTo\n      uni.navigateTo({\n        url: tabItems[tabId].url,\n        fail: (navErr) => {\n          console.error('导航失败:', navErr);\n          uni.showToast({\n            title: '页面跳转失败，请稍后再试',\n            icon: 'none'\n          });\n        }\n      });\n    }\n      });\n}\n    \n    // 显示更多选项弹出菜单\nfunction showMoreOptions() {\n      // 准备更多菜单中的选项（后3个导航项）\n  const moreOptions = tabItems.slice(4).map(item => item.text);\n      \n      uni.showActionSheet({\n        itemList: moreOptions,\n        success: (res) => {\n          // 计算实际的导航项索引 (4 + 用户点击的索引)\n          const tabIndex = 4 + res.tapIndex;\n      currentTab.value = tabItems[tabIndex].id;\n          \n          // 尝试使用switchTab切换到tabBar页面\n          uni.switchTab({\n            url: tabItems[tabIndex].url,\n            fail: (err) => {\n              console.error('switchTab失败:', err);\n              // 如果switchTab失败（可能是因为页面不在tabBar中），则使用navigateTo\n          uni.navigateTo({\n                url: tabItems[tabIndex].url,\n                fail: (navErr) => {\n                  console.error('导航失败:', navErr);\n                  uni.showToast({\n                    title: '页面跳转失败，请稍后再试',\n                    icon: 'none'\n                  });\n                }\n              });\n            }\n          });\n        }\n      });\n}\n\nfunction showUserInfo() {\n      uni.navigateTo({\n        url: '/subPackages/merchant-admin-home/pages/user/profile'\n      });\n}\n\nfunction viewAllStats() {\n      uni.navigateTo({\n        url: '/subPackages/merchant-admin-home/pages/statistics/index'\n      });\n}\n\nfunction viewStatDetail(stat) {\n      uni.navigateTo({\n        url: `/subPackages/merchant-admin-home/pages/statistics/detail?type=${stat.type}`\n      });\n}\n\nfunction viewActivityDetail(activity) {\n      uni.navigateTo({\n        url: `/subPackages/merchant-admin-home/pages/activity/detail?title=${encodeURIComponent(activity.title)}`\n      });\n}\n\n\n\nfunction goBack() {\n      uni.navigateBack();\n}\n\nfunction viewAssistantDetail() {\n      uni.navigateTo({\n        url: '/subPackages/merchant-admin-home/pages/assistant/detail'\n      });\n}\n\nfunction handleInsight(type) {\n      const actions = {\n        trend: {\n          title: '消费趋势分析',\n          url: '/subPackages/merchant-admin-home/pages/analytics/consumer-trends'\n        },\n        pricing: {\n          title: '竞品价格监测',\n          url: '/subPackages/merchant-admin-home/pages/analytics/competitor-pricing'\n        },\n        forecast: {\n          title: '销售预测模型',\n          url: '/subPackages/merchant-admin-home/pages/analytics/sales-forecast'\n        }\n      };\n      \n      const action = actions[type];\n      if (action) {\n        uni.navigateTo({\n          url: action.url,\n          success: () => {\n            uni.showToast({\n              title: `正在查看${action.title}`,\n              icon: 'none'\n            });\n          }\n        });\n      }\n}\n\nfunction viewFullReport() {\n      uni.navigateTo({\n        url: '/subPackages/merchant-admin-home/pages/statistics/index'\n      });\n}\n\nfunction viewAnalyticDetail(type) {\n      uni.navigateTo({\n        url: `/subPackages/merchant-admin-home/pages/analytics/detail?type=${type}`\n      });\n}\n\n\n\nfunction navigateToSettings(setting) {\n      uni.navigateTo({\n        url: `/subPackages/merchant-admin-home/pages/settings/detail?setting=${setting}`\n      });\n}\n\nfunction viewAllSettings() {\n      uni.navigateTo({\n        url: '/subPackages/merchant-admin-home/pages/settings/index'\n      });\n    }\n\n// 添加状态栏高度变量\nconst statusBarHeight = ref(20);\nconst navbarHeight = ref(64);\n\n// 生命周期\nonMounted(() => {\n  getCurrentDate();\n  \n  // 获取系统信息并设置状态栏高度\n  const sysInfo = uni.getSystemInfoSync();\n  statusBarHeight.value = sysInfo.statusBarHeight;\n  navbarHeight.value = statusBarHeight.value + 44; // 状态栏 + 导航栏固定高度\n});\n</script>\n\n<style lang=\"scss\" scoped>\n:root {\n  /* 颜色系统 - 苹果风格 */\n  --brand-primary: #0A84FF; /* 苹果蓝 */\n  --brand-secondary: #5E5CE6; /* 苹果紫 */\n  --accent-purple: #5E5CE6;\n  --accent-pink: #FF2D55;\n  --accent-orange: #FF9500;\n  --accent-yellow: #FFCC00;\n  --accent-green: #30D158;\n  --accent-teal: #64D2FF;\n  \n  /* 中性色 */\n  --bg-primary: #F2F3F7;\n  --bg-secondary: #FFFFFF;\n  --bg-tertiary: #FAFAFA;\n  --bg-card: #FFFFFF;\n  \n  /* 文本颜色 */\n  --text-primary: #1C1C1E;\n  --text-secondary: #3A3A3C;\n  --text-tertiary: #8E8E93;\n  --text-quaternary: #C7C7CC;\n  \n  /* 边框颜色 */\n  --border-light: rgba(60, 60, 67, 0.06);\n  --border-medium: rgba(60, 60, 67, 0.12);\n  \n  /* 功能色 */\n  --success: #30D158;\n  --warning: #FF9F0A;\n  --danger: #FF453A;\n  --info: #64D2FF;\n  \n  /* 间距 */\n  --space-xs: 4px;\n  --space-sm: 8px;\n  --space-md: 16px;\n  --space-lg: 24px;\n  --space-xl: 32px;\n  \n  /* 圆角 - 更新为35度圆角 */\n  --radius-sm: 12px;\n  --radius-md: 20px;\n  --radius-lg: 35px;\n  \n  /* 阴影 - 更精致的阴影系统 */\n  --shadow-xs: 0 2px 8px rgba(0, 0, 0, 0.04), 0 1px 3px rgba(0, 0, 0, 0.02);\n  --shadow-sm: 0 4px 12px rgba(0, 0, 0, 0.06), 0 2px 6px rgba(0, 0, 0, 0.03);\n  --shadow-md: 0 8px 24px rgba(0, 0, 0, 0.08), 0 4px 12px rgba(0, 0, 0, 0.04);\n  --shadow-lg: 0 16px 32px rgba(0, 0, 0, 0.1), 0 8px 16px rgba(0, 0, 0, 0.05);\n  --shadow-xl: 0 24px 48px rgba(0, 0, 0, 0.12), 0 12px 24px rgba(0, 0, 0, 0.06);\n}\n\n/* 页面容器 */\n.merchant-dashboard {\n  background-color: var(--bg-primary);\n  min-height: 100vh;\n  padding-bottom: 100px;\n  position: relative;\n  overflow-x: hidden;\n}\n\n/* 状态栏背景 */\n.status-bar-bg {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  height: var(--status-bar-height, 44px);\n  background: #0052CC;\n  z-index: 100;\n}\n\n/* 顶部导航栏 - 恢复原始样式 */\n.navbar {\n  position: fixed;\n  top: var(--status-bar-height, 44px);\n  left: 0;\n  right: 0;\n  height: 62px; /* 再增加高度3px，从59px到62px */\n  background: #0052CC;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  z-index: 100;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n}\n\n.navbar-left {\n  position: absolute;\n  left: 16px;\n  top: 0;\n  bottom: 0;\n  display: flex;\n  align-items: center;\n}\n\n.back-icon {\n  width: 24px;\n  height: 24px;\n  background-image: url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='white'%3E%3Cpath d='M15.41 7.41L14 6l-6 6 6 6 1.41-1.41L10.83 12z'/%3E%3C/svg%3E\");\n  background-repeat: no-repeat;\n  background-position: center;\n  background-size: contain;\n}\n\n.navbar-title {\n  color: white;\n  font-size: 20px; /* 增加字体大小 */\n  font-weight: 700; /* 增加字体粗细 */\n  letter-spacing: 0.5px;\n}\n\n.navbar-right {\n  position: absolute;\n  right: 16px;\n  top: 0;\n  bottom: 0;\n  display: flex;\n  align-items: center;\n}\n\n/* 主内容区域 */\n.content-container {\n  padding: calc(var(--status-bar-height, 44px) + 62px + 16px) 16px 0; /* 调整padding，将59px改为62px */\n  display: flex;\n  flex-direction: column;\n  gap: 16px; /* 减小卡片之间的间距 */\n}\n\n/* 业绩概览卡片 - 更精致的设计 */\n.performance-card {\n  background: linear-gradient(145deg, #ffffff, #f8f9fc);\n  border-radius: var(--radius-lg);\n  padding: 18px; /* 减小内边距 */\n  position: relative;\n  box-shadow: var(--shadow-md);\n  overflow: hidden;\n  border: 1px solid rgba(255, 255, 255, 0.8);\n  width: 90%; /* 调整宽度 */\n  margin-left: auto;\n  margin-right: auto;\n  transform: translateZ(0);\n  backdrop-filter: blur(20px);\n  -webkit-backdrop-filter: blur(20px);\n}\n\n.performance-card::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  right: 0;\n  width: 180px;\n  height: 180px;\n  background: radial-gradient(circle, rgba(46, 91, 255, 0.06) 0%, transparent 70%);\n  border-radius: 50%;\n  transform: translate(30%, -30%);\n}\n\n.performance-card::after {\n  content: '';\n  position: absolute;\n  bottom: 0;\n  left: 0;\n  width: 150px;\n  height: 150px;\n  background: radial-gradient(circle, rgba(46, 91, 255, 0.04) 0%, transparent 70%);\n  border-radius: 50%;\n  transform: translate(-30%, 30%);\n}\n\n.performance-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 12px; /* 减小下边距 */\n  position: relative;\n  z-index: 2;\n}\n\n.performance-title {\n  flex: 1;\n}\n\n.greeting-container {\n  display: flex;\n  align-items: center;\n  margin-top: 0;\n}\n\n.greeting {\n  font-size: 18px; /* 减小字体大小 */\n  font-weight: 700;\n  margin-bottom: 2px;\n  display: block;\n  letter-spacing: 0.3px;\n  color: var(--text-primary);\n  background: linear-gradient(135deg, #1C1C1E, #3A3A3C);\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  background-clip: text;\n  text-fill-color: transparent;\n}\n\n.greeting-icon {\n  width: 18px;\n  height: 18px;\n  opacity: 0.8;\n  margin-left: 8px;\n}\n\n.date {\n  font-size: 13px; /* 减小字体大小 */\n  color: var(--text-tertiary);\n  display: block;\n  margin-top: 2px; /* 减小上边距 */\n  font-weight: 500;\n}\n\n.merchant-badge {\n  padding: 6px 12px; /* 减小内边距 */\n  background: linear-gradient(135deg, #0A84FF, #5E5CE6);\n  border-radius: 16px; /* 减小圆角 */\n  font-size: 12px;\n  color: white;\n  font-weight: 600;\n  display: flex;\n  align-items: center;\n  gap: 6px;\n  box-shadow: 0 4px 12px rgba(10, 132, 255, 0.25);\n  border: 1px solid rgba(255, 255, 255, 0.3);\n}\n\n.merchant-badge-icon {\n  width: 14px; /* 减小图标尺寸 */\n  height: 14px; /* 减小图标尺寸 */\n}\n\n.performance-metrics {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  background: linear-gradient(145deg, rgba(255, 255, 255, 0.9), rgba(248, 249, 252, 0.9));\n  border-radius: 24px;\n  padding: 16px; /* 减小内边距 */\n  position: relative;\n  z-index: 2;\n  box-shadow: var(--shadow-sm);\n  border: 1px solid rgba(255, 255, 255, 0.8);\n  backdrop-filter: blur(10px);\n  -webkit-backdrop-filter: blur(10px);\n  margin-top: 12px; /* 减小上边距 */\n}\n\n.metric-item {\n  text-align: center;\n  flex: 1;\n}\n\n.metric-divider {\n  width: 1px;\n  height: 40px;\n  background: linear-gradient(to bottom, transparent, rgba(0, 0, 0, 0.06), transparent);\n}\n\n.metric-value {\n  font-size: 20px; /* 减小字体大小 */\n  font-weight: 700;\n  margin-bottom: 4px;\n  display: block;\n  background: linear-gradient(135deg, #0A84FF, #5E5CE6);\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  background-clip: text;\n  text-fill-color: transparent;\n}\n\n.metric-label {\n  font-size: 12px;\n  color: var(--text-tertiary);\n  font-weight: 500;\n}\n\n/* 快捷功能卡片 - 更现代的设计 */\n.quick-actions-card {\n  background: linear-gradient(145deg, #ffffff, #f8f9fc);\n  border-radius: var(--radius-lg);\n  padding: 18px 16px; /* 增加左右内边距，从12px改为16px */\n  box-shadow: var(--shadow-md);\n  position: relative;\n  overflow: hidden;\n  border: 1px solid rgba(255, 255, 255, 0.8);\n  width: 90%; /* 调整宽度 */\n  margin-left: auto;\n  margin-right: auto;\n  transform: translateZ(0);\n  backdrop-filter: blur(20px);\n  -webkit-backdrop-filter: blur(20px);\n}\n\n.quick-actions-card::before {\n  content: '';\n  position: absolute;\n  top: -10%;\n  right: -10%;\n  width: 200px;\n  height: 200px;\n  background: radial-gradient(circle, rgba(10, 132, 255, 0.03) 0%, transparent 70%);\n  border-radius: 50%;\n}\n\n.section-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 16px; /* 减小下边距 */\n  padding-bottom: 12px; /* 减小内边距 */\n  border-bottom: 1px solid rgba(60, 60, 67, 0.08);\n}\n\n.overview-title {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  font-size: 16px;\n  font-weight: 600;\n  color: var(--text-primary);\n}\n\n.overview-icon {\n  width: 20px;\n  height: 20px;\n}\n\n.section-action {\n  display: flex;\n  align-items: center;\n  gap: 4px;\n  color: var(--brand-primary);\n  font-size: 14px;\n  font-weight: 500;\n}\n\n.action-text {\n  color: var(--brand-primary);\n}\n\n.quick-actions {\n  display: grid;\n  grid-template-columns: repeat(3, 1fr);\n  gap: 12px; /* 增加间距，从8px改为12px */\n}\n\n.action-item {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: 12px 4px; /* 增加水平内边距，添加4px的左右内边距 */\n  transition: all 0.3s ease;\n  border-radius: 20px;\n  background: linear-gradient(145deg, #ffffff, #f8f9fc);\n  box-shadow: var(--shadow-xs);\n  border: 1px solid rgba(255, 255, 255, 0.8);\n  position: relative; /* 添加相对定位 */\n  z-index: 1; /* 确保按钮在正确的层级 */\n  min-height: 80px; /* 确保最小高度 */\n  overflow: visible; /* 确保内容不会被裁剪 */\n}\n\n.action-item:active {\n  transform: scale(0.96);\n  box-shadow: var(--shadow-sm);\n  background-color: rgba(0, 0, 0, 0.02); /* 添加点击效果 */\n}\n\n.action-icon-container {\n  width: 44px; /* 减小图标容器尺寸 */\n  height: 44px; /* 减小图标容器尺寸 */\n  border-radius: 16px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-bottom: 8px; /* 减小下边距 */\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);\n  position: relative; /* 添加相对定位 */\n  z-index: 2; /* 确保图标在按钮上层 */\n}\n\n.action-svg {\n  width: 20px; /* 减小图标尺寸 */\n  height: 20px; /* 减小图标尺寸 */\n  position: relative; /* 添加相对定位 */\n  z-index: 3; /* 确保SVG在最上层 */\n}\n\n.action-text {\n  font-size: 12px; /* 减小字体大小 */\n  position: relative; /* 添加相对定位 */\n  z-index: 2; /* 确保文本在按钮上层 */\n  width: 100%; /* 确保文本宽度占满容器 */\n  text-align: center; /* 文本居中 */\n  padding: 0 2px; /* 添加水平内边距 */\n}\n\n/* 业务概览 - 更精致的设计 */\n.business-overview {\n  background-color: var(--bg-card);\n  border-radius: var(--radius-lg);\n  padding: var(--space-md) var(--space-md); /* 减小左右内边距，确保卡片能完整显示 */\n  margin-bottom: var(--space-lg);\n  box-shadow: 0 6px 18px rgba(0, 0, 0, 0.09), 0 2px 6px rgba(0, 0, 0, 0.05);\n  position: relative;\n  overflow: hidden; /* 修改回hidden，但确保内部元素不会超出容器 */\n  border: 1px solid rgba(200, 210, 230, 0.3);\n  width: 90%; /* 调整为90%，与其他卡片保持一致 */\n  margin-left: auto;\n  margin-right: auto;\n  transform: translateZ(0); /* 启用GPU加速，增强立体感 */\n  max-width: 600px; /* 调整最大宽度限制 */\n}\n\n.overview-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 10px 12px; /* 减小内边距 */\n  border-bottom: 1px solid #f0f2f5;\n  border-radius: var(--radius-lg) var(--radius-lg) 0 0; /* 添加顶部圆角 */\n  overflow: hidden; /* 确保内容不会溢出 */\n}\n\n/* 统一所有模块标题样式 */\n.overview-title {\n  display: flex;\n  align-items: center;\n  font-size: 15px; /* 稍微减小字体大小 */\n  font-weight: 600;\n  color: var(--text-primary);\n}\n\n.overview-icon {\n  width: 16px; /* 减小图标尺寸 */\n  height: 16px; /* 减小图标尺寸 */\n  margin-right: 6px; /* 减小右边距 */\n  flex-shrink: 0;\n}\n\n.tab-group {\n  display: flex;\n  background-color: #f5f7fa;\n  border-radius: 6px;\n  padding: 2px;\n  transform: scale(0.95); /* 稍微缩小标签组 */\n}\n\n.tab {\n  padding: 4px 10px;\n  font-size: 12px;\n  border-radius: 4px;\n  color: #666;\n  transition: all 0.2s ease;\n}\n\n.tab.active {\n  background-color: white;\n  color: var(--brand-primary);\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\n}\n\n.overview-cards {\n  display: grid;\n  grid-template-columns: repeat(2, 1fr);\n  gap: 1px;\n  background-color: #f0f2f5;\n  width: 98%; /* 稍微减小宽度，确保不会超出父容器 */\n  border-radius: 0 0 var(--radius-lg) var(--radius-lg); /* 添加底部圆角 */\n  overflow: hidden; /* 确保内容不会溢出 */\n  margin: 0 auto; /* 确保居中 */\n}\n\n.overview-card {\n  background-color: white;\n  padding: 12px 14px; /* 减小内边距 */\n  display: flex;\n  flex-direction: column;\n  position: relative;\n}\n\n.card-label {\n  font-size: 11px; /* 减小字体大小 */\n  color: #666;\n  margin-bottom: 3px; /* 减小下边距 */\n}\n\n.card-value {\n  font-size: 16px; /* 减小字体大小 */\n  font-weight: 600;\n  color: #333;\n  margin-bottom: 4px; /* 减小下边距 */\n}\n\n.card-trend {\n  display: flex;\n  align-items: center;\n  gap: 3px; /* 减小间距 */\n  font-size: 11px; /* 减小字体大小 */\n  font-weight: 500;\n}\n\n.card-trend.up {\n  color: var(--success);\n}\n\n.card-trend.down {\n  color: var(--danger);\n}\n\n.icon-customer {\n  background: linear-gradient(135deg, #FF9F0A, #FF3B30);\n}\n\n.icon-campaign {\n  background: linear-gradient(135deg, #FF2D55, #FF375F);\n}\n\n.icon-setting {\n  background: linear-gradient(135deg, #8E8E93, #636366);\n}\n\n.icon-product {\n  background: linear-gradient(135deg, #32D74B, #30B856);\n}\n\n.icon-verification {\n  background: linear-gradient(135deg, #64D2FF, #5AC8F5);\n}\n\n.icon-analysis {\n  background: linear-gradient(135deg, #BF5AF2, #A347D1);\n}\n\n.ai-assistant {\n  background: linear-gradient(145deg, #ffffff, #f8f9fc);\n  border-radius: var(--radius-lg);\n  padding: 18px; /* 减小内边距 */\n  box-shadow: var(--shadow-md);\n  position: relative;\n  overflow: hidden;\n  border: 1px solid rgba(255, 255, 255, 0.8);\n  width: 90%; /* 调整宽度 */\n  margin-left: auto;\n  margin-right: auto;\n  transform: translateZ(0);\n  backdrop-filter: blur(20px);\n  -webkit-backdrop-filter: blur(20px);\n}\n\n.assistant-insights {\n  display: flex;\n  flex-direction: column;\n  gap: 16px;\n}\n\n.insight-card {\n  background: linear-gradient(145deg, #ffffff, #f8f9fc);\n  border-radius: 24px;\n  padding: 16px; /* 减小内边距 */\n  display: flex;\n  align-items: center;\n  transition: all 0.3s ease;\n  box-shadow: var(--shadow-sm);\n  border: 1px solid rgba(255, 255, 255, 0.8);\n  border-left: 4px solid var(--brand-primary);\n}\n\n.insight-card.warning {\n  border-left-color: var(--warning);\n}\n\n.insight-card.opportunity {\n  border-left-color: var(--success);\n}\n\n.insight-card:active {\n  transform: scale(0.98);\n  box-shadow: var(--shadow-md);\n}\n\n.insight-icon-container {\n  width: 44px; /* 减小图标容器尺寸 */\n  height: 44px; /* 减小图标容器尺寸 */\n  border-radius: 16px;\n  margin-right: 14px; /* 减小右边距 */\n  background: rgba(10, 132, 255, 0.1);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  flex-shrink: 0;\n}\n\n.insight-card .insight-icon-container.warning {\n  background: rgba(255, 159, 10, 0.1);\n}\n\n.insight-card .insight-icon-container.opportunity {\n  background: rgba(48, 209, 88, 0.1);\n}\n\n.insight-icon {\n  width: 24px; /* 减小图标尺寸 */\n  height: 24px; /* 减小图标尺寸 */\n}\n\n.insight-content {\n  flex: 1;\n}\n\n.insight-title {\n  font-size: 14px; /* 减小字体大小 */\n  font-weight: 600;\n  color: var(--text-primary);\n  margin-bottom: 2px; /* 减小下边距 */\n  display: block;\n}\n\n.insight-desc {\n  font-size: 12px; /* 减小字体大小 */\n  color: var(--text-tertiary);\n  line-height: 1.4;\n}\n\n.insight-action {\n  width: 24px;\n  height: 24px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-left: 12px;\n  flex-shrink: 0;\n}\n\n/* 底部导航栏 - 恢复原始样式 */\n.tab-bar {\n  position: fixed;\n  bottom: 0;\n  left: 0;\n  right: 0;\n  height: 56px;\n  background-color: #FFFFFF;\n  display: flex;\n  justify-content: space-around;\n  align-items: center;\n  border-top: 1px solid #F0F0F0;\n  padding-bottom: env(safe-area-inset-bottom);\n  z-index: 100;\n}\n\n.tab-item {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  height: 100%;\n  padding: 6px 0;\n  box-sizing: border-box;\n  position: relative;\n}\n\n.tab-icon {\n  width: 24px;\n  height: 24px;\n  margin-bottom: 4px;\n  color: #999999;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  background-size: contain;\n  background-position: center;\n  background-repeat: no-repeat;\n}\n\n.tab-icon.dashboard {\n  background-image: url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%230A84FF'%3E%3Cpath d='M3 13h8V3H3v10zm0 8h8v-6H3v6zm10 0h8V11h-8v10zm0-18v6h8V3h-8z'/%3E%3C/svg%3E\");\n}\n\n.tab-icon.store {\n  background-image: url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%230A84FF'%3E%3Cpath d='M20 4H4v2h16V4zm1 10v-2l-1-5H4l-1 5v2h1v6h10v-6h4v6h2v-6h1zm-9 4H6v-4h6v4z'/%3E%3C/svg%3E\");\n}\n\n.tab-icon.marketing {\n  background-image: url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%230A84FF'%3E%3Cpath d='M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z'/%3E%3C/svg%3E\");\n}\n\n.tab-icon.orders {\n  background-image: url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%230A84FF'%3E%3Cpath d='M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-5 14H7v-2h7v2zm3-4H7v-2h10v2zm0-4H7V7h10v2z'/%3E%3C/svg%3E\");\n}\n\n.tab-icon.more {\n  background-image: url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%230A84FF'%3E%3Cpath d='M12 8c1.1 0 2-.9 2-2s-.9-2-2-2-2 .9-2 2 .9 2 2 2zm0 2c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2zm0 6c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2z'/%3E%3C/svg%3E\");\n}\n\n  .tab-text {\n  font-size: 10px;\n  color: #999999;\n}\n\n.tab-item.active .tab-icon {\n  color: #0052CC;\n}\n\n.tab-item.active .tab-text {\n  color: #0052CC;\n}\n\n.active-indicator {\n  position: absolute;\n  top: 0;\n  width: 20px;\n  height: 3px;\n  background-color: #0052CC;\n  border-radius: 1.5px;\n}\n\n/* 营销中心与其他图标保持一致的颜色 */\n.tab-item.active[data-tab=\"marketing\"] .tab-icon {\n  color: #0052CC;\n}\n\n.tab-item.active[data-tab=\"marketing\"] .tab-text {\n  color: #0052CC;\n}\n\n.tab-item.active[data-tab=\"marketing\"] .active-indicator {\n  background-color: #0052CC;\n}\n\n\n\n.simple-chart.wide {\n  position: relative;\n  height: 280px;\n  margin-bottom: 30px;\n  padding: 0;\n  background: #fff;\n  border-radius: var(--radius-md);\n  box-shadow: var(--shadow-sm);\n  overflow: hidden;\n  width: 100%;\n}\n.trend-svg {\n  width: 100%;\n  height: 100%;\n  display: block;\n}\n.y-axis.wide {\n  position: absolute;\n  left: 0;\n  top: 0;\n  bottom: 25px;\n  width: 40px;\n  display: flex;\n  flex-direction: column;\n  justify-content: space-between;\n  z-index: 2;\n  pointer-events: none;\n}\n.y-axis-label {\n  font-size: 13px;\n  color: #999;\n  text-align: right;\n  padding-right: 10px;\n  transform: translateY(-50%);\n}\n.x-axis.wide {\n  position: absolute;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  height: 25px;\n  z-index: 2;\n}\n.x-axis-label {\n  position: absolute;\n  font-size: 13px;\n  color: #999;\n  transform: translateX(-50%);\n  text-align: center;\n  bottom: 0;\n}\n.chart-legend {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  gap: var(--space-lg);\n}\n.legend-item {\n  display: flex;\n  align-items: center;\n  font-size: 13px;\n  color: var(--text-secondary);\n}\n.legend-dot {\n  width: 8px;\n  height: 8px;\n  border-radius: 50%;\n  margin-right: var(--space-sm);\n}\n.legend-dot.sales {\n  background: #0A84FF;\n}\n.legend-dot.orders {\n  background: #FF9500;\n}\n\n/* 更精致、高级的销售趋势图表样式 */\n.premium-chart-container {\n  background: linear-gradient(145deg, #ffffff, #f8f9fc);\n  border-radius: var(--radius-lg);\n  padding: var(--space-lg);\n  margin: 0 0 var(--space-lg);\n  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.04), 0 1px 3px rgba(0, 0, 0, 0.05);\n  width: 100%;\n  border: 1px solid rgba(200, 210, 230, 0.3);\n  position: relative;\n  overflow: hidden;\n}\n\n.premium-chart-container::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  height: 3px;\n  background: linear-gradient(90deg, #0A84FF, #5E5CE6);\n  border-radius: var(--radius-lg) var(--radius-lg) 0 0;\n}\n\n.premium-chart-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: var(--space-lg);\n  padding-bottom: var(--space-md);\n  border-bottom: 1px solid rgba(200, 210, 230, 0.3);\n}\n\n.title-section {\n  display: flex;\n  flex-direction: column;\n}\n\n.premium-title {\n  font-size: 18px;\n  font-weight: 600;\n  color: var(--text-primary);\n  margin-bottom: 4px;\n}\n\n.premium-subtitle {\n  font-size: 13px;\n  color: var(--text-tertiary);\n}\n\n.premium-tabs {\n  display: flex;\n  background: rgba(200, 210, 230, 0.15);\n  border-radius: 20px;\n  padding: 2px;\n}\n\n.premium-tab {\n  padding: 8px 16px;\n  font-size: 13px;\n  color: var(--text-tertiary);\n  border-radius: 18px;\n  transition: all 0.3s ease;\n  font-weight: 500;\n}\n\n.premium-tab.active {\n  background: linear-gradient(90deg, #0A84FF, #5E5CE6);\n  color: white;\n  box-shadow: 0 4px 8px rgba(10, 132, 255, 0.2);\n}\n\n.premium-chart {\n  position: relative;\n  height: 280px;\n  margin-bottom: var(--space-md);\n  padding: 20px 20px 40px;\n  background-color: #FCFCFF;\n  border-radius: var(--radius-md);\n  border: 1px solid rgba(200, 210, 230, 0.3);\n  overflow: hidden;\n}\n\n/* 图表背景 */\n.chart-background {\n  position: absolute;\n  left: 20px;\n  right: 20px;\n  top: 20px;\n  bottom: 40px;\n  z-index: 1;\n}\n\n.grid-line {\n  position: absolute;\n  left: 0;\n  right: 0;\n  height: 1px;\n  background-color: rgba(200, 210, 230, 0.2);\n}\n\n.grid-line:nth-child(1) { top: 0%; }\n.grid-line:nth-child(2) { top: 33.33%; }\n.grid-line:nth-child(3) { top: 66.66%; }\n.grid-line:nth-child(4) { top: 100%; }\n\n.grid-line-vertical {\n  position: absolute;\n  top: 0;\n  bottom: 0;\n  width: 1px;\n  background-color: rgba(200, 210, 230, 0.1);\n}\n\n.grid-line-vertical:nth-child(1) { left: 0%; }\n.grid-line-vertical:nth-child(2) { left: 16.67%; }\n.grid-line-vertical:nth-child(3) { left: 33.33%; }\n.grid-line-vertical:nth-child(4) { left: 50%; }\n.grid-line-vertical:nth-child(5) { left: 66.67%; }\n.grid-line-vertical:nth-child(6) { left: 83.33%; }\n.grid-line-vertical:nth-child(7) { left: 100%; }\n\n/* 区域图表 */\n.area-chart {\n  position: absolute;\n  left: 20px;\n  right: 20px;\n  top: 20px;\n  bottom: 40px;\n  z-index: 2;\n}\n\n/* 数据点 */\n.data-points {\n  position: absolute;\n  left: 20px;\n  right: 20px;\n  top: 20px;\n  bottom: 40px;\n  z-index: 3;\n}\n\n.data-point {\n  position: absolute;\n  width: 4px;\n  height: 4px;\n  border-radius: 50%;\n  transform: translate(-50%, -50%);\n  cursor: pointer;\n  transition: all 0.2s ease;\n}\n\n.data-point.sales {\n  background-color: #0A84FF;\n  box-shadow: 0 0 0 2px rgba(10, 132, 255, 0.1);\n}\n\n.data-point.orders {\n  background-color: #FF9500;\n  box-shadow: 0 0 0 2px rgba(255, 149, 0, 0.1);\n}\n\n.active-point {\n  position: absolute;\n  width: 14px;\n  height: 14px;\n  border-radius: 50%;\n  transform: translate(-50%, -50%);\n  z-index: 4;\n  box-shadow: 0 0 0 4px rgba(255, 255, 255, 0.8);\n}\n\n.active-point.sales {\n  background-color: #0A84FF;\n  border: 2px solid white;\n}\n\n.active-point.orders {\n  background-color: #FF9500;\n  border: 2px solid white;\n}\n\n/* 提示框 */\n.premium-tooltip {\n  position: absolute;\n  transform: translateX(-50%);\n  z-index: 10;\n  background: white;\n  border-radius: 8px;\n  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);\n  padding: 12px;\n  min-width: 160px;\n  pointer-events: none;\n  border: 1px solid rgba(200, 210, 230, 0.3);\n}\n\n.premium-tooltip::after {\n  content: '';\n  position: absolute;\n  bottom: -8px;\n  left: 50%;\n  transform: translateX(-50%);\n  width: 14px;\n  height: 14px;\n  background: white;\n  border-right: 1px solid rgba(200, 210, 230, 0.3);\n  border-bottom: 1px solid rgba(200, 210, 230, 0.3);\n  transform: translateX(-50%) rotate(45deg);\n}\n\n.tooltip-date {\n  font-size: 12px;\n  color: var(--text-tertiary);\n  text-align: center;\n  margin-bottom: 8px;\n  border-bottom: 1px solid rgba(200, 210, 230, 0.3);\n  padding-bottom: 8px;\n}\n\n.tooltip-content {\n  display: flex;\n  flex-direction: column;\n  gap: 8px;\n}\n\n.tooltip-item {\n  display: flex;\n  align-items: center;\n}\n\n.tooltip-dot {\n  width: 8px;\n  height: 8px;\n  border-radius: 50%;\n  margin-right: 8px;\n}\n\n.tooltip-item.sales .tooltip-dot {\n  background-color: #0A84FF;\n}\n\n.tooltip-item.orders .tooltip-dot {\n  background-color: #FF9500;\n}\n\n.tooltip-label {\n  font-size: 12px;\n  color: var(--text-tertiary);\n  margin-right: 8px;\n}\n\n.tooltip-value {\n  font-size: 14px;\n  font-weight: 600;\n  color: var(--text-primary);\n  margin-left: auto;\n}\n\n/* 坐标轴 */\n.y-axis-premium {\n  position: absolute;\n  left: 0;\n  top: 20px;\n  bottom: 40px;\n  width: 40px;\n  display: flex;\n  flex-direction: column;\n  justify-content: space-between;\n  z-index: 2;\n  pointer-events: none;\n}\n\n.x-axis-premium {\n  position: absolute;\n  left: 20px;\n  right: 20px;\n  bottom: 0;\n  height: 40px;\n  z-index: 2;\n}\n\n.y-axis-label {\n  font-size: 12px;\n  color: var(--text-tertiary);\n  text-align: center;\n  transform: translateY(-50%);\n}\n\n.x-axis-label {\n  position: absolute;\n  font-size: 12px;\n  color: var(--text-tertiary);\n  transform: translateX(-50%);\n  text-align: center;\n  bottom: 12px;\n}\n\n/* 图表底部 */\n.chart-footer {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-top: var(--space-md);\n}\n\n.chart-legend {\n  display: flex;\n  gap: var(--space-lg);\n}\n\n.legend-item {\n  display: flex;\n  align-items: center;\n  font-size: 13px;\n  color: var(--text-secondary);\n}\n\n.legend-dot {\n  width: 8px;\n  height: 8px;\n  border-radius: 50%;\n  margin-right: var(--space-sm);\n}\n\n.legend-dot.sales {\n  background: #0A84FF;\n}\n\n.legend-dot.orders {\n  background: #FF9500;\n}\n\n.chart-actions {\n  display: flex;\n  gap: var(--space-md);\n}\n\n.action-btn {\n  display: flex;\n  align-items: center;\n  padding: 6px 12px;\n  border-radius: 4px;\n  background: rgba(200, 210, 230, 0.15);\n  font-size: 12px;\n  color: var(--text-secondary);\n  cursor: pointer;\n  transition: all 0.2s ease;\n}\n\n.action-btn:hover {\n  background: rgba(200, 210, 230, 0.3);\n}\n\n.action-icon {\n  width: 14px;\n  height: 14px;\n  margin-right: 4px;\n  opacity: 0.7;\n}\n\n.action-icon.export {\n  background-image: url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23666'%3E%3Cpath d='M19 9h-4V3H9v6H5l7 7 7-7zM5 18v2h14v-2H5z'/%3E%3C/svg%3E\");\n  background-size: contain;\n}\n\n.action-icon.report {\n  background-image: url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23666'%3E%3Cpath d='M14 2H6c-1.1 0-1.99.9-1.99 2L4 20c0 1.1.89 2 1.99 2H18c1.1 0 2-.9 2-2V8l-6-6zm2 16H8v-2h8v2zm0-4H8v-2h8v2zm-3-5V3.5L18.5 9H13z'/%3E%3C/svg%3E\");\n  background-size: contain;\n}\n\n/* 分析洞察模块样式 */\n.analytics-grid {\n  display: grid;\n  grid-template-columns: repeat(2, 1fr);\n  grid-gap: 12px;\n  margin-top: 12px;\n}\n\n.analytics-card {\n  background-color: var(--bg-secondary);\n  border-radius: 12px;\n  padding: 16px;\n  display: flex;\n  align-items: center;\n  box-shadow: var(--shadow-xs);\n  transition: all 0.3s ease;\n}\n\n.analytics-card:active {\n  transform: scale(0.98);\n  background-color: var(--bg-tertiary);\n}\n\n.analytics-icon {\n  width: 40px;\n  height: 40px;\n  border-radius: 10px;\n  margin-right: 12px;\n  background-size: 24px;\n  background-position: center;\n  background-repeat: no-repeat;\n  background-color: rgba(10, 132, 255, 0.1);\n}\n\n.analytics-icon.customer-insights {\n  background-image: url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%230A84FF'%3E%3Cpath d='M9 11.75c-.69 0-1.25.56-1.25 1.25s.56 1.25 1.25 1.25 1.25-.56 1.25-1.25-.56-1.25-1.25-1.25zm6 0c-.69 0-1.25.56-1.25 1.25s.56 1.25 1.25 1.25 1.25-.56 1.25-1.25-.56-1.25-1.25-1.25zM12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8 0-.29.02-.58.05-.86 2.36-1.05 4.23-2.98 5.21-5.37C11.07 8.33 14.05 10 17.42 10c.78 0 1.53-.09 2.25-.26.21.71.33 1.47.33 2.26 0 4.41-3.59 8-8 8z'/%3E%3C/svg%3E\");\n}\n\n.analytics-icon.product-analytics {\n  background-image: url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%230A84FF'%3E%3Cpath d='M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-5 14H7v-7h2v7zm4 0h-2v-7h2v7zm4 0h-2v-7h2v7z'/%3E%3C/svg%3E\");\n}\n\n.analytics-icon.market-trends {\n  background-image: url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%230A84FF'%3E%3Cpath d='M3.5 18.49l6-6.01 4 4L22 6.92l-1.41-1.41-7.09 7.97-4-4L2 16.99z'/%3E%3C/svg%3E\");\n}\n\n.analytics-icon.performance-metrics {\n  background-image: url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%230A84FF'%3E%3Cpath d='M15.9 5c-.17 0-.32.09-.41.23l-.07.15-5.18 11.65c-.16.29-.26.61-.26.96 0 1.11.9 2.01 2.01 2.01.96 0 1.77-.68 1.96-1.59l.01-.03L16.4 9.6l3.3 5.89c.12.22.35.35.59.35.05 0 .1 0 .15-.01.29-.06.51-.31.54-.6l.37-4.88 2.04.87c.22.09.47.01.63-.18.21-.23.21-.59-.01-.82l-6.53-5.89c-.18-.17-.43-.22-.64-.15z'/%3E%3C/svg%3E\");\n}\n\n.analytics-content {\n  flex: 1;\n}\n\n.analytics-title {\n  font-size: 15px;\n  font-weight: 500;\n  color: var(--text-primary);\n  margin-bottom: 4px;\n  display: block;\n}\n\n.analytics-desc {\n  font-size: 12px;\n  color: var(--text-tertiary);\n  display: block;\n  line-height: 1.4;\n}\n\n/* 系统设置模块样式 */\n.settings-container {\n  margin-bottom: 80px; /* 为底部导航腾出空间 */\n}\n\n.settings-grid {\n  display: grid;\n  grid-template-columns: repeat(4, 1fr);\n  grid-gap: 12px;\n  margin-top: 12px;\n}\n\n.settings-card {\n  background-color: var(--bg-secondary);\n  border-radius: 12px;\n  padding: 16px 8px;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  box-shadow: var(--shadow-xs);\n  transition: all 0.3s ease;\n}\n\n.settings-card:active {\n  transform: scale(0.95);\n  background-color: var(--bg-tertiary);\n}\n\n.settings-icon {\n  width: 44px;\n  height: 44px;\n  border-radius: 22px;\n  margin-bottom: 8px;\n  background-size: 24px;\n  background-position: center;\n  background-repeat: no-repeat;\n  background-color: rgba(10, 132, 255, 0.1);\n}\n\n.settings-icon.account {\n  background-image: url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%230A84FF'%3E%3Cpath d='M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z'/%3E%3C/svg%3E\");\n}\n\n.settings-icon.security {\n  background-image: url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%230A84FF'%3E%3Cpath d='M12 1L3 5v6c0 5.55 3.84 10.74 9 12 5.16-1.26 9-6.45 9-12V5l-9-4zm0 10.99h7c-.53 4.12-3.28 7.79-7 8.94V12H5V6.3l7-3.11v8.8z'/%3E%3C/svg%3E\");\n}\n\n.settings-icon.notifications {\n  background-image: url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%230A84FF'%3E%3Cpath d='M12 22c1.1 0 2-.9 2-2h-4c0 1.1.89 2 2 2zm6-6v-5c0-3.07-1.64-5.64-4.5-6.32V4c0-.83-.67-1.5-1.5-1.5s-1.5.67-1.5 1.5v.68C7.63 5.36 6 7.92 6 11v5l-2 2v1h16v-1l-2-2z'/%3E%3C/svg%3E\");\n}\n\n.settings-icon.payment {\n  background-image: url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%230A84FF'%3E%3Cpath d='M20 4H4c-1.11 0-1.99.89-1.99 2L2 18c0 1.11.89 2 1.99 2h16c1.11 0 2-.89 2-2V6c0-1.11-.89-2-2-2zm0 14H4v-6h16v6zm0-10H4V6h16v2z'/%3E%3C/svg%3E\");\n}\n\n.settings-title {\n  font-size: 13px;\n  color: var(--text-secondary);\n  text-align: center;\n}\n\n/* 响应式调整 */\n@media screen and (max-width: 360px) {\n  .settings-grid {\n    grid-template-columns: repeat(2, 1fr);\n  }\n  \n  .analytics-card {\n    padding: 12px;\n  }\n  \n  .analytics-icon {\n    width: 36px;\n    height: 36px;\n  }\n  \n  .analytics-title {\n    font-size: 14px;\n  }\n}\n\n/* 分析洞察模块 */\n.analytics-section {\n  margin-bottom: var(--space-lg);\n  background-color: var(--bg-card);\n  border-radius: var(--radius-lg);\n  padding: var(--space-lg);\n  box-shadow: var(--shadow-sm);\n  width: 96%;\n  margin-left: auto;\n  margin-right: auto;\n}\n\n.analytics-grid {\n  display: grid;\n  grid-template-columns: repeat(2, 1fr);\n  grid-gap: 12px;\n  margin-top: 12px;\n}\n\n.analytics-card {\n  background-color: var(--bg-secondary);\n  border-radius: 12px;\n  padding: 16px;\n  display: flex;\n  align-items: center;\n  box-shadow: var(--shadow-xs);\n  transition: all 0.3s ease;\n}\n\n.analytics-card:active {\n  transform: scale(0.98);\n  background-color: var(--bg-tertiary);\n}\n\n.analytics-icon {\n  width: 40px;\n  height: 40px;\n  border-radius: 10px;\n  margin-right: 12px;\n  background-size: 24px;\n  background-position: center;\n  background-repeat: no-repeat;\n  background-color: rgba(10, 132, 255, 0.1);\n}\n\n.analytics-icon.customer-insights {\n  background-image: url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%230A84FF'%3E%3Cpath d='M9 11.75c-.69 0-1.25.56-1.25 1.25s.56 1.25 1.25 1.25 1.25-.56 1.25-1.25-.56-1.25-1.25-1.25zm6 0c-.69 0-1.25.56-1.25 1.25s.56 1.25 1.25 1.25 1.25-.56 1.25-1.25-.56-1.25-1.25-1.25zM12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8 0-.29.02-.58.05-.86 2.36-1.05 4.23-2.98 5.21-5.37C11.07 8.33 14.05 10 17.42 10c.78 0 1.53-.09 2.25-.26.21.71.33 1.47.33 2.26 0 4.41-3.59 8-8 8z'/%3E%3C/svg%3E\");\n}\n\n.analytics-icon.product-analytics {\n  background-image: url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%230A84FF'%3E%3Cpath d='M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-5 14H7v-7h2v7zm4 0h-2v-7h2v7zm4 0h-2v-7h2v7z'/%3E%3C/svg%3E\");\n}\n\n.analytics-icon.market-trends {\n  background-image: url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%230A84FF'%3E%3Cpath d='M3.5 18.49l6-6.01 4 4L22 6.92l-1.41-1.41-7.09 7.97-4-4L2 16.99z'/%3E%3C/svg%3E\");\n}\n\n.analytics-icon.performance-metrics {\n  background-image: url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%230A84FF'%3E%3Cpath d='M15.9 5c-.17 0-.32.09-.41.23l-.07.15-5.18 11.65c-.16.29-.26.61-.26.96 0 1.11.9 2.01 2.01 2.01.96 0 1.77-.68 1.96-1.59l.01-.03L16.4 9.6l3.3 5.89c.12.22.35.35.59.35.05 0 .1 0 .15-.01.29-.06.51-.31.54-.6l.37-4.88 2.04.87c.22.09.47.01.63-.18.21-.23.21-.59-.01-.82l-6.53-5.89c-.18-.17-.43-.22-.64-.15z'/%3E%3C/svg%3E\");\n}\n\n.analytics-content {\n  flex: 1;\n}\n\n.analytics-title {\n  font-size: 15px;\n  font-weight: 500;\n  color: var(--text-primary);\n  margin-bottom: 4px;\n  display: block;\n}\n\n.analytics-desc {\n  font-size: 12px;\n  color: var(--text-tertiary);\n  display: block;\n  line-height: 1.4;\n}\n\n/* 系统设置模块样式 */\n.settings-section {\n  margin-bottom: var(--space-lg);\n  background-color: var(--bg-card);\n  border-radius: var(--radius-lg);\n  padding: var(--space-lg);\n  box-shadow: var(--shadow-sm);\n  width: 96%;\n  margin-left: auto;\n  margin-right: auto;\n}\n\n.settings-grid {\n  display: grid;\n  grid-template-columns: repeat(4, 1fr);\n  grid-gap: 12px;\n  margin-top: 12px;\n}\n\n.settings-card {\n  background-color: var(--bg-secondary);\n  border-radius: 12px;\n  padding: 16px 8px;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  box-shadow: var(--shadow-xs);\n  transition: all 0.3s ease;\n}\n\n.settings-card:active {\n  transform: scale(0.95);\n  background-color: var(--bg-tertiary);\n}\n\n.settings-icon {\n  width: 44px;\n  height: 44px;\n  border-radius: 22px;\n  margin-bottom: 8px;\n  background-size: 24px;\n  background-position: center;\n  background-repeat: no-repeat;\n  background-color: rgba(10, 132, 255, 0.1);\n}\n\n.settings-icon.account {\n  background-image: url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%230A84FF'%3E%3Cpath d='M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z'/%3E%3C/svg%3E\");\n}\n\n.settings-icon.security {\n  background-image: url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%230A84FF'%3E%3Cpath d='M12 1L3 5v6c0 5.55 3.84 10.74 9 12 5.16-1.26 9-6.45 9-12V5l-9-4zm0 10.99h7c-.53 4.12-3.28 7.79-7 8.94V12H5V6.3l7-3.11v8.8z'/%3E%3C/svg%3E\");\n}\n\n.settings-icon.notifications {\n  background-image: url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%230A84FF'%3E%3Cpath d='M12 22c1.1 0 2-.9 2-2h-4c0 1.1.89 2 2 2zm6-6v-5c0-3.07-1.64-5.64-4.5-6.32V4c0-.83-.67-1.5-1.5-1.5s-1.5.67-1.5 1.5v.68C7.63 5.36 6 7.92 6 11v5l-2 2v1h16v-1l-2-2z'/%3E%3C/svg%3E\");\n}\n\n.settings-icon.payment {\n  background-image: url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%230A84FF'%3E%3Cpath d='M20 4H4c-1.11 0-1.99.89-1.99 2L2 18c0 1.11.89 2 1.99 2h16c1.11 0 2-.89 2-2V6c0-1.11-.89-2-2-2zm0 14H4v-6h16v6zm0-10H4V6h16v2z'/%3E%3C/svg%3E\");\n}\n\n.settings-title {\n  font-size: 13px;\n  color: var(--text-secondary);\n  text-align: center;\n}\n\n/* 响应式调整 */\n@media screen and (max-width: 360px) {\n  .settings-grid {\n    grid-template-columns: repeat(2, 1fr);\n  }\n  \n  .analytics-card {\n    padding: 12px;\n  }\n  \n  .analytics-icon {\n    width: 36px;\n    height: 36px;\n  }\n  \n  .analytics-title {\n    font-size: 14px;\n  }\n}\n\n/* 入口按钮样式 */\n.entry-section {\n  margin-bottom: calc(var(--space-lg) + 70px); /* 增加底部间距，避免被导航栏遮挡 */\n  background-color: var(--bg-card);\n  border-radius: var(--radius-lg);\n  padding: 0;\n  box-shadow: var(--shadow-sm);\n  border: 1px solid rgba(200, 210, 230, 0.3);\n  width: 96%;\n  margin-left: auto;\n  margin-right: auto;\n}\n\n.entry-button {\n  display: flex;\n  align-items: center;\n  padding: var(--space-md) var(--space-lg);\n  transition: background-color 0.2s ease;\n  position: relative;\n}\n\n.entry-button:active {\n  background-color: var(--bg-secondary);\n}\n\n.entry-icon {\n  width: 36px;\n  height: 36px;\n  border-radius: 18px;\n  background-size: 20px;\n  background-position: center;\n  background-repeat: no-repeat;\n  background-color: rgba(10, 132, 255, 0.1);\n  margin-right: var(--space-md);\n  flex-shrink: 0;\n}\n\n.entry-icon.settings {\n  background-image: url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%230A84FF'%3E%3Cpath d='M19.14 12.94c.04-.3.06-.61.06-.94 0-.32-.02-.64-.07-.94l2.03-1.58c.18-.14.23-.41.12-.61l-1.92-3.32c-.12-.22-.37-.29-.59-.22l-2.39.96c-.5-.38-1.03-.7-1.62-.94l-.36-2.54c-.04-.24-.24-.41-.48-.41h-3.84c-.24 0-.43.17-.47.41l-.36 2.54c-.59.24-1.13.57-1.62.94l-2.39-.96c-.22-.08-.47 0-.59.22L2.74 8.87c-.12.21-.08.47.12.61l2.03 1.58c-.05.3-.09.63-.09.94s.02.64.07.94l-2.03 1.58c-.18.14-.23.41-.12.61l1.92 3.32c.12.22.37.29.59.22l2.39-.96c.5.38 1.03.7 1.62.94l.36 2.54c.05.24.24.41.48.41h3.84c.24 0 .44-.17.47-.41l.36-2.54c.59-.24 1.13-.56 1.62-.94l2.39.96c.22.08.47 0 .59-.22l1.92-3.32c.12-.22.07-.47-.12-.61l-2.01-1.58zM12 15.6c-1.98 0-3.6-1.62-3.6-3.6s1.62-3.6 3.6-3.6 3.6 1.62 3.6 3.6-1.62 3.6-3.6 3.6z'/%3E%3C/svg%3E\");\n}\n\n.entry-icon.analytics {\n  background-image: url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%230A84FF'%3E%3Cpath d='M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-5h2v5zm4 0h-2v-3h2v3zm0-5h-2v-2h2v2zm4 5h-2V7h2v10z'/%3E%3C/svg%3E\");\n}\n\n.entry-icon.campaign {\n  background-image: url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%230A84FF'%3E%3Cpath d='M11 17.25C11 17.25 16 14 16 9.75C16 5.5 11 5.5 11 9.75M11 17.25C11 17.25 6 14 6 9.75C6 5.5 11 5.5 11 9.75M11 17.25V21M11 9.75V3' stroke='%230A84FF' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E\");\n}\n\n.entry-text {\n  font-size: 16px;\n  font-weight: 500;\n  color: var(--text-primary);\n  flex: 1;\n}\n\n.entry-arrow {\n  width: 20px;\n  height: 20px;\n  background-image: url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%238E8E93'%3E%3Cpath d='M9 18L15 12L9 6' stroke='%238E8E93' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E\");\n  background-size: contain;\n  background-position: center;\n  background-repeat: no-repeat;\n  flex-shrink: 0;\n}\n\n/* 添加媒体查询以在小屏幕上优化销售趋势图表 */\n@media screen and (max-width: 375px) {\n  .chart-header {\n    flex-direction: column;\n    align-items: flex-start;\n  }\n  \n  .time-tabs {\n    margin-left: 0;\n    margin-top: var(--space-xs);\n    width: 100%;\n  }\n  \n  .simple-chart.wide {\n    height: 220px; /* 在小屏幕上减小图表高度 */\n  }\n  \n  .x-axis-label {\n    font-size: 10px; /* 减小标签字体大小 */\n  }\n}\n\n\n\n\n\n\n\n\n</style>\n", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/subPackages/merchant-admin-home/pages/merchant-home/index.vue'\nwx.createPage(MiniProgramPage)"], "names": ["reactive", "ref", "computed", "uni", "onMounted"], "mappings": ";;;;;;;;;;AA2OA,UAAM,WAAWA,cAAAA,SAAS;AAAA,MAClB,MAAM;AAAA,MACN,QAAQ;AAAA,IAChB,CAAC;AAGD,UAAM,eAAeA,cAAAA,SAAS;AAAA,MACtB,UAAU;AAAA,MACV,OAAO;AAAA,MACP,QAAQ;AAAA,MACR,WAAW;AAAA,IACnB,CAAC;AAGD,UAAM,cAAcC,cAAAA,IAAI,YAAY;AACZA,kBAAG,IAAC,IAAI;AAChC,UAAM,mBAAmBA,cAAAA,IAAI,OAAO;AACpC,UAAM,cAAcA,cAAAA,IAAI,QAAQ;AAChC,UAAM,cAAcA,cAAAA,IAAI,KAAK;AAC7B,UAAM,gBAAgBA,cAAAA,IAAI,GAAG;AAC7B,UAAM,aAAaA,cAAAA,IAAI,CAAC;AAGDD,kBAAAA,SAAS;AAAA,MACxB;AAAA,MACA;AAAA,MACA;AAAA,IACR,CAAC;AAGD,UAAM,eAAeA,cAAAA,SAAS;AAAA,MACtB;AAAA,QACE,MAAM;AAAA,QACN,MAAM;AAAA,QACN,KAAK;AAAA,MACN;AAAA,MACD;AAAA,QACE,MAAM;AAAA,QACN,MAAM;AAAA,QACN,KAAK;AAAA,MACN;AAAA,MACD;AAAA,QACE,MAAM;AAAA,QACN,MAAM;AAAA,QACN,KAAK;AAAA,MACN;AAAA,MACD;AAAA,QACE,MAAM;AAAA,QACN,MAAM;AAAA,QACN,KAAK;AAAA,MACN;AAAA,MACD;AAAA,QACE,MAAM;AAAA,QACN,MAAM;AAAA,QACN,KAAK;AAAA,MACN;AAAA,MACD;AAAA,QACE,MAAM;AAAA,QACN,MAAM;AAAA,QACN,KAAK;AAAA,MACN;AAAA,IACT,CAAC;AAGD,UAAM,YAAYA,cAAAA,SAAS;AAAA,MACnB,OAAO;AAAA,QACP;AAAA,UACE,OAAO;AAAA,UACP,OAAO;AAAA,UACP,YAAY;AAAA,UACV,OAAO;AAAA,UACP,MAAM;AAAA,UACN,MAAM;AAAA,QACT;AAAA,QACD;AAAA,UACE,OAAO;AAAA,UACP,OAAO;AAAA,UACP,YAAY;AAAA,UACV,OAAO;AAAA,UACP,MAAM;AAAA,UACN,MAAM;AAAA,QACT;AAAA,QACD;AAAA,UACE,OAAO;AAAA,UACP,OAAO;AAAA,UACP,YAAY;AAAA,UACV,OAAO;AAAA,UACP,MAAM;AAAA,UACN,MAAM;AAAA,QACT;AAAA,QACD;AAAA,UACE,OAAO;AAAA,UACP,OAAO;AAAA,UACP,YAAY;AAAA,UACV,OAAO;AAAA,UACP,MAAM;AAAA,UACN,MAAM;AAAA,QACP;AAAA,MACF;AAAA,MACD,MAAM;AAAA,QACJ;AAAA,UACE,OAAO;AAAA,UACP,OAAO;AAAA,UACP,YAAY;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,UACN,MAAM;AAAA,QACP;AAAA,QACD;AAAA,UACE,OAAO;AAAA,UACP,OAAO;AAAA,UACP,YAAY;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,UACN,MAAM;AAAA,QACP;AAAA,QACD;AAAA,UACE,OAAO;AAAA,UACP,OAAO;AAAA,UACP,YAAY;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,UACN,MAAM;AAAA,QACP;AAAA,QACD;AAAA,UACE,OAAO;AAAA,UACP,OAAO;AAAA,UACP,YAAY;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,UACN,MAAM;AAAA,QACP;AAAA,MACF;AAAA,MACD,OAAO;AAAA,QACL;AAAA,UACE,OAAO;AAAA,UACP,OAAO;AAAA,UACP,YAAY;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,UACN,MAAM;AAAA,QACP;AAAA,QACD;AAAA,UACE,OAAO;AAAA,UACP,OAAO;AAAA,UACP,YAAY;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,UACN,MAAM;AAAA,QACP;AAAA,QACD;AAAA,UACE,OAAO;AAAA,UACP,OAAO;AAAA,UACP,YAAY;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,UACN,MAAM;AAAA,QACP;AAAA,QACD;AAAA,UACE,OAAO;AAAA,UACP,OAAO;AAAA,UACP,YAAY;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,UACN,MAAM;AAAA,QACP;AAAA,MACF;AAAA,IACT,CAAC;AAGD,UAAM,QAAQA,cAAAA,SAAS;AAAA,MACf;AAAA,QACE,OAAO;AAAA,QACP,MAAM;AAAA,QACN,WAAW;AAAA,QACX,UAAU;AAAA,QACV,MAAM;AAAA,MACP;AAAA,MACD;AAAA,QACE,OAAO;AAAA,QACP,MAAM;AAAA,QACN,WAAW;AAAA,QACX,UAAU;AAAA,QACV,MAAM;AAAA,MACP;AAAA,MACD;AAAA,QACE,OAAO;AAAA,QACP,MAAM;AAAA,QACN,WAAW;AAAA,QACX,UAAU;AAAA,QACV,MAAM;AAAA,MACP;AAAA,MACD;AAAA,QACE,OAAO;AAAA,QACP,MAAM;AAAA,QACN,WAAW;AAAA,QACX,UAAU;AAAA,QACV,MAAM;AAAA,MACP;AAAA,MACD;AAAA,QACE,OAAO;AAAA,QACP,MAAM;AAAA,QACN,WAAW;AAAA,QACX,UAAU;AAAA,QACV,MAAM;AAAA,MACP;AAAA,MACD;AAAA,QACE,OAAO;AAAA,QACP,MAAM;AAAA,QACN,WAAW;AAAA,QACX,UAAU;AAAA,QACV,MAAM;AAAA,MACP;AAAA,IACT,CAAC;AAGkBA,kBAAAA,SAAS;AAAA,MACpB;AAAA,QACE,OAAO;AAAA,QACP,WAAW;AAAA,QACX,SAAS;AAAA,QACT,QAAQ;AAAA,QACR,OAAO;AAAA,QACP,OAAO;AAAA,QACP,UAAU;AAAA,MACX;AAAA,MACD;AAAA,QACE,OAAO;AAAA,QACP,WAAW;AAAA,QACX,SAAS;AAAA,QACT,QAAQ;AAAA,QACR,OAAO;AAAA,QACP,OAAO;AAAA,QACP,UAAU;AAAA,MACX;AAAA,MACD;AAAA,QACE,OAAO;AAAA,QACP,WAAW;AAAA,QACX,SAAS;AAAA,QACT,QAAQ;AAAA,QACR,OAAO;AAAA,QACP,OAAO;AAAA,QACP,UAAU;AAAA,MACX;AAAA,IACT,CAAC;AAGD,UAAM,WAAWA,cAAAA,SAAS;AAAA,MAClB;AAAA,QACE,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,MAAM;AAAA,QACZ,KAAK;AAAA,MACA;AAAA,MACD;AAAA,QACE,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,MAAM;AAAA,QACN,KAAK;AAAA,MACN;AAAA,MACD;AAAA,QACE,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,MAAM;AAAA,QACZ,KAAK;AAAA,MACA;AAAA,MACD;AAAA,QACE,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,MAAM;AAAA,QACN,KAAK;AAAA,MACN;AAAA,MACD;AAAA,QACE,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,MAAM;AAAA,QACN,KAAK;AAAA,MACZ;AAAA,IACH,CAAC;AAGD,UAAM,eAAeE,cAAQ,SAAC,MAAM;AAClC,aAAO,UAAU,iBAAiB,KAAK,KAAK,UAAU;AAAA,IACxD,CAAC;AAEyBA,kBAAAA,SAAS,MAAM;AACvC,aAAO,MAAM,OAAO,UAAQ,CAAC,KAAK,SAAS,EAAE;AAAA,IAC/C,CAAC;AAGmBA,kBAAAA,SAAS,MAAM;AAEjC,YAAM,WAAW,SAAS,MAAM,GAAG,CAAC;AAGhC,eAAS,KAAK;AAAA,QACZ,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,MAAM;AAAA,QACN,KAAK;AAAA,MACb,CAAO;AAKD,aAAO;AAAA,IACb,CAAC;AAGD,aAAS,iBAAiB;AACpB,YAAM,OAAO,oBAAI;AACjB,YAAM,OAAO,KAAK;AAClB,YAAM,QAAQ,KAAK,SAAQ,IAAK;AAChC,YAAM,MAAM,KAAK;AACrB,kBAAY,QAAQ,GAAG,IAAI,IAAI,KAAK,IAAI,GAAG;AAAA,IAC7C;AAIA,aAAS,gBAAgB,MAAM;AAC7BC,oBAAY,MAAA,MAAA,OAAA,wEAAA,UAAU,IAAI;AAE1B,uBAAiB,QAAQ;AAGzB,UAAI,WAAW,SAAS,UAAU,OAAO,SAAS,SAAS,OAAO;AAGlEA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO,QAAQ;AAAA,QACf,MAAM;AAAA,QACN,UAAU;AAAA,MACd,CAAG;AAAA,IACH;AAyBA,aAAS,kBAAkB,QAAQ;AACjCA,+GAAY,UAAU,OAAO,GAAG;AAEhC,UAAI;AAEF,YAAI,CAAC,OAAO,KAAK;AACf,gBAAM,IAAI,MAAM,QAAQ;AAAA,QACzB;AAGD,YAAI,OAAO,SAAS,QAAQ;AAC1BA,wBAAAA,MAAI,WAAW;AAAA,YACb,KAAK;AAAA,YACL,MAAM,CAAC,QAAQ;AACbA,4BAAc,MAAA,MAAA,SAAA,wEAAA,SAAS,GAAG;AAC1BA,4BAAAA,MAAI,UAAU;AAAA,gBACZ,OAAO;AAAA,gBACP,MAAM;AAAA,gBACN,UAAU;AAAA,cACtB,CAAW;AAAA,YACF;AAAA,UACT,CAAO;AACD;AAAA,QACD;AAGD,YAAI,OAAO,SAAS,QAAQ;AAC1BA,wBAAA,MAAA,MAAA,OAAA,wEAAY,YAAY,OAAO,GAAG;AAClCA,wBAAAA,MAAI,WAAW;AAAA,YACb,KAAK,OAAO;AAAA,YACZ,SAAS,MAAM;AACbA,4BAAAA,MAAY,MAAA,OAAA,wEAAA,WAAW;AAAA,YACxB;AAAA,YACD,MAAM,CAAC,QAAQ;AACbA,4BAAc,MAAA,MAAA,SAAA,wEAAA,aAAa,GAAG;AAE9BA,4BAAAA,MAAI,UAAU;AAAA,gBACZ,KAAK,OAAO;AAAA,gBACZ,MAAM,CAAC,cAAc;AACnBA,6HAAc,qBAAqB,SAAS;AAC5CA,gCAAAA,MAAI,UAAU;AAAA,oBACZ,OAAO;AAAA,oBACP,MAAM;AAAA,oBACN,UAAU;AAAA,kBAC1B,CAAe;AAAA,gBACF;AAAA,cACb,CAAW;AAAA,YACF;AAAA,UACT,CAAO;AACD;AAAA,QACD;AAGCA,sBAAAA,MAAI,WAAW;AAAA,UACb,KAAK,OAAO;AAAA,UACZ,MAAM,CAAC,QAAQ;AACbA,0BAAc,MAAA,MAAA,SAAA,wEAAA,SAAS,GAAG;AAG5BA,0BAAAA,MAAI,UAAU;AAAA,cACZ,KAAK,OAAO;AAAA,cACZ,MAAM,CAAC,cAAc;AACnBA,8BAAc,MAAA,MAAA,SAAA,wEAAA,iBAAiB,SAAS;AAG1CA,8BAAAA,MAAI,UAAU;AAAA,kBACV,OAAO;AAAA,kBACT,MAAM;AAAA,kBACN,UAAU;AAAA,gBACtB,CAAW;AAAA,cACF;AAAA,YACT,CAAO;AAAA,UACA;AAAA,QACP,CAAK;AAAA,MACF,SAAQ,OAAO;AACdA,sBAAc,MAAA,MAAA,SAAA,wEAAA,cAAc,KAAK;AAGjCA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,UACN,UAAU;AAAA,QAChB,CAAK;AAAA,MACF;AAAA,IACH;AA6DA,aAAS,UAAU,OAAO;AAEpB,UAAI,UAAU,QAAQ;AACxB;AACI;AAAA,MACD;AAEL,UAAI,UAAU,WAAW;AAAO;AAEhC,iBAAW,QAAQ;AAGnB,UAAI,UAAU,GAAG;AACbA,sBAAAA,MAAI,WAAW;AAAA,UACf,KAAK;AAAA,UACL,MAAM,CAAC,QAAQ;AACbA,0BAAc,MAAA,MAAA,SAAA,wEAAA,cAAc,GAAG;AAC/BA,0BAAAA,MAAI,UAAU;AAAA,cACZ,OAAO;AAAA,cACP,MAAM;AAAA,YAChB,CAAS;AAAA,UACF;AAAA,QACP,CAAK;AACD;AAAA,MACD;AAGDA,oBAAAA,MAAI,UAAU;AAAA,QACZ,KAAK,SAAS,KAAK,EAAE;AAAA,QACrB,MAAM,CAAC,QAAQ;AACbA,wBAAc,MAAA,MAAA,SAAA,wEAAA,gBAAgB,GAAG;AAEjCA,wBAAAA,MAAI,WAAW;AAAA,YACb,KAAK,SAAS,KAAK,EAAE;AAAA,YACrB,MAAM,CAAC,WAAW;AAChBA,4BAAc,MAAA,MAAA,SAAA,wEAAA,SAAS,MAAM;AAC7BA,4BAAAA,MAAI,UAAU;AAAA,gBACZ,OAAO;AAAA,gBACP,MAAM;AAAA,cAClB,CAAW;AAAA,YACF;AAAA,UACT,CAAO;AAAA,QACF;AAAA,MACL,CAAO;AAAA,IACP;AAGA,aAAS,kBAAkB;AAEzB,YAAM,cAAc,SAAS,MAAM,CAAC,EAAE,IAAI,UAAQ,KAAK,IAAI;AAEvDA,oBAAAA,MAAI,gBAAgB;AAAA,QAClB,UAAU;AAAA,QACV,SAAS,CAAC,QAAQ;AAEhB,gBAAM,WAAW,IAAI,IAAI;AAC7B,qBAAW,QAAQ,SAAS,QAAQ,EAAE;AAGlCA,wBAAAA,MAAI,UAAU;AAAA,YACZ,KAAK,SAAS,QAAQ,EAAE;AAAA,YACxB,MAAM,CAAC,QAAQ;AACbA,4BAAA,MAAA,MAAA,SAAA,wEAAc,gBAAgB,GAAG;AAErCA,4BAAAA,MAAI,WAAW;AAAA,gBACT,KAAK,SAAS,QAAQ,EAAE;AAAA,gBACxB,MAAM,CAAC,WAAW;AAChBA,gCAAc,MAAA,MAAA,SAAA,wEAAA,SAAS,MAAM;AAC7BA,gCAAAA,MAAI,UAAU;AAAA,oBACZ,OAAO;AAAA,oBACP,MAAM;AAAA,kBAC1B,CAAmB;AAAA,gBACF;AAAA,cACjB,CAAe;AAAA,YACF;AAAA,UACb,CAAW;AAAA,QACF;AAAA,MACT,CAAO;AAAA,IACP;AAcA,aAAS,eAAe,MAAM;AACxBA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,iEAAiE,KAAK,IAAI;AAAA,MACvF,CAAO;AAAA,IACP;AAUA,aAAS,SAAS;AACZA,oBAAG,MAAC,aAAY;AAAA,IACtB;AAEA,aAAS,sBAAsB;AACzBA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK;AAAA,MACb,CAAO;AAAA,IACP;AAEA,aAAS,cAAc,MAAM;AACvB,YAAM,UAAU;AAAA,QACd,OAAO;AAAA,UACL,OAAO;AAAA,UACP,KAAK;AAAA,QACN;AAAA,QACD,SAAS;AAAA,UACP,OAAO;AAAA,UACP,KAAK;AAAA,QACN;AAAA,QACD,UAAU;AAAA,UACR,OAAO;AAAA,UACP,KAAK;AAAA,QACN;AAAA,MACT;AAEM,YAAM,SAAS,QAAQ,IAAI;AAC3B,UAAI,QAAQ;AACVA,sBAAAA,MAAI,WAAW;AAAA,UACb,KAAK,OAAO;AAAA,UACZ,SAAS,MAAM;AACbA,0BAAAA,MAAI,UAAU;AAAA,cACZ,OAAO,OAAO,OAAO,KAAK;AAAA,cAC1B,MAAM;AAAA,YACpB,CAAa;AAAA,UACF;AAAA,QACX,CAAS;AAAA,MACF;AAAA,IACP;AA6BA,UAAM,kBAAkBF,cAAAA,IAAI,EAAE;AAC9B,UAAM,eAAeA,cAAAA,IAAI,EAAE;AAG3BG,kBAAAA,UAAU,MAAM;AACd;AAGA,YAAM,UAAUD,oBAAI;AACpB,sBAAgB,QAAQ,QAAQ;AAChC,mBAAa,QAAQ,gBAAgB,QAAQ;AAAA,IAC/C,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACz5BD,GAAG,WAAW,eAAe;"}