<template>
  <view class="search-bar">
    <view class="search-input-container" @tap="navigateToSearch">
      <svg class="search-icon" viewBox="0 0 24 24" width="20" height="20">
        <path fill="#999999" d="M9.5,3A6.5,6.5 0 0,1 16,9.5C16,11.11 15.41,12.59 14.44,13.73L14.71,14H15.5L20.5,19L19,20.5L14,15.5V14.71L13.73,14.44C12.59,15.41 11.11,16 9.5,16A6.5,6.5 0 0,1 3,9.5A6.5,6.5 0 0,1 9.5,3M9.5,5C7,5 5,7 5,9.5C5,12 7,14 9.5,14C12,14 14,12 14,9.5C14,7 12,5 9.5,5Z" />
      </svg>
      <text class="search-placeholder">{{ placeholder }}</text>
    </view>
  </view>
</template>

<script>
export default {
  name: 'SearchBar',
  props: {
    placeholder: {
      type: String,
      default: '搜索商品'
    }
  },
  methods: {
    navigateToSearch() {
      uni.navigateTo({
        url: '/subPackages/cashback/pages/search/index'
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.search-bar {
  padding: 10px 16px;
  
  .search-input-container {
    display: flex;
    align-items: center;
    height: 36px;
    background-color: #F5F7FA;
    border-radius: 18px;
    padding: 0 12px;
    
    .search-icon {
      margin-right: 6px;
    }
    
    .search-placeholder {
      font-size: 14px;
      color: #999999;
    }
  }
}
</style> 