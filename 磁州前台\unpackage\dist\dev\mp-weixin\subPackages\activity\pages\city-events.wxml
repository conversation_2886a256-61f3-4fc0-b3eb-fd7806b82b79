<view class="beautiful-cizhou"><view class="custom-navbar" style="{{'padding-top:' + e}}"><view class="navbar-left" bindtap="{{b}}"><image src="{{a}}" class="back-icon"></image></view><view class="navbar-title">最美磁州</view><view class="navbar-right"><view class="share-icon" bindtap="{{d}}"><image src="{{c}}" class="icon-image"></image></view></view></view><scroll-view class="content-scroll" scroll-y refresher-enabled refresher-triggered="{{v}}" bindrefresherrefresh="{{w}}" style="{{'padding-top:' + x}}"><view class="hero-section"><swiper class="hero-swiper" circular autoplay interval="4000" duration="600" indicator-dots indicator-color="rgba(255,255,255,0.4)" indicator-active-color="#FFFFFF"><swiper-item wx:for="{{f}}" wx:for-item="item" wx:key="d"><image src="{{item.a}}" mode="aspectFill" class="hero-image"></image><view class="hero-caption"><text class="hero-title">{{item.b}}</text><text class="hero-desc">{{item.c}}</text></view></swiper-item></swiper></view><view class="category-tabs"><scroll-view scroll-x class="tab-scroll" show-scrollbar="false"><view wx:for="{{g}}" wx:for-item="tab" wx:key="b" class="{{['tab-item', tab.c && 'active']}}" bindtap="{{tab.d}}"><text class="tab-text">{{tab.a}}</text></view></scroll-view></view><view class="feature-section"><view class="section-header"><view class="section-title"><text class="title-text">磁州特色</text></view><view class="section-more" bindtap="{{h}}"><text class="more-text">更多</text><text class="more-icon">></text></view></view><view class="feature-cards"><view wx:for="{{i}}" wx:for-item="feature" wx:key="f" class="feature-card" bindtap="{{feature.g}}"><image src="{{feature.a}}" mode="aspectFill" class="feature-image"></image><view class="feature-info"><text class="feature-name">{{feature.b}}</text><text class="feature-desc">{{feature.c}}</text><view wx:if="{{feature.d}}" class="feature-tag">{{feature.e}}</view></view></view></view></view><view class="attractions-section"><view class="section-header"><view class="section-title"><text class="title-text">景点推荐</text></view><view class="section-more" bindtap="{{j}}"><text class="more-text">全部</text><text class="more-icon">></text></view></view><scroll-view scroll-x class="attractions-scroll" show-scrollbar="false" enhanced><view wx:for="{{k}}" wx:for-item="item" wx:key="f" class="attraction-item" bindtap="{{item.g}}"><image src="{{item.a}}" mode="aspectFill" class="attraction-image"></image><view class="attraction-info"><view class="attraction-name-row"><text class="attraction-name">{{item.b}}</text><view class="attraction-rating"><text class="rating-value">{{item.c}}</text><image src="{{l}}" class="rating-star"></image></view></view><text class="attraction-address">{{item.d}}</text><view class="attraction-tags"><text wx:for="{{item.e}}" wx:for-item="tag" wx:key="b" class="tag-item">{{tag.a}}</text></view></view></view></scroll-view></view><view class="events-section"><view class="section-header"><view class="section-title"><text class="title-text">本月活动</text></view><view class="section-more" bindtap="{{m}}"><text class="more-text">日历</text><text class="more-icon">></text></view></view><view class="events-list"><view wx:for="{{n}}" wx:for-item="event" wx:key="j" class="event-item" bindtap="{{event.k}}"><view class="event-date"><text class="event-month">{{event.a}}</text><text class="event-day">{{event.b}}</text></view><view class="event-content"><text class="event-title">{{event.c}}</text><text class="event-location">{{event.d}}</text><view class="{{['event-status', event.f && 'status-ongoing', event.g && 'status-upcoming', event.h && 'status-ended']}}">{{event.e}}</view></view><view class="event-action"><text class="action-text">{{event.i}}</text></view></view></view></view><view class="culture-section"><view class="section-header"><view class="section-title"><text class="title-text">本地文化</text></view><view class="section-more" bindtap="{{o}}"><text class="more-text">探索</text><text class="more-icon">></text></view></view><view class="culture-cards"><view wx:for="{{p}}" wx:for-item="item" wx:key="e" class="culture-card" bindtap="{{item.f}}"><view class="culture-icon-bg" style="{{'background-color:' + item.b}}"><image src="{{item.a}}" class="culture-icon"></image></view><view class="culture-info"><text class="culture-name">{{item.c}}</text><text class="culture-desc">{{item.d}}</text></view><view class="arrow-right"><text class="arrow-icon">></text></view></view></view></view><view class="guides-section"><view class="section-header"><view class="section-title"><text class="title-text">精选攻略</text></view><view class="section-more" bindtap="{{q}}"><text class="more-text">全部</text><text class="more-icon">></text></view></view><view class="guides-list"><view wx:for="{{r}}" wx:for-item="guide" wx:key="g" class="guide-item" bindtap="{{guide.h}}"><image src="{{guide.a}}" mode="aspectFill" class="guide-image"></image><view class="guide-content"><text class="guide-title">{{guide.b}}</text><view class="guide-info"><view class="guide-author"><image src="{{guide.c}}" class="author-avatar"></image><text class="author-name">{{guide.d}}</text></view><view class="guide-stats"><view class="stat-item"><image src="{{s}}" class="stat-icon"></image><text class="stat-count">{{guide.e}}</text></view><view class="stat-item"><image src="{{t}}" class="stat-icon"></image><text class="stat-count">{{guide.f}}</text></view></view></view></view></view></view></view></scroll-view></view>