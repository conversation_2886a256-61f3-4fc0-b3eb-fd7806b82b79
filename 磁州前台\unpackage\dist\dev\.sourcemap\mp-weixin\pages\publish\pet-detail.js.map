{"version": 3, "file": "pet-detail.js", "sources": ["pages/publish/pet-detail.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvcHVibGlzaC9wZXQtZGV0YWlsLnZ1ZQ"], "sourcesContent": ["<template>\n  <view class=\"pet-detail-container\">\n    <!-- 添加自定义导航栏 -->\n    <view class=\"custom-navbar\" :style=\"{ paddingTop: statusBarHeight + 'px' }\">\n      <view class=\"navbar-left\" @click=\"goBack\">\n        <image src=\"/static/images/tabbar/返回键.png\" class=\"back-icon\"></image>\n      </view>\n      <view class=\"navbar-title\">宠物信息详情</view>\n      <view class=\"navbar-right\">\n        <!-- 占位 -->\n      </view>\n    </view>\n    \n    <!-- 隐藏的分享按钮，用于自动触发 -->\n    <button id=\"shareButton\" class=\"hidden-share-btn\" open-type=\"share\"></button>\n    \n    <view class=\"pet-detail-wrapper\">\n      <!-- 宠物基本信息卡片 -->\n      <view class=\"content-card pet-info-card\">\n        <view class=\"pet-header\">\n          <view class=\"pet-title-row\">\n            <text class=\"pet-title\">{{petData.title}}</text>\n            <text class=\"pet-price\">{{petData.price}}</text>\n          </view>\n          <view class=\"pet-meta\">\n            <view class=\"pet-tag-group\">\n              <view class=\"pet-tag\" v-for=\"(tag, index) in petData.tags\" :key=\"index\">{{tag}}</view>\n            </view>\n            <text class=\"pet-publish-time\">发布于 {{formatTime(petData.publishTime)}}</text>\n          </view>\n        </view>\n        \n        <!-- 宠物图片轮播 -->\n        <swiper class=\"pet-swiper\" :indicator-dots=\"true\" :autoplay=\"true\" :interval=\"3000\" :duration=\"500\">\n          <swiper-item v-for=\"(image, index) in petData.images\" :key=\"index\">\n            <image :src=\"image\" mode=\"aspectFill\" class=\"pet-image\"></image>\n          </swiper-item>\n        </swiper>\n        \n        <!-- 基本信息 -->\n        <view class=\"pet-basic-info\">\n          <view class=\"info-item\">\n            <text class=\"info-label\">品种</text>\n            <text class=\"info-value\">{{petData.breed}}</text>\n          </view>\n          <view class=\"info-item\">\n            <text class=\"info-label\">年龄</text>\n            <text class=\"info-value\">{{petData.age}}</text>\n          </view>\n          <view class=\"info-item\">\n            <text class=\"info-label\">性别</text>\n            <text class=\"info-value\">{{petData.gender}}</text>\n          </view>\n          <view class=\"info-item\">\n            <text class=\"info-label\">疫苗情况</text>\n            <text class=\"info-value\">{{petData.vaccination}}</text>\n          </view>\n        </view>\n      </view>\n      \n      <!-- 宠物特征 -->\n      <view class=\"content-card pet-features-card\">\n        <view class=\"section-title\">宠物特征</view>\n        <view class=\"features-list\">\n          <view class=\"feature-item\" v-for=\"(item, index) in petData.features\" :key=\"index\">\n            <text class=\"feature-label\">{{item.label}}</text>\n            <text class=\"feature-value\">{{item.value}}</text>\n          </view>\n        </view>\n      </view>\n      \n      <!-- 健康状况 -->\n      <view class=\"content-card health-card\">\n        <view class=\"section-title\">健康状况</view>\n        <view class=\"health-list\">\n          <view class=\"health-item\" v-for=\"(item, index) in petData.health\" :key=\"index\">\n            <text class=\"health-label\">{{item.label}}</text>\n            <text class=\"health-value\">{{item.value}}</text>\n          </view>\n        </view>\n      </view>\n      \n      <!-- 宠物描述 -->\n      <view class=\"content-card description-card\">\n        <view class=\"section-title\">宠物描述</view>\n        <view class=\"description-content\">\n          <text class=\"description-text\">{{petData.description}}</text>\n        </view>\n      </view>\n      \n      <!-- 主人信息 -->\n      <view class=\"content-card owner-card\">\n        <view class=\"owner-header\">\n          <view class=\"owner-avatar\">\n            <image :src=\"petData.owner.avatar\" mode=\"aspectFill\"></image>\n          </view>\n          <view class=\"owner-info\">\n            <text class=\"owner-name\">{{petData.owner.name}}</text>\n            <view class=\"owner-meta\">\n              <text class=\"owner-type\">{{petData.owner.type}}</text>\n              <text class=\"owner-rating\">信用等级 {{petData.owner.rating}}</text>\n            </view>\n          </view>\n          <view class=\"owner-auth\" v-if=\"petData.owner.isVerified\">\n            <text class=\"iconfont icon-verified\"></text>\n            <text class=\"auth-text\">已认证</text>\n          </view>\n        </view>\n      </view>\n      \n      <!-- 联系方式 -->\n      <view class=\"content-card contact-card\">\n        <view class=\"contact-header\">\n          <text class=\"section-title\">联系方式</text>\n        </view>\n        <view class=\"contact-content\">\n          <view class=\"contact-item\">\n            <text class=\"contact-label\">联系人</text>\n            <text class=\"contact-value\">{{petData.contact.name}}</text>\n          </view>\n          <view class=\"contact-item\">\n            <text class=\"contact-label\">电话</text>\n            <text class=\"contact-value contact-phone\" @click=\"callPhone\">{{petData.contact.phone}}</text>\n          </view>\n          <view class=\"contact-tips\">\n            <text class=\"tips-icon iconfont icon-info\"></text>\n            <text class=\"tips-text\">请说明在\"磁州生活网\"看到的信息</text>\n          </view>\n        </view>\n      </view>\n      \n      <!-- 相关推荐模块 -->\n      <view class=\"content-card related-pets-card\">\n        <view class=\"section-title\">相关推荐</view>\n        <view class=\"related-pets-content\">\n          <view class=\"related-pets-list\">\n            <view class=\"related-pet-item\" v-for=\"(pet, index) in relatedPets.slice(0, 3)\" :key=\"index\" @click=\"navigateToPet(pet.id)\">\n              <view class=\"pet-item-content\">\n                <view class=\"pet-item-left\">\n                  <image class=\"pet-image\" :src=\"pet.image\" mode=\"aspectFill\"></image>\n                </view>\n                <view class=\"pet-item-middle\">\n                  <text class=\"pet-item-title\">{{pet.title}}</text>\n                  <view class=\"pet-item-meta\">{{pet.breed}} · {{pet.age}}</view>\n                  <view class=\"pet-item-tags\">\n                    <text class=\"pet-item-tag\" v-for=\"(tag, tagIndex) in pet.tags.slice(0, 2)\" :key=\"tagIndex\">{{tag}}</text>\n                    <text class=\"pet-item-tag-more\" v-if=\"pet.tags && pet.tags.length > 2\">+{{pet.tags.length - 2}}</text>\n                  </view>\n                </view>\n                <view class=\"pet-item-right\">\n                  <text class=\"pet-item-price\">{{pet.price}}</text>\n                </view>\n              </view>\n        </view>\n            <view class=\"empty-related-pets\" v-if=\"relatedPets.length === 0\">\n              <image src=\"/static/images/empty.png\" class=\"empty-image\" mode=\"aspectFit\"></image>\n              <text class=\"empty-text\">暂无相关推荐</text>\n            </view>\n          </view>\n          <view class=\"view-more-btn\" v-if=\"relatedPets.length > 0\" @click.stop=\"navigateToPetList\">\n            <text class=\"view-more-text\">查看更多宠物</text>\n            <text class=\"view-more-icon iconfont icon-right\"></text>\n          </view>\n        </view>\n      </view>\n    </view>\n    \n    <!-- 底部操作栏 -->\n    <view class=\"interaction-toolbar\">\n      <view class=\"toolbar-item\" @click=\"goToHome\">\n        <image src=\"/static/images/tabbar/a首页.png\" class=\"toolbar-icon\"></image>\n        <text class=\"toolbar-text\">首页</text>\n      </view>\n      <view class=\"toolbar-item\" @click=\"toggleCollect\">\n        <image src=\"/static/images/tabbar/a收藏.png\" class=\"toolbar-icon\"></image>\n        <text class=\"toolbar-text\">收藏</text>\n      </view>\n      <button class=\"share-button toolbar-item\" open-type=\"share\">\n        <image src=\"/static/images/tabbar/a分享.png\" class=\"toolbar-icon\"></image>\n        <text class=\"toolbar-text\">分享</text>\n      </button>\n      <view class=\"toolbar-item\" @click=\"openChat\">\n        <image src=\"/static/images/tabbar/a消息.png\" class=\"toolbar-icon\"></image>\n        <text class=\"toolbar-text\">私信</text>\n      </view>\n      <view class=\"toolbar-item call-button\" @click=\"callPhone\">\n        <view class=\"call-button-content\">\n          <text class=\"call-text\">打电话</text>\n          <text class=\"call-subtitle\">请说在磁州生活网看到的</text>\n        </view>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script setup>\nimport { ref, onMounted } from 'vue'\n\n// 状态栏高度\nconst statusBarHeight = ref(20);\n\n// 获取状态栏高度\nonMounted(() => {\n  try {\n    const sysInfo = uni.getSystemInfoSync();\n    statusBarHeight.value = sysInfo.statusBarHeight || 20;\n  } catch (e) {\n    console.error('获取状态栏高度失败', e);\n  }\n});\n\n// 返回上一页\nconst goBack = () => {\n  uni.navigateBack({\n    fail: () => {\n      uni.switchTab({\n        url: '/pages/index/index'\n      });\n    }\n  });\n};\n\n// 格式化时间\nconst formatTime = (timestamp) => {\n  const date = new Date(timestamp);\n  return `${date.getMonth() + 1}月${date.getDate()}日`;\n};\n\n// 响应式数据\nconst isCollected = ref(false);\nconst petData = ref({\n  id: 'pet12345',\n  title: '纯种金毛幼犬',\n  price: '2000元',\n  tags: ['纯种', '已打疫苗', '可上门看'],\n  publishTime: Date.now() - 86400000 * 2, // 2天前\n  images: [\n    '/static/images/pet1.jpg',\n    '/static/images/pet2.jpg',\n    '/static/images/pet3.jpg'\n  ],\n  breed: '金毛',\n  age: '3个月',\n  gender: '公',\n  vaccination: '已打两针',\n  features: [\n    { label: '毛色', value: '金黄色' },\n    { label: '体型', value: '中等' },\n    { label: '性格', value: '温顺' },\n    { label: '训练情况', value: '已学会基本指令' }\n  ],\n  health: [\n    { label: '驱虫情况', value: '已驱虫' },\n    { label: '疫苗情况', value: '已打两针' },\n    { label: '体检情况', value: '健康' },\n    { label: '其他情况', value: '无' }\n  ],\n  description: '纯种金毛幼犬，3个月大，已打两针疫苗，已驱虫，身体健康。性格温顺，已学会基本指令，适合家庭饲养。可上门看狗，价格可谈。',\n  owner: {\n    name: '李女士',\n    avatar: '/static/images/avatar.png',\n    type: '个人',\n    rating: 'A+',\n    isVerified: true\n  },\n  contact: {\n    name: '李女士',\n    phone: '13912345678'\n  }\n});\n\nconst relatedPets = ref([\n  {\n    id: 'pet001',\n    title: '纯种金毛幼犬',\n    price: '1800元',\n    breed: '金毛',\n    age: '2个月',\n    image: '/static/images/pet-similar1.jpg',\n    tags: ['纯种', '健康', '可上门看']\n  },\n  {\n    id: 'pet002',\n    title: '纯种金毛幼犬',\n    price: '2200元',\n    breed: '金毛',\n    age: '4个月',\n    image: '/static/images/pet-similar2.jpg',\n    tags: ['纯种', '已打疫苗']\n  },\n  {\n    id: 'pet003',\n    title: '拉布拉多幼犬',\n    price: '1600元',\n    breed: '拉布拉多',\n    age: '3个月',\n    image: '/static/images/pet-similar3.jpg',\n    tags: ['健康', '聪明']\n  }\n])\n\n// 方法\nconst toggleCollect = () => {\n  isCollected.value = !isCollected.value;\n  if (isCollected.value) {\n    uni.showToast({\n      title: '收藏成功',\n      icon: 'success'\n    });\n  }\n};\n\nconst showShareOptions = () => {\n  uni.showShareMenu({\n    withShareTicket: true,\n    menus: ['shareAppMessage', 'shareTimeline']\n  });\n};\n\nconst callPhone = () => {\n  uni.makePhoneCall({\n    phoneNumber: petData.value.contact.phone,\n    fail: () => {\n      uni.showToast({\n        title: '拨打电话失败',\n        icon: 'none'\n      });\n    }\n  });\n};\n\nconst navigateToPet = (id) => {\n  if (id === petData.value.id) return;\n  uni.navigateTo({ url: `/pages/publish/pet-detail?id=${id}` });\n}\n\nconst navigateToPetList = (e) => {\n  if (e) e.stopPropagation();\n  // 获取宠物品种作为分类参数\n  const petCategory = petData.value.breed || '';\n  uni.navigateTo({\n    url: `/subPackages/service/pages/filter?type=pet&title=${encodeURIComponent('宠物信息')}&category=${encodeURIComponent(petCategory)}&active=pet` \n  });\n}\n\n// 跳转到首页\nconst goToHome = () => {\n  uni.switchTab({\n    url: '/pages/index/index'\n  });\n};\n\n// 打开私信聊天\nconst openChat = () => {\n  if (!petData.value.owner || !petData.value.owner.id) {\n    uni.showToast({\n      title: '无法获取发布者信息',\n      icon: 'none'\n    });\n    return;\n  }\n  \n  // 跳转到聊天页面\n  uni.navigateTo({\n    url: `/pages/chat/chat?targetId=${petData.value.owner.id}&targetName=${encodeURIComponent(petData.value.owner.name)}&targetAvatar=${encodeURIComponent(petData.value.owner.avatar)}`\n  });\n};\n\n// 生命周期钩子\nonMounted(() => {\n  // 修改页面标题\n  uni.setNavigationBarTitle({\n    title: '宠物详情'\n  });\n});\n</script>\n\n<style>\n.pet-detail-container {\n  min-height: 100vh;\n  background-color: #f5f7fa;\n  padding-bottom: 150rpx;\n  padding-top: 0; /* 移除顶部内边距，由导航栏控制 */\n}\n\n.pet-detail-wrapper {\n  padding: 24rpx;\n  padding-top: 144px; /* 增加顶部内边距，确保内容不被导航栏遮挡 */\n}\n\n.content-card {\n  background-color: #fff;\n  border-radius: 16rpx;\n  margin-bottom: 24rpx;\n  padding: 30rpx;\n  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);\n}\n\n/* 宠物基本信息卡片 */\n.pet-title-row {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 16rpx;\n}\n\n.pet-title {\n  font-size: 36rpx;\n  font-weight: 600;\n  color: #333;\n}\n\n.pet-price {\n  font-size: 36rpx;\n  font-weight: 600;\n  color: #ff4d4f;\n}\n\n.pet-meta {\n  margin-bottom: 24rpx;\n}\n\n.pet-tag-group {\n  display: flex;\n  flex-wrap: wrap;\n  margin-bottom: 12rpx;\n}\n\n.pet-tag {\n  font-size: 24rpx;\n  color: #1890ff;\n  background-color: rgba(24, 144, 255, 0.1);\n  padding: 4rpx 16rpx;\n  border-radius: 6rpx;\n  margin-right: 16rpx;\n  margin-bottom: 12rpx;\n}\n\n.pet-publish-time {\n  font-size: 24rpx;\n  color: #999;\n}\n\n/* 轮播图 */\n.pet-swiper {\n  height: 400rpx;\n  border-radius: 12rpx;\n  overflow: hidden;\n  margin-bottom: 24rpx;\n}\n\n.pet-image {\n  width: 100%;\n  height: 100%;\n}\n\n/* 基本信息 */\n.pet-basic-info {\n  display: flex;\n  flex-wrap: wrap;\n  padding: 24rpx;\n  background-color: #f9fafc;\n  border-radius: 12rpx;\n}\n\n.info-item {\n  width: 50%;\n  padding: 12rpx 24rpx;\n  box-sizing: border-box;\n}\n\n.info-label {\n  font-size: 26rpx;\n  color: #999;\n  margin-bottom: 8rpx;\n  display: block;\n}\n\n.info-value {\n  font-size: 28rpx;\n  color: #333;\n  font-weight: 500;\n}\n\n/* 宠物特征 */\n.features-list {\n  display: flex;\n  flex-direction: column;\n}\n\n.feature-item {\n  display: flex;\n  padding: 16rpx 0;\n  border-bottom: 1rpx solid #f0f0f0;\n}\n\n.feature-item:last-child {\n  border-bottom: none;\n}\n\n.feature-label {\n  width: 160rpx;\n  font-size: 28rpx;\n  color: #999;\n}\n\n.feature-value {\n  flex: 1;\n  font-size: 28rpx;\n  color: #333;\n}\n\n/* 健康状况 */\n.health-list {\n  display: flex;\n  flex-direction: column;\n}\n\n.health-item {\n  display: flex;\n  padding: 16rpx 0;\n  border-bottom: 1rpx solid #f0f0f0;\n}\n\n.health-item:last-child {\n  border-bottom: none;\n}\n\n.health-label {\n  width: 160rpx;\n  font-size: 28rpx;\n  color: #999;\n}\n\n.health-value {\n  flex: 1;\n  font-size: 28rpx;\n  color: #333;\n}\n\n/* 宠物描述 */\n.description-content {\n  padding: 24rpx;\n  background-color: #f9fafc;\n  border-radius: 12rpx;\n}\n\n.description-text {\n  font-size: 28rpx;\n  color: #666;\n  line-height: 1.6;\n}\n\n/* 主人信息 */\n.owner-header {\n  display: flex;\n  align-items: center;\n}\n\n.owner-avatar {\n  width: 80rpx;\n  height: 80rpx;\n  border-radius: 50%;\n  overflow: hidden;\n  margin-right: 20rpx;\n}\n\n.owner-avatar image {\n  width: 100%;\n  height: 100%;\n}\n\n.owner-info {\n  flex: 1;\n}\n\n.owner-name {\n  font-size: 30rpx;\n  font-weight: 500;\n  color: #333;\n  margin-bottom: 8rpx;\n}\n\n.owner-meta {\n  display: flex;\n  align-items: center;\n}\n\n.owner-type, .owner-rating {\n  font-size: 24rpx;\n  color: #666;\n  margin-right: 16rpx;\n}\n\n/* 联系方式 */\n.contact-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 24rpx;\n}\n\n.section-title {\n  font-size: 36rpx;\n  font-weight: 600;\n  color: #333;\n  position: relative;\n  padding-left: 20rpx;\n}\n\n.section-title::before {\n  content: '';\n  position: absolute;\n  left: 0;\n  top: 50%;\n  transform: translateY(-50%);\n  width: 8rpx;\n  height: 32rpx;\n  background-color: #1890ff;\n  border-radius: 4rpx;\n}\n\n.contact-content {\n  display: flex;\n  flex-direction: column;\n}\n\n.contact-item {\n  display: flex;\n  padding: 12rpx 0;\n  border-bottom: 1rpx solid #f0f0f0;\n}\n\n.contact-item:last-child {\n  border-bottom: none;\n}\n\n.contact-label {\n  width: 160rpx;\n  font-size: 28rpx;\n  color: #999;\n}\n\n.contact-value {\n  flex: 1;\n  font-size: 28rpx;\n  color: #333;\n}\n\n.contact-phone {\n  color: #1890ff;\n}\n\n.contact-tips {\n  display: flex;\n  align-items: center;\n  margin-top: 12rpx;\n}\n\n.tips-icon {\n  margin-right: 8rpx;\n}\n\n.tips-text {\n  font-size: 24rpx;\n  color: #999;\n}\n\n/* 相关推荐模块 */\n.related-pets-card {\n  margin-top: 24rpx;\n}\n\n.related-pets-content {\n  padding: 24rpx;\n  background-color: #f9fafc;\n  border-radius: 12rpx;\n}\n\n.related-pets-list {\n  display: flex;\n  flex-direction: column;\n}\n\n.related-pet-item {\n  display: flex;\n  padding: 20rpx;\n  background-color: #fff;\n  border-radius: 12rpx;\n  margin-bottom: 16rpx;\n}\n\n.pet-item-content {\n  display: flex;\n  align-items: center;\n}\n\n.pet-item-left {\n  width: 200rpx;\n  height: 150rpx;\n  border-radius: 8rpx;\n  overflow: hidden;\n  margin-right: 20rpx;\n}\n\n.pet-item-middle {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  justify-content: space-between;\n}\n\n.pet-item-title {\n  font-size: 28rpx;\n  font-weight: 500;\n  color: #333;\n}\n\n.pet-item-meta {\n  font-size: 24rpx;\n  color: #999;\n}\n\n.pet-item-tags {\n  display: flex;\n  flex-wrap: wrap;\n}\n\n.pet-item-tag {\n  font-size: 24rpx;\n  color: #1890ff;\n  background-color: rgba(24, 144, 255, 0.1);\n  padding: 4rpx 16rpx;\n  border-radius: 6rpx;\n  margin-right: 16rpx;\n  margin-bottom: 12rpx;\n}\n\n.pet-item-tag-more {\n  font-size: 24rpx;\n  color: #ff4d4f;\n}\n\n.pet-item-right {\n  width: 120rpx;\n  text-align: right;\n}\n\n.pet-item-price {\n  font-size: 28rpx;\n  color: #ff4d4f;\n}\n\n.empty-related-pets {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  padding: 40rpx 0;\n}\n\n.empty-image {\n  width: 120rpx;\n  height: 120rpx;\n  margin-bottom: 20rpx;\n}\n\n.empty-text {\n  font-size: 28rpx;\n  color: #999;\n}\n\n.view-more-btn {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  padding: 12rpx 24rpx;\n  background-color: #fff;\n  border-radius: 12rpx;\n  margin-top: 24rpx;\n}\n\n.view-more-text {\n  font-size: 28rpx;\n  color: #1890ff;\n  margin-right: 8rpx;\n}\n\n.view-more-icon {\n  font-size: 24rpx;\n  color: #999;\n}\n\n/* 底部互动工具栏 */\n.interaction-toolbar {\n  position: fixed;\n  bottom: 0;\n  left: 0;\n  right: 0;\n  background-color: #fff;\n  padding: 10rpx 10rpx;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  border-top: 1rpx solid #f0f0f0;\n  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);\n  height: 120rpx;\n  z-index: 100;\n}\n\n.toolbar-item {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: 6rpx 0;\n  margin: 0 4rpx;\n}\n\n.toolbar-icon {\n  width: 44rpx;\n  height: 44rpx;\n  margin-bottom: 6rpx;\n}\n\n.toolbar-text {\n  font-size: 22rpx;\n  color: #666;\n}\n\n.share-button {\n  background: transparent;\n  border: none;\n  margin: 0;\n  padding: 0;\n  line-height: normal;\n  border-radius: 0;\n  flex: 1;\n}\n\n.share-button::after {\n  display: none;\n}\n\n.call-button {\n  flex: 3; /* 占据更多空间，原来是2，现在改为3让按钮更长 */\n  background: linear-gradient(135deg, #0052CC, #0066FF);\n  height: 90rpx;\n  margin: 0 0 0 10rpx;\n  border-radius: 45rpx;\n  box-shadow: 0 4rpx 12rpx rgba(0, 82, 204, 0.2);\n}\n\n.call-button-content {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  height: 100%;\n}\n\n.call-text {\n  color: #fff;\n  font-size: 30rpx;\n  font-weight: bold;\n  line-height: 1.2;\n}\n\n.call-subtitle {\n  color: rgba(255, 255, 255, 0.9);\n  font-size: 20rpx;\n  line-height: 1.2;\n}\n\n/* 隐藏原来的底部操作栏 */\n.action-bar {\n  display: none;\n}\n\n/* 隐藏的分享按钮 */\n.hidden-share-btn {\n  position: absolute;\n  top: -9999rpx;\n  left: -9999rpx;\n  width: 0;\n  height: 0;\n  padding: 0;\n  margin: 0;\n  opacity: 0;\n}\n\n/* 自定义导航栏 */\n.custom-navbar {\n  background: linear-gradient(135deg, #0066FF, #0052CC);\n  height: 88rpx;\n  padding-top: 44px; /* 状态栏高度 */\n  display: flex;\n  align-items: center;\n  position: fixed; /* 改为固定定位 */\n  top: 0;\n  left: 0;\n  right: 0;\n  padding-bottom: 10rpx;\n  box-shadow: 0 4rpx 12rpx rgba(0, 82, 204, 0.2);\n  z-index: 100; /* 提高z-index确保在最上层 */\n}\n\n.navbar-left {\n  width: 60px;\n  display: flex;\n  align-items: center;\n}\n\n.back-icon {\n  width: 20px;\n  height: 20px;\n}\n\n.navbar-title {\n  flex: 1;\n  text-align: center;\n  font-size: 17px;\n  font-weight: 500;\n  color: #fff;\n}\n\n.navbar-right {\n  width: 60px;\n  display: flex;\n  justify-content: flex-end;\n  align-items: center;\n}\n</style> ", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/pages/publish/pet-detail.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "onMounted", "uni", "MiniProgramPage"], "mappings": ";;;;;;AAuMA,UAAM,kBAAkBA,cAAAA,IAAI,EAAE;AAG9BC,kBAAAA,UAAU,MAAM;AACd,UAAI;AACF,cAAM,UAAUC,oBAAI;AACpB,wBAAgB,QAAQ,QAAQ,mBAAmB;AAAA,MACpD,SAAQ,GAAG;AACVA,kFAAc,aAAa,CAAC;AAAA,MAC7B;AAAA,IACH,CAAC;AAGD,UAAM,SAAS,MAAM;AACnBA,oBAAAA,MAAI,aAAa;AAAA,QACf,MAAM,MAAM;AACVA,wBAAAA,MAAI,UAAU;AAAA,YACZ,KAAK;AAAA,UACb,CAAO;AAAA,QACF;AAAA,MACL,CAAG;AAAA,IACH;AAGA,UAAM,aAAa,CAAC,cAAc;AAChC,YAAM,OAAO,IAAI,KAAK,SAAS;AAC/B,aAAO,GAAG,KAAK,aAAa,CAAC,IAAI,KAAK,SAAS;AAAA,IACjD;AAGA,UAAM,cAAcF,cAAAA,IAAI,KAAK;AAC7B,UAAM,UAAUA,cAAAA,IAAI;AAAA,MAClB,IAAI;AAAA,MACJ,OAAO;AAAA,MACP,OAAO;AAAA,MACP,MAAM,CAAC,MAAM,QAAQ,MAAM;AAAA,MAC3B,aAAa,KAAK,IAAK,IAAG,QAAW;AAAA;AAAA,MACrC,QAAQ;AAAA,QACN;AAAA,QACA;AAAA,QACA;AAAA,MACD;AAAA,MACD,OAAO;AAAA,MACP,KAAK;AAAA,MACL,QAAQ;AAAA,MACR,aAAa;AAAA,MACb,UAAU;AAAA,QACR,EAAE,OAAO,MAAM,OAAO,MAAO;AAAA,QAC7B,EAAE,OAAO,MAAM,OAAO,KAAM;AAAA,QAC5B,EAAE,OAAO,MAAM,OAAO,KAAM;AAAA,QAC5B,EAAE,OAAO,QAAQ,OAAO,UAAW;AAAA,MACpC;AAAA,MACD,QAAQ;AAAA,QACN,EAAE,OAAO,QAAQ,OAAO,MAAO;AAAA,QAC/B,EAAE,OAAO,QAAQ,OAAO,OAAQ;AAAA,QAChC,EAAE,OAAO,QAAQ,OAAO,KAAM;AAAA,QAC9B,EAAE,OAAO,QAAQ,OAAO,IAAK;AAAA,MAC9B;AAAA,MACD,aAAa;AAAA,MACb,OAAO;AAAA,QACL,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,YAAY;AAAA,MACb;AAAA,MACD,SAAS;AAAA,QACP,MAAM;AAAA,QACN,OAAO;AAAA,MACR;AAAA,IACH,CAAC;AAED,UAAM,cAAcA,cAAAA,IAAI;AAAA,MACtB;AAAA,QACE,IAAI;AAAA,QACJ,OAAO;AAAA,QACP,OAAO;AAAA,QACP,OAAO;AAAA,QACP,KAAK;AAAA,QACL,OAAO;AAAA,QACP,MAAM,CAAC,MAAM,MAAM,MAAM;AAAA,MAC1B;AAAA,MACD;AAAA,QACE,IAAI;AAAA,QACJ,OAAO;AAAA,QACP,OAAO;AAAA,QACP,OAAO;AAAA,QACP,KAAK;AAAA,QACL,OAAO;AAAA,QACP,MAAM,CAAC,MAAM,MAAM;AAAA,MACpB;AAAA,MACD;AAAA,QACE,IAAI;AAAA,QACJ,OAAO;AAAA,QACP,OAAO;AAAA,QACP,OAAO;AAAA,QACP,KAAK;AAAA,QACL,OAAO;AAAA,QACP,MAAM,CAAC,MAAM,IAAI;AAAA,MAClB;AAAA,IACH,CAAC;AAGD,UAAM,gBAAgB,MAAM;AAC1B,kBAAY,QAAQ,CAAC,YAAY;AACjC,UAAI,YAAY,OAAO;AACrBE,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AAAA,MACF;AAAA,IACH;AASA,UAAM,YAAY,MAAM;AACtBA,oBAAAA,MAAI,cAAc;AAAA,QAChB,aAAa,QAAQ,MAAM,QAAQ;AAAA,QACnC,MAAM,MAAM;AACVA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,UACd,CAAO;AAAA,QACF;AAAA,MACL,CAAG;AAAA,IACH;AAEA,UAAM,gBAAgB,CAAC,OAAO;AAC5B,UAAI,OAAO,QAAQ,MAAM;AAAI;AAC7BA,oBAAG,MAAC,WAAW,EAAE,KAAK,gCAAgC,EAAE,GAAE,CAAE;AAAA,IAC9D;AAEA,UAAM,oBAAoB,CAAC,MAAM;AAC/B,UAAI;AAAG,UAAE;AAET,YAAM,cAAc,QAAQ,MAAM,SAAS;AAC3CA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,oDAAoD,mBAAmB,MAAM,CAAC,aAAa,mBAAmB,WAAW,CAAC;AAAA,MACnI,CAAG;AAAA,IACH;AAGA,UAAM,WAAW,MAAM;AACrBA,oBAAAA,MAAI,UAAU;AAAA,QACZ,KAAK;AAAA,MACT,CAAG;AAAA,IACH;AAGA,UAAM,WAAW,MAAM;AACrB,UAAI,CAAC,QAAQ,MAAM,SAAS,CAAC,QAAQ,MAAM,MAAM,IAAI;AACnDA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AACD;AAAA,MACD;AAGDA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,6BAA6B,QAAQ,MAAM,MAAM,EAAE,eAAe,mBAAmB,QAAQ,MAAM,MAAM,IAAI,CAAC,iBAAiB,mBAAmB,QAAQ,MAAM,MAAM,MAAM,CAAC;AAAA,MACtL,CAAG;AAAA,IACH;AAGAD,kBAAAA,UAAU,MAAM;AAEdC,oBAAAA,MAAI,sBAAsB;AAAA,QACxB,OAAO;AAAA,MACX,CAAG;AAAA,IACH,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACrXD,GAAG,WAAWC,SAAe;"}