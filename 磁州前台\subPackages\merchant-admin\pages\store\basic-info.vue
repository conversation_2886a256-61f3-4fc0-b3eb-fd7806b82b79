<template>
  <view class="basic-info-container">
    <!-- 顶部导航栏 -->
    <view class="header-area">
      <!-- 顶部安全区域 -->
      <view class="safe-area-top"></view>
      
      <!-- 自定义导航栏 -->
      <view class="custom-navbar">
        <view class="navbar-left" @click="goBack">
          <image src="/static/images/tabbar/最新返回键.png" class="back-icon" style="filter: brightness(0) invert(1);"></image>
        </view>
        <view class="navbar-title">店铺信息</view>
        <view class="navbar-right">
          <!-- 占位元素保持导航栏平衡 -->
        </view>
      </view>
    </view>
    
    <!-- 背景装饰 -->
    <view class="bg-decoration bg-circle-1"></view>
    <view class="bg-decoration bg-circle-2"></view>
    <view class="bg-decoration bg-circle-3"></view>
    
    <!-- 内容区域 -->
    <scroll-view class="content-scroll" scroll-y>
      <form @submit="saveStoreInfo">
        <!-- 店铺Logo上传 -->
        <view class="form-section">
          <view class="section-header">
            <text class="section-title">店铺LOGO</text>
          </view>
          
          <view class="logo-upload-area">
            <view class="logo-preview">
              <image :src="formData.logo || '/static/images/default-logo.png'" mode="aspectFit" class="logo-image"></image>
              <view class="upload-mask" @click="uploadImage('logo')">
                <text class="upload-icon">+</text>
                <text class="upload-text">更换LOGO</text>
              </view>
            </view>
            <view class="upload-tips">
              <text>建议上传1:1比例正方形图片</text>
              <text>尺寸不小于300*300px，大小不超过2MB</text>
            </view>
          </view>
        </view>
        
        <!-- 封面图上传 -->
        <view class="form-section">
          <view class="section-header">
            <text class="section-title">店铺封面</text>
          </view>
          
          <view class="cover-upload-area">
            <view class="cover-preview">
              <image :src="formData.coverImage || '/static/images/default-cover.jpg'" mode="aspectFill" class="cover-image"></image>
              <view class="upload-mask" @click="uploadImage('cover')">
                <text class="upload-icon">+</text>
                <text class="upload-text">更换封面</text>
              </view>
            </view>
            <view class="upload-tips">
              <text>建议上传16:9比例横版图片</text>
              <text>尺寸不小于1200*675px，大小不超过5MB</text>
            </view>
          </view>
        </view>
        
        <!-- 店铺基本信息 -->
        <view class="form-section">
          <view class="section-header">
            <text class="section-title">基本信息</text>
          </view>
          
          <view class="form-item">
            <text class="form-label required">店铺名称</text>
            <input type="text" class="form-input" v-model="formData.name" placeholder="请输入店铺名称" maxlength="30" />
            <text class="input-counter">{{formData.name.length}}/30</text>
          </view>
          
          <view class="form-item">
            <text class="form-label required">店铺简介</text>
            <textarea class="form-textarea" v-model="formData.description" placeholder="请输入店铺简介，向顾客介绍您的店铺特色" maxlength="200" />
            <text class="input-counter">{{formData.description.length}}/200</text>
          </view>
          
          <view class="form-item">
            <text class="form-label required">联系电话</text>
            <input type="number" class="form-input" v-model="formData.phone" placeholder="请输入联系电话" maxlength="11" />
          </view>
          
          <view class="form-item">
            <text class="form-label">微信号</text>
            <input type="text" class="form-input" v-model="formData.wechat" placeholder="请输入微信号（选填）" />
          </view>
          
          <view class="form-item">
            <text class="form-label">电子邮箱</text>
            <input type="text" class="form-input" v-model="formData.email" placeholder="请输入电子邮箱（选填）" />
          </view>
        </view>
        
        <!-- 店铺分类 -->
        <view class="form-section">
          <view class="section-header">
            <text class="section-title">店铺分类</text>
          </view>
          
          <view class="form-item">
            <text class="form-label required">主营类目</text>
            <picker mode="selector" :range="categoryOptions" range-key="name" @change="onCategoryChange">
              <view class="picker-content">
                <text class="picker-text">{{formData.category ? formData.category : '请选择主营类目'}}</text>
                <text class="picker-arrow">›</text>
              </view>
            </picker>
          </view>
          
          <view class="form-item">
            <text class="form-label">店铺标签</text>
            <view class="tags-container">
              <view class="tag-item" v-for="(tag, index) in formData.tags" :key="index">
                <text class="tag-text">{{tag}}</text>
                <text class="tag-delete" @click="removeTag(index)">×</text>
              </view>
              <view class="tag-add" @click="showAddTagModal" v-if="formData.tags.length < 5">
                <text class="tag-add-icon">+</text>
                <text class="tag-add-text">添加标签</text>
              </view>
            </view>
            <text class="tags-tips">最多可添加5个标签，每个标签不超过6个字</text>
          </view>
        </view>
        
        <!-- 店铺公告 -->
        <view class="form-section">
          <view class="section-header">
            <text class="section-title">店铺公告</text>
          </view>
          
          <view class="form-item">
            <textarea class="form-textarea" v-model="formData.announcement" placeholder="请输入店铺公告，如优惠活动、新品上新等信息（选填）" maxlength="300" />
            <text class="input-counter">{{formData.announcement.length}}/300</text>
          </view>
        </view>
      </form>
      
      <!-- 底部安全区域 -->
      <view class="safe-area-bottom"></view>
    </scroll-view>
    
    <!-- 底部保存按钮 -->
    <view class="footer">
      <view class="safe-area-bottom-footer"></view>
      <view class="footer-content">
        <button class="save-btn" @click="saveStoreInfo">保存修改</button>
      </view>
    </view>
    
    <!-- 添加标签弹窗 -->
    <view class="modal-overlay" v-if="showTagModal" @click="hideAddTagModal"></view>
    <view class="tag-modal" v-if="showTagModal">
      <view class="modal-header">
        <text class="modal-title">添加店铺标签</text>
      </view>
      <view class="modal-content">
        <input type="text" class="tag-input" v-model="newTag" placeholder="请输入标签内容" maxlength="6" />
        <text class="tag-input-counter">{{newTag.length}}/6</text>
      </view>
      <view class="modal-footer">
        <button class="modal-btn cancel" @click="hideAddTagModal">取消</button>
        <button class="modal-btn confirm" @click="addTag" :disabled="!newTag.trim()">确认</button>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      formData: {
        logo: '/static/images/default-logo.png',
        coverImage: '/static/images/default-cover.jpg',
        name: '磁州小吃美食店',
        description: '专注本地特色小吃，用心制作每一道美食',
        phone: '13812345678',
        wechat: 'cizhouxiaochi',
        email: '<EMAIL>',
        category: '餐饮美食',
        tags: ['小吃', '特色美食', '磁州美食'],
        announcement: '即日起，本店推出新品"磁州小酥饼"，欢迎品尝！'
      },
      categoryOptions: [
        { id: 1, name: '餐饮美食' },
        { id: 2, name: '服装鞋包' },
        { id: 3, name: '生鲜果蔬' },
        { id: 4, name: '美容美发' },
        { id: 5, name: '教育培训' },
        { id: 6, name: '家居建材' },
        { id: 7, name: '生活服务' },
        { id: 8, name: '其他' }
      ],
      showTagModal: false,
      newTag: '',
      statusBarHeight: 20
    }
  },
  onLoad() {
    // 页面加载完成后的处理
    this.setStatusBarHeight();
  },
  methods: {
    // 设置状态栏高度
    setStatusBarHeight() {
      // 获取系统信息设置状态栏高度
      uni.getSystemInfo({
        success: (res) => {
          this.statusBarHeight = res.statusBarHeight;
          // 将状态栏高度设置为CSS变量
          if (typeof document !== 'undefined') {
            document.documentElement.style.setProperty('--status-bar-height', `${this.statusBarHeight}px`);
          }
        }
      });
    },
    
    // 返回上一页
    goBack() {
      uni.navigateBack();
    },
    
    // 上传图片
    uploadImage(type) {
      uni.chooseImage({
        count: 1,
        sizeType: ['original', 'compressed'],
        sourceType: ['album', 'camera'],
        success: (res) => {
          const tempFilePath = res.tempFilePaths[0];
          
          // 检查文件大小
          const maxSize = type === 'logo' ? 2 * 1024 * 1024 : 5 * 1024 * 1024; // 2MB或5MB
          
          uni.getFileInfo({
            filePath: tempFilePath,
            success: (fileInfo) => {
              if (fileInfo.size > maxSize) {
                const maxSizeMB = maxSize / 1024 / 1024;
                uni.showToast({
                  title: `文件大小不能超过${maxSizeMB}MB`,
                  icon: 'none'
                });
                return;
              }
              
              // 显示加载中
              uni.showLoading({
                title: '上传中...',
                mask: true
              });
              
              // 模拟上传过程
              setTimeout(() => {
                // 这里应该是实际的上传逻辑
                if (type === 'logo') {
                  this.formData.logo = tempFilePath;
                } else if (type === 'cover') {
                  this.formData.coverImage = tempFilePath;
                }
                
                uni.hideLoading();
                uni.showToast({
                  title: '上传成功',
                  icon: 'success'
                });
              }, 1000);
            }
          });
        }
      });
    },
    
    // 分类选择变更
    onCategoryChange(e) {
      const index = e.detail.value;
      this.formData.category = this.categoryOptions[index].name;
    },
    
    // 显示添加标签弹窗
    showAddTagModal() {
      this.showTagModal = true;
      this.newTag = '';
    },
    
    // 隐藏添加标签弹窗
    hideAddTagModal() {
      this.showTagModal = false;
    },
    
    // 添加标签
    addTag() {
      if (this.newTag.trim() && this.formData.tags.length < 5) {
        this.formData.tags.push(this.newTag.trim());
        this.hideAddTagModal();
      }
    },
    
    // 删除标签
    removeTag(index) {
      this.formData.tags.splice(index, 1);
    },
    
    // 保存店铺信息
    saveStoreInfo() {
      // 表单验证
      if (!this.formData.name.trim()) {
        uni.showToast({
          title: '请输入店铺名称',
          icon: 'none'
        });
        return;
      }
      
      if (!this.formData.description.trim()) {
        uni.showToast({
          title: '请输入店铺简介',
          icon: 'none'
        });
        return;
      }
      
      if (!this.formData.phone || !/^1\d{10}$/.test(this.formData.phone)) {
        uni.showToast({
          title: '请输入正确的手机号码',
          icon: 'none'
        });
        return;
      }
      
      if (!this.formData.category) {
        uni.showToast({
          title: '请选择主营类目',
          icon: 'none'
        });
        return;
      }
      
      // 显示加载中
      uni.showLoading({
        title: '保存中...',
        mask: true
      });
      
      // 模拟保存过程
      setTimeout(() => {
        // 这里应该是实际的保存逻辑
        uni.hideLoading();
        uni.showToast({
          title: '保存成功',
          icon: 'success',
          duration: 2000,
          success: () => {
            // 延迟返回上一页
            setTimeout(() => {
              this.goBack();
            }, 2000);
          }
        });
      }, 1500);
    }
  }
}
</script>

<style lang="scss">
.basic-info-container {
  min-height: 100vh;
  background-color: #F5F8FC;
  position: relative;
  padding-top: calc(110rpx + var(--status-bar-height, 20px));
  padding-bottom: 180rpx;
}

/* 顶部导航栏样式 */
.header-area {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  background-color: #0A84FF;
  color: #fff;
}

.safe-area-top {
  height: var(--status-bar-height, 20px);
  width: 100%;
}

.custom-navbar {
  height: 110rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 30rpx;
}

.navbar-title {
  font-size: 36rpx;
  font-weight: 600;
}

.navbar-left, .navbar-right {
  width: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-icon {
  width: 48rpx;
  height: 48rpx;
}

/* 背景装饰样式 */
.bg-decoration {
  position: absolute;
  z-index: -1;
  border-radius: 50%;
  opacity: 0.07;
  background-color: #0A84FF;
}

.bg-circle-1 {
  top: -100rpx;
  right: -150rpx;
  width: 400rpx;
  height: 400rpx;
}

.bg-circle-2 {
  top: 20%;
  left: -200rpx;
  width: 500rpx;
  height: 500rpx;
}

.bg-circle-3 {
  bottom: 10%;
  right: -100rpx;
  width: 300rpx;
  height: 300rpx;
}

/* 内容区域 */
.content-scroll {
  padding: 30rpx;
}

/* 表单区块样式 */
.form-section {
  background-color: #FFFFFF;
  border-radius: 24rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.section-header {
  margin-bottom: 30rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  position: relative;
  padding-left: 20rpx;
  display: inline-block;
}

.section-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 6rpx;
  width: 8rpx;
  height: 32rpx;
  background-color: #0A84FF;
  border-radius: 4rpx;
}

/* Logo上传区域 */
.logo-upload-area {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.logo-preview {
  width: 180rpx;
  height: 180rpx;
  border-radius: 20rpx;
  position: relative;
  overflow: hidden;
  background-color: #f0f0f0;
}

.logo-image {
  width: 100%;
  height: 100%;
}

.upload-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.4);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #FFFFFF;
  opacity: 0;
  transition: opacity 0.3s;
}

.logo-preview:active .upload-mask,
.cover-preview:active .upload-mask {
  opacity: 1;
}

.upload-icon {
  font-size: 48rpx;
  margin-bottom: 10rpx;
}

.upload-text {
  font-size: 26rpx;
}

.upload-tips {
  margin-left: 30rpx;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.upload-tips text {
  font-size: 24rpx;
  color: #999;
  line-height: 1.6;
}

/* 封面图上传区域 */
.cover-upload-area {
  margin-bottom: 20rpx;
}

.cover-preview {
  width: 100%;
  height: 400rpx;
  border-radius: 20rpx;
  position: relative;
  overflow: hidden;
  background-color: #f0f0f0;
  margin-bottom: 20rpx;
}

.cover-image {
  width: 100%;
  height: 100%;
}

/* 表单项样式 */
.form-item {
  margin-bottom: 30rpx;
  position: relative;
}

.form-label {
  display: block;
  font-size: 28rpx;
  color: #333;
  margin-bottom: 16rpx;
}

.required::after {
  content: '*';
  color: #FF3B30;
  margin-left: 6rpx;
}

.form-input,
.form-textarea,
.picker-content {
  width: 100%;
  background-color: #F5F8FC;
  border-radius: 12rpx;
  padding: 24rpx;
  font-size: 28rpx;
  color: #333;
  box-sizing: border-box;
  border: 2rpx solid transparent;
}

.form-input:focus,
.form-textarea:focus {
  border-color: #0A84FF;
}

.form-textarea {
  height: 200rpx;
  line-height: 1.5;
}

.input-counter {
  position: absolute;
  right: 24rpx;
  bottom: 24rpx;
  font-size: 24rpx;
  color: #999;
}

/* Picker样式 */
.picker-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;
  min-height: 88rpx;
}

.picker-text {
  color: #333;
}

.picker-arrow {
  font-size: 40rpx;
  color: #999;
  transform: rotate(90deg);
}

/* 标签样式 */
.tags-container {
  display: flex;
  flex-wrap: wrap;
  margin-top: 20rpx;
  margin-bottom: 20rpx;
}

.tag-item, .tag-add {
  display: flex;
  align-items: center;
  background-color: #F5F8FC;
  padding: 12rpx 20rpx;
  border-radius: 30rpx;
  margin-right: 20rpx;
  margin-bottom: 20rpx;
}

.tag-item {
  background-color: rgba(10, 132, 255, 0.1);
}

.tag-text {
  font-size: 26rpx;
  color: #0A84FF;
}

.tag-delete {
  margin-left: 10rpx;
  font-size: 28rpx;
  color: #0A84FF;
}

.tag-add {
  background-color: #FFFFFF;
  border: 2rpx dashed #CCCCCC;
  padding: 10rpx 20rpx;
}

.tag-add-icon {
  font-size: 28rpx;
  color: #999;
  margin-right: 8rpx;
}

.tag-add-text {
  font-size: 26rpx;
  color: #999;
}

.tags-tips {
  font-size: 24rpx;
  color: #999;
}

/* 底部按钮区域 */
.footer {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #FFFFFF;
  padding: 20rpx 30rpx;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  z-index: 99;
}

.safe-area-bottom-footer {
  height: constant(safe-area-inset-bottom);
  height: env(safe-area-inset-bottom);
}

.footer-content {
  display: flex;
  justify-content: center;
}

.save-btn {
  width: 90%;
  height: 88rpx;
  background: linear-gradient(135deg, #0A84FF, #0055FF);
  color: #FFFFFF;
  font-size: 32rpx;
  font-weight: 500;
  border-radius: 44rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 6rpx 16rpx rgba(10, 132, 255, 0.3);
}

.save-btn:active {
  transform: scale(0.98);
  box-shadow: 0 4rpx 8rpx rgba(10, 132, 255, 0.3);
}

/* 底部安全区域 */
.safe-area-bottom {
  height: 100rpx;
}

/* 添加标签弹窗 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  z-index: 999;
}

.tag-modal {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 80%;
  background-color: #FFFFFF;
  border-radius: 24rpx;
  overflow: hidden;
  z-index: 1000;
  box-shadow: 0 20rpx 40rpx rgba(0, 0, 0, 0.2);
}

.modal-header {
  padding: 30rpx;
  text-align: center;
  border-bottom: 2rpx solid #F2F2F7;
}

.modal-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.modal-content {
  padding: 30rpx;
  position: relative;
}

.tag-input {
  width: 100%;
  height: 88rpx;
  background-color: #F5F8FC;
  border-radius: 12rpx;
  padding: 0 24rpx;
  font-size: 28rpx;
  color: #333;
}

.tag-input-counter {
  position: absolute;
  right: 54rpx;
  bottom: 42rpx;
  font-size: 24rpx;
  color: #999;
}

.modal-footer {
  display: flex;
  border-top: 2rpx solid #F2F2F7;
}

.modal-btn {
  flex: 1;
  height: 100rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  background-color: transparent;
}

.modal-btn::after {
  border: none;
}

.cancel {
  color: #999;
  border-right: 1px solid #F2F2F7;
}

.confirm {
  color: #0A84FF;
  font-weight: 500;
}

/* 适配暗黑模式 */
@media (prefers-color-scheme: dark) {
  .basic-info-container {
    background-color: #1C1C1E;
  }
  
  .form-section {
    background-color: #2C2C2E;
  }
  
  .section-title {
    color: #FFFFFF;
  }
  
  .form-label,
  .form-input,
  .form-textarea,
  .picker-text {
    color: #FFFFFF;
  }
  
  .form-input,
  .form-textarea,
  .picker-content {
    background-color: #3A3A3C;
  }
  
  .tag-modal {
    background-color: #2C2C2E;
  }
  
  .modal-title {
    color: #FFFFFF;
  }
  
  .modal-header,
  .modal-footer {
    border-color: #3A3A3C;
  }
  
  .tag-input {
    background-color: #3A3A3C;
    color: #FFFFFF;
  }
  
  .footer {
    background-color: #2C2C2E;
  }
}
</style> 