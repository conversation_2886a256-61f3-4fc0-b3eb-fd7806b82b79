<template>
  <view class="publish-container">
    <!-- 顶部导航栏（同步发布信息页面风格） -->
    <view class="custom-navbar" :style="`padding-top: ${statusBarHeight}px; height: ${statusBarHeight + 88}rpx;`">
      <view class="navbar-left" @click="goBack">
        <image class="back-icon" src="/static/images/tabbar/最新返回键.png"></image>
      </view>
      <view class="navbar-title">我的发布</view>
      <view class="navbar-right">
        <!-- 右侧按钮可自定义 -->
      </view>
    </view>
    
    <!-- 状态标签栏 -->
    <view class="tab-container">
      <view 
        v-for="(tab, index) in tabs" 
        :key="index" 
        class="tab-item" 
        :class="{ active: currentTab === index }"
        @click="switchTab(index)"
      >
        <text class="tab-text">{{ tab }}</text>
        <view v-if="currentTab === index" class="active-line"></view>
      </view>
    </view>
    
    <!-- 信息列表 -->
    <view class="list-container">
      <view 
        class="info-card" 
        v-for="(item, index) in filteredList" 
        :key="index"
        @click="goToDetail(item)"
      >
        <!-- 信息标题和状态 -->
        <view class="info-header">
          <text class="info-title">{{ item.title }}</text>
          <text class="info-status" :class="getStatusClass(item.status)">{{ item.statusText }}</text>
        </view>
        <!-- 信息内容 -->
        <view class="info-content">{{ item.content }}</view>
        <!-- 信息时间和统计 -->
        <view class="info-stats">
          <text class="info-date">{{ item.date }}</text>
          <text class="info-views">浏览 {{ item.views }}</text>
          <text class="info-likes">点赞 {{ item.likes }}</text>
        </view>
        <!-- 操作按钮，阻止冒泡 -->
        <view class="info-actions" @click.stop>
          <button class="activity-btn" data-type="edit" @click="handleEdit(item)">编辑</button>
          <button class="activity-btn" data-type="promote" @click="promotePublish(item)">推广</button>
          <button class="activity-btn" data-type="share" open-type="share" :data-item="JSON.stringify(item)">转发</button>
        </view>
      </view>
      
      <!-- 无数据提示 -->
      <view class="empty-container" v-if="filteredList.length === 0">
        <image class="empty-icon" src="/static/images/empty-data.png"></image>
        <text class="empty-text">暂无数据</text>
      </view>
    </view>
    
    <!-- 置顶卡片弹窗 -->
    <view class="top-overlay" v-if="topCardVisible" @click="hideTopCard">
      <view class="top-card" @click.stop>
        <view class="top-title">{{ promotionTypeText }}，提升10倍曝光率</view>
        <view class="top-block ad-block" @click="showAdTips">
          <view class="block-left">
            <view class="block-main">看广告{{ promotionTypeText }}1天</view>
            <view class="block-sub">免费特权</view>
          </view>
          <view class="block-right">
            <view class="free-btn">免费</view>
            <view class="arrow">&gt;</view>
          </view>
        </view>
        <view class="top-block pay-block" @click="showTopOptions">
          <view class="block-left">
            <view class="block-main">选择{{ promotionTypeText }}时长</view>
            <view class="block-sub">3天/1周/1月任选</view>
          </view>
          <view class="block-right">
            <view class="pay-btn">付费</view>
            <view class="arrow">&gt;</view>
          </view>
        </view>
        
        <!-- 置顶时长选择 -->
        <view class="top-options-dropdown" v-if="topOptionsVisible">
          <view class="top-options-title">选择{{ promotionTypeText }}时长</view>
          <view class="top-options-list">
            <view 
              class="top-option-item" 
              :class="{'active': selectedOption === 0}" 
              @click="selectOption(0)"
            >
              <view class="top-option-days">3天</view>
              <view class="top-option-price">¥2.8</view>
            </view>
            <view 
              class="top-option-item" 
              :class="{'active': selectedOption === 1}" 
              @click="selectOption(1)"
            >
              <view class="top-option-days">1周</view>
              <view class="top-option-price">¥5.8</view>
            </view>
            <view 
              class="top-option-item" 
              :class="{'active': selectedOption === 2}" 
              @click="selectOption(2)"
            >
              <view class="top-option-days">1个月</view>
              <view class="top-option-price">¥19.8</view>
            </view>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 广告提示弹窗 -->
    <view class="ad-tips-overlay" v-if="adTipsVisible" @click="hideAdTips">
      <view class="ad-tips-card" @click.stop>
        <view class="ad-tips-title">广告须知</view>
        <view class="ad-tips-content">
          <view class="ad-tips-item">1. 完整观看广告可免费获得1天置顶</view>
          <view class="ad-tips-item">2. 广告必须观看<text class="highlight">10秒以上</text>才能生效</view>
          <view class="ad-tips-item">3. 提前关闭广告将无法获得置顶特权</view>
        </view>
        <view class="ad-tips-btns">
          <view class="ad-tips-btn cancel" @click="hideAdTips">取消</view>
          <view class="ad-tips-btn confirm" @click="startWatchAd">观看广告</view>
        </view>
      </view>
    </view>
    
    <!-- 广告播放弹窗 -->
    <view class="ad-overlay" v-if="adWatchingVisible">
      <view class="ad-container">
        <view class="ad-header">
          <text class="ad-title">广告</text>
          <text class="ad-countdown">{{ adCountdown }}秒</text>
        </view>
        <view class="ad-content">
          <image class="ad-image" src="/static/images/banner/banner-2.png" mode="aspectFill"></image>
          <view class="ad-message">广告内容展示区域</view>
        </view>
        <view class="ad-footer">
          <view class="ad-tips">观看完整广告获得1天置顶特权</view>
          <view 
            class="ad-close-btn" 
            :class="{'ad-close-active': adCountdown <= adMinTime}" 
            @click="closeAd"
          >
            {{ adCountdown <= adMinTime ? '关闭广告' : `请观看${adMinTime}秒 (${adCountdown})` }}
          </view>
        </view>
      </view>
    </view>
    
    <!-- 置顶结果提示 -->
    <view class="top-result" v-if="topResultVisible">
      <view class="top-result-content">
        <image class="top-result-icon" :src="topSuccess ? '/static/images/pay/success.png' : '/static/images/pay/fail.png'"></image>
        <view class="top-result-text">{{ topResultText }}</view>
      </view>
    </view>
    
    <!-- 自定义弹窗组件 -->
    <view class="custom-modal" v-if="customModal && customModal.show">
      <view class="modal-mask" @click="closeCustomModal"></view>
      <view class="modal-content">
        <view class="modal-title">{{customModal.title}}</view>
        <view class="modal-body">
          <view class="modal-text">刷新需要支付费用，刷新后{{customModal.type}}将获得更多曝光</view>
          <view class="price-text">本次刷新需支付<text class="price-amount">2元</text></view>
          <view class="rank-hint">
            <image class="rank-icon" src="/static/images/tabbar/置顶.png" mode="aspectFit"></image>
            <text>刷新后将在所有付费置顶中排名第一位</text>
          </view>
          <view class="special-hint">购买刷新套餐更划算！</view>
        </view>
        <view class="modal-footer">
          <button class="modal-btn cancel-btn" @click="handleCustomModalCancel">{{customModal.cancelText || '购买套餐'}}</button>
          <button class="modal-btn confirm-btn" @click="handleCustomModalConfirm">{{customModal.buttonText || '立即刷新'}}</button>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  enableShareAppMessage: true,
  enableShareTimeline: true,
  data() {
    return {
      statusBarHeight: 0,
      tabs: ['全部', '审核中', '已发布', '已过期'],
      currentTab: 0,
      infoList: [
        {
          id: 1,
          title: '招聘服务员',
          content: '本店诚聘服务员2名，要求形象好，有经验者优先，薪资面议',
          date: '2024-01-15',
          views: 156,
          likes: 23,
          status: 'passed', // 审核通过
          statusText: '审核通过'
        },
        {
          id: 2,
          title: '出售二手电动车',
          content: '9成新电动车转让，行驶3000公里，价格实惠',
          date: '2024-01-14',
          views: 89,
          likes: 12,
          status: 'reviewing', // 审核中
          statusText: '审核中'
        },
        {
          id: 3,
          title: '求租门面房',
          content: '求租商业街店铺，面积50-100平米，价格合适即可',
          date: '2024-01-13',
          views: 245,
          likes: 35,
          status: 'passed', // 审核通过
          statusText: '审核通过'
        }
      ],
      topCardVisible: false,
      currentItem: null,
      topOptionsVisible: false,
      selectedOption: 0,
      topOptions: [
        { days: 3, price: 2.8 },
        { days: 7, price: 5.8 },
        { days: 30, price: 19.8 }
      ],
      adTipsVisible: false,
      adWatchingVisible: false,
      adCountdown: 30,
      adMinTime: 10,
      topResultVisible: false,
      topSuccess: false,
      topResultText: '',
      userRefreshCount: 2, // 用户剩余刷新次数
      customModal: null, // 添加自定义弹窗数据
      promotionType: 'top', // 'top', 'refresh', 'renew'
      promotionTypeText: '置顶', // 对应的中文文本
    };
  },
  
  onLoad() {
    const sysInfo = uni.getSystemInfoSync();
    this.statusBarHeight = sysInfo.statusBarHeight;
  },
  
  computed: {
    filteredList() {
      if (this.currentTab === 0) {
        return this.infoList;
      } else if (this.currentTab === 1) {
        return this.infoList.filter(item => item.status === 'reviewing');
      } else if (this.currentTab === 2) {
        return this.infoList.filter(item => item.status === 'passed');
      } else if (this.currentTab === 3) {
        return this.infoList.filter(item => item.status === 'expired');
      }
      return [];
    }
  },
  
  methods: {
    goBack() {
      console.log('返回按钮被点击');
      uni.navigateBack({
        fail: function() {
          uni.switchTab({
            url: '/pages/index/index'
          });
        }
      });
    },
    
    switchTab(index) {
      this.currentTab = index;
    },
    
    getStatusClass(status) {
      switch (status) {
        case 'reviewing':
          return 'status-reviewing';
        case 'passed':
          return 'status-passed';
        case 'expired':
          return 'status-expired';
        default:
          return '';
      }
    },
    
    handleTop(item) {
      this.currentItem = item;
      this.topCardVisible = true;
    },
    
    hideTopCard() {
      this.topCardVisible = false;
      this.topOptionsVisible = false;
    },
    
    showTopOptions() {
      this.topOptionsVisible = true;
    },
    
    hideTopOptions() {
      this.topOptionsVisible = false;
    },
    
    selectOption(index) {
      this.selectedOption = index;
      const option = this.topOptions[index];
      const orderInfo = {
        orderType: this.promotionType, 
        days: option.days,
        amount: option.price,
        infoId: this.currentItem.id,
        userId: uni.getStorageSync('userId')
      };
      
      uni.navigateTo({
        url: `/pages/pay/index?orderInfo=${encodeURIComponent(JSON.stringify(orderInfo))}`
      });
      
      this.hideTopCard();
    },
    
    showAdTips() {
      this.adTipsVisible = true;
    },
    
    hideAdTips() {
      this.adTipsVisible = false;
    },
    
    startWatchAd() {
      this.adTipsVisible = false;
      this.adWatchingVisible = true;
      this.adCountdown = 30;
      
      this.adTimer = setInterval(() => {
        this.adCountdown--;
        if (this.adCountdown <= 0) {
          clearInterval(this.adTimer);
          this.handleAdComplete(true);
        }
      }, 1000);
    },
    
    closeAd() {
      if (this.adCountdown <= 30 - this.adMinTime) {
        clearInterval(this.adTimer);
        this.adTimer = null;
        this.handleAdComplete(true);
      } else {
        uni.showToast({
          title: `请观看至少${this.adMinTime}秒广告`,
          icon: 'none',
          duration: 1500
        });
      }
    },
    
    handleAdComplete(success) {
      this.adWatchingVisible = false;
      
      if (success) {
        try {
          uni.showLoading({ title: '处理中...' });
          
          const infoId = this.currentItem.id;
          
          setTimeout(() => {
            uni.hideLoading();
            
            this.topResultVisible = true;
            this.topSuccess = true;
            this.topResultText = `${this.promotionTypeText}成功！信息已${this.promotionTypeText}1天`;
            
            setTimeout(() => {
              this.topResultVisible = false;
              this.hideTopCard();
            }, 2000);
          }, 1000);
          
        } catch (error) {
          uni.hideLoading();
          console.error('免费操作请求失败', error);
          uni.showToast({
            title: '网络错误，请稍后再试',
            icon: 'none'
          });
        }
      } else {
        this.topResultVisible = true;
        this.topSuccess = false;
        this.topResultText = `${this.promotionTypeText}失败！请观看至少10秒广告`;
        setTimeout(() => {
          this.topResultVisible = false;
        }, 2000);
      }
    },
    
    handleRefresh(item) {
      // 检查用户是否有剩余的刷新次数
      if (this.userRefreshCount <= 0) {
        // 没有剩余刷新次数，直接显示付费刷新选项
        this.showDirectPayRefresh(item);
        return;
      }
      
      // 有剩余刷新次数，询问是否使用
      uni.showModal({
        title: '刷新发布',
        content: `您当前有${this.userRefreshCount}次刷新机会，是否使用1次刷新该发布？`,
        confirmText: '确认使用',
        cancelText: '购买套餐',
        success: (res) => {
          if (res.confirm) {
            // 使用一次刷新次数
            this.userRefreshCount -= 1;
            
            uni.showLoading({ title: '刷新中...' });
            
            // 模拟刷新过程
      setTimeout(() => {
        uni.hideLoading();
        uni.showToast({
          title: '刷新成功',
          icon: 'success'
        });
      }, 1000);
          } else {
            // 用户选择购买套餐
            uni.navigateTo({
              url: '/pages/services/refresh-package'
            });
          }
        }
      });
    },
    
    // 修改showDirectPayRefresh方法
    showDirectPayRefresh(item) {
      this.customModal = {
        show: true,
        title: '付费刷新',
        type: '发布',
        cancelText: '购买套餐',
        buttonText: '立即刷新',
        item: item
      };
    },
    
    // 关闭自定义弹窗
    closeCustomModal() {
      this.customModal = null;
    },
    
    // 处理自定义弹窗取消
    handleCustomModalCancel() {
      this.closeCustomModal();
      uni.navigateTo({
        url: '/pages/services/refresh-package'
      });
    },
    
    // 处理自定义弹窗确认
    handleCustomModalConfirm() {
      this.closeCustomModal();
      
      // 用户选择立即付费刷新
      uni.showLoading({ title: '支付中...' });
      
      // 模拟支付流程
      setTimeout(() => {
        uni.hideLoading();
        uni.showToast({
          title: '刷新成功',
          icon: 'success'
        });
      }, 1500);
    },
    
    handleEdit(item) {
      uni.showActionSheet({
        itemList: ['修改内容', '删除内容', item.status === 'passed' ? '下架内容' : '上架内容'],
        success: (res) => {
          switch (res.tapIndex) {
            case 0: // 修改内容
              this.modifyPublish(item);
              break;
            case 1: // 删除内容
              this.confirmDelete(item);
              break;
            case 2: // 上架/下架内容
              this.togglePublishStatus(item);
              break;
          }
        }
      });
    },
    
    modifyPublish(item) {
      // 直接导航到详情页
      uni.navigateTo({
        url: `/pages/publish/detail?id=${item.id}&edit=1`
      });
    },
    
    togglePublishStatus(item) {
      const action = item.status === 'passed' ? '下架' : '上架';
      uni.showModal({
        title: `${action}内容`,
        content: `确定要${action}该发布内容吗？`,
        success: (res) => {
          if (res.confirm) {
            uni.showLoading({ title: '处理中...' });
            
            // 模拟操作
            setTimeout(() => {
              // 更新状态
              if (item.status === 'passed') {
                item.status = 'expired';
                item.statusText = '已下架';
              } else {
                item.status = 'passed';
                item.statusText = '审核通过';
              }
              
              uni.hideLoading();
              uni.showToast({
                title: `${action}成功`,
                icon: 'success'
              });
            }, 1000);
          }
        }
      });
    },
    
    promotePublish(item) {
      this.currentItem = item;
      uni.showActionSheet({
        itemList: ['置顶信息', '刷新信息', '续费信息'],
        success: (res) => {
          switch (res.tapIndex) {
            case 0: // 置顶
              this.promotionType = 'top';
              this.promotionTypeText = '置顶';
              this.topCardVisible = true;
              break;
            case 1: // 刷新
              uni.navigateTo({
                url: `/pages/services/refresh-info?id=${item.id}`
              });
              break;
            case 2: // 续费
              uni.navigateTo({
                url: `/pages/services/renew-info?id=${item.id}`
              });
              break;
          }
        }
      });
    },
    
    handleShare(item) {
      uni.showShareMenu({
        withShareTicket: true,
        menus: ['shareAppMessage', 'shareTimeline']
      });
    },
    
    handleMore(item) {
      this.showMoreActions(item);
    },
    
    showMoreActions(item) {
      uni.showActionSheet({
        itemList: ['编辑信息', '删除信息', '举报', '复制链接', '收藏', '取消置顶'],
        success: (res) => {
          switch(res.tapIndex) {
            case 0:
              this.handleEdit(item);
              break;
            case 1:
              this.confirmDelete(item);
              break;
            case 2:
              uni.showToast({
                title: '举报成功',
                icon: 'success'
              });
              break;
            case 3:
              uni.setClipboardData({
                data: `http://example.com/info/${item.id}`,
                success: () => {
                  uni.showToast({
                    title: '链接已复制',
                    icon: 'success'
                  });
                }
              });
              break;
            case 4:
              uni.showToast({
                title: '收藏成功',
                icon: 'success'
              });
              break;
            case 5:
              uni.showToast({
                title: '已取消置顶',
                icon: 'success'
              });
              break;
          }
        }
      });
    },
    
    confirmDelete(item) {
      uni.showModal({
        title: '提示',
        content: '确定要删除该信息吗？',
        success: (res) => {
          if (res.confirm) {
            this.infoList = this.infoList.filter(info => info.id !== item.id);
            uni.showToast({
              title: '删除成功',
              icon: 'success'
            });
          }
        }
      });
    },
    
    onShareAppMessage(res) {
      // 来自页面内的转发按钮
      if (res.from === 'button') {
        const item = res.target.dataset.item;
        if (item) {
          return {
            title: item.title || '我的发布内容分享',
            path: `/pages/publish/detail?id=${item.id}`,
            imageUrl: item.image || '/static/images/default-activity.png'
          };
        }
      }
      
      // 默认分享
      return {
        title: '我的发布',
        path: '/pages/my/publish',
        imageUrl: '/static/images/default-share.png'
      };
    },
    
    goToDetail(item) {
      uni.navigateTo({
        url: `/pages/publish/detail?id=${item.id}`
      });
    },
  }
};
</script>

<style scoped>
.publish-container {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.custom-navbar {
  background: linear-gradient(135deg, #0066FF, #0052CC);
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  padding-bottom: 10rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 82, 204, 0.2);
  z-index: 10;
}

.navbar-title {
  color: #FFFFFF;
  font-size: 36rpx;
  font-weight: 600;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
  margin: 0 auto;
  z-index: 1;
}

.navbar-left, .navbar-right {
  position: absolute;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2;
}

.navbar-left {
  left: 20rpx;
  width: 60rpx;
}

.navbar-right {
  right: 20rpx;
  width: 60rpx;
}

.back-icon {
  width: 40rpx;
  height: 40rpx;
  background: none;
}

.tab-container {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  background: #fff;
  padding: 0 20rpx;
  border-bottom: 1rpx solid #eee;
}

.tab-item {
  position: relative;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 25%;
  box-sizing: border-box;
}

.tab-text {
  font-size: 30rpx;
  color: #666;
}

.tab-item.active .tab-text {
  color: #0052d9;
  font-weight: bold;
}

.active-line {
  position: absolute;
  bottom: 0;
  width: 60rpx;
  height: 6rpx;
  background: #0052d9;
  border-radius: 3rpx;
}

.list-container {
  padding: 20rpx;
}

.info-card {
  background: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.info-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20rpx;
}

.info-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.info-status {
  font-size: 24rpx;
  padding: 6rpx 12rpx;
  border-radius: 6rpx;
}

.status-reviewing {
  background: #FFF8E6;
  color: #FF9900;
}

.status-passed {
  background: #E6F7E6;
  color: #52C41A;
}

.status-expired {
  background: #F0F0F0;
  color: #999;
}

.info-content {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
  margin-bottom: 20rpx;
}

.info-stats {
  display: flex;
  color: #999;
  font-size: 24rpx;
  margin-bottom: 30rpx;
}

.info-date {
  margin-right: 30rpx;
}

.info-views {
  margin-right: 30rpx;
}

.info-actions {
  display: flex;
  gap: 10px;
  justify-content: flex-end;
  margin-top: 8px;
}

.activity-btn {
  height: 28px;
  padding: 0 12px;
  border-radius: 14px;
  font-size: 12px;
  font-weight: 600;
  position: relative;
  overflow: hidden;
  box-shadow: 0 1px 5px rgba(0, 0, 0, 0.08);
  border: 0.5px solid rgba(255, 255, 255, 0.5);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  background: linear-gradient(135deg, #ECF5FF 0%, #E2F0FF 100%);
  color: #007AFF;
  display: flex;
  align-items: center;
  justify-content: center;
}

.activity-btn::before {
  display: none;
}

.activity-btn::after {
  display: none;
}

.activity-btn:active {
  transform: translateY(1px) scale(0.98);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  background: linear-gradient(135deg, #E2F0FF 0%, #D8EAFF 100%);
}

.activity-btn[data-type="edit"] {
  background: linear-gradient(135deg, #EEF6FF 0%, #E2F0FF 100%);
  color: #0070E0;
}

.activity-btn[data-type="promote"] {
  background: linear-gradient(135deg, #FFF2E6 0%, #FFE8D1 100%);
  color: #FF7D00;
}

.activity-btn[data-type="share"] {
  background: linear-gradient(135deg, #E9F9F0 0%, #DCF2E5 100%);
  color: #27AE60;
}

.empty-container {
  padding: 100rpx 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.empty-icon {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 30rpx;
}

.empty-text {
  font-size: 30rpx;
  color: #999;
}

/* 置顶卡片样式 */
.top-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 999;
}

.top-card {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  width: 85%;
  max-width: 650rpx;
}

.top-title {
  font-size: 34rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 30rpx;
  text-align: center;
}

.top-block {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx 20rpx;
  margin-bottom: 20rpx;
  border-radius: 12rpx;
  border: 1rpx solid #f0f0f0;
}

.ad-block {
  background-color: #F8FFF9;
}

.pay-block {
  background-color: #FFF9F0;
}

.block-left {
  flex: 1;
}

.block-main {
  font-size: 30rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 6rpx;
}

.block-sub {
  font-size: 24rpx;
  color: #999;
}

.block-right {
  display: flex;
  align-items: center;
}

.arrow {
  margin-left: 10rpx;
  font-size: 24rpx;
  color: #999;
}

.ad-tips-overlay, .ad-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.ad-tips-card {
  width: 80%;
  background-color: #fff;
  border-radius: 20rpx;
  overflow: hidden;
}

.ad-tips-title {
  font-size: 34rpx;
  font-weight: bold;
  text-align: center;
  padding: 30rpx 0;
  color: #333;
  border-bottom: 1rpx solid #eee;
}

.ad-tips-content {
  padding: 30rpx;
}

.ad-tips-item {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 20rpx;
  line-height: 1.5;
}

.highlight {
  color: #FF6600;
  font-weight: bold;
}

.ad-tips-btns {
  display: flex;
  border-top: 1rpx solid #eee;
}

.ad-tips-btn {
  flex: 1;
  text-align: center;
  padding: 24rpx 0;
  font-size: 30rpx;
}

.ad-tips-btn.cancel {
  color: #999;
  border-right: 1rpx solid #eee;
}

.ad-tips-btn.confirm {
  color: #0066FF;
  font-weight: bold;
}

.ad-container {
  width: 90%;
  background-color: #fff;
  border-radius: 12rpx;
  overflow: hidden;
}

.ad-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 30rpx;
  background-color: #f5f5f5;
}

.ad-title {
  font-size: 28rpx;
  color: #999;
}

.ad-countdown {
  font-size: 26rpx;
  color: #FF6600;
  font-weight: bold;
}

.ad-content {
  position: relative;
  height: 400rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}

.ad-image {
  width: 100%;
  height: 100%;
}

.ad-message {
  position: absolute;
  background-color: rgba(0, 0, 0, 0.5);
  color: #fff;
  padding: 10rpx 20rpx;
  border-radius: 8rpx;
  font-size: 26rpx;
}

.ad-footer {
  padding: 20rpx 30rpx;
  display: flex;
  flex-direction: column;
}

.ad-tips {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 20rpx;
}

.ad-close-btn {
  align-self: center;
  width: 80%;
  text-align: center;
  padding: 16rpx 0;
  border-radius: 50rpx;
  font-size: 28rpx;
  background-color: #f0f0f0;
  color: #999;
}

.ad-close-active {
  background-color: #0066FF;
  color: #fff;
}

.top-result {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1001;
  pointer-events: none;
}

.top-result-content {
  background-color: rgba(0, 0, 0, 0.7);
  padding: 40rpx;
  border-radius: 20rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.top-result-icon {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 20rpx;
}

.top-result-text {
  color: #fff;
  font-size: 30rpx;
}

/* 付费置顶选项弹出层 */
.top-options-dropdown {
  background-color: #fff;
  width: 100%;
  border-radius: 0 0 20rpx 20rpx;
  padding: 30rpx 20rpx;
  box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.1);
  animation: dropDown 0.3s ease;
}

@keyframes dropDown {
  from { 
    opacity: 0;
    transform: translateY(-50%);
  }
  to { 
    opacity: 1;
    transform: translateY(0);
  }
}

.top-options-title {
  text-align: center;
  font-size: 32rpx;
  color: #333;
  font-weight: bold;
  margin-bottom: 30rpx;
  position: relative;
  padding-bottom: 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.top-options-list {
  display: flex;
  justify-content: space-between;
  margin-top: 20rpx;
  padding: 0 10rpx;
}

.top-option-item {
  width: 30%;
  height: 140rpx;
  background-color: #f8f8f8;
  border-radius: 12rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border: 2rpx solid transparent;
  transition: all 0.2s;
}

.top-option-item.active {
  background-color: #FFF5E6;
  border-color: #FF9500;
}

.top-option-days {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 10rpx;
}

.top-option-price {
  font-size: 28rpx;
  color: #FF9500;
  font-weight: 600;
}

/* 付费按钮样式优化 */
.pay-btn {
  background-color: #FF9500;
  color: #fff;
  font-size: 24rpx;
  padding: 6rpx 20rpx;
  border-radius: 30rpx;
}

/* 免费按钮样式优化 */
.free-btn {
  background-color: #34C759;
  color: #fff;
  font-size: 24rpx;
  padding: 6rpx 20rpx;
  border-radius: 30rpx;
}

/* 自定义弹窗样式 */
.custom-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.65);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(10rpx);
  -webkit-backdrop-filter: blur(10rpx);
}

.modal-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: transparent;
  z-index: 1001;
}

.modal-content {
  background-color: rgba(255, 255, 255, 0.98);
  border-radius: 16rpx;
  overflow: hidden;
  width: 85%;
  max-width: 600rpx;
  position: relative;
  z-index: 1002;
  box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.25);
  transform: translateY(0);
  animation: modal-in 0.3s cubic-bezier(0.23, 1, 0.32, 1);
}

@keyframes modal-in {
  from {
    opacity: 0;
    transform: translateY(20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.modal-title {
  font-size: 34rpx;
  font-weight: 600;
  text-align: center;
  padding: 30rpx 0;
  color: #000;
  border-bottom: 0.5rpx solid rgba(60, 60, 67, 0.12);
}

.modal-body {
  padding: 35rpx 30rpx;
}

.modal-text {
  font-size: 28rpx;
  color: rgba(60, 60, 67, 0.85);
  margin-bottom: 20rpx;
  line-height: 1.5;
  text-align: center;
  letter-spacing: 0.5rpx;
}

.price-text {
  font-size: 30rpx;
  color: rgba(60, 60, 67, 0.85);
  text-align: center;
  margin: 25rpx 0 15rpx;
  font-weight: 500;
}

.price-amount {
  font-size: 40rpx;
  color: #FF2D55;
  font-weight: 600;
  margin: 0 6rpx;
}

.rank-hint {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 25rpx 0;
  font-size: 26rpx;
  color: rgba(60, 60, 67, 0.7);
  background-color: rgba(0, 122, 255, 0.08);
  padding: 12rpx 20rpx;
  border-radius: 30rpx;
}

.rank-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: 10rpx;
}

.special-hint {
  font-size: 26rpx;
  color: #FF9500;
  text-align: center;
  margin: 18rpx 0 10rpx;
  font-weight: 500;
}

.modal-footer {
  display: flex;
  justify-content: space-between;
  padding: 0;
  border-top: 0.5rpx solid rgba(60, 60, 67, 0.12);
}

.modal-btn {
  flex: 1;
  text-align: center;
  padding: 25rpx 0;
  font-size: 32rpx;
  border-radius: 0;
  border: none;
  margin: 0;
  line-height: 1.5;
  font-weight: 500;
  transition: background-color 0.2s ease;
}

.modal-btn::after {
  border: none;
}

.cancel-btn {
  background-color: rgba(242, 242, 247, 0.95);
  color: #007AFF;
}

.cancel-btn:active {
  background-color: rgba(229, 229, 234, 1);
}

.confirm-btn {
  background-color: #007AFF;
  color: white;
}

.confirm-btn:active {
  background-color: #0062CC;
}
</style> 