"use strict";
const common_vendor = require("../common/vendor.js");
const utils_request = require("./request.js");
const utils_format = require("./format.js");
require("../store/index.js");
class DistributionService {
  /**
   * 检查用户是否是分销员
   * @param {Object} options 选项
   * @param {String} options.userId 用户ID
   * @returns {Promise<Boolean>} 是否是分销员
   */
  async isDistributor(options = {}) {
    try {
      return new Promise((resolve) => {
        setTimeout(() => {
          const isDistributor = ["1001", "1002", "1003"].includes(options.userId);
          resolve(isDistributor);
        }, 300);
      });
    } catch (error) {
      common_vendor.index.__f__("error", "at utils/distributionService.js:33", "检查分销员状态失败", error);
      return false;
    }
  }
  /**
   * 获取分销员信息
   * @param {Object} options 选项
   * @param {String} options.userId 用户ID
   * @returns {Promise<Object>} 分销员信息
   */
  async getDistributorInfo(options = {}) {
    try {
      return new Promise((resolve) => {
        setTimeout(() => {
          resolve({
            id: options.userId,
            level: "黄金分销员",
            levelValue: 2,
            totalCommission: 1256.8,
            availableCommission: 458.2,
            pendingCommission: 320.5,
            withdrawnCommission: 478.1,
            totalOrders: 32,
            teamCount: 5,
            createdAt: "2023-05-15",
            inviteCode: "DIS" + options.userId,
            status: "active"
          });
        }, 300);
      });
    } catch (error) {
      common_vendor.index.__f__("error", "at utils/distributionService.js:68", "获取分销员信息失败", error);
      return null;
    }
  }
  /**
   * 获取分销中心数据
   */
  async getDistributionCenter() {
    return await utils_request.request({
      url: "/distribution/center",
      method: "GET"
    });
  }
  /**
   * 申请成为分销员
   * @param {Object} data 申请数据
   */
  async applyDistributor(data) {
    return await utils_request.request({
      url: "/distribution/apply",
      method: "POST",
      data
    });
  }
  /**
   * 获取分销员申请状态
   */
  async getApplyStatus() {
    return await utils_request.request({
      url: "/distribution/apply/status",
      method: "GET"
    });
  }
  /**
   * 获取佣金明细列表
   * @param {Object} params 查询参数
   */
  async getCommissionList(params) {
    return await utils_request.request({
      url: "/distribution/commission/list",
      method: "GET",
      params
    });
  }
  /**
   * 获取佣金统计数据
   */
  async getCommissionStats() {
    return await utils_request.request({
      url: "/distribution/commission/stats",
      method: "GET"
    });
  }
  /**
   * 获取推广工具列表
   */
  async getPromotionTools() {
    return await utils_request.request({
      url: "/distribution/promotion/tools",
      method: "GET"
    });
  }
  /**
   * 获取分销团队数据
   * @param {Object} params 查询参数
   */
  async getTeamMembers(params) {
    return await utils_request.request({
      url: "/distribution/team/members",
      method: "GET",
      params
    });
  }
  /**
   * 获取团队统计数据
   */
  async getTeamStats() {
    return await utils_request.request({
      url: "/distribution/team/stats",
      method: "GET"
    });
  }
  /**
   * 获取可分销商品列表
   * @param {Object} params 查询参数
   */
  async getDistributableProducts(params) {
    return await utils_request.request({
      url: "/distribution/products",
      method: "GET",
      params
    });
  }
  /**
   * 提交佣金提现申请
   * @param {Object} data 提现数据
   */
  async withdrawCommission(data) {
    return await utils_request.request({
      url: "/distribution/withdraw",
      method: "POST",
      data
    });
  }
  /**
   * 获取提现记录列表
   * @param {Object} params 查询参数
   */
  async getWithdrawRecords(params) {
    return await utils_request.request({
      url: "/distribution/withdraw/records",
      method: "GET",
      params
    });
  }
  /**
   * 生成商品推广码
   * @param {Object} data 商品数据
   */
  async generateProductCode(data) {
    return await utils_request.request({
      url: "/distribution/promotion/code",
      method: "POST",
      data
    });
  }
  /**
   * 获取分销配置
   */
  async getDistributionConfig() {
    return await utils_request.request({
      url: "/distribution/config",
      method: "GET"
    });
  }
  /**
   * 获取分销海报
   * @param {Object} params 海报参数
   */
  async getDistributionPoster(params) {
    return await utils_request.request({
      url: "/distribution/promotion/poster",
      method: "GET",
      params
    });
  }
  /**
   * 获取带有分销参数的商品链接
   * @param {Object} data 商品数据
   */
  async getProductShareLink(data) {
    return await utils_request.request({
      url: "/distribution/promotion/link",
      method: "POST",
      data
    });
  }
  /**
   * 获取商家列表（支持分销）
   * @param {Object} params 查询参数
   */
  async getMerchantsWithDistribution(params) {
    return await utils_request.request({
      url: "/distribution/merchants",
      method: "GET",
      params
    });
  }
  /**
   * 申请成为商家专属分销员
   * @param {Object} data 申请数据
   */
  async applyMerchantDistributor(data) {
    return await utils_request.request({
      url: "/distribution/merchant/apply",
      method: "POST",
      data
    });
  }
  /**
   * 获取分销等级设置
   */
  async getDistributionLevels() {
    return await utils_request.request({
      url: "/distribution/levels",
      method: "GET"
    });
  }
  /**
   * 保存分销等级设置
   * @param {Object} data 等级设置数据
   */
  async saveDistributionLevels(data) {
    return await utils_request.request({
      url: "/distribution/levels",
      method: "POST",
      data
    });
  }
  /**
   * 获取佣金规则设置
   */
  async getCommissionRules() {
    return await utils_request.request({
      url: "/distribution/commission/rules",
      method: "GET"
    });
  }
  /**
   * 保存佣金规则设置
   * @param {Object} data 佣金规则数据
   */
  async saveCommissionRules(data) {
    return await utils_request.request({
      url: "/distribution/commission/rules",
      method: "POST",
      data
    });
  }
  /**
   * 获取分销员列表（管理端）
   * @param {Object} params 查询参数
   */
  async getDistributorsList(params) {
    return await utils_request.request({
      url: "/distribution/admin/distributors",
      method: "GET",
      params
    });
  }
  /**
   * 审核分销员申请（管理端）
   * @param {Object} data 审核数据
   */
  async reviewDistributorApplication(data) {
    return await utils_request.request({
      url: "/distribution/admin/review",
      method: "POST",
      data
    });
  }
  /**
   * 设置分销员等级（管理端）
   * @param {Object} data 等级数据
   */
  async setDistributorLevel(data) {
    return await utils_request.request({
      url: "/distribution/admin/set-level",
      method: "POST",
      data
    });
  }
  /**
   * 禁用/启用分销员（管理端）
   * @param {Object} data 状态数据
   */
  async toggleDistributorStatus(data) {
    return await utils_request.request({
      url: "/distribution/admin/toggle-status",
      method: "POST",
      data
    });
  }
  /**
   * 获取分销订单列表（管理端）
   * @param {Object} params 查询参数
   */
  async getDistributionOrders(params) {
    return await utils_request.request({
      url: "/distribution/admin/orders",
      method: "GET",
      params
    });
  }
  /**
   * 手动调整佣金（管理端）
   * @param {Object} data 调整数据
   */
  async adjustCommission(data) {
    return await utils_request.request({
      url: "/distribution/admin/adjust-commission",
      method: "POST",
      data
    });
  }
  /**
   * 审核提现申请（管理端）
   * @param {Object} data 审核数据
   */
  async reviewWithdrawal(data) {
    return await utils_request.request({
      url: "/distribution/admin/review-withdrawal",
      method: "POST",
      data
    });
  }
  /**
   * 获取分销统计数据（管理端）
   * @param {Object} params 查询参数
   */
  async getDistributionStats(params) {
    return await utils_request.request({
      url: "/distribution/admin/stats",
      method: "GET",
      params
    });
  }
  /**
   * 获取分销商品设置列表（管理端）
   * @param {Object} params 查询参数
   */
  async getDistributionProductSettings(params) {
    return await utils_request.request({
      url: "/distribution/admin/product-settings",
      method: "GET",
      params
    });
  }
  /**
   * 设置商品分销参数（管理端）
   * @param {Object} data 商品分销数据
   */
  async setProductDistribution(data) {
    return await utils_request.request({
      url: "/distribution/admin/set-product",
      method: "POST",
      data
    });
  }
  /**
   * 批量设置商品分销参数（管理端）
   * @param {Object} data 批量设置数据
   */
  async batchSetProductDistribution(data) {
    return await utils_request.request({
      url: "/distribution/admin/batch-set-products",
      method: "POST",
      data
    });
  }
  /**
   * 获取分销条件
   * @returns {Promise<Object>} 分销条件
   */
  async getDistributionConditions() {
    try {
      return new Promise((resolve) => {
        setTimeout(() => {
          resolve({
            requirePurchase: true,
            minimumPurchase: 100,
            requireApproval: true,
            autoApprove: false,
            needRealName: true,
            needMobile: true,
            description: "成为分销员需要购买任意商品并提交申请，审核通过后即可成为分销员。"
          });
        }, 300);
      });
    } catch (error) {
      common_vendor.index.__f__("error", "at utils/distributionService.js:463", "获取分销条件失败", error);
      return null;
    }
  }
  /**
   * 获取佣金明细
   * @param {Object} options 选项
   * @param {Number} options.page 页码
   * @param {Number} options.pageSize 每页数量
   * @param {String} options.status 状态筛选
   * @returns {Promise<Object>} 佣金明细和分页信息
   */
  async getCommissionRecords(options = {}) {
    try {
      const { page = 1, pageSize = 10, status = "all" } = options;
      return new Promise((resolve) => {
        setTimeout(() => {
          const statuses = ["pending", "settled", "withdrawn"];
          const records = Array(30).fill().map((_, index) => {
            const amount = Math.floor(Math.random() * 100) + 10;
            const recordStatus = statuses[Math.floor(Math.random() * statuses.length)];
            return {
              id: "C" + (1e4 + index),
              orderId: "O" + (2e4 + index),
              productName: `商品${index % 10 + 1}`,
              amount,
              status: recordStatus,
              createdAt: new Date(Date.now() - Math.floor(Math.random() * 30) * 24 * 60 * 60 * 1e3).toISOString(),
              settledAt: recordStatus === "pending" ? null : new Date(Date.now() - Math.floor(Math.random() * 20) * 24 * 60 * 60 * 1e3).toISOString(),
              withdrawnAt: recordStatus === "withdrawn" ? new Date(Date.now() - Math.floor(Math.random() * 10) * 24 * 60 * 60 * 1e3).toISOString() : null,
              level: Math.floor(Math.random() * 3) + 1
            };
          });
          let filteredRecords = records;
          if (status !== "all") {
            filteredRecords = records.filter((record) => record.status === status);
          }
          const start = (page - 1) * pageSize;
          const end = start + pageSize;
          const paginatedRecords = filteredRecords.slice(start, end);
          resolve({
            list: paginatedRecords,
            pagination: {
              total: filteredRecords.length,
              page,
              pageSize,
              totalPages: Math.ceil(filteredRecords.length / pageSize)
            },
            summary: {
              total: records.reduce((sum, record) => sum + record.amount, 0),
              pending: records.filter((record) => record.status === "pending").reduce((sum, record) => sum + record.amount, 0),
              settled: records.filter((record) => record.status === "settled").reduce((sum, record) => sum + record.amount, 0),
              withdrawn: records.filter((record) => record.status === "withdrawn").reduce((sum, record) => sum + record.amount, 0)
            }
          });
        }, 500);
      });
    } catch (error) {
      common_vendor.index.__f__("error", "at utils/distributionService.js:532", "获取佣金明细失败", error);
      return {
        list: [],
        pagination: {
          total: 0,
          page: options.page || 1,
          pageSize: options.pageSize || 10,
          totalPages: 0
        },
        summary: {
          total: 0,
          pending: 0,
          settled: 0,
          withdrawn: 0
        }
      };
    }
  }
  /**
   * 获取团队成员
   * @param {Object} options 选项
   * @param {Number} options.page 页码
   * @param {Number} options.pageSize 每页数量
   * @param {Number} options.level 团队层级
   * @returns {Promise<Object>} 团队成员和分页信息
   */
  async getTeamMembers(options = {}) {
    try {
      const { page = 1, pageSize = 10, level = 1 } = options;
      return new Promise((resolve) => {
        setTimeout(() => {
          const members = Array(level === 1 ? 15 : 8).fill().map((_, index) => ({
            id: "U" + (1e4 + index),
            nickname: `用户${index + 1}`,
            avatar: `/static/images/avatars/avatar-${index % 8 + 1}.jpg`,
            level: Math.floor(Math.random() * 3) + 1,
            levelName: ["普通分销员", "黄金分销员", "钻石分销员"][Math.floor(Math.random() * 3)],
            joinTime: new Date(Date.now() - Math.floor(Math.random() * 90) * 24 * 60 * 60 * 1e3).toISOString(),
            contribution: Math.floor(Math.random() * 2e3) + 100,
            orderCount: Math.floor(Math.random() * 20) + 1
          }));
          const start = (page - 1) * pageSize;
          const end = start + pageSize;
          const paginatedMembers = members.slice(start, end);
          resolve({
            list: paginatedMembers,
            pagination: {
              total: members.length,
              page,
              pageSize,
              totalPages: Math.ceil(members.length / pageSize)
            },
            summary: {
              level1Count: 15,
              level2Count: 8,
              totalContribution: members.reduce((sum, member) => sum + member.contribution, 0)
            }
          });
        }, 500);
      });
    } catch (error) {
      common_vendor.index.__f__("error", "at utils/distributionService.js:601", "获取团队成员失败", error);
      return {
        list: [],
        pagination: {
          total: 0,
          page: options.page || 1,
          pageSize: options.pageSize || 10,
          totalPages: 0
        },
        summary: {
          level1Count: 0,
          level2Count: 0,
          totalContribution: 0
        }
      };
    }
  }
  /**
   * 获取提现设置
   * @returns {Promise<Object>} 提现设置
   */
  async getWithdrawalSettings() {
    try {
      return new Promise((resolve) => {
        setTimeout(() => {
          resolve({
            minAmount: 50,
            maxAmount: 5e3,
            feeRate: 0.01,
            withdrawalCycle: "weekly",
            withdrawalDays: [1, 4],
            // 周一和周四可提现
            autoApprove: false,
            needRealName: true,
            withdrawalMethods: ["wechat", "alipay", "bank"]
          });
        }, 300);
      });
    } catch (error) {
      common_vendor.index.__f__("error", "at utils/distributionService.js:642", "获取提现设置失败", error);
      return null;
    }
  }
  /**
   * 申请提现
   * @param {Object} data 提现数据
   * @returns {Promise<Object>} 提现结果
   */
  async applyWithdrawal(data) {
    try {
      return new Promise((resolve) => {
        setTimeout(() => {
          if (data.amount < 50) {
            resolve({
              success: false,
              message: "提现金额不能小于50元"
            });
            return;
          }
          resolve({
            success: true,
            message: "提现申请已提交，等待审核",
            data: {
              withdrawalId: "W" + Date.now(),
              amount: data.amount,
              fee: data.amount * 0.01,
              actualAmount: data.amount * 0.99,
              status: "pending",
              createdAt: (/* @__PURE__ */ new Date()).toISOString()
            }
          });
        }, 500);
      });
    } catch (error) {
      common_vendor.index.__f__("error", "at utils/distributionService.js:681", "申请提现失败", error);
      return {
        success: false,
        message: "提现申请失败，请稍后再试",
        error
      };
    }
  }
  /**
   * 获取分销协议
   * @returns {Promise<String>} 分销协议内容
   */
  async getDistributionAgreement() {
    try {
      return new Promise((resolve) => {
        setTimeout(() => {
          resolve(`# 磁州生活网分销员协议

## 一、总则

1.1 本协议是磁州生活网平台与分销员之间关于分销合作的约定。
1.2 成为分销员即表示您已阅读并同意本协议的全部条款。

## 二、分销员资格

2.1 分销员需年满18周岁，具有完全民事行为能力。
2.2 分销员需遵守国家法律法规和平台规则。
2.3 分销员资格需经平台审核通过。

## 三、分销规则

3.1 分销员可获得其推广成交订单的佣金。
3.2 佣金比例根据分销员等级和商品设置而定。
3.3 佣金将在订单完成并结算后计入分销员账户。

## 四、提现规则

4.1 分销员可申请将账户佣金提现至指定账户。
4.2 提现金额不低于平台设定的最低提现额度。
4.3 提现需完成实名认证和绑定提现账户。

## 五、禁止行为

5.1 禁止虚假交易、刷单等作弊行为。
5.2 禁止使用不当手段获取分销资格或佣金。
5.3 禁止发布违法违规信息进行推广。

## 六、违约责任

6.1 若分销员违反本协议，平台有权取消其分销资格。
6.2 因违规行为获得的佣金，平台有权追回。
6.3 造成平台损失的，分销员需承担赔偿责任。

## 七、协议修改

7.1 平台有权根据需要修改本协议内容。
7.2 协议修改后，平台将通过适当方式通知分销员。
7.3 修改后的协议自公布之日起生效。

## 八、协议终止

8.1 分销员可主动申请终止分销合作。
8.2 平台有权根据规则终止与分销员的合作。
8.3 协议终止后，未结算的佣金将按规则处理。

## 九、其他条款

9.1 本协议解释权归磁州生活网平台所有。
9.2 本协议自分销员资格审核通过之日起生效。`);
        }, 300);
      });
    } catch (error) {
      common_vendor.index.__f__("error", "at utils/distributionService.js:756", "获取分销协议失败", error);
      return "";
    }
  }
  /**
   * 生成推广链接
   * @param {Object} options 选项
   * @param {String} options.type 推广类型
   * @param {String} options.id 推广对象ID
   * @param {String} options.distributorId 分销员ID
   * @returns {Promise<Object>} 推广链接信息
   */
  async generatePromotionLink(options) {
    try {
      const { type, id, distributorId } = options;
      return new Promise((resolve) => {
        setTimeout(() => {
          const baseUrl = "https://cizhoulife.com";
          let path = "";
          switch (type) {
            case "product":
              path = `/pages/product/detail?id=${id}&distributor=${distributorId}`;
              break;
            case "merchant":
              path = `/pages/merchant/detail?id=${id}&distributor=${distributorId}`;
              break;
            case "activity":
              path = `/pages/activity/detail?id=${id}&distributor=${distributorId}`;
              break;
            default:
              path = `/?distributor=${distributorId}`;
          }
          resolve({
            url: baseUrl + path,
            shortUrl: `https://czl.cn/${type.charAt(0)}${id.substring(0, 4)}`,
            qrCodeUrl: `/static/images/qrcode-placeholder.png`,
            distributorId,
            type,
            id
          });
        }, 300);
      });
    } catch (error) {
      common_vendor.index.__f__("error", "at utils/distributionService.js:805", "生成推广链接失败", error);
      return null;
    }
  }
  /**
   * 格式化佣金金额
   * @param {Number} amount 金额
   * @returns {String} 格式化后的金额
   */
  formatCommission(amount) {
    return utils_format.formatNumber(amount);
  }
}
const distributionService = new DistributionService();
exports.distributionService = distributionService;
//# sourceMappingURL=../../.sourcemap/mp-weixin/utils/distributionService.js.map
