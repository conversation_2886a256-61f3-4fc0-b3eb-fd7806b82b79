<template>
  <view class="coupon-container">
    <!-- 自定义导航栏 -->
    <view class="custom-navbar" :style="{ paddingTop: statusBarHeight + 'px' }">
      <view class="navbar-left" @click="goBack">
        <image src="/static/images/tabbar/返回键.png" class="back-icon"></image>
      </view>
      <view class="navbar-title">我的卡券</view>
      <view class="navbar-right"></view>
    </view>
    
    <!-- 顶部选项卡 -->
    <view class="tabs-container" :style="{ top: navbarHeight + 'px' }">
      <view 
        class="tab-item" 
        v-for="(tab, index) in tabs" 
        :key="index"
        :class="{ active: currentTab === index }"
        @click="switchTab(index)"
      >
        <text class="tab-text">{{ tab.name }}</text>
      </view>
      <view class="tab-line" :style="tabLineStyle"></view>
    </view>
    
    <!-- 内容区域 -->
    <view class="content-area" :style="{ paddingTop: (navbarHeight + tabsHeight) + 'px' }">
      <swiper class="content-swiper" :current="currentTab" @change="onSwiperChange">
        <!-- 未使用卡券 -->
        <swiper-item>
          <scroll-view scroll-y class="tab-scroll" @scrolltolower="loadMore(0)" refresher-enabled :refresher-triggered="refreshing[0]" @refresherrefresh="onRefresh(0)">
            <view v-if="availableCoupons.length > 0" class="coupon-list">
              <view 
                class="coupon-item" 
                v-for="(item, index) in availableCoupons" 
                :key="index"
                :class="{ 'coupon-item-selected': selectedCoupon === item.id }"
                @click="selectCoupon(item)"
              >
                <view class="coupon-left">
                  <view class="coupon-value">
                    <text class="coupon-unit">¥</text>
                    <text class="coupon-amount">{{ item.amount }}</text>
                  </view>
                  <view class="coupon-condition">{{ item.condition }}</view>
                </view>
                <view class="coupon-middle">
                  <view class="coupon-name">{{ item.name }}</view>
                  <view class="coupon-scope">{{ item.scope }}</view>
                  <view class="coupon-time">{{ item.validTime }}</view>
                </view>
                <view class="coupon-right">
                  <view class="coupon-btn" @click.stop="useCoupon(item)">
                    <text class="btn-text">立即使用</text>
                  </view>
                </view>
                <view class="coupon-border">
                  <view class="border-circle border-top-left"></view>
                  <view class="border-circle border-top-right"></view>
                  <view class="border-circle border-bottom-left"></view>
                  <view class="border-circle border-bottom-right"></view>
                </view>
              </view>
            </view>
            <view v-else class="empty-view">
              <image class="empty-icon" src="/static/images/empty.png" mode="aspectFit"></image>
              <view class="empty-text">暂无可用卡券</view>
            </view>
            <view v-if="availableCoupons.length > 0 && !hasMore[0]" class="list-bottom">没有更多了</view>
          </scroll-view>
        </swiper-item>
        
        <!-- 已使用卡券 -->
        <swiper-item>
          <scroll-view scroll-y class="tab-scroll" @scrolltolower="loadMore(1)" refresher-enabled :refresher-triggered="refreshing[1]" @refresherrefresh="onRefresh(1)">
            <view v-if="usedCoupons.length > 0" class="coupon-list">
              <view class="coupon-item coupon-used" v-for="(item, index) in usedCoupons" :key="index">
                <view class="coupon-mask">
                  <view class="coupon-mask-text">已使用</view>
                </view>
                <view class="coupon-left">
                  <view class="coupon-value">
                    <text class="coupon-unit">¥</text>
                    <text class="coupon-amount">{{ item.amount }}</text>
                  </view>
                  <view class="coupon-condition">{{ item.condition }}</view>
                </view>
                <view class="coupon-middle">
                  <view class="coupon-name">{{ item.name }}</view>
                  <view class="coupon-scope">{{ item.scope }}</view>
                  <view class="coupon-time">使用时间: {{ item.usedTime }}</view>
                </view>
                <view class="coupon-right"></view>
                <view class="coupon-border">
                  <view class="border-circle border-top-left"></view>
                  <view class="border-circle border-top-right"></view>
                  <view class="border-circle border-bottom-left"></view>
                  <view class="border-circle border-bottom-right"></view>
                </view>
              </view>
            </view>
            <view v-else class="empty-view">
              <image class="empty-icon" src="/static/images/empty.png" mode="aspectFit"></image>
              <view class="empty-text">暂无已使用卡券</view>
            </view>
            <view v-if="usedCoupons.length > 0 && !hasMore[1]" class="list-bottom">没有更多了</view>
          </scroll-view>
        </swiper-item>
        
        <!-- 已过期卡券 -->
        <swiper-item>
          <scroll-view scroll-y class="tab-scroll" @scrolltolower="loadMore(2)" refresher-enabled :refresher-triggered="refreshing[2]" @refresherrefresh="onRefresh(2)">
            <view v-if="expiredCoupons.length > 0" class="coupon-list">
              <view class="coupon-item coupon-expired" v-for="(item, index) in expiredCoupons" :key="index">
                <view class="coupon-mask">
                  <view class="coupon-mask-text">已过期</view>
                </view>
                <view class="coupon-left">
                  <view class="coupon-value">
                    <text class="coupon-unit">¥</text>
                    <text class="coupon-amount">{{ item.amount }}</text>
                  </view>
                  <view class="coupon-condition">{{ item.condition }}</view>
                </view>
                <view class="coupon-middle">
                  <view class="coupon-name">{{ item.name }}</view>
                  <view class="coupon-scope">{{ item.scope }}</view>
                  <view class="coupon-time">已于 {{ item.expireTime }} 过期</view>
                </view>
                <view class="coupon-right"></view>
                <view class="coupon-border">
                  <view class="border-circle border-top-left"></view>
                  <view class="border-circle border-top-right"></view>
                  <view class="border-circle border-bottom-left"></view>
                  <view class="border-circle border-bottom-right"></view>
                </view>
              </view>
            </view>
            <view v-else class="empty-view">
              <image class="empty-icon" src="/static/images/empty.png" mode="aspectFit"></image>
              <view class="empty-text">暂无过期卡券</view>
            </view>
            <view v-if="expiredCoupons.length > 0 && !hasMore[2]" class="list-bottom">没有更多了</view>
          </scroll-view>
        </swiper-item>
      </swiper>
    </view>
    
    <!-- 底部固定操作栏 -->
    <view class="footer-bar" v-if="currentTab === 0">
      <view class="footer-info">
        <text class="footer-text">共 {{ availableCoupons.length }} 张可用卡券</text>
      </view>
      <view class="footer-btn" @click="getCoupon">
        <text class="footer-btn-text">领取优惠券</text>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';

// 响应式状态
const statusBarHeight = ref(20);
const navbarHeight = ref(64); // 导航栏高度
const tabsHeight = ref(44); // 选项卡高度
const tabs = ref([
  { name: '未使用' },
  { name: '已使用' },
  { name: '已过期' }
]);
const currentTab = ref(0);
const availableCoupons = ref([]);
const usedCoupons = ref([]);
const expiredCoupons = ref([]);
const page = ref([1, 1, 1]); // 当前页码
const pageSize = ref(10); // 每页显示数量
const hasMore = ref([true, true, true]); // 是否有更多数据
const refreshing = ref([false, false, false]); // 刷新状态
const selectedCoupon = ref(null); // 选中的卡券ID

// 计算属性
const tabLineStyle = computed(() => {
  return {
    transform: `translateX(${currentTab.value * (100 / tabs.value.length)}%)`,
    width: `${100 / tabs.value.length}%`
  }
});

// 返回上一页
const goBack = () => {
  uni.navigateBack();
};

// 切换选项卡
const switchTab = (index) => {
  currentTab.value = index;
};

// 轮播图切换事件
const onSwiperChange = (e) => {
  currentTab.value = e.detail.current;
};

// 加载未使用卡券
const loadAvailableCoupons = () => {
  // 模拟请求延迟
  setTimeout(() => {
    // 模拟数据
    const mockData = Array.from({ length: 5 }, (_, i) => ({
      id: `available_${page.value[0]}_${i}`,
      name: `${['满减券', '折扣券', '代金券'][i % 3]} ${page.value[0]}_${i}`,
      amount: Math.floor(Math.random() * 50) + 10,
      condition: `满${Math.floor(Math.random() * 100) + 50}元可用`,
      scope: `适用于${['全部商家', '餐饮美食', '生活服务', '休闲娱乐'][i % 4]}`,
      validTime: `有效期至 ${new Date(Date.now() + 86400000 * 30).toLocaleDateString()}`
    }));
    
    if (page.value[0] === 1) {
      availableCoupons.value = mockData;
    } else {
      availableCoupons.value = [...availableCoupons.value, ...mockData];
    }
    
    // 模拟是否还有更多数据
    hasMore.value[0] = page.value[0] < 2;
    
    // 关闭刷新状态
    refreshing.value[0] = false;
  }, 500);
};

// 加载已使用卡券
const loadUsedCoupons = () => {
  // 模拟请求延迟
  setTimeout(() => {
    // 模拟数据
    const mockData = Array.from({ length: 3 }, (_, i) => ({
      id: `used_${page.value[1]}_${i}`,
      name: `${['满减券', '折扣券', '代金券'][i % 3]} ${page.value[1]}_${i}`,
      amount: Math.floor(Math.random() * 50) + 10,
      condition: `满${Math.floor(Math.random() * 100) + 50}元可用`,
      scope: `适用于${['全部商家', '餐饮美食', '生活服务', '休闲娱乐'][i % 4]}`,
      usedTime: new Date(Date.now() - 86400000 * (Math.floor(Math.random() * 10) + 1)).toLocaleDateString()
    }));
    
    if (page.value[1] === 1) {
      usedCoupons.value = mockData;
    } else {
      usedCoupons.value = [...usedCoupons.value, ...mockData];
    }
    
    // 模拟是否还有更多数据
    hasMore.value[1] = page.value[1] < 2;
    
    // 关闭刷新状态
    refreshing.value[1] = false;
  }, 500);
};

// 加载已过期卡券
const loadExpiredCoupons = () => {
  // 模拟请求延迟
  setTimeout(() => {
    // 模拟数据
    const mockData = Array.from({ length: 4 }, (_, i) => ({
      id: `expired_${page.value[2]}_${i}`,
      name: `${['满减券', '折扣券', '代金券'][i % 3]} ${page.value[2]}_${i}`,
      amount: Math.floor(Math.random() * 50) + 10,
      condition: `满${Math.floor(Math.random() * 100) + 50}元可用`,
      scope: `适用于${['全部商家', '餐饮美食', '生活服务', '休闲娱乐'][i % 4]}`,
      expireTime: new Date(Date.now() - 86400000 * (Math.floor(Math.random() * 20) + 10)).toLocaleDateString()
    }));
    
    if (page.value[2] === 1) {
      expiredCoupons.value = mockData;
    } else {
      expiredCoupons.value = [...expiredCoupons.value, ...mockData];
    }
    
    // 模拟是否还有更多数据
    hasMore.value[2] = page.value[2] < 2;
    
    // 关闭刷新状态
    refreshing.value[2] = false;
  }, 500);
};

// 加载更多数据
const loadMore = (tabIndex) => {
  if (!hasMore.value[tabIndex]) return;
  
  page.value[tabIndex]++;
  if (tabIndex === 0) {
    loadAvailableCoupons();
  } else if (tabIndex === 1) {
    loadUsedCoupons();
  } else {
    loadExpiredCoupons();
  }
};

// 下拉刷新
const onRefresh = (tabIndex) => {
  refreshing.value[tabIndex] = true;
  page.value[tabIndex] = 1;
  hasMore.value[tabIndex] = true;
  
  if (tabIndex === 0) {
    loadAvailableCoupons();
  } else if (tabIndex === 1) {
    loadUsedCoupons();
  } else {
    loadExpiredCoupons();
  }
};

// 选择卡券
const selectCoupon = (item) => {
  selectedCoupon.value = item.id;
};

// 使用卡券
const useCoupon = (item) => {
  uni.showModal({
    title: '提示',
    content: `确认使用「${item.name}」？`,
    success: (res) => {
      if (res.confirm) {
        uni.navigateTo({
          url: '/pages/coupon/use?id=' + item.id
        });
      }
    }
  });
};

// 领取优惠券
const getCoupon = () => {
  uni.navigateTo({
    url: '/pages/coupon/center'
  });
};

// 生命周期钩子 - 页面加载
onMounted(() => {
  // 获取状态栏高度
  const sysInfo = uni.getSystemInfoSync();
  statusBarHeight.value = sysInfo.statusBarHeight;
  navbarHeight.value = statusBarHeight.value + 44;
  
  // 加载初始数据
  loadAvailableCoupons();
  loadUsedCoupons();
  loadExpiredCoupons();
});
</script>

<style>
.coupon-container {
  min-height: 100vh;
  background-color: #f5f7fa;
  position: relative;
  padding-bottom: 100rpx;
}

/* 自定义导航栏 */
.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 44px;
  display: flex;
  align-items: center;
  padding: 0 15px;
  background-color: #0052CC;
  color: #fff;
  z-index: 100;
}

.navbar-left {
  width: 80rpx;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.navbar-title {
  flex: 1;
  text-align: center;
  font-size: 16px;
  font-weight: 500;
}

.navbar-right {
  width: 80rpx;
  text-align: right;
}

.back-icon {
  width: 40rpx;
  height: 40rpx;
}

/* 选项卡样式 */
.tabs-container {
  position: fixed;
  left: 0;
  right: 0;
  height: 44px;
  display: flex;
  background-color: #fff;
  border-bottom: 1px solid #eee;
  z-index: 99;
}

.tab-item {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
}

.tab-text {
  font-size: 14px;
  color: #333;
  padding: 0 15px;
}

.tab-item.active .tab-text {
  color: #0052CC;
  font-weight: bold;
}

.tab-line {
  position: absolute;
  bottom: 0;
  height: 3px;
  background-color: #0052CC;
  border-radius: 2px;
  transition: transform 0.3s;
}

/* 内容区域 */
.content-area {
  position: relative;
  height: 100vh;
}

.content-swiper {
  height: 100%;
}

.tab-scroll {
  height: 100%;
}

/* 卡券列表 */
.coupon-list {
  padding: 15px;
}

.coupon-item {
  position: relative;
  display: flex;
  background: #fff;
  border-radius: 8px;
  margin-bottom: 15px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.coupon-item-selected {
  border: 1px solid #0052CC;
}

.coupon-left {
  width: 220rpx;
  padding: 20px 0;
  background: linear-gradient(135deg, #0052CC, #0066FF);
  color: #fff;
  text-align: center;
  display: flex;
  flex-direction: column;
  justify-content: center;
  position: relative;
}

.coupon-value {
  display: flex;
  justify-content: center;
  align-items: baseline;
}

.coupon-unit {
  font-size: 24rpx;
  font-weight: bold;
}

.coupon-amount {
  font-size: 60rpx;
  font-weight: bold;
  line-height: 1;
}

.coupon-condition {
  font-size: 22rpx;
  margin-top: 10rpx;
  color: rgba(255, 255, 255, 0.8);
}

.coupon-middle {
  flex: 1;
  padding: 20px 15px;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.coupon-name {
  font-size: 30rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 10rpx;
}

.coupon-scope {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 10rpx;
}

.coupon-time {
  font-size: 22rpx;
  color: #999;
}

.coupon-right {
  width: 180rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  padding-right: 15px;
}

.coupon-btn {
  width: 150rpx;
  height: 60rpx;
  background: linear-gradient(135deg, #FF6B6B, #FF4D4F);
  border-radius: 30rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.btn-text {
  font-size: 24rpx;
  color: #fff;
}

/* 卡券样式 - 已使用/已过期 */
.coupon-used, .coupon-expired {
  position: relative;
  opacity: 0.8;
}

.coupon-used .coupon-left, .coupon-expired .coupon-left {
  background: linear-gradient(135deg, #999, #666);
}

.coupon-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2;
}

.coupon-mask-text {
  font-size: 80rpx;
  font-weight: bold;
  color: rgba(153, 153, 153, 0.8);
  transform: rotate(-15deg);
  border: 5px solid rgba(153, 153, 153, 0.8);
  padding: 10rpx 40rpx;
  border-radius: 10rpx;
}

/* 卡券边框装饰 */
.coupon-border {
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  pointer-events: none;
}

.border-circle {
  position: absolute;
  width: 20rpx;
  height: 20rpx;
  background-color: #f5f7fa;
  border-radius: 50%;
}

.border-top-left {
  top: -10rpx;
  left: 210rpx;
}

.border-top-right {
  top: -10rpx;
  right: 170rpx;
}

.border-bottom-left {
  bottom: -10rpx;
  left: 210rpx;
}

.border-bottom-right {
  bottom: -10rpx;
  right: 170rpx;
}

/* 空状态 */
.empty-view {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding-top: 100px;
}

.empty-icon {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 20px;
}

.empty-text {
  font-size: 14px;
  color: #999;
}

/* 列表底部 */
.list-bottom {
  text-align: center;
  padding: 15px 0;
  font-size: 14px;
  color: #999;
}

/* 底部固定操作栏 */
.footer-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 100rpx;
  background-color: #fff;
  display: flex;
  align-items: center;
  padding: 0 30rpx;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);
  z-index: 98;
}

.footer-info {
  flex: 1;
}

.footer-text {
  font-size: 28rpx;
  color: #666;
}

.footer-btn {
  width: 240rpx;
  height: 70rpx;
  background: linear-gradient(135deg, #0052CC, #0066FF);
  border-radius: 35rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.footer-btn-text {
  font-size: 28rpx;
  color: #fff;
}
</style> 