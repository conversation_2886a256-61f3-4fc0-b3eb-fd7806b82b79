<template>
  <view class="section-header">
    <view class="title-with-icon">
      <slot name="icon">
        <svg class="section-icon" width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M19 11H5C3.89543 11 3 11.8954 3 13V20C3 21.1046 3.89543 22 5 22H19C20.1046 22 21 21.1046 21 20V13C21 11.8954 20.1046 11 19 11Z" stroke="#0A84FF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          <path d="M7 11V7C7 5.93913 7.42143 4.92172 8.17157 4.17157C8.92172 3.42143 9.93913 3 11 3H13C14.0609 3 15.0783 3.42143 15.8284 4.17157C16.5786 4.92172 17 5.93913 17 7V11" stroke="#0A84FF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
      </slot>
      <text class="section-title">{{ title }}</text>
    </view>
    <view class="section-action" v-if="hasAction" @tap="onAction">
      <text class="action-text">{{ actionText }}</text>
      <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M9 18L15 12L9 6" stroke="#0A84FF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
      </svg>
    </view>
  </view>
</template>

<script>
export default {
  name: 'SectionHeader',
  props: {
    title: {
      type: String,
      default: '区块标题'
    },
    hasAction: {
      type: Boolean,
      default: false
    },
    actionText: {
      type: String,
      default: '更多'
    }
  },
  methods: {
    onAction() {
      this.$emit('action');
    }
  }
}
</script>

<style lang="scss">
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  padding: 0 5px;
}

.title-with-icon {
  display: flex;
  align-items: center;
}

.section-icon {
  margin-right: 8px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.section-action {
  display: flex;
  align-items: center;
  color: #0A84FF;
}

.action-text {
  font-size: 14px;
  margin-right: 4px;
}
</style> 