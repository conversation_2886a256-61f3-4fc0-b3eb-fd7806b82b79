import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

export interface AppState {
  // 侧边栏状态
  sidebarCollapsed: boolean
  // 主题设置
  theme: 'light' | 'dark' | 'auto'
  // 语言设置
  locale: string
  // 缓存的视图
  cachedViews: string[]
  // 全局加载状态
  globalLoading: boolean
  // 设备类型
  device: 'desktop' | 'tablet' | 'mobile'
  // 页面大小
  pageSize: number
}

export const useAppStore = defineStore('app', () => {
  // 状态
  const sidebarCollapsed = ref(false)
  const theme = ref<'light' | 'dark' | 'auto'>('light')
  const locale = ref('zh-CN')
  const cachedViews = ref<string[]>([])
  const globalLoading = ref(false)
  const device = ref<'desktop' | 'tablet' | 'mobile'>('desktop')
  const pageSize = ref(20)

  // 计算属性
  const isDark = computed(() => {
    if (theme.value === 'dark') return true
    if (theme.value === 'light') return false
    return window.matchMedia('(prefers-color-scheme: dark)').matches
  })

  const isMobile = computed(() => device.value === 'mobile')
  const isTablet = computed(() => device.value === 'tablet')
  const isDesktop = computed(() => device.value === 'desktop')

  // 方法
  const toggleSidebar = () => {
    sidebarCollapsed.value = !sidebarCollapsed.value
    localStorage.setItem('sidebarCollapsed', String(sidebarCollapsed.value))
  }

  const setSidebarCollapsed = (collapsed: boolean) => {
    sidebarCollapsed.value = collapsed
    localStorage.setItem('sidebarCollapsed', String(collapsed))
  }

  const setTheme = (newTheme: 'light' | 'dark' | 'auto') => {
    theme.value = newTheme
    localStorage.setItem('theme', newTheme)
    
    // 应用主题
    if (newTheme === 'dark' || (newTheme === 'auto' && window.matchMedia('(prefers-color-scheme: dark)').matches)) {
      document.documentElement.classList.add('dark')
    } else {
      document.documentElement.classList.remove('dark')
    }
  }

  const setLocale = (newLocale: string) => {
    locale.value = newLocale
    localStorage.setItem('locale', newLocale)
  }

  const addCachedView = (viewName: string) => {
    if (!cachedViews.value.includes(viewName)) {
      cachedViews.value.push(viewName)
    }
  }

  const removeCachedView = (viewName: string) => {
    const index = cachedViews.value.indexOf(viewName)
    if (index > -1) {
      cachedViews.value.splice(index, 1)
    }
  }

  const clearCachedViews = () => {
    cachedViews.value = []
  }

  const setGlobalLoading = (loading: boolean) => {
    globalLoading.value = loading
  }

  const setDevice = (newDevice: 'desktop' | 'tablet' | 'mobile') => {
    device.value = newDevice
  }

  const setPageSize = (size: number) => {
    pageSize.value = size
    localStorage.setItem('pageSize', String(size))
  }

  // 初始化应用设置
  const initApp = () => {
    // 从本地存储恢复设置
    const savedSidebarCollapsed = localStorage.getItem('sidebarCollapsed')
    if (savedSidebarCollapsed !== null) {
      sidebarCollapsed.value = savedSidebarCollapsed === 'true'
    }

    const savedTheme = localStorage.getItem('theme') as 'light' | 'dark' | 'auto'
    if (savedTheme) {
      setTheme(savedTheme)
    }

    const savedLocale = localStorage.getItem('locale')
    if (savedLocale) {
      locale.value = savedLocale
    }

    const savedPageSize = localStorage.getItem('pageSize')
    if (savedPageSize) {
      pageSize.value = Number(savedPageSize)
    }

    // 检测设备类型
    detectDevice()
    
    // 监听窗口大小变化
    window.addEventListener('resize', detectDevice)
  }

  // 检测设备类型
  const detectDevice = () => {
    const width = window.innerWidth
    if (width < 768) {
      setDevice('mobile')
    } else if (width < 1024) {
      setDevice('tablet')
    } else {
      setDevice('desktop')
    }
  }

  // 清理函数
  const cleanup = () => {
    window.removeEventListener('resize', detectDevice)
  }

  // 重置应用状态
  const resetApp = () => {
    sidebarCollapsed.value = false
    theme.value = 'light'
    locale.value = 'zh-CN'
    cachedViews.value = []
    globalLoading.value = false
    pageSize.value = 20
    
    // 清除本地存储
    localStorage.removeItem('sidebarCollapsed')
    localStorage.removeItem('theme')
    localStorage.removeItem('locale')
    localStorage.removeItem('pageSize')
  }

  return {
    // 状态
    sidebarCollapsed,
    theme,
    locale,
    cachedViews,
    globalLoading,
    device,
    pageSize,
    
    // 计算属性
    isDark,
    isMobile,
    isTablet,
    isDesktop,
    
    // 方法
    toggleSidebar,
    setSidebarCollapsed,
    setTheme,
    setLocale,
    addCachedView,
    removeCachedView,
    clearCachedViews,
    setGlobalLoading,
    setDevice,
    setPageSize,
    initApp,
    detectDevice,
    cleanup,
    resetApp
  }
})
