"use strict";
const common_vendor = require("../../common/vendor.js");
const common_assets = require("../../common/assets.js");
if (!Math) {
  ReportCard();
}
const ReportCard = () => "../../components/ReportCard.js";
const _sfc_main = {
  __name: "carpool-detail",
  setup(__props, { expose: __expose }) {
    const formatTime = (timestamp) => {
      const date = new Date(timestamp);
      return `${date.getMonth() + 1}月${date.getDate()}日`;
    };
    const isCollected = common_vendor.ref(false);
    const statusBarHeight = common_vendor.ref(20);
    const navigationBarHeight = common_vendor.ref(60);
    const getSystemInfo = () => {
      common_vendor.index.getSystemInfo({
        success: (res) => {
          statusBarHeight.value = res.statusBarHeight || 20;
          navigationBarHeight.value = statusBarHeight.value + 44;
        }
      });
    };
    const tripData = common_vendor.ref({
      id: "trip12345",
      type: "car-to-people",
      // 'car-to-people' 或 'people-to-car'
      title: "磁县→邯郸",
      price: "50",
      tags: ["准点发车", "舒适车型", "可带行李"],
      publishTime: Date.now() - 864e5 * 2,
      // 2天前
      startPoint: "磁县汽车站",
      startAddress: "河北省邯郸市磁县磁州镇汽车站",
      endPoint: "邯郸东站",
      endAddress: "河北省邯郸市丛台区邯郸东站",
      startTime: "08:00",
      endTime: "09:30",
      date: "2024-03-20",
      distance: "45",
      duration: "1.5",
      remainingSeats: 3,
      carType: "舒适型轿车",
      carColor: "白色",
      carPlate: "冀D·12345",
      tripNotes: "可提前预约，支持改签",
      isVerified: true,
      driver: {
        name: "王师傅",
        avatar: "/static/images/avatar.png",
        type: "专业司机",
        isVerified: true,
        isPremium: true,
        rating: 4.8,
        ratingCount: 56,
        ratingTags: ["准时", "礼貌", "车内整洁"],
        trips: 128,
        years: 5
      },
      contact: {
        name: "王师傅",
        phone: "138****1234"
      },
      expiryTime: "2024-03-25"
    });
    const typeText = common_vendor.computed(() => {
      if (tripData.value.type === "car-to-people") {
        return "车找人";
      } else if (tripData.value.type === "people-to-car") {
        return "人找车";
      }
      return "拼车信息";
    });
    const goBack = () => {
      common_vendor.index.navigateBack();
    };
    const showActionSheet = () => {
      common_vendor.index.showActionSheet({
        itemList: ["举报", "分享", "收藏"],
        success: function(res) {
          if (res.tapIndex === 0) {
            common_vendor.index.showToast({
              title: "举报功能开发中",
              icon: "none"
            });
          } else if (res.tapIndex === 1) {
            common_vendor.index.showShareMenu({
              withShareTicket: true
            });
          } else if (res.tapIndex === 2) {
            toggleCollect();
          }
        }
      });
    };
    const toggleCollect = () => {
      isCollected.value = !isCollected.value;
      common_vendor.index.showToast({
        title: isCollected.value ? "已收藏" : "已取消收藏",
        icon: "none"
      });
    };
    const callPhone = () => {
      if (tripData.value.contact.phone) {
        common_vendor.index.makePhoneCall({
          phoneNumber: tripData.value.contact.phone.replace(/\*+/g, "")
        });
      } else {
        common_vendor.index.showToast({
          title: "电话号码不可用",
          icon: "none"
        });
      }
    };
    const openChat = () => {
      common_vendor.index.showToast({
        title: "私信功能开发中",
        icon: "none"
      });
    };
    const rateDriver = () => {
      common_vendor.index.showToast({
        title: "评价功能开发中",
        icon: "none"
      });
    };
    common_vendor.onMounted(() => {
      getSystemInfo();
      const pages = getCurrentPages();
      const currentPage = pages[pages.length - 1];
      const options = currentPage.options || {};
      if (options.id) {
        common_vendor.index.__f__("log", "at pages/publish/carpool-detail.vue:392", "获取拼车信息，ID:", options.id);
      }
    });
    __expose({
      onShareAppMessage() {
        return {
          title: `${tripData.value.startPoint}→${tripData.value.endPoint} ${tripData.value.date} ${tripData.value.startTime}出发`,
          path: `/pages/publish/carpool-detail?id=${tripData.value.id}`
        };
      }
    });
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_assets._imports_0$7,
        b: common_vendor.o(goBack),
        c: common_assets._imports_1$4,
        d: common_vendor.o(showActionSheet),
        e: statusBarHeight.value + "px",
        f: common_vendor.t(typeText.value),
        g: tripData.value.isVerified
      }, tripData.value.isVerified ? {} : {}, {
        h: common_vendor.n(tripData.value.type),
        i: common_vendor.t(tripData.value.startPoint),
        j: tripData.value.startAddress
      }, tripData.value.startAddress ? {
        k: common_vendor.t(tripData.value.startAddress)
      } : {}, {
        l: common_vendor.t(tripData.value.distance),
        m: common_vendor.t(tripData.value.endPoint),
        n: tripData.value.endAddress
      }, tripData.value.endAddress ? {
        o: common_vendor.t(tripData.value.endAddress)
      } : {}, {
        p: common_vendor.t(tripData.value.startPoint),
        q: common_vendor.t(tripData.value.endPoint),
        r: common_vendor.t(tripData.value.distance),
        s: common_vendor.t(tripData.value.duration),
        t: tripData.value.tripNotes
      }, tripData.value.tripNotes ? {
        v: common_vendor.t(tripData.value.tripNotes)
      } : {}, {
        w: common_vendor.t(tripData.value.date),
        x: common_vendor.t(tripData.value.startTime),
        y: tripData.value.type === "people-to-car"
      }, tripData.value.type === "people-to-car" ? {
        z: common_vendor.t(tripData.value.passengers)
      } : {}, {
        A: tripData.value.type === "car-to-people"
      }, tripData.value.type === "car-to-people" ? {
        B: common_vendor.t(tripData.value.remainingSeats)
      } : {}, {
        C: tripData.value.price
      }, tripData.value.price ? {
        D: common_vendor.t(tripData.value.price)
      } : {}, {
        E: tripData.value.driver.avatar,
        F: common_vendor.t(tripData.value.driver.name),
        G: tripData.value.driver.isVerified
      }, tripData.value.driver.isVerified ? {} : {}, {
        H: tripData.value.driver.isPremium
      }, tripData.value.driver.isPremium ? {} : {}, {
        I: common_vendor.t(formatTime(tripData.value.publishTime)),
        J: tripData.value.type === "car-to-people"
      }, tripData.value.type === "car-to-people" ? common_vendor.e({
        K: common_vendor.t(tripData.value.driver.ratingCount || 0),
        L: (tripData.value.driver.rating || 0) * 20 + "%",
        M: common_vendor.t(tripData.value.driver.rating || 0),
        N: tripData.value.driver.ratingTags && tripData.value.driver.ratingTags.length > 0
      }, tripData.value.driver.ratingTags && tripData.value.driver.ratingTags.length > 0 ? {
        O: common_vendor.f(tripData.value.driver.ratingTags, (tag, index, i0) => {
          return {
            a: common_vendor.t(tag),
            b: index
          };
        })
      } : {}) : {}, {
        P: tripData.value.type && tripData.value.type.includes("car-to")
      }, tripData.value.type && tripData.value.type.includes("car-to") ? {
        Q: common_vendor.t(tripData.value.carType),
        R: common_vendor.t(tripData.value.carColor || "未提供"),
        S: common_vendor.t(tripData.value.carPlate || "未提供")
      } : {}, {
        T: tripData.value.remark
      }, tripData.value.remark ? {
        U: common_vendor.t(tripData.value.remark)
      } : {}, {
        V: common_assets._imports_2$3,
        W: common_vendor.p({
          ["content-id"]: tripData.value.id,
          ["content-type"]: "carpool"
        }),
        X: common_vendor.t(formatTime(tripData.value.publishTime)),
        Y: common_vendor.t(tripData.value.expiryTime || "未设置"),
        Z: navigationBarHeight.value + "px",
        aa: common_assets._imports_3$3,
        ab: isCollected.value ? "/static/images/tabbar/a收藏选中.png" : "/static/images/tabbar/a收藏.png",
        ac: common_vendor.t(isCollected.value ? "已收藏" : "收藏"),
        ad: common_vendor.o(toggleCollect),
        ae: common_assets._imports_4,
        af: common_vendor.o(rateDriver),
        ag: common_assets._imports_5,
        ah: common_vendor.o(openChat),
        ai: common_assets._imports_6,
        aj: common_vendor.o(callPhone)
      });
    };
  }
};
_sfc_main.__runtimeHooks = 2;
wx.createPage(_sfc_main);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/publish/carpool-detail.js.map
