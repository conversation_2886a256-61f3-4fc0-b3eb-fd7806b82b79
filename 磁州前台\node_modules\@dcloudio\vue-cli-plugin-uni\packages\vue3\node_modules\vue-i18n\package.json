{"name": "vue-i18n", "version": "9.1.7", "description": "Internationalization plugin for Vue.js", "keywords": ["i18n", "internationalization", "intlify", "plugin", "vue", "vue.js"], "license": "MIT", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "homepage": "https://github.com/intlify/vue-i18n-next/tree/master/packages/vue-i18n#readme", "repository": {"type": "git", "url": "git+https://github.com/intlify/vue-i18n-next.git", "directory": "packages/vue-i18n"}, "bugs": {"url": "https://github.com/intlify/vue-i18n-next/issues"}, "files": ["index.js", "dist", "vetur"], "main": "index.js", "module": "dist/vue-i18n.esm-bundler.js", "unpkg": "dist/vue-i18n.global.js", "jsdelivr": "dist/vue-i18n.global.js", "types": "dist/vue-i18n.d.ts", "dependencies": {"@intlify/core-base": "9.1.7", "@intlify/shared": "9.1.7", "@intlify/vue-devtools": "9.1.7", "@vue/devtools-api": "^6.0.0-beta.7"}, "devDependencies": {"@intlify/devtools-if": "9.1.7"}, "peerDependencies": {"vue": "^3.0.0"}, "engines": {"node": ">= 10"}, "buildOptions": {"name": "VueI18n", "formats": ["esm-bundler", "esm-bundler-runtime", "esm-browser", "esm-browser-runtime", "cjs", "global", "global-runtime"]}, "sideEffects": false, "vetur": {"tags": "vetur/tags.json", "attributes": "vetur/attributes.json"}}