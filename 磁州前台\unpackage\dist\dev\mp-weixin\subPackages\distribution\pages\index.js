"use strict";
const common_vendor = require("../../../common/vendor.js");
const utils_distributionService = require("../../../utils/distributionService.js");
const _sfc_main = {
  __name: "index",
  setup(__props) {
    const userInfo = common_vendor.reactive({
      userId: "1001",
      // 模拟数据，实际应从用户系统获取
      nickname: "张三",
      avatar: "/static/images/default-avatar.png"
    });
    const distributorInfo = common_vendor.reactive({
      level: "普通分销员",
      totalCommission: 0,
      availableCommission: 0,
      pendingCommission: 0,
      withdrawnCommission: 0,
      inviteCode: ""
    });
    const isDistributor = common_vendor.ref(false);
    const recommendedProducts = common_vendor.ref([]);
    common_vendor.onMounted(async () => {
      await checkDistributorStatus();
      if (isDistributor.value) {
        await getRecommendedProducts();
      }
    });
    const checkDistributorStatus = async () => {
      try {
        isDistributor.value = await utils_distributionService.distributionService.isDistributor({
          userId: userInfo.userId
        });
        if (isDistributor.value) {
          const info = await utils_distributionService.distributionService.getDistributorInfo({
            userId: userInfo.userId
          });
          if (info) {
            Object.assign(distributorInfo, info);
          }
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at subPackages/distribution/pages/index.vue:215", "检查分销员状态失败", error);
        common_vendor.index.showToast({
          title: "获取分销员信息失败",
          icon: "none"
        });
      }
    };
    const getRecommendedProducts = async () => {
      try {
        const result = await utils_distributionService.distributionService.getDistributableProducts({
          page: 1,
          pageSize: 4,
          sortBy: "commission"
        });
        recommendedProducts.value = result.list || [];
      } catch (error) {
        common_vendor.index.__f__("error", "at subPackages/distribution/pages/index.vue:234", "获取推荐商品失败", error);
      }
    };
    const formatCommission = (amount) => {
      return utils_distributionService.distributionService.formatCommission(amount);
    };
    const navigateTo = (url) => {
      common_vendor.index.navigateTo({ url });
    };
    const navigateToProduct = (product) => {
      common_vendor.index.navigateTo({
        url: `/pages/product/detail?id=${product.id}&isDistribution=true`
      });
    };
    const goBack = () => {
      common_vendor.index.navigateBack();
    };
    const showHelp = () => {
      common_vendor.index.showModal({
        title: "我要赚钱帮助",
        content: "在我要赚钱，您可以查看佣金收益、管理团队、使用推广工具以及提现佣金等。",
        showCancel: false
      });
    };
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_vendor.o(goBack),
        b: common_vendor.o(showHelp),
        c: userInfo.avatar || "/static/images/default-avatar.png",
        d: common_vendor.t(userInfo.nickname || "游客"),
        e: common_vendor.t(distributorInfo.level || "普通用户"),
        f: distributorInfo.inviteCode
      }, distributorInfo.inviteCode ? {
        g: common_vendor.t(distributorInfo.inviteCode)
      } : {}, {
        h: !isDistributor.value
      }, !isDistributor.value ? {
        i: common_vendor.o(($event) => navigateTo("/subPackages/distribution/pages/apply"))
      } : {}, {
        j: isDistributor.value
      }, isDistributor.value ? {
        k: common_vendor.o(($event) => navigateTo("/subPackages/distribution/pages/commission")),
        l: common_vendor.t(formatCommission(distributorInfo.totalCommission)),
        m: common_vendor.t(formatCommission(distributorInfo.availableCommission)),
        n: common_vendor.t(formatCommission(distributorInfo.pendingCommission)),
        o: common_vendor.t(formatCommission(distributorInfo.withdrawnCommission)),
        p: common_vendor.o(($event) => navigateTo("/subPackages/distribution/pages/withdraw"))
      } : {}, {
        q: isDistributor.value
      }, isDistributor.value ? {
        r: common_vendor.o(($event) => navigateTo("/subPackages/distribution/pages/products")),
        s: common_vendor.o(($event) => navigateTo("/subPackages/distribution/pages/team")),
        t: common_vendor.o(($event) => navigateTo("/subPackages/distribution/pages/promotion")),
        v: common_vendor.o(($event) => navigateTo("/subPackages/distribution/pages/commission"))
      } : {}, {
        w: isDistributor.value
      }, isDistributor.value ? {
        x: common_vendor.o(($event) => navigateTo("/subPackages/distribution/pages/products")),
        y: common_vendor.f(recommendedProducts.value, (product, index, i0) => {
          return {
            a: product.image,
            b: common_vendor.t(product.name),
            c: common_vendor.t(product.price),
            d: common_vendor.t(product.commission),
            e: index,
            f: common_vendor.o(($event) => navigateToProduct(product), index)
          };
        })
      } : {}, {
        z: !isDistributor.value
      }, !isDistributor.value ? {
        A: common_vendor.o(($event) => navigateTo("/subPackages/distribution/pages/apply"))
      } : {});
    };
  }
};
wx.createPage(_sfc_main);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/subPackages/distribution/pages/index.js.map
