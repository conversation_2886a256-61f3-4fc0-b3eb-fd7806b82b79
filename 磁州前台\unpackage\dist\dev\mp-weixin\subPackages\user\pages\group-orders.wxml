<view class="group-orders-container"><view class="custom-navbar" style="{{'padding-top:' + c}}"><view class="navbar-left" bindtap="{{b}}"><image src="{{a}}" class="back-icon"></image></view><view class="navbar-title">团购订单</view><view class="navbar-right"></view></view><view class="filter-tabs" style="{{'margin-top:' + e}}"><view wx:for="{{d}}" wx:for-item="tab" wx:key="b" class="{{['tab-item', tab.c && 'active']}}" bindtap="{{tab.d}}"><text>{{tab.a}}</text></view></view><scroll-view scroll-y class="orders-list" bindscrolltolower="{{n}}" refresher-enabled refresher-triggered="{{o}}" bindrefresherrefresh="{{p}}"><view wx:if="{{f}}"><view wx:for="{{g}}" wx:for-item="item" wx:key="B" class="order-item" bindtap="{{item.C}}"><view class="order-header"><view class="shop-info"><image class="shop-avatar" src="{{item.a}}" mode="aspectFill"></image><text class="shop-name">{{item.b}}</text><image class="shop-arrow" src="{{h}}" mode="aspectFit"></image></view><view class="{{['order-status', item.d]}}">{{item.c}}</view></view><view class="order-content"><image class="goods-image" src="{{item.e}}" mode="aspectFill"></image><view class="goods-info"><view class="goods-name">{{item.f}}</view><view wx:if="{{item.g}}" class="goods-spec">{{item.h}}</view><view class="goods-price-count"><text class="goods-price">¥{{item.i}}</text><text class="goods-count">x{{item.j}}</text></view></view></view><view class="order-footer"><view class="order-time">下单时间：{{item.k}}</view><view class="order-total"><text>共{{item.l}}件商品</text><text class="total-price">实付：<text class="price-num">¥{{item.m}}</text></text></view></view><view class="order-actions"><view wx:if="{{item.n}}" class="action-btn cancel-btn" catchtap="{{item.o}}">取消订单</view><view wx:if="{{item.p}}" class="action-btn pay-btn" catchtap="{{item.q}}">立即付款</view><view wx:if="{{item.r}}" class="action-btn" catchtap="{{item.s}}">查看配送</view><view wx:if="{{item.t}}" class="action-btn" catchtap="{{item.v}}">确认收货</view><view wx:if="{{item.w}}" class="action-btn" catchtap="{{item.x}}">评价</view><view wx:if="{{item.y}}" class="action-btn" catchtap="{{item.z}}">删除订单</view><view class="action-btn contact-btn" catchtap="{{item.A}}">联系商家</view></view></view></view><view wx:if="{{i}}" class="empty-state"><image src="{{j}}" class="empty-icon"></image><view class="empty-text">暂无团购订单</view><view class="empty-subtext">去看看有哪些优惠的团购活动吧</view><view class="empty-btn" bindtap="{{k}}">浏览团购</view></view><view wx:if="{{l}}" class="loading-state"><view class="loading-icon"></view><text>加载中...</text></view><view wx:if="{{m}}" class="load-all"><text>已加载全部订单</text></view></scroll-view></view>