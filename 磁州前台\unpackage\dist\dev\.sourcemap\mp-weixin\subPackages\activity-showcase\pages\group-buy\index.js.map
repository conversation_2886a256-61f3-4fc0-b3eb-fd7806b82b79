{"version": 3, "file": "index.js", "sources": ["subPackages/activity-showcase/pages/group-buy/index.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/c3ViUGFja2FnZXNcYWN0aXZpdHktc2hvd2Nhc2VccGFnZXNcZ3JvdXAtYnV5XGluZGV4LnZ1ZQ"], "sourcesContent": ["<template>\r\n  <view class=\"group-buy-page\">\r\n    <!-- 自定义导航栏 -->\r\n    <view class=\"custom-navbar\">\r\n      <view class=\"navbar-bg\"></view>\r\n      <view class=\"navbar-content\">\r\n        <view class=\"back-btn\" @click=\"goBack\">\r\n          <image class=\"back-icon\" src=\"/static/images/tabbar/最新返回键.png\" mode=\"aspectFit\"></image>\r\n        </view>\r\n        <view class=\"navbar-title\">拼团活动</view>\r\n        <view class=\"navbar-right\">\r\n          <view class=\"search-btn\" @click=\"navigateToSearch\">\r\n            <svg class=\"icon\" viewBox=\"0 0 1024 1024\" xmlns=\"http://www.w3.org/2000/svg\" width=\"22\" height=\"22\">\r\n              <path d=\"M909.6 854.5L649.9 594.8C690.2 542.7 712 479 712 412c0-80.2-31.3-155.4-87.9-212.1-56.6-56.7-132-87.9-212.1-87.9s-155.5 31.3-212.1 87.9C143.2 256.5 112 331.8 112 412c0 80.1 31.3 155.5 87.9 212.1C256.5 680.8 331.8 712 412 712c67 0 130.6-21.8 182.7-62l259.7 259.6c3.2 3.2 8.4 3.2 11.6 0l43.6-43.5c3.2-3.2 3.2-8.4 0-11.6zM570.4 570.4C528 612.7 471.8 636 412 636s-116-23.3-158.4-65.6C211.3 528 188 471.8 188 412s23.3-116.1 65.6-158.4C296 211.3 352.2 188 412 188s116.1 23.2 158.4 65.6S636 352.2 636 412s-23.3 116.1-65.6 158.4z\" fill=\"#FFFFFF\"></path>\r\n            </svg>\r\n          </view>\r\n          <view class=\"close-btn\" @click=\"goBack\">\r\n            <svg class=\"icon\" viewBox=\"0 0 1024 1024\" xmlns=\"http://www.w3.org/2000/svg\" width=\"22\" height=\"22\">\r\n              <path d=\"M512 421.490332L331.349941 240.840273c-24.988383-24.988383-65.35828-24.988383-90.346664 0-24.988383 24.988383-24.988383 65.35828 0 90.346664L421.653336 512 240.840273 692.812059c-24.988383 24.988383-24.988383 65.35828 0 90.346664 24.988383 24.988383 65.35828 24.988383 90.346664 0L512 602.509668l180.650059 180.650059c24.988383 24.988383 65.35828 24.988383 90.346664 0 24.988383-24.988383 24.988383-65.35828 0-90.346664L602.346664 512l180.813063-180.812059c24.988383-24.988383 24.988383-65.35828 0-90.346664-24.988383-24.988383-65.35828-24.988383-90.346664 0L512 421.490332z\" fill=\"#FFFFFF\"></path>\r\n            </svg>\r\n          </view>\r\n        </view>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 分类选项卡 -->\r\n    <view class=\"category-tabs\">\r\n      <view \r\n        class=\"tab-item\" \r\n        v-for=\"(tab, index) in tabs\" \r\n        :key=\"index\"\r\n        :class=\"{ active: currentTabIndex === index }\"\r\n        @click=\"switchTab(index)\"\r\n      >\r\n        <text>{{ tab.name }}</text>\r\n      </view>\r\n      <view class=\"tab-line\" :style=\"tabLineStyle\"></view>\r\n    </view>\r\n    \r\n    <!-- 内容区域 -->\r\n    <scroll-view \r\n      class=\"content-scroll\" \r\n      scroll-y \r\n      @scrolltolower=\"loadMore\"\r\n    >\r\n      <!-- 轮播广告 -->\r\n      <swiper class=\"banner-swiper\" indicator-dots autoplay circular\r\n        :indicator-color=\"'rgba(255, 255, 255, 0.6)'\"\r\n        :indicator-active-color=\"'#ffffff'\"\r\n        v-if=\"currentTabIndex === 0\">\r\n        <swiper-item v-for=\"(banner, index) in banners\" :key=\"index\">\r\n          <image class=\"banner-image\" :src=\"banner.image\" mode=\"aspectFill\"></image>\r\n        </swiper-item>\r\n      </swiper>\r\n      \r\n      <!-- 分类图标 -->\r\n      <view class=\"category-icons\" v-if=\"currentTabIndex === 0\">\r\n        <view class=\"category-item\" v-for=\"(category, index) in categories\" :key=\"index\" @click=\"filterByCategory(category)\">\r\n          <image class=\"category-icon\" :src=\"category.icon\" mode=\"aspectFill\"></image>\r\n          <text class=\"category-name\">{{ category.name }}</text>\r\n        </view>\r\n      </view>\r\n      \r\n      <!-- 限时拼团 -->\r\n      <view class=\"section limited-time\" v-if=\"currentTabIndex === 0\">\r\n        <view class=\"section-header\">\r\n          <text class=\"section-title\">限时拼团</text>\r\n          <view class=\"countdown\">\r\n            <text class=\"countdown-label\">距结束</text>\r\n            <text class=\"countdown-time\">{{ limitedTimeCountdown }}</text>\r\n          </view>\r\n        </view>\r\n        \r\n        <scroll-view class=\"limited-scroll\" scroll-x>\r\n          <view class=\"limited-items\">\r\n            <view class=\"limited-item\" v-for=\"(item, index) in limitedTimeItems\" :key=\"index\" @click=\"navigateToDetail(item.id)\">\r\n              <image class=\"item-image\" :src=\"item.image\" mode=\"aspectFill\"></image>\r\n              <view class=\"item-info\">\r\n                <view class=\"item-title\">{{ item.title }}</view>\r\n                <view class=\"price-container\">\r\n                  <text class=\"group-price\">¥{{ item.groupPrice }}</text>\r\n                  <text class=\"daily-price\">¥{{ item.dailyPrice }}</text>\r\n                  <text class=\"original-price\">¥{{ item.marketPrice }}</text>\r\n                </view>\r\n                <view class=\"group-info\">\r\n                  <text class=\"group-count\">{{ item.groupCount }}人团</text>\r\n                  <view class=\"join-btn\">去拼团</view>\r\n                </view>\r\n              </view>\r\n            </view>\r\n          </view>\r\n        </scroll-view>\r\n      </view>\r\n      \r\n      <!-- 拼团列表 -->\r\n      <view class=\"section group-list-section\">\r\n        <view class=\"section-header\" v-if=\"currentTabIndex === 0\">\r\n          <text class=\"section-title\">热门拼团</text>\r\n        </view>\r\n        \r\n        <view class=\"group-items\">\r\n          <view \r\n            class=\"group-item\" \r\n            v-for=\"(item, index) in groupItems\" \r\n            :key=\"index\"\r\n            @click=\"navigateToDetail(item.id)\"\r\n          >\r\n            <view class=\"item-image-container\">\r\n              <image class=\"item-image\" :src=\"item.image\" mode=\"aspectFill\"></image>\r\n              <view class=\"item-tag\" v-if=\"item.tag\">{{ item.tag }}</view>\r\n            </view>\r\n            <view class=\"item-content\">\r\n              <view class=\"item-title\">{{ item.title }}</view>\r\n              <view class=\"item-desc\">{{ item.description }}</view>\r\n              \r\n              <!-- 三种价格醒目比对 -->\r\n              <view class=\"price-comparison\">\r\n                <view class=\"price-row\">\r\n                  <text class=\"price-label\">拼团价</text>\r\n                  <text class=\"price-value group-price\">¥{{ item.groupPrice }}</text>\r\n                </view>\r\n                <view class=\"price-row\">\r\n                  <text class=\"price-label\">日常价</text>\r\n                  <text class=\"price-value daily-price\">¥{{ item.dailyPrice }}</text>\r\n                </view>\r\n                <view class=\"price-row\">\r\n                  <text class=\"price-label\">市场价</text>\r\n                  <text class=\"price-value market-price\">¥{{ item.marketPrice }}</text>\r\n                </view>\r\n                <view class=\"price-save\">\r\n                  <text class=\"save-text\">拼团立省¥{{ (item.marketPrice - item.groupPrice).toFixed(2) }}</text>\r\n                </view>\r\n              </view>\r\n              \r\n              <view class=\"item-footer\">\r\n                <view class=\"group-progress\">\r\n                  <view class=\"avatars\">\r\n                    <image \r\n                      class=\"user-avatar\" \r\n                      v-for=\"(avatar, avatarIndex) in item.userAvatars.slice(0, 3)\" \r\n                      :key=\"avatarIndex\" \r\n                      :src=\"avatar\"\r\n                    ></image>\r\n                    <view class=\"avatar-count\" v-if=\"item.joinCount > 3\">+{{ item.joinCount - 3 }}</view>\r\n                  </view>\r\n                  <text class=\"progress-text\">{{ item.joinCount }}人已参团</text>\r\n                </view>\r\n                <view class=\"action-btn\">\r\n                  <text>{{ item.groupCount }}人团 ¥{{ item.groupPrice }}</text>\r\n                </view>\r\n              </view>\r\n            </view>\r\n          </view>\r\n        </view>\r\n      </view>\r\n      \r\n      <!-- 我的拼团 -->\r\n      <view class=\"section my-groups-section\" v-if=\"currentTabIndex === 1\">\r\n        <view v-if=\"myGroups.length === 0\" class=\"empty-tip\">\r\n          <image class=\"empty-image\" src=\"/static/images/empty-group.png\" mode=\"aspectFit\"></image>\r\n          <text class=\"empty-text\">您还没有参与任何拼团活动</text>\r\n          <view class=\"empty-btn\" @click=\"switchTab(0)\">\r\n            <text>去拼团</text>\r\n          </view>\r\n        </view>\r\n        <view v-else class=\"my-group-list\">\r\n          <view \r\n            class=\"my-group-item\" \r\n            v-for=\"(item, index) in myGroups\" \r\n            :key=\"index\"\r\n            @click=\"navigateToDetail(item.id)\"\r\n          >\r\n            <view class=\"item-image-container\">\r\n              <image class=\"item-image\" :src=\"item.image\" mode=\"aspectFill\"></image>\r\n              <view class=\"item-status-tag\" :class=\"item.status\">{{ getStatusText(item.status) }}</view>\r\n            </view>\r\n            <view class=\"item-content\">\r\n              <view class=\"item-title\">{{ item.title }}</view>\r\n              \r\n              <!-- 三种价格比对 -->\r\n              <view class=\"price-comparison compact\">\r\n                <view class=\"price-row\">\r\n                  <text class=\"price-label\">拼团价</text>\r\n                  <text class=\"price-value group-price\">¥{{ item.groupPrice }}</text>\r\n                </view>\r\n                <view class=\"price-row\">\r\n                  <text class=\"price-label\">日常价</text>\r\n                  <text class=\"price-value daily-price\">¥{{ item.dailyPrice }}</text>\r\n                </view>\r\n              </view>\r\n              \r\n              <view class=\"group-status\">\r\n                <view class=\"status-info\">\r\n                  <text class=\"status-text\" :class=\"item.status\">{{ getStatusText(item.status) }}</text>\r\n                  <text class=\"status-desc\" v-if=\"item.status === 'pending'\">\r\n                    还差{{ item.groupCount - item.joinCount }}人成团\r\n                  </text>\r\n                </view>\r\n                <view class=\"status-action\" v-if=\"item.status === 'pending'\">\r\n                  <view class=\"share-btn\" @click.stop=\"shareGroup(item.id)\">邀请好友</view>\r\n                </view>\r\n              </view>\r\n            </view>\r\n          </view>\r\n        </view>\r\n      </view>\r\n      \r\n      <!-- 加载更多提示 -->\r\n      <view class=\"loading-more\" v-if=\"loading\">\r\n        <text>加载中...</text>\r\n      </view>\r\n      \r\n      <!-- 到底了提示 -->\r\n      <view class=\"no-more\" v-if=\"noMore\">\r\n        <text>已经到底啦~</text>\r\n      </view>\r\n    </scroll-view>\r\n    \r\n    <!-- 浮动按钮 -->\r\n    <view class=\"float-btn\" @click=\"scrollToTop\">\r\n      <view class=\"btn-icon\">\r\n        <svg class=\"icon\" viewBox=\"0 0 1024 1024\" xmlns=\"http://www.w3.org/2000/svg\" width=\"20\" height=\"20\">\r\n          <path d=\"M512 352L188 676c-12.3 12.3-32.2 12.3-44.5 0-12.3-12.3-12.3-32.2 0-44.5l346-346c6.2-6.2 14.3-9.3 22.3-9.3s16.1 3.1 22.3 9.3l346 346c12.3 12.3 12.3 32.2 0 44.5-12.3 12.3-32.2 12.3-44.5 0L512 352z\" fill=\"#FFFFFF\"></path>\r\n        </svg>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 底部导航 -->\r\n    <view class=\"bottom-tabbar\">\r\n      <view class=\"tab-item\" @click=\"navigateToHome\">\r\n        <view class=\"tab-icon\">\r\n          <svg class=\"icon\" viewBox=\"0 0 1024 1024\" xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\">\r\n            <path d=\"M512 128L128 447.936V896h255.936V640H640v256h255.936V447.936L512 128z\" :fill=\"isHomePage ? '#FF2C54' : '#666666'\"></path>\r\n          </svg>\r\n        </view>\r\n        <text class=\"tab-text\" :class=\"{ active: isHomePage }\">首页</text>\r\n      </view>\r\n      <view class=\"tab-item\" @click=\"navigateToCategory\">\r\n        <view class=\"tab-icon\">\r\n          <svg class=\"icon\" viewBox=\"0 0 1024 1024\" xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\">\r\n            <path d=\"M320 320H192V192h128v128zm256 0H448V192h128v128zm256 0H704V192h128v128zM320 576H192V448h128v128zm256 0H448V448h128v128zm256 0H704V448h128v128zM320 832H192V704h128v128zm256 0H448V704h128v128zm256 0H704V704h128v128z\" fill=\"#666666\"></path>\r\n          </svg>\r\n        </view>\r\n        <text class=\"tab-text\">分类</text>\r\n      </view>\r\n      <view class=\"tab-item\" @click=\"navigateToCart\">\r\n        <view class=\"tab-icon\">\r\n          <svg class=\"icon\" viewBox=\"0 0 1024 1024\" xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\">\r\n            <path d=\"M922.9 701.9H327.4l29.9-60.9 496.8-.9c16.8 0 31.2-12 34.2-28.6l68.8-385.1c1.8-10.1-.9-20.5-7.5-28.4a34.99 34.99 0 0 0-26.6-12.5l-632-2.1-5.4-25.4c-3.4-16.2-18-28-34.6-28H96.5a35.3 35.3 0 1 0 0 70.6h125.9L246 312l58.1 281.3-74.8 122.1a34.96 34.96 0 0 0-3 36.8c6 11.9 18.1 19.4 31.5 19.4h62.8a102.43 102.43 0 0 0-20.6 61.7c0 56.6 46 102.6 102.6 102.6s102.6-46 102.6-102.6c0-22.3-7.4-44-20.6-61.7h161.1a102.43 102.43 0 0 0-20.6 61.7c0 56.6 46 102.6 102.6 102.6s102.6-46 102.6-102.6c0-22.3-7.4-44-20.6-61.7H923c19.4 0 35.3-15.8 35.3-35.3a35.42 35.42 0 0 0-35.4-35.2zM305.7 253l575.8 1.9-56.4 315.8-452.3.8L305.7 253zm96.9 612.7c-17.4 0-31.6-14.2-31.6-31.6 0-17.4 14.2-31.6 31.6-31.6s31.6 14.2 31.6 31.6a31.6 31.6 0 0 1-31.6 31.6zm325.1 0c-17.4 0-31.6-14.2-31.6-31.6 0-17.4 14.2-31.6 31.6-31.6s31.6 14.2 31.6 31.6a31.6 31.6 0 0 1-31.6 31.6z\" fill=\"#666666\"></path>\r\n          </svg>\r\n        </view>\r\n        <text class=\"tab-text\">购物车</text>\r\n      </view>\r\n      <view class=\"tab-item\" @click=\"navigateToMine\">\r\n        <view class=\"tab-icon\">\r\n          <svg class=\"icon\" viewBox=\"0 0 1024 1024\" xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\">\r\n            <path d=\"M858.5 763.6a374 374 0 0 0-80.6-119.5 375.63 375.63 0 0 0-119.5-80.6c-.4-.2-.8-.3-1.2-.5C719.5 518 760 444.7 760 362c0-137-111-248-248-248S264 225 264 362c0 82.7 40.5 156 102.8 201.1-.4.2-.8.3-1.2.5-44.8 18.9-85 46-119.5 80.6a375.63 375.63 0 0 0-80.6 119.5A371.7 371.7 0 0 0 136 901.8a8 8 0 0 0 8 8.2h60c4.4 0 7.9-3.5 8-7.8 2-77.2 33-149.5 87.8-204.3 56.7-56.7 132-87.9 212.2-87.9s155.5 31.2 212.2 87.9C779 752.7 810 825 812 902.2c.1 4.4 3.6 7.8 8 7.8h60a8 8 0 0 0 8-8.2c-1-47.8-10.9-94.3-29.5-138.2zM512 534c-45.9 0-89.1-17.9-121.6-50.4S340 407.9 340 362c0-45.9 17.9-89.1 50.4-121.6S466.1 190 512 190s89.1 17.9 121.6 50.4S684 316.1 684 362c0 45.9-17.9 89.1-50.4 121.6S557.9 534 512 534z\" fill=\"#666666\"></path>\r\n          </svg>\r\n        </view>\r\n        <text class=\"tab-text\">我的</text>\r\n      </view>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  data() {\r\n    return {\r\n      refreshing: false,\r\n      loading: false,\r\n      noMore: false,\r\n      currentTabIndex: 0,\r\n      isHomePage: false,\r\n      tabs: [\r\n        { name: '全部拼团' },\r\n        { name: '我的拼团' }\r\n      ],\r\n      banners: [\r\n        { image: 'https://via.placeholder.com/750x300', link: '' },\r\n        { image: 'https://via.placeholder.com/750x300', link: '' },\r\n        { image: 'https://via.placeholder.com/750x300', link: '' }\r\n      ],\r\n      categories: [\r\n        { name: '美食', icon: 'https://via.placeholder.com/60x60', id: 'food' },\r\n        { name: '电器', icon: 'https://via.placeholder.com/60x60', id: 'appliance' },\r\n        { name: '服饰', icon: 'https://via.placeholder.com/60x60', id: 'clothing' },\r\n        { name: '美妆', icon: 'https://via.placeholder.com/60x60', id: 'beauty' },\r\n        { name: '家居', icon: 'https://via.placeholder.com/60x60', id: 'home' }\r\n      ],\r\n      limitedTimeItems: [\r\n        {\r\n          id: 1,\r\n          image: 'https://via.placeholder.com/200x200',\r\n          title: '高端智能扫地机器人',\r\n          marketPrice: '3999',\r\n          dailyPrice: '2999',\r\n          groupPrice: '1999',\r\n          groupCount: 3\r\n        },\r\n        {\r\n          id: 2,\r\n          image: 'https://via.placeholder.com/200x200',\r\n          title: '九阳豆浆机',\r\n          marketPrice: '599',\r\n          dailyPrice: '499',\r\n          groupPrice: '299',\r\n          groupCount: 2\r\n        },\r\n        {\r\n          id: 3,\r\n          image: 'https://via.placeholder.com/200x200',\r\n          title: '小米空气净化器',\r\n          marketPrice: '999',\r\n          dailyPrice: '899',\r\n          groupPrice: '699',\r\n          groupCount: 3\r\n        }\r\n      ],\r\n      limitedTimeCountdown: '23:59:59',\r\n      groupItems: [\r\n        {\r\n          id: 2,\r\n          image: 'https://via.placeholder.com/300x300',\r\n          title: '九阳豆浆机',\r\n          description: '破壁免滤双预约多功能',\r\n          groupPrice: '299',\r\n          dailyPrice: '499',\r\n          marketPrice: '599',\r\n          tag: '热门',\r\n          groupCount: 2,\r\n          joinCount: 156,\r\n          userAvatars: [\r\n            'https://via.placeholder.com/50x50',\r\n            'https://via.placeholder.com/50x50',\r\n            'https://via.placeholder.com/50x50',\r\n            'https://via.placeholder.com/50x50'\r\n          ],\r\n          endTime: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000).toISOString()\r\n        },\r\n        {\r\n          id: 3,\r\n          image: 'https://via.placeholder.com/300x300',\r\n          title: '小米空气净化器',\r\n          description: '除菌除醛除异味',\r\n          groupPrice: '699',\r\n          dailyPrice: '899',\r\n          marketPrice: '999',\r\n          tag: '爆款',\r\n          groupCount: 3,\r\n          joinCount: 78,\r\n          userAvatars: [\r\n            'https://via.placeholder.com/50x50',\r\n            'https://via.placeholder.com/50x50'\r\n          ],\r\n          endTime: new Date(Date.now() + 2 * 24 * 60 * 60 * 1000).toISOString()\r\n        },\r\n        {\r\n          id: 4,\r\n          image: 'https://via.placeholder.com/300x300',\r\n          title: '华为智能手表',\r\n          description: '心率监测，睡眠分析，运动追踪',\r\n          groupPrice: '899',\r\n          dailyPrice: '1099',\r\n          marketPrice: '1299',\r\n          tag: '新品',\r\n          groupCount: 2,\r\n          joinCount: 45,\r\n          userAvatars: [\r\n            'https://via.placeholder.com/50x50',\r\n            'https://via.placeholder.com/50x50',\r\n            'https://via.placeholder.com/50x50'\r\n          ],\r\n          endTime: new Date(Date.now() + 4 * 24 * 60 * 60 * 1000).toISOString()\r\n        },\r\n        {\r\n          id: 5,\r\n          image: 'https://via.placeholder.com/300x300',\r\n          title: '飞利浦电动牙刷',\r\n          description: '声波震动，智能提醒，长效续航',\r\n          groupPrice: '399',\r\n          dailyPrice: '599',\r\n          marketPrice: '699',\r\n          tag: '限量',\r\n          groupCount: 5,\r\n          joinCount: 230,\r\n          userAvatars: [\r\n            'https://via.placeholder.com/50x50',\r\n            'https://via.placeholder.com/50x50'\r\n          ],\r\n          endTime: new Date(Date.now() + 1 * 24 * 60 * 60 * 1000).toISOString()\r\n        }\r\n      ],\r\n      myGroups: [\r\n        {\r\n          id: 6,\r\n          image: 'https://via.placeholder.com/300x300',\r\n          title: '小米空气净化器',\r\n          description: '除菌除醛除异味',\r\n          groupPrice: '699',\r\n          dailyPrice: '899',\r\n          marketPrice: '999',\r\n          groupCount: 3,\r\n          joinCount: 2,\r\n          status: 'pending',\r\n          endTime: new Date(Date.now() + 12 * 60 * 60 * 1000).toISOString()\r\n        },\r\n        {\r\n          id: 7,\r\n          image: 'https://via.placeholder.com/300x300',\r\n          title: '华为智能手表',\r\n          description: '心率监测，睡眠分析，运动追踪',\r\n          groupPrice: '899',\r\n          dailyPrice: '1099',\r\n          marketPrice: '1299',\r\n          groupCount: 2,\r\n          joinCount: 2,\r\n          status: 'success',\r\n          endTime: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString()\r\n        },\r\n        {\r\n          id: 8,\r\n          image: 'https://via.placeholder.com/300x300',\r\n          title: '飞利浦电动牙刷',\r\n          description: '声波震动，智能提醒，长效续航',\r\n          groupPrice: '399',\r\n          dailyPrice: '599',\r\n          marketPrice: '699',\r\n          groupCount: 5,\r\n          joinCount: 3,\r\n          status: 'failed',\r\n          endTime: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString()\r\n        }\r\n      ]\r\n    }\r\n  },\r\n  computed: {\r\n    tabLineStyle() {\r\n      const width = 100 / this.tabs.length\r\n      const left = this.currentTabIndex * width\r\n      return {\r\n        width: width + '%',\r\n        transform: `translateX(${left * 100}%)`\r\n      }\r\n    }\r\n  },\r\n  onLoad() {\r\n    // 页面加载时获取数据\r\n    this.fetchData()\r\n    // 启动倒计时\r\n    this.startCountdown()\r\n    // 检查是否是首页\r\n    const pages = getCurrentPages()\r\n    if (pages.length === 1) {\r\n      this.isHomePage = true\r\n    }\r\n  },\r\n  methods: {\r\n    // 返回上一页\r\n    goBack() {\r\n      uni.navigateBack()\r\n    },\r\n    \r\n    // 切换选项卡\r\n    switchTab(index) {\r\n      this.currentTabIndex = index\r\n      this.fetchData()\r\n    },\r\n    \r\n    // 加载更多\r\n    loadMore() {\r\n      if (this.loading || this.noMore) return\r\n      \r\n      this.loading = true\r\n      \r\n      // 模拟加载更多数据\r\n      setTimeout(() => {\r\n        if (this.currentTabIndex === 0) {\r\n          // 添加更多拼团数据\r\n          const moreItems = [\r\n            {\r\n              id: 9,\r\n              image: 'https://via.placeholder.com/300x300',\r\n              title: '德尔玛加湿器',\r\n              description: '大容量，静音设计，智能恒湿',\r\n              groupPrice: '199',\r\n              dailyPrice: '249',\r\n              marketPrice: '299',\r\n              tag: '特惠',\r\n              groupCount: 2,\r\n              joinCount: 67,\r\n              userAvatars: [\r\n                'https://via.placeholder.com/50x50',\r\n                'https://via.placeholder.com/50x50'\r\n              ],\r\n              endTime: new Date(Date.now() + 5 * 24 * 60 * 60 * 1000).toISOString()\r\n            },\r\n            {\r\n              id: 10,\r\n              image: 'https://via.placeholder.com/300x300',\r\n              title: '小熊酸奶机',\r\n              description: '家用全自动，陶瓷内胆',\r\n              groupPrice: '159',\r\n              dailyPrice: '199',\r\n              marketPrice: '259',\r\n              tag: '热卖',\r\n              groupCount: 3,\r\n              joinCount: 120,\r\n              userAvatars: [\r\n                'https://via.placeholder.com/50x50'\r\n              ],\r\n              endTime: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000).toISOString()\r\n            }\r\n          ]\r\n          \r\n          this.groupItems = [...this.groupItems, ...moreItems]\r\n        }\r\n        \r\n        this.noMore = true // 示例中加载一次后就没有更多数据\r\n        this.loading = false\r\n      }, 1500)\r\n    },\r\n    \r\n    // 获取数据\r\n    fetchData() {\r\n      // 实际项目中，这里应该根据当前选中的选项卡调用API获取数据\r\n      // 这里将在下一步实现\r\n    },\r\n    \r\n    // 导航到详情页\r\n    navigateToDetail(id) {\r\n      uni.navigateTo({\r\n        url: `/subPackages/activity-showcase/pages/detail/index?id=${id}&type=group`\r\n      })\r\n    },\r\n    \r\n    // 导航到搜索页\r\n    navigateToSearch() {\r\n      uni.navigateTo({\r\n        url: '/subPackages/activity-showcase/pages/search/index'\r\n      })\r\n    },\r\n    \r\n    // 按分类筛选\r\n    filterByCategory(category) {\r\n      uni.showToast({\r\n        title: `已选择${category.name}分类`,\r\n        icon: 'none'\r\n      })\r\n      // 实际项目中应该根据分类筛选数据\r\n    },\r\n    \r\n    // 启动倒计时\r\n    startCountdown() {\r\n      // 模拟倒计时\r\n      let hours = 23\r\n      let minutes = 59\r\n      let seconds = 59\r\n      \r\n      setInterval(() => {\r\n        seconds--\r\n        if (seconds < 0) {\r\n          seconds = 59\r\n          minutes--\r\n          if (minutes < 0) {\r\n            minutes = 59\r\n            hours--\r\n            if (hours < 0) {\r\n              hours = 23\r\n            }\r\n          }\r\n        }\r\n        \r\n        this.limitedTimeCountdown = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`\r\n      }, 1000)\r\n    },\r\n    \r\n    // 获取状态文本\r\n    getStatusText(status) {\r\n      const statusMap = {\r\n        'pending': '拼团中',\r\n        'success': '拼团成功',\r\n        'failed': '拼团失败'\r\n      }\r\n      return statusMap[status] || ''\r\n    },\r\n    \r\n    // 分享拼团\r\n    shareGroup(id) {\r\n      uni.showShareMenu({\r\n        withShareTicket: true,\r\n        menus: ['shareAppMessage', 'shareTimeline']\r\n      })\r\n    },\r\n    \r\n    // 滚动到顶部\r\n    scrollToTop() {\r\n      uni.pageScrollTo({\r\n        scrollTop: 0,\r\n        duration: 300\r\n      })\r\n    },\r\n    \r\n    // 导航到首页\r\n    navigateToHome() {\r\n      if (!this.isHomePage) {\r\n        uni.switchTab({\r\n          url: '/pages/index/index'\r\n        })\r\n      }\r\n    },\r\n    \r\n    // 导航到分类页\r\n    navigateToCategory() {\r\n      uni.switchTab({\r\n        url: '/pages/category/index'\r\n      })\r\n    },\r\n    \r\n    // 导航到购物车\r\n    navigateToCart() {\r\n      uni.switchTab({\r\n        url: '/pages/cart/index'\r\n      })\r\n    },\r\n    \r\n    // 导航到我的页面\r\n    navigateToMine() {\r\n      uni.switchTab({\r\n        url: '/pages/mine/index'\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.group-buy-page {\r\n  display: flex;\r\n  flex-direction: column;\r\n  height: 100vh;\r\n  background-color: #F5F5F7;\r\n}\r\n\r\n/* 自定义导航栏 */\r\n.custom-navbar {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  height: calc(var(--status-bar-height, 25px) + 62px);\r\n  width: 100%;\r\n  z-index: 100;\r\n  \r\n  .navbar-bg {\r\n    position: absolute;\r\n    top: 0;\r\n    left: 0;\r\n    width: 100%;\r\n    height: 100%;\r\n    background: linear-gradient(135deg, #FF2C54 0%, #FF4F64 100%);\r\n    box-shadow: 0 4rpx 20rpx rgba(255, 44, 84, 0.3);\r\n  }\r\n  \r\n  .navbar-content {\r\n    position: relative;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n    height: 100%;\r\n    padding-top: var(--status-bar-height, 25px);\r\n    padding-left: 30rpx;\r\n    padding-right: 30rpx;\r\n    box-sizing: border-box;\r\n    \r\n    .back-btn {\r\n      width: 40rpx;\r\n      height: 40rpx;\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: center;\r\n      background: rgba(255, 255, 255, 0.2);\r\n      backdrop-filter: blur(10px);\r\n      border-radius: 50%;\r\n      padding: 16rpx;\r\n    }\r\n    \r\n    .back-icon {\r\n      width: 100%;\r\n      height: 100%;\r\n    }\r\n    \r\n    .navbar-title {\r\n      font-size: 18px;\r\n      font-weight: 600;\r\n      color: #FFFFFF;\r\n      position: absolute;\r\n      left: 50%;\r\n      transform: translateX(-50%);\r\n      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\r\n    }\r\n    \r\n    .navbar-right {\r\n      width: 160rpx;\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: flex-end;\r\n      gap: 20rpx;\r\n      \r\n      .search-btn, .close-btn {\r\n        width: 64rpx;\r\n        height: 64rpx;\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n        background: rgba(255, 255, 255, 0.2);\r\n        backdrop-filter: blur(10px);\r\n        border-radius: 50%;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n/* 分类选项卡 */\r\n.category-tabs {\r\n  position: relative;\r\n  display: flex;\r\n  background-color: #FFFFFF;\r\n  height: 88rpx;\r\n  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);\r\n  border-radius: 0 0 35rpx 35rpx;\r\n  margin: 0 20rpx;\r\n  margin-top: calc(var(--status-bar-height, 25px) + 62px);\r\n  \r\n  .tab-item {\r\n    flex: 1;\r\n    display: flex;\r\n    justify-content: center;\r\n    align-items: center;\r\n    font-size: 28rpx;\r\n    color: #666666;\r\n    position: relative;\r\n    \r\n    &.active {\r\n      color: #FF2C54;\r\n      font-weight: 600;\r\n    }\r\n  }\r\n  \r\n  .tab-line {\r\n    position: absolute;\r\n    bottom: 0;\r\n    left: 0;\r\n    height: 4rpx;\r\n    background-color: #FF2C54;\r\n    transition: transform 0.3s ease;\r\n    border-radius: 2rpx;\r\n  }\r\n}\r\n\r\n/* 内容区域 */\r\n.content-scroll {\r\n  flex: 1;\r\n  width: 100%;\r\n  padding: 0 20rpx;\r\n  padding-bottom: calc(100rpx + env(safe-area-inset-bottom));\r\n  margin-top: 20rpx;\r\n}\r\n\r\n/* 轮播图 */\r\n.banner-swiper {\r\n  width: 100%;\r\n  height: 300rpx;\r\n  border-radius: 35rpx;\r\n  overflow: hidden;\r\n  box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.08);\r\n  margin-bottom: 30rpx;\r\n  \r\n  .banner-image {\r\n    width: 100%;\r\n    height: 100%;\r\n  }\r\n}\r\n\r\n/* 分类图标 */\r\n.category-icons {\r\n  display: flex;\r\n  justify-content: space-around;\r\n  padding: 40rpx 20rpx;\r\n  background-color: #FFFFFF;\r\n  margin-bottom: 30rpx;\r\n  border-radius: 35rpx;\r\n  box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.05);\r\n  \r\n  .category-item {\r\n    display: flex;\r\n    flex-direction: column;\r\n    align-items: center;\r\n    \r\n    .category-icon {\r\n      width: 100rpx;\r\n      height: 100rpx;\r\n      border-radius: 35rpx;\r\n      margin-bottom: 16rpx;\r\n      box-shadow: 0 8rpx 16rpx rgba(0, 0, 0, 0.08);\r\n      transition: transform 0.3s ease;\r\n      \r\n      &:active {\r\n        transform: scale(0.95);\r\n      }\r\n    }\r\n    \r\n    .category-name {\r\n      font-size: 24rpx;\r\n      color: #333333;\r\n      font-weight: 500;\r\n    }\r\n  }\r\n}\r\n\r\n/* 通用section样式 */\r\n.section {\r\n  background-color: #FFFFFF;\r\n  margin-bottom: 30rpx;\r\n  padding: 30rpx 0;\r\n  border-radius: 35rpx;\r\n  box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.05);\r\n  \r\n  .section-header {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    padding: 0 30rpx 20rpx;\r\n    \r\n    .section-title {\r\n      font-size: 32rpx;\r\n      font-weight: 600;\r\n      color: #333333;\r\n      position: relative;\r\n      padding-left: 20rpx;\r\n      \r\n      &::before {\r\n        content: '';\r\n        position: absolute;\r\n        left: 0;\r\n        top: 50%;\r\n        transform: translateY(-50%);\r\n        width: 8rpx;\r\n        height: 32rpx;\r\n        background: linear-gradient(135deg, #FF2C54 0%, #FF4F64 100%);\r\n        border-radius: 4rpx;\r\n      }\r\n    }\r\n    \r\n    .countdown {\r\n      display: flex;\r\n      align-items: center;\r\n      background: rgba(255, 44, 84, 0.08);\r\n      border-radius: 20rpx;\r\n      padding: 6rpx 12rpx;\r\n      \r\n      .countdown-label {\r\n        font-size: 24rpx;\r\n        color: #666666;\r\n        margin-right: 10rpx;\r\n      }\r\n      \r\n      .countdown-time {\r\n        font-size: 24rpx;\r\n        color: #FF2C54;\r\n        font-weight: 600;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n/* 限时拼团 */\r\n.limited-time {\r\n  .limited-scroll {\r\n    width: 100%;\r\n    white-space: nowrap;\r\n    \r\n    .limited-items {\r\n      padding: 0 20rpx;\r\n      display: inline-block;\r\n      \r\n      .limited-item {\r\n        display: inline-block;\r\n        width: 240rpx;\r\n        margin-right: 20rpx;\r\n        background-color: #FFFFFF;\r\n        border-radius: 25rpx;\r\n        overflow: hidden;\r\n        box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.08);\r\n        transition: transform 0.3s ease;\r\n        \r\n        &:active {\r\n          transform: translateY(-5rpx);\r\n        }\r\n        \r\n        .item-image {\r\n          width: 240rpx;\r\n          height: 240rpx;\r\n          border-radius: 25rpx 25rpx 0 0;\r\n        }\r\n        \r\n        .item-info {\r\n          padding: 16rpx;\r\n          \r\n          .item-title {\r\n            font-size: 26rpx;\r\n            color: #333333;\r\n            white-space: normal;\r\n            display: -webkit-box;\r\n            -webkit-line-clamp: 2;\r\n            -webkit-box-orient: vertical;\r\n            overflow: hidden;\r\n            text-overflow: ellipsis;\r\n            height: 72rpx;\r\n            line-height: 1.4;\r\n            font-weight: 500;\r\n          }\r\n          \r\n          .price-container {\r\n            margin-top: 10rpx;\r\n            display: flex;\r\n            flex-wrap: wrap;\r\n            align-items: baseline;\r\n            \r\n            .group-price {\r\n              font-size: 32rpx;\r\n              color: #FF2C54;\r\n              font-weight: 600;\r\n              margin-right: 10rpx;\r\n            }\r\n            \r\n            .daily-price {\r\n              font-size: 24rpx;\r\n              color: #FF9500;\r\n              text-decoration: line-through;\r\n              margin-right: 10rpx;\r\n            }\r\n            \r\n            .original-price {\r\n              font-size: 22rpx;\r\n              color: #999999;\r\n              text-decoration: line-through;\r\n            }\r\n          }\r\n          \r\n          .group-info {\r\n            display: flex;\r\n            justify-content: space-between;\r\n            align-items: center;\r\n            margin-top: 16rpx;\r\n            \r\n            .group-count {\r\n              font-size: 22rpx;\r\n              color: #FF2C54;\r\n              background-color: rgba(255, 44, 84, 0.1);\r\n              padding: 4rpx 10rpx;\r\n              border-radius: 20rpx;\r\n            }\r\n            \r\n            .join-btn {\r\n              font-size: 22rpx;\r\n              color: #FFFFFF;\r\n              background: linear-gradient(135deg, #FF2C54 0%, #FF4F64 100%);\r\n              padding: 6rpx 16rpx;\r\n              border-radius: 20rpx;\r\n              box-shadow: 0 4rpx 8rpx rgba(255, 44, 84, 0.2);\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n/* 加载更多和到底了提示 */\r\n.loading-more, .no-more {\r\n  text-align: center;\r\n  padding: 30rpx 0;\r\n  font-size: 24rpx;\r\n  color: #999999;\r\n}\r\n\r\n/* 拼团列表 */\r\n.group-list-section {\r\n  .group-items {\r\n    padding: 0 20rpx;\r\n    \r\n    .group-item {\r\n      display: flex;\r\n      margin-bottom: 30rpx;\r\n      padding: 20rpx;\r\n      background-color: #FFFFFF;\r\n      border-radius: 30rpx;\r\n      box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.06);\r\n      transition: transform 0.3s ease;\r\n      \r\n      &:active {\r\n        transform: scale(0.98);\r\n      }\r\n      \r\n      .item-image-container {\r\n        position: relative;\r\n        width: 220rpx;\r\n        height: 220rpx;\r\n        margin-right: 20rpx;\r\n        \r\n        .item-image {\r\n          width: 100%;\r\n          height: 100%;\r\n          border-radius: 25rpx;\r\n          box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);\r\n        }\r\n        \r\n        .item-tag {\r\n          position: absolute;\r\n          top: 10rpx;\r\n          left: 10rpx;\r\n          padding: 4rpx 12rpx;\r\n          font-size: 20rpx;\r\n          color: #FFFFFF;\r\n          border-radius: 15rpx;\r\n          background: linear-gradient(135deg, #FF2C54 0%, #FF4F64 100%);\r\n          box-shadow: 0 4rpx 8rpx rgba(255, 44, 84, 0.2);\r\n        }\r\n      }\r\n      \r\n      .item-content {\r\n        flex: 1;\r\n        display: flex;\r\n        flex-direction: column;\r\n        \r\n        .item-title {\r\n          font-size: 28rpx;\r\n          font-weight: 600;\r\n          color: #333333;\r\n          margin-bottom: 8rpx;\r\n        }\r\n        \r\n        .item-desc {\r\n          font-size: 24rpx;\r\n          color: #666666;\r\n          margin-bottom: 12rpx;\r\n          display: -webkit-box;\r\n          -webkit-line-clamp: 1;\r\n          -webkit-box-orient: vertical;\r\n          overflow: hidden;\r\n          text-overflow: ellipsis;\r\n        }\r\n        \r\n        .price-comparison {\r\n          margin-bottom: 12rpx;\r\n          padding: 12rpx;\r\n          background: rgba(245, 245, 247, 0.6);\r\n          border-radius: 20rpx;\r\n          \r\n          .price-row {\r\n            display: flex;\r\n            align-items: center;\r\n            margin-bottom: 6rpx;\r\n            \r\n            .price-label {\r\n              font-size: 22rpx;\r\n              color: #666666;\r\n              width: 80rpx;\r\n            }\r\n            \r\n            .price-value {\r\n              font-size: 24rpx;\r\n              \r\n              &.group-price {\r\n                font-size: 32rpx;\r\n                color: #FF2C54;\r\n                font-weight: 600;\r\n              }\r\n              \r\n              &.daily-price {\r\n                color: #FF9500;\r\n                text-decoration: line-through;\r\n              }\r\n              \r\n              &.market-price {\r\n                color: #999999;\r\n                text-decoration: line-through;\r\n              }\r\n            }\r\n          }\r\n          \r\n          .price-save {\r\n            margin-top: 8rpx;\r\n            \r\n            .save-text {\r\n              display: inline-block;\r\n              font-size: 20rpx;\r\n              color: #FFFFFF;\r\n              background: linear-gradient(135deg, #FF2C54 0%, #FF4F64 100%);\r\n              padding: 4rpx 12rpx;\r\n              border-radius: 15rpx;\r\n              box-shadow: 0 4rpx 8rpx rgba(255, 44, 84, 0.2);\r\n            }\r\n          }\r\n          \r\n          &.compact {\r\n            .price-row {\r\n              display: inline-flex;\r\n              margin-right: 20rpx;\r\n              \r\n              .price-label {\r\n                width: auto;\r\n                margin-right: 8rpx;\r\n              }\r\n            }\r\n          }\r\n        }\r\n        \r\n        .item-footer {\r\n          display: flex;\r\n          justify-content: space-between;\r\n          align-items: center;\r\n          margin-top: auto;\r\n          \r\n          .group-progress {\r\n            display: flex;\r\n            align-items: center;\r\n            \r\n            .avatars {\r\n              display: flex;\r\n              margin-right: 10rpx;\r\n              \r\n              .user-avatar {\r\n                width: 40rpx;\r\n                height: 40rpx;\r\n                border-radius: 50%;\r\n                border: 2rpx solid #FFFFFF;\r\n                margin-left: -10rpx;\r\n                box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.1);\r\n                \r\n                &:first-child {\r\n                  margin-left: 0;\r\n                }\r\n              }\r\n              \r\n              .avatar-count {\r\n                width: 40rpx;\r\n                height: 40rpx;\r\n                border-radius: 50%;\r\n                background-color: #F2F2F7;\r\n                display: flex;\r\n                align-items: center;\r\n                justify-content: center;\r\n                font-size: 20rpx;\r\n                color: #666666;\r\n                margin-left: -10rpx;\r\n                box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.1);\r\n              }\r\n            }\r\n            \r\n            .progress-text {\r\n              font-size: 22rpx;\r\n              color: #666666;\r\n            }\r\n          }\r\n          \r\n          .action-btn {\r\n            height: 60rpx;\r\n            padding: 0 20rpx;\r\n            background: linear-gradient(135deg, #FF2C54 0%, #FF4F64 100%);\r\n            border-radius: 30rpx;\r\n            display: flex;\r\n            align-items: center;\r\n            justify-content: center;\r\n            font-size: 24rpx;\r\n            color: #FFFFFF;\r\n            box-shadow: 0 4rpx 12rpx rgba(255, 44, 84, 0.3);\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n/* 我的拼团 */\r\n.my-groups-section {\r\n  .empty-tip {\r\n    display: flex;\r\n    flex-direction: column;\r\n    align-items: center;\r\n    padding: 60rpx 0;\r\n    \r\n    .empty-image {\r\n      width: 200rpx;\r\n      height: 200rpx;\r\n      margin-bottom: 20rpx;\r\n      opacity: 0.8;\r\n    }\r\n    \r\n    .empty-text {\r\n      font-size: 28rpx;\r\n      color: #999999;\r\n      margin-bottom: 30rpx;\r\n    }\r\n    \r\n    .empty-btn {\r\n      display: flex;\r\n      justify-content: center;\r\n      align-items: center;\r\n      height: 80rpx;\r\n      width: 300rpx;\r\n      border-radius: 40rpx;\r\n      background: linear-gradient(135deg, #FF2C54 0%, #FF4F64 100%);\r\n      font-size: 28rpx;\r\n      font-weight: 500;\r\n      color: #FFFFFF;\r\n      box-shadow: 0 8rpx 16rpx rgba(255, 44, 84, 0.2);\r\n      transition: transform 0.3s ease;\r\n      \r\n      &:active {\r\n        transform: scale(0.95);\r\n      }\r\n    }\r\n  }\r\n  \r\n  .my-group-list {\r\n    padding: 0 20rpx;\r\n    \r\n    .my-group-item {\r\n      display: flex;\r\n      margin-bottom: 20rpx;\r\n      padding: 20rpx;\r\n      background-color: #FFFFFF;\r\n      border-radius: 30rpx;\r\n      box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.06);\r\n      transition: transform 0.3s ease;\r\n      \r\n      &:active {\r\n        transform: scale(0.98);\r\n      }\r\n      \r\n      .item-image-container {\r\n        position: relative;\r\n        width: 160rpx;\r\n        height: 160rpx;\r\n        margin-right: 20rpx;\r\n        \r\n        .item-image {\r\n          width: 100%;\r\n          height: 100%;\r\n          border-radius: 25rpx;\r\n          box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);\r\n        }\r\n        \r\n        .item-status-tag {\r\n          position: absolute;\r\n          top: 10rpx;\r\n          left: 10rpx;\r\n          padding: 4rpx 12rpx;\r\n          font-size: 20rpx;\r\n          color: #FFFFFF;\r\n          border-radius: 15rpx;\r\n          box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.15);\r\n          \r\n          &.pending {\r\n            background: linear-gradient(135deg, #FF9500 0%, #FFBD2E 100%);\r\n          }\r\n          \r\n          &.success {\r\n            background: linear-gradient(135deg, #34C759 0%, #30DB5B 100%);\r\n          }\r\n          \r\n          &.failed {\r\n            background: linear-gradient(135deg, #FF3B30 0%, #FF5E54 100%);\r\n          }\r\n        }\r\n      }\r\n      \r\n      .item-content {\r\n        flex: 1;\r\n        display: flex;\r\n        flex-direction: column;\r\n        \r\n        .item-title {\r\n          font-size: 28rpx;\r\n          font-weight: 600;\r\n          color: #333333;\r\n          margin-bottom: 12rpx;\r\n        }\r\n        \r\n        .group-status {\r\n          display: flex;\r\n          justify-content: space-between;\r\n          align-items: center;\r\n          margin-top: 12rpx;\r\n          \r\n          .status-info {\r\n            .status-text {\r\n              font-size: 24rpx;\r\n              font-weight: 600;\r\n              margin-right: 10rpx;\r\n              \r\n              &.pending {\r\n                color: #FF9500;\r\n              }\r\n              \r\n              &.success {\r\n                color: #34C759;\r\n              }\r\n              \r\n              &.failed {\r\n                color: #FF3B30;\r\n              }\r\n            }\r\n            \r\n            .status-desc {\r\n              font-size: 22rpx;\r\n              color: #666666;\r\n            }\r\n          }\r\n          \r\n          .status-action {\r\n            .share-btn {\r\n              height: 56rpx;\r\n              padding: 0 20rpx;\r\n              background: linear-gradient(135deg, #FF2C54 0%, #FF4F64 100%);\r\n              border-radius: 28rpx;\r\n              display: flex;\r\n              align-items: center;\r\n              justify-content: center;\r\n              font-size: 24rpx;\r\n              color: #FFFFFF;\r\n              box-shadow: 0 4rpx 12rpx rgba(255, 44, 84, 0.3);\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n/* 浮动按钮 */\r\n.float-btn {\r\n  position: fixed;\r\n  right: 30rpx;\r\n  bottom: 160rpx;\r\n  width: 90rpx;\r\n  height: 90rpx;\r\n  border-radius: 50%;\r\n  background: linear-gradient(135deg, #FF2C54 0%, #FF4F64 100%);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  box-shadow: 0 8rpx 20rpx rgba(255, 44, 84, 0.3);\r\n  z-index: 90;\r\n  transform: translateZ(0);\r\n  transition: transform 0.3s ease;\r\n  \r\n  &:active {\r\n    transform: scale(0.9);\r\n  }\r\n}\r\n\r\n/* 底部导航 */\r\n.bottom-tabbar {\r\n  position: fixed;\r\n  left: 20rpx;\r\n  right: 20rpx;\r\n  bottom: 20rpx;\r\n  height: 100rpx;\r\n  background-color: rgba(255, 255, 255, 0.9);\r\n  backdrop-filter: blur(10px);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-around;\r\n  box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.1);\r\n  z-index: 99;\r\n  padding-bottom: env(safe-area-inset-bottom);\r\n  border-radius: 35rpx;\r\n  \r\n  .tab-item {\r\n    display: flex;\r\n    flex-direction: column;\r\n    align-items: center;\r\n    justify-content: center;\r\n    \r\n    .tab-icon {\r\n      width: 48rpx;\r\n      height: 48rpx;\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: center;\r\n      transition: transform 0.3s ease;\r\n      \r\n      &:active {\r\n        transform: scale(0.9);\r\n      }\r\n    }\r\n    \r\n    .tab-text {\r\n      font-size: 20rpx;\r\n      color: #666666;\r\n      margin-top: 4rpx;\r\n      \r\n      &.active {\r\n        color: #FF2C54;\r\n        font-weight: 500;\r\n      }\r\n    }\r\n  }\r\n}\r\n</style> ", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/subPackages/activity-showcase/pages/group-buy/index.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni"], "mappings": ";;;AA0QA,MAAK,YAAU;AAAA,EACb,OAAO;AACL,WAAO;AAAA,MACL,YAAY;AAAA,MACZ,SAAS;AAAA,MACT,QAAQ;AAAA,MACR,iBAAiB;AAAA,MACjB,YAAY;AAAA,MACZ,MAAM;AAAA,QACJ,EAAE,MAAM,OAAQ;AAAA,QAChB,EAAE,MAAM,OAAO;AAAA,MAChB;AAAA,MACD,SAAS;AAAA,QACP,EAAE,OAAO,uCAAuC,MAAM,GAAI;AAAA,QAC1D,EAAE,OAAO,uCAAuC,MAAM,GAAI;AAAA,QAC1D,EAAE,OAAO,uCAAuC,MAAM,GAAG;AAAA,MAC1D;AAAA,MACD,YAAY;AAAA,QACV,EAAE,MAAM,MAAM,MAAM,qCAAqC,IAAI,OAAQ;AAAA,QACrE,EAAE,MAAM,MAAM,MAAM,qCAAqC,IAAI,YAAa;AAAA,QAC1E,EAAE,MAAM,MAAM,MAAM,qCAAqC,IAAI,WAAY;AAAA,QACzE,EAAE,MAAM,MAAM,MAAM,qCAAqC,IAAI,SAAU;AAAA,QACvE,EAAE,MAAM,MAAM,MAAM,qCAAqC,IAAI,OAAO;AAAA,MACrE;AAAA,MACD,kBAAkB;AAAA,QAChB;AAAA,UACE,IAAI;AAAA,UACJ,OAAO;AAAA,UACP,OAAO;AAAA,UACP,aAAa;AAAA,UACb,YAAY;AAAA,UACZ,YAAY;AAAA,UACZ,YAAY;AAAA,QACb;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,OAAO;AAAA,UACP,OAAO;AAAA,UACP,aAAa;AAAA,UACb,YAAY;AAAA,UACZ,YAAY;AAAA,UACZ,YAAY;AAAA,QACb;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,OAAO;AAAA,UACP,OAAO;AAAA,UACP,aAAa;AAAA,UACb,YAAY;AAAA,UACZ,YAAY;AAAA,UACZ,YAAY;AAAA,QACd;AAAA,MACD;AAAA,MACD,sBAAsB;AAAA,MACtB,YAAY;AAAA,QACV;AAAA,UACE,IAAI;AAAA,UACJ,OAAO;AAAA,UACP,OAAO;AAAA,UACP,aAAa;AAAA,UACb,YAAY;AAAA,UACZ,YAAY;AAAA,UACZ,aAAa;AAAA,UACb,KAAK;AAAA,UACL,YAAY;AAAA,UACZ,WAAW;AAAA,UACX,aAAa;AAAA,YACX;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,UACD;AAAA,UACD,SAAS,IAAI,KAAK,KAAK,IAAG,IAAK,IAAI,KAAK,KAAK,KAAK,GAAI,EAAE,YAAY;AAAA,QACrE;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,OAAO;AAAA,UACP,OAAO;AAAA,UACP,aAAa;AAAA,UACb,YAAY;AAAA,UACZ,YAAY;AAAA,UACZ,aAAa;AAAA,UACb,KAAK;AAAA,UACL,YAAY;AAAA,UACZ,WAAW;AAAA,UACX,aAAa;AAAA,YACX;AAAA,YACA;AAAA,UACD;AAAA,UACD,SAAS,IAAI,KAAK,KAAK,IAAG,IAAK,IAAI,KAAK,KAAK,KAAK,GAAI,EAAE,YAAY;AAAA,QACrE;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,OAAO;AAAA,UACP,OAAO;AAAA,UACP,aAAa;AAAA,UACb,YAAY;AAAA,UACZ,YAAY;AAAA,UACZ,aAAa;AAAA,UACb,KAAK;AAAA,UACL,YAAY;AAAA,UACZ,WAAW;AAAA,UACX,aAAa;AAAA,YACX;AAAA,YACA;AAAA,YACA;AAAA,UACD;AAAA,UACD,SAAS,IAAI,KAAK,KAAK,IAAG,IAAK,IAAI,KAAK,KAAK,KAAK,GAAI,EAAE,YAAY;AAAA,QACrE;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,OAAO;AAAA,UACP,OAAO;AAAA,UACP,aAAa;AAAA,UACb,YAAY;AAAA,UACZ,YAAY;AAAA,UACZ,aAAa;AAAA,UACb,KAAK;AAAA,UACL,YAAY;AAAA,UACZ,WAAW;AAAA,UACX,aAAa;AAAA,YACX;AAAA,YACA;AAAA,UACD;AAAA,UACD,SAAS,IAAI,KAAK,KAAK,IAAG,IAAK,IAAI,KAAK,KAAK,KAAK,GAAI,EAAE,YAAY;AAAA,QACtE;AAAA,MACD;AAAA,MACD,UAAU;AAAA,QACR;AAAA,UACE,IAAI;AAAA,UACJ,OAAO;AAAA,UACP,OAAO;AAAA,UACP,aAAa;AAAA,UACb,YAAY;AAAA,UACZ,YAAY;AAAA,UACZ,aAAa;AAAA,UACb,YAAY;AAAA,UACZ,WAAW;AAAA,UACX,QAAQ;AAAA,UACR,SAAS,IAAI,KAAK,KAAK,IAAM,IAAE,KAAK,KAAK,KAAK,GAAI,EAAE,YAAY;AAAA,QACjE;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,OAAO;AAAA,UACP,OAAO;AAAA,UACP,aAAa;AAAA,UACb,YAAY;AAAA,UACZ,YAAY;AAAA,UACZ,aAAa;AAAA,UACb,YAAY;AAAA,UACZ,WAAW;AAAA,UACX,QAAQ;AAAA,UACR,SAAS,IAAI,KAAK,KAAK,IAAG,IAAK,IAAI,KAAK,KAAK,KAAK,GAAI,EAAE,YAAY;AAAA,QACrE;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,OAAO;AAAA,UACP,OAAO;AAAA,UACP,aAAa;AAAA,UACb,YAAY;AAAA,UACZ,YAAY;AAAA,UACZ,aAAa;AAAA,UACb,YAAY;AAAA,UACZ,WAAW;AAAA,UACX,QAAQ;AAAA,UACR,SAAS,IAAI,KAAK,KAAK,IAAG,IAAK,IAAI,KAAK,KAAK,KAAK,GAAI,EAAE,YAAY;AAAA,QACtE;AAAA,MACF;AAAA,IACF;AAAA,EACD;AAAA,EACD,UAAU;AAAA,IACR,eAAe;AACb,YAAM,QAAQ,MAAM,KAAK,KAAK;AAC9B,YAAM,OAAO,KAAK,kBAAkB;AACpC,aAAO;AAAA,QACL,OAAO,QAAQ;AAAA,QACf,WAAW,cAAc,OAAO,GAAG;AAAA,MACrC;AAAA,IACF;AAAA,EACD;AAAA,EACD,SAAS;AAEP,SAAK,UAAU;AAEf,SAAK,eAAe;AAEpB,UAAM,QAAQ,gBAAgB;AAC9B,QAAI,MAAM,WAAW,GAAG;AACtB,WAAK,aAAa;AAAA,IACpB;AAAA,EACD;AAAA,EACD,SAAS;AAAA;AAAA,IAEP,SAAS;AACPA,oBAAAA,MAAI,aAAa;AAAA,IAClB;AAAA;AAAA,IAGD,UAAU,OAAO;AACf,WAAK,kBAAkB;AACvB,WAAK,UAAU;AAAA,IAChB;AAAA;AAAA,IAGD,WAAW;AACT,UAAI,KAAK,WAAW,KAAK;AAAQ;AAEjC,WAAK,UAAU;AAGf,iBAAW,MAAM;AACf,YAAI,KAAK,oBAAoB,GAAG;AAE9B,gBAAM,YAAY;AAAA,YAChB;AAAA,cACE,IAAI;AAAA,cACJ,OAAO;AAAA,cACP,OAAO;AAAA,cACP,aAAa;AAAA,cACb,YAAY;AAAA,cACZ,YAAY;AAAA,cACZ,aAAa;AAAA,cACb,KAAK;AAAA,cACL,YAAY;AAAA,cACZ,WAAW;AAAA,cACX,aAAa;AAAA,gBACX;AAAA,gBACA;AAAA,cACD;AAAA,cACD,SAAS,IAAI,KAAK,KAAK,IAAG,IAAK,IAAI,KAAK,KAAK,KAAK,GAAI,EAAE,YAAY;AAAA,YACrE;AAAA,YACD;AAAA,cACE,IAAI;AAAA,cACJ,OAAO;AAAA,cACP,OAAO;AAAA,cACP,aAAa;AAAA,cACb,YAAY;AAAA,cACZ,YAAY;AAAA,cACZ,aAAa;AAAA,cACb,KAAK;AAAA,cACL,YAAY;AAAA,cACZ,WAAW;AAAA,cACX,aAAa;AAAA,gBACX;AAAA,cACD;AAAA,cACD,SAAS,IAAI,KAAK,KAAK,IAAG,IAAK,IAAI,KAAK,KAAK,KAAK,GAAI,EAAE,YAAY;AAAA,YACtE;AAAA,UACF;AAEA,eAAK,aAAa,CAAC,GAAG,KAAK,YAAY,GAAG,SAAS;AAAA,QACrD;AAEA,aAAK,SAAS;AACd,aAAK,UAAU;AAAA,MAChB,GAAE,IAAI;AAAA,IACR;AAAA;AAAA,IAGD,YAAY;AAAA,IAGX;AAAA;AAAA,IAGD,iBAAiB,IAAI;AACnBA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,wDAAwD,EAAE;AAAA,OAChE;AAAA,IACF;AAAA;AAAA,IAGD,mBAAmB;AACjBA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK;AAAA,OACN;AAAA,IACF;AAAA;AAAA,IAGD,iBAAiB,UAAU;AACzBA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO,MAAM,SAAS,IAAI;AAAA,QAC1B,MAAM;AAAA,OACP;AAAA,IAEF;AAAA;AAAA,IAGD,iBAAiB;AAEf,UAAI,QAAQ;AACZ,UAAI,UAAU;AACd,UAAI,UAAU;AAEd,kBAAY,MAAM;AAChB;AACA,YAAI,UAAU,GAAG;AACf,oBAAU;AACV;AACA,cAAI,UAAU,GAAG;AACf,sBAAU;AACV;AACA,gBAAI,QAAQ,GAAG;AACb,sBAAQ;AAAA,YACV;AAAA,UACF;AAAA,QACF;AAEA,aAAK,uBAAuB,GAAG,MAAM,SAAQ,EAAG,SAAS,GAAG,GAAG,CAAC,IAAI,QAAQ,WAAW,SAAS,GAAG,GAAG,CAAC,IAAI,QAAQ,SAAQ,EAAG,SAAS,GAAG,GAAG,CAAC;AAAA,MAC/I,GAAE,GAAI;AAAA,IACR;AAAA;AAAA,IAGD,cAAc,QAAQ;AACpB,YAAM,YAAY;AAAA,QAChB,WAAW;AAAA,QACX,WAAW;AAAA,QACX,UAAU;AAAA,MACZ;AACA,aAAO,UAAU,MAAM,KAAK;AAAA,IAC7B;AAAA;AAAA,IAGD,WAAW,IAAI;AACbA,oBAAAA,MAAI,cAAc;AAAA,QAChB,iBAAiB;AAAA,QACjB,OAAO,CAAC,mBAAmB,eAAe;AAAA,OAC3C;AAAA,IACF;AAAA;AAAA,IAGD,cAAc;AACZA,oBAAAA,MAAI,aAAa;AAAA,QACf,WAAW;AAAA,QACX,UAAU;AAAA,OACX;AAAA,IACF;AAAA;AAAA,IAGD,iBAAiB;AACf,UAAI,CAAC,KAAK,YAAY;AACpBA,sBAAAA,MAAI,UAAU;AAAA,UACZ,KAAK;AAAA,SACN;AAAA,MACH;AAAA,IACD;AAAA;AAAA,IAGD,qBAAqB;AACnBA,oBAAAA,MAAI,UAAU;AAAA,QACZ,KAAK;AAAA,OACN;AAAA,IACF;AAAA;AAAA,IAGD,iBAAiB;AACfA,oBAAAA,MAAI,UAAU;AAAA,QACZ,KAAK;AAAA,OACN;AAAA,IACF;AAAA;AAAA,IAGD,iBAAiB;AACfA,oBAAAA,MAAI,UAAU;AAAA,QACZ,KAAK;AAAA,OACN;AAAA,IACH;AAAA,EACF;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACxnBA,GAAG,WAAW,eAAe;"}